# Slardar 后端 API 文档
本文档旨在详细介绍 slardar.ts 文件中定义的所有后端 API 接口，为开发人员提供统一的参考。
文件位置：apps/quality/api/lambda/slardar.ts
## 核心功能
该模块主要负责与 Slardar 平台进行交互，用于监控和分析应用的崩溃（Crash）、卡顿（Block）等性能问题。它封装了数据查询、处理、存储和告警等一系列功能。
## 前端导入
import { querySlardarValue } from '@api/slardar';
## API 接口列表
### 性能指标查询 1. querySlardarValue
- 路径 : POST /slardar/value
- 功能 : 查询指定版本、指定指标（如崩溃率）在发布后72小时内的时间序列数据。 2. querySlardarInfo
- 路径 : POST /slardar/info
- 功能 : 获取当前应用已启用的 Slardar 监控项配置信息。 3. queryVersionQualityValue
- 路径 : POST /slardar/quality/value
- 功能 : 查询指定版本的质量指标数据。 4. queryVersionInfo
- 路径 : POST /version/detail
- 功能 : 查询特定版本码的详细信息。
### Crash 问题查询与分析 5. querySlardarCrashList
- 路径 : POST /slardar/crash
- 功能 : 查询指定版本的 Crash 问题列表，并聚合展示 Meego 链接、历史排名等信息。 6. queryTop10SlardarCrashList
- 路径 : POST /slardar/crash/top10
- 功能 : 查询指定版本、指定类型的 Top 10 Crash 问题。 7. queryNewCrashList
- 路径 : POST /slardar/crash/new/list
- 功能 : 查询指定版本新增的 Crash 问题列表。 8. queryHistoryCrashList
- 路径 : POST /slardar/crash/history/list
- 功能 : 查询一组 Crash 问题的历史趋势数据。 9. queryCrashListWithIssueId
- 路径 : POST /slardar/crash/list/with/issueid
- 功能 : 根据 Issue ID 查询相关的 Crash 列表信息。 10. getCrashInfoDetailById
- 路径 : POST /slardar/getCrashInfoDetailById
- 功能 : 直接从 Slardar 拉取并返回指定 Crash Issue 的详细信息。 11. getIssuePercent
- 路径 : POST /slardar/getIssuePercent
- 功能 : 获取 Crash 在不同维度（如OS版本、设备型号）下的分布情况。 12. fetchTransparentData
- 路径 : POST /slardar/fetchTransparentData
- 功能 : 提供一个透传接口，直接调用 Slardar 的 crashIssueListSearch API。
### Crash 问题管理与联动 13. updateSlardarCrashIssue
- 路径 : POST /slardar/update/crash/issue
- 功能 : 更新 Crash Issue 与 Meego Bug 的关联关系。 14. checkAndUpdateBug
- 路径 : POST /issue/check/update/bug
- 功能 : 检查并更新 Meego Bug 的优先级。 15. createBugIssue
- 路径 : POST /slardar/create/bug/issue
- 功能 : 为指定的 Crash 问题创建 Meego Bug。 16. createGrayScaleBug
- 路径 : POST /slardar/create/grayscale/bug
- 功能 : 为灰度版本发现的 Crash 问题创建 Meego Bug。 17. updateSlardarWarn
- 路径 : POST /slardar/crash/warn/update
- 功能 : 更新 Crash 问题的告警状态（是否告警）。 18. sendWarningIssue
- 路径 : POST /slardar/send/warning/issue
- 功能 : 手动触发发送 Crash 问题的告警通知。 19. updateIssueLevel
- 路径 : POST /slardar/update/issue/level
- 功能 : 更新 Crash 问题的严重等级。
### 评论与备注 20. getCommentListData
- 路径 : POST /slardar/getCommentListData
- 功能 : 获取指定 Crash Issue 的评论列表。 21. addIssueComment
- 路径 : POST /slardar/addIssueComment
- 功能 : 为指定的 Crash Issue 添加评论。
### 场景与配置 22. fetchSceneList
- 路径 : POST /metric/scenelist
- 功能 : 获取性能指标的场景列表。 23. updateSceneOwner
- 路径 : POST /metric/updatesceneowner
- 功能 : 更新性能指标场景的负责人。
### 内部与辅助接口 24. iosQualityUpdate
- 路径 : POST /version/iosupdatequality
- 功能 : iOS 质量数据更新的内部入口。 25. findJOBInfoByIssue
- 路径 : POST /slardar/find/job/by/issue
- 功能 : 根据 Issue 查询关联的 JOB（可能是 CI/CD 任务）信息。 26. findGrayVersionTime
- 路径 : POST /slardar/find/gray/version/time
- 功能 : 查询灰度版本的开始时间。 27. getSlardarLink
- 路径 : POST /slardar/get/link
- 功能 : 解析 Slardar 链接，提取关键信息。