# MCP开发指南
## 目标
开发MCP服务
## 方法
1. 新增 Tool
一个 Tool 的定义主要由以下几个部分组成：
- name：Tool 的唯一标识。建议使用 xxx_yyy_zzz 英文+下划线的命名方式，比如“get_libra_info”
- description：Tool 说明&描述。一般用于辅助理解 Tool 的功能，尽量表述清晰完整
- inputSchema：Tool 请求入参。是 Object 对象类型，LLM 会自动传入符合定义的参数
- annotations(可选)：Tool 表现行为标注。具体可参考官方的释义
{
  name: string;          // Unique identifier for the tool
  description?: string;  // Human-readable description
  inputSchema: {         // JSON Schema for the tool's parameters
    type: "object",
    properties: { ... }  // Tool-specific parameters
  },
  annotations?: {        // Optional hints about tool behavior
    title?: string;      // Human-readable title for the tool
    readOnlyHint?: boolean;    // If true, the tool does not modify its environment
    destructiveHint?: boolean; // If true, the tool may perform destructive updates
    idempotentHint?: boolean;  // If true, repeated calls with same args have no additional effect
    openWorldHint?: boolean;   // If true, tool interacts with external entities
  }
}
在纸飞机项目中，请在 apps/host/api/service/mcp/tool-handlers 目录下完成 Tool文件的新增。Tool 的命名为：McpToolXXXHandler，其中 XXX 为自定义(用于描述 Tool 的具体功能)。新增的 Tool 类均需遵循 McpToolBaseHandler 接口，实现两个主要的方法：
- toolDefinition()：Tool 定义，返回 McpToolDefinition[] 数组
- toolHandler()：Tool 的实际执行逻辑，处理完毕后返回 McpToolRsp
@Injectable()
export default class McpToolXXXHandler implements McpToolBaseHandler {
    // Tool 定义
    toolDefinition(): McpToolDefinition[] {}
    
    // Tool 处理逻辑
    async toolHandler(toolName: string, args: any): Promise<McpToolRsp> {}
}
完整的示例可参考：apps/host/api/service/mcp/tool-handlers/McpToolLibraHandler.ts
1.1.2 注册 Tool
Tool 新增完毕后，需要在 apps/host/api/service/mcp/McpToolHandlers.ts 中完成注册
