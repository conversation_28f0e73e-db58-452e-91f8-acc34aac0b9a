# Tea API 开发指南
本文档详细介绍了 tea.ts 文件中定义的所有 API 接口，这些接口主要负责与公司内部的 Tea 数据平台进行交互，包括指标管理、数据查询、配置更新等。
## 文件位置
apps/quality/api/lambda/tea.ts
## 使用
如果需要调用这些代码，到文件apps/quality/api/lambda/tea.ts去详细查看对应的API接口，获取调用参数，在前端进行调用
## API 接口列表
### 指标管理 (Metric Management) 1. createTeaMetric
- 路径 : PUT /tea/metric
- 核心功能 : 创建一个新的 Tea 指标。
- 路径 : PATCH /tea/metric/:name
- 核心功能 : 更新一个已存在的 Tea 指标。
- 路径 : POST /tea/metric/delete/delete_metric
- 核心功能 : 删除一个 Tea 指标的配置。
- 路径 : POST /tea/metric/delete/clean_metric_values
- 核心功能 : 清除一个 Tea 指标的所有历史数据。
### 指标查询 (Metric Query) 5. getTeaMetric
- 路径 : POST /tea/metric
- 核心功能 : 分页、排序、筛选查询 Tea 指标列表。
- 路径 : POST /tea/metric/versioned
- 核心功能 : 查询所有“按版本”维度的 Tea 指标。
- 路径 : POST /tea/metric/versioned/get_metric_by_id
- 核心功能 : 根据名称查询单个“按版本”维度的 Tea 指标。 8. getTeaSerialMetric
- 路径 : POST /tea/metric/serial
- 核心功能 : 查询所有“按时间序列”维度的 Tea 指标。
### 数据查询 (Data Query) 9. rawTeaRequestOld
- 路径 : POST /tea/raw_request/old
- 核心功能 : 直接向旧版 Tea 平台代理原始数据请求。
- 路径 : POST /tea/metric/gray/value
- 核心功能 : 查询灰度版本相关的指标数据。
- 路径 : GET /tea/overall/:metricId/:timestamp
- 核心功能 : 查询用于绘制“总览”图表的指标数据。 12. queryTeaOverallTableValues
- 路径 : POST /tea/overall/:timestamp
- 核心功能 : 查询用于填充“总览”表格的指标数据。
### 配置与工具 (Configuration & Utilities) 13. saveTeaMetricExportIndex
- 路径 : POST /tea/metric/config/sort_index
- 核心功能 : 保存指标导出的排序配置。 14. getTeaMetricExportIndex
- 路径 : POST /tea/metric/config/get_sort_index
- 核心功能 : 获取指标导出的排序配置。 15. fetchTeaToken
- 路径 : GET /tea/token
- 核心功能 : 获取访问 Tea 平台的认证 Token。 16. getMetricAndCommonFilter
- 路径 : GET /tea/option
- 核心功能 : 从一个 Tea 的 URL 中解析出指标 ( metric ) 和通用过滤器 ( common_filter )。
- 路径 : GET /tea/quota
- 核心功能 : 获取当天的 Tea API 调用配额。
## 注意事项
- 新旧 Tea 平台 : 代码中大量存在对新旧 Tea 平台的兼容逻辑（通过 URL 中是否包含 tea-next 判断），在维护时需要特别注意。
- 数据校验 : 所有接口的输入数据都使用了 zod 进行严格的 schema 校验。
- 错误处理 : 接口统一使用 success() 和 fail() 函数来包装返回结果，保证了前端处理的一致性。