# 定时任务开发指南
## 简介
纸飞机的定时任务基于 FaaS 配置的Timer触发器纸飞机Host定时任务
## 方法
直接在host: apps/host/api/trigger/HostCronJobTrigger.ts 或者 quality: apps/quality/api/trigger/QualityCronJobTrigger.ts中定义Job。在类方法上使用 @CronJob 注解描述一个定时任务：
@CronJob(
    '0 17 * * *',  // cron表达式，定义任务执行的时间策略
    '定时触发灰度未上车MR', // job描述，后面会出现在日志和告警中方便过滤
    ['zhanglinwei.yimu'] // 任务Owner，告警用WIP
)
async handleGrayMrPendingRemind() { // 任务方法名，可用于手动触发定时任务
  await useInject(GrayMrPendingRemindServer).handler(''); // 任务逻辑，这里可以调用其他服务
}
