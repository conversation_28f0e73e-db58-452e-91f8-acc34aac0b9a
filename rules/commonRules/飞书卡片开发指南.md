# 飞书通知卡片开发指南
## 目标：
纸飞机项目中开发飞书通知卡片，功能包含飞书卡片的创建，发送，回调，以及回调中更新卡片
## 方法：
必须通过参考代码去进行开发
- 构建：参考apps/quality/api/service/libra/LibraCard.ts中的任意一个
- 发送： 参考 await useInject(MessageService).sendNormalMsg(card, UserIdType.openId, 'ou_288568c940e05d5a96e07227d7103b6a');//发送可以发送给个人或者群聊
- 回调：回调操作是某个Active在卡片发送之后在飞书上操作而发生的回调，需要在卡片构建时，对需要回调的active进行设计回调方法，如 apps/quality/api/service/libra/LibraCard.ts中的最后一个关闭灰度实验卡片通知的关闭按钮；
- 回调方法设置步骤：1.在packages/shared/dist/src/lark/larkCard.d.ts枚举中加入新的方法 2，在这个中添加调用的方法apps/host/api/service/CardCallbackHandler.ts 3.在新的文件或者在用户指定位置实现调用的方法，可以参考 apps/host/api/service/handler/larkCard/experimentUpdate.ts
- 更新卡片：已经发生的卡片，调用某个active回调后需要更新卡片；方法是首先构建卡片，然后通过参考await useInject(LarkService).updateCard(JSON.stringify(msg), messageId);进行发送更新