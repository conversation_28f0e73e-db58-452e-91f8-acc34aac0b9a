# BitsService 开发指南

## 文件路径

`apps/host/api/service/third/bits.ts`

## 服务名称

`BitsService`

## 简介

`BitsService` 是与公司内部代码与构建平台 **Bits** 进行交互的核心服务。它封装了所有对 Bits 系统 API 的调用，为上层业务逻辑提供了一套标准化的接口。该服务是整个自动化流程的基石之一，所有与代码仓库、合并请求（Merge Request, MR）、CI/CD 流水线、组件和制品库相关的操作，都通过此服务完成。

作为 `third` 目录下的服务，它被设计为一个原子化的、无业务逻辑的客户端，专注于与 Bits API 的直接通信。更复杂的业务流程（例如，根据 MR 状态更新 Meego 任务）则由上层的服务（如 `MeegoOldService`）通过调用 `BitsService` 来实现。

## 核心概念

- **认证 (Authentication)**: 服务通过注入的 `TccService` 获取访问 Bits API 所需的 Bearer Token。`createRequest` 方法是所有 API 请求的起点，它会自动在请求头中附加认证信息。
- **用户模拟 (User Impersonation)**: 许多方法接受一个可选的 `creator` 参数。当提供了该参数时，Bits API 请求会携带 `x-bits-user` 头，使得操作可以代表指定用户执行，这对于构建自动化机器人和工具至关重要。
- **API 端点**: 服务主要与 `https://bits.bytedance.net/openapi` 和 `https://bits.bytedance.net/api` 这两个端点进行通信，分别对应公用和私有 API。
- **MR (Merge Request)**: MR 是服务中最常操作的对象，涉及查询、创建、修改、评审、合并等一系列操作。

## 核心依赖

- `TccService`: **（认证核心）** 用于获取访问 Bits API 的密钥（Token）。
- `NetworkX`: 一个 HTTP 请求工具类，所有对 Bits API 的调用都通过它来发起。
- `ComponentModelService`: 提供了一个数据库缓存层，用于存储和查询组件（Component）信息，避免了对 Bits API 的重复请求。
- `MeegoService`: 用于实现 Bits 和 Meego 系统之间的联动，例如在更新 MR 时同步更新关联的 Meego Story 信息。
- `BytedLogger`: 用于记录详细的请求和响应日志，方便问题排查。

## 核心功能与方法

`BitsService` 的方法可以根据其功能领域进行划分：

### 1. MR 查询与搜索 (MR Query & Search)

- `getMrInfo(...)`: **最基础的方法**，根据 MR ID、项目 ID 或 IID 获取单个 MR 的所有详细信息。
- `searchMr(...)` & `searchAllMr(...)`: 根据复杂的条件（如作者、分支、状态、关键字等）搜索 MR 列表。
- `fetchAllMrInfo(...)`: 批量获取多个 MR 的详细信息。
- `getMrIdAndTitle(...)`: 轻量级搜索，仅返回 MR 的 ID 和标题。

### 2. MR 操作 (MR Operations)

- `createMr(...)`: 创建一个新的 MR，支持设置标题、描述、分支、评审人等所有属性。
- `forceMergeMr(...)`: 在满足特定权限的情况下，强制合并一个 MR。
- `setSuperMr(...)`: 将一个 MR 设置或取消设置为“超级 MR”。
- `doMrApproveByMrId(...)`: 通过用户 Token 批准一个 MR。
- `cherryPick(...)`: 将一个 MR 的改动应用（cherry-pick）到另一个分支。

### 3. MR 评审与协作 (MR Review & Collaboration)

- `getMrReviewersInfo(...)`: 获取 MR 的所有评审人及其评审状态（通过、未通过等）。
- `checkQaReviewOk(...)`: 一个便捷方法，专门用于检查 QA 角色的评审人是否已全部通过。
- `remindCodeReview(...)` & `remindQACodeReview(...)`: 向指定的 Code Reviewer 或 QA 发送提醒。
- `addUser(...)` & `removeUser(...)`: 在 MR 关联的飞书群中添加或移除成员。

### 4. CI/CD 与构建 (CI/CD & Builds)

- `bitsBuild(...)`: **核心构建方法**，通过触发一个预定义的构建模板来启动一次 CI/CD 流程。
- `getMrPipelineList(...)`: 获取一个 MR 关联的所有流水线（Pipeline）及其状态。
- `getReleaseInfo(...)`: 查询指定版本和应用的发布记录。
- `bitsTemplateParams(...)`: 获取一个构建模板所需的参数列表。
- `getArtifactList(...)`: 获取指定构建产物（Artifact）的列表。

### 5. 代码、分支与组件 (Code, Branches & Components)

- `searchBranch(...)`: 在项目中搜索符合条件的分支。
- `getMrComponents(...)`: 获取一个 MR 涉及的所有组件及其发布记录。
- `searchComponentInfoByIdWithCache(...)`: 通过组件 ID 查询其详细信息，并利用数据库进行缓存。

### 6. Meego/Feature 集成 (Meego/Feature Integration)

- `getBindMeegoTaskInfo(...)`: 获取一个 MR 关联的所有 Meego 任务（Story 或 Bug）。
- `getMrInfoFromMeegoFeature(...)`: 反向查询，根据 Meego Feature ID 找到关联的 MR 信息。
- `updateStoryVersionByMrInfo(...)`: 当 MR 信息更新时，调用此方法将相关信息（如版本号）同步回 Meego 的 Story 中。

## 使用场景示例

- **检查 MR 状态**: 上层服务（如 `MeegoOldService`）在判断需求是否“准入”时，会调用 `getMrInfo`、`getMrReviewersInfo` 和 `getMrPipelineList` 来综合判断 MR 是否已合并、是否已通过所有评审、流水线是否成功。
- **自动化创建 MR**: 当需要自动从一个分支向另一个分支同步代码时，可以调用 `createMr` 来创建一个自动化 MR。
- **触发打包构建**: 发布平台或机器人通过调用 `bitsBuild` 并传入版本、平台等参数，来触发一次自动化的打包流程。
- **同步 Meego 信息**: 在 MR 被合并后，可以调用 `getBindMeegoTaskInfo` 找到关联的 Meego 任务，然后调用 `MeegoService` 的接口更新任务状态。

## 注意事项

- **权限与用户模拟**: 许多写操作（如创建 MR、合并、批准）都依赖于正确的用户身份。在使用 `creator` 参数时，必须确保执行操作的机器人或服务主体拥有模拟该用户的权限。
- **API 版本**: 注意区分 `/openapi` 和 `/api` 下的接口，它们的鉴权方式和功能范围可能不同。
- **参数依赖**: 调用方法前，务必确保所有必需的参数（如 `project_id`, `mr_id`, `iid`）都已正确获取。