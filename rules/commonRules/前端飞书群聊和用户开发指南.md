# 前端飞书群聊开发指南
## 目标
前端如何开发飞书群聊输入框，以及如何在列表中显示飞书群聊
## 方法
import ProFormSelector from '@/component/ProFormSelector';
### 输入框
<ProFormSelector<ChatInfo>
            mode={'multiple'}
            form={form}
            name={'concernedGroup'}
            placeholder="请输入群组"
            buildSelectValue={buildLarkChatValue}
            search={searchLarkChatList}
          />
          <ProFormSelector<User>
            mode={'multiple'}
            form={form}
            name={'concernedUser'}
            placeholder="请输入关注人"
            buildSelectValue={buildUserSelectValue}
            search={searchUserList}
          />
### form数据转化（需要转化为格式ChatInfo或者User）
  form
      .validateFields([
        'metricName',
        'metricId',
        'P0',
        'P1',
        'concernedUser',
        'concernedGroup',
        'businessLine',
        'isReversed',
      ])
      .then(
        (values: {
          metricName: string;
          metricId: number;
          concernedUser: any[];
          concernedGroup: any[];
          P0: string;
          P1: string;
          businessLine: string[];
          isReversed: boolean;
        }) => {
          // 将包含 JSON 字符串的数组转换为 User 对象数组
          const subscribeUser = values?.concernedUser?.map(item => {
            if (item.value) {
              try {
                return JSON.parse(item.value) as User;
              } catch (error) {
                console.error('解析 JSON 失败:', error);
                // 解析失败时返回原始对象
                return item as User;
              }
            }
            return item as User;
          });
          const subscribeChatGroups = values?.concernedGroup?.map(item => {
            if (item.value) {
              try {
                return JSON.parse(item.value) as ChatInfo;
              } catch (error) {
                console.error('解析 JSON 失败:', error);
                // 解析失败时返回原始对象
                return item as ChatInfo;
              }
            }
            return item as ChatInfo;
          });
### 显示
 const users = row?.subscribe_users?.map((user, index) => {
          const userView = createUserView(user);
          if ((index + 1) % 3 === 0) {
            return (
              <React.Fragment key={index}>
                {userView}
                <br />
              </React.Fragment>
            );
          } else {
            return <React.Fragment key={index}>{userView}</React.Fragment>;
          }
        });

        return (
          <div>
            <Space size={[6, 0]} wrap>
              {users}
            </Space>
          </div>
        );