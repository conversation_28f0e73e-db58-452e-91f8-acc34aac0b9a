# MeegoOldService 开发指南

## 文件路径

`apps/host/api/service/meegoOld.ts`

## 服务名称

`MeegoOldService`

## 简介

`MeegoOldService` 是一个高级业务流程编排服务，在整个研发流程中扮演着“大脑”的角色。它并不直接执行原子性的操作（例如直接调用 Meego 的 HTTP 接口），而是通过组合和调用下层的原子服务（如 `MeegoService`、`BitsService` 等）来实现复杂的业务逻辑。

该服务的核心职责包括：

- **进度跟踪与报告**：自动化地查询版本、需求、任务和缺陷的状态，并生成结构化的进度报告。
- **风险预警与准入检查**：根据预设规则（如 Bug 修复率、MR 合入状态、测试进度等）判断需求和版本的健康度，并对不满足准入标准的项进行预警。
- **自动化消息通知**：将上述报告和预警信息格式化为飞书（Lark）卡片，并推送到指定的群聊或个人，实现信息的自动同步。
- **自动化工作流**：执行一些自动化的工作流任务，例如根据条件更新 Meego 上的工作项、同步飞书群成员等。

值得注意的是，文件名中的 `Old` 表明它可能包含一些历史实现方式，但它仍然是当前业务逻辑的核心。它作为业务逻辑层，构建在 `MeegoService`（负责与 Meego 系统通信）之上。

## 核心依赖

- `MeegoService`: **（最重要的依赖）** 所有与 Meego 系统（需求、缺陷等工作项管理）的交互都通过此服务进行。`MeegoOldService` 将业务逻辑处理后的请求委托给 `MeegoService` 来执行。
- `BitsService`: 用于访问代码仓库和 MR（Merge Request）信息，是实现 MR 状态检查、代码合入检查等功能的关键。
- `LarkService` & `LarkCardService`: 负责与飞书平台通信。服务中几乎所有的对外输出都是通过这两个服务将数据格式化为飞书卡片和消息并发送出去。
- `MeegoStoryModelService`: 用于操作数据库，查询和持久化一些历史数据，例如历史 Bug 趋势。
- `VersionUtilService`, `TimeUtilService`: 提供版本号处理、时间计算等通用工具能力。
- `AirplaneConfigService`: 用于获取和管理动态配置项。

## 核心功能与方法

`MeegoOldService` 的方法可以根据其功能分为以下几类：

### 1. 进度跟踪与报告 (Progress Tracking & Reporting)

这类方法负责从多个数据源拉取信息，并聚合成一份完整的进度报告。

- `queryStoryProgress(versionCode, platform, versionName)`: **核心入口方法**。用于查询指定版本下所有需求（Story）的详细进度。它会调用 `queryStoryProgressInfo` 获取原始数据，然后计算总体风险，并最终调用 `sendCard` 将格式化后的报告发送到飞书群。
- `queryStoryProgressInfo(...)`: 内部核心方法，负责获取单个或多个需求的详细状态。它会聚合来自 Meego 的需求信息、来自 Bits 的 MR 状态和来自 `MeegoService` 的 Bug 修复率，最终计算出每个需求的 `storyState`（如 `Achieved`, `Testing`, `Risk` 等）。
- `queryHistoryBugProgress(...)`: 查询并报告历史遗留 Bug 的处理进度。
- `queryOpenIssueListByProjectVersion(...)`: 查询指定版本中所有未关闭的 Bug，并将其汇总后发送到飞书群。
- `queryModelIssueFixRate(...)`: 针对特定机型（Model）查询关联 Bug 的修复率，用于专项测试报告。

### 2. 准入检查与风险预警 (Readiness Checks & Risk Alerting)

这类方法用于自动化检查某个事项是否满足进入下一阶段的标准。

- `checkStoryMrReady(storyId, platform)`: **关键方法**。用于检查一个需求关联的 MR 是否已“准备就绪”。检查项包括：MR 是否存在、是否为 WIP、目标分支是否正确、流水线是否成功、QA 和客户端负责人是否已 Approve 等。这是实现“MR 自动准入”功能的基础。
- `checkFeatureMrReady(...)`: `checkStoryMrReady` 的一个变体，功能类似。
- `remainStoryRiskByVersion(...)`: 查询指定版本中所有处于风险状态（未达标）的需求，并发送预警信息。
- `remainStoryMrMergeByVersion(...)`: 查询指定版本中所有 MR 尚未合入的需求，并发送预警信息。

### 3. 自动化任务 (Automated Tasks)

- `autoAddVeClientVersion(...)`: 一个自动化任务示例。它会查找所有涉及 VE/Effect 的需求，并自动为其关联上客户端版本号。
- `syncPmsToLarkGroup(...)`: 自动将某个版本下所有需求的 PM（产品经理）同步到对应的飞书项目中。

### 4. 个人维度数据查询 (Individual Developer Queries)

- `getRDDevelopInfo(...)`, `getSingleRDDevelopInfo(...)`, `getRDAllIssueList(...)`: 一系列为单个研发同学提供数据查询的接口。允许机器人或其他工具查询个人名下的需求和 Bug 列表及状态。

### 5. 消息与订阅 (Messaging & Subscriptions)

- `subscribeVersionMessage(...)`: 允许用户或群聊订阅特定版本的相关消息通知。
- `sendVersionProcessMessage(...)`, `sendTrainMessage(...)`: 用于发送各类版本流程中的广播通知，例如“开始提测”、“版本发车”等。

## 使用场景示例

- **版本封版检查**: 在版本提测或封版前，机器人或发布平台调用 `queryStoryProgress`，自动生成一份完整的版本状态报告，并发送到研发大群，供所有人评审。
- **每日站会报告**: 通过定时任务每天早上调用 `remainStoryRiskByVersion` 和 `remainStoryMrMergeByVersion`，将前一天未完成的风险项和未合入的 MR 发送到群里，提醒相关人员跟进。
- **MR 自动检查**: 开发者在提交 MR 后，可以通过机器人指令触发 `checkStoryMrReady`，进行自助检查，确保 MR 满足合入标准，减少人工沟通成本。
- **个人任务查询**: 在飞书机器人中集成 `getSingleRDDevelopInfo`，允许研发同学通过发送指令“我的任务”来快速获取自己当前负责的需求和 Bug 列表。

## 注意事项

- **业务逻辑高度耦合**: 该服务包含了大量与业务流程紧密耦合的逻辑，例如各种状态的判断标准、风险等级的计算规则、飞书卡片的文案和格式等。修改时必须充分理解其上下文，并进行完整测试。
- **硬编码的字段ID**: 代码中存在一些硬编码的 Meego 自定义字段 ID（如 `field_9a70e6`）。如果 Meego 中的配置发生变更，这些代码也需要同步修改。
- **对下游服务的强依赖**: 服务的正确运行严重依赖 `MeegoService`、`BitsService` 和 `LarkService` 的稳定性。在排查问题时，需要综合考虑整个调用链路。
- **命名约定**: `Old` 的后缀提醒我们，在进行新功能开发时，需要思考新增的逻辑是属于业务流程编排（应放在本文件中），还是属于与某个外部系统的原子交互（可能应放在如下游的 `MeegoService` 中）。