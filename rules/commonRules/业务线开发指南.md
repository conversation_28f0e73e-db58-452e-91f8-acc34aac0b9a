# 业务线开发指南
## 目标
业务线的获取，以及业务线选择框的UI
## 方法
### 业务线选择框UI
 <Form.Item name="businessLine" rules={[{ required: true, message: '请选择业务线' }]}>
            <TreeSelect
              dropdownStyle={{ maxHeight: 400, minWidth: 300, overflow: 'auto' }}
              zIndex={2000}
              style={{
                maxWidth: '100%',
                minWidth: '100%',
                border: '1px solid #d9d9d9',
                borderRadius: 6,
                backgroundColor: '#fff',
              }}
              treeData={LineTree}
              multiple={true}
              placeholder={'选择业务线'}
              showClear={true}
            />
          </Form.Item>
### 业务线获取
#### host项目：
import {  queryBusinessTree} from '@api/libraPatorlConfig';
 useEffect(() => {
    queryBusinessTree({
      data: {},
    }).then((res: any) => {
      setLineTree(res);
    });
  }, []);
#### quality项目：
import { getMeegoTreeValue } from '@shared/storyRevenueReviewPlatform/StoryRevenuePlatformUtils';
const res = getMeegoTreeValue('business');