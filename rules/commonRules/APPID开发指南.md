# AppId 开发指南
## 一些参选需求获取APPID，APPID都是固定的，每个APPID对应一个LibraAppId，如果涉及到Libra相关的需求，可能要转化为LibraAppId。
1775: '剪映',
3006: 'CapCut',
177502: '剪映-Android',
177501: '剪映-iOS',
300602: 'CapCut-Android',
300601: 'CapCut-iOS',
3704: '剪映-PC',
359289: 'CapCut-PC',
348188: 'CapCut-Web',
548669: '剪映-Web',
2515: '醒图',
7356: 'Hypic',
581595: '即梦',
513695: '即梦-Web',
getLibraAppIdByAppId(appId: string) {
if (appId === '1775') {
// 剪映 App
return 147;
}
if (appId === '3704') {
// 剪映 PC
return 399;
}
if (appId === '2515') {
// 醒图 App
return 255;
}
if (appId === '3006') {
// CapCut App
return 305;
}
if (appId === '7356') {
// Hypic App
return 367;
}
if (appId === '359289') {
// CapCut PC
return 360;
}
if (appId === '1180') {
// TikTok App
return 22;
}
if (appId === '1128') {
// 抖音 App
return 12;
}
if (appId === '2329') {
// 抖音极速版 App
return 142;
}
if (appId === '348188') {
// CapCut_Web
return 381;
}
if (appId === '548669') {
// jianying_web
return 1071;
}
if (appId === '581595') {
// 即梦 App
return 992;
}
if (appId === '513695') {
// 即梦 Web
return 1021;
}
return 0;
}