# Meego Controller 开发指南

## 简介

`MeegoController` 是“纸飞机”项目中 `host` 应用的核心后端控制器之一，负责处理所有与 Meego 平台相关的业务逻辑和数据交互。它作为 Meego、Bits、Lark 等内部系统之间的桥梁，提供了一系列 API 来支持版本管理、需求跟踪、MR 检查、缺陷管理和自动化通知等功能。

本文档旨在为开发人员提供一个清晰的指南，以便理解和使用 `MeegoController` 的功能。

## 核心依赖

`MeegoController` 依赖于多个内部服务和数据模型来完成其功能：

-   **`MeegoService`**: 用于与 Meego 新版 API 进行交互，处理工作项（如 Story、Issue）的查询和更新。
-   **`MeegoOldService`**: 用于与 Meego 旧版 API 进行交互，部分遗留接口仍在使用。
-   **`BitsService`**: 用于与 Bits 系统（代码托管和 MR 管理）进行交互，主要用于查询 MR 详情和状态。
-   **`LarkService`**: 用于与飞书开放平台进行交互，实现发送文本消息、群聊消息等功能。
-   **`LarkCardService`**: 专门用于构建和处理飞书卡片消息，提供更丰富的消息交互体验。
-   **`VersionProcessInfoDao`**: 数据访问对象，用于查询和操作版本开发流程中的数据。
-   **`SlardarIssueItemTable`**: MongoDB 数据模型，用于存储和管理 Slardar 上报的 Issue 数据。

## API 端点详解

以下是 `MeegoController` 暴露的主要 API 端点及其功能说明。

---

### 1. `POST /queryCurrentIssueState`

-   **功能**: 查询指定版本、业务线和群组下的当前缺陷（Issue）状态。
-   **请求体 (Body)**:
    ```json
    {
      "version": "剪映-Android-8.7.0" | ["剪映-Android-8.7.0"],
      "business": "剪映",
      "group": "剪映Android端",
      "filter_story_ids": [123, 456] // 可选，用于过滤特定需求
    }
    ```
-   **说明**: 用于获取特定版本周期的缺陷概览。

---

### 2. `POST /queryModelIssueFixRate`

-   **功能**: 查询不同模块的缺陷修复率，并提供缺陷视图信息。
-   **请求体 (Body)**:
    ```json
    {
      "version": "剪映-Android-8.7.0",
      "business": "剪映"
    }
    ```
-   **说明**: 用于监控和统计各业务模块的质量状况。

---

### 3. `POST /checkStoryMrReady`

-   **功能**: 自动化检查指定版本下的所有需求（Story）是否都已关联了符合发布标准的 MR。
-   **检查标准**:
    1.  需求必须关联 MR。
    2.  关联的 MR 状态必须是 `OPEN` 或 `MERGED`。
    3.  MR 的目标分支必须是 `develop` 或 `robot.prepare` 分支。
    4.  MR 不能是 `WIP` (Work in Progress) 状态。
-   **请求体 (Body)**:
    ```json
    {
      "version": "剪映-Android-8.7.0"
    }
    ```
-   **说明**: 这是版本封板前的关键检查，确保所有代码都已准备就绪。

---

### 4. `POST /checkFeatureMrReady`

-   **功能**: 检查单个功能型 MR 所关联的需求是否满足合入主干的标准。
-   **检查标准**:
    -   无未解决的 S/P0/P1 级缺陷。
    -   缺陷解决率满足特定标准（例如 >= 85%）或未解决的 P2 级 Bug 数量在可接受范围内。
-   **请求体 (Body)**:
    ```json
    {
      "mrId": 12345,
      "platform": "Android",
      "appid": 1775 // 可选
    }
    ```
-   **说明**: 用于 CI/CD 流程中的卡点，防止不符合质量要求的 MR 被合入。

---

### 5. `POST /queryStoryProgress`

-   **功能**: 查询指定版本和平台下所有需求的进展情况。
-   **请求体 (Body)**:
    ```json
    {
      "versionCode": "8.7.0",
      "platform": "Android"
    }
    ```
-   **说明**: QA 同学用于在封板阶段监控每个需求的具体状态。

---

### 6. `POST /queryHistoryBugProgress`

-   **功能**: 查询历史遗留 Bug 的清理进度，并发送飞书卡片到指定群聊。
-   **请求体 (Body)**:
    ```json
    {
      "team": "剪映iOS全球工具",
      "group": "历史bug测试群",
      "fix_view_id": "MGecXUCVR", // Meego 上的视图 ID
      "condition_view_id": "zo1QQSj4g", // Meego 上的视图 ID
      "personal_goal": 2 // 每人需要修复的目标数量
    }
    ```
-   **说明**: 这是一个定时任务或手动触发的功能，用于推动团队清理技术债。它会统计团队和个人的 Bug 修复进度，并通过飞书卡片进行公示。

---

### 7. `POST /remainStoryRiskByVersion`

-   **功能**: 对指定版本中，缺陷修复率较低的需求负责人（Owner）发送个人预警。
-   **请求体 (Body)**:
    ```json
    {
      "version": "剪映-Android-8.7.0",
      "fixRate": 85.0 // 低于此修复率的需求将被警告
    }
    ```
-   **说明**: 在封板日前自动提醒相关开发人员，以降低版本发布风险。

---

### 8. `POST /remainStoryMrMergeByVersion`

-   **功能**: 对已满足合入要求但尚未合并的 MR 进行催促。
-   **请求体 (Body)**:
    ```json
    {
      "version": "剪映-Android-8.7.0"
    }
    ```
-   **说明**: 自动化流程，旨在加速代码集成，确保版本进度。

---

### 9. `POST /open/auto_add_ve_client_version`

-   **功能**: 这是一个开放给 Meego Webhook 的接口，当 Meego 中的特定工作项发生变更时，会自动调用此接口。
-   **请求体 (Body)**: `MeegoUpdateCallback` 结构
-   **说明**: 该接口会根据 Meego 回调信息，自动为 VE (Video Engine) 客户端添加版本信息，实现了跨系统的自动化流程。

## 关键逻辑说明

### 历史 Bug 进度计算 (`queryHistoryBugProgress`)

该功能的核心逻辑是：
1.  **获取团队成员**：从 Meego 获取指定团队的所有成员。
2.  **获取缺陷列表**：从 Meego 的指定视图（`fix_view_id`）中拉取所有需要处理的缺陷。
3.  **计算总体进度**：统计所有缺陷的完成情况（已解决、已关闭、已拒绝），计算出团队的总体修复率。
4.  **计算个人进度**：将缺陷分配到具体的负责人，并计算每个成员的修复数量和修复率。
5.  **发送飞书卡片**：将统计结果渲染成一个精美的飞书卡片，发送到指定群聊，并@相关人员。

### MR 准备情况检查 (`checkStoryMrReady`)

该功能的核心逻辑是：
1.  **查询版本下的所有需求**：获取指定版本下的所有 Story。
2.  **遍历需求**：对每个 Story 执行以下检查：
    a. **查询关联的 MR**：通过 Bits 服务查询该 Story 关联的所有 MR。
    b. **检查 MR 数量**：如果未关联任何 MR，则将该 Story 记为“未绑定 MR”。
    c. **检查 MR 状态**：如果已关联 MR，则遍历所有 MR，检查是否存在至少一个“有效”的 MR（状态为 OPEN/MERGED，目标分支正确，非 WIP）。如果所有 MR 都不符合要求，则将该 Story 记为“MR 状态错误”。
3.  **返回结果**：最后汇总所有不符合要求的 Story 并返回。

## 如何使用

所有 API 都遵循标准的 HTTP POST 请求格式。您可以使用任何 HTTP 客户端（如 Postman、curl 或代码中的 HTTP 库）来调用这些接口。

**示例 (使用 curl)**:

```bash
curl -X POST http://localhost:8000/api/queryStoryProgress \
-H "Content-Type: application/json" \
-d '{
  "versionCode": "8.7.0",
  "platform": "Android"
}'
```

在开发和调试时，请确保您的本地环境可以访问 Meego、Bits 等下游服务。