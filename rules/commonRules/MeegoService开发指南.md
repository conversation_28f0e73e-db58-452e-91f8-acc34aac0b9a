# Meego Service 开发指南

## 简介

`MeegoService` 是“纸飞机”项目中 `host` 应用的核心后端服务，它充当了与 Meego 平台所有交互的抽象层。该服务封装了 Meego API 的复杂性，统一处理认证、数据查询、工作项更新等操作，使得其他业务模块可以方便地使用 Meego 数据，而无需关心底层的 API 实现细节。

本文档旨在为开发人员提供一个清晰的指南，以便理解和使用 `MeegoService` 的功能。

## 核心概念

-   **认证 (Authentication)**: 与 Meego API 的通信需要一个插件令牌（Plugin Token）。`MeegoService` 通过 `requestMeegoToken` 方法自动处理令牌的获取和缓存（使用 Redis）。所有对外的 API 请求都会自动携带此令牌进行认证。

-   **项目密钥 (Project Key)**: Meego 中的大多数操作都与一个项目（Project）相关联。在我们的代码中，常量 `faceuProjectKey` (`'faceu'`) 被用作许多操作的默认项目密钥。

-   **网络层 (Network Layer)**: `createNetwork` 方法负责创建一个预配置的 `NetworkX` HTTP 客户端实例。该实例包含了正确的 Meego API 基地址和认证头，是所有对 Meego API 请求的基础。

-   **业务线 ID (Business ID)**: 许多查询需要按业务线进行过滤。`businessAllInfoArray` 是一个重要的常量，它硬编码了业务线名称（如“剪映”、“CapCut”）到其在 Meego 中唯一 ID 的映射。

## 核心依赖

-   **`TccService`**: 用于从配置中心获取敏感信息，如 Meego 插件的密钥（`plugin_secret`）。
-   **`RedisClient`**: 用于缓存 Meego API 的访问令牌，避免每次请求都重新获取，提高性能并减少认证次数。
-   **`LarkService` & `LarkCardService`**: 用于向飞书发送消息和卡片，例如在某些自动化流程完成后发送通知。
-   **数据库模型与 DAO**: 如 `VersionProcessInfoTable`, `MajorIssueDao` 等，用于将从 Meego 获取的数据与本地数据库中的业务数据进行关联和持久化。

## 关键方法与使用示例

以下是 `MeegoService` 中最常用和最重要的一些公共方法。

---

### 1. 认证与网络

-   `async createNetwork(userKey?: string): Promise<NetworkX>`
    -   **功能**: 创建并返回一个配置好的 HTTP 客户端，用于后续所有 Meego API 调用。可以传入 `userKey` 来模拟特定用户执行操作。
    -   **内部调用**: 此方法会自动调用 `requestMeegoToken` 来确保请求的有效性。

---

### 2. 通用工作项查询

-   `async requestWorkItem(projectKey: string, workType: string, workItemIds: number[]): Promise<MeegoWorkResult<WorkItemInfo[]>>`
    -   **功能**: 根据工作项 ID 批量获取工作项（如 `story`, `issue`）的详细信息。
    -   **示例**:
        ```typescript
        const stories = await meegoService.requestWorkItem('faceu', 'story', [123, 456]);
        ```

-   `async requestWorkflow(projectKey: string, workType: string, workItemId: string): Promise<MeegoWorkResult<NodesConnections>>`
    -   **功能**: 获取指定工作项的工作流详情，包括所有状态（Nodes）和状态之间的转换关系（Connections）。

---

### 3. 元数据与配置查询

-   `async queryVersionId(projectKey: string, versionName: string): Promise<number>`
    -   **功能**: 根据版本的完整名称（如 `剪映-Android-8.7.0`）精确查询其在 Meego 中的唯一 ID。这是进行版本相关查询的基础，非常常用。

-   `async businessAllInfo(projectKey: string): Promise<MeegoWorkResult<BusinessIdInfo[]>>`
    -   **功能**: 获取一个项目下所有的业务线（Business）列表及其 ID。

-   `async queryWorkItemMeta(projectKey: string, work_item_type_key: string): Promise<MeegoWorkResult<FieldConf[]>>`
    -   **功能**: 获取指定工作项类型（如 `issue`）的元数据，包括所有自定义字段的配置信息。

---

### 4. 复杂查询与搜索

-   `async issueAllSearch(...)`
    -   **功能**: 一个功能强大的缺陷（Issue）搜索方法，支持根据解决版本、优先级、状态等多个维度进行组合查询。它还包含了一些复杂的业务逻辑，例如过滤掉由自动化测试创建的 P3 等级缺陷，或排除某些历史遗留问题。

-   `async queryStoryList(versionName: string): Promise<WorkItemInfo[]>`
    -   **功能**: 查询指定版本下的所有需求（Story）列表。

-   `async getStoryIssueList(storyId: number): Promise<WorkItemInfo[]>`
    -   **功能**: 获取单个需求下关联的所有缺陷列表。

---

### 5. 工作项更新

-   `async updateWorkItem(projectKey: string, workType: string, workItemId: number, params: FieldValuePair[]): Promise<MeegoWorkResult<MeegoWorkItemOperateResult>>`
    -   **功能**: 更新一个工作项的指定字段。`params` 参数是一个包含字段键值对的数组。
    -   **示例**:
        ```typescript
        // 将一个 Story 的状态更新为“已完成”
        await meegoService.updateWorkItem('faceu', 'story', 123, [
          { field_key: 'work_item_status', field_value: 'DONE' }
        ]);
        ```

-   `async autoAddVeClientVersion(body: MeegoUpdateCallback): Promise<NetworkResult<any>>`
    -   **功能**: 这是一个由 Meego Webhook 触发的方法。当 Meego 中的工作项发生变更时，会调用此接口，该方法会根据回调信息自动更新相关工作项，实现了跨系统的自动化流程。

## 重要常量与枚举

-   **`businessAllInfoArray`**: 一个静态映射表，定义了从业务名称到 Meego Business ID 的转换。**注意：当 Meego 中新增业务线或变更 ID 时，可能需要同步更新此常量。**
-   **`MeegoBusinessType`**: 一个枚举类型，提供了强类型的业务线名称，应在代码中优先使用此枚举而非字符串，以提高代码健壮性。

## 如何使用（示例）

假设我们需要在另一个服务中实现一个功能：获取某个版本下所有 P0/P1 等级的未关闭缺陷。

```typescript
// In another service or controller

@Inject()
private meegoService: MeegoService;

async getCriticalBugsForVersion(versionName: string) {
  const projectKey = faceuProjectKey;
  const businessId = businessAllInfoArray[MeegoBusinessType.LV]; // 获取“剪映”的业务 ID

  // 1. 获取版本 ID
  const versionId = await this.meegoService.queryVersionId(projectKey, versionName);
  if (!versionId) {
    throw new Error(`Version not found: ${versionName}`);
  }

  // 2. 定义查询参数
  const priorities = ['0', '1']; // P0 和 P1
  const statuses = ['NEW', 'OPEN', 'RESOLVED']; // 未关闭的状态

  // 3. 调用搜索方法
  const bugs = await this.meegoService.issueSearchByParams(
    projectKey,
    [businessId],
    [versionId],
    priorities,
    statuses
  );

  return bugs;
}
```