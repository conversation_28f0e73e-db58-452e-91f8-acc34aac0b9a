# version API 开发指南
本文档详细介绍了 version.ts 文件中定义的所有 API 接口，这些接口主要负责管理和查询应用的版本信息。
## 文件：
apps/quality/api/lambda/version.ts
## 使用
如果需要调用这些代码，到文件apps/quality/api/lambda/version.ts去详细查看对应的API接口，获取调用参数，在前端进行调用
## API 接口列表
### 1. queryPreviousVersion
- 路径 : POST /versions/previous
- 核心功能 : 查询指定应用和版本的上一个版本信息。
### 2. queryRecentVersions
- 路径 : POST /versions
- 核心功能 : 分页查询最近的版本列表，支持动态过滤和排序。
### 3. queryReleaseVersions
- 路径 : GET /versions/release/:platform
- 核心功能 : 查询所有已发布的版本，可以根据平台进行筛选。
### 4. patchVersion
- 路径 : PATCH /versions/modify
- 核心功能 : 修改指定版本的属性，例如发布时间 ( timestamp ) 或版本类型 ( version_type )。
### 5. getOver2TimeStamp
- 路径 : PATCH /versions/getOver2TimeStamp
- 核心功能 : 根据给定的起始时间戳和数量，计算并获取版本“过曝”的时间点。
### 6. getPreVersionCode
- 路径 : PATCH /versions/getPreVersionCode
- 核心功能 : 获取指定版本的前一个版本号。
### 7. getAppVersionInfo
- 路径 : POST /search/getAppVersionInfo
- 核心功能 : 根据产品线和平台查询版本列表。
## 注意事项
- 依赖注入 : 所有服务和数据模型都通过 @edenx/runtime/bff 的 useInject 进行依赖注入，确保了代码的解耦和可测试性。
- 数据校验 : 所有接口的输入数据都使用了 zod 进行严格的 schema 校验，保证了数据的安全性和一致性。
- iOS 版本逻辑 : 多个接口中包含了对 iOS 版本的特殊处理逻辑（例如通过 versionCode 是否包含 . 来判断），在维护时需要特别注意。