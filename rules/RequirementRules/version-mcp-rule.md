# 需求介绍
提供MCP查询版本等相关信息的能力
# 功能介绍
提问所有的版本相关信息时，需要在消息中带有至少两个参数：
- App名称（如：剪映）
- 平台名称（如：iOS）
版本号为可选参数，如指定版本号，返回特定版本的信息，否则返回正在运行的版本（最多三个）
- 1.查询版本进度
- 2.查询MR能否准入
- 3.查询版本全量/提审时间
- 4.查询版本BM
- 5.查询小版本号
# 开发指南
- 1.借助MCP开发指南
- 2.参考已经完成的版本进度功能apps/host/api/service/mcp/tool-handlers/McpToolVersionHandle.ts
- 3.功能代码放置在apps/host/api/service/mcp/tool-handlers/McpToolVersionHandle.ts中
- 4.参考apps/host/api/service/releasePlatform/versionBotService.ts文件中发送的飞书卡片
- 5，MCP返回的信息也参考飞书卡片去设计