1. 通过 apps/host/api/service/third/gitlab.ts 的 createMr() 接口创建 MR
2. 创建 MR 后，需要生成飞书卡片进行通知，可以通知到个人。
3. 创建和发送飞书卡片的代码，可以参考飞书卡片开发指南
5. 我需要你记录每一个创建的 MR，并记录到 MangoDB 数据库。请设计这样的记录的数据接口，并实现数据库接口。具体可以参考:数据库开发指南
6. 上述能力的实现，需要通过一个前端页面承载，即可以实现 faceu-android/unified_adloader 和 faceu-ios/LVUniteAd 两个仓库的分支通过（以创建 MR 的形式）。将分支 lv/dev merge 到 oversea/dev 分支，并创建 MR。
    - faceu-android/unified_adloader 仓库地址：[Repository] faceu-android/unified_adloader。projectId 为：540548
    - faceu-ios/LVUniteAd 仓库地址：[Repository] faceu-ios/LVUniteAd。projectId 为：540549
7. 创建 MR 的方式，不是直接将 lv/dev merge 到 oversea/dev 分支，而是要先基于 lv/dev 创建一个影子分支，类似参考：apps/host/api/service/tbc/TBCCodeSyncService.ts 的 syncBranch() 的影子分支做法：
   const mappingName = (name: string) => name.split('/').join('_');
   const midBranch = `p/tbc.robot.merge/${mappingName(sourceBranch)}_to_${mappingName(targetBranch)}_${nanoid(6)}`;
   ret = await this.gitlabService.createBranch(projectId, midBranch, sourceBranch);
8. 将基于 lv/dev 创建出来的影子分支，通过 apps/host/api/service/third/gitlab.ts 的 createMr() 接口创建 MR，merge 到 oversea/dev 分支。
7. 每成功创建一个 MR，都需要将它记录到数据库，并发送飞书卡片通知。
8. 前端页面，主要内容就是可以支持展示这些 MR 记录（Table 列表的方式），同时提供按钮可以手动发起 MR merge 操作。前端页面需要基于 Semi-UI 实现。
9. 前端页面的路由页面，请放在 apps/host/src/routes/devops/tbc/ad-code-sync 目录；前端页面的具体实现页面，请放在 apps/host/src/pages/tbc/ad-code-sync 目录。
10. 以上 MR 同步的后端能力实现，统一封装在一个 Service 里面：apps/host/api/service/tbc/TBCAdCodeSyncService.ts
11. 补充：lv/dev 作为 sourceBranch，oversea/dev 作为 targetBranch，但是不要写死，要有一定的灵活性，支持 sourceBranch 和 targetBranch 随时可以配置和替换。
    请给出完整的代码，并在项目过程中完成改动。