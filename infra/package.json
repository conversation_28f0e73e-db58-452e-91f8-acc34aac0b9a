{"name": "infra", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"prepare": "cd .. && husky install infra/git-hooks && git config blame.ignoreRevsFile .git-blame-ignore-revs", "format:ci": "pretty-quick --since HEAD~1", "format": "pretty-quick --staged", "commit": "cz"}, "keywords": [], "author": "", "license": "ISC", "devDependencies": {"@byted/create": "1.19.4", "@byted/prettier-config-standard": "^1.0.0", "@changesets/cli": "2.24.1", "@commitlint/cli": "^17.0.3", "@commitlint/config-conventional": "^17.0.3", "commitizen": "4.2.5", "eslint": "^8.57.0", "husky": "^8.0.1", "lint-staged": "^13.0.3", "prettier": "^2.7.1", "pretty-quick": "3.1.3"}, "config": {"commitizen": {"path": "cz-conventional-changelog"}}, "pnpm": {"overrides": {"@byted-service/consul": "2.1.6", "@byted-service/kmsv2": "2.2.9"}}}