import { appTools, defineConfig } from '@edenx/app-tools';
import { garfishPlugin } from '@edenx/plugin-garfish';
import { bffPlugin } from '@edenx/plugin-bff';
import { guluxPlugin } from '@edenx/plugin-gulux';

// https://edenx.bytedance.net/configure/app/usage
export default defineConfig<'rspack'>({
  dev: {
    port: 8081,
  },
  runtime: {
    router: true,
    state: true,
  },
  bff: {
    timeout: 1000 * 400,
    prefix: `/api/quality/${process.env.MY_REGION}`,
    enableHandleWeb: true,
    separateMode: true,
  },
  deploy: {
    region: process.env.MY_REGION,
    microFrontend: true,
    handleDependencies: true,
    mode: 'standalone',
  },
  output: {
    disableTsChecker: false,
  },
  plugins: [appTools({ bundler: 'experimental-rspack' }), bffPlugin(), guluxPlugin(), garfishPlugin()] as any,
  experiments: {
    sourceBuild: true,
  },
});
