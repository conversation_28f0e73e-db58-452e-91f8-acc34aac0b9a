// 熔断工单状态
export enum CircuitBreakerTicketStatus {
  NotStart = 1, // 未跟进
  Processing = 2, // 跟进中
  Closed = 3, // 结束跟进
}

// 熔断工单类型
export enum CircuitBreakerTicketType {
  Slardar = 1,
  Feedback = 2,
}

// 熔断状态
export enum CircuitBreakerStatus {
  UnConfirmed = 1, // 待确认
  Confirmed = 2, // 确认
  Mistake = 3, // 误报
  Missed = 4, // 漏报
}

// 熔断issue解决状态
export enum CircuitBreakerIssueStatus {
  UnResolved = 1, // 未处理
  Resolved = 2, // 已处理
  NotAffected = 3, // 无需处理
}

// 熔断原因类型
export enum CircuitBreakerReason {
  ClientCodeChange = 1, // 客户端代码变更
  ExperimentChange = 2, // 实验发布/变更
  LynxPublish = 3, // Lynx发布
  ServerCodeChange = 4, // 服务端代码变更
  Mistake = 5, // 误报-作弊/黑产
  Other = 6, // 其他
  CommonIncrease = 7, // 普涨-无新增问题
}

export enum CircuitBreakerStopLossMean {
  Rollback = 1, // 回滚发布
  StopGray = 2, // 停止本轮灰度
  StopExperiment = 3, // 暂停实验止损
  CloseLynx = 4, // 关闭Lynx开关
  NoNeedHandle = 5, // 无需处理
  Other = 6, // 其他
}

export enum CircuitBreakerOSType {
  Android = 'Android',
  iOS = 'iOS',
  Windows = 'Windows',
  Mac = 'Mac',
}

export interface CircuitBreakerTicket {
  ticketId: string;
  ticketStatus: CircuitBreakerTicketStatus; // 工单状态
  ticketType: CircuitBreakerTicketType; // 工单类型
  affectGray: boolean; // 是否影响灰度
  circuitBreakerStatus?: CircuitBreakerStatus; // 熔断状态
  circuitBreakerIssueStatus?: CircuitBreakerIssueStatus; // 熔断issue解决状态
  circuitBreakerReason?: CircuitBreakerReason; // 工单告警原因类型
  circuitBreakerDetailReason?: string; // 工单告警详细原因
  circuitBreakerStopLossMean?: CircuitBreakerStopLossMean; // 止损手段
  remark?: string; // 备注
  meegoLink?: string; // meego链接

  ticketCreateTime: number; // 工单创建时间
  ticketStartHandleTime?: number; // 工单首次进入处理时间
  ticketUpdateTime: number; // 工单更新时间
  ticketCloseTime?: number; // 工单关闭时间
  ticketHandler?: string; // 工单当前处理人

  appId: number;
  version: string;
  versionCode: string;
  os: CircuitBreakerOSType;
}

export enum SlardarCircuitBreakerAlarmLevel {
  fatal = 'fatal',
  warning = 'warning',
  notice = 'notice',
}

export enum SlardarCircuitBreakerCrashType {
  // Android
  JavaStartCrash = 'JavaStartCrash',
  JavaCrash = 'JavaCrash',
  NativeCrash = 'NativeCrash',
  ANR = 'ANR',

  // iOS
  OOMCrash = 'OOMCrash',
  Crash = 'Crash',
  WatchDog = 'WatchDog',

  Unknown = 'Unknown',
}

export interface SlardarCircuitBreakerTicket extends CircuitBreakerTicket {
  alarmRuleId: number;
  alarmType: SlardarCircuitBreakerCrashType;
  alarmLevel: SlardarCircuitBreakerAlarmLevel;
  alarmCount: number;
  alarmRuleName: string;
  alarmStartTime: number; // 崩溃首次告警时间
}

export enum FeedbackCircuitBreakerLevel {
  P0 = 1,
  P1 = 2,
  P2 = 3,
}

export interface FeedbackCircuitBreakerTicket extends CircuitBreakerTicket {
  feedbackDescribe: string; // 反馈描述
  feedbackLink: string; // 反馈链接
  feedbackLevel: FeedbackCircuitBreakerLevel; // 反馈等级
  feedbackType: string; // 问题类型
  feedbackStartTime: number; // 反馈开始时间
  feedbackUserCount: number; // 反馈用户数
}

export const getFeedbackLevel = (level: FeedbackCircuitBreakerLevel) => {
  switch (level) {
    case FeedbackCircuitBreakerLevel.P0:
      return 'P0';
    case FeedbackCircuitBreakerLevel.P1:
      return 'P1';
    case FeedbackCircuitBreakerLevel.P2:
    default:
      return 'P2';
  }
};

export enum CircuitBreakerCallbackActionType {
  StartProcess = 1,
  NotNeedProcess = 2,
  FinishProcess = 3,
  FinishTicket = 4,
}
