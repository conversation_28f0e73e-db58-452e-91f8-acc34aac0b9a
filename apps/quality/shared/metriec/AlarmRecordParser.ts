import { QualityAlarmOrderRecord } from '@shared/metriec/order/QualityAlarmOrderRecord';

export class AlarmRecordParser {
  static parse(dataList: any) {
    if (!Array.isArray(dataList)) {
      return {
        success: true,
        total: 0,
        data: [],
      };
    }
    const convertDataList: QualityAlarmOrderRecord[] = dataList.map((data, index) => ({
      appId: data.appId,
      platformType: data.platformType,
      displayName: data.displayName,
      versionCode: data.versionCode,
      owner: data.owner,
      recordId: data.recordId,
      metricName: data.metricName,
      business: data.business,
      businessName: data.businessName,
      timestamp: data.timestamp,
      orderStatus: data.orderStatus,
      historyItems: data.historyItems,
      teaUrl: data.teaUrl,
    }));

    return {
      success: true,
      total: convertDataList.length,
      data: convertDataList,
    };
  }
}
