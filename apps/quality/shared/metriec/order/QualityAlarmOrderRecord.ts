import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import { PlatformType } from '@pa/shared/dist/src/core';
import { HistoryItem, OrderStatus } from '@shared/typings/tea/alarm';
import { Business } from '@shared/typings/tea/metric';

export interface QualityAlarmOrderRecord {
  appId: string;
  platformType: PlatformType;
  business: Business;
  businessName: string;
  versionCode: string;
  displayName: string;
  owner: string;
  recordId: string;
  metricName: string;
  timestamp: number;
  orderStatus: OrderStatus;
  groupId?: string;
  historyItems?: HistoryItem[];
  teaUrl?: string;
}

export function formatAlarmTime(ts: number) {
  dayjs.extend(utc);
  return dayjs.unix(ts).utcOffset(8).format('YYYY-MM-DD HH:mm:ss');
}
