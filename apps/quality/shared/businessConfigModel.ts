import { BusinessType, ProductType } from '@pa/shared/dist/src/appSettings/appSettings';
import { PlatformType } from '@pa/shared/dist/src/core';

export interface BusinessAppInfo {
  app_name: string;
  platform: PlatformType;
  bits_workspace: number;
  app_id: number;
  product_type: ProductType;
  calendar_segment_list: string[];
  group_name: string;
  business_id: BusinessType;
  route_config: RouteModule;
  icon: string;
  tea_app_id: number;
  oversea: boolean;
  aid: number;
}

export interface BusinessConfig {
  business_id: number;
  app_list: BusinessAppInfo[];
}

export interface Route {
  path?: string;
  name?: string;
  icon?: React.ReactNode;
  routes?: Route[];
  defaultPath?: string;
  rootPath?: string;
  hideInMenu?: boolean;
}

export interface RouteModule {
  [key: string]: Route | undefined;
  releaseProcess?: Route;
  quality?: Route;
  tools?: Route;
  abtest?: Route;
  settings?: Route;
}
