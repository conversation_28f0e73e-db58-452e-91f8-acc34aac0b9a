export interface CalendarData {
  Tracks: TracksItem[];
  SingleDayEventTypes: SingleDayEventTypesItem[];
}
export interface DataItem {
  event_id: number;
  track_id: number;
  name: string;
  start_date: string;
  calc_end_date: string;
  title_note: string;
  item: string;
  event_name: string; // 日历事件的版本号
}
export interface Event {
  event_id: number;
  track_id: number;
  name: string;
  start_date: string;
  calc_end_date: string;
  title_note: string;
}
export interface EventsItem {
  event: Event;
  segments: SegmentsItem[];
}
export interface GetOpenapiCalendarWorkspaceIdResponse {
  code: number;
  data: CalendarData;
  message: string;
}
export interface ItemsItem {
  item_id: number;
  template_id: number;
  length: number;
  holiday_event: number;
  phase: number;
  name: string;
  color: string;
  note: string;
}
export interface SegmentsItem {
  segment_id: number;
  length: number;
  event_id: number;
  holiday_event: number;
  name: string;
  identifier: string;
  color: string;
  note: string;
  calc_start_date: string;
  calc_end_date: string;
}
export interface SingleDayEventType {
  type_id: number;
  workspace_id: number;
  hidden: number;
  type_name: number;
  type_shape: number;
  type_color: number;
}
export interface SingleDayEventTypesItem {
  single_day_event_type: SingleDayEventType;
  single_day_events: SingleDayEventsItem[];
}
export interface SingleDayEventsItem {
  sd_event_id: number;
  event_type_id: number;
  event_content: string;
  date: string;
}
export interface Template {
  template_id: number;
  track_id: number;
  template_name: number;
}
export interface TemplatesItem {
  template: Template;
  items: ItemsItem[];
}
export interface Track {
  track_id: number;
  workspace_id: number;
  associated_track: number;
  association_offset: number;
  hidden: number;
  name: string;
  color: string;
  template: string;
  association_direction: string;
  track_group: string;
  end_days: string;
}
export interface TracksItem {
  track: Track;
  events: EventsItem[];
  templates: TemplatesItem[];
}

export interface mrPackage {
  code: number;
  data: string;
  message: string;
}
