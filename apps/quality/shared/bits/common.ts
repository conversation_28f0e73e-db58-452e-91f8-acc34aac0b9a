export interface BitsResult<T> {
  code: number;
  message: string;
  data?: T;
}

export interface VersionGroup {
  have_group: boolean;
  group_number: string;
  group_name: string;
  have_version_close_group: boolean;
  bot_mapping: Record<string, string>;
  baseResp: {
    StatusMessage: string;
    StatusCode: number;
  };
}

export interface StageInfosItem {
  /**
   *stage名称（用户自定义）
   */
  stage_name: string;
  /**
   *	StageStatus_NOT_STARTED StageStatus = 1
   *	StageStatus_RUNNING     StageStatus = 2
   *	StageStatus_SUCCESS     StageStatus = 3
   *	StageStatus_FAILED      StageStatus = 4
   *	StageStatus_SKIPPED     StageStatus = 5
   *	StageStatus_STOPPED     StageStatus = 6
   */
  stage_status: number;
  /**
   *时间戳
   */
  start_time?: number;
  /**
   *时间戳
   */
  end_time?: number;
  stage_id: string;
  /**
   *StageType_CUSTOM                      StageType = 0 自定义
   *	StageType_BITS_INTEGRATION_REGRESSION StageType = 1  集成&回归
   *	StageType_BITS_PUBLIC_TEST            StageType = 2 测试
   *	StageType_BITS_GRAY                   StageType = 3 灰度
   *	StageType_BITS_OFFICIAL               StageType = 4 正式
   *
   */
  stage_type?: number;
}
