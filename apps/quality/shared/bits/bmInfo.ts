export interface BmInfo {
  version: string;
  email: string;
  type: BmType;
  master_group: number;
}

export enum BmType {
  rd = 1,
  qa = 2,
  da = 3,
  crash = 101,
  ioswatchdog = 1280,
  iosoom = 1281,
  java_crash = 1286,
  native_crash = 1287,
  oom_crash = 1288,
  anr_crash = 1289,
}

export interface Duty {
  module: string;
  owner: string;
  backup: string;
}

export interface OnCall {
  business: string;
  duty: Duty[];
}

export interface VEOnCallInfo {
  oncall: OnCall[];
}
