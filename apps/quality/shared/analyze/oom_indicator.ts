export interface OOMIndicatorAnalyzeParams {
  target_version: string;
  target_version_code: string;
  target_version_begin: number;
  base_version: string;
  base_version_code: string;
  base_version_begin: number;
}

export interface ConclusionInfo {
  name: string;
  content: string;
  extra: string;
}

export interface CommonResult {
  conclusions: ConclusionInfo[];
}

export interface JavaResult {
  conclusions: ConclusionInfo[];
}

export interface NativeResult {
  conclusions: ConclusionInfo[];
}
