// Meego 与 Libra 映射关系信息（StoryRevenueMeegoLibraMapInfo）
import { User } from '@pa/shared/dist/src/core';
import { CopyUser } from '@shared/storyRevenueReviewPlatform/StoryRevenueTaskInfo';

export enum StoryRevenueLibraInfoSourceType {
  FromLibra = 0, // 从 Libra 获取
  FromManual = 1, // 手动录入
}

export class StoryRevenueLibraInfo {
  appId: string; // app id
  flightId: number; // 实验 id
  startTime: number; // 实验开始时间
  endTime: number; // 实验结束时间
  duration: number; // 实验时长(秒)
  status: number; // 实验状态
  owners: CopyUser[]; // 实验 Owner(可以多个)
  displayName: string; // 实验名称
  region: string; // 实验地区 cn、sg、va
  type: StoryRevenueLibraInfoSourceType; // 来源类型
}

export interface StoryRevenueMeegoLibraMapInfo {
  meegoId: string; // Meego Id
  libraInfos: StoryRevenueLibraInfo[]; // Meego 关联的 Libra 实验
}
