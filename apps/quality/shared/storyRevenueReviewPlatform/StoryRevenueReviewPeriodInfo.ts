export enum StoryRevenueReviewPeriodUpdateType {
  Add = 1,
  Update = 2,
  Delete = 3,
}

export enum StoryRevenueReviewPeriodStatus {
  Unlocked = 0, // 未锁定
  Locked = 1, // 锁定
}

export const MEEGO_BENEFITS_REVIEW_FIELD = {
  // 参与收益review周期
  field_c4fe21: {
    field_key: 'field_c4fe21',
    field_value: [
      {
        label: '2024Q3',
        value: 'b9zzelc_w',
      },
      {
        label: '2024Q4',
        value: 'ex587c0j5',
      },
      {
        label: '2025Q1',
        value: 'xyuifq81f',
      },
      {
        label: '2025Q2',
        value: '6evrt9kov',
      },
      {
        label: '2025Q3',
        value: '0ud45968i',
      },
      {
        label: '2025Q4',
        value: 'n6v46hkt0',
      },
    ],
  },
  // 收益review状态
  field_21cf3c: {
    field_key: 'field_21cf3c',
    field_value: [
      {
        label: '已review',
        value: '3qi2y30ai',
      },
      {
        label: '待review',
        value: '56flfga06',
      },
      {
        label: '仅统计',
        value: '9rui5j5p2',
      },
    ],
  },
};

// Meego 中存储的 Review 周期信息
export interface MeegoReviewPeriodInfo {
  field_key: string;
  field_value: {
    label: string;
    value: string;
  };
}

// 需求收益 Review 周期信息（StoryRevenueReviewPeriodInfo）
export interface StoryRevenueReviewPeriodInfo {
  reviewPeriodId: string; // Review 周期（唯一 ID）
  reviewPeriodName: string; // Review 周期名称
  /**
   * @deprecated status 属性已过期，请使用 locked 来表示锁定、未锁定状态
   */
  status: StoryRevenueReviewPeriodStatus; // 【已过期！！！】状态（锁定、未锁定）
  meegoViewUrl: string; // 收益 Meego 视图链接
  locked: boolean; //  当前版本是否锁定（编辑锁定，即非管理员、非 DS 无法编辑）
  isDefault?: boolean; // 是否是默认版本
  lastLockTime?: number; // 上次锁定时间
  lastUnlockTime?: number; // 上次解锁时间
  dsLocked?: boolean; // DS 是否锁定（数据存档锁定，即最终锁定状态）
  dsLockTime?: number; // DS 锁定周期时间
  isSyncingDataToMeego?: boolean; // 是否正在将数据回写到 Meego 中
  meegoReviewPeriodInfo?: MeegoReviewPeriodInfo; // Meego 中存储的 Review 周期信息
  updating?: boolean; // 是否正在更新中（比如创建完 Review 周期后批量生成 Task Info）
  enableSyncDataFromOtherPeriod?: boolean; // 是否允许跨周期同步数据
}
