import { User } from '@pa/shared/dist/src/core';
import { StoryRevenueTableColumnConfig } from '@shared/storyRevenueReviewPlatform/StoryRevenueUserSettings/StoryRevenueTableColumnUserSetting';

export interface StoryRevenueTaskInfoEditAction {
  editor: User;
  edit_time: number;
  old_value: any;
  new_value: any;
}

export interface StoryRevenueTaskInfoEditLog {
  task_meego_id: string;
  info_key_path: string;
  column_config: StoryRevenueTableColumnConfig;
  period_id: string;
  actions: StoryRevenueTaskInfoEditAction[];
}
