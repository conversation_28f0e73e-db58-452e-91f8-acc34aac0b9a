export enum StoryRevenuePermission {
  View = 1 << 0, // 001 可查看
  Edit = 1 << 1, // 010 可编辑
  Export = 1 << 2, // 100 可导出
  Grant = 1 << 3, // 1000 可授权
  Remind = 1 << 4, // 10000 可催促
}

export enum StoryRevenueUserMemberUpdateType {
  Add = 1,
  Update = 2,
  Delete = 3,
}

export enum StoryRevenueRoleType {
  Guest = 0, // 无权限
  NormalUser = StoryRevenuePermission.View, // 普通身份可查看
  ProductManager = StoryRevenuePermission.View | StoryRevenuePermission.Edit, // 产品身份可查看、可编辑
  DataAnalyst = StoryRevenuePermission.View | StoryRevenuePermission.Edit | StoryRevenuePermission.Export, // 数据分析师可查看、可编辑、可导出
  PMO = StoryRevenuePermission.View |
    StoryRevenuePermission.Edit |
    StoryRevenuePermission.Export |
    StoryRevenuePermission.Remind, // PMO身份可导出、可编辑、可查看、可催促
  Admin = StoryRevenuePermission.View |
    StoryRevenuePermission.Edit |
    StoryRevenuePermission.Export |
    StoryRevenuePermission.Grant |
    StoryRevenuePermission.Remind, // 管理员身份可授权、可导出、可编辑、可查看、可催促
}

export interface StoryRevenueUserMemberInfo {
  email: string;
  roleType: StoryRevenueRoleType;
  name: string;
  avatar?: string;
  openId: string;
}
