import {
  CopyUser,
  StoryRevenueLTMetricLossType,
  StoryRevenuePublishedAppRevenueInfo,
  StoryRevenueSignificanceType,
  StoryRevenueTaskInfo,
  StoryRevenueTaskRevenueConclusionInfo,
  StoryRevenueTaskRevenueExpectationType,
  StoryRevenueTaskRevenueReviewType,
  StoryRevenueTaskRevenueSelfAssessmentType,
  StoryRevenueTaskSubExperimentInfo,
  StoryRevenueTaskSubExperimentStatus,
} from '@shared/storyRevenueReviewPlatform/StoryRevenueTaskInfo';
import ExcelJS from 'exceljs';
import { saveAs } from 'file-saver';
import { User } from '@pa/shared/dist/src/core';
import { StoryRevenueReviewPeriodInfo } from '@shared/storyRevenueReviewPlatform/StoryRevenueReviewPeriodInfo';

interface ExcelHeaderProps {
  header: string;
  key: string;
  width?: number;
}

export class StoryRevenueExcelExporter {
  taskInfoList: StoryRevenueTaskInfo[];
  reviewPeriodName?: string;
  reviewPeriodIdMapInfo?: Map<string, StoryRevenueReviewPeriodInfo>;

  // 构造方法
  constructor(
    taskInfoList: StoryRevenueTaskInfo[],
    reviewPeriodName?: string,
    reviewPeriodIdMapInfo?: Map<string, StoryRevenueReviewPeriodInfo>,
  ) {
    this.taskInfoList = taskInfoList;
    this.reviewPeriodName = reviewPeriodName;
    this.reviewPeriodIdMapInfo = reviewPeriodIdMapInfo;
  }

  // 收益 Review Type 显示名称
  private getStoryRevenueReviewTypeDisplayName(reviewType?: StoryRevenueTaskRevenueReviewType): string {
    if (reviewType === undefined) {
      return '';
    }
    switch (reviewType) {
      case StoryRevenueTaskRevenueReviewType.ParticipateInTheEvaluation:
        return '参与评估（能AA/AB严谨量化表现）';
      case StoryRevenueTaskRevenueReviewType.NotParticipateForExperimentNotOpen:
        return '不参与-优化迭代但未开实验';
      case StoryRevenueTaskRevenueReviewType.NotParticipateForNoConclusionYet:
        return ' 不参与-已开实验但尚无结论';
      case StoryRevenueTaskRevenueReviewType.NotParticipateForBugFix:
        return '不参与-bug修复';
      case StoryRevenueTaskRevenueReviewType.NotParticipateForSecurityCompliance:
        return '不参与-安全合规降负反馈类';
      case StoryRevenueTaskRevenueReviewType.NotParticipateForDataInfrastructure:
        return '不参与-数据基建/产品基建';
      case StoryRevenueTaskRevenueReviewType.NotParticipateForStoryUnderDevelopment:
        return '不参与-需求开发中';
      case StoryRevenueTaskRevenueReviewType.NotParticipateForStoryIsPending:
        return '不参与-需求pending';
      case StoryRevenueTaskRevenueReviewType.NotParticipateForLongTermCapacityBuilding:
        return '不参与-长期建设能力（后续需要参与评估）';
      case StoryRevenueTaskRevenueReviewType.NotParticipateForNegativeIssueGovernanceAndExperienceOpt:
        return '不参与-负向问题治理&体验优化';
      default:
        return '';
    }
  }

  // 预期类型（超出预期、符合预期、低于预期）显示名称
  private getStoryRevenueExpectationTypeDisplayName(expectationType?: StoryRevenueTaskRevenueExpectationType): string {
    if (expectationType === undefined) {
      return '';
    }
    switch (expectationType) {
      case StoryRevenueTaskRevenueExpectationType.ExceedExpectations:
        return '超出预期';
      case StoryRevenueTaskRevenueExpectationType.MeetExpectations:
        return '符合预期';
      case StoryRevenueTaskRevenueExpectationType.BelowExpectations:
        return '低于预期';
      default:
        return '';
    }
  }

  // 显著性描述显示名称
  private getStoryRevenueSignificanceDisplayName(significance: StoryRevenueSignificanceType): string {
    switch (significance) {
      case StoryRevenueSignificanceType.Positive:
        return '正向显著';
      case StoryRevenueSignificanceType.Negative:
        return '负向显著';
      case StoryRevenueSignificanceType.None:
        return '不显著';
      default:
        return '';
    }
  }

  // 是/否-显示名称
  private getStoryRevenueBooleanDisplayName(value?: boolean): string {
    if (value === undefined || value === null) {
      return '';
    }
    return value ? '是' : '否';
  }

  // 0/1 - 显示名称
  private getStoryRevenueBooleanFlagDisplayName(value?: boolean): string {
    if (value === undefined || value === null) {
      return '';
    }
    return value ? '1' : '0';
  }

  // 大盘指标折损显示名称
  private getLTMetricLossDisplayName(ltMetricLossType?: StoryRevenueLTMetricLossType): string {
    if (ltMetricLossType === undefined || ltMetricLossType === null) {
      return '';
    }
    switch (ltMetricLossType) {
      case StoryRevenueLTMetricLossType.Experience:
        return '体验负向';
      case StoryRevenueLTMetricLossType.Business:
        return '收入负向';
      default:
        return '';
    }
  }

  // 收益指标结论
  private getStoryRevenueConclusionDisplayName(
    revenueMetricAutoComputedConclusion?: StoryRevenueTaskRevenueConclusionInfo[],
    revenueMetricConclusion?: string,
  ): string {
    if (revenueMetricConclusion) {
      // 优先读取人工编辑结论
      return revenueMetricConclusion;
    } else if (revenueMetricAutoComputedConclusion) {
      // 再读取 platformRevenueMetricAutoComputedConclusion（由 Libra 自动计算得出）
      let displayConclusion = '';
      for (const conclusion of revenueMetricAutoComputedConclusion) {
        let metricInfoStr = '';
        for (const metricInfo of conclusion.significanceValues) {
          metricInfoStr += `指标组名：${metricInfo.metricGroupName}\n`;
          metricInfoStr += `指标名：${metricInfo.metricIdName}\n`;
          metricInfoStr += `显著性：${this.getStoryRevenueSignificanceDisplayName(metricInfo.significance)}\n`;
          metricInfoStr += `相对差：${metricInfo.relativeDiff}\n`;
          metricInfoStr += `绝对差：${metricInfo.absoluteDiff}\n`;
          metricInfoStr += `P值：${metricInfo.pValue}\n`;
        }
        displayConclusion += `vid: ${conclusion.vid}, 是否有收益: ${conclusion.hasRevenue ? '1' : '0'}，显著性指标：\n${metricInfoStr}`;
      }
      return displayConclusion;
    }
    return '';
  }

  // 收益自评显示名称
  private getRevenueSelfAssessmentDisplayName(
    expectationType?: StoryRevenueTaskRevenueExpectationType,
    revenueSelfAssessment?: StoryRevenueTaskRevenueSelfAssessmentType,
  ): string {
    if (revenueSelfAssessment && expectationType === StoryRevenueTaskRevenueExpectationType.ExceedExpectations) {
      return '超出预期';
    }
    if (revenueSelfAssessment === undefined) {
      return '';
    }
    switch (revenueSelfAssessment) {
      case StoryRevenueTaskRevenueSelfAssessmentType.BelowExpectationsAndContinuousOptimization:
        return '后续继续优化（产品决策正常）';
      case StoryRevenueTaskRevenueSelfAssessmentType.BelowExpectationsAndStopTrying:
        return '后续停止尝试（产品决策正常）';
      case StoryRevenueTaskRevenueSelfAssessmentType.BelowExpectationsForPMWrongDecision:
        return '产品决策失误';
      case StoryRevenueTaskRevenueSelfAssessmentType.MeetExpectationsForNegativeGovernance:
        return '负向治理（体验优化）';
      case StoryRevenueTaskRevenueSelfAssessmentType.MeetExpectationsForLongTermValue:
        return '长期价值（长期收益更大/基础能力建设）';
      case StoryRevenueTaskRevenueSelfAssessmentType.MeetExpectationsForLongTermExploration:
        return '长期探索（AI类需要保持耐心探索）';
      case StoryRevenueTaskRevenueSelfAssessmentType.MeetExpectationsForVerticalScenario:
        return '垂直场景（高级功能/渗透低的场景，不预期撬动端or模块）';
      default:
        return '';
    }
  }

  // 实验状态显示名称
  private getStoryRevenueExperimentStatusDisplayName(status?: StoryRevenueTaskSubExperimentStatus): string {
    if (status === undefined) {
      return '';
    }
    switch (status) {
      case StoryRevenueTaskSubExperimentStatus.Ended:
        return '已结束';
      case StoryRevenueTaskSubExperimentStatus.InProgress:
        return '进行中';
      case StoryRevenueTaskSubExperimentStatus.ToBeScheduled:
        return '待调度';
      case StoryRevenueTaskSubExperimentStatus.InDebug:
        return '调试中';
      case StoryRevenueTaskSubExperimentStatus.Paused:
        return '已暂停';
      case StoryRevenueTaskSubExperimentStatus.ToBeScheduledEnded:
        return '待调度结束';
      case StoryRevenueTaskSubExperimentStatus.Released:
        return '已上线';
      default:
        return '';
    }
  }

  // 日期转换为 yyyy-MM-dd 格式
  private formatUnixTimestamp(unixTimestamp: number, isMillisecond = false): string {
    let date = new Date(unixTimestamp * 1000); // 将秒转换为毫秒
    if (isMillisecond) {
      date = new Date(unixTimestamp); // 毫秒
    }
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0'); // 月份从0开始，所以要加1
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  }

  // 生成超链接
  private getHyperLink(name: string, url: string): string {
    return `=HYPERLINK("${url}", "${name}")`;
  }

  // 生成用户显示字符串
  private getUsersDisplayName(users: CopyUser[]): string {
    const result: string[] = [];
    if (!users) {
      return '';
    }
    for (const user of users) {
      if (user === null || user === undefined) {
        continue;
      }
      const cn_name = user.name ?? '';
      const en_name = user.email ? user.email.replace('@bytedance.com', '') : '';
      if (cn_name.length > 0 && en_name.length > 0) {
        result.push(`${cn_name}(${en_name})`);
      }
    }
    return result.join();
  }

  // 表头
  private genHeaderValues(): ExcelHeaderProps[] {
    const values: ExcelHeaderProps[] = [];

    // 【重要！】请勿调整顺序，否则会导致表格数据对不齐
    // 序号
    values.push({
      header: '序号',
      key: 'serialNumber',
    });
    // task基础信息-收益task_id
    values.push({
      header: '收益task_id',
      key: 'taskId',
      width: 20,
    });
    // 需求信息-需求名称(Meego链接)
    values.push({
      header: '需求信息',
      key: 'meegoTitle',
      width: 40,
    });
    // 需求信息-优先级
    values.push({
      header: '优先级',
      key: 'meegoPriority',
    });
    // 需求信息-Owner
    values.push({
      header: 'Owner',
      key: 'meegoOwner',
    });
    // 需求信息-业务线
    values.push({
      header: '业务线',
      key: 'meegoBusinessLine',
    });
    // 需求信息-子模块
    values.push({
      header: '子模块',
      key: 'meegoSubmodules',
    });
    // 需求信息-RD工作量
    values.push({
      header: 'RD工作量',
      key: 'meegoRDWorkload',
    });
    // 收益信息-是否参与review
    values.push({
      header: '是否参与季度需求收益评估',
      key: 'reviewType',
    });
    // 收益信息-参与review周期
    values.push({
      header: '参与review周期',
      key: 'reviewPeriod',
    });
    // 实验信息-子实验数量
    values.push({
      header: '实验数量',
      key: 'subExperimentCount',
    });
    // 实验信息-关联实验链接
    values.push({
      header: '关联实验链接',
      key: 'libraUrl',
    });
    // 收益信息-收益自评
    values.push({
      header: '收益自评',
      key: 'expectationType',
    });
    // 收益信息-自评原因
    values.push({
      header: '自评原因',
      key: 'revenueSelfAssessment',
    });
    // 收益信息-有端收益
    values.push({
      header: '有端收益(PM)',
      key: 'hasPlatformRevenue',
    });
    // 收益信息-有端收益
    values.push({
      header: '有端收益(DS)',
      key: 'dsHasPlatformRevenue',
    });
    // 收益信息-端收益指标结论
    values.push({
      header: '端收益指标详情',
      key: 'platformRevenueConclusion',
    });
    // 收益信息-有模块收益
    values.push({
      header: '有模块收益(PM)',
      key: 'hasModuleRevenue',
    });
    // 收益信息-有模块收益
    values.push({
      header: '有模块收益(DS)',
      key: 'dsHasModuleRevenue',
    });
    // 收益信息-模块收益指标结论
    values.push({
      header: '模块收益指标详情',
      key: 'moduleRevenueConclusion',
    });
    // 收益信息-有关键过程指标收益
    values.push({
      header: '有关键过程收益(PM)',
      key: 'hasKeyProcessRevenue',
    });
    // 收益信息-有关键过程指标收益
    values.push({
      header: '有关键过程收益(DS)',
      key: 'dsHasKeyProcessRevenue',
    });
    // 收益信息-过程指标收益结论
    values.push({
      header: '过程指标收益详情',
      key: 'keyProcessRevenueConclusion',
    });
    // 是否有指标损失
    values.push({
      header: '有指标损失',
      key: 'hasMetricLoss',
    });
    // DS修正收益判断差异说明
    values.push({
      header: 'DS修正收益判断差异说明',
      key: 'dsRevenueConclusionRemark',
    });
    // 收益信息-是否上线/推全
    values.push({
      header: '本次是否计划上线/推全',
      key: 'isFullyRelease',
    });
    // 收益信息-备注
    values.push({
      header: '备注',
      key: 'revenueRemark',
    });
    // task基础信息-task填写完整性
    values.push({
      header: 'task填写完整性',
      key: 'taskFillInCompleted',
    });
    // 实验信息-关联实验状态
    values.push({
      header: '关联实验状态',
      key: 'libraStatus',
    });
    // 实验信息-关联实验开启时间
    values.push({
      header: '关联实验开启时间',
      key: 'libraStartDate',
    });
    // 实验信息-关联实验关闭时间
    values.push({
      header: '关联实验关闭时间',
      key: 'libraEndDate',
    });
    // 需求信息-上线区域
    values.push({
      header: '上线区域',
      key: 'publishedRegions',
    });
    // 需求信息-上线应用
    values.push({
      header: '上线应用',
      key: 'publishedApps',
    });
    // 需求信息-需求文档
    values.push({
      header: '需求文档',
      key: 'meegoPrdUrl',
    });
    // 需求信息-涉及研发团队
    values.push({
      header: '涉及研发团队',
      key: 'meegoRelatedRDTeams',
    });
    // 需求信息-技术Owner
    values.push({
      header: '技术Owner',
      key: 'meegoTechOwners',
    });
    // 需求信息-需求状态
    values.push({
      header: '需求状态',
      key: 'meegoStatus',
    });
    // 需求信息-需求测试完成时间
    values.push({
      header: '需求测试完成时间',
      key: 'meegoTestFinishedTime',
    });
    // 需求信息-需求内审完成时间
    values.push({
      header: '需求内审完成时间',
      key: 'meegoInternalReviewFinishedTime',
    });
    // 需求信息-创建人
    values.push({
      header: '创建人',
      key: 'meegoCreator',
    });
    // 需求信息-meego_id
    values.push({
      header: 'meego_id',
      key: 'meegoId',
    });
    // task基础信息-task终止
    values.push({
      header: 'task终止',
      key: 'taskTerminated',
    });
    // task基础信息-收益review时长
    values.push({
      header: '收益review时长',
      key: 'reviewDuration',
    });
    // 需求收益复盘文档
    values.push({
      header: '需求收益复盘文档',
      key: 'reviewDocUrl',
    });
    // DS review人
    values.push({
      header: 'DS review人',
      key: 'dsReviewer',
    });
    // 是否为分析依据
    values.push({
      header: '是否为分析依据',
      key: 'isAnalysisBasis',
    });
    // 需求类型（产品需求 or 技术需求）
    values.push({
      header: '需求类型',
      key: 'meegoType',
    });
    // 大盘指标折损
    values.push({
      header: '大盘指标折损',
      key: 'ltMetricLossType',
    });
    // 是否与算法相关
    values.push({
      header: '算法相关',
      key: 'algorithmRelated',
    });
    // 无法回收结论的原因
    values.push({
      header: '无法回收结论原因',
      key: 'noConclusionReason',
    });
    // DS字段是否填写完整
    values.push({
      header: 'DS字段填写完整',
      key: 'revenueInfoCompleted',
    });
    // 业务标签
    values.push({
      header: '业务标签',
      key: 'bizTag',
    });
    // 需求来源业务线
    values.push({
      header: '需求来源业务线',
      key: 'sourceBusiness',
    });
    return values;
  }

  private genBizTag(tags?: { primary: string; secondary: string }[]) {
    if (!tags) {
      return '';
    }
    let bizTag = '';
    const tagsCount = tags.length;
    for (let i = 0; i < tagsCount; i++) {
      const tag = tags[i];
      const { primary, secondary } = tag;
      if (primary.length > 0) {
        bizTag += primary;
        if (secondary.length > 0) {
          bizTag += `-${secondary}`;
        }
      } else {
        bizTag = secondary.length > 0 ? secondary : '';
      }
      if (bizTag.length > 0 && i < tagsCount - 1) {
        bizTag += ',';
      }
    }
    return bizTag;
  }

  private genSourceBusiness(sourceBusiness?: { primary: string; secondary: string }[]) {
    // 复用 genBizTag 的能力
    return this.genBizTag(sourceBusiness);
  }

  // Task Info 主行数据，会显式包含 task_id。一条记录代表一个 Meego 需求。
  private taskInfoMainRowData(taskInfo: StoryRevenueTaskInfo, index: number) {
    const { meegoInfo, experimentInfo, revenueInfo } = taskInfo;

    // 【重要！】请勿调整顺序，否则会导致表格数据对不齐
    // 序号
    const serialNumber = `${index + 1}`;
    // task基础信息-收益task_id
    const taskId = taskInfo._id ?? '';
    // 需求信息-需求名称(Meego链接)
    const meegoTitle = { text: meegoInfo.name, hyperlink: meegoInfo.url };
    // 需求信息-优先级
    const meegoPriority = meegoInfo.priority;
    // 需求信息-Owner
    const meegoOwner = this.getUsersDisplayName(meegoInfo.owners);
    // 需求信息-业务线
    let meegoBusiness = meegoInfo.primaryBusiness;
    if (meegoInfo.secondaryBusiness.length > 0) {
      meegoBusiness += `-${meegoInfo.secondaryBusiness}`;
    }
    const meegoBusinessLine = meegoBusiness;
    // 需求信息-子模块
    const meegoSubmodules = meegoInfo.submodules.join(',');
    // 需求信息-RD工作量
    const meegoRDWorkload = meegoInfo.rdWorkload.toString();
    // 收益信息-是否参与review
    const reviewType = this.getStoryRevenueReviewTypeDisplayName(revenueInfo?.reviewType);
    // 收益信息-参与review周期
    const reviewPeriod: { text: string; hyperlink: string } = {
      text: this.reviewPeriodName ?? taskInfo.reviewPeriodId,
      hyperlink: `https://meego.larkoffice.com/faceu/storyView/${taskInfo.reviewPeriodId}`,
    };
    // 实验信息-子实验数量
    const subExperimentCount = experimentInfo ? experimentInfo.subExperimentCount.toString() : '';
    // 实验信息-关联实验链接
    const libraUrl = '';
    // let libraUrl: string | { text: string; hyperlink: string } = "";
    // if (experimentInfo && experimentInfo.subExperimentCount === 1) {
    // 	// 有且仅有 1 个子实验，则展示
    // 	libraUrl = {
    // 		text: experimentInfo.subExperimentList[0].libraTitle,
    // 		hyperlink: experimentInfo.subExperimentList[0].libraUrl,
    // 	};
    // }
    // 收益信息-收益自评
    const expectationType = this.getStoryRevenueExpectationTypeDisplayName(revenueInfo?.expectationType);
    // 收益信息-自评原因
    const revenueSelfAssessment = this.getRevenueSelfAssessmentDisplayName(
      revenueInfo?.expectationType,
      revenueInfo?.revenueSelfAssessment,
    );
    // 收益信息-有端收益
    const hasPlatformRevenue = this.getStoryRevenueBooleanDisplayName(
      revenueInfo?.manullyHasPlatformRevenue ?? revenueInfo?.hasPlatformRevenue,
    );
    // 收益信息-有端收益
    const dsHasPlatformRevenue = this.getStoryRevenueBooleanDisplayName(revenueInfo?.dsHasPlatformRevenue);
    // 收益信息-端收益指标结论
    const platformRevenueConclusion = this.getStoryRevenueConclusionDisplayName(
      revenueInfo?.platformRevenueMetricAutoComputedConclusion,
      revenueInfo?.platformRevenueMetricConclusion,
    );
    // 收益信息-有模块收益
    const hasModuleRevenue = this.getStoryRevenueBooleanDisplayName(
      revenueInfo?.manullyHasModuleRevenue ?? revenueInfo?.hasModuleRevenue,
    );
    // 收益信息-有模块收益
    const dsHasModuleRevenue = this.getStoryRevenueBooleanDisplayName(revenueInfo?.dsHasModuleRevenue);
    // 收益信息-模块收益指标结论
    const moduleRevenueConclusion = this.getStoryRevenueConclusionDisplayName(
      revenueInfo?.moduleRevenueMetricAutoComputedConclusion,
      revenueInfo?.moduleRevenueMetricConclusion,
    );
    // 收益信息-有关键过程指标收益
    const hasKeyProcessRevenue = this.getStoryRevenueBooleanDisplayName(
      revenueInfo?.manullyHasKeyProcessRevenue ?? revenueInfo?.hasKeyProcessRevenue,
    );
    // 收益信息-有关键过程指标收益
    const dsHasKeyProcessRevenue = this.getStoryRevenueBooleanDisplayName(revenueInfo?.dsHasKeyProcessRevenue);
    // 收益信息-过程指标收益结论
    const keyProcessRevenueConclusion = this.getStoryRevenueConclusionDisplayName(
      revenueInfo?.keyProcessRevenueMetricAutoComputedConclusion,
      revenueInfo?.keyProcessRevenueMetricConclusion,
    );
    // 是否有指标损失
    const hasMetricLoss = this.getStoryRevenueBooleanDisplayName(
      revenueInfo?.manuallyHasMetricLoss ?? revenueInfo?.hasMetricLoss,
    );
    // DS修正收益判断差异说明
    const dsRevenueConclusionRemark = revenueInfo?.dsRevenueConclusionRemark ?? '';
    // 收益信息-是否上线/推全
    const isFullyRelease = this.getStoryRevenueBooleanDisplayName(revenueInfo?.isFullyRelease);
    // 收益信息-备注
    const revenueRemark = revenueInfo?.remark ?? '';
    // task基础信息-task填写完整性
    const taskFillInCompleted = this.getStoryRevenueBooleanDisplayName(taskInfo.fillInCompleted);
    // 实验信息-关联实验状态
    let libraStatus = '';
    if (experimentInfo && experimentInfo.subExperimentCount === 1) {
      // 有且仅有 1 个子实验，则展示
      libraStatus = this.getStoryRevenueExperimentStatusDisplayName(experimentInfo.subExperimentList[0].status);
    }
    // 实验信息-关联实验开启时间
    let libraStartDate = '';
    if (experimentInfo && experimentInfo.subExperimentCount === 1) {
      // 有且仅有 1 个子实验，则展示
      libraStartDate = this.formatUnixTimestamp(experimentInfo.subExperimentList[0].startTime);
    }
    // 实验信息-关联实验关闭时间
    let libraEndDate = '';
    if (experimentInfo && experimentInfo.subExperimentCount === 1) {
      // 有且仅有 1 个子实验，则展示
      libraEndDate = this.formatUnixTimestamp(experimentInfo.subExperimentList[0].endTime);
    }
    // 需求信息-上线区域
    const publishedRegions = meegoInfo.publishedRegions.join(',');
    // 需求信息-上线应用
    const publishedApps = meegoInfo.publishedApps.join(',');
    // 需求信息-需求文档
    const meegoPrdUrl = meegoInfo.prdUrl;
    // 需求信息-涉及研发团队
    const meegoRelatedRDTeams = meegoInfo.relatedRDTeams.join(',');
    // 需求信息-技术Owner
    const meegoTechOwners = this.getUsersDisplayName(meegoInfo.techOwners);
    // 需求信息-需求状态
    const meegoStatus = meegoInfo.status;
    // 需求信息-需求测试完成时间
    const meegoTestFinishedTime = this.formatUnixTimestamp(meegoInfo.testFinishedTime, true);
    // 需求信息-需求内审完成时间
    const meegoInternalReviewFinishedTime = this.formatUnixTimestamp(meegoInfo.internalReviewFinishedTime, true);
    // 需求信息-创建人
    const meegoCreator = this.getUsersDisplayName([meegoInfo.creator]);
    // 需求信息-meego_id
    const meegoId = meegoInfo.id;
    // task基础信息-task终止
    const taskTerminated = this.getStoryRevenueBooleanDisplayName(taskInfo.terminated);
    // task基础信息-收益review时长
    const reviewDuration = taskInfo.reviewDuration.toString();
    // 需求收益复盘文档
    const reviewDocUrl = revenueInfo?.reviewDocUrl ?? '';
    // DS review人
    const dsReviewer = revenueInfo?.dsReviewer ? this.getUsersDisplayName(revenueInfo?.dsReviewer) : '';
    // 是否为分析依据（只要子实验中有一个是分析依据，则是标记为有分析依据）
    let isAnalysisBasis = '';
    if (taskInfo.experimentInfo && taskInfo.experimentInfo.subExperimentCount > 0) {
      const { subExperimentList } = taskInfo.experimentInfo;
      for (const subExperimentInfo of subExperimentList) {
        if (subExperimentInfo.markAsAnalysisBasis) {
          isAnalysisBasis = this.getStoryRevenueBooleanFlagDisplayName(true);
          break;
        }
      }
    } else if (taskInfo.publishedAppRevenueInfos && taskInfo.publishedAppRevenueInfos.length > 0) {
      const { publishedAppRevenueInfos } = taskInfo;
      for (const publishedAppRevenueInfo of publishedAppRevenueInfos) {
        if (publishedAppRevenueInfo.markAsAnalysisBasis) {
          isAnalysisBasis = this.getStoryRevenueBooleanFlagDisplayName(true);
          break;
        }
      }
    }
    // 需求类型
    const meegoType = meegoInfo.type;
    // 大盘指标折损
    const ltMetricLossType = '';
    // 是否与算法相关
    const algorithmRelated = this.getStoryRevenueBooleanDisplayName(meegoInfo.algorithmRelated);
    // 无法回收结论的原因
    const noConclusionReason = revenueInfo?.noConclusionReason ?? '';
    // DS字段是否填写完整
    const revenueInfoCompleted = this.getStoryRevenueBooleanDisplayName(taskInfo.revenueInfoCompleted);
    // 业务标签
    const bizTag = this.genBizTag(meegoInfo.bizTag);
    // 需求来源业务线
    const sourceBusiness = this.genSourceBusiness(meegoInfo.sourceBusiness);
    return {
      serialNumber,
      taskId,
      meegoTitle,
      meegoPriority,
      meegoOwner,
      meegoBusinessLine,
      meegoSubmodules,
      meegoRDWorkload,
      reviewType,
      reviewPeriod,
      subExperimentCount,
      libraUrl,
      expectationType,
      revenueSelfAssessment,
      hasPlatformRevenue,
      dsHasPlatformRevenue,
      platformRevenueConclusion,
      hasModuleRevenue,
      dsHasModuleRevenue,
      moduleRevenueConclusion,
      hasKeyProcessRevenue,
      dsHasKeyProcessRevenue,
      keyProcessRevenueConclusion,
      hasMetricLoss,
      dsRevenueConclusionRemark,
      isFullyRelease,
      revenueRemark,
      taskFillInCompleted,
      libraStatus,
      libraStartDate,
      libraEndDate,
      publishedRegions,
      publishedApps,
      meegoPrdUrl,
      meegoRelatedRDTeams,
      meegoTechOwners,
      meegoStatus,
      meegoTestFinishedTime,
      meegoInternalReviewFinishedTime,
      meegoCreator,
      meegoId,
      taskTerminated,
      reviewDuration,
      reviewDocUrl,
      dsReviewer,
      isAnalysisBasis,
      meegoType,
      ltMetricLossType,
      algorithmRelated,
      noConclusionReason,
      revenueInfoCompleted,
      bizTag,
      sourceBusiness,
    };
  }

  // Task Info 子行数据，不会包含 task_id。一条记录代表一个 Libra 实验。
  private taskInfoSubRowData(taskInfo: StoryRevenueTaskInfo, subExperimentInfo: StoryRevenueTaskSubExperimentInfo) {
    const { meegoInfo, revenueInfo } = taskInfo;
    // 【重要！】请勿调整顺序，否则会导致表格数据对不齐
    // 序号
    const serialNumber = '';
    // task基础信息-收益task_id
    const taskId = '';
    // 需求信息-需求名称(Meego链接)
    const meegoTitle = { text: meegoInfo.name, hyperlink: meegoInfo.url };
    // 需求信息-优先级
    const meegoPriority = meegoInfo.priority;
    // 需求信息-Owner
    const meegoOwner = this.getUsersDisplayName(meegoInfo.owners);
    // 需求信息-业务线
    let meegoBusinessLine = meegoInfo.primaryBusiness;
    if (meegoInfo.secondaryBusiness.length > 0) {
      meegoBusinessLine += `-${meegoInfo.secondaryBusiness}`;
    }
    // 需求信息-子模块
    const meegoSubmodules = meegoInfo.submodules.join(',');
    // 需求信息-RD工作量
    const meegoRDWorkload = meegoInfo.rdWorkload.toString();
    // 收益信息-是否参与review
    const reviewType = this.getStoryRevenueReviewTypeDisplayName(revenueInfo?.reviewType);
    // 收益信息-参与review周期(“子实验信息：是否为分析依据”)
    const reviewPeriod = this.getStoryRevenueBooleanFlagDisplayName(subExperimentInfo.markAsAnalysisBasis);
    // 实验信息-子实验数量（“子实验id”）
    const subExperimentCount = `${subExperimentInfo.libraFlightId}(实验id)`;
    // 实验信息-关联实验链接（“子实验名称”）
    let libraUrl: string | { text: string; hyperlink: string } = '';
    if (subExperimentInfo) {
      libraUrl = { text: subExperimentInfo.libraTitle, hyperlink: subExperimentInfo.libraUrl };
    }
    // 收益信息-收益自评（子实验 Owner）
    // const expectationType = this.getUsersDisplayName(subExperimentInfo.libraOwners);
    const expectationType = this.getStoryRevenueExpectationTypeDisplayName(revenueInfo?.expectationType);
    // 收益信息-自评原因（子实验状态）
    // const revenueSelfAssessment = this.getStoryRevenueExperimentStatusDisplayName(subExperimentInfo.status);
    const revenueSelfAssessment = this.getRevenueSelfAssessmentDisplayName(
      revenueInfo?.expectationType,
      revenueInfo?.revenueSelfAssessment,
    );
    // 收益信息-有端收益
    const hasPlatformRevenue = this.getStoryRevenueBooleanDisplayName(
      subExperimentInfo.revenueInfo?.manullyHasPlatformRevenue ?? subExperimentInfo.revenueInfo?.hasPlatformRevenue,
    );
    // 收益信息-有端收益
    const dsHasPlatformRevenue = this.getStoryRevenueBooleanDisplayName(
      subExperimentInfo.revenueInfo?.dsHasPlatformRevenue,
    );
    // 收益信息-端收益指标结论
    let platformRevenueConclusion = '';
    if (subExperimentInfo.revenueInfo) {
      platformRevenueConclusion = this.getStoryRevenueConclusionDisplayName(
        subExperimentInfo.revenueInfo?.platformRevenueMetricAutoComputedConclusion,
        subExperimentInfo.revenueInfo?.platformRevenueMetricConclusion,
      );
    }
    // 收益信息-有模块收益
    const hasModuleRevenue = this.getStoryRevenueBooleanDisplayName(
      subExperimentInfo.revenueInfo?.manullyHasModuleRevenue ?? subExperimentInfo.revenueInfo?.hasModuleRevenue,
    );
    // 收益信息-有模块收益
    const dsHasModuleRevenue = this.getStoryRevenueBooleanDisplayName(
      subExperimentInfo.revenueInfo?.dsHasModuleRevenue,
    );
    // 收益信息-模块收益指标结论
    const moduleRevenueConclusion = this.getStoryRevenueConclusionDisplayName(
      subExperimentInfo.revenueInfo?.moduleRevenueMetricAutoComputedConclusion,
      subExperimentInfo.revenueInfo?.moduleRevenueMetricConclusion,
    );
    // 收益信息-有关键过程指标收益
    const hasKeyProcessRevenue = this.getStoryRevenueBooleanDisplayName(
      subExperimentInfo.revenueInfo?.manullyHasKeyProcessRevenue ?? subExperimentInfo.revenueInfo?.hasKeyProcessRevenue,
    );
    // 收益信息-有关键过程指标收益
    const dsHasKeyProcessRevenue = this.getStoryRevenueBooleanDisplayName(
      subExperimentInfo.revenueInfo?.dsHasKeyProcessRevenue,
    );
    // 收益信息-过程指标收益结论
    const keyProcessRevenueConclusion = this.getStoryRevenueConclusionDisplayName(
      subExperimentInfo.revenueInfo?.keyProcessRevenueMetricAutoComputedConclusion,
      subExperimentInfo.revenueInfo?.keyProcessRevenueMetricConclusion,
    );
    // 是否有指标损失
    const hasMetricLoss = this.getStoryRevenueBooleanDisplayName(
      subExperimentInfo.revenueInfo?.manuallyHasMetricLoss ?? subExperimentInfo.revenueInfo?.hasMetricLoss,
    );
    // DS修正收益判断差异说明
    const dsRevenueConclusionRemark = subExperimentInfo.revenueInfo?.dsRevenueConclusionRemark ?? '';
    // 收益信息-是否上线/推全
    const isFullyRelease = '';
    // 收益信息-备注
    const revenueRemark = '';
    // task基础信息-task填写完整性
    const taskFillInCompleted = '';
    // 实验信息-关联实验状态
    const libraStatus = this.getStoryRevenueExperimentStatusDisplayName(subExperimentInfo.status);
    // 实验信息-关联实验开启时间
    const libraStartDate = this.formatUnixTimestamp(subExperimentInfo.startTime);
    // 实验信息-关联实验关闭时间
    const libraEndDate = this.formatUnixTimestamp(subExperimentInfo.endTime);
    // 需求信息-上线区域
    const publishedRegions = '';
    // 需求信息-上线应用(“子实验对应上线应用”)
    const publishedApps = subExperimentInfo.publishedApp ?? '';
    // 需求信息-需求文档
    const meegoPrdUrl = '';
    // 需求信息-涉及研发团队
    const meegoRelatedRDTeams = '';
    // 需求信息-技术Owner
    const meegoTechOwners = '';
    // 需求信息-需求状态
    const meegoStatus = '';
    // 需求信息-需求测试完成时间
    const meegoTestFinishedTime = '';
    // 需求信息-需求内审完成时间
    const meegoInternalReviewFinishedTime = '';
    // 需求信息-创建人
    const meegoCreator = '';
    // 需求信息-meego_id
    const meegoId = '';
    // task基础信息-task终止
    const taskTerminated = '';
    // task基础信息-收益review时长
    const reviewDuration = '';
    // 需求收益复盘文档
    const reviewDocUrl = '';
    // DS review人
    const dsReviewer = '';
    // 是否为分析依据
    const isAnalysisBasis = this.getStoryRevenueBooleanFlagDisplayName(subExperimentInfo.markAsAnalysisBasis);
    // 需求类型
    const meegoType = '';
    // 大盘指标折损
    const ltMetricLossType = this.getLTMetricLossDisplayName(subExperimentInfo.revenueInfo?.ltMetricLossType);
    // 是否与算法相关
    const algorithmRelated = '';
    // 无法回收结论的原因
    const noConclusionReason = '';
    // DS字段是否填写完整
    const revenueInfoCompleted = '';
    // 业务标签
    const bizTag = this.genBizTag(meegoInfo.bizTag);
    // 需求来源业务线
    const sourceBusiness = this.genSourceBusiness(meegoInfo.sourceBusiness);

    return {
      serialNumber,
      taskId,
      meegoTitle,
      meegoPriority,
      meegoOwner,
      meegoBusinessLine,
      meegoSubmodules,
      meegoRDWorkload,
      reviewType,
      reviewPeriod,
      subExperimentCount,
      libraUrl,
      expectationType,
      revenueSelfAssessment,
      hasPlatformRevenue,
      dsHasPlatformRevenue,
      platformRevenueConclusion,
      hasModuleRevenue,
      dsHasModuleRevenue,
      moduleRevenueConclusion,
      hasKeyProcessRevenue,
      dsHasKeyProcessRevenue,
      keyProcessRevenueConclusion,
      hasMetricLoss,
      dsRevenueConclusionRemark,
      isFullyRelease,
      revenueRemark,
      taskFillInCompleted,
      libraStatus,
      libraStartDate,
      libraEndDate,
      publishedRegions,
      publishedApps,
      meegoPrdUrl,
      meegoRelatedRDTeams,
      meegoTechOwners,
      meegoStatus,
      meegoTestFinishedTime,
      meegoInternalReviewFinishedTime,
      meegoCreator,
      meegoId,
      taskTerminated,
      reviewDuration,
      reviewDocUrl,
      dsReviewer,
      isAnalysisBasis,
      meegoType,
      ltMetricLossType,
      algorithmRelated,
      noConclusionReason,
      revenueInfoCompleted,
      bizTag,
      sourceBusiness,
    };
  }

  // Task Info 子行数据，不会包含 task_id，也不包含 Libra 实验信息。根据 taskInfo.publishedAppRevenueInfos 生成 DS 收益标注（按上线应用进行收益分析）
  private taskInfoSubRowDataByPublishedAppRevenueInfos(
    taskInfo: StoryRevenueTaskInfo,
    publishedAppRevenueInfo: StoryRevenuePublishedAppRevenueInfo,
  ) {
    const { meegoInfo, revenueInfo } = taskInfo;
    // 【重要！】请勿调整顺序，否则会导致表格数据对不齐
    // 序号
    const serialNumber = '';
    // task基础信息-收益task_id
    const taskId = '';
    // 需求信息-需求名称(Meego链接)
    const meegoTitle = { text: meegoInfo.name, hyperlink: meegoInfo.url };
    // 需求信息-优先级
    const meegoPriority = meegoInfo.priority;
    // 需求信息-Owner
    const meegoOwner = this.getUsersDisplayName(meegoInfo.owners);
    // 需求信息-业务线
    let meegoBusinessLine = meegoInfo.primaryBusiness;
    if (meegoInfo.secondaryBusiness.length > 0) {
      meegoBusinessLine += `-${meegoInfo.secondaryBusiness}`;
    }
    // 需求信息-子模块
    const meegoSubmodules = meegoInfo.submodules.join(',');
    // 需求信息-RD工作量
    const meegoRDWorkload = meegoInfo.rdWorkload.toString();
    // 收益信息-是否参与review
    const reviewType = this.getStoryRevenueReviewTypeDisplayName(revenueInfo?.reviewType);
    // 收益信息-参与review周期(“DS 标注信息：：是否为分析依据”)
    const reviewPeriod = this.getStoryRevenueBooleanFlagDisplayName(publishedAppRevenueInfo.markAsAnalysisBasis);
    // 实验信息-子实验数量
    const subExperimentCount = '';
    // 实验信息-关联实验链接
    const libraUrl = '';
    // 收益信息-收益自评
    const expectationType = this.getStoryRevenueExpectationTypeDisplayName(revenueInfo?.expectationType);
    // 收益信息-自评原因
    const revenueSelfAssessment = this.getRevenueSelfAssessmentDisplayName(
      revenueInfo?.expectationType,
      revenueInfo?.revenueSelfAssessment,
    );
    // 收益信息-有端收益
    const hasPlatformRevenue = '';
    // 收益信息-有端收益
    const dsHasPlatformRevenue = this.getStoryRevenueBooleanDisplayName(publishedAppRevenueInfo.dsHasPlatformRevenue);
    // 收益信息-端收益指标结论
    const platformRevenueConclusion = '';
    // 收益信息-有模块收益
    const hasModuleRevenue = '';
    // 收益信息-有模块收益
    const dsHasModuleRevenue = this.getStoryRevenueBooleanDisplayName(publishedAppRevenueInfo.dsHasModuleRevenue);
    // 收益信息-模块收益指标结论
    const moduleRevenueConclusion = '';
    // 收益信息-有关键过程指标收益
    const hasKeyProcessRevenue = '';
    // 收益信息-有关键过程指标收益
    const dsHasKeyProcessRevenue = this.getStoryRevenueBooleanDisplayName(
      publishedAppRevenueInfo.dsHasKeyProcessRevenue,
    );
    // 收益信息-过程指标收益结论
    const keyProcessRevenueConclusion = '';
    // 是否有指标损失
    const hasMetricLoss = '';
    // DS修正收益判断差异说明
    const dsRevenueConclusionRemark = publishedAppRevenueInfo.dsRevenueConclusionRemark ?? '';
    // 收益信息-是否上线/推全
    const isFullyRelease = '';
    // 收益信息-备注
    const revenueRemark = '';
    // task基础信息-task填写完整性
    const taskFillInCompleted = '';
    // 实验信息-关联实验状态
    const libraStatus = '';
    // 实验信息-关联实验开启时间
    const libraStartDate = '';
    // 实验信息-关联实验关闭时间
    const libraEndDate = '';
    // 需求信息-上线区域
    const publishedRegions = '';
    // 需求信息-上线应用(“对应上线应用”)
    const publishedApps = publishedAppRevenueInfo.publishedApp ?? '';
    // 需求信息-需求文档
    const meegoPrdUrl = '';
    // 需求信息-涉及研发团队
    const meegoRelatedRDTeams = '';
    // 需求信息-技术Owner
    const meegoTechOwners = '';
    // 需求信息-需求状态
    const meegoStatus = '';
    // 需求信息-需求测试完成时间
    const meegoTestFinishedTime = '';
    // 需求信息-需求内审完成时间
    const meegoInternalReviewFinishedTime = '';
    // 需求信息-创建人
    const meegoCreator = '';
    // 需求信息-meego_id
    const meegoId = '';
    // task基础信息-task终止
    const taskTerminated = '';
    // task基础信息-收益review时长
    const reviewDuration = '';
    // 需求收益复盘文档
    const reviewDocUrl = '';
    // DS review人
    const dsReviewer = '';
    // 是否为分析依据
    const isAnalysisBasis = this.getStoryRevenueBooleanFlagDisplayName(publishedAppRevenueInfo.markAsAnalysisBasis);
    // 需求类型
    const meegoType = '';
    // 大盘指标折损
    const ltMetricLossType = '';
    // 是否与算法相关
    const algorithmRelated = '';
    // 无法回收结论的原因
    const noConclusionReason = '';
    // DS字段是否填写完整
    const revenueInfoCompleted = '';
    // 业务标签
    const bizTag = this.genBizTag(meegoInfo.bizTag);
    // 需求来源业务线
    const sourceBusiness = this.genSourceBusiness(meegoInfo.sourceBusiness);

    return {
      serialNumber,
      taskId,
      meegoTitle,
      meegoPriority,
      meegoOwner,
      meegoBusinessLine,
      meegoSubmodules,
      meegoRDWorkload,
      reviewType,
      reviewPeriod,
      subExperimentCount,
      libraUrl,
      expectationType,
      revenueSelfAssessment,
      hasPlatformRevenue,
      dsHasPlatformRevenue,
      platformRevenueConclusion,
      hasModuleRevenue,
      dsHasModuleRevenue,
      moduleRevenueConclusion,
      hasKeyProcessRevenue,
      dsHasKeyProcessRevenue,
      keyProcessRevenueConclusion,
      hasMetricLoss,
      dsRevenueConclusionRemark,
      isFullyRelease,
      revenueRemark,
      taskFillInCompleted,
      libraStatus,
      libraStartDate,
      libraEndDate,
      publishedRegions,
      publishedApps,
      meegoPrdUrl,
      meegoRelatedRDTeams,
      meegoTechOwners,
      meegoStatus,
      meegoTestFinishedTime,
      meegoInternalReviewFinishedTime,
      meegoCreator,
      meegoId,
      taskTerminated,
      reviewDuration,
      reviewDocUrl,
      dsReviewer,
      isAnalysisBasis,
      meegoType,
      ltMetricLossType,
      algorithmRelated,
      noConclusionReason,
      revenueInfoCompleted,
      bizTag,
      sourceBusiness,
    };
  }

  // 根据单个 TaskInfo 生成表格数据
  private genTaskInfoValues(taskInfo: StoryRevenueTaskInfo, index: number, worksheet: ExcelJS.Worksheet) {
    const mainRowData = this.taskInfoMainRowData(taskInfo, index);
    const row = worksheet.addRow(mainRowData);
    row.getCell('meegoTitle').font = { color: { argb: 'FF0000FF' }, underline: true }; // 蓝色字体、下划线
    // row.getCell("libraUrl").font = { color: { argb: "FF0000FF" }, underline: true }; // 蓝色字体、下划线
    row.getCell('reviewPeriod').font = { color: { argb: 'FF0000FF' }, underline: true }; // 蓝色字体、下划线
  }

  // 根据子实验信息生成表格数据
  private genSubExperimentInfoValues(
    taskInfo: StoryRevenueTaskInfo,
    subExperimentInfo: StoryRevenueTaskSubExperimentInfo,
    worksheet: ExcelJS.Worksheet,
  ): void {
    const subRowData = this.taskInfoSubRowData(taskInfo, subExperimentInfo);
    const row = worksheet.addRow(subRowData);
    // 需求信息-需求名称(Meego链接)
    row.getCell('meegoTitle').font = { color: { argb: 'FF0000FF' }, underline: true }; // 蓝色字体、下划线
    // 实验信息-关联实验链接（“子实验名称”）
    row.getCell('libraUrl').font = { color: { argb: 'FF0000FF' }, underline: true }; // 蓝色字体、下划线
  }

  // 根据 taskInfo.publishedAppRevenueInfos 生成 DS 收益标注（按上线应用进行收益分析）
  private genPublishedAppRevenueInfoValues(
    taskInfo: StoryRevenueTaskInfo,
    publishedAppRevenueInfo: StoryRevenuePublishedAppRevenueInfo,
    worksheet: ExcelJS.Worksheet,
  ): void {
    const subRowData = this.taskInfoSubRowDataByPublishedAppRevenueInfos(taskInfo, publishedAppRevenueInfo);
    const row = worksheet.addRow(subRowData);
    row.getCell('meegoTitle').font = { color: { argb: 'FF0000FF' }, underline: true }; // 蓝色字体、下划线
  }

  // 导出 Excel 表格
  async didExport() {
    // 创建工作簿和工作表
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('My Sheet');

    // 定义列
    worksheet.columns = this.genHeaderValues();

    // 遍历每一个 task info
    const taskListLength = this.taskInfoList.length;
    for (let i = 0; i < taskListLength; i++) {
      const taskInfo = this.taskInfoList[i];
      // 添加 task info 信息
      this.genTaskInfoValues(taskInfo, i, worksheet);
      // 同时要处理子实验 >= 1 的情况
      if (taskInfo.experimentInfo && taskInfo.experimentInfo.subExperimentCount >= 1) {
        for (const subExperiment of taskInfo.experimentInfo.subExperimentList) {
          // 添加子实验信息
          this.genSubExperimentInfoValues(taskInfo, subExperiment, worksheet);
        }
      } else if (
        taskInfo.publishedAppRevenueInfos &&
        Array.isArray(taskInfo.publishedAppRevenueInfos) &&
        taskInfo.publishedAppRevenueInfos.length > 0
      ) {
        // 展示 publishedAppRevenueInfos，按照上线应用分析收益信息
        for (const publishedAppRevenueInfo of taskInfo.publishedAppRevenueInfos) {
          this.genPublishedAppRevenueInfoValues(taskInfo, publishedAppRevenueInfo, worksheet);
        }
      }
    }

    // 创建二进制文件
    const buffer = await workbook.xlsx.writeBuffer();

    // 触发文件下载
    const blob = new Blob([buffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
    saveAs(blob, `${this.reviewPeriodName ?? 'unknown'}.xlsx`);
  }

  /**
   * ===========================================
   * Section: 生成风神数据版本数据
   * ===========================================
   */
  // Task Info 主行数据，会显式包含 task_id。一条记录代表一个 Meego 需求。
  private taskInfoMainRowDashboardData(taskInfo: StoryRevenueTaskInfo, index: number) {
    const { meegoInfo, experimentInfo, revenueInfo } = taskInfo;
    const reviewPeriodInfo = this.reviewPeriodIdMapInfo?.get(taskInfo.reviewPeriodId);
    // 【重要！】请勿调整顺序，否则会导致表格数据对不齐
    // 序号
    const serialNumber = `${index + 1}`;
    // task基础信息-收益task_id
    const taskId = taskInfo._id ?? '';
    // 需求信息-需求名称
    const meegoTitle = meegoInfo.name;
    // 需求信息-需求链接
    const meegoUrl = meegoInfo.url;
    // 需求信息-优先级
    const meegoPriority = meegoInfo.priority;
    // 需求信息-Owner
    const meegoOwner = this.getUsersDisplayName(meegoInfo.owners);
    // 需求信息-业务线
    let meegoBusiness = meegoInfo.primaryBusiness;
    if (meegoInfo.secondaryBusiness.length > 0) {
      meegoBusiness += `-${meegoInfo.secondaryBusiness}`;
    }
    const meegoBusinessLine = meegoBusiness;
    // 需求信息-子模块
    const meegoSubmodules = meegoInfo.submodules.join(',');
    // 需求信息-RD工作量
    const meegoRDWorkload = meegoInfo.rdWorkload.toString();
    // 收益信息-是否参与review
    const reviewType = this.getStoryRevenueReviewTypeDisplayName(revenueInfo?.reviewType);
    // 收益信息-参与review周期名称
    const reviewPeriodName = reviewPeriodInfo?.reviewPeriodName ?? '';
    // 收益信息-参与review周期 Id
    const { reviewPeriodId } = taskInfo;
    // 收益信息-参与review周期 URL
    const reviewPeriodUrl = `https://meego.larkoffice.com/faceu/storyView/${reviewPeriodId}`;
    // 实验信息-子实验数量
    const subExperimentCount = experimentInfo ? experimentInfo.subExperimentCount.toString() : '';
    // 实验信息-关联实验名称
    const libraTitle = '';
    // 实验信息-关联实验链接
    const libraUrl = '';
    // let libraUrl: string | { text: string; hyperlink: string } = "";
    // if (experimentInfo && experimentInfo.subExperimentCount === 1) {
    // 	// 有且仅有 1 个子实验，则展示
    // 	libraUrl = {
    // 		text: experimentInfo.subExperimentList[0].libraTitle,
    // 		hyperlink: experimentInfo.subExperimentList[0].libraUrl,
    // 	};
    // }
    // 收益信息-收益自评
    const expectationType = this.getStoryRevenueExpectationTypeDisplayName(revenueInfo?.expectationType);
    // 收益信息-自评原因
    const revenueSelfAssessment = this.getRevenueSelfAssessmentDisplayName(
      revenueInfo?.expectationType,
      revenueInfo?.revenueSelfAssessment,
    );
    // 收益信息-有端收益
    const hasPlatformRevenue = this.getStoryRevenueBooleanDisplayName(
      revenueInfo?.manullyHasPlatformRevenue ?? revenueInfo?.hasPlatformRevenue,
    );
    // 收益信息-有端收益
    const dsHasPlatformRevenue = this.getStoryRevenueBooleanDisplayName(revenueInfo?.dsHasPlatformRevenue);
    // 收益信息-端收益指标结论
    const platformRevenueConclusion = this.getStoryRevenueConclusionDisplayName(
      revenueInfo?.platformRevenueMetricAutoComputedConclusion,
      revenueInfo?.platformRevenueMetricConclusion,
    );
    // 收益信息-有模块收益
    const hasModuleRevenue = this.getStoryRevenueBooleanDisplayName(
      revenueInfo?.manullyHasModuleRevenue ?? revenueInfo?.hasModuleRevenue,
    );
    // 收益信息-有模块收益
    const dsHasModuleRevenue = this.getStoryRevenueBooleanDisplayName(revenueInfo?.dsHasModuleRevenue);
    // 收益信息-模块收益指标结论
    const moduleRevenueConclusion = this.getStoryRevenueConclusionDisplayName(
      revenueInfo?.moduleRevenueMetricAutoComputedConclusion,
      revenueInfo?.moduleRevenueMetricConclusion,
    );
    // 收益信息-有关键过程指标收益
    const hasKeyProcessRevenue = this.getStoryRevenueBooleanDisplayName(
      revenueInfo?.manullyHasKeyProcessRevenue ?? revenueInfo?.hasKeyProcessRevenue,
    );
    // 收益信息-有关键过程指标收益
    const dsHasKeyProcessRevenue = this.getStoryRevenueBooleanDisplayName(revenueInfo?.dsHasKeyProcessRevenue);
    // 收益信息-过程指标收益结论
    const keyProcessRevenueConclusion = this.getStoryRevenueConclusionDisplayName(
      revenueInfo?.keyProcessRevenueMetricAutoComputedConclusion,
      revenueInfo?.keyProcessRevenueMetricConclusion,
    );
    // 是否有指标损失
    const hasMetricLoss = this.getStoryRevenueBooleanDisplayName(
      revenueInfo?.manuallyHasMetricLoss ?? revenueInfo?.hasMetricLoss,
    );
    // DS修正收益判断差异说明
    const dsRevenueConclusionRemark = revenueInfo?.dsRevenueConclusionRemark ?? '';
    // 收益信息-是否上线/推全
    const isFullyRelease = this.getStoryRevenueBooleanDisplayName(revenueInfo?.isFullyRelease);
    // 收益信息-备注
    const revenueRemark = revenueInfo?.remark ?? '';
    // task基础信息-task填写完整性
    const taskFillInCompleted = this.getStoryRevenueBooleanDisplayName(taskInfo.fillInCompleted);
    // 实验信息-关联实验状态
    let libraStatus = '';
    if (experimentInfo && experimentInfo.subExperimentCount === 1) {
      // 有且仅有 1 个子实验，则展示
      libraStatus = this.getStoryRevenueExperimentStatusDisplayName(experimentInfo.subExperimentList[0].status);
    }
    // 实验信息-关联实验开启时间
    let libraStartDate = '';
    if (experimentInfo && experimentInfo.subExperimentCount === 1) {
      // 有且仅有 1 个子实验，则展示
      libraStartDate = this.formatUnixTimestamp(experimentInfo.subExperimentList[0].startTime);
    }
    // 实验信息-关联实验关闭时间
    let libraEndDate = '';
    if (experimentInfo && experimentInfo.subExperimentCount === 1) {
      // 有且仅有 1 个子实验，则展示
      libraEndDate = this.formatUnixTimestamp(experimentInfo.subExperimentList[0].endTime);
    }
    // 需求信息-上线区域
    const publishedRegions = meegoInfo.publishedRegions.join(',');
    // 需求信息-上线应用
    const publishedApps = meegoInfo.publishedApps.join(',');
    // 需求信息-需求文档
    const meegoPrdUrl = meegoInfo.prdUrl;
    // 需求信息-涉及研发团队
    const meegoRelatedRDTeams = meegoInfo.relatedRDTeams.join(',');
    // 需求信息-技术Owner
    const meegoTechOwners = this.getUsersDisplayName(meegoInfo.techOwners);
    // 需求信息-需求状态
    const meegoStatus = meegoInfo.status;
    // 需求信息-需求测试完成时间
    const meegoTestFinishedTime = this.formatUnixTimestamp(meegoInfo.testFinishedTime, true);
    // 需求信息-需求内审完成时间
    const meegoInternalReviewFinishedTime = this.formatUnixTimestamp(meegoInfo.internalReviewFinishedTime, true);
    // 需求信息-创建人
    const meegoCreator = this.getUsersDisplayName([meegoInfo.creator]);
    // 需求信息-meego_id
    const meegoId = meegoInfo.id;
    // task基础信息-task终止
    const taskTerminated = this.getStoryRevenueBooleanDisplayName(taskInfo.terminated);
    // task基础信息-收益review时长
    const reviewDuration = taskInfo.reviewDuration.toString();
    // 需求收益复盘文档
    const reviewDocUrl = revenueInfo?.reviewDocUrl ?? '';
    // DS review人
    const dsReviewer = revenueInfo?.dsReviewer ? this.getUsersDisplayName(revenueInfo?.dsReviewer) : '';
    // DS 锁定周期时间
    const dsLockTime = reviewPeriodInfo?.dsLockTime
      ? this.formatUnixTimestamp(reviewPeriodInfo?.dsLockTime, false)
      : '';
    // 是否为分析依据（只要子实验中有一个是分析依据，则是标记为有分析依据）
    let isAnalysisBasis = '';
    if (taskInfo.experimentInfo && taskInfo.experimentInfo.subExperimentCount > 0) {
      const { subExperimentList } = taskInfo.experimentInfo;
      for (const subExperimentInfo of subExperimentList) {
        if (subExperimentInfo.markAsAnalysisBasis) {
          isAnalysisBasis = this.getStoryRevenueBooleanFlagDisplayName(true);
          break;
        }
      }
    } else if (taskInfo.publishedAppRevenueInfos && taskInfo.publishedAppRevenueInfos.length > 0) {
      const { publishedAppRevenueInfos } = taskInfo;
      for (const publishedAppRevenueInfo of publishedAppRevenueInfos) {
        if (publishedAppRevenueInfo.markAsAnalysisBasis) {
          isAnalysisBasis = this.getStoryRevenueBooleanFlagDisplayName(true);
          break;
        }
      }
    }
    // 需求类型
    const meegoType = meegoInfo.type;
    // 大盘指标折损
    const ltMetricLossType = '';
    // 是否与算法相关
    const algorithmRelated = this.getStoryRevenueBooleanDisplayName(meegoInfo.algorithmRelated);
    // 无法回收结论的原因
    const noConclusionReason = revenueInfo?.noConclusionReason ?? '';
    // DS字段是否填写完整
    const revenueInfoCompleted = this.getStoryRevenueBooleanDisplayName(taskInfo.revenueInfoCompleted);
    // 业务标签
    const bizTag = this.genBizTag(meegoInfo.bizTag);
    // 需求来源业务线
    const sourceBusiness = this.genSourceBusiness(meegoInfo.sourceBusiness);

    return {
      serialNumber,
      taskId,
      meegoTitle,
      meegoUrl,
      meegoPriority,
      meegoOwner,
      meegoBusinessLine,
      meegoSubmodules,
      meegoRDWorkload,
      reviewType,
      reviewPeriodName,
      reviewPeriodId,
      reviewPeriodUrl,
      subExperimentCount,
      libraTitle,
      libraUrl,
      expectationType,
      revenueSelfAssessment,
      hasPlatformRevenue,
      dsHasPlatformRevenue,
      platformRevenueConclusion,
      hasModuleRevenue,
      dsHasModuleRevenue,
      moduleRevenueConclusion,
      hasKeyProcessRevenue,
      dsHasKeyProcessRevenue,
      keyProcessRevenueConclusion,
      hasMetricLoss,
      dsRevenueConclusionRemark,
      isFullyRelease,
      revenueRemark,
      taskFillInCompleted,
      libraStatus,
      libraStartDate,
      libraEndDate,
      publishedRegions,
      publishedApps,
      meegoPrdUrl,
      meegoRelatedRDTeams,
      meegoTechOwners,
      meegoStatus,
      meegoTestFinishedTime,
      meegoInternalReviewFinishedTime,
      meegoCreator,
      meegoId,
      taskTerminated,
      reviewDuration,
      reviewDocUrl,
      dsReviewer,
      dsLockTime,
      isAnalysisBasis,
      meegoType,
      ltMetricLossType,
      algorithmRelated,
      noConclusionReason,
      revenueInfoCompleted,
      bizTag,
      sourceBusiness,
    };
  }

  // Task Info 子行数据，不会包含 task_id。一条记录代表一个 Libra 实验。
  private taskInfoSubRowDashboardData(
    taskInfo: StoryRevenueTaskInfo,
    subExperimentInfo: StoryRevenueTaskSubExperimentInfo,
  ) {
    const { meegoInfo, revenueInfo } = taskInfo;
    const reviewPeriodInfo = this.reviewPeriodIdMapInfo?.get(taskInfo.reviewPeriodId);
    // 【重要！】请勿调整顺序，否则会导致表格数据对不齐
    // 序号
    const serialNumber = '';
    // task基础信息-收益task_id
    const taskId = '';
    // 需求信息-需求名称
    const meegoTitle = meegoInfo.name;
    // 需求信息-需求链接
    const meegoUrl = meegoInfo.url;
    // 需求信息-优先级
    const meegoPriority = meegoInfo.priority;
    // 需求信息-Owner
    const meegoOwner = this.getUsersDisplayName(meegoInfo.owners);
    // 需求信息-业务线
    let meegoBusinessLine = meegoInfo.primaryBusiness;
    if (meegoInfo.secondaryBusiness.length > 0) {
      meegoBusinessLine += `-${meegoInfo.secondaryBusiness}`;
    }
    // 需求信息-子模块
    const meegoSubmodules = meegoInfo.submodules.join(',');
    // 需求信息-RD工作量
    const meegoRDWorkload = meegoInfo.rdWorkload.toString();
    // 收益信息-是否参与review
    const reviewType = this.getStoryRevenueReviewTypeDisplayName(revenueInfo?.reviewType);
    // 收益信息-参与review周期名称
    const reviewPeriodName = reviewPeriodInfo?.reviewPeriodName ?? '';
    // 收益信息-参与review周期 Id(“子实验信息：是否为分析依据”)
    const reviewPeriodId = this.getStoryRevenueBooleanFlagDisplayName(subExperimentInfo.markAsAnalysisBasis);
    // 收益信息-参与review周期 URL
    const reviewPeriodUrl = '';
    // 实验信息-子实验数量（“子实验id”）
    const subExperimentCount = `${subExperimentInfo.libraFlightId}(实验id)`;
    // 实验信息-关联实验名称
    let libraTitle = '';
    // 实验信息-关联实验链接（“子实验名称”）
    let libraUrl = '';
    if (subExperimentInfo) {
      libraTitle = subExperimentInfo.libraTitle;
      libraUrl = subExperimentInfo.libraUrl;
    }
    // 收益信息-收益自评（子实验 Owner）
    // const expectationType = this.getUsersDisplayName(subExperimentInfo.libraOwners);
    const expectationType = this.getStoryRevenueExpectationTypeDisplayName(revenueInfo?.expectationType);
    // 收益信息-自评原因（子实验状态）
    // const revenueSelfAssessment = this.getStoryRevenueExperimentStatusDisplayName(subExperimentInfo.status);
    const revenueSelfAssessment = this.getRevenueSelfAssessmentDisplayName(
      revenueInfo?.expectationType,
      revenueInfo?.revenueSelfAssessment,
    );
    // 收益信息-有端收益
    const hasPlatformRevenue = this.getStoryRevenueBooleanDisplayName(
      subExperimentInfo.revenueInfo?.manullyHasPlatformRevenue ?? subExperimentInfo.revenueInfo?.hasPlatformRevenue,
    );
    // 收益信息-有端收益
    const dsHasPlatformRevenue = this.getStoryRevenueBooleanDisplayName(
      subExperimentInfo.revenueInfo?.dsHasPlatformRevenue,
    );
    // 收益信息-端收益指标结论
    let platformRevenueConclusion = '';
    if (subExperimentInfo.revenueInfo) {
      platformRevenueConclusion = this.getStoryRevenueConclusionDisplayName(
        subExperimentInfo.revenueInfo?.platformRevenueMetricAutoComputedConclusion,
        subExperimentInfo.revenueInfo?.platformRevenueMetricConclusion,
      );
    }
    // 收益信息-有模块收益
    const hasModuleRevenue = this.getStoryRevenueBooleanDisplayName(
      subExperimentInfo.revenueInfo?.manullyHasModuleRevenue ?? subExperimentInfo.revenueInfo?.hasModuleRevenue,
    );
    // 收益信息-有模块收益
    const dsHasModuleRevenue = this.getStoryRevenueBooleanDisplayName(
      subExperimentInfo.revenueInfo?.dsHasModuleRevenue,
    );
    // 收益信息-模块收益指标结论
    const moduleRevenueConclusion = this.getStoryRevenueConclusionDisplayName(
      subExperimentInfo.revenueInfo?.moduleRevenueMetricAutoComputedConclusion,
      subExperimentInfo.revenueInfo?.moduleRevenueMetricConclusion,
    );
    // 收益信息-有关键过程指标收益
    const hasKeyProcessRevenue = this.getStoryRevenueBooleanDisplayName(
      subExperimentInfo.revenueInfo?.manullyHasKeyProcessRevenue ?? subExperimentInfo.revenueInfo?.hasKeyProcessRevenue,
    );
    // 收益信息-有关键过程指标收益
    const dsHasKeyProcessRevenue = this.getStoryRevenueBooleanDisplayName(
      subExperimentInfo.revenueInfo?.dsHasKeyProcessRevenue,
    );
    // 收益信息-过程指标收益结论
    const keyProcessRevenueConclusion = this.getStoryRevenueConclusionDisplayName(
      subExperimentInfo.revenueInfo?.keyProcessRevenueMetricAutoComputedConclusion,
      subExperimentInfo.revenueInfo?.keyProcessRevenueMetricConclusion,
    );
    // 是否有指标损失
    const hasMetricLoss = this.getStoryRevenueBooleanDisplayName(
      subExperimentInfo.revenueInfo?.manuallyHasMetricLoss ?? subExperimentInfo.revenueInfo?.hasMetricLoss,
    );
    // DS修正收益判断差异说明
    const dsRevenueConclusionRemark = subExperimentInfo.revenueInfo?.dsRevenueConclusionRemark ?? '';
    // 收益信息-是否上线/推全
    const isFullyRelease = '';
    // 收益信息-备注
    const revenueRemark = '';
    // task基础信息-task填写完整性
    const taskFillInCompleted = '';
    // 实验信息-关联实验状态
    const libraStatus = this.getStoryRevenueExperimentStatusDisplayName(subExperimentInfo.status);
    // 实验信息-关联实验开启时间
    const libraStartDate = this.formatUnixTimestamp(subExperimentInfo.startTime);
    // 实验信息-关联实验关闭时间
    const libraEndDate = this.formatUnixTimestamp(subExperimentInfo.endTime);
    // 需求信息-上线区域
    const publishedRegions = '';
    // 需求信息-上线应用(“子实验对应上线应用”)
    const publishedApps = subExperimentInfo.publishedApp ?? '';
    // 需求信息-需求文档
    const meegoPrdUrl = '';
    // 需求信息-涉及研发团队
    const meegoRelatedRDTeams = '';
    // 需求信息-技术Owner
    const meegoTechOwners = '';
    // 需求信息-需求状态
    const meegoStatus = '';
    // 需求信息-需求测试完成时间
    const meegoTestFinishedTime = '';
    // 需求信息-需求内审完成时间
    const meegoInternalReviewFinishedTime = '';
    // 需求信息-创建人
    const meegoCreator = '';
    // 需求信息-meego_id
    const meegoId = '';
    // task基础信息-task终止
    const taskTerminated = '';
    // task基础信息-收益review时长
    const reviewDuration = '';
    // 需求收益复盘文档
    const reviewDocUrl = '';
    // DS review人
    const dsReviewer = '';
    // DS 锁定周期时间
    const dsLockTime = reviewPeriodInfo?.dsLockTime
      ? this.formatUnixTimestamp(reviewPeriodInfo?.dsLockTime, false)
      : '';
    // 是否为分析依据
    const isAnalysisBasis = this.getStoryRevenueBooleanFlagDisplayName(subExperimentInfo.markAsAnalysisBasis);
    // 需求类型
    const meegoType = '';
    // 大盘指标折损
    const ltMetricLossType = this.getLTMetricLossDisplayName(subExperimentInfo.revenueInfo?.ltMetricLossType);
    // 是否与算法相关
    const algorithmRelated = '';
    // 无法回收结论的原因
    const noConclusionReason = '';
    // DS字段是否填写完整
    const revenueInfoCompleted = '';
    // 业务标签
    const bizTag = this.genBizTag(meegoInfo.bizTag);
    // 需求来源业务线
    const sourceBusiness = this.genSourceBusiness(meegoInfo.sourceBusiness);

    return {
      serialNumber,
      taskId,
      meegoTitle,
      meegoUrl,
      meegoPriority,
      meegoOwner,
      meegoBusinessLine,
      meegoSubmodules,
      meegoRDWorkload,
      reviewType,
      reviewPeriodName,
      reviewPeriodId,
      reviewPeriodUrl,
      subExperimentCount,
      libraTitle,
      libraUrl,
      expectationType,
      revenueSelfAssessment,
      hasPlatformRevenue,
      dsHasPlatformRevenue,
      platformRevenueConclusion,
      hasModuleRevenue,
      dsHasModuleRevenue,
      moduleRevenueConclusion,
      hasKeyProcessRevenue,
      dsHasKeyProcessRevenue,
      keyProcessRevenueConclusion,
      hasMetricLoss,
      dsRevenueConclusionRemark,
      isFullyRelease,
      revenueRemark,
      taskFillInCompleted,
      libraStatus,
      libraStartDate,
      libraEndDate,
      publishedRegions,
      publishedApps,
      meegoPrdUrl,
      meegoRelatedRDTeams,
      meegoTechOwners,
      meegoStatus,
      meegoTestFinishedTime,
      meegoInternalReviewFinishedTime,
      meegoCreator,
      meegoId,
      taskTerminated,
      reviewDuration,
      reviewDocUrl,
      dsReviewer,
      dsLockTime,
      isAnalysisBasis,
      meegoType,
      ltMetricLossType,
      algorithmRelated,
      noConclusionReason,
      revenueInfoCompleted,
      bizTag,
      sourceBusiness,
    };
  }

  // Task Info 子行数据，不会包含 task_id，也不包含 Libra 实验信息。根据 taskInfo.publishedAppRevenueInfos 生成 DS 收益标注（按上线应用进行收益分析）
  private taskInfoSubRowDashboardDataByPublishedAppRevenueInfos(
    taskInfo: StoryRevenueTaskInfo,
    publishedAppRevenueInfo: StoryRevenuePublishedAppRevenueInfo,
  ) {
    const { meegoInfo, revenueInfo } = taskInfo;
    const reviewPeriodInfo = this.reviewPeriodIdMapInfo?.get(taskInfo.reviewPeriodId);
    // 【重要！】请勿调整顺序，否则会导致表格数据对不齐
    // 序号
    const serialNumber = '';
    // task基础信息-收益task_id
    const taskId = '';
    // 需求信息-需求名称
    const meegoTitle = meegoInfo.name;
    // 需求信息-需求链接
    const meegoUrl = meegoInfo.url;
    // 需求信息-优先级
    const meegoPriority = meegoInfo.priority;
    // 需求信息-Owner
    const meegoOwner = this.getUsersDisplayName(meegoInfo.owners);
    // 需求信息-业务线
    let meegoBusinessLine = meegoInfo.primaryBusiness;
    if (meegoInfo.secondaryBusiness.length > 0) {
      meegoBusinessLine += `-${meegoInfo.secondaryBusiness}`;
    }
    // 需求信息-子模块
    const meegoSubmodules = meegoInfo.submodules.join(',');
    // 需求信息-RD工作量
    const meegoRDWorkload = meegoInfo.rdWorkload.toString();
    // 收益信息-是否参与review
    const reviewType = this.getStoryRevenueReviewTypeDisplayName(revenueInfo?.reviewType);
    // 收益信息-参与review周期名称
    const reviewPeriodName = reviewPeriodInfo?.reviewPeriodName ?? '';
    // 收益信息-参与review周期 Id(“DS 标注信息：：是否为分析依据”)
    const reviewPeriodId = this.getStoryRevenueBooleanFlagDisplayName(publishedAppRevenueInfo.markAsAnalysisBasis);
    // 收益信息-参与review周期 URL
    const reviewPeriodUrl = '';
    // 实验信息-子实验数量
    const subExperimentCount = '';
    // 实验信息-关联实验名称
    const libraTitle = '';
    // 实验信息-关联实验链接
    const libraUrl = '';
    // 收益信息-收益自评
    const expectationType = this.getStoryRevenueExpectationTypeDisplayName(revenueInfo?.expectationType);
    // 收益信息-自评原因
    const revenueSelfAssessment = this.getRevenueSelfAssessmentDisplayName(
      revenueInfo?.expectationType,
      revenueInfo?.revenueSelfAssessment,
    );
    // 收益信息-有端收益
    const hasPlatformRevenue = '';
    // 收益信息-有端收益
    const dsHasPlatformRevenue = this.getStoryRevenueBooleanDisplayName(publishedAppRevenueInfo.dsHasPlatformRevenue);
    // 收益信息-端收益指标结论
    const platformRevenueConclusion = '';
    // 收益信息-有模块收益
    const hasModuleRevenue = '';
    // 收益信息-有模块收益
    const dsHasModuleRevenue = this.getStoryRevenueBooleanDisplayName(publishedAppRevenueInfo.dsHasModuleRevenue);
    // 收益信息-模块收益指标结论
    const moduleRevenueConclusion = '';
    // 收益信息-有关键过程指标收益
    const hasKeyProcessRevenue = '';
    // 收益信息-有关键过程指标收益
    const dsHasKeyProcessRevenue = this.getStoryRevenueBooleanDisplayName(
      publishedAppRevenueInfo.dsHasKeyProcessRevenue,
    );
    // 收益信息-过程指标收益结论
    const keyProcessRevenueConclusion = '';
    // 是否有指标损失
    const hasMetricLoss = '';
    // DS修正收益判断差异说明
    const dsRevenueConclusionRemark = publishedAppRevenueInfo.dsRevenueConclusionRemark ?? '';
    // 收益信息-是否上线/推全
    const isFullyRelease = '';
    // 收益信息-备注
    const revenueRemark = '';
    // task基础信息-task填写完整性
    const taskFillInCompleted = '';
    // 实验信息-关联实验状态
    const libraStatus = '';
    // 实验信息-关联实验开启时间
    const libraStartDate = '';
    // 实验信息-关联实验关闭时间
    const libraEndDate = '';
    // 需求信息-上线区域
    const publishedRegions = '';
    // 需求信息-上线应用(“对应上线应用”)
    const publishedApps = publishedAppRevenueInfo.publishedApp ?? '';
    // 需求信息-需求文档
    const meegoPrdUrl = '';
    // 需求信息-涉及研发团队
    const meegoRelatedRDTeams = '';
    // 需求信息-技术Owner
    const meegoTechOwners = '';
    // 需求信息-需求状态
    const meegoStatus = '';
    // 需求信息-需求测试完成时间
    const meegoTestFinishedTime = '';
    // 需求信息-需求内审完成时间
    const meegoInternalReviewFinishedTime = '';
    // 需求信息-创建人
    const meegoCreator = '';
    // 需求信息-meego_id
    const meegoId = '';
    // task基础信息-task终止
    const taskTerminated = '';
    // task基础信息-收益review时长
    const reviewDuration = '';
    // 需求收益复盘文档
    const reviewDocUrl = '';
    // DS review人
    const dsReviewer = '';
    // DS 锁定周期时间
    const dsLockTime = reviewPeriodInfo?.dsLockTime
      ? this.formatUnixTimestamp(reviewPeriodInfo?.dsLockTime, false)
      : '';
    // 是否为分析依据
    const isAnalysisBasis = this.getStoryRevenueBooleanFlagDisplayName(publishedAppRevenueInfo.markAsAnalysisBasis);
    // 需求类型
    const meegoType = '';
    // 大盘指标折损
    const ltMetricLossType = '';
    // 是否与算法相关
    const algorithmRelated = '';
    // 无法回收结论的原因
    const noConclusionReason = '';
    // DS字段是否填写完整
    const revenueInfoCompleted = '';
    // 业务标签
    const bizTag = this.genBizTag(meegoInfo.bizTag);
    // 需求来源业务线
    const sourceBusiness = this.genSourceBusiness(meegoInfo.sourceBusiness);

    return {
      serialNumber,
      taskId,
      meegoTitle,
      meegoUrl,
      meegoPriority,
      meegoOwner,
      meegoBusinessLine,
      meegoSubmodules,
      meegoRDWorkload,
      reviewType,
      reviewPeriodName,
      reviewPeriodId,
      reviewPeriodUrl,
      subExperimentCount,
      libraTitle,
      libraUrl,
      expectationType,
      revenueSelfAssessment,
      hasPlatformRevenue,
      dsHasPlatformRevenue,
      platformRevenueConclusion,
      hasModuleRevenue,
      dsHasModuleRevenue,
      moduleRevenueConclusion,
      hasKeyProcessRevenue,
      dsHasKeyProcessRevenue,
      keyProcessRevenueConclusion,
      hasMetricLoss,
      dsRevenueConclusionRemark,
      isFullyRelease,
      revenueRemark,
      taskFillInCompleted,
      libraStatus,
      libraStartDate,
      libraEndDate,
      publishedRegions,
      publishedApps,
      meegoPrdUrl,
      meegoRelatedRDTeams,
      meegoTechOwners,
      meegoStatus,
      meegoTestFinishedTime,
      meegoInternalReviewFinishedTime,
      meegoCreator,
      meegoId,
      taskTerminated,
      reviewDuration,
      reviewDocUrl,
      dsReviewer,
      dsLockTime,
      isAnalysisBasis,
      meegoType,
      ltMetricLossType,
      algorithmRelated,
      noConclusionReason,
      revenueInfoCompleted,
      bizTag,
      sourceBusiness,
    };
  }

  async didExportAeolusDashboardData() {
    const dataList = [];
    // 遍历每一个 task info
    const taskListLength = this.taskInfoList.length;
    for (let i = 0; i < taskListLength; i++) {
      const taskInfo = this.taskInfoList[i];
      // 添加 task info 信息
      const mainRowData = this.taskInfoMainRowDashboardData(taskInfo, i);
      dataList.push(mainRowData);
      // 同时要处理子实验 >= 1 的情况
      if (taskInfo.experimentInfo && taskInfo.experimentInfo.subExperimentCount >= 1) {
        for (const subExperiment of taskInfo.experimentInfo.subExperimentList) {
          // 添加子实验信息
          const subRowData = this.taskInfoSubRowDashboardData(taskInfo, subExperiment);
          dataList.push(subRowData);
        }
      } else if (
        taskInfo.publishedAppRevenueInfos &&
        Array.isArray(taskInfo.publishedAppRevenueInfos) &&
        taskInfo.publishedAppRevenueInfos.length > 0
      ) {
        // 展示 publishedAppRevenueInfos，按照上线应用分析收益信息
        for (const publishedAppRevenueInfo of taskInfo.publishedAppRevenueInfos) {
          const subRowDataByPublishedAppRevenueInfos = this.taskInfoSubRowDashboardDataByPublishedAppRevenueInfos(
            taskInfo,
            publishedAppRevenueInfo,
          );
          dataList.push(subRowDataByPublishedAppRevenueInfos);
        }
      }
    }
    return dataList;
  }
}
