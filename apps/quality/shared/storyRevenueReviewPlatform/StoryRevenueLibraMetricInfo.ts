// Libra 实验指标收益信息（StoryRevenueLibraMetricInfo）
export interface StoryRevenueLibraMetricInfo {
  flightId: string; // 实验 id
  vids: string[]; // 实验组 id（对照组和实验组）
  metricId: string; // 指标 id
  metricName: string; // 指标名称
  metricDesc: string; // 指标描述
  metricGroupId: string; // 所属指标组 id
  metricGroupName: string; // 所属指标组名称
  confidence: number; // 置信度
  absoluteDiff: string; // 绝对差
  relativeDiff: string; // 相对差
  pValue: string; // P 值
  significanceDesc: string; // 显著性（非显著、正向显著、负向显著）
}
