import {
  StoryRevenueTaskInfo,
  StoryRevenueTaskRevenueExpectationType,
  StoryRevenueTaskRevenueReviewType,
  StoryRevenueTaskRevenueSelfAssessmentType,
} from '@shared/storyRevenueReviewPlatform/StoryRevenueTaskInfo';
import {
  ConditionType,
  EnumFilterStatus,
  FilterOptionType,
  FilterRuleDesc,
  FilterStatus,
  FilterType,
  NumberFilterStatus,
  TextFilterStatus,
  TreeEnumFilterStatus,
  TreeEnumOptions,
  UserFilterStatus,
} from '@shared/utils/conditionFilter';
import { compact } from 'lodash';
import {
  StoryRevenueTableColumnConfig,
  StoryRevenueTableColumnDefaultUserSetting,
  StoryRevenueTableColumnFilterConfig,
  StoryRevenueTableColumnIdSort,
} from '@shared/storyRevenueReviewPlatform/StoryRevenueUserSettings/StoryRevenueTableColumnUserSetting';
import { StoryRevenueFaceUMeegoFeildInfo } from '@shared/storyRevenueReviewPlatform/StoryRevenueFaceUMeegoFeildInfo';
import { User } from '@pa/shared/dist/src/core';
import { StoryRevenueReviewPeriodInfo } from '@shared/storyRevenueReviewPlatform/StoryRevenueReviewPeriodInfo';
import { StoryRevenueRoleType } from '@shared/storyRevenueReviewPlatform/StoryRevenueUserMemberInfo';

export const taskInfoEditable = (
  taskInfo: StoryRevenueTaskInfo,
  userRoleType: StoryRevenueRoleType,
  periodInfo?: StoryRevenueReviewPeriodInfo,
) => {
  if (!periodInfo) {
    return false;
  }

  if (taskInfo.updating) {
    return false;
  }

  if (periodInfo.updating) {
    // 整个 Review 周期正在更新中，不允许编辑
    return false;
  }

  if (periodInfo.isSyncingDataToMeego) {
    // 如果数据正在回写到 Meego 中，则不允许编辑
    return false;
  }

  if (periodInfo.dsLocked) {
    // 如果数据已存档，则不允许编辑（DS 和管理员也不行）
    return false;
  }

  if (userRoleType === StoryRevenueRoleType.DataAnalyst || userRoleType === StoryRevenueRoleType.Admin) {
    return true;
  }

  if (periodInfo.locked) {
    return false;
  }

  // 如果版本未锁定，则需要根据用户权限判断
  if (userRoleType === StoryRevenueRoleType.ProductManager) {
    return true;
  }

  return false;
};

export const getMeegoFieldValueAndLabel = (fieldKey: string): { value: string | number; label: string }[] => {
  const meegoOptions = StoryRevenueFaceUMeegoFeildInfo.find(it => it.field_key === fieldKey);
  const res: { value: string | number; label: string }[] = [];
  if (!meegoOptions || !meegoOptions.options) {
    return [];
  }
  meegoOptions.options.forEach(option => {
    if (option.label) {
      res.push({ value: option.label, label: option.label });
    }
    const childrens = option.children as { [key: string]: any }[];
    if (childrens) {
      childrens.forEach(c => {
        if (c.label) {
          res.push({ value: c.label, label: c.label });
        }
      });
    }
  });
  return res;
};

export const getMeegoTreeValue = (fieldKey: string): TreeEnumOptions[] => {
  const meegoOptions = StoryRevenueFaceUMeegoFeildInfo.find(it => it.field_key === fieldKey);
  const res: TreeEnumOptions[] = [];
  if (!meegoOptions || !meegoOptions.options) {
    return [];
  }
  const getChildrenOptions = (
    chilrens: { [key: string]: any }[],
    parentNode: TreeEnumOptions,
    parentKey: string,
  ): TreeEnumOptions[] => {
    const childrenOptionList: TreeEnumOptions[] = [];
    chilrens.forEach((children, index) => {
      const childKey = `${parentKey}-${index.toString()}`;
      const childrenNode = {
        label: children.label,
        value: children.label,
        key: childKey,
        children: [],
      } as TreeEnumOptions;
      childrenNode.children = getChildrenOptions(children.children ?? [], childrenNode, childKey);
      childrenOptionList.push(childrenNode);
    });
    return childrenOptionList;
  };
  meegoOptions.options.forEach((option, index) => {
    const optionNode = {
      label: option.label,
      value: option.label,
      key: index.toString(),
      children: [],
    } as TreeEnumOptions;
    optionNode.children = getChildrenOptions(option.children ?? [], optionNode, index.toString());
    res.push(optionNode);
  });
  return res;
};

const getBoolValueAndOption = (okLabelNames: string, ngLabelNames: string) => [
  {
    value: 'true',
    label: okLabelNames,
  },
  {
    value: 'false',
    label: ngLabelNames,
  },
];

const expectionTypeOptions = (): { value: string | number; label: string }[] => [
  {
    value: StoryRevenueTaskRevenueExpectationType.ExceedExpectations,
    label: '超出预期',
  },
  {
    value: StoryRevenueTaskRevenueExpectationType.MeetExpectations,
    label: '符合预期',
  },
  {
    value: StoryRevenueTaskRevenueExpectationType.BelowExpectations,
    label: '不及预期',
  },
];

export const selfAssesmentType2Desc = (): Record<number, string> => {
  const reviewTypeMap: Record<number, string> = {};
  reviewTypeMap[StoryRevenueTaskRevenueSelfAssessmentType.BelowExpectationsAndContinuousOptimization] =
    '后续继续优化（产品决策正常）';
  reviewTypeMap[StoryRevenueTaskRevenueSelfAssessmentType.BelowExpectationsAndStopTrying] =
    '后续停止尝试（产品决策正常）';
  reviewTypeMap[StoryRevenueTaskRevenueSelfAssessmentType.BelowExpectationsForPMWrongDecision] = '产品决策失误';
  reviewTypeMap[StoryRevenueTaskRevenueSelfAssessmentType.MeetExpectationsForNegativeGovernance] =
    '负向治理（体验优化）';
  reviewTypeMap[StoryRevenueTaskRevenueSelfAssessmentType.MeetExpectationsForLongTermValue] =
    '长期价值（长期收益更大/基础能力建设）';
  reviewTypeMap[StoryRevenueTaskRevenueSelfAssessmentType.MeetExpectationsForLongTermExploration] =
    '长期探索（AI类需要保持耐心探索）';
  reviewTypeMap[StoryRevenueTaskRevenueSelfAssessmentType.MeetExpectationsForVerticalScenario] =
    '垂直场景（高级功能/渗透低的场景，不预期撬动端or模块）';
  reviewTypeMap[StoryRevenueTaskRevenueSelfAssessmentType.ExceedExpectations] = '超出预期';
  return reviewTypeMap;
};

export const reviewType2DescMap = (): Record<number, string> => {
  const reviewTypeMap: { [key: number]: string } = {};
  reviewTypeMap[StoryRevenueTaskRevenueReviewType.ParticipateInTheEvaluation] = '参与评估（能AA/AB严谨量化表现）';
  reviewTypeMap[StoryRevenueTaskRevenueReviewType.NotParticipateForExperimentNotOpen] = '不参与-优化迭代但未开实验';
  reviewTypeMap[StoryRevenueTaskRevenueReviewType.NotParticipateForNoConclusionYet] = '不参与-已开实验但尚无结论';
  reviewTypeMap[StoryRevenueTaskRevenueReviewType.NotParticipateForDataInfrastructure] = '不参与-数据基建/产品基建';
  reviewTypeMap[StoryRevenueTaskRevenueReviewType.NotParticipateForStoryUnderDevelopment] = '不参与-需求开发中';
  reviewTypeMap[StoryRevenueTaskRevenueReviewType.NotParticipateForStoryIsPending] = '不参与-需求pending';
  reviewTypeMap[StoryRevenueTaskRevenueReviewType.NotParticipateForLongTermCapacityBuilding] =
    '不参与-长期建设能力（后续需要参与评估）';
  reviewTypeMap[StoryRevenueTaskRevenueReviewType.NotParticipateForNegativeIssueGovernanceAndExperienceOpt] =
    '不参与-负向问题治理&体验优化';
  reviewTypeMap[StoryRevenueTaskRevenueReviewType.NotParticipateForBugFix] = '不参与-bug修复[废弃]';
  reviewTypeMap[StoryRevenueTaskRevenueReviewType.NotParticipateForSecurityCompliance] =
    '不参与-安全合规降负反馈类[废弃]';
  return reviewTypeMap;
};

const selfAssessmentTypeOptions = (): { value: string | number; label: string }[] => {
  const reviewTypeMap: Record<number, string> = selfAssesmentType2Desc();
  const res: { value: string | number; label: string }[] = [];
  for (const key in reviewTypeMap) {
    res.push({ value: key, label: reviewTypeMap[key] });
  }
  return res;
};

export const getfilterOptions = (
  columnId: string,
  valueType: FilterOptionType,
): { value: string | number; label: string }[] => {
  if (
    [
      'has_platform_revenue',
      'has_module_revenue',
      'has_key_process_revenue',
      'has_metric_loss',
      'ds_has_platform_revenue',
      'ds_has_module_revenue',
      'ds_has_key_process_revenue',
    ].includes(columnId)
  ) {
    return getBoolValueAndOption('有', '无');
  }
  if (columnId === 'is_full_release') {
    return getBoolValueAndOption('是', '暂无计划');
  }
  if (columnId === 'revenue_self_assessment') {
    return selfAssessmentTypeOptions();
  }
  if (columnId === 'task_integrity') {
    return getBoolValueAndOption('完整', '不完整');
  }
  if (valueType === FilterOptionType.TypeBool) {
    return getBoolValueAndOption('是', '否');
  }
  if (columnId === 'is_review_case') {
    const reviewTypeMap: { [key: number]: string } = reviewType2DescMap();
    const res: { value: string | number; label: string }[] = [];
    for (const key in reviewTypeMap) {
      res.push({ value: key, label: reviewTypeMap[key] });
    }
    // 排序一下，把废弃的选项排到最后
    res.sort((a, b) => {
      if (
        Number(a.value) === StoryRevenueTaskRevenueReviewType.NotParticipateForBugFix ||
        Number(a.value) === StoryRevenueTaskRevenueReviewType.NotParticipateForSecurityCompliance
      ) {
        return 1;
      } else if (
        Number(b.value) === StoryRevenueTaskRevenueReviewType.NotParticipateForBugFix ||
        Number(b.value) === StoryRevenueTaskRevenueReviewType.NotParticipateForSecurityCompliance
      ) {
        return -1;
      } else {
        return 0;
      }
    });
    return res;
  }
  if (columnId === 'feature_priority') {
    return [
      {
        value: 'P00',
        label: 'P00',
      },
      {
        value: 'P0',
        label: 'P0',
      },
      {
        value: 'P1',
        label: 'P1',
      },
      {
        value: 'P2',
        label: 'P2',
      },
    ];
  }
  if (columnId === 'source_business') {
    const res = getMeegoTreeValue('field_16bce2');
    return res;
  }
  if (columnId === 'primary_business') {
    const res = getMeegoTreeValue('business');
    return res;
  }
  if (columnId === 'biz_tag') {
    const res = getMeegoTreeValue('field_39ef9f');
    return res;
  }
  if (columnId === 'sub_modules') {
    const res = getMeegoFieldValueAndLabel('field_2a112b');
    return res;
  }
  if (columnId === 'is_match_expected') {
    return expectionTypeOptions();
  }
  if (columnId === 'published_apps') {
    return getMeegoFieldValueAndLabel('supported_apps');
  }
  if (columnId === 'published_regions') {
    return getMeegoFieldValueAndLabel('field_74de8f');
  }
  return [];
};

export const getTargetValue = (status: FilterStatus): string[] | undefined => {
  if (status.config?.filterType === FilterType.FilterTypeEnum) {
    return (status as EnumFilterStatus)?.selectedValues;
  }
  if (status.config?.filterType === FilterType.FilterTypeNumber) {
    const number = (status as NumberFilterStatus)?.inputNumber;
    if (number !== undefined) {
      return [number.toString()];
    } else {
      return undefined;
    }
  }
  if (status.config?.filterType === FilterType.FilterTypeUser) {
    return compact((status as UserFilterStatus)?.selectedUsers?.map((it: User) => it.email));
  }
  if (status.config?.filterType === FilterType.FilterTypeText) {
    return (status as TextFilterStatus)?.inputText;
  }
  if (status.config?.filterType === FilterType.FilterTypeTreeEnum) {
    return (status as TreeEnumFilterStatus)?.selectedValues;
  }
};

export const transTargetValues = (oldValue: string[], config: StoryRevenueTableColumnFilterConfig) => {
  if (config.value_type === FilterOptionType.TypeNumber) {
    return oldValue.map(it => Number(it));
  } else if (config.value_type === FilterOptionType.TypeBool) {
    return oldValue.map(it => it === 'true');
  } else {
    return oldValue;
  }
};

export const transFilterStatus2Rules = (
  columnConfigs: StoryRevenueTableColumnConfig[],
  filterStatus: FilterStatus[],
) => {
  const rules: FilterRuleDesc[] = [];
  const flatColumnConfigs: StoryRevenueTableColumnConfig[] = columnConfigs;
  filterStatus.forEach(it => {
    if (it.conditionType === undefined) {
      return;
    }
    let targetValues: any[] | undefined = getTargetValue(it);
    const columnConfig = flatColumnConfigs.find(it2 => it2.column_id === it.config?.filterId);
    if (!columnConfig || !columnConfig.filter_config) {
      return;
    }
    if (!targetValues || targetValues.length < 1) {
      if (
        it.conditionType === ConditionType.ConditionTypeNotNull ||
        it.conditionType === ConditionType.ConditionTypeNull
      ) {
        rules.push({
          keyPath: columnConfig.filter_config.key_path,
          conditionType: it.conditionType,
          targetValue: [],
          rawIsArray: columnConfig.filter_config.raw_array,
        } as FilterRuleDesc);
      }
      return;
    }
    targetValues = transTargetValues(targetValues, columnConfig.filter_config);
    rules.push({
      keyPath: columnConfig.filter_config.key_path,
      conditionType: it.conditionType,
      targetValue: targetValues,
    } as FilterRuleDesc);
  });
  return rules;
};

export const transRawValue2LogDisplayValue = (columnId: string, value: any): string => {
  // is_analysis_basis特殊处理，不展示空状态
  if (columnId === 'is_analysis_basis' && typeof value === 'boolean') {
    return value === true ? '是' : '否';
  }
  if (value === undefined || value === null) {
    return '空';
  }
  if (columnId === 'ds_reviewer') {
    const users = value as User[];
    if (!users || users.length === 0) {
      return '空';
    }
    return users.map(it => it.name).toString();
  }
  if (columnId === 'revenue_self_assessment') {
    const assementTypeMap: Record<number, string> = selfAssesmentType2Desc();
    return assementTypeMap[Number(value)] !== undefined ? assementTypeMap[Number(value)] : '空';
  }
  if (columnId === 'is_match_expected') {
    const exceptionTypeOption = expectionTypeOptions().find(it => Number(it.value) === Number(value));
    return exceptionTypeOption?.label ?? '空';
  }
  if (columnId === 'is_review_case') {
    const reviewTypeMap: Record<number, string> = reviewType2DescMap();
    return reviewTypeMap[Number(value)] !== undefined ? reviewTypeMap[Number(value)] : '空';
  }
  if (
    columnId === 'has_platform_revenue' ||
    columnId === 'has_module_revenue' ||
    columnId === 'has_key_process_revenue' ||
    columnId === 'has_metric_loss' ||
    columnId === 'ds_has_platform_revenue' ||
    columnId === 'ds_has_module_revenue' ||
    columnId === 'ds_has_key_process_revenue'
  ) {
    if (value === true) {
      return '有';
    } else if (value === false) {
      return '无';
    }
  }
  if (columnId === 'is_full_release') {
    if (value === true) {
      return '是';
    } else if (value === false) {
      return '暂无计划';
    }
  }
  if (typeof value === 'string') {
    return value.split('\n').join(' ');
  }
  if (typeof value === 'boolean') {
    return value === true ? '是' : '否';
  }
  return value ? value.toString() : '空';
};

export const TableColumnConfigMap = () => {
  const configMap: { [key: string]: StoryRevenueTableColumnConfig } = {};
  const defaultColumnsSetting = StoryRevenueTableColumnDefaultUserSetting();
  defaultColumnsSetting.groups.forEach(it => {
    const columnConfigs: StoryRevenueTableColumnConfig[] = [];
    it.configs.forEach(config => {
      configMap[config.column_id] = config;
      if (StoryRevenueTableColumnIdSort().includes(config.column_id)) {
        columnConfigs.push(config);
      }
    });
  });
  return configMap;
};

export const objectToKeyValueWithFullFieldPath = (obj: any, parentPath = ''): Record<string, any> => {
  const result: Record<string, any> = {};
  for (const key in obj) {
    if (Object.prototype.hasOwnProperty.call(obj, key)) {
      const currentPath = parentPath ? `${parentPath}.${key}` : key;
      if (typeof obj[key] === 'object' && obj[key] !== null) {
        const nestedResult = objectToKeyValueWithFullFieldPath(obj[key], currentPath);
        Object.assign(result, nestedResult);
      } else {
        result[currentPath] = obj[key];
      }
    }
  }
  return result;
};

export const getValueByFieldPath = (obj: any, fieldPath: string): any => {
  const pathArray = fieldPath.split('.');
  let current = obj;
  for (const key of pathArray) {
    if (current[key] === undefined) {
      return undefined;
    }
    current = current[key];
  }
  return current;
};
