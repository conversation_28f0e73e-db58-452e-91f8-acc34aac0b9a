import { PlatformType } from '@pa/shared/dist/src/core';

export enum ConfigType {
  array = 'array',
  string = 'string',
  user_array = 'user_array',
  user = 'user',
  chat_array = 'chat_array',
  chat = 'chat',
  slardar_issue_list = 'slardar_issue_list',
}

export interface AirplaneConfig {
  businessId: string; // 业务线;

  platform?: PlatformType;

  name: string; // 配置项

  configType: ConfigType; // 用于标明当前项的类型

  data?: any; // 配置内容
}

export interface SettingsConfigItem {
  requestPlatform: boolean;
  configType: ConfigType;
  label: string;
  placeholder: string;
  rules: any[];
}

export enum VersionConfigKeys {
  autoLevelBlackList = 'autoLevelBlackList',
}

export const VERSION_CONFIG: {
  [key: string]: SettingsConfigItem;
} = {
  autoLevelBlackList: {
    requestPlatform: true,
    configType: ConfigType.slardar_issue_list,
    label: 'Slardar 自动建单黑名单',
    placeholder: '请输入Issue 链接',
    rules: [{ required: true, message: '请输入Issue 链接' }],
  },
};
