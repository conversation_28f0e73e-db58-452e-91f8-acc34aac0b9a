export interface WalleResponse<T> {
  data: T;
  error_msg: string;
  ret: number;
}

export interface ConsumeResult {
  all_assignees: string[];
  component_name: string;
  assign_types: AssignType[];
}

export interface AssignType {
  assignees: string[];
  assign_type_name: string;
  assign_type_info: string;
}

export enum DiscoverStage {
  NEW_FEATURE = 'new_feature',
  INTEGRATION = 'integration', // 集成阶段
  GREY = 'grey',
  USER_STORY = 'lr',
  SMALL = 'test',
  ONLINE = 'release',
}

export const DiscoverStag2Option: { [key: string]: string } = {
  new_feature: 'option_1',
  integration: 'option_6',
  grey: 'option_12',
  lr: 'option_4',
  test: 'jk5c1n7xz',
  release: 'option_11',
};
