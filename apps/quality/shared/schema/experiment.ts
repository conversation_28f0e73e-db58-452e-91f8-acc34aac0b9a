import { z } from 'zod';
import { HandleStatus, IndicatorType, Priority } from '@shared/libra/common';

export const FeedbackRequestInfoSchema = z.object({
  name: z.string(),
  url: z.string(),
  id: z.string(),
  ownerInfo: z.array(z.string()),
  appId: z.array(z.number()),
  priority: z.nativeEnum(Priority),
  meegoId: z.number().optional(),
  data: z.array(
    z.object({
      vId: z.string(),
      feedbackCount: z.number(),
      hasError: z.boolean(),
    }),
  ),
});

export const FeedbackRequestInfosSchema = z.object({ data: z.array(FeedbackRequestInfoSchema) });
const CrashDataEntrySchema = z.object({
  value: z.number(),
  valueSum: z.number(),
  version: z.string().optional(),
  appId: z.number(),
  hasError: z.boolean(),
  url: z.string(),
});

const CrashTypeSchema = z.record(
  z.object({
    data: z.array(CrashDataEntrySchema),
  }),
);

export const CrashRequestDataSchema = z.record(CrashTypeSchema);

// 定义 CrashRequestInfoSchema
export const CrashRequestInfoSchema = z.object({
  name: z.string(),
  url: z.string(),
  id: z.string(),
  ownerInfo: z.array(z.string()),
  appId: z.array(z.number()),
  priority: z.nativeEnum(Priority),
  meegoId: z.string().optional(),
  data: CrashRequestDataSchema,
});

export const CrashRequestInfosSchema = z.object({ data: z.array(CrashRequestInfoSchema) });
export const GroupRequestInfoSchema = z.object({
  name: z.string(),
  url: z.string(),
  id: z.string(),
  ownerInfo: z.array(z.string()),
  appId: z.array(z.number()),
  priority: z.nativeEnum(Priority),
  meegoId: z.number().optional(),
  data: z.object({
    day: z.number(),
    msg: z.array(z.string()),
    detail: z.string(),
  }),
});

export const GroupRequestInfosSchema = z.object({ data: z.array(GroupRequestInfoSchema) });

const LTDataSchema = z.object({
  data: z.array(
    z.object({
      name: z.string(),
      value: z.number(),
    }),
  ),
  hasError: z.boolean(),
});

export const LTRequestInfoSchema = z.object({
  name: z.string(),
  url: z.string(),
  id: z.string(),
  ownerInfo: z.array(z.string()),
  appId: z.array(z.number()),
  priority: z.nativeEnum(Priority),
  meegoId: z.number().optional(),
  data: z.array(
    z.object({
      vId: z.string(),
      lt: LTDataSchema,
      export: LTDataSchema,
      share: LTDataSchema,
      other: LTDataSchema,
    }),
  ),
});

export const LTRequestInfosSchema = z.object({ data: z.array(LTRequestInfoSchema) });

export const CreateBugParamSchema = z.object({
  id: z.string(),
  appId: z.number(),
  indicatorType: z.nativeEnum(IndicatorType),
});

export const InviteGroupParamSchema = z.object({
  email: z.string(),
  chatId: z.string(),
});

export const ExperimentParamSchema = z.object({
  pageSize: z.number(),
  current: z.number(),
  appId: z.number(),
  indicatorType: z.nativeEnum(IndicatorType),
  isVersion: z.boolean().optional(),
  sorter: z
    .array(
      z.object({
        key: z.string(),
        order: z.string(),
      }),
    )
    .optional(),
  filters: z
    .array(
      z.object({
        columnKey: z.string(),
        filter: z.array(z.any()),
      }),
    )
    .optional(),
  version: z.string().optional(),
});

export const updateExperimentParam = z.object({
  id: z.string(),
  appId: z.number(),
  indicatorType: z.nativeEnum(IndicatorType),
  handleStatus: z.nativeEnum(HandleStatus),
  priority: z.nativeEnum(Priority),
  remark: z.string(),
  isVersion: z.boolean().optional(),
});

export const getExperimentContrastSchema = z.object({
  id: z.array(z.string()).optional(),
});

export const setExperimentContrastSchema = z.object({
  id: z.string(),
  appId: z.number(),
  indicatorType: z.nativeEnum(IndicatorType),
  contrastVid: z.string().optional(),
});

export const setExperimentSafeSchema = z.object({
  id: z.string(),
  appId: z.number(),
  time: z.number(),
  indicatorType: z.nativeEnum(IndicatorType),
  vid: z.string().optional(),
  vids: z.array(z.string()),
});
