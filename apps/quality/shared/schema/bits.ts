import { z } from 'zod';
import { GroupName } from '@shared/utils/offlineslardar';

export const queryBitsVersionLogSchema = z.object({
  PageNum: z.number(),
  PageSize: z.number(),
  VersionName: z.optional(z.array(z.string())),
  VersionCode: z.optional(z.array(z.string())),
  CPU: z.optional(z.array(z.string())),
  ReleasePlatforms: z.optional(z.array(z.string())),
  AppId: z.number(),
});

export const registerBitsVersionSchema = z.object({
  version_name: z.string(),
  version_code: z.string(),
  callback_addr: z.string(),
  job_id: z.string(),
});

export const queryBitsVersionAmountSchema = z.object({
  version_code: z.string(),
});

export const queryStartTimeParams = z.object({
  group_name: z.nativeEnum(GroupName),
  version: z.string(),
  app_release_info_version: z.preprocess(
    val => parseInt(String(val), 10),
    z.number().refine(v => v === 1, { message: 'app_release_info_version must be 1' }),
  ),
});
