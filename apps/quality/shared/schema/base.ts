import { z } from 'zod';
export const avatarSchema = z.object({
  avatar_240: z.string().optional(),
  avatar_640: z.string().optional(),
  avatar_72: z.string().optional(),
  avatar_origin: z.string().optional(),
});
export const userSchema = z.object({
  name: z.string(),
  email: z.string().optional(),
  open_id: z.string(),
  user_id: z.string().optional(),
  avatar: avatarSchema.optional().or(z.string()),
});
