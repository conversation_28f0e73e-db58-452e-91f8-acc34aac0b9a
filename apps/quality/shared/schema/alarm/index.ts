import { z } from 'zod';
import { SlardarPlatformType } from '@pa/shared/dist/src/appSettings/appSettings';
import { CrashType } from '@shared/typings/slardar/crash/issueListSearch';
import { DeviceLevel } from '@shared/common';

export interface Condition {
  type?: string;
  op?: string;
  dimension?: string;
  value?: string;
  values?: string[];
  map_key?: string;
  func?: string;
  args?: string[];
  sub_condition?: Condition;
  sub_conditions?: Condition[];
  sub_string_length?: number;
  groupKey?: string | null;
}

export const ConditionSchema: z.ZodType<Condition> = z.lazy(() =>
  z.object({
    type: z.string().optional(),
    op: z.string().optional(),
    dimension: z.string().optional(),
    value: z.string().optional(),
    values: z.array(z.string()).optional(),
    map_key: z.string().optional(),
    func: z.string().optional(),
    args: z.array(z.string()).optional(),
    sub_condition: ConditionSchema.optional(),
    sub_conditions: z.array(ConditionSchema).optional(),
    sub_string_length: z.number().optional(),
    groupKey: z.string().nullable().optional(),
  }),
);

export const queryGetSlardarCommentsSchema = z.object({
  aid: z.number(),
  issue_id: z.string(),
  platform: z.nativeEnum(SlardarPlatformType),
});

export const addSlardarCommentsSchema = z.object({
  aid: z.number(),
  issue_id: z.string(),
  platform: z.nativeEnum(SlardarPlatformType),
  comment: z.string(),
});

export const getIssuePercentParamsSchema = z.object({
  aid: z.number(),
  platform: z.string(),
  region: z.string(),
  sdk: z.boolean().optional(),
  start_time: z.number(),
  end_time: z.number(),
  crash_type: z.string(),
  crash_time_type: z.string(),
  issue_id: z.string(),
  filters_conditions: ConditionSchema,
  limit: z.number(),
  granularity: z.number(),
  field: z.string(),
  map_key: z.string().optional(),
});

export const QueryGetCrashListRequestSchema = z.object({
  // GetCrashListRequest 的字段
  aid: z.number(),
  os: z.string(),
  region: z.string(),
  pgsz: z.number(),
  pgno: z.number(),
  filters_conditions: ConditionSchema,
  start_time: z.number(),
  end_time: z.number(),
  granularity: z.number(),
  order_by: z.string(),
  crash_time_type: z.string(),
  crash_type: z.string(),
  labels: z.array(z.number()).optional(),
  group_name: z.string().optional(),
  filters: z.string().optional(),
  sdk_version_conditions: ConditionSchema.optional(),
  versions_conditions: ConditionSchema.optional(),
  status: z.array(z.string()).optional(),
  issue_levels: z.array(z.number()).optional(),
  tags: z.array(z.string()).optional(),
  modules: z.array(z.number()).optional(),
  managers: z.array(z.string()).optional(),
  token: z.string().optional(),
  token_type: z.number().optional(),
  message: z.string().optional(),
  is_new: z.boolean().optional(),
  is_done: z.boolean().optional(),
  search_field: z.string().optional(),
  min_crash_count: z.number().optional(),
  min_crash_user: z.number().optional(),
  tag_filters: z.record(z.string()).optional(),
  simple: z.boolean().optional(),
  versions: z.array(z.string()).optional(),
  pre_issue_id: z.string().optional(),
  sdk: z.boolean().optional(),
  extra: z.record(z.string()).optional(),
  view_id: z.number().optional(),
  shortCutKey: z.string().optional(),
  type: z.string().optional(),
  ios_issue_id_version: z.string().optional(),
  is_issue_based_tags: z.boolean().optional(),
  group_bys: z.array(z.string()).optional(),
  is_sub_issue_agg: z.boolean().optional(),
  subregion: z.string().optional(),
});

export const queryPreviousVersionSchema = z.object({
  aid: z.number(),
  version: z.string(),
  versionCode: z.optional(z.string()),
});

export const queryRecentVersionSchema = z.object({
  appId: z.number(),
  platform: z.optional(z.nativeEnum(SlardarPlatformType)),
  pageNum: z.optional(z.number()),
  pageSize: z.optional(z.number()),
  sort: z.optional(z.string()),
  filter: z.optional(z.string()),
});

export const querySlardarValueSchema = z.object({
  aid: z.number(),
  versionCodes: z.array(z.string()),
  slardarInfoId: z.string(),
  baseTimestamp: z.optional(z.number()),
  baseVersion: z.optional(z.string()),
  deviceLevel: z.nativeEnum(DeviceLevel),
});

export const querySlardarInfoSchema = z.object({
  aid: z.number(),
  platform: z.string(),
});
export const querySlardarVersionQualityValueSchema = z.object({
  versions: z.array(z.string()),
  deviceLevel: z.nativeEnum(DeviceLevel),
});

export const queryVersionDetail = z.object({
  aid: z.number(),
  version_code: z.array(z.string()),
});

export const fetchSceneListParam = z.object({
  scene: z.string(),
  owner: z.string(),
});

export const updateSceneOwnerParam = z.object({
  aid: z.number(),
  scene: z.string(),
  platform: z.string(),
  owner: z.string(),
});

export const querySlardarCrashSchema = z.object({
  aid: z.number(),
  pageSize: z.number(),
  current: z.number(),
  platform: z.nativeEnum(SlardarPlatformType),
  versionCode: z.string().optional(),
  start_time: z.number(),
  end_time: z.number(),
  baseVersionCode: z.string(),
  crashType: z.nativeEnum(CrashType),
  product: z.string(),
  isDeteriorate: z.boolean(),
  filterOOM: z.boolean().optional(),
  filters: z
    .array(
      z.object({
        columnKey: z.string(),
        filter: z.array(z.any()),
      }),
    )
    .optional(),
  isNew: z.boolean().optional(),
  isMemoryGraph: z.boolean().optional(),
  version: z.string().optional(),
  deviceLevel: z.nativeEnum(DeviceLevel),
  sorter: z.record(z.enum(['ascend', 'descend'])).optional(),
});

export const updateSlardarWarnSchema = z.object({
  aid: z.number(),
  platform: z.nativeEnum(SlardarPlatformType),
  versionCode: z.string(),
  crashType: z.nativeEnum(CrashType),
  issue_id: z.string(),
  is_warning: z.boolean().optional(),
});

export const updateSlardarCrashSchema = z.object({
  platform: z.nativeEnum(SlardarPlatformType),
  versionCode: z.string(),
  crashType: z.nativeEnum(CrashType),
  issue_id: z.string(),
  is_warning: z.boolean().optional(),
  issue_level: z.number(),
  meego_url: z.string().optional(),
  assignee: z.string().optional(),
  crash_file: z.string().optional(),
  crash_clazz: z.string().optional(),
  creator: z.string(),
});

export const updateSlardarCrashIssueSchema = z.object({
  aid: z.number(),
  issue_id: z.string(),
  meego_url: z.string(),
  version_code: z.string(),
  version: z.string().optional(),
});

export const checkAndUpdateBugSchema = z.object({
  url: z.string(),
  priority: z.number(),
  version_code: z.string(),
  platform: z.nativeEnum(SlardarPlatformType),
  version: z.string().optional(),
});
export const meegoBugSchema = z.object({
  issueLink: z.string(),
  discoverVersion: z.string(),
  priority: z.string(),
  assignee: z.string().optional(),
});

export const updateIssueLevelSchema = z.object({
  aid: z.number(),
  platform: z.nativeEnum(SlardarPlatformType),
  version_code: z.string(),
  crash_type: z.nativeEnum(CrashType),
  device_level: z.nativeEnum(DeviceLevel),
});

export const createBugIssueSchema = z.object({
  aid: z.number(),
  platform: z.nativeEnum(SlardarPlatformType),
  versionCode: z.string(),
  version: z.string().optional(),
  creator: z.string(),
  access_token: z.string(),
});

export const sendWarningIssueSchema = z.object({
  aid: z.number(),
  platform: z.nativeEnum(SlardarPlatformType),
  versionCode: z.string(),
  start_time: z.number(),
  end_time: z.number(),
});

export const top10CrashListSchema = z.object({
  version_code: z.string(),
  platform: z.nativeEnum(SlardarPlatformType),
  crash_type: z.nativeEnum(CrashType),
  deviceLevel: z.nativeEnum(DeviceLevel),
  aid: z.number().optional(),
});

export const queryNewCrashSchema = z.object({
  aid: z.number(),
  platform: z.nativeEnum(SlardarPlatformType),
  crashType: z.nativeEnum(CrashType),
  version_code: z.string(),
  start_time: z.number(),
  end_time: z.number(),
});

export const queryHistoryCrashSchema = z.object({
  aid: z.number(),
  platform: z.nativeEnum(SlardarPlatformType),
  crashType: z.nativeEnum(CrashType),
  date: z.string(),
  timeStamp: z.number(),
  issueIds: z.array(z.string()),
});

export const queryCrashRateMetricSchema = z.object({
  platform: z.nativeEnum(SlardarPlatformType),
  start_time: z.number(),
  end_time: z.number(),
});
export const queryCrashListWithIssueIdSchema = z.object({
  version_code: z.string(),
  platform: z.nativeEnum(SlardarPlatformType),
  issue_list: z.array(z.string()),
  crash_type: z.nativeEnum(CrashType),
  deviceLevel: z.nativeEnum(DeviceLevel),
  aid: z.number().optional(),
});

export const querySlardarCrashIssueSchema = z.object({
  aid: z.number(),
  issue_list: z.array(z.string()),
});

export const findJOBInfoByIssueSchema = z.object({
  issue_link: z.string(),
  is_oversea: z.boolean(),
});

export const findGrayVersionTimeSchema = z.object({
  version: z.string(),
});
