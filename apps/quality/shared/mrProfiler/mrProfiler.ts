// 用于归因的数据来源
import { MRCodeChangeGitDiffInfo } from '../../api/model/MRCodeChangeModel';

export enum MRAttrInputSource {
  UNKNOWN = 0,
  BACKTRACE = '堆栈归因',
  ALOG = 'alog归因',
}

// 用于MR搜索的数据结构
export interface MRAttrSearchMethodInfo {
  file_path: string;
  method: string;
  class: string;
}

export interface BatchMRSearchParam {
  target_name: string;
  versions: string[];
  method_infos: MRAttrSearchMethodInfo[];
}

// MR归因结果的修改点
export interface MRAttrChangePoint {
  path: string;
  method: string[];
}

export interface MRAttrChangePointWithIndex {
  change_points: MRAttrChangePoint[];
  index: number;
}

export interface MRAttrInfo {
  mr_id: number;
  mr_title: string;
  mr_author: string;
  change_points: MRAttrChangePoint[];
  git_diff_infos: MRCodeChangeGitDiffInfo;
  source: MRAttrInputSource;
  is_merged: boolean;
  index: number;
}

export interface MRAttrVersionedInfo {
  mr_infos: MRAttrInfo[];
  version: string;
}

export interface MRAttrResultData {
  versioned_mr_infos: MRAttrVersionedInfo[];
}

// 用于前端展示的MR归因结果
export interface DisplayMRAttrInfo {
  version: string;
  id: string;
  mr_id: number;
  mr_title: string;
  mr_author: string;
  change_points: MRAttrChangePoint[];
  source: MRAttrInputSource;
  git_diff_infos: MRCodeChangeGitDiffInfo;
  is_merged: boolean;
  index: number;
}
