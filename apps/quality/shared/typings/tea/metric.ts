import { VersionType } from '@shared/utils/version_utils';
import { DBSlardarVersionQualityIssue } from '../../../api/model/SlardarVersionQualityIssueTable';
import { DBSlardarCrashIssue } from '../../../api/model/SlardarCrashIssueTable';

/**
 * 基础结构
 */
export enum ResultCode {
  success = 0,
  error = 1,
}

export enum TeaIdType {
  /**
   * 请求结果ID
   */
  RESULT_ID = 1,
  /**
   * 看板图表ID
   */
  REPORT_ID,

  // 新 tea
  SNAPSHOT_ID,
}

// 数据的分组类型，如连续数据或按版本分组的数据
export enum TeaDimensionType {
  SERIAL,
  VERSION,
}

export const TeaDimensionName: Readonly<Record<TeaDimensionType, string>> = {
  [TeaDimensionType.SERIAL]: '大盘数据',
  [TeaDimensionType.VERSION]: '灰度数据',
};

export enum TeaMetricType {
  SINGLETON, // 事件指标
  COMPUTE, // 组合指标
}

export enum Business {
  TOOLS, // 工具
  ECOSYSTEM, // 生态
  CLOUD_SPACE, // 云空间
  COMMERCIALISE, // 商业化
  INFRASTRUCTURE, // 基础技术
  MAIN_FRAMEWORK, // 主框架
  RETOUCH_MAIN, // 图像编辑器-主框架
  RETOUCH_EDIT, // 图像编辑器-图像编辑
  DREAMINA_TOOLS, // 即梦-工具
  DREAMINA_COMMUNTIY, // 即梦-社区
  DREAMINA_MAIN_FRAMEWORK, // 即梦-主框架
  DREAMINA_COMMERCIALISE, // 即梦商业化
}

export const BusinessName: Readonly<Record<Business, string>> = {
  [Business.TOOLS]: '工具',
  [Business.ECOSYSTEM]: '生态',
  [Business.CLOUD_SPACE]: '商业创作-云空间',
  [Business.COMMERCIALISE]: '商业化',
  [Business.INFRASTRUCTURE]: '基础技术',
  [Business.MAIN_FRAMEWORK]: '主框架',
  [Business.RETOUCH_MAIN]: '图像编辑器-主框架',
  [Business.RETOUCH_EDIT]: '图像编辑器-图像编辑',
  [Business.DREAMINA_TOOLS]: '即梦-工具', // 即梦-工具
  [Business.DREAMINA_COMMUNTIY]: '即梦-社区', // 即梦-社区
  [Business.DREAMINA_MAIN_FRAMEWORK]: '即梦-主框架', // 即梦-主框架
  [Business.DREAMINA_COMMERCIALISE]: '即梦-商业化', // 即梦商业化
};

export enum TeaPriority {
  MELTDOWN, // 熔断
  WATCH, // 仅观察
}

export const TeaPriorityName: Readonly<Record<TeaPriority, string>> = {
  [TeaPriority.MELTDOWN]: '熔断',
  [TeaPriority.WATCH]: '仅观察',
};

/**
 * 值为分钟数
 */
export enum TeaGranularity {
  MIN_5 = 5,
  MIN_30 = 30,
  HOUR_1 = 60,
  HOUR_3 = 180,
  HOUR_6 = 360,
  HOUR_12 = 720,
  HOUR_24 = 1440,
  DAY_7 = 10080,
  DAY_14 = 20160,
  ALL = -1,
}

export const TeaGranularityName: Readonly<Record<TeaGranularity, string>> = {
  [TeaGranularity.MIN_5]: '5 min',
  [TeaGranularity.MIN_30]: '30 min',
  [TeaGranularity.HOUR_1]: '1 hour',
  [TeaGranularity.HOUR_3]: '3 hour',
  [TeaGranularity.HOUR_6]: '6 hour',
  [TeaGranularity.HOUR_12]: '12 hour',
  [TeaGranularity.HOUR_24]: '24 hour',
  [TeaGranularity.DAY_7]: '7 day',
  [TeaGranularity.DAY_14]: '14 day',
  [TeaGranularity.ALL]: 'all',
};

export enum Platform {
  Android = 1,
  iOS = 2,
  PC = 3,
  Multi = 4,
}

export enum TeaValueType {
  ELAPSED_TIME,
  FAILURE_RATE,
  SUCCESS_RATE,
  DATA_RATE,
  FRAME_RATE,
}

export const TeaValueTypeName: Readonly<Record<TeaValueType, string>> = {
  [TeaValueType.ELAPSED_TIME]: '耗时',
  [TeaValueType.FAILURE_RATE]: '失败率',
  [TeaValueType.SUCCESS_RATE]: '成功率',
  [TeaValueType.DATA_RATE]: '数据速率',
  [TeaValueType.FRAME_RATE]: '帧率',
};

export const TeaValueUnit: Readonly<Record<TeaValueType, string>> = {
  [TeaValueType.ELAPSED_TIME]: 'ms',
  [TeaValueType.FAILURE_RATE]: '%',
  [TeaValueType.SUCCESS_RATE]: '%',
  [TeaValueType.DATA_RATE]: 'KB/s',
  [TeaValueType.FRAME_RATE]: 'fps',
};

export interface CompareVersionInfo {
  version: string;
  value: number;
}

export interface AlarmVersion {
  version: string;
  versionCode: string;
  versionType: VersionType;
  timestamp: number;
}

export interface MetricCreationInfo {
  tea_id: string;
  display_name: string;
  name: string;
  granularity: TeaGranularity[];
  refresh_rate: TeaGranularity;
  dimension: TeaDimensionType;
  platform: Platform;
  type: TeaIdType;
  value_type: TeaValueType;
  poc: string;
  business: Business;
  priority: TeaPriority;
  business_module?: string;
  path: [string, string];
}

export interface DegradationData {
  version: string;
  javaDegradationIssues: DBSlardarVersionQualityIssue[];
  nativeDegradationIssues: DBSlardarVersionQualityIssue[];
  issueInfos: DBSlardarCrashIssue[];
}
