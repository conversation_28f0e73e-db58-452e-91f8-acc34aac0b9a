/**
 * Reference: https://bytedance.larkoffice.com/docx/AsPKdYf9MoEIJzxetXeciEmvntf
 */
import { Empty } from '@shared/utils/tools';

export enum TeaPeriodType {
  RANGE = 'range',
  LAST = 'last',
  TODAY = 'today',
  PAST_RANGE = 'past_range',
}

export enum TeaPeriodTimezone {
  CN = 'Asia/Shanghai',
}

export enum TeaPeriodUnit {
  DAY = 'day',
  WEEK = 'week',
  MONTH = 'month',
  YEAR = 'year',
}

export enum TeaPeriodGranularity {
  ALL = 'all',
  SECOND = 'second',
  MINUTE = 'minute',
  FIVE_MINUTE = 'five_minute',
  TEN_MINUTE = 'ten_minute',
  FIFTEEN_MINUTE = 'fifteen_minute',
  THIRTY_MINUTE = 'thirty_minute',
  HOUR = 'hour',
  DAY = 'day',
  WEEK = 'week',
  MONTH = 'month',
  QUARTER = 'quarter',
  YEAR = 'year',
}

export interface TeaPeriodSpan {
  type: 'timestamp' | 'past';
  timestamp?: string; // 你怎么写的接口，这东西用字符串？
  past?: {
    unit: TeaPeriodUnit;
    amount: number;
  };
  offset?: number;
}

export interface TeaPeriod {
  granularity: TeaPeriodGranularity;
  type: TeaPeriodType;
  last?: {
    unit: TeaPeriodUnit;
    amount: number;
  };
  range?: [number, number];
  timezone?: TeaPeriodTimezone;
  interval?: number;
  real_time?: boolean;
  week_start?: number;
  spans?: [TeaPeriodSpan, TeaPeriodSpan];
  align_unit: TeaPeriodGranularity;
}

export enum TeaProfileFilterConditionOp {
  EQ = '=',
  NE = '!=',
  GT = '>',
  GE = '>=',
  LT = '<',
  LE = '<=',
  IS_NOT_NULL = 'is_not_null',
  IS_NULL = 'is_null',
  IN = 'in',
  NOT_IN = 'not_in',
  CONTAIN = 'contain',
  NOT_CONTAIN = 'not_contain',
}

export interface TeaProfileFilterCondition {
  property_name: string;
  property_type: string;
  property_operation: TeaProfileFilterConditionOp;
  property_compose_type?: string;
  property_value_type?: string;
  property_values: (string | number)[];
}

export interface TeaProfileFilterExpression {
  logic: 'and' | 'or';
  conditions: TeaProfileFilterCondition[];
}

export interface TeaProfileFilters {
  show_name: string;
  show_label: string; // consistent
  expression: {
    logic: 'and' | 'or';
    expressions?: TeaProfileFilterExpression[];
    conditions?: TeaProfileFilterCondition[];
  };
}

export interface TeaDslContentQuery {
  event_name?: string;
  event_id?: number;
  event_type?: 'origin' | 'virtual' | 'bav';
  groups_v2?: string[];
  filter?: TeaProfileFilters[];
  show_label: string;
  show_name: string;
  formula?: string;
  combination_format?: string;
  event_indicator?: string;
  measure_info?: any; // 暂时没用
}

export interface TeaDslContentOption {
  refresh_cache?: boolean;
}

export enum TeaDslQueryType {
  EVENT = 'event',
  RETENTION = 'retention',
  FUNNEL = 'funnel',
  PATH_FIND = 'path_find',
  LIFE_CYCLE = 'life_cycle',
  WEB_SESSION = 'web_session',
  EVENT_TOPK = 'event_topk',
  LTV = 'ltv',
  BEHAVIOR_ATTRIBUTION = 'behavior_attribution',
  COMPOSITION = 'composition',
  ADVERTISE = 'advertise',
  CONFIDENCE = 'confidence',
}

export interface TeaDslContent {
  profile_groups_v2: []; // 暂不支持分组
  profile_filters: TeaProfileFilters[];
  orders: []; // 排序暂时没啥用吧
  query_type: TeaDslQueryType;
  queries: TeaDslContentQuery[][];
  option: TeaDslContentOption;
  page: {
    limit: number;
    offset: number;
  };
}

export interface TeaDsl {
  version: 3;
  app_ids: [];
  use_app_cloud_id: true;
  option: Empty;
  show_option: Empty;
  resources: [
    {
      project_ids: [number];
      subject_ids: [1];
      app_ids: [];
    },
  ];
  periods: TeaPeriod[];
  content: TeaDslContent;
}

export interface TeaAnalysisDataItem {
  item_key: string;
  event_show_name: string;
  filter_label?: string;
  show_name: string;
  show_label: string;
  data: number[];
  formatted_data: string[];
  data_for_draw: number[];
  contain_realtime: boolean;
  avg: number;
  sum: number;
}

export interface TeaAnalysisData {
  trace_id: string;
  query_id: string;
  result_status: string;
  error_code?: number;
  error_message?: string;
  result_time: number;
  date_index_list: string[];
  data_item_list: TeaAnalysisDataItem[];
}
