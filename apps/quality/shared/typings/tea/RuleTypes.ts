import { PlatformType } from '@pa/shared/dist/src/core';
import { InnerOperator } from '../../../api/service/alarm/engine/Condition';

export interface AlarmCondition {
  conditionName: string;
  metricName: string;
  displayName: string;
  compareVersion: CompareVersionType;
  valueType: ValueType;
  owner: string;
  operator: Operator;
  value: number;
}

export interface AlarmRule {
  metricName: string;
  appId: string;
  platformType: PlatformType;
  name: string;
  displayName: string;
  owner: string;
  conditions?: AlarmCondition[];
}

export interface AlarmNewRulesContainer {
  metricName: string;
  appId: string;
  platformType: PlatformType;

  displayName?: string;
  owner?: string;
  business?: string;

  rules: AlarmNewRule[]; // or
}

export interface AlarmNewRule {
  conditions: AlarmNewCondition[]; // and
}

export interface AlarmNewCondition {
  compareVersion: CompareVersionType;
  operator: Operator;
  valueType: ValueType;
  value: number;
}

export enum CompareVersionType {
  currentVersion = 'currentVersion',
  lastSmallVersion = 'lastSmallVersion',
  lastBigVersion = 'lastBigVersion',
}
export enum ValueType {
  absolute = 'absolute',
  percentage = 'percentage',
}

export type Operator = InnerOperator;
