// 归属业务方
import { Business } from '@shared/typings/tea/metric';
import dayjs from 'dayjs';
import { PlatformType } from '@pa/shared/dist/src/core';

export interface Duty {
  owner: string;
}

export enum PerformanceValueType {
  PERCENTAGE,
  ABSOLUTE_VALUE,
}

export enum PerformanceDataType {
  SUCCESS_RATE,
  FAILURE_RATE,
  COST_TIME,
  ERROR_COUNT,
}

export interface PerformanceValue {
  value: number;
  type: PerformanceValueType;
}

export enum WarningLevel {
  P2_normal,
  P1_warning,
  P0_error,
}

export enum PerformanceResult {
  stable = 0, // 稳定
  optimization = 1, // 优化
  deterioration = 2, // 劣化
}

export interface MetricDataType {
  metricId: string;
  name?: string;
  owner?: string;
  result: PerformanceResult;
  business?: Business;
  value: number;
  versionCode?: string;
  teaUrl?: string;
}

export interface PerformanceCompareValue {
  metricId?: string;
  compareValue: number; // 绝对值差值
  absoluteValue: number; // 绝对值差值
  percentage: number; // 百分比差值
  versionCode?: string; // 对比版本
  result?: PerformanceCompareResult;
}

export interface PerformanceCompareResult {
  metricId?: string;
  result?: PerformanceResult;
  reason?: string; // 失败原因
}

export enum OrderStatus {
  New = 'New', // 新建
  // Assigned = "Assigned", // 已分配
  // InProgress = "In Progress", // 处理中
  Pending = 'Pending', // 等待中
  Completed = 'Completed', // 已完成
  // Closed = "Closed", // 已关闭
  // Cancelled = "Cancelled", // 已取消
  // Rejected = "Rejected", // 拒绝
}

export const OrderStatusText: Readonly<Record<OrderStatus, string>> = {
  [OrderStatus.New]: '未处理',
  [OrderStatus.Pending]: '处理中',
  [OrderStatus.Completed]: '已处理',
};

export enum EventType {
  Done,
  UnDo,
}

export interface HistoryItem {
  timeStamp: number; // 时间点
  event?: string; // 事件
  message: string; // 信息
  owner?: string;
  from?: string;
  type: EventType;
  extra?: any;
}

export interface MultiBusinessProps {
  appId: string;
  platformType: PlatformType;
}

export interface MetricProps {
  metricId: string;
  owner: string;
}

export interface AlarmCardWarningSimpleInfo {
  title: string;
  detailContent: string;
  mainContent: string;
}

export const testHistoryData: HistoryItem[] = [
  {
    timeStamp: dayjs().unix(),
    message: '系统自动发起告警',
    type: EventType.Done,
  },
  {
    timeStamp: dayjs().unix(),
    owner: '<EMAIL>',
    message: '拉群处理',
    type: EventType.Done,
  },
  {
    timeStamp: dayjs().unix(),
    owner: '<EMAIL>',
    message: '跟进处理中',
    type: EventType.UnDo,
  },
];
