/* eslint-disable */

export const protobufPackage = "bear" as const;

/** 时间粒度 */
export enum PeriodInterval {
    PERIOD_INTERVAL_DAY = "PERIOD_INTERVAL_DAY",
    PERIOD_INTERVAL_WEEK = "PERIOD_INTERVAL_WEEK",
    PERIOD_INTERVAL_MONTH = "PERIOD_INTERVAL_MONTH",
    PERIOD_INTERVAL_HOUR = "PERIOD_INTERVAL_HOUR",
    PERIOD_INTERVAL_MIN5 = "PERIOD_INTERVAL_MIN5",
    UNRECOGNIZED = "UNRECOGNIZED",
}

/** 时间区间类型 */
export enum PeriodType {
    /** LAST - 最近 */
    LAST = "LAST" /** RANGE - 范围 */,
    RANGE = "RANGE" /** SINCE - 从...至今 */,
    SINCE = "SINCE",
    UNRECOGNIZED = "UNRECOGNIZED",
}

/** LAST时间粒度 */
export enum LastInterval {
    /** LAST_DAY - 天 */
    LAST_DAY = "LAST_DAY" /** LAST_WEEK - 周 */,
    LAST_WEEK = "LAST_WEEK" /** LAST_MONTH - 自然月 */,
    LAST_MONTH = "LAST_MONTH" /** LAST_DUAL_MONTH - 自然双月 */,
    LAST_DUAL_MONTH = "LAST_DUAL_MONTH",
    UNRECOGNIZED = "UNRECOGNIZED",
}

/** 去除日期选项 */
export enum ExcludeOption {
    /** EXCLUDE_CUSTOM - 自定义去除区间 */
    EXCLUDE_CUSTOM = "EXCLUDE_CUSTOM",
    UNRECOGNIZED = "UNRECOGNIZED",
}

export enum WeekEndOption {
    /** WEEK_1 - 周一 ~ 周日 */
    WEEK_1 = "WEEK_1",
    /** WEEK_2 - 周二 ~ 周一 */
    WEEK_2 = "WEEK_2",
    WEEK_3 = "WEEK_3",
    WEEK_4 = "WEEK_4",
    WEEK_5 = "WEEK_5",
    WEEK_6 = "WEEK_6",
    WEEK_7 = "WEEK_7",
    UNRECOGNIZED = "UNRECOGNIZED",
}

/** 过滤属性类型枚举值 */
export enum AttributeType {
    ATTRIBUTE_TYPE_RULE = "ATTRIBUTE_TYPE_RULE",
    /** ATTRIBUTE_TYPE_EVENT_PARAM - 事件属性 */
    ATTRIBUTE_TYPE_EVENT_PARAM = "ATTRIBUTE_TYPE_EVENT_PARAM",
    /** ATTRIBUTE_TYPE_PROFILE - 用户属性 */
    ATTRIBUTE_TYPE_PROFILE = "ATTRIBUTE_TYPE_PROFILE",
    /** ATTRIBUTE_TYPE_PROFILE_RULE - 用户属性分组规则 */
    ATTRIBUTE_TYPE_PROFILE_RULE = "ATTRIBUTE_TYPE_PROFILE_RULE",
    /** ATTRIBUTE_TYPE_PARAM_RULE - 事件属性分组规则 */
    ATTRIBUTE_TYPE_PARAM_RULE = "ATTRIBUTE_TYPE_PARAM_RULE",
    /** ATTRIBUTE_TYPE_COHORT - 用户分群 */
    ATTRIBUTE_TYPE_COHORT = "ATTRIBUTE_TYPE_COHORT",
    /** ATTRIBUTE_TYPE_COMPLEX - 虚拟属性 */
    ATTRIBUTE_TYPE_COMPLEX = "ATTRIBUTE_TYPE_COMPLEX",
    /** ATTRIBUTE_TYPE_ITEM - 外接属性 */
    ATTRIBUTE_TYPE_ITEM = "ATTRIBUTE_TYPE_ITEM",
    /** ATTRIBUTE_TYPE_EVENT_PARAM_TIMESTAMP - 事件属性时间类型 */
    ATTRIBUTE_TYPE_EVENT_PARAM_TIMESTAMP = "ATTRIBUTE_TYPE_EVENT_PARAM_TIMESTAMP",
    /** ATTRIBUTE_TYPE_PROFILE_TIMESTAMP - 用户属性时间类型 */
    ATTRIBUTE_TYPE_PROFILE_TIMESTAMP = "ATTRIBUTE_TYPE_PROFILE_TIMESTAMP",
    UNRECOGNIZED = "UNRECOGNIZED",
}

/** Group by 属性类型 */
export enum GroupByAttributeType {
    /** GROUP_BY_ATTRIBUTE_TYPE_EVENT_PARAM - 事件属性 */
    GROUP_BY_ATTRIBUTE_TYPE_EVENT_PARAM = "GROUP_BY_ATTRIBUTE_TYPE_EVENT_PARAM",
    /** GROUP_BY_ATTRIBUTE_TYPE_PROFILE - 用户属性 */
    GROUP_BY_ATTRIBUTE_TYPE_PROFILE = "GROUP_BY_ATTRIBUTE_TYPE_PROFILE",
    /** GROUP_BY_ATTRIBUTE_TYPE_PROFILE_RULE - 用户属性分组规则 */
    GROUP_BY_ATTRIBUTE_TYPE_PROFILE_RULE = "GROUP_BY_ATTRIBUTE_TYPE_PROFILE_RULE",
    /** GROUP_BY_ATTRIBUTE_TYPE_PARAM_RULE - 事件属性分组规则 */
    GROUP_BY_ATTRIBUTE_TYPE_PARAM_RULE = "GROUP_BY_ATTRIBUTE_TYPE_PARAM_RULE",
    /** GROUP_BY_ATTRIBUTE_TYPE_COMPLEX - 虚拟属性 */
    GROUP_BY_ATTRIBUTE_TYPE_COMPLEX = "GROUP_BY_ATTRIBUTE_TYPE_COMPLEX",
    /** GROUP_BY_ATTRIBUTE_TYPE_ITEM - 外接属性 */
    GROUP_BY_ATTRIBUTE_TYPE_ITEM = "GROUP_BY_ATTRIBUTE_TYPE_ITEM",
    UNRECOGNIZED = "UNRECOGNIZED",
}

/** 属性算子 */
export enum AttributeFilterOperatorType {
    /** IN - SQL IN */
    IN = "IN" /** NOT_IN - SQL NOT IN */,
    NOT_IN = "NOT_IN" /** NOT_IN_INCLUDE_NULL - SQL (NOT IN (xx) or IS NULL) */,
    NOT_IN_INCLUDE_NULL = "NOT_IN_INCLUDE_NULL" /** LT - SQL < */,
    LT = "LT" /** LTE - SQL <= */,
    LTE = "LTE" /** GT - SQL > */,
    GT = "GT" /** GTE - SQL >= */,
    GTE = "GTE" /** IS_NULL - SQL IS NULL */,
    IS_NULL = "IS_NULL" /** IS_NOT_NULL - SQL IS NOT NULL */,
    IS_NOT_NULL = "IS_NOT_NULL" /** LIKE - SQL LIKE '%xx%' */,
    LIKE = "LIKE" /** NOT_LIKE - SQL NOT LIKE '%xx%' */,
    NOT_LIKE = "NOT_LIKE" /** NOT_LIKE_INCLUDE_NULL - SQL NOT LIKE '%xx%' OR IS NULL */,
    NOT_LIKE_INCLUDE_NULL = "NOT_LIKE_INCLUDE_NULL" /** CUSTOM_LIKE - SQL LIKE '%xx' or LIKE 'xx%' OR LIKE '%xx%' */,
    CUSTOM_LIKE = "CUSTOM_LIKE",
    UNRECOGNIZED = "UNRECOGNIZED",
}

/** 表达式逻辑 */
export enum Logic {
    /** AND - 与 */
    AND = "AND" /** OR - 或 */,
    OR = "OR",
    UNRECOGNIZED = "UNRECOGNIZED",
}

/** 数值类型 */
export enum NumberType {
    /** INT - 整数 */
    INT = "INT" /** FLOAT - 浮点数 */,
    FLOAT = "FLOAT",
    UNRECOGNIZED = "UNRECOGNIZED",
}

/** 嵌套标识类型，LOGIC对应外层表达式组合，EXPR_CONDITION为内层表达式组 */
export enum ConditionType {
    EXPR_LOGIC_GROUP = "EXPR_LOGIC_GROUP",
    EXPR_CONDITION = "EXPR_CONDITION",
    UNRECOGNIZED = "UNRECOGNIZED",
}

/** 规则类型 */
export enum RuleType {
    /** ENUM - STRING类型属性枚举类型分组 */
    ENUM = "ENUM" /** FIXED_INTERVAL - NUMBER类型属性固定间隔分组 */,
    FIXED_INTERVAL = "FIXED_INTERVAL" /** CUSTOM_INTERVAL - NUMBER类型属性自定义间隔分组 */,
    CUSTOM_INTERVAL = "CUSTOM_INTERVAL",
    UNRECOGNIZED = "UNRECOGNIZED",
}

export enum RuleRefParamType {
    /** RULE_REF_PARAM_TYPE_EVENT_PARAM - 事件属性 */
    RULE_REF_PARAM_TYPE_EVENT_PARAM = "RULE_REF_PARAM_TYPE_EVENT_PARAM" /** RULE_REF_PARAM_TYPE_PROFILE - 用户属性 */,
    RULE_REF_PARAM_TYPE_PROFILE = "RULE_REF_PARAM_TYPE_PROFILE",
    UNRECOGNIZED = "UNRECOGNIZED",
}

export enum ComplexType {
    COMPLEX_TYPE_INT = "COMPLEX_TYPE_INT",
    COMPLEX_TYPE_FLOAT = "COMPLEX_TYPE_FLOAT",
    COMPLEX_TYPE_STRING = "COMPLEX_TYPE_STRING",
    UNRECOGNIZED = "UNRECOGNIZED",
}

export enum TimestampScopeType {
    EVENT_RELATIVE = "EVENT_RELATIVE",
    NATURAL_TIME = "NATURAL_TIME",
    UNRECOGNIZED = "UNRECOGNIZED",
}

export enum TimestampPeriodType {
    TIMESTAMP_PERIOD_TYPE_RANGE = "TIMESTAMP_PERIOD_TYPE_RANGE",
    TIMESTAMP_PERIOD_TYPE_LAST = "TIMESTAMP_PERIOD_TYPE_LAST",
    UNRECOGNIZED = "UNRECOGNIZED",
}

export enum CohortMatchType {
    COHORT_MATCH_TYPE_NORMAL = "COHORT_MATCH_TYPE_NORMAL",
    COHORT_MATCH_TYPE_DYNAMIC = "COHORT_MATCH_TYPE_DYNAMIC",
    COHORT_MATCH_TYPE_SPECIFIC = "COHORT_MATCH_TYPE_SPECIFIC",
    UNRECOGNIZED = "UNRECOGNIZED",
}

/** 复合指标算子类型 */
export enum MeasureType {
    /** SUM - 按...求和 */
    SUM = "SUM" /** SUM_PER_AU - 按...求全活跃用户人均值 */,
    SUM_PER_AU = "SUM_PER_AU" /** SUM_PER_PV - 按...求平均值 */,
    SUM_PER_PV = "SUM_PER_PV" /** SUM_PER_UV - 按...求人均值 */,
    SUM_PER_UV = "SUM_PER_UV" /** PCT - 按...求分位数 */,
    PCT = "PCT" /** MAX - 按...求最大值 */,
    MAX = "MAX" /** MIN - 按...求最小值 */,
    MIN = "MIN" /** COUNT_DISTINCT - 按...去重数 */,
    COUNT_DISTINCT = "COUNT_DISTINCT" /** COUNT_DISTINCT_SAMPLE - 按...去重数（近似计算） */,
    COUNT_DISTINCT_SAMPLE = "COUNT_DISTINCT_SAMPLE" /** COUNT_DISTINCT_USER - 按...和用户去重 */,
    COUNT_DISTINCT_USER = "COUNT_DISTINCT_USER" /** COUNT_DISTINCT_USER_SAMPLE - 按...和用户去重（近似计算） */,
    COUNT_DISTINCT_USER_SAMPLE = "COUNT_DISTINCT_USER_SAMPLE" /** FIRST_VALUE - 最早值 */,
    FIRST_VALUE = "FIRST_VALUE" /** LAST_VALUE - 最新值 */,
    LAST_VALUE = "LAST_VALUE" /** PCT_TDIGEST - 按...求分位数 精度更高 */,
    PCT_TDIGEST = "PCT_TDIGEST",
    UNRECOGNIZED = "UNRECOGNIZED",
}

export enum MeasureContentType {
    /** MEASURE_CONTENT_TYPE_EVENT_PARAM - 事件属性 */
    MEASURE_CONTENT_TYPE_EVENT_PARAM = "MEASURE_CONTENT_TYPE_EVENT_PARAM",
    /** MEASURE_CONTENT_TYPE_COMPLEX_EVENT_PARAM - 虚拟事件属性 */
    MEASURE_CONTENT_TYPE_COMPLEX_EVENT_PARAM = "MEASURE_CONTENT_TYPE_COMPLEX_EVENT_PARAM",
    /** MEASURE_CONTENT_TYPE_JOINED_ITEM - ITEM属性 */
    MEASURE_CONTENT_TYPE_JOINED_ITEM = "MEASURE_CONTENT_TYPE_JOINED_ITEM",
    /** MEASURE_CONTENT_TYPE_PROFILE - 用户属性 */
    MEASURE_CONTENT_TYPE_PROFILE = "MEASURE_CONTENT_TYPE_PROFILE",
    UNRECOGNIZED = "UNRECOGNIZED",
}

/** 指标算子类型 */
export enum MetricsOperatorType {
    /** PV - 次数 */
    PV = "PV" /** UV - 用户数 */,
    UV = "UV" /** UV_PER_AU - 渗透率 */,
    UV_PER_AU = "UV_PER_AU" /** PV_PER_UV - 人均次数 */,
    PV_PER_UV = "PV_PER_UV" /** MEASURE - 按属性计算指标 */,
    MEASURE = "MEASURE" /** FREQUENCY - 频次分布 */,
    FREQUENCY = "FREQUENCY" /** PV_PER_AU - 活跃用户人均次数 */,
    PV_PER_AU = "PV_PER_AU" /** DISTINCT_MULTI_ATTR - 属性分组distinct数量 */,
    DISTINCT_MULTI_ATTR = "DISTINCT_MULTI_ATTR",
    UNRECOGNIZED = "UNRECOGNIZED",
}

export enum EventType {
    ORIGIN = "ORIGIN",
    VIRTUAL = "VIRTUAL",
    INDICATOR = "INDICATOR",
    UNRECOGNIZED = "UNRECOGNIZED",
}

/** 四则运算符 */
export enum MathOp {
    /** ADD - + */
    ADD = "ADD" /** SUB - - */,
    SUB = "SUB" /** MULTI -  */,
    MULTI = "MULTI" /** DIV - / */,
    DIV = "DIV",
    UNRECOGNIZED = "UNRECOGNIZED",
}

export enum GroupOrder {
    ASC = "ASC",
    DESC = "DESC",
    UNRECOGNIZED = "UNRECOGNIZED",
}

export enum MicroAnalysisType {
    /** MICRO_ANALYSIS_USER_LIST - 用户列表获取 */
    MICRO_ANALYSIS_USER_LIST = "MICRO_ANALYSIS_USER_LIST" /** MICRO_ANALYSIS_SAVE_COHORT - 另存为分群 */,
    MICRO_ANALYSIS_SAVE_COHORT = "MICRO_ANALYSIS_SAVE_COHORT",
    UNRECOGNIZED = "UNRECOGNIZED",
}

export enum FunnelAnalysisVersionEnum {
    FUNNEL_DSL_VERSION_1_0_0 = "FUNNEL_DSL_VERSION_1_0_0",
    UNRECOGNIZED = "UNRECOGNIZED",
}

export enum CohortDslVersionEnum {
    COHORT_DSL_VERSION_1_0_0 = "COHORT_DSL_VERSION_1_0_0",
    UNRECOGNIZED = "UNRECOGNIZED",
}

export enum Direction {
    NO_DIRECTION = "NO_DIRECTION",
    FOLLOWING = "FOLLOWING",
    PREVIOUS = "PREVIOUS",
    UNRECOGNIZED = "UNRECOGNIZED",
}

export enum EventsOp {
    EVENT_IN = "EVENT_IN",
    EVENT_NOT_IN = "EVENT_NOT_IN",
    UNRECOGNIZED = "UNRECOGNIZED",
}

export enum EventCategory {
    ALL = "ALL",
    NO_PERF = "NO_PERF",
    UNRECOGNIZED = "UNRECOGNIZED",
}

/** 组合指标计算单元类型 */
export enum ComputeUnitType {
    /** COMPUTE_UNIT_TYPE_NUMBER - 数值 */
    COMPUTE_UNIT_TYPE_NUMBER = "COMPUTE_UNIT_TYPE_NUMBER",
    /** COMPUTE_UNIT_TYPE_REFERENCE_METRICS - 依赖DSL中的单一指标 */
    COMPUTE_UNIT_TYPE_REFERENCE_METRICS = "COMPUTE_UNIT_TYPE_REFERENCE_METRICS",
    /** COMPUTE_UNIT_TYPE_DYNAMIC_ORIGIN_EVENT_METRICS - 动态传入单一原始事件指标 */
    COMPUTE_UNIT_TYPE_DYNAMIC_ORIGIN_EVENT_METRICS = "COMPUTE_UNIT_TYPE_DYNAMIC_ORIGIN_EVENT_METRICS",
    /** COMPUTE_UNIT_TYPE_DYNAMIC_VIRTUAL_EVENT_METRICS - 动态传入单一虚拟事件指标 */
    COMPUTE_UNIT_TYPE_DYNAMIC_VIRTUAL_EVENT_METRICS = "COMPUTE_UNIT_TYPE_DYNAMIC_VIRTUAL_EVENT_METRICS",
    /** COMPUTE_UNIT_TYPE_DYNAMIC_INDICATOR_METRICS - 动态传入单一虚拟指标 */
    COMPUTE_UNIT_TYPE_DYNAMIC_INDICATOR_METRICS = "COMPUTE_UNIT_TYPE_DYNAMIC_INDICATOR_METRICS",
    UNRECOGNIZED = "UNRECOGNIZED",
}

export enum EventAnalysisDslVersion {
    EVENT_ANALYSIS_DSL_VERSION_1_0_0 = "EVENT_ANALYSIS_DSL_VERSION_1_0_0",
    UNRECOGNIZED = "UNRECOGNIZED",
}

export enum ConfidenceAnalysisDslVersion {
    CONFIDENCE_ANALYSIS_DSL_VERSION_1_0_0 = "CONFIDENCE_ANALYSIS_DSL_VERSION_1_0_0",
    UNRECOGNIZED = "UNRECOGNIZED",
}

/** sum: 总和; max: 最大值; min: 最小值; distinct: 去重数 */
export enum DistributionType {
    /** DISTRIBUTION_TYPE_DISTRIBUTE_COUNT - 次数; */
    DISTRIBUTION_TYPE_DISTRIBUTE_COUNT = "DISTRIBUTION_TYPE_DISTRIBUTE_COUNT",
    /** DISTRIBUTION_TYPE_DISTRIBUTE_HOUR - 小时数； */
    DISTRIBUTION_TYPE_DISTRIBUTE_HOUR = "DISTRIBUTION_TYPE_DISTRIBUTE_HOUR",
    /** DISTRIBUTION_TYPE_DISTRIBUTE_DAY - 天数； */
    DISTRIBUTION_TYPE_DISTRIBUTE_DAY = "DISTRIBUTION_TYPE_DISTRIBUTE_DAY",
    /** DISTRIBUTION_TYPE_DISTRIBUTION - 属性分布算子 */
    DISTRIBUTION_TYPE_DISTRIBUTION = "DISTRIBUTION_TYPE_DISTRIBUTION",
    UNRECOGNIZED = "UNRECOGNIZED",
}

export enum DistributionCalculateType {
    DISTRIBUTION_CALCULATE_TYPE_SUM = "DISTRIBUTION_CALCULATE_TYPE_SUM",
    DISTRIBUTION_CALCULATE_TYPE_MAX = "DISTRIBUTION_CALCULATE_TYPE_MAX",
    DISTRIBUTION_CALCULATE_TYPE_MIN = "DISTRIBUTION_CALCULATE_TYPE_MIN",
    DISTRIBUTION_CALCULATE_TYPE_DISTINCT = "DISTRIBUTION_CALCULATE_TYPE_DISTINCT",
    UNRECOGNIZED = "UNRECOGNIZED",
}

export enum DistributionAnalysisDslVersion {
    DISTRIBUTION_ANALYSIS_DSL_VERSION_1_0_0 = "DISTRIBUTION_ANALYSIS_DSL_VERSION_1_0_0",
    UNRECOGNIZED = "UNRECOGNIZED",
}

export enum GridType {
    NORMAL = "NORMAL",
    SMALL = "SMALL",
    LARGE = "LARGE",
    UNRECOGNIZED = "UNRECOGNIZED",
}

export enum AbProfile {
    AB = "AB",
    AB__FULLY = "AB__FULLY",
    AB__FIRST = "AB__FIRST",
    AB_VERSION = "AB_VERSION",
    UNRECOGNIZED = "UNRECOGNIZED",
}

export enum LibraAnalysisType {
    CONFIDENCE_ANALYSIS = "CONFIDENCE_ANALYSIS",
    UNRECOGNIZED = "UNRECOGNIZED",
}

export enum LibraTemplateAnalysisDslVersion {
    LIBRA_TEMPLATE_ANALYSIS_DSL_VERSION_1_0_0 = "LIBRA_TEMPLATE_ANALYSIS_DSL_VERSION_1_0_0",
    UNRECOGNIZED = "UNRECOGNIZED",
}

export enum RetentionAnalysisDslVersion {
    RETENTION_ANALYSIS_DSL_VERSION_1_0_0 = "RETENTION_ANALYSIS_DSL_VERSION_1_0_0",
    UNRECOGNIZED = "UNRECOGNIZED",
}

export enum RetentionIntervalType {
    /** RETENTION_INTERVAL_TYPE_N - N日留存，由查询范围决定 */
    RETENTION_INTERVAL_TYPE_N = "RETENTION_INTERVAL_TYPE_N" /** RETENTION_INTERVAL_TYPE_CUSTOM - 自定义区间留存 */,
    RETENTION_INTERVAL_TYPE_CUSTOM = "RETENTION_INTERVAL_TYPE_CUSTOM",
    UNRECOGNIZED = "UNRECOGNIZED",
}

export interface Last {
    interval: LastInterval;
    /** interval粒度下的数据值 */
    value: string;
    /** LastInterval == LAST_WEEK */
    week_end_option: WeekEndOption;
}

export interface Range {
    /** 秒级时间戳 */
    start: number;
    /** 秒级时间戳 */
    end: number;
    /**
     * disable_timestamp_revise 说明
     * 当禁用起止时间修正时 disable_timestamp_revise == true，查询时间范围为 start =< x < end （左闭右开）
     * 当启用起止时间修正时 disable_timestamp_revise == false，对应起止时间会根据查询粒度进行修正，修正规则如下
     * 月级别：start修正为对应月1号0点0分0秒，end修正为（对应月+1月）1号0点0分0秒   start =< x < end （左闭右开）
     * 周级别：start修正为对应天0点0分0秒，end修正为（对应天+7天）0点0分0秒   start =< x < end （左闭右开）
     * 天级别：start修正为对应天0点0分0秒，end修正为（对应天+1天）0点0分0秒   start =< x < end （左闭右开）
     * 小时级别：start修正为对应天对应小时0分0秒，end修正为（对应天对应小时+1小时）0分0秒   start =< x < end （左闭右开）
     * 5MIN级别：start修正为对应天对应小时对应分钟0秒，end修正为（对应天对应小时最近的5min整点，例如13:45、13:50等）0秒   start =< x < end （左闭右开）
     */
    disable_timestamp_revise: boolean;
}

export interface Since {
    /** 秒计时间戳 */
    begin: number;
    /** 是否禁用起止时间修正 */
    disable_timestamp_revise: boolean;
}

export interface PeriodExclude {
    /** 去除日期选项 */
    exclude_day_option: ExcludeOption;
    /** exclude_day_option == EXCLUDE_CUSTOM */
    custom_exclude_range: Range[];
}

/** 时间结构定义 */
export interface Period {
    /** 时间粒度 */
    interval: PeriodInterval;
    /** 时间区间类型 */
    type: PeriodType;
    /** type == PeriodType.LAST */
    last?: Last | undefined;
    /** type == PeriodType.RANGE */
    range?: Range | undefined;
    /** type == PeriodType.SINCE */
    since?: Since | undefined;
    /** 去除区间选项 */
    period_exclude: PeriodExclude | undefined;
    /** 周级起始选项 */
    week_end_option: WeekEndOption;
}

/** 数值单元 */
export interface NumberUnit {
    /** 数值类型 */
    type: NumberType;
    /** 整数值 type == NumberType.INT */
    int_value?: number | undefined;
    /** 浮点值 type == NumberType.FLOAT */
    float_value?: number | undefined;
}

export interface RuleFilter {
    op: AttributeFilterOperatorType;
    value: string[];
}

export interface RuleFilterGroup {
    /** 标识是否为嵌套结构还是表达式 */
    condition_type: ConditionType;
    /** filter 以及 filterGroup间的逻辑关系 */
    logic: Logic;
    /** conditionType == ConditionType.EXPR_CONDITION */
    filter: RuleFilter[];
    /** conditionType == ConditionType.EXPR_LOGIC_GROUP */
    filter_group: RuleFilterGroup[];
}

/** 字符串参数规则分组 */
export interface RuleContent {
    /** 分组名称 */
    name: string;
    /** 过滤规则 */
    filter_group: RuleFilterGroup | undefined;
}

export interface Rule {
    /** 是否使用TEA的元信息 */
    use_tea_meta: boolean;
    /** use_tea_meta == true 传入TEA元信息的规则ID */
    id: number;
    /** use_tea_meta == false 依赖的属性名称 */
    ref_name: string;
    /** RuleType == RuleType.ENUM */
    rule_content: RuleContent[];
}

export interface Complex {
    /** 是否使用TEA的元信息 */
    use_tea_meta: boolean;
    /** use_tea_meta == true, 传入TEA元信息的虚拟属性名称 */
    name: string;
    /** use_tea_meta == false，虚拟属性数据类型 */
    type: ComplexType;
    /** use_tea_meta == false, 表达式 校验合法性 */
    expr: string;
}

export interface TimestampPeriod {
    type: TimestampPeriodType;
    last?: number | undefined;
    range?: Range | undefined;
}

export interface Timestamp {
    scope_type: TimestampScopeType;
    attr_name: string;
    period: TimestampPeriod | undefined;
}

export interface Cohort {
    cohort_match_type: CohortMatchType;
    specific_match_date: string;
    dynamic_match_offset: number;
}

export interface AttributeContent {
    /** 属性类型 */
    attr_type: AttributeType;
    /** attrType not in (AttributeType.ATTRIBUTE_TYPE_RULE, AttributeType.ATTRIBUTE_TYPE_COMPLEX) */
    attr_name?: string | undefined;
    /** attrType == AttributeType.ATTRIBUTE_TYPE_RULE 传入，属性分组信息 */
    rule?: Rule | undefined;
    /** attrType == AttributeType.ATTRIBUTE_TYPE_COMPLEX 传入，虚拟属性信息 */
    complex?: Complex | undefined;
    /** attrType in (AttributeType.ATTRIBUTE_TYPE_EVENT_PARAM_TIMESTAMP, AttributeType.ATTRIBUTE_TYPE_PROFILE_TIMESTAMP) */
    timestamp?: Timestamp | undefined;
    /** attrType in (AttributeType.ATTRIBUTE_TYPE_COHORT) */
    cohort?: Cohort | undefined;
    /** 属性操作符 */
    op: AttributeFilterOperatorType;
    /** 过滤值信息 */
    value: string[];
}

/**
 * Filter Group实体，group 与 content 互斥
 * conditionType == ConditionType.EXPR_LOGIC_GROUP -> group
 * conditionType == ConditionType.EXPR_CONDITION -> content
 * conditionType决定logic的作用域
 * eg:
 * (a > b) or (b < c and c > d)
 * {
 * "conditionType": 0,
 * "group": [
 * {
 * "condition_type": 1,
 * "content": [
 * {
 * "attrType": 0,
 * "attr": a,
 * "op": 5,
 * "value": ["b"]
 * }
 * ],
 * "logic": 0
 * },
 * {
 * "condition_type": 1,
 * "content": [
 * {
 * "attrType": 0,
 * "attr": b,
 * "op": 3,
 * "value": ["c"]
 * },
 * {
 * "attrType": 0,
 * "attr": c,
 * "op": 5,
 * "value": ["d"]
 * }
 * ],
 * "logic": 0
 * }
 * ],
 * "logic": 1
 * }
 */
export interface AttributeFilter {
    /** 标识是否为嵌套结构还是表达式 */
    condition_type: ConditionType;
    /** conditionType == ConditionType.EXPR_LOGIC_GROUP 属性过滤条件分组 */
    group: AttributeFilter[];
    /** conditionType == ConditionType.EXPR_CONDITION 属性过滤条件 */
    content: AttributeContent[];
    /** 过滤组与过滤条件间的逻辑关系 */
    logic: Logic;
}

export interface Measure {
    /** 复合计算类型 */
    type: MeasureType;
    /** 属性类型 */
    content_type: MeasureContentType;
    /** 事件属性 */
    attr?: string | undefined;
    /** 虚拟属性 */
    complex?: Complex | undefined;
    /** pct 分位值 */
    pct: number;
}

export interface MetricsOperator {
    /** 算子类型 */
    operator_type: MetricsOperatorType;
    /** operatorType == MetricsOperatorType.MEASURE */
    measure: Measure | undefined;
}

export interface VirtualEventNestedOriginEvent {
    /** 原始事件名称 */
    origin_event_name: string;
    /** 原始事件过滤信息 */
    filter: AttributeFilter | undefined;
}

export interface OriginEventFilter {
    /** 事件名称 */
    name: string;
    /** 原始事件过滤信息 */
    filter: AttributeFilter | undefined;
}

export interface VirtualEventFilter {
    /** 是否使用TEA的元信息 */
    use_tea_meta: boolean;
    /** use_tea_meta == false 必须传入，TEA元信息虚拟事件名称 */
    name: string;
    /** use_tea_meta == true 必须传入 */
    events: VirtualEventNestedOriginEvent[];
    /** 公共过滤，过滤中的参数在虚拟事件任意事件中即可 */
    filter: AttributeFilter | undefined;
}

/** option */
export interface DsExtraPart {
    stage_id: string;
    timezone: string;
}

/** 分组定义 */
export interface GeneralGroupBy {
    /**
     * 分组口径说明
     * 1. 当分组依赖虚拟事件时，分组属性只需要归属虚拟事件中任意一个事件即可，无需全部满足
     * 2. 分组应用于动态组合指标时，分组属性必须是组合指标内指标属性的交集属性之一
     */
    metrics_id: string[];
    /** 分组属性类型 */
    type: GroupByAttributeType;
    /** 属性名称 type not in (GroupByAttributeType.GROUP_BY_ATTRIBUTE_TYPE_RULE, GroupByAttributeType.GROUP_BY_ATTRIBUTE_TYPE_COMPLEX) */
    attr?: string | undefined;
    /** 分组规则信息 type == GroupByAttributeType.GROUP_BY_ATTRIBUTE_TYPE_RULE */
    rule?: Rule | undefined;
    /** 分组虚拟属性信息 type ==  GroupByAttributeType.GROUP_BY_ATTRIBUTE_TYPE_COMPLEX */
    complex?: Complex | undefined;
}

/** 通用过滤器 */
export interface CommonFilter {
    /** 过滤器ID，DSL内保证不重复 */
    filter_id: string;
    /** 过滤信息，过滤信息中的参数需在每个指标中都存在 */
    filter: AttributeFilter | undefined;
}

export interface CohortCreateMeta {
    /** 分群名称 */
    name: string;
    /** 分群描述 */
    desc: string;
}

export interface CohortDirectlyQueryMeta {
    /** 分群ID，目前不暴露 */
    cohort_id: number;
    /** 分群版本，目前不对外暴露 */
    cohort_version: number;
}

export interface MicroAnalysisCohortOption {
    create_meta?: CohortCreateMeta | undefined;
    directly_query_meta?: CohortDirectlyQueryMeta | undefined;
    /** 是否异步创建 */
    async_create: boolean;
    /** 是否临时分群 */
    temporary: boolean;
}

export interface MicroAnalysisOption {
    /** 显微镜类型 */
    micro_analysis_type: MicroAnalysisType;
    /** 预览、导出用户数量 */
    size: number;
    /** 分群创建选项 */
    cohort_option: MicroAnalysisCohortOption | undefined;
}

export interface AnalysisSubject {
    /** 属性类型 */
    attr_type: AttributeType;
    /** 分析主体数据类型 */
    attr_data_type: string;
    /** attrType not in (AttributeType.ATTRIBUTE_TYPE_RULE, AttributeType.ATTRIBUTE_TYPE_COMPLEX) */
    attr_name?: string | undefined;
    /** attrType == AttributeType.ATTRIBUTE_TYPE_RULE 传入，属性分组信息 */
    rule?: Rule | undefined;
    /** attrType == AttributeType.ATTRIBUTE_TYPE_COMPLEX 传入，虚拟属性信息 */
    complex?: Complex | undefined;
    /** attrType in (AttributeType.ATTRIBUTE_TYPE_EVENT_PARAM_TIMESTAMP, AttributeType.ATTRIBUTE_TYPE_PROFILE_TIMESTAMP) */
    timestamp?: Timestamp | undefined;
    /** attrType in (AttributeType.ATTRIBUTE_TYPE_COHORT) */
    cohort?: Cohort | undefined;
}

export interface FunnelAnalysisPeriod {
    /** 转化分析中，PeriodExclude不生效 */
    period: Period | undefined;
    /** 时间窗口类型 second,minute,hour,day */
    window_type: FunnelAnalysisPeriod_FunnelWindowTypeEnum;
    /** 时间窗口值 */
    window_number: number;
}

export enum FunnelAnalysisPeriod_FunnelWindowTypeEnum {
    second = "second",
    minute = "minute",
    hour = "hour",
    day = "day",
    UNRECOGNIZED = "UNRECOGNIZED",
}

export interface FunnelAnalysisQueryEvent {
    metrics_id: string;
    event_type: FunnelAnalysisQueryEvent_FunnelAnalysisQueryEventTypeEnum;
    event_name: string;
    /** 属性过滤 */
    attrs: AttributeFilter | undefined;
    /** event_type = virtual 虚拟事件时生效，use_tea_meta = true只需要传入虚拟事件名 */
    use_tea_meta: boolean;
    /** event_type = virtual && use_tea_meta = false时生效，动态传入虚拟事件 */
    events: VirtualEventNestedOriginEvent[];
}

export enum FunnelAnalysisQueryEvent_FunnelAnalysisQueryEventTypeEnum {
    /** ORIGIN - 原始事件 */
    ORIGIN = "ORIGIN" /** VIRTUAL - 虚拟事件 */,
    VIRTUAL = "VIRTUAL",
    UNRECOGNIZED = "UNRECOGNIZED",
}

export interface FunnelAnalysisQueryItem {
    /** 标记当前查询是单事件路径还是多事件路径 */
    item_type: FunnelAnalysisQueryItem_FunnelAnalysisQueryItemType;
    /** 单事件路径，item_type = single 时有效 */
    single_event: FunnelAnalysisQueryEvent | undefined;
    /** parallel group id，item_type = parallel时有效 */
    parallel_event_group_id: string;
    /** 多事件路径，item_type = parallel时有效。每次查询只支持设置一次多事件路径对比 */
    parallel_event_group: FunnelAnalysisQueryEvent[];
}

export enum FunnelAnalysisQueryItem_FunnelAnalysisQueryItemType {
    single = "single",
    parallel = "parallel",
    UNRECOGNIZED = "UNRECOGNIZED",
}

export interface FunnelOption {
    /** 分组数量，最大10000，默认1000 */
    group_limit_size: number;
    /** 分组顺序 */
    group_order: GroupOrder;
    /** 时区 */
    timezone: string;
    /** 抽样数据集 */
    use_sample_data_set: boolean;
    /** 数据源扩展分区配置 */
    ds_extra_part: DsExtraPart | undefined;
    /** 公用筛选器是否对所有环节生效 */
    inherit_common_filter: boolean;
    /** 针对gamebi转化分析，是否往后推窗口期 */
    enable_funnel_ext_window: boolean;
    /** app_launch事件使用新time字段 */
    use_new_session_time: boolean;
    /** 是否跳过 row detection uv 限制 */
    skip_row_limit: boolean;
    /** 分析主体 */
    analysis_subject: AnalysisSubject | undefined;
}

export interface FunnelAnalysisDSL {
    version: FunnelAnalysisVersionEnum;
    period: FunnelAnalysisPeriod | undefined;
    eventPath: FunnelAnalysisQueryItem[];
    /** 当前最多只能传一个过滤器，且只会对一个事件生效 */
    filters: CommonFilter[];
    /** 受限于转化相关的算子, 仅支持单个属性groupby */
    groupBys: GeneralGroupBy[];
    option: FunnelOption | undefined;
}

export interface CohortQueryEventMeasure {
    /** 属性类型 */
    content_type: MeasureContentType;
    /** 事件属性 */
    attr?: string | undefined;
    /** 虚拟属性 */
    complex?: Complex | undefined;
    op: CohortQueryEventMeasure_CohortQueryEventResultTypeMeasureOpEnum;
}

export enum CohortQueryEventMeasure_CohortQueryEventResultTypeMeasureOpEnum {
    /** SUM - 按属性求和 */
    SUM = "SUM" /** DISTINCT - 按属性求去重 */,
    DISTINCT = "DISTINCT",
    UNRECOGNIZED = "UNRECOGNIZED",
}

export interface CohortQueryEventResultType {
    type: CohortQueryEventResultType_CohortQueryEventResultTypeEnum;
    /** type = MEASURE时生效 */
    measure: CohortQueryEventMeasure | undefined;
}

export enum CohortQueryEventResultType_CohortQueryEventResultTypeEnum {
    /** EVENTS - 总次数 */
    EVENTS = "EVENTS" /** DISTINCT_DAY - 触发天数 */,
    DISTINCT_DAY = "DISTINCT_DAY" /** MEASURE - 属性计算 */,
    MEASURE = "MEASURE",
    UNRECOGNIZED = "UNRECOGNIZED",
}

export interface CohortQueryEvent {
    event_type: CohortQueryEvent_CohortSupportEventTypeEnum;
    event_name: string;
    result_type: CohortQueryEventResultType | undefined;
    /** 属性过滤 */
    attrs: AttributeFilter | undefined;
    /** event_type = virtual 虚拟事件时生效，use_tea_meta = true只需要传入虚拟事件名 */
    use_tea_meta: boolean;
    /** event_type = virtual && use_tea_meta = false时生效，动态传入虚拟事件 */
    events: VirtualEventNestedOriginEvent[];
}

export enum CohortQueryEvent_CohortSupportEventTypeEnum {
    /** ORIGIN - 原始事件 */
    ORIGIN = "ORIGIN" /** VIRTUAL - 虚拟事件 */,
    VIRTUAL = "VIRTUAL",
    UNRECOGNIZED = "UNRECOGNIZED",
}

export interface CohortAttr {
    /** 属性类型 */
    attr_type: CohortAttr_CohortAttributeType;
    /** attrType not in (AttributeType.ATTRIBUTE_TYPE_RULE, AttributeType.ATTRIBUTE_TYPE_COMPLEX) */
    attr_name?: string | undefined;
    /** attrType == AttributeType.ATTRIBUTE_TYPE_RULE 传入，属性分组信息 */
    rule?: Rule | undefined;
    /** attrType == AttributeType.ATTRIBUTE_TYPE_COMPLEX 传入，虚拟属性信息 */
    complex?: Complex | undefined;
    /** attrType in (AttributeType.ATTRIBUTE_TYPE_PROFILE_TIMESTAMP) */
    timestamp?: Timestamp | undefined;
    /** CohortAttributeType = profile，并且是天级属性才可用 */
    daily_attr_op: CohortAttr_CohortDailyAttrMeasureOpEnum;
}

export enum CohortAttr_CohortAttributeType {
    /** ATTRIBUTE_TYPE_PROFILE - 用户属性 */
    ATTRIBUTE_TYPE_PROFILE = "ATTRIBUTE_TYPE_PROFILE" /** ATTRIBUTE_TYPE_PROFILE_RULE - 用户属性分组规则 */,
    ATTRIBUTE_TYPE_PROFILE_RULE = "ATTRIBUTE_TYPE_PROFILE_RULE" /** ATTRIBUTE_TYPE_COHORT - 用户分群 */,
    ATTRIBUTE_TYPE_COHORT = "ATTRIBUTE_TYPE_COHORT" /** ATTRIBUTE_TYPE_COMPLEX - 虚拟属性 */,
    ATTRIBUTE_TYPE_COMPLEX = "ATTRIBUTE_TYPE_COMPLEX" /** ATTRIBUTE_TYPE_ITEM - 外接属性 */,
    ATTRIBUTE_TYPE_ITEM = "ATTRIBUTE_TYPE_ITEM" /** ATTRIBUTE_TYPE_PROFILE_TIMESTAMP - 用户属性时间类型 */,
    ATTRIBUTE_TYPE_PROFILE_TIMESTAMP = "ATTRIBUTE_TYPE_PROFILE_TIMESTAMP",
    UNRECOGNIZED = "UNRECOGNIZED",
}

export enum CohortAttr_CohortDailyAttrMeasureOpEnum {
    /** DAILY_ATTR_OP_ANY - 任意一天 */
    DAILY_ATTR_OP_ANY = "DAILY_ATTR_OP_ANY" /** DAILY_ATTR_OP_SUM - 求和，只能用于int、float类型的天级用户属性 */,
    DAILY_ATTR_OP_SUM = "DAILY_ATTR_OP_SUM",
    UNRECOGNIZED = "UNRECOGNIZED",
}

export interface CohortQueryItem {
    /** 是否嵌套结构 */
    nested: boolean;
    /** nested = true时生效 */
    sub_content: CohortQueryItem[];
    /** nested = true 时生效 */
    logic: Logic;
    type: CohortQueryItem_CohortQueryItemTypeEnum;
    /** nested = false 时生效 */
    negated: boolean;
    /** nested = false 时生效适用于事件、timestamp类型的用户属性、天级别用户属性。（天级用户属性只能） */
    period: Period | undefined;
    query_event?: CohortQueryEvent | undefined;
    attr?: CohortAttr | undefined;
    /** nested = false时生效 */
    op: AttributeFilterOperatorType;
    /** nested = false时生效 */
    value: string[];
}

export enum CohortQueryItem_CohortQueryItemTypeEnum {
    QUERY_EVENT = "QUERY_EVENT",
    ATTR = "ATTR",
    UNRECOGNIZED = "UNRECOGNIZED",
}

export interface CohortOption {
    timezone: string;
    /** 数据源扩展分区配置 */
    ds_extra_part: DsExtraPart | undefined;
    /** 分析主体 */
    analysis_subject: AnalysisSubject | undefined;
}

export interface CohortDsl {
    version: CohortDslVersionEnum;
    logic: Logic;
    filters: CohortQueryItem[];
    option: CohortOption | undefined;
}

/** 行为细查-行为流 */
export interface BehaviorFlowDsl {
    query_id: string[];
    queryType: string;
    startTimestamp: number;
    endTimestamp: number;
    events: string[];
    eventsOp: EventsOp;
    eventCategory: EventCategory;
    size: number;
    direction: Direction;
    extra: string;
    reverse: boolean;
    searchKey: string;
    sessionId: string;
    maxQueryTimes: number;
    appName: string;
    byteAppId: number;
    cursorKey: string;
    commonFilter: CommonFilter | undefined;
    option: BehaviorOption | undefined;
}

export interface BehaviorOption {
    /** 是否合并时间区间查询，merge == true 即不按日期分组 */
    merge: boolean;
    /** 分组数量，最大10000，默认1000 */
    groupLimitSize: number;
    /** 分组顺序 */
    groupOrder: GroupOrder;
    /** 时区 */
    timezone: string;
    /** 抽样数据集 */
    useSampleDataSet: boolean;
    /** 数据源扩展分区配置 */
    dsExtraPart: DsExtraPart | undefined;
    /** 是否累计计算 */
    isStack: boolean;
    /** 分析主体 */
    analysisSubject: AnalysisSubject | undefined;
}

/** 行为细查-用户画像 */
export interface BehaviorProfileDsl {
    queryId: string;
    queryType: string;
    attributes: AttributeObject[];
    option: BehaviorOption | undefined;
    appName: string;
    byteAppId: number;
}

export interface AttributeObject {
    /**
     * 属性类型
     * 用户明细 ATTRIBUTE_TYPE_PROFILE
     * 事件明细 ATTRIBUTE_TYPE_EVENT_PARAM
     */
    attrType: AttributeType;
    /** attrType not in (AttributeType.ATTRIBUTE_TYPE_RULE, AttributeType.ATTRIBUTE_TYPE_COMPLEX) */
    attrName?: string | undefined;
    /** attrType == AttributeType.ATTRIBUTE_TYPE_RULE 传入，属性分组信息 */
    rule?: Rule | undefined;
}

/** 行为细查-用户基本信息 */
export interface BehaviorBaseInfoDsl {
    queryId: string;
    queryType: string;
}

/** 用户明细 */
export interface UserDetailDsl {
    /** 时间区间 */
    period: Period | undefined;
    /** 事件条件 */
    eventIterm: EventCondition | undefined;
    /** 公共过滤器 */
    commonFilter: CommonFilter | undefined;
    /** 查询属性 */
    attributes: AttributeObject[];
    /** 排序属性 */
    resultOrder: AttributeOrder[];
    /** 查询的一些选项 */
    option: BehaviorOption | undefined;
}

export interface AttributeOrder {
    attribute: AttributeObject | undefined;
    isAsc: boolean;
}

export interface EventCondition {
    /** 事件条件ID，单一DSL内需保证唯一性，业务自行生成，标识数据 */
    eventConditionId: string;
    /** 事件类型, 只支持 ORIGIN,VIRTUAL */
    eventType: EventType;
    /** 原始事件信息 */
    originEvent?: OriginEventCondition | undefined;
    /** 虚拟事件信息 */
    virtualEvent?: VirtualEventCondition | undefined;
}

export interface OriginEventCondition {
    /** 事件名称 */
    name: string;
    /** 属性过滤，结构同事件分析 */
    filter: AttributeFilter | undefined;
}

export interface VirtualEventCondition {
    /** 是否使用TEA的元信息 */
    useTeaMeta: boolean;
    /** use_tea_meta == false 必须传入，TEA元信息虚拟事件名称 */
    name: string;
    /** use_tea_meta == true 必须传入 */
    events: VirtualEventNestedOriginEvent[];
    /** 公共过滤，过滤中的参数在虚拟事件任意事件中即可 */
    filter: AttributeFilter | undefined;
}

/** 事件明细 */
export interface EventDetailDsl {
    /** 时间区间 */
    period: Period | undefined;
    /** 事件条件 */
    eventIterm: EventCondition | undefined;
    /** 公共过滤器 */
    commonFilter: CommonFilter | undefined;
    /** 查询属性 */
    attributes: AttributeObject[];
    /** 排序属性 */
    resultOrder: AttributeOrder[];
    /** 查询的一些选项 */
    option: BehaviorOption | undefined;
}

/** 原始事件 */
export interface OriginEventMetrics {
    /** 事件名称 */
    name: string;
    /** 属性过滤 */
    filter: AttributeFilter | undefined;
    /** 算子 */
    metrics_operator: MetricsOperator | undefined;
}

/** 虚拟事件 */
export interface VirtualEventMetrics {
    /** 是否使用TEA的元信息 */
    useTeaMeta: boolean;
    /** use_tea_meta == false 必须传入，TEA元信息虚拟事件名称 */
    name: string;
    /** use_tea_meta == true 必须传入 */
    events: VirtualEventNestedOriginEvent[];
    /** 公共过滤，过滤中的参数在虚拟事件任意事件中即可 */
    filter: AttributeFilter | undefined;
    metricsOperator: MetricsOperator | undefined;
}

/** 虚拟指标 */
export interface IndicatorMetrics {
    /** 是否使用TEA的元信息 */
    useTeaMeta: boolean;
    /** use_tea_meta == false， TEA指标元数据ID */
    id: number;
    filter: AttributeFilter | undefined;
}

/** 单一指标定义 */
export interface SingletonEventMetrics {
    /** 指标ID，单一DSL内需保证唯一性，业务自行生成，标识数据 */
    metrics_id: string;
    /** 事件类型 */
    event_type: EventType;
    /** 原始事件信息 */
    origin_event?: OriginEventMetrics | undefined;
    /** 虚拟事件信息 */
    virtual_event?: VirtualEventMetrics | undefined;
    /** 指标信息 */
    indicator?: IndicatorMetrics | undefined;
    /** 计算置信度组合指标转单一指标专用，记录分母 */
    refer_event_type: EventType;
    /** 原始事件信息 */
    refer_origin_event?: OriginEventMetrics | undefined;
    /** 虚拟事件信息 */
    refer_virtual_event?: VirtualEventMetrics | undefined;
}

/** 组合指标计算单元 */
export interface ComputeUnit {
    /** 组合指标计算单元类型 */
    type: ComputeUnitType;
    /** type == ComputeUnitType.REFERENCE_METRICS，单一指标ID */
    singleton_metrics_id?: string | undefined;
    /** type == ComputeUnitType.NUMBER，数值 */
    number?: NumberUnit | undefined;
    /** 原始事件信息 */
    origin_event?: OriginEventMetrics | undefined;
    /** 虚拟事件信息 */
    virtual_event?: VirtualEventMetrics | undefined;
    /** 指标信息 */
    indicator?: IndicatorMetrics | undefined;
}

/** 组合指标计算组 */
export interface ComputeGroup {
    /** 标识是否为嵌套运算 */
    type: ConditionType;
    /** 运算符 */
    op: MathOp;
    /** 计算单元，size == 2 */
    cal_units: ComputeUnit[];
    /** 计算单元分组 size == 2 */
    groups: ComputeGroup[];
}

/** 组合指标 */
export interface ComputeEventMetrics {
    /** 组合指标ID，单一DSL内需保证唯一性，业务自行生成，标识数据 */
    metrics_id: string;
    /**
     * 组合指标口径说明
     * 1. 组合指标内计算单元要么都是是动态传入, 要么都是依赖单一指标，不允许混合使用
     * 2. 当组合指标使用单一指标依赖时禁止对此指标传入Group By条件
     * 3. 当组合指标内含有分组时，计算规则为：
     * - 不含有分组的指标可以与任意指标进行组合运算
     * - 指标分别有分组计算的情况下，分组属性必须有交集才可以运算，交集定义如下
     * - 普通事件属性、Item属性、用户属性，类型和名称一致认为相同
     * - 规则分组：当使用TEA元信息时，规则类型与规则ID必须相同，当动态传入时，依赖的属性名称与规则分组的名称必须有交集
     * - 虚拟属性：当使用TEA元信息时，属性名称一致认为完全一致，当动态传入时，表达式完全一致认为相同
     * 4. 当计算分组值不重叠时，运算中会做取0处理。
     */
    group: ComputeGroup | undefined;
}

/** 查询选项 */
export interface EventAnalysisOption {
    /** 是否合并时间区间查询，merge == true 即不按日期分组 */
    merge: boolean;
    /** 分组数量，最大10000，默认1000 */
    group_limit_size: string;
    /** 分组顺序 */
    group_order: GroupOrder;
    /** 时区 */
    timezone: string;
    /** 抽样数据集 */
    use_sample_data_set: boolean;
    /** 数据源扩展分区配置 */
    ds_extra_part: DsExtraPart | undefined;
    /** 是否累计计算 */
    is_stack: boolean;
    /** 是否异步 */
    is_async: boolean;
    /** 是否跳过 row detection uv 限制 */
    skip_row_limit: boolean;
    /** 分析主体 */
    analysis_subject: AnalysisSubject | undefined;
}

/** 事件分析DSL */
export interface EventAnalysisDSL {
    version: EventAnalysisDslVersion;
    /** 时间区间 */
    period: Period | undefined;
    /** 单一指标 */
    singleton_metrics: SingletonEventMetrics[];
    /** 组合指标 */
    compute_metrics: ComputeEventMetrics[];
    /** 公共过滤器 */
    common_filter: CommonFilter[];
    /** 公共分组 */
    general_group_by: GeneralGroupBy[];
    /** 查询的一些选项 */
    option: EventAnalysisOption | undefined;
}

/** 置信度分析DSL */
export interface ConfidenceAnalysisDSL {
    version: ConfidenceAnalysisDslVersion;
    /** 时间区间 */
    period: Period | undefined;
    /** 单一指标 */
    singletonMetrics: SingletonEventMetrics[];
    /** 组合指标 */
    computeMetrics: ComputeEventMetrics[];
    /** 公共过滤器 */
    commonFilter: CommonFilter[];
    /** 公共分组 */
    generalGroupBy: GeneralGroupBy[];
    /** 查询的一些选项 */
    option: EventAnalysisOption | undefined;
}

export interface DistributionMeasure {
    /** 复合计算类型 */
    type: DistributionCalculateType;
    /** 属性类型 */
    contentType: MeasureContentType;
    /** 事件属性，content_type == MeasureContentType.MEASURE_CONTENT_TYPE_EVENT_PARAM */
    attr?: string | undefined;
    /** 虚拟事件属性，content_type == MeasureContentType.MEASURE_CONTENT_TYPE_COMPLEX_EVENT_PARAM */
    complex?: Complex | undefined;
}

export interface DistributionMetricsOperator {
    type: DistributionType;
    rules: RuleContent[];
    measure: DistributionMeasure | undefined;
}

/** 原始事件 */
export interface OriginMetrics {
    /** 事件名称 */
    name: string;
    /** 属性过滤 */
    filter: AttributeFilter | undefined;
    /** 算子 */
    metricsOperator: DistributionMetricsOperator | undefined;
}

/** 虚拟事件 */
export interface VirtualMetrics {
    /** 是否使用TEA的元信息 */
    useTeaMeta: boolean;
    /** use_tea_meta == false 必须传入，TEA元信息虚拟事件名称 */
    name: string;
    /** use_tea_meta == true 必须传入 */
    events: VirtualEventNestedOriginEvent[];
    /** 公共过滤，过滤中的参数在虚拟事件任意事件中即可 */
    filter: AttributeFilter | undefined;
    metricsOperator: DistributionMetricsOperator | undefined;
}

/** 单一指标定义 */
export interface DistributionMetrics {
    /** 指标ID，单一DSL内需保证唯一性，业务自行生成，标识数据 */
    metricsId: string;
    /** 事件类型 当前支持   ORIGIN = 0; 和 VIRTUAL = 1; */
    eventType: EventType;
    /** 原始事件信息 */
    originEvent?: OriginMetrics | undefined;
    /** 虚拟事件信息 */
    virtualEvent?: VirtualMetrics | undefined;
}

/** 查询选项 */
export interface DistributionOption {
    /** 是否合并时间区间查询，merge == true 即不按日期分组 */
    merge: boolean;
    /** 分组数量，最大10000，默认1000 */
    groupLimitSize: number;
    /** 分组顺序 */
    groupOrder: GroupOrder;
    /** 时区 */
    timezone: string;
    /** 抽样数据集 */
    useSampleDataSet: boolean;
    /** 数据源扩展分区配置 */
    dsExtraPart: DsExtraPart | undefined;
    /** 分析主体 */
    analysisSubject: AnalysisSubject | undefined;
}

/** 分布分析DSL */
export interface DistributionAnalysisDSL {
    version: DistributionAnalysisDslVersion;
    /** 时间区间 */
    period: Period | undefined;
    /** 单一指标 */
    distributionMetrics: DistributionMetrics | undefined;
    /** 公共过滤器 */
    commonFilter: CommonFilter[];
    /** 公共分组 */
    generalGroupBy: GeneralGroupBy[];
    /** 查询的一些选项 */
    option: DistributionOption | undefined;
}

export interface EventMicroAnalysisDsl {
    /** 事件分析DSL */
    dsl: EventAnalysisDSL | undefined;
    /** 显微镜分析选项 */
    microAnalysisOption: MicroAnalysisOption | undefined;
}

export interface FunnelMicroOption {
    /** 显微镜-当前层 */
    currentLevel: number;
    /** 显微镜-是否为流式 */
    lost: boolean;
}

export interface FunnelMicroAnalysisDsl {
    dsl: FunnelAnalysisDSL | undefined;
    microAnalysisOption: MicroAnalysisOption | undefined;
    funnelMicroOption: FunnelMicroOption | undefined;
}

export interface Option {
    /** 是否合并时间区间查询，merge == true 即不按日期分组 */
    merge: boolean;
    /** 分组数量，最大10000，默认1000 */
    groupLimitSize: number;
    /** 分组顺序 */
    groupOrder: GroupOrder;
    /** 时区 */
    timezone: string;
    /** 抽样数据集 */
    useSampleDataSet: boolean;
    /** 数据源扩展分区配置 */
    dsExtraPart: DsExtraPart | undefined;
    /** 是否累计计算 */
    isStack: boolean;
}

export interface HeatQueryDSL {
    heatId: number;
    rangeTs: number[];
    heat: Heat[];
    commonFilter: CommonFilter | undefined;
    /** 查询的一些选项 */
    option: Option | undefined;
}

export interface Heat {
    isTopPage: boolean;
    isHtml: boolean;
    pageKey: string;
    items: HeatItem[];
    /** is_top_page = 0 rule */
    pageRule: PageRule | undefined;
    /** 栅格大小 15*10 30*20 50*30 */
    gridType: GridType;
}

export interface HeatItem {
    /** @inject_tag: json:"elementPath" */
    elementPath: string;
    positions: string[];
    /** @inject_tag: json:"textContent" */
    textContent: string;
}

export interface PageRule {
    attr: string;
    op: string;
    val: string;
}

export interface HeatCardQueryDsl {
    heatId: number;
    rangeTs: number[];
    commonFilter: CommonFilter | undefined;
    /** 查询的一些选项 */
    option: Option | undefined;
}

export interface HeatPagePreviewDsl {
    match_rule: PageRule | undefined;
    option: Option | undefined;
}

export interface HeatManualQueryDsl {
    info: string;
}

export interface HeatMicroAnalysisDsl {
    dsl: HeatQueryDSL | undefined;
    micro_analysis_option: MicroAnalysisOption | undefined;
}

export interface LibraOption {
    /** 是否merge，默认false */
    merge: boolean;
    /** 是否使用抽样数据集，默认false */
    is_sample: boolean;
    /** 分组限制，默认100 */
    group_limit_num: number;
    /** 只有在retention_analysis查询生效，默认false */
    with_event_sample_reduction: boolean;
}

export interface LibraUpdateContent {
    /** 分析ab的字段{ab, ab__fully, ab__first, ab_version} */
    ab_profile: AbProfile;
    /** ab值 */
    vids: number[];
    /** 时间区间 */
    period: Period | undefined;
    /** 全局过滤参数 */
    filter_content: AttributeContent[];
    /** 查询的一些选项 */
    libra_option: LibraOption | undefined;
}

/** libra通过模板分析DSL */
export interface LibraTemplateAnalysisDSL {
    /** 版本号 */
    version: LibraTemplateAnalysisDslVersion;
    /** 查询template_id */
    templateId: string;
    /** 查询分析类型，目前仅支持置信度分析 */
    libraAnalysisType: LibraAnalysisType;
    /** 查询更新部分 */
    libraUpdateContent: LibraUpdateContent | undefined;
    /** 是否异步 */
    isAsync: boolean;
}

export interface RetentionJoinAttr {
    /** 属性类型 */
    attrType: AttributeType;
    /** attrType not in (AttributeType.ATTRIBUTE_TYPE_RULE, AttributeType.ATTRIBUTE_TYPE_COMPLEX) */
    attrName?: string | undefined;
    /** attrType == AttributeType.ATTRIBUTE_TYPE_RULE 传入，属性分组信息 */
    rule?: Rule | undefined;
    /** attrType == AttributeType.ATTRIBUTE_TYPE_COMPLEX 传入，虚拟属性信息 */
    complex?: Complex | undefined;
}

export interface RetentionEventFilter {
    eventType: EventType;
    /** 原始事件 */
    originEventFilter?: OriginEventFilter | undefined;
    /** 虚拟事件 */
    virtualEventFilter?: VirtualEventFilter | undefined;
    /** 必须同时出现 */
    joinAttr: RetentionJoinAttr | undefined;
}

export interface RetentionMetrics {
    /** 指标唯一标识 */
    metricsId: string;
    /** 起始事件 */
    firstEvent: RetentionEventFilter | undefined;
    /** 回访事件 */
    returnEvent: RetentionEventFilter | undefined;
}

export interface RetentionAnalysisOption {
    /** 分组数量，最大10000，默认1000 */
    groupLimitSize: number;
    /** 分组顺序 */
    groupOrder: GroupOrder;
    /** 时区 */
    timezone: string;
    /** 抽样数据集 */
    useSampleDataSet: boolean;
    /** 数据源扩展分区配置 */
    dsExtraPart: DsExtraPart | undefined;
    /** 公共过滤器是否作用于回流事件 */
    isCommonFilterUsedForReturnEvent: boolean;
    /** 固定period区间 主站同环比使用 true:不随留存区间扩展查询区间 */
    isFixQueryPeriodRange: boolean;
    /** 是否跳过 row detection uv 限制 */
    skipRowLimit: boolean;
    /** 分析主体 */
    analysisSubject: AnalysisSubject | undefined;
}

export interface RetentionIntervalTuple {
    start: number;
    end: number;
}

export interface CustomRetentionInterval {
    /** start - end 日留存 (eg: 1-3日留存) */
    useTeaMeta: boolean;
    id: number;
    interval_tuple: RetentionIntervalTuple[];
}

export interface RetentionInterval {
    type: RetentionIntervalType;
    n: number;
    custom_retention_interval: CustomRetentionInterval | undefined;
}

export interface RetentionAnalysisDSL {
    /** 留存版本 */
    version: RetentionAnalysisDslVersion;
    /** 时间区间 */
    period: Period | undefined;
    /** 留存区间粒度 */
    retention_nterval: RetentionInterval | undefined;
    /** 留存指标 */
    retention_metrics: RetentionMetrics[];
    /** 公共过滤器，默认筛选条件仅作用于起始事件（可以设置Option的is_common_filter_used_for_return_event使对回流事件生效） */
    common_filter: CommonFilter[];
    /** 仅作用于起始事件 */
    general_group_by: GeneralGroupBy[];
    /** 留存分析选项 */
    option: RetentionAnalysisOption | undefined;
}
