/*
 * 接口文档：
 * https://cloud.bytedance.net/bam/rd/toutiao.appmonitor.slardar_app_open_api/api_doc/show_doc?version=1.0.50&endpoint_id=1139978
 */

export interface AggFuncItem {
  label?: string;
  key?: string;
}

export interface Comment {
  link?: string;
  comment?: string;
  /**
   *事件说明
   */
  event_comment?: string;
  /**
   *指标负责人
   */
  manager?: string;
  /**
   *计算口径
   */
  expression?: string;
}

export interface DataExploreMetaItem {
  event_type?: string;
  filter?: FlexFilter[];
}

export interface FlexFilter {
  type?: string;
  op?: string;
  /**
   *用于表明filter的那个维度
   */
  filter_name?: string;
  values?: string[];
  sub_conditions?: FlexFilter[];
  dimension?: string;
}

export interface FlexMeasureCategoryMetaItem {
  key?: string;
  label?: string;
  /**
   *event 表明是事件选择后需要调用事件相关接口
   *simple 表明为其他的固定的指标
   */
  type?: string;
  measure_meta_list?: FlexMeasureMetaItem[];
}

export interface FlexMeasureMetaItem {
  /**
   *metric category 不在存在 都是basic 只不过value值不一样
   *事件类型的复合指标用如下方式表示,后端自己解析,废弃之前的agg type的方式，因为不是指标模式：
   *event{'xxxxxx'}.metric{'yyyyy'}.count
   *event{'xxxxxx'}.metric{'xxxxx'}.count
   *event{'xxxxxx'}.metric{'xxxxx'}.max
   *event{'xxxxxx'}.metric{'xxxxx'}.min
   *event{'xxxxxx'}.metric{'xxxxx'}.sum
   *basic 为普通类型
   *group 表明为分组，到在一个级别菜单
   */
  key?: string;
  /**
   *指标类型 一般basic类型 为基础指标
   */
  type?: string;
  /**
   *页面展示的指标名
   */
  label?: string;
  /**
   *指标简称
   */
  short_label?: string;
  /**
   *optional MeasureName measure_name
   *实际指标名称 对应服务端查询时的指标名
   */
  measure_name?: string;
  /**
   *部分指标存在递归
   */
  measure_meta_list?: FlexMeasureMetaItem[];
  /**
   *FlexMeasureValueFormat measure_value_format
   */
  comment?: Comment;
  event_name?: string;
  unit?: FlexMeasureUnit;
  data_explore_meta?: DataExploreMetaItem;
  agg_func_list?: AggFuncItem[];
  /**
   *该指标是否支持分布图，默认为false
   */
  is_support_histogram?: boolean;
}

export interface FlexMeasureUnit {
  user_unit_type?: string;
  user_unit?: string;
  /**
   *单位类型
   */
  unit_type?: string;
  /**
   *数据本身的单位
   */
  unit?: string;
  /**
   *展示单位
   */
  display_unit?: string;
  /**
   *后缀
   */
  suffix?: string;
  /**
   *小数位数
   */
  decimal?: number;
  /**
   *是否使用千分位分隔符
   */
  thousands_sep?: boolean;
  histogram_user_unit?: string;
  /**
   *展示单位
   */
  histogram_display_unit?: string;
  /**
   *后缀
   */
  histogram_suffix?: string;
  /**
   *小数位数
   */
  histogram_decimal?: number;
  /**
   *是否使用千分位分隔符
   */
  histogram_thousands_sep?: boolean;
}

export interface FlexMetaResponseData {
  measure_category_meta_list?: FlexMeasureCategoryMetaItem[];
}

export interface FlexMetaRequest {
  aid?: number;
  os?: string;
  lang?: string;
  region?: string;
  sdk?: boolean;
  type?: string;
}
