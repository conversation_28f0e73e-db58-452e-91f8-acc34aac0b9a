export interface AggFuncItem {
  label?: string;
  key?: string;
}

export interface DataExploreMeta {
  event_type?: string;
  filter?: FlexFilter[];
}

export interface FlexFilter {
  type?: string;
  op?: string;
  /**
   *用于表明filter的那个维度
   */
  filter_name?: string;
  values?: string[];
  sub_conditions?: FlexFilter[];
  dimension?: string;
}

export interface FlexFilterCandidateRequest {
  aid?: number;
  os?: string;
  region?: string;
  start_time?: number;
  end_time?: number;
  measure_list?: FlexMeasure[];
  sdk?: boolean;
  type?: string;
  filter_name?: string;
}

export interface FlexFunc {
  name?: string;
}

export interface FlexMeasure {
  /**
   *指标类型 对应上述 简单指标/自定义指标 的参数值
   */
  type?: string;
  raw_measure_list?: FlexRawMeasure[];
  /**
   *自定义指标的计算公式
   */
  formula?: string;
  name?: string;
  unit?: FlexMeasureUnit;
  no_sample_value?: number;
  /**
   *前端用于还原页面
   */
  customId?: string;
  agg_func_list?: AggFuncItem[];
}

export interface FlexMeasureUnit {
  user_unit_type?: string;
  user_unit?: string;
  /**
   *单位类型
   */
  unit_type?: string;
  /**
   *数据本身的单位
   */
  unit?: string;
  /**
   *展示单位
   */
  display_unit?: string;
  /**
   *后缀
   */
  suffix?: string;
  /**
   *小数位数
   */
  decimal?: number;
  /**
   *是否使用千分位分隔符
   */
  thousands_sep?: boolean;
  histogram_user_unit?: string;
  /**
   *展示单位
   */
  histogram_display_unit?: string;
  /**
   *后缀
   */
  histogram_suffix?: string;
  /**
   *小数位数
   */
  histogram_decimal?: number;
  /**
   *是否使用千分位分隔符
   */
  histogram_thousands_sep?: boolean;
}

export interface FlexRawMeasure {
  /**
   *MeasureName measure_name
   *实际服务端使用的指标值
   */
  measure_name?: string;
  /**
   *过滤条件
   */
  filter_list?: FlexFilter[];
  /**
   *前端传了此参数, 后端没有用到. 但是还原图表的时候是从这个字段读的事件名
   */
  event_name?: string;
  data_explore_meta?: DataExploreMeta;
  unit?: FlexMeasureUnit;
  func?: FlexFunc;
  measure_name_obj?: MeasureName;
}

export interface MeasureName {
  metric: string;
  event_dimension: string;
  event_name: string;
  map_key: string;
}

export interface FlexFilterCandidate {
  label?: string;
  value?: string;
  not_support_long_term_query?: boolean;
}

export interface FlexFilterCandidateResponseData {
  candidate_list?: FlexFilterCandidate[];
}
