/*
 * 接口文档：
 * https://cloud.bytedance.net/bam/rd/toutiao.appmonitor.slardar_app_open_api/api_doc/show_doc?version=1.0.50&endpoint_id=1139945
 */

export interface AggFuncItem {
  label?: string;
  key?: string;
}

export interface Comment {
  link?: string;
  comment?: string;
  /**
   *事件说明
   */
  event_comment?: string;
  /**
   *指标负责人
   */
  manager?: string;
  /**
   *计算口径
   */
  expression?: string;
}

export interface DataExploreMeta {
  event_type?: string;
  filter?: FlexFilter[];
}

export interface FlexFilter {
  type?: string;
  op?: string;
  /**
   *用于表明filter的那个维度
   */
  filter_name?: string;
  values?: string[];
  sub_conditions?: FlexFilter[];
  dimension?: string;
}

export interface FlexFunc {
  name?: string;
}

export interface FlexGroupBy {
  type?: string;
  /**
   *分组维度 通用维度为维度名称 自定义tag为json 字符串
   */
  group_by_name?: string;
  /**
   *分组展示的标签内容
   */
  label?: string;
  group_by_number_terms?: FlexGroupByNumberTerm[];
  comment?: Comment;
  related_measure_name?: string[];
}

export interface FlexGroupByNumberTerm {
  name?: string;
  min?: number;
  max?: number;
}

export interface FlexLongTermOptions {
  is_long_term_query?: boolean;
  cache_ttl_second?: number;
  not_read_cache?: boolean;
}

export interface FlexMeasure {
  /**
   *指标类型 对应上述 简单指标/自定义指标 的参数值
   */
  type?: string;
  raw_measure_list?: FlexRawMeasure[];
  /**
   *自定义指标的计算公式
   */
  formula?: string;
  name?: string;
  unit?: FlexMeasureUnit;
  no_sample_value?: number;
  /**
   *前端用于还原页面
   */
  customId?: string;
  agg_func_list?: AggFuncItem[];
}

export interface FlexMeasureUnit {
  user_unit_type?: string;
  user_unit?: string;
  /**
   *单位类型
   */
  unit_type?: string;
  /**
   *数据本身的单位
   */
  unit?: string;
  /**
   *展示单位
   */
  display_unit?: string;
  /**
   *后缀
   */
  suffix?: string;
  /**
   *小数位数
   */
  decimal?: number;
  /**
   *是否使用千分位分隔符
   */
  thousands_sep?: boolean;
  histogram_user_unit?: string;
  /**
   *展示单位
   */
  histogram_display_unit?: string;
  /**
   *后缀
   */
  histogram_suffix?: string;
  /**
   *小数位数
   */
  histogram_decimal?: number;
  /**
   *是否使用千分位分隔符
   */
  histogram_thousands_sep?: boolean;
}

export interface FlexRawMeasure {
  /**
   *MeasureName measure_name
   *实际服务端使用的指标值
   */
  measure_name?: string;
  /**
   *过滤条件
   */
  filter_list?: FlexFilter[];
  /**
   *前端传了此参数, 后端没有用到. 但是还原图表的时候是从这个字段读的事件名
   */
  event_name?: string;
  data_explore_meta?: DataExploreMeta;
  unit?: FlexMeasureUnit;
  func?: FlexFunc;
}

export interface FlexTopN {
  type?: string;
  /**
   *按照哪个指标信息进行排序
   */
  measure?: FlexMeasure;
  /**
   *取top的数量
   */
  top_count?: number;
  bottom_count?: number;
  group_filter_op?: string;
  group_filter_value?: number;
}

export interface TimeShift {
  time_shift?: number;
}

export interface DataStat {
  end_time?: number;
  delay?: number;
  count?: number;
  pct_90_delay?: number;
  pct_99_delay?: number;
  clickhouse_cluster?: string;
  metric_name?: string;
}

export interface FlexSeriesItem {
  name?: string;
  data?: number[];
  /**
   *多天指标的算术平均
   */
  avg?: number;
  /**
   *总计数据. 表示整个时间范围内的总计数据, 也就是时间上卷
   */
  time_rollup_data?: number;
  sum?: number;
  measure_name?: string;
  group_name?: string;
  measure_idx?: number;
  /**
   *group by的维度结果
   */
  group_by_values?: GroupByValue[];
  /**
   *同比分析, 时间偏移
   */
  time_shift_series?: TimeShiftFlexSeriesItem[];
  /**
   *false if value is NaN or Inf
   */
  data_validity?: boolean[];
}

export interface GroupByValue {
  group_by_name?: string;
  label?: string;
  value?: string;
}

export interface TimeShiftFlexSeriesItem {
  time_shift?: number;
  item?: FlexSeriesItem;
  data_delta?: number[];
  sum_delta?: number;
  avg_delta?: number;
  time_rollup_data_delta?: number;
}

export interface FlexSeriesRequest {
  aid?: number;
  os?: string;
  region?: string;
  start_time?: number;
  end_time?: number;
  filter_list?: FlexFilter[];
  measure_list?: FlexMeasure[];
  group_by_list?: FlexGroupBy[];
  granularity?: string;
  topN?: FlexTopN;
  /**
   *表格里需要总计数据 (也就是时间上卷)
   */
  need_time_rollup?: boolean;
  time_shift_list?: TimeShift[];
  /**
   *条件配置settings
   */
  cond_settings?: Record<string, string>;
  sdk?: boolean;
  type?: string;
  granularity_origin?: string;
  /**
   *自定义时间过滤聚合tag
   */
  customTimeTag?: string;
  long_term_options?: FlexLongTermOptions;
  dashboard_id?: string;
}

export interface FlexSeriesResponseData {
  x_axis?: number[];
  series?: FlexSeriesItem[];
  data_stats?: DataStat[];
}
