import { FlexSeriesRequest } from './flex/querySeries';
import { FlexMetaRequest } from './flex/meta';
import { FlexFilterCandidateRequest } from './flex/queryCandidate';
import { GetCrashListRequest } from './crash/issueListSearch';
import { GetCrashTrendRequest } from './crash/trend';
import { GetCrashEventListRequest, GetIssueEventDetailRequest, GetAlogRequest } from './crash/issueLocation';
import { AutoAssignment, AutoAssignmentRequest } from '@shared/typings/slardar/crash/issueAutoAssign';
import { GetNodeGroupListRequest } from '../../../api/model/MemoryGraphModels';

export type SlardarRequest =
  | FlexMetaRequest
  | FlexSeriesRequest
  | FlexFilterCandidateRequest
  | GetCrashListRequest
  | GetCrashTrendRequest
  | GetIssueEventDetailRequest
  | AutoAssignment
  | GetNodeGroupListRequest
  | AutoAssignmentRequest
  | GetCrashEventListRequest
  | GetAlogRequest;
