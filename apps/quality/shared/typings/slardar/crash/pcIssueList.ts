export interface GetPCIssueListRequest {
  app_id: number;
  start_time: number;
  end_time: number;
  granularity: number;
  filters: Filters;
  context: Context;
  issue_ids?: any[];
  issue_states?: any[];
  issue_managers?: any[];
  issue_tags?: any[];
  region: string;
  lang: string;
  search_type: number;
  crash_time_type: string;
  is_first_issue: boolean;
  crash_type: string;
  page_num: number;
  page_size: number;
  order_by_metric: string;
  list_metrics?: any[];
  metrics: Context;
}

export interface Filters {
  type: string;
  sub_conditions: SubCondition[];
}

export interface SubCondition {
  dimension: string;
  op: string;
  values: string[];
  groupKey: string;
}

export interface Context {
  type: string;
  sub_conditions: SubCondition[];
}

// /

export interface PCCrashListResult {
  issue_list: PCIssueDetail[];
  issue_list_count: number;
}

export interface PCIssueDetail {
  count: number;
  count_ratio: number;
  crash_reason: string;
  crash_reason_max: string;
  error_mini_chart: ErrorMiniChart;
  error_ratio_mini_chart: ErrorMiniChart;
  error_type: any;
  issue_id: string;
  issue_level: number;
  issue_manager: string;
  issue_status: string;
  max_event_id: string;
  max_key_stack: string;
  max_server_timestamp: number;
  meego_issue_url: string;
  tag_info: TagInfo[];
  top_versions: TopVersions;
  user: number;
  user_mini_chart: ErrorMiniChart;
  user_ratio: number;
  user_ratio_mini_chart: ErrorMiniChart;
}

export interface ErrorMiniChart {
  dimensions: Dimension[];
  source: ChartData[];
}

export interface Dimension {
  name: string;
  type: string;
}

export interface ChartData {
  start_time: number;
  value: number;
}

export interface TagInfo {
  id: number;
  tag_name: string;
  group_id: number;
}

export interface TopVersions {
  windows: VersionScope;
}

export interface VersionScope {
  min: string;
  max: string;
}
