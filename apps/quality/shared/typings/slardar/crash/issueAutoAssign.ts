import { current_region, Region } from '../../../../api/utils/region';

export interface AlarmRule {
  enable?: boolean;
  rules?: Rule[];
  auto_delete?: AutoDelete;
}

export interface AutoAssignment {
  aid: number;
  os: string;
  region: string;
  config: Config;
  name?: string;
  avatar?: string;
}

export interface AutoDelete {
  dead_time?: number;
  onchange?: boolean;
}

export interface ComponentSetting {
  match_rule?: ComponentMatchRule;
  name?: string;
  module?: number;
  dock_people?: string[]; // 对接人
  follower?: string[]; // 关注人
  block_list?: string[]; // 维护人黑名单
  priority?: number;
  oncall_url?: string;
  white_list?: string[]; // 维护人白名单
  group_id?: string;
  manager?: string; // 组件负责人
}

export enum ComponentMatchRule {
  LibraryName = 'library_name',
  LibraryKeyword = 'library_keyword',
  ThreadName = 'thread_name',
  AndroidNativeSo = 'native_so',
}

export interface Config {
  manager_rule?: ManagerRule;
  alarm_rule?: AlarmRule;
  rank_rule?: RankRule;
  platform?: Platform; // enum platform
}

export interface ManagerRule {
  enable?: boolean;
  git_url?: string;
  assign_priority?: string[];
  component_setting?: ComponentSetting[];
  block_list?: string[];
  file_block_list?: string[];
}

export enum Platform {
  DEFAULT = 0,
  SLARDAR = 1,
  WALLE = 2,
}

export interface RankRule {
  enable?: boolean;
  p0?: RankSetting[];
  p1?: RankSetting[];
}

export interface RankSetting {
  version_type?: string;
  threshold?: number;
  exception_threshold?: number;
  top?: number;
}

export interface Rule {
  version_type?: string;
  crash_type?: string;
  alarm_type?: string;
  threshold?: number;
  value_bar?: number;
  group?: string[];
  call_back?: string;
}

export interface UpdateConfigResponse {
  errmsg?: string;
  errno?: number;
}

export interface AutoAssignmentRequest {
  aid: number;
  os: string;
  region: string;
  sdk: boolean;
  type: string;
  config: Config;
}
