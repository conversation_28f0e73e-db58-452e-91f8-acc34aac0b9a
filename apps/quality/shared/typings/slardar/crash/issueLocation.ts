import { SlardarPlatformType } from '@pa/shared/dist/src/appSettings/appSettings';
import { User } from '@pa/shared/dist/src/core';
import { Condition } from '@shared/typings/slardar/crash/trend';
import { SymboLicateAddressDetail } from "@shared/typings/slardar/crash/ttMleaksFinderData";

/**
 * /api_v2/app/log/get
 */
export interface GetAlogRequest {
  aid: number;
  os: string;
  region: string;
  sdk: boolean;
  device_id: string;
  log_type: string;
  limit: number;
  time: string;
}

/**
 * /api_v2/app/crash/event/list
 */
export interface GetCrashEventListRequest {
  aid: number;
  crash_time_type: string;
  crash_type: string;
  start_time: number;
  end_time: number;
  event_id: string;
  event_ids: string[];
  filters_conditions: Condition | undefined;
  granularity: number;
  pgno: number;
  pgsz: number;
  sdk: boolean;
  issue_id: string;
  sub_issue_id: string;
  lang: string;
  os: string;
  region: string;
  subregion: string;
  token: string;
  token_type: number;
  versions_conditions: Condition;
}

export interface GetIssueEventDetailRequest {
  aid: string;
  crash_time: number;
  device_id: string;
  event_id: string;
  issue_id: string;
  crash_type: string;
  lang: string;
  os: string;
  region: string;
}

export interface IssueEventListResult {
  total?: number;
  result?: IssueEventListItem[];
}
export interface GetSymboLicateAddressListResult {
  symbol: SymboLicateAddressDetail[] | undefined;
  version: string | undefined;
  leaks_cycle_key_class: string | undefined;
  leaks_retain_cycle: string | undefined;
}

export interface IssueEventListItem {
  aid: string;
  crash_time: string;
  device_id: string;
  event_id: string;
  issue_id: string;
  internal_session_id: string;
  app_version: string;
  update_version_code: string;
  insert_time: string;
  mem_use: string;
  free_mem_use: string;
  event_detail: IssueEventDetail;
}

export interface IssueEventDetail {
  main_thread: IssueEventThread;
  other_threads: IssueEventThread[];
}

export interface IssueEventThread {
  thread_name: string;
  backtrace: IssueEventThreadBacktrace[];
  reason: string;
}

export interface LvIssueEventThreadBacktraceWrapper {
  crash_time: string;
  device_id: string;
  back_traces: IssueEventThreadBacktrace[];
}

export interface IssueEventThreadBacktrace {
  file_path: string;
  is_sys: boolean;
  lib_name: string;
  location: string;
  method: string;
  simple_method: string;
  unit: string;
  wrong: boolean;
}

// / 这里目前只需要筛选数据，后续有需要可以进行扩展
export interface IssueEventDetailResult {
  filters: IssueEventDetailFilters;
  custom: IssueEventDetailCustom;
}

export interface IssueEventDetailCustom {
  leaks_retain_cycle?: string;
  leaks_cycle_key_class?: string;
}
export interface IssueEventDetailFilters {
  lv_job_info?: string;
}

// / 这里只取 mr 相关的信息，后续有需要继续添加
export interface BitsJobDetailResult {
  mr_id?: string;
  job: {
    jobResult?: string;
  };
}

export interface BitsJobDetailJobResult {
  buildId?: string;
  packageType?: string;
  mr_id?: string;
}
