export interface Condition {
  type?: string;
  op?: string;
  dimension?: string;
  value?: string;
  values?: string[];
  map_key?: string;
  func?: string;
  args?: string[];
  sub_condition?: Condition;
  sub_conditions?: Condition[];
  sub_string_length?: number;
  groupKey?: string;
}

export interface GetCrashTrendRequest {
  aid?: number;
  os?: string;
  region?: string;
  crash_type?: string;
  sdk?: boolean;
  start_time?: number;
  end_time?: number;
  filters?: string; // 注意这个在后端是interface  这里string 是不得已的写的
  filters_conditions?: Condition;
  sdk_version_conditions?: Condition;
  token?: string;
  tag_filters?: Record<string, string>;
  granularity?: string;
  trend_types?: string[];
  versions?: string[];
  versions_conditions?: Condition;
  uint_versions?: number[]; // 这个为uint64  thrift中找不到
  history?: number;
  daily_avg?: number;
  issue_id?: string;
  sub_issue_id?: string;
  group_name?: string;
  is_new?: boolean;
  labels?: number[][];
  start_date?: string;
  end_date?: string;
  order_by?: string;
  crash_time_type?: string;
  anls_dim?: string[];
  status?: string[];
  issue_levels?: number[];
  tags?: string[];
  modules?: number[];
  managers?: string[];
  token_type?: number;
  message?: string;
  is_done?: boolean;
  search_field?: string;
  extra?: Record<string, string>;
  business?: string;
  view_id?: number;
  shortCutKey?: string;
  pgno?: number;
  pgsz?: number;
  type?: string;
  ios_issue_id_version?: string;
  group_bys?: string[];
  subregion?: string;
}

export interface GetCrashTrendResponseData {
  active: Record<string, any>[];
  active_total_: number;
  active_total_series: string[];
  count: Record<string, any>[];
  count_start: Record<string, any>[];
  count_start_total_: number;
  count_start_total_series: string[];
  count_total_: number;
  count_total_rate: number;
  count_total_series: string[];
  session_count: number;
  session_merged: number;
  session_user: number;
  user_active: Record<string, any>[];
  user_active_total_: number;
  user_active_total_all_: number;
  user_active_total_rate: number;
  user_active_total_series: string[];
}
