export interface CreateOrJoinChatRequest {
  id?: number;
  aid?: number;
  os?: string;
  region?: string;
  issue_id?: string;
  crash_type?: string;
  lark_chat_id?: string;
  title?: string;
  user?: string;
  sdk?: boolean;
  type?: string;
  sub_issue_id?: string;
}

export interface CreateOrJoinChatResponse {
  errmsg?: string;
  errno?: number;
  data?: CreateOrJoinData;
}

export interface CreateOrJoinData {
  lark_chat_id?: string;
  lark_open_id?: string;
}
