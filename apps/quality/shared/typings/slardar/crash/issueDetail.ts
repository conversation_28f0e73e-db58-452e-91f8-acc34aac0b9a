import { Condition } from '@shared/typings/slardar/crash/issueListSearch';

export interface BizInfo {
  /**
   *组件名
   */
  lib_name?: string;
  /**
   *业务线
   */
  biz_line?: string;
  status?: BizStatus;
  biz_type?: BizInfoType;
}

export enum BizInfoType {
  NORMAL = 1, // 普通类型
  SO = 2, // SO 类型
}

export enum BizStatus {
  AUTO = 1, // 系统自动分配
  USER_UPDATE = 2, // 用户手动修改
  NOT_FOUND_LIB = 3, // 没有找到组件
  NOT_CONF_BIZ = 4, // 没有给组件配置业务线
}

export interface GetIssueDetailResponse {
  errmsg?: string;
  errno?: number;
  data?: GetIssueDetailResponseData;
}

export interface GetIssueDetailResponseData {
  managers?: string[];
  status?: string;
  issue_level?: number;
  managers_role?: ManagerRole[];
  count?: number;
  user?: number;
  object_size?: number;
  title?: string;
  event_detail?: string;
  crash_file?: string;
  crash_clazz?: string;
  crash_method?: string;
  crash_line_number?: string;
  crash_exception?: string;
  crash_reason?: string;
  version?: number;
  id?: number;
  lark_chat_id?: string;
  avg_cpu_speed?: number;
  once_start_up_time?: number;
  issue_id_across_app?: string;
  across_app_lark?: string;
  doc?: string;
  start_time?: number;
  end_time?: number;
  snapshot?: number;
  sdk_fixed_info?: Record<string, string>;
  issue_id_v2?: string[];
  biz_info?: BizInfo;
  manager_type?: ManagerType;
}

export interface ManagerRole {
  manager?: string;
  role?: string;
}

export enum ManagerType {
  OTHER = 0,
  SLARDAR_AUTO_ASSIGN = 1,
}

export interface BizInfo {
  /**
   *组件名
   */
  lib_name?: string;
  /**
   *业务线
   */
  biz_line?: string;
  status?: BizStatus;
  biz_type?: BizInfoType;
}

export interface GetIssueDetailResponse {
  errmsg?: string;
  errno?: number;
  data?: GetIssueDetailResponseData;
}

export interface GetPercentDetailData {
  count: number;
  field?: string;
  percent: number;
  type?: string;
  value: [];
}

export interface GetPercentResponseData {
  detail: GetPercentDetailData[];
  limit?: number;
  count: number;
}

export interface CommentTextResponseData {
  avatar_url: string;
  commentText: string;
  id: number;
  submitTime: string;
  user: string;
}

export interface GetIssueDetailResponseData {
  managers?: string[];
  status?: string;
  issue_level?: number;
  managers_role?: ManagerRole[];
  count?: number;
  user?: number;
  object_size?: number;
  title?: string;
  event_detail?: string;
  crash_file?: string;
  crash_clazz?: string;
  crash_method?: string;
  crash_line_number?: string;
  crash_exception?: string;
  crash_reason?: string;
  version?: number;
  id?: number;
  lark_chat_id?: string;
  avg_cpu_speed?: number;
  once_start_up_time?: number;
  issue_id_across_app?: string;
  across_app_lark?: string;
  doc?: string;
  start_time?: number;
  end_time?: number;
  snapshot?: number;
  sdk_fixed_info?: Record<string, string>;
  issue_id_v2?: string[];
  biz_info?: BizInfo;
  manager_type?: ManagerType;
}

export interface ManagerRole {
  manager?: string;
  role?: string;
}
