import { z } from 'zod';

import { CrashType } from '@shared/typings/slardar/crash/issueListSearch';

export interface SymboLicateAddressDetail {
  name: string;
  symbol: string;
  value: string;
}
export interface SymboLicateAddressList {
  results: SymboLicateAddressDetail[];
}

// RuleSchema 定义
export const RuleSchema = z.object({
  check_libra: z.number(),
  is_close: z.number(),
  aid: z.number(),
  interval_seconds: z.number(),
  id: z.number(),
  create_time: z.string(),
  update_time: z.string(),
  ack_until_time: z.string(),
  next_trigger_time: z.string(),
  last_execute_time: z.string(),
  name: z.string(),
  business: z.string(),
  owners: z.string(),
  groups: z.string(),
  note: z.string(),
  extra_type: z.string(),
  extra_value: z.string(),
  create_by: z.string(),
  update_by: z.string(),
  method: z.string(),
  extra_form: z.string(), // JSON string; you can parse it if needed
  os: z.string(),
  region: z.string(),
  category: z.string(),
  callback_url: z.string().url(),
  view_category: z.string(),
  on_duty_id: z.string(),
  duty_plan: z.string(),
  alarm_period: z.string(), // JSON string; you can parse it if needed
  lark_tenant: z.string(),
});

// AlarmSchema 定义
export const AlarmSchema = z.object({
  type: z.string(),
  alarm: z.boolean(),
  alarm_context: z.array(
    z.object({
      contexts: z.array(
        z.object({
          context_key: z.string(),
          context_value: z.string(),
        }),
      ),
      specific_contexts: z.array(
        z.object({
          groupbys: z.record(z.string(), z.string()),
          contexts: z.array(
            z.object({
              context_key: z.string(),
              context_value: z.string(),
            }),
          ),
        }),
      ),
      trend_tags: z.nullable(z.any()), // Nullable field
      current: z.number(),
      watch_value: z.array(
        z.object({
          groupbys: z.record(z.string(), z.string()),
          value: z.number(),
        }),
      ),
      value_bar_disable: z.boolean(),
      alarm: z.boolean(),
      lark_alert: z.string(),
      en_lark_alert: z.string(),
      lark_url: z.string(),
      LarkAlertSceneHitCount: z.number(),
      LarkAlertSceneTitle: z.string(),
      LarkAlertSceneKeyNumber: z.string(),
      EnLarkAlertSceneTitle: z.string(),
      LarkAlertSceneWatches: z.array(z.string()),
      AlarmStrategyType: z.number(),
      type: z.string(),
    }),
  ),
  query_id: z.string(),
  top_issues: z.nullable(z.any()), // Nullable field
  libra_info: z.nullable(z.any()),
  libra_check_param: z.object({
    aid: z.number(),
    os: z.string(),
    region: z.string(),
    start_time: z.number(),
    end_time: z.number(),
    crash_type: z.string(),
    condition: z.object({
      type: z.string(),
      map_key: z.string(),
      func: z.string(),
      args: z.nullable(z.any()), // Nullable field
    }),
    limit: z.number(),
    rule_name: z.string(),
    rule_id: z.number(),
  }),
  new_libra_check_param: z.object({
    key: z.string(),
  }),
  alarm_context_v2: z.object({
    strategy_relation: z.string(),
    group_type: z.string(),
    crash_type: z.nativeEnum(CrashType),
    alarm_level: z.string(),
    alarm_context_v2: z.record(
      z.string(),
      z.record(
        z.string(),
        z.object({
          window_start_time: z.number(),
          window_end_time: z.number(),
          alarm: z.boolean(),
          metric_name: z.string(),
          metric_text_name: z.string(),
          watch_value: z.string(),
          watch_value_expression: z.string(),
          sample_count: z.number(),
          simple_metric_expression: z.string(),
          metric_value: z.string(),
          metric_expression: z.string(),
          compare_window_metric_value: z.string(),
          compare_window_metric_expression: z.string(),
          windows_count: z.number(),
          compare_days: z.number(),
          compare_group: z.string(),
          type: z.string(),
          threshold: z.number(),
          check_type: z.string(),
          range: z.number(),
          op: z.string(),
          report_req_md5: z.string(),
          vid_compare_type: z.nullable(z.any()), // Nullable field
        }),
      ),
    ),
    window_end_time: z.number(),
  }),
  auto_event_list: z.array(z.any()),
  manual_event_list: z.array(z.any()),
  alarm_level: z.string(),
  report_req_list: z.array(
    z.object({
      aid: z.number(),
      os: z.string(),
      region: z.string(),
      metrics_name: z.string(),
      start_time: z.number(),
      end_time: z.number(),
      ref_start_time: z.number(),
      ref_end_time: z.number(),
      filters_conditions: z.object({
        type: z.string(),
        map_key: z.string(),
        func: z.string(),
        args: z.nullable(z.any()), // Nullable field
      }),
      platform: z.string(),
      granularity: z.number(),
      attribution_scene: z.string(),
      request_md5: z.string(),
    }),
  ),
  version_top_issues: z.nullable(z.any()),
  AlarmStrategyMask: z.number(),
});

export interface MeegoModuleConfig {
  modules: { value: string; label: string };
  category: { value: string; label: string };
}

export interface ProcessIssueResult {
  ret: number;
  data?: string;
  error_msg?: string;
  message?: string;
  operator?: string;
  status?: string;
  priority?: number;
}
