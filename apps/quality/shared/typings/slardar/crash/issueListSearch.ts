import { SlardarPlatformType } from '@pa/shared/dist/src/appSettings/appSettings';
import { User } from '@pa/shared/dist/src/core';
import { Prop } from '@gulux/gulux/typegoose';

export interface Condition {
  type?: string;
  op?: string;
  dimension?: string;
  value?: string;
  values?: string[];
  map_key?: string;
  func?: string;
  args?: string[];
  sub_condition?: Condition;
  sub_conditions?: Condition[];
  sub_string_length?: number;
  groupKey?: string;
}

export interface GetNodeDetailParentNodeItem {
  class_name: string;
  class_name_with_size: string;
  ptr: string;
  vm_size: number;
  retained_size: number;
  node_type: string;
  vm_type: number;
  tag_index_list: any[];
}

export interface GetNodeDetailParentEdge {
  reference_type: number;
  offset: number;
  name: string;
}

export interface GetNodeListItem {
  node: GetNodeDetailParentNodeItem;
  edge: GetNodeDetailParentEdge;
  is_indirect: boolean;
}

export interface GetNodeDetailData {
  ptr: string;
  vm_size: number;
  vm_type: string;
  class_name: string;
  class_name_with_size: string;
  node_type: string;
  retained_size: number;
  is_cycle_leaked: boolean;
  is_leaked: boolean;
  parent_list: GetNodeListItem[];
  compress_heap_ancestor_list: GetNodeListItem[];
  children: GetNodeListItem[];
  idom_parent: GetNodeDetailParentNodeItem;
  idom_children: GetNodeDetailParentNodeItem[];
}

export interface GetNodeDetail {
  aid: number;
  os: string;
  lang: string;
  region: string;
  tos_key: string;
  session_id: string;
  ptr: string;
  sdk: boolean;
}

export interface GetNodeListDataItem {
  class_name: string;
  class_name_with_size: string;
  ptr: string;
  vm_size: number;
  retained_size: number;
  node_type: string;
  vm_type: number;
  tag_index_list: [];
  matrix_frame_list: any[];
}

export interface GetNodeList {
  aid: number;
  os: string;
  lang: string;
  region: string;
  tos_key: string;
  session_id: string;
  is_grouped_with_size: boolean;
  is_abandoned: boolean;
  node_group_list_index: number;
  page: number;
  size: number;
  matrix_uuid: string;
  tag_index: number;
  keyword: string;
  sdk: boolean;
}

export interface GetCrashIosMemoryListV2 {
  aid: number;
  crash_time_type: string;
  device_id: string;
  end_time: number;
  start_time: number;
  filters_conditions: Condition;
  os: string;
  page_num: number;
  page_size: number;
  region: string;
  sdk: boolean;
}

export interface CrashIosMemoryListV2Item {
  crash_time: number;
  event_id: string;
  url: string;
  insert_time: number;
  last_scene: string;
  memory_usage: number;
  free_memory_usage: number;
}

export interface CrashIosMemoryListV2ListItem {
  internal_session_id: string;
  did: string;
  min_crash_time: number;
  update_version_code: string;
  os_version: string;
  device_model: string;
  items: CrashIosMemoryListV2Item[];
}

export interface CrashIosMemoryListV2Data {
  memory_list: CrashIosMemoryListV2ListItem[];
  total: number;
}

export interface GetCrashListRequest {
  aid: number;
  os: string;
  region: string;
  pgsz: number;
  pgno: number;
  filters_conditions: Condition;
  start_time: number;
  end_time: number;
  granularity: number;
  order_by: string;
  crash_time_type: string;
  crash_type: string;
  labels?: number[];
  group_name?: string;
  filters?: string;
  sdk_version_conditions?: Condition;
  versions_conditions?: Condition;
  status?: string[];
  issue_levels?: number[];
  tags?: string[];
  modules?: number[];
  managers?: string[];
  token?: string;
  token_type?: number;
  message?: string;
  is_new?: boolean;
  is_done?: boolean;
  search_field?: string;
  min_crash_count?: number;
  min_crash_user?: number;
  tag_filters?: Record<string, string>;
  simple?: boolean;
  versions?: string[];
  pre_issue_id?: string;
  sdk?: boolean;
  extra?: Record<string, string>;
  view_id?: number;
  shortCutKey?: string;
  type?: string;
  ios_issue_id_version?: string;
  is_issue_based_tags?: boolean;
  group_bys?: string[];
  is_sub_issue_agg?: boolean;
  subregion?: string;
}

export interface CrashListItem {
  crash_file?: string;
  crash_clazz?: string;
  crash_method?: string;
  crash_line_number?: string;
  crash_exception?: string;
  crash_reason?: string;
  owner?: number;
  id?: number;
  version?: number;
  end_time?: number;
  start_time?: number;
  crash_time?: number;
  max_crash_time?: number;
  min_crash_time?: number;
  max_insert_time?: number;
  min_insert_time?: number;
  count?: number;
  user?: number;
  event_id?: string;
  event_detail?: string;
  title?: string;
  message?: string;
  cost_time?: number;
  scene?: string;
  appear_times?: number;
  valid_cost_percent?: number;
  malloc_avg?: number;
  num_avg?: number;
  timeout_duration?: number;
  issue_time_cost?: number;
  block_duration?: number;
  start_os_version?: string;
  end_os_version?: string;
  min_app_version?: string;
  max_app_version?: string;
  managers?: string[];
  managers_role?: ManagersRole[];
  new_occur?: boolean;
  reoccur?: boolean;
  remarkable_dimensions?: RemarkableDimensions[];
  crash_mini_chart?: CrashMiniChart[];
  user_mini_chart?: CrashMiniChart[];
  appear_mini_chart?: CrashMiniChart[];
  cost_mini_chart?: CrashMiniChart[];
  status?: string;
  issue_level?: number;
  crash_type?: string;
  matched_business?: string;
  modules?: Module;
  labels?: Lable[];
  issue_flex_agg_rule?: IssueFlexAggRule;
  issue_id?: string;
  ensure_type?: string;
  memory_object_size?: number;
  memory_object_size_avg?: number;
  memory_object_size_per_instance?: number;
  shallow_heap_size_avg?: number;
  instance_num?: number;
  thread_name?: string;
  mem_stack?: MemStack;
  once_start_up_time?: number;
  avg_cpu_speed?: number;
  max_cpu_speed?: number;
  mem_count?: number;
  commit_id?: string;
  jenkins_job_id?: string;
  issue_id_across_app?: string;
  across_app_lark?: string;
  doc?: string;
  assign_time?: number;
  hitch_duration_sum?: number;
  hitch_time_duration?: number;
  sub_issue_id?: string;
}

export interface GetCrashFiledPercentParams {
  aid: number;
  platform: SlardarPlatformType;
  start_time: number;
  end_time: number;

  /**
   * 指标类型
   */
  crash_type: string;

  crash_time_type?: string;

  filters_conditions?: Condition;

  granularity?: number;

  /**
   * 针对单issue
   */
  issue_id?: string;

  /**
   * App版本号
   */
  version?: string | string[];

  /**
   * App小版本
   */
  update_version_code?: string | string[];

  /**
   * 维度
   */
  field?: string;

  /**
   * 自定义维度
   */
  map_key?: string;
}

export interface GetCrashFieldPercentRequest {
  /**
   *app id
   */
  aid?: number;
  /**
   *Android iOS
   */
  os?: string;
  /**
   *cn maliva
   */
  region?: string;
  /**
   *崩溃异常类型
   */
  crash_type?: string;
  /**
   *发生时间 crash_time / 上报时间 insert_time
   */
  crash_time_type?: string;
  /**
   *查询时间范围的起始时间
   */
  start_time?: number;
  /**
   *查询时间范围的结束时间
   */
  end_time?: number;
  filters?: string;
  /**
   *过滤条件
   */
  filters_conditions?: Condition;
  sdk_version_conditions?: Condition;
  versions_conditions?: Condition;
  status?: string[];
  issue_levels?: number[];
  tags?: string[];
  modules?: number[];
  managers?: string[];
  token?: string;
  token_type?: number;
  message?: string;
  /**
   *是否为新增issue
   */
  is_new?: boolean;
  is_done?: boolean;
  labels?: number[][];
  search_field?: string;
  /**
   *是否是sdk
   */
  sdk?: boolean;
  /**
   *聚合字段
   */
  field?: string;
  map_key?: string;
  limit?: number;
  min_sample_count?: number;
  granularity?: number;
  issue_id?: string;
  /**
   *ios issue id版本 v1 or v2
   */
  ios_issue_id_version?: string;
}

export interface FieldPercent {
  field?: string;
  count?: number;
  percent?: number;
  type?: string;
  value?: string[];
}

export interface GetCrashFieldPercentResponseData {
  total?: number;
  limit?: number;
  detail?: FieldPercent[];
}

export interface CrashListResult {
  total?: number;
  result?: CrashListItem[];
}

export interface CrashMiniChart {
  timestamp?: number;
  value?: number;
  warning?: boolean;
  warning_ratio?: number;
  float_value?: number;
}

export interface IssueFlexAggRule {
  id?: number;
  app_id?: number;
  rank?: number;
  create_time?: string;
  last_update_time?: string;
  os?: string;
  rule_name?: string;
  issue_id?: string;
  rules?: string;
  create_user?: string;
  last_update_user?: string;
}

export interface Lable {
  target_id?: string;
  label_id?: number;
  label_key?: string;
  label_value?: string;
  key?: string;
  value?: string;
}

export interface ManagersRole {
  manager?: string;
  role?: string;
}

export interface MemStack {
  stack?: string[];
  business_method_num?: number;
}

export interface Module {
  name?: string;
  id?: number;
}

export interface RemarkableDimensions {
  dimension?: string;
  percent?: number;
  value?: string;
}

export enum CrashType {
  // Android
  JavaStartCrash = 'JavaStartCrash',
  JavaCrash = 'JavaCrash',
  NativeCrash = 'NativeCrash',
  ANR = 'ANR',
  JavaOOM = 'JavaOOM',
  NativeOOM = 'NativeOOM',
  NativeMemLeak = 'NativeMemLeak',
  JavaMemLeak = 'JavaMemLeak',
  JavaSmallInstance = 'JavaSmallInstance',
  Lag = 'Lag',
  SeriousLag = 'SeriousLag',

  // iOS
  OOMCrash = 'OOMCrash',
  Crash = 'Crash',
  WatchDog = 'WatchDog',
  custom_exception = 'custom_exception',
  CpuMeTriket = 'CpuMeTriket',
}

export const CrashName: Readonly<Record<keyof typeof CrashType, string>> = {
  // Android
  JavaStartCrash: 'Java 启动崩溃',
  ANR: 'ANR',
  JavaCrash: 'Java 崩溃',
  NativeOOM: 'Native OOM',
  JavaOOM: 'Java OOM',
  NativeCrash: 'Native 崩溃',
  NativeMemLeak: 'Native 内存泄漏',
  JavaMemLeak: 'Java 内存泄漏',
  JavaSmallInstance: 'Java 小对象',
  Lag: '卡顿',
  SeriousLag: '严重卡顿',

  // iOS
  OOMCrash: 'OOM 崩溃',
  Crash: '崩溃',
  WatchDog: '卡死',
  custom_exception: '自定义异常',
  CpuMeTriket: 'CPU异常',
};

export interface CrashNewListInfo {
  issue_id: string;
  platform: SlardarPlatformType;
  managers: string[];
  crash_file: string;
  crash_clazz: string;
  crash_method: string;
  crash_line_number: number;
  crash_exception: string;
  crash_reason: string;
  start_os_version: string;
  end_os_version: string;
  crash_type: CrashType;
  user: number;
  ranking: number;
  rate: number;
  slardar_url?: string;
}

export interface CrashHistoryListInfo {
  issue_id: string;
  platform: SlardarPlatformType;
  managers: string[];
  crash_file: string;
  crash_clazz: string;
  crash_method: string;
  crash_line_number: number;
  crash_exception: string;
  crash_reason: string;
  start_os_version: string;
  end_os_version: string;
  crash_type: CrashType;
  user: number;
  last_user: number;
  rate: number;
  last_rate: number;
  ranking: number;
  last_ranking: number;
  slardar_url?: string;
}

export interface CrashListInfo {
  aid: number;
  id: number;
  issue_id: string;
  platform: SlardarPlatformType;
  managers: string[];
  crash_file: string;
  crash_clazz: string;
  crash_method: string;
  crash_line_number: number;
  crash_exception: string;
  crash_reason: string;
  start_os_version: string;
  end_os_version: string;
  crash_type: CrashType;
  version_code: string;
  version?: string;
  count: number;
  users: number;
  status: string; // 处理状态，默认待处理
  issue_level: number; // 优先级
  issue_level_reason?: string; // 定级原因
  issue_level_time?: number;
  meego_url: string;
  meego_map?: {
    [key: string]: string;
  };
  is_warning: boolean;
  slardar_url?: string;
  userInfos?: User[];
  ranking: number;
  baseRanking?: number;
  count_rate?: string;
  base_count_rate?: string;
  user_rate?: string;
  base_user_rate?: string;
  meego_level?: number;
  meego_status?: string;
  meego_operator?: string[];
  remarkable_dimensions?: RemarkableDimensions[];
  labels?: Lable[];
  memory_object_size?: number;
  instance_num?: number;
  rate?: string;
}

export const IssueStatusName = {
  new_created: '新创建',
  long: '长期跟进',
  unassigned: '未处理',
  be_processed_again: '重启',
  done: '已处理',
  doing: '跟进中',
} as const;
