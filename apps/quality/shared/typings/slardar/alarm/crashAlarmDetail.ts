export interface AlarmBehavior {
  /**
   *类别 未ack non_ack
   */
  category?: string;
  /**
   *间隔时长
   */
  interval_second?: number;
  /**
   *发生次数
   */
  frequency?: number;
  /**
   *类型 升级upgrade 自动ack auto_ack
   */
  type?: string;
  /**
   *报警方式
   */
  method?: string[];
  /**
   *升级接收人 主值班人 primary 备值班人 standby
   */
  duty_list?: string[];
  /**
   *自动ack时间间隔
   */
  auto_ack_interval?: number;
  /**
   *0 分钟 1 小时 2 天
   */
  auto_ack_unit?: number;
  /**
   *自动ack责任人
   */
  auto_ack_user_list?: string[];
  /**
   *行为开关
   */
  behavior_switch?: boolean;
}
export interface AlarmParam {
  /**
   *基本字段
   */
  alarm_type: string;
  /**
   *时间窗口，单位分钟
   */
  interval: number;
  /**
   *阈值才会有的字段
   */
  op?: string;
  /**
   *阈值类型的阈值，但是也有异常率
   */
  threshold?: number;
  /**
   *报警策略：阈值 threshold，同比波动报警 last_day_compare，环比波动报警 last_windows_compare，同组对比报警 different_group_compare
   */
  type: string;
  /**
   *同环比才有的字段
   *up 向上波动 down 向下波动
   */
  check_type?: string;
  /**
   *同环比的阈值
   */
  range?: number;
  /**
   *样本量
   */
  value_bar: number;
  windows?: number;
  /**
   *同环比是否对无穷大情况报警 0无穷大报警 1无穷大不报警
   */
  inf_alarm?: InfAlarm;
  /**
   *同比n天之前同一时刻
   */
  compare_days?: number;
  /**
   *异常率分母session是否需要被过滤 true过滤 false不过滤
   */
  filter_session?: boolean;
  /**
   *同组对比才有的字段
   *同组别对比情况报警 0 任意一组
   */
  compare_group?: CompareGroup;
  vid_compare_type?: number;
}
export interface AlarmTimeRange {
  week_index_list: number[];
  start_timestamp: number;
  end_timestamp: number;
}
export interface ChatInfo {
  /**
   *群聊id | duty chat group list
   */
  chat_list?: string[];
  lark_tenant?: string;
}
export enum CompareGroup {
  AnyGroup = 0,
}
export interface DetailResponse {
  errno: number;
  errmsg: string;
  data: RuleDisplayData;
}
export interface Filter {
  column: string;
  op: string;
  /**
   *可能支持in操作
   */
  value: string[];
  func?: string;
  args?: string[];
}
export enum InfAlarm {
  InfAlarm = 0,
  InfNotAlarm = 1,
}
export interface RuleDisplayData {
  /**
   *其实包含rule结构体，附加数据库中其他字段等字段
   */
  aid: number;
  os: string;
  region: string;
  id: number;
  name: string;
  method: string[];
  /**
   *报警接收人
   */
  owners: string[];
  groups: string[];
  /**
   *处理建议
   */
  note: string;
  /**
   *回调url
   */
  callback_url: string;
  extra: TempExtra;
  business: string;
  create_by: string;
  update_by: string;
  create_time: number;
  update_time: number;
  ack_until_time: number;
  next_trigger_time: number;
  last_execute_time: number;
  accuracy_rate: number;
  /**
   *报警执行时间间隔
   */
  interval_minutes: number;
  /**
   *ms 配置的值班模板id字符，多个模板id用逗号","分隔
   */
  on_duty_id?: string;
  check_libra: boolean;
  is_walle?: number;
  /**
   *自动触发事件列表 libra_test libra实验 bits_fuse bits发版熔断
   */
  auto_event_list?: TriggerEventItem[];
  /**
   *手动触发事件列表
   */
  manual_event_list?: TriggerEventItem[];
  /**
   *值班计划
   */
  duty_plan?: string;
  /**
   *32: optional list<string> did_list // did list
   */
  third_party_id?: number;
  version_phase?: string;
  /**
   *报警规则是否关闭 true 关闭 false 开启
   */
  is_close?: boolean;
  alarm_range?: AlarmTimeRange;
  chat_info?: ChatInfo;
}
export interface TagFilter {
  filter_key: string;
  op: string;
  filter_value: string[];
}
export interface TempExtra {
  crash_type: string;
  /**
   *老字段（需要支持，但是后面都不要用到这个字段）
   */
  group_type: string;
  /**
   *实际上是condition中的小版本号top，不过咱们兼容一下老格式，这里不做修改
   */
  top: number;
  tag_filters: TagFilter[];
  /**
   *true 表示包含null; false 表示不包含null
   */
  include_null?: boolean;
  filters: Filter[];
  alarm_params: AlarmParam[];
  /**
   *notice、warning、fatal
   */
  alarm_level: string;
  /**
   *and（同时满足）、or（满足一项）
   */
  alarm_params_relation: string;
  /**
   *后端会将AddRequest.sdk赋值给本字段，兼容历史格式
   */
  sdk: boolean;
  issue_tag: number[][];
  issue_tag_op?: string;
  /**
   *自动触发事件列表 libra_test libra实验 bits_fuse bits发版熔断
   */
  auto_event_list?: TriggerEventItem[];
  /**
   *手动触发事件列表
   */
  manual_event_list?: TriggerEventItem[];
  /**
   *报警开关 是否关闭
   */
  is_close?: boolean;
  behavior_list?: AlarmBehavior[];
  latest?: number;
  auto_delete_interval?: number;
  is_done_delete?: boolean;
  inf_alarm?: number;
  ios_issue_id_version?: string;
}
export interface TriggerEventItem {
  /**
   *前端展示的标签名称
   */
  label?: string;
  /**
   *触发事件名称
   */
  value?: string;
  /**
   *是否可以被删除 true-不可以删除 false-可以删除
   */
  disabled?: boolean;
}
