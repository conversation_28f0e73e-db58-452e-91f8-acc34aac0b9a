// 定义 ChatInfo 接口
export interface ChatInfo {
  chat_list: string[];
  lark_tenant?: string;
}

// 定义 AlarmEvent 接口
export interface AlarmEvent {
  id: number;
  series_id: string;
  start_time: number;
  end_time: number;
  create_time: number;
  update_time: number;
  recover_time: number;
  ack_time: number;
  ack_status: number;
  status: number;
  slardar_event_id: number;
  is_new_event: boolean;
}

// 定义 AlarmEventConfig 接口
export interface AlarmEventConfig {
  detection_window: number;
  anomaly_count: number;
  alarm_threshold: number;
  threshold_window: number;
  alarm_type: number;
  recover_continue_num?: number;
}

// 定义 DisplayAlarmEventConfig 接口
export interface DisplayAlarmEventConfig {
  detection_window: number;
  anomaly_count: number;
  detect_relation: number;
  alarm_strategy_list: number[];
}

// 定义 PostProcessor 接口
export interface PostProcessor {
  notification_event: NotificationEvent;
  upgrade_notification_event_list: UpgradeNotificationEvent[];
  downgrade_event?: DowngradeEvent;
  alarm_history_event: AlarmHistoryEvent;
  alarm_event: AlarmEvent;
  top_issue: TopIssue;
}

// 定义 TopIssue 接口
export interface TopIssue {
  need_top_issue: boolean;
  top_issue_list: IssueInfo[];
  top_issue_window: number;
  start_time: number;
}

// 定义 IssueInfo 接口
export interface IssueInfo {
  issue_id: string;
  issue_title: string;
  issue_owner: string;
  issue_url: string;
}

// 定义 AlarmHistoryEvent 接口
export interface AlarmHistoryEvent {
  history_id: number;
  abnormal_status: string;
  create_time: number;
  issue_url?: string;
}

// 定义 DowngradeEvent 接口
export interface DowngradeEvent {
  frequency: number;
  start_time: number;
  end_time: number;
  is_downgrade: boolean;
  need_notify: boolean;
  downgrade_info: DowngradeInfo;
  need_check: boolean;
  duty_list: string[];
  method?: string[];
  downgrade_type: string;
  behavior_switch?: boolean;
  category?: string;
  auto_ack_interval?: number;
  auto_ack_unit?: number;
  auto_ack_user?: string[];
}

// 定义 DowngradeInfo 接口
export interface DowngradeInfo {
  level: number;
  downgrade_interval: number;
  start_downgrade_time: number;
}

// 定义 UpgradeNotificationEvent 接口
export interface UpgradeNotificationEvent {
  duty_plan: string;
  duty_list: string[];
  chat_list: string[];
  method?: string[];
  upgrade_type: string;
  frequency: number;
  start_time: number;
  end_time: number;
  behavior_switch?: boolean;
  category?: string;
  interval_second?: number;
}

// 定义 NotificationEvent 接口
export interface NotificationEvent {
  duty_plan: string;
  duty_list: string[];
  chat_list: string[];
  method?: string[];
  callback_url: string[];
  chat_info?: ChatInfo;
  primary_duty_list: string[];
  standby_duty_list: string[];
}

// 定义 DetectConfig 接口
export interface DetectConfig {
  upper_sensitive?: number;
  lower_sensitive?: number;
  abnormal_type?: number;
}

// 定义 ThirdConfig 接口
export interface ThirdConfig {
  kepler_config?: DetectConfig;
  display_alarm_event_config?: DisplayAlarmEventConfig;
  enable_event_merge: boolean;
}

// 定义 SmartAlarmExtraData 接口
export interface SmartAlarmExtraData {
  configs: RuleConfig;
  behavior_list: SmartAlarmBehavior[];
  web_extra_data?: WebExtraData;
  auxiliary_configs?: AuxiliaryConfigs;
}

// 定义 AuxiliaryConfigs 接口
export interface AuxiliaryConfigs {
  open: boolean;
  config_list: AuxiliaryConfig[];
  alertStrategy: AlertStrategy;
  custom_function?: string;
}

// 定义 AlertStrategy 枚举
export enum AlertStrategy {
  ONEOF = 1,
  BOTH = 2,
  CUSTOM = 3,
}

// 定义 WebExtraData 接口
export interface WebExtraData {
  env: string;
  exclude_null?: boolean;
  alarm_language?: string;
  is_downgrade?: boolean;
  window_detection?: WindowDetection;
}

// 定义 AuxiliaryConfig 接口
export interface AuxiliaryConfig {
  filter_list: FlexFilter[];
  measure_list: FlexMeasure[];
  group_flag: boolean;
  cacl_rule: AlertCaclRule;
}

// 定义 InfAlarm 枚举
export enum InfAlarm {
  InfAlarm = 0,
  InfNotAlarm = 1,
}

// 定义 AlertCaclRule 接口
export interface AlertCaclRule {
  caclType: AlertCaclType;
  threadhold: number;
  op: AlertCaclOP;
  fOP: FluctuationOP;
  fluctuationRange: string[];
  compare_days?: number;
  inf_alarm?: InfAlarm;
  windows?: number;
  detect_window_minute: number;
}

// 定义 SmartAlarmBehavior 接口
export interface SmartAlarmBehavior {
  category: string;
  interval_second: number;
  frequency: number;
  type: string;
  method: string[];
  duty_list: string[];
  auto_ack_interval?: number;
  auto_ack_unit?: number;
  auto_ack_user_list: string[];
  behavior_switch: boolean;
}

// 定义 RuleConfig 接口
export interface RuleConfig {
  measure_list: FlexMeasure[];
  filter_list: FlexFilter[];
  cond_settings?: Record<string, string>;
  group_by_list?: FlexGroupBy[];
  start_time_offset: string;
  end_time_offset: string;
  start_time: number;
  end_time: number;
}

// 定义 DetectPoint 接口
export interface DetectPoint {
  start_time: number;
  value: number;
  status?: number;
  abnormal_status?: number;
  pred_value?: number;
  upper_value?: number;
  lower_value?: number;
  exception_rate?: number;
}

// 定义 Field 接口
export interface Field {
  id: string;
  name: string;
}

// 定义 TrafficEnv 接口
export interface TrafficEnv {
  Open: boolean;
  Env: string;
}

// 定义 BaseResp 接口
export interface BaseResp {
  StatusMessage: string;
  StatusCode: number;
  Extra?: Record<string, string>;
}

// 定义 CommonResp 接口
export interface CommonResp {
  Code: number;
  Message: string;
}

// 定义 Base 接口
export interface Base {
  LogID: string;
  Caller: string;
  Addr: string;
  Client: string;
  TrafficEnv?: TrafficEnv;
  Extra?: Record<string, string>;
}

export interface WindowDetection {
  windows: number;
  min_trigger: number;
}

const FlexMeasureCategoryMetaTypeEvent = 'event';
const FlexMeasureCategoryMetaTypeSimple = 'simple';
const FlexMeasureCategoryMetaTypeCustomFuncEvent = 'custom_func_event';

const FlexMeasureMetaTypeBasic = 'basic';
const FlexMeasureMetaTypeGroup = 'group';
const FlexMeasureMetaTypeCustomFuncTable = 'custom_func_table';
const FlexMeasureMetaTypeCustomFuncMetric = 'custom_func_metric';
const FlexMeasureMetaTypeCustomFuncDimension = 'custom_func_dimension';

const FlexMeasureUnitTypeTime = 'time';
const FlexMeasureUnitTypeNumber = 'number';
const FlexMeasureUnitTypeStorage = 'storage';
const FlexMeasureUnitTypeTemperature = 'temperature';

const FlexMeasureUnitTimeDay = 'd';
const FlexMeasureUnitTimeHour = 'h';
const FlexMeasureUnitTimeMinute = 'm';
const FlexMeasureUnitTimeSecond = 's';
const FlexMeasureUnitTimeMillsecond = 'ms';
const FlexMeasureUnitTimeDefault = 'ms';

const FlexMeasureUnitNumberBillion = 'B';
const FlexMeasureUnitNumberMillion = 'M';
const FlexMeasureUnitNumberKilo = 'K';
const FlexMeasureUnitNumberPercent = '%';
const FlexMeasureUnitNumberPermillage = '‰';
const FlexMeasureUnitNumberDefault = '1';

const FlexMeasureUnitStorageTB = 'TB';
const FlexMeasureUnitStorageGB = 'GB';
const FlexMeasureUnitStorageMB = 'MB';
const FlexMeasureUnitStorageKB = 'KB'; // 1000Byte
const FlexMeasureUnitStorageTiB = 'TiB';
const FlexMeasureUnitStorageGiB = 'GiB';
const FlexMeasureUnitStorageMiB = 'MiB';
const FlexMeasureUnitStorageKiB = 'KiB'; // 1024Byte
const FlexMeasureUnitStorageByte = 'B';
const FlexMeasureUnitStorageDefault = 'B';

const FlexMeasureUnitTemperatureCelsius = '℃';
const FlexMeasureUnitTemperatureFahrenheit = '℉';
const FlexMeasureUnitTemperatureDefault = '℃';

const FlexPivotFieldTypeMeasure = 'measure';
const FlexPivotFieldTypeDimension = 'dimension';
const FlexPivotFieldTypeHistogram = 'histogram';
const FlexPivotFieldTypeTimestamp = 'timestamp';
const FlexPivotFieldTypeDataValidity = 'validity';

const FlexPivotDataTypeNumber = 'number';
const FlexPivotDataTypeString = 'string';
const FlexPivotDataTypeBoolean = 'bool';

export interface Comment {
  link: string; // measure link
  comment: string; // measure comment
  event_comment: string; // 事件说明
  manager?: string; // The employee responsible for the indicator | BDEE employee
  expression?: string; // 计算口径
}

export interface GroupByValue {
  group_by_name: string;
  label: string;
  value: string;
}

export interface FlexMeasureUnit {
  user_unit_type?: string; // unit type of measure result that user set
  user_unit?: string; // unit of measure result that user set
  unit_type: string; // default unit type
  unit: string; // default unit name
  display_unit?: string; // unit name to display
  suffix?: string; // unit suffix
  decimal?: number; // decimal places to number unit
  thousands_sep?: boolean; // 是否使用千分位分隔符
  histogram_user_unit?: string; // unit of measure result that user set for histogram
  histogram_display_unit?: string; // 展示单位
  histogram_suffix?: string; // 后缀
  histogram_decimal?: number; // 小数位数
  histogram_thousands_sep?: boolean; // 是否使用千分位分隔符
}

export interface MeasureName {
  metric: string; // metric name for measure
  event_dimension: string; // event dimension name for measure
  event_name: string; // event name for measure
  map_key: string; // event map key name
}

const FlexFilterTypeNumber = 'number';
const FlexFilterTypeString = 'string';
const FlexFilterTypeStringRegexRange = 'string_regex_range';
const FlexFilterTypeRegex = 'regex';
const FlexFilterTypeRegexNoCandidate = 'regex_no_candidate';

const FlexTopNTypeTopN = '';
const FlexTopNTypeBottomN = 'bottom_n';
const FlexTopNTypeGroupFilter = 'group_filter';

const FlexGroupByTypeDirect = '';
const FlexGroupByTypeNumberTerm = 'number_term';

// [min, max)
export interface FlexGroupByNumberTerm {
  name: string; // group by term name
  min: number; // group by min value
  max: number; // group by max value
}

export interface FlexGroupBy {
  type?: string; // group by type
  group_by_name: string; // 分组维度 通用维度为维度名称 自定义tag为json 字符串
  label: string; // 分组展示的标签内容
  group_by_number_terms?: FlexGroupByNumberTerm[];
  comment?: Comment;
  related_measure_name?: string[]; // related measure name that need to group by
}

export interface FlexFunc {
  name: string; // function name that used. e.g avg, max, min
}

export interface FlexRawMeasure {
  measure_name: string; // 实际服务端使用的指标值
  filter_list: FlexFilter[]; // 过滤条件
  event_name?: string; // 前端传了此参数, 后端没有用到. 但是还原图表的时候是从这个字段读的事件名
  data_explore_meta?: DataExploreMeta;
  unit?: FlexMeasureUnit;
  func?: FlexFunc;
  measure_name_obj?: MeasureName;
}

const FlexMeasureTypePolynomial = 'polynomial'; // 自定义指标
const FlexMeasureTypeMonomial = 'monomial'; // 简单指标

export interface FlexMeasure {
  type: string; // 指标类型 对应上述 简单指标/自定义指标 的参数值
  raw_measure_list: FlexRawMeasure[];
  formula: string; // 自定义指标的计算公式
  name: string; // metric measure name
  unit?: FlexMeasureUnit;
  no_sample_value?: number; // no sample value
  customId?: string; // custom id. use for get cache
  agg_func_list?: AggFuncItem[];
  attribution_type_list?: string[];
  isYAxis2?: boolean;
}

export interface DataExploreMeta {
  event_type: string; // event type. e.g oom, crash
  filter: FlexFilter[];
}

export interface FlexFilter {
  type: string; //
  op: string; // filter operator for generate sql, like 'in/not in/regex'
  filter_name: string; // 用于表明filter的那个维度
  values: string[]; // filter value list
  sub_conditions: FlexFilter[];
  dimension?: string; // filter dimension name
  func: string; // filter function name
  args: string[]; // filter function arguments
}

export interface FlexSeriesItem {
  name: string; // Series chart name
  data: number[]; // Series chart data
  avg: number; // 多天指标的算术平均
  time_rollup_data?: number; // 总计数据. 表示整个时间范围内的总计数据, 也就是时间上卷
  sum: number;
  measure_name: string;
  group_name: string;
  measure_idx: number;
  group_by_values: GroupByValue[]; // group by的维度结果
  time_shift_series?: TimeShiftFlexSeriesItem[]; // 同比分析, 时间偏移
  data_validity?: boolean[]; // false if value is NaN or Inf
}

export interface TimeShiftFlexSeriesItem {
  time_shift: number;
  item: FlexSeriesItem;
  data_delta: number[];
  sum_delta: number;
  avg_delta: number;
  time_rollup_data_delta?: number;
}

export interface FlexPieItem {
  name: string; // Pie chart name
  data: number; // Pie data result
  measure_name: string;
  measure_idx: number; // measure index
  group_by_values: GroupByValue[];
  time_shift_pies?: TimeShiftFlexPieItem[]; // 时间偏移后的结果
  data_validity?: boolean; // false if value is NaN or Inf
}

// 时间偏移
export interface TimeShiftFlexPieItem {
  time_shift: number;
  item: FlexPieItem;
  data_delta: number; // measure result after shift
}

export interface Compare {
  time_shift: number;
  measusure: string; // measure name
}

export interface FlexIndicatorCardItem {
  name: string; // Indicator chart name
  data: number; // Indicator chart total result data
  measure_name: string;
  measure_idx: number;
  time_shift_indicator_cards?: TimeShiftFlexIndicatorCardItem[]; // 时间偏移后的结果
  data_validity?: boolean; // false if value is NaN or Inf
}

// 时间偏移
export interface TimeShiftFlexIndicatorCardItem {
  time_shift: number;
  item: FlexIndicatorCardItem;
  data_delta: number;
}

export interface Label {
  color: string; // label color
}

export enum AlertCaclType {
  // 整体类指标
  ThreadHold = 1, // 阈值
  WoW_Fluctuation = 2, // window on window, 按时间窗口 环比
  DoD_Fluctuation = 3, // day on day 同比
  // 双版本指标
  Rate = 4, // 变化率
  Count = 5, // 变化量
}

export enum AlertCaclOP {
  GT = 1, // 大于
  GTE = 2, // 大于等于
  LT = 3, // 小于
  LTE = 4, // 小于等于
}

export enum FluctuationOP {
  UP = 1, // 向上波动
  DOWN = 2, // 向下波动
  UP_AND_DOWN = 3, // 向上或者向下
  RANGE = 4,
}

export interface VersionInfo {
  type: string; // last_version、latest_version、latest_canary_version、last_canary_version、fixed_version
  value: string; // 版本号，type 为 fixed_version 时使用
}

// 实际的table的元信息返回值，根据用户填写的FlexMeasureCustomFuncTableCategoryMetaItem，
// 获取出table的指标，并且返回

export interface AggFuncItem {
  label: string; // aggregation function label
  key: string; // aggregation function key
}
