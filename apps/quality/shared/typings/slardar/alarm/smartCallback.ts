// 假设 api_common 和 flex/base 对应的类型定义已导入

import {
  AlarmEvent,
  AlarmEventConfig,
  ChatInfo,
  DetectConfig,
  DetectPoint,
  PostProcessor,
  SmartAlarmExtraData,
  ThirdConfig,
} from '@shared/typings/slardar/alarm/apiCommon';

export interface CommonCallbackRequest {
  rule: CommonCallbackRule;
  alarm: CommonCallbackAlarm;
}

export interface CommonCallbackRule {
  is_close: number;
  is_deleted: number;
  interval_seconds: number;
  platform: string;
  detect_start_time: number;
  detect_end_time: number;
  platform_id: string;
  platform_os: string;
  alarm_level: string;
  rule_strategy: SmartAlarmExtraData;
  smart_engine: string;
  third_config: ThirdConfig;
  chat_info?: ChatInfo;
  is_sdk: boolean;
  sub_task_id?: number;
  check_libra: number;
  aid: number;
  id: number;
  create_time: number;
  update_time: number;
  ack_until_time: number;
  next_trigger_time: number;
  last_execute_time: number;
  name: string;
  business: string;
  owners: string;
  groups: string;
  note: string;
  extra_type: string;
  extra_value: string; // 报警详情
  create_by: string; // 创建人
  update_by: string; // 更新人
  method: string; // 报警方式
  extra_form: string; // 上传表单
  os: string; // OS
  region: string;
  category: string;
  callback_url: string;
  view_category: string;
  on_duty_id: string;
  duty_plan: string;
  alarm_period: string;
  lark_tenant: string;
}

export interface CommonCallbackAlarm {
  task_id: number;
  task_sub_id: number;
  region: string;
  interval: number;
  task_type: number;
  detect_config: DetectConfig;
  detect_point_list: DetectPoint[];
  current_timestamp: number;
  log_id: string;
  callback_url: string;
  series_id: string;
  group_type: string;
  tag: string;
  post_processor: PostProcessor;
  alarm_event_config: AlarmEventConfig;
  alarm_event: AlarmEvent;
  is_during_ack: boolean;
  type: string;
  alarm: boolean;
  alarm_context: AlarmContext[];
  query_id: string;
  top_issues: string[];
  libra_info?: Exp[];
  libra_check_param: LibraCheckParam;
  new_libra_check_param: NewLibraCheckParam;
  alarm_context_v2: RuleContext;
  only_lark: boolean;
  auto_event_list: TriggerEventItem[];
  manual_event_list: TriggerEventItem[];
  alarm_level: string;
  report_req_list: CreateReportRequest[];
  version_top_issues: { [key: string]: string[] };
  alarm_strategy_mask: number;
}

export interface CreateReportRequest {
  aid: number;
  os: string;
  region: string;
  metrics_name: string;
  start_time: number;
  end_time: number;
  ref_start_time: number;
  ref_end_time: number;
  filters_conditions: Condition;
  platform: string;
  granularity: number;
  attribution_scene: string;
  request_md5: string;
}

export interface RuleContext {
  strategy_relation: string;
  group_type: string;
  crash_type: string;
  alarm_level: string;
  alarm_context_v2: { [key: number]: { [key: string]: AlarmContextV2 } };
  window_end_time: number;
}

export interface AlarmContextV2 {
  window_start_time: number;
  window_end_time: number;
  alarm: boolean;
  metric_name: string;
  metric_text_name: string;
  watch_value: string;
  watch_value_expression: string;
  sample_count: number;
  simple_metric_expression: string;
  metric_value: string;
  metric_expression: string;
  compare_window_metric_value: string;
  compare_window_metric_expression: string;
  windows_count: number;
  compare_days: number;
  compare_group: string;
  type: string;
  threshold: number;
  check_type: string;
  range: number;
  op: string;
  report_req_md5: string;
  vid_compare_type?: number;
}

export interface AlarmContext {
  contexts: Context[];
  specific_contexts: SpecificContext[];
  trend_tags: { [key: string]: string };
  current: number;
  watch_value: WatchValue[];
  value_bar_disable: boolean;
  alarm: boolean;
  lark_alert: string;
  en_lark_alert: string;
  lark_url: string;
  lark_alert_scene_hit_count: number;
  lark_alert_scene_title: string;
  lark_alert_scene_key_number: string;
  en_lark_alert_scene_title: string;
  lark_alert_scene_watches: string[];
  alarm_strategy_type: number;
  type: string;
  extra: string;
}

export interface WatchValue {
  groupbys: { [key: string]: string };
  value: number;
}

export interface SpecificContext {
  groupbys: { [key: string]: string };
  contexts: Context[];
}

export interface Context {
  context_key: string;
  context_value: string;
  value: number;
}

export interface Exp {
  is_release: number;
  status: number;
  flight: Flight;
  name: string;
  id: number;
  details: Detail[];
  proportion: number;
}

export interface Detail {
  hit_num: number;
  user: number;
}

export interface Flight {
  app_name: string;
  app_id: number;
  owners: string[];
  id: number;
  name: string;
  start_time: number;
}

export interface TriggerEventItem {
  label: string;
  value: string;
  is_disabled: boolean;
}

export interface LibraCheckParam {
  aid: number;
  os: string;
  region: string;
  start_time: number;
  end_time: number;
  crash_type: string;
  condition: Condition;
  limit: number;
  rule_name: string;
  rule_id: number;
}

export interface Condition {
  type: string;
  op: string;
  values: string[];
  value: string;
  dimension: string;
  map_key: string;
  func: string;
  args: string[];
  sub_condition?: Condition;
  sub_conditions: Condition[];
}

export interface NewLibraCheckParam {
  key: string;
}
