import { FlexSeriesResponseData } from './flex/querySeries';
import { FlexMetaResponseData } from './flex/meta';
import { FlexFilterCandidateResponseData } from './flex/queryCandidate';
import {
  CrashIosMemoryListV2Data,
  CrashListResult,
  GetNodeDetailData,
  GetNodeListDataItem,
} from './crash/issueListSearch';
import { GetCrashTrendResponseData } from './crash/trend';
import { IssueEventDetailResult, IssueEventListResult } from './crash/issueLocation';
import { CommentTextResponseData, GetIssueDetailResponseData } from '@shared/typings/slardar/crash/issueDetail';
import { Config } from '@shared/typings/slardar/crash/issueAutoAssign';
import { PCCrashListResult } from '@shared/typings/slardar/crash/pcIssueList';
import { MemoryGraphDetail, GroupItem } from '../../../api/model/MemoryGraphModels';
import { SymboLicateAddressList } from '@shared/typings/slardar/crash/ttMleaksFinderData';

export type SlardarResponseData =
  | FlexSeriesResponseData
  | FlexMetaResponseData
  | FlexFilterCandidateResponseData
  | CrashListResult
  | GetCrashTrendResponseData
  | IssueEventListResult
  | IssueEventDetailResult
  | GetIssueDetailResponseData
  | Config
  | PCCrashListResult
  | GroupItem[]
  | MemoryGraphDetail
  | CrashIosMemoryListV2Data
  | GetNodeListDataItem[]
  | GetNodeDetailData
  | SymboLicateAddressList
  | CommentTextResponseData[]
  | string
  | string[]
  | number;

export interface SlardarResponse<T extends SlardarResponseData> {
  errno: number;
  errmsg: string;
  errtitle?: string;
  async_state?: number;
  data: T;
}
