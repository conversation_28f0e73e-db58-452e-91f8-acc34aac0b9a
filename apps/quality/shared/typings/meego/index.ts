export const MeegoStatus2Name: {
  [key: string]: string;
} = {
  'FIX IN FUTURE': '延期处理',
  CLOSED: '已关闭',
  OPEN: '未处理',
  REJECT: '拒绝修复',
  RESOLVED: '已解决',
  REOPENED: '重新打开',
  'IN PROGRESS': '处理中',
};

export const Status2Color: {
  [key: string]: string;
} = {
  'FIX IN FUTURE': 'purple',
  CLOSED: 'gray',
  OPEN: 'blue',
  REJECT: 'yellow',
  RESOLVED: 'green',
  REOPENED: 'yellow',
  'IN PROGRESS': 'yellow',
};

export interface MeegoToken {
  token: string;
  access_token?: string;
  expire_time: number;
}

export interface MeegoResult<T> {
  error: Error;
  data: T;
}

export interface Pagination {
  page_num: number;
  page_size: number;
  total: number;
}

export interface Err {
  code?: number;
  msg?: string;
}

export interface MeegoWorkResult<T> {
  pagination: Pagination;
  data: T;
  err: Err;
  err_code: number;
  err_msg: string;
}

export interface MeegoBatchUpdateResult<T> {
  data: T;
  err: Err;
  err_code: number;
  err_msg: string;
}

export interface MeegoAddBotResult {
  chat_id: string;
  failed_members: any[];
  success_members: string[];
}

export interface NodesConnections {
  connections: Connection[]; // 工作项下的连接数组
  workflow_nodes: WorkflowNode[]; // 工作项下的工作流节点数组
  state_flow_nodes: any[]; // 工作项下的状态流节点数组,暂时没用到
}

export interface Connection {
  source_state_key: string;
  target_state_key: string;
}

export interface WorkflowNode {
  actual_begin_time: string;
  actual_finish_time: string;
  fields: FieldValuePair[];
  id: string;
  name: string;
  node_schedule: NodeSchedule;
  owners: string[];
  role_assignee?: RoleAssignee[];
  schedules: Schedule[];
  state_key: string;
  status: number;
  sub_tasks: any[];
}

export interface FieldValuePair {
  field_key: string;
  field_alias: string;
  field_value: FieldValue[] | FieldValue | RoleFiledValue[] | number | string | number[] | boolean;
  field_type_key: string;
  target_state?: TargetState;
  help_description?: string;
}

export interface NodeSchedule {
  owners: string[];
  points: number;
  estimate_end_date?: number;
  estimate_start_date?: number;
}

export interface RoleAssignee {
  owners: string[];
  role: string;
}

export interface Schedule {
  estimate_end_date: number;
  estimate_start_date: number;
  owners: string[];
  points: number;
}

export interface TargetState {
  state_key: string;
  transition_id: number;
}

export interface NodeBasicInfo {
  id: string;
  name: string;
  owners: string[];
}

export interface WorkItemStatus {
  state_key: string;
  is_archived_state: boolean;
  is_init_state: boolean;
  updated_at: number;
  updated_by: string;
}

export interface StateTime {
  state_key: string;
  start_time: number;
  end_time: number;
  name: string;
}

export interface FieldValueTree {
  children: FieldValueTree;
  label?: string;
  value: string;
}

export interface FieldValue {
  label: string;
  value: string;
  children?: FieldValue;
}

export interface RoleFiledValue {
  owners: string[];
  role: string;
}

export interface Roles {
  owners: string[];
  role: string;
}

// meego平台的视图数据结构
export interface FixView {
  view_id: string; // 视图ID
  name: string; // 视图名称
  created_by: string; // 创建人
  created_at: number; // 创建时间，毫秒精度
  modified_by: string; // 最后一次修改人user_key
  work_item_id_list: number[]; // 固定视图包含的工作项ID列表
  editable: boolean; // 当前视图是否可编辑
}

export interface FieldValuePair {
  field_key: string;
  field_alias: string;
  field_value: FieldValue[] | FieldValue | RoleFiledValue[] | number | string | number[] | boolean;
  field_type_key: string;
  target_state?: TargetState;
}

export interface MeegoUser {
  avatar_url: string;
  email: string;
  name_cn: string;
  name_en: string;
  out_id: string;
  status: string;
  user_id: number;
  user_key: string;
  username: string;
}

export interface MeegoTeam {
  team_id: number;
  team_name: string;
  user_keys: string[];
  administrators: string[];
}

export interface MeegoWorkItemOperateResult {
  data?: number;
  err: Err;
  err_code: number;
  err_msg: string;
}

export interface WorkItemInfo {
  id: number;
  name: string;
  simple_name: string;
  work_item_type_key: string;
  project_key: string;
  template_id: number;
  template_type: string;
  pattern: string;
  sub_stage: string;
  current_nodes: NodeBasicInfo[];
  created_by: string;
  updated_by: string;
  deleted_by: string;
  created_at: number;
  updated_at: string;
  deleted_at: string;
  fields: FieldValuePair[];
  work_item_status: WorkItemStatus;
  workflow_infos: NodesConnections;
  state_times: StateTime[];
  // multi_texts: MultiText[];富文本信息暂时不要
}

export interface BusinessIdInfo {
  id: string;
  name: string;
}

export interface WorkItemKeyType {
  name: string;
  type_key: string;
}

export interface FieldConf {
  is_required: number;
  is_visibility: number;
  is_validity: number;
  role_assign: RoleAssign[];
  field_name: string; // 字段名称
  field_key: string;
  field_alias: string;
  field_type_key: string;
  default_value: DefaultValue[];
  label: string;
  compound_fields: FieldConf[];
  options: FieldOption[];
}

export interface FieldOption {
  is_disabled: number;
  is_visibility: number;
  label: string;
  value: string;
}

export interface RoleAssign {
  role: string;
  name: string;
  default_appear: number;
  deletable: number;
  member_assign: number;
  members: string[];
}

export interface DefaultValue {
  default_appear: number;
}

export class MeegoInfo {
  id: number;
  name: string;
  link: string;
  priority: number;
  status?: string;
  issue_operator?: string[];
  isError?: boolean;
  isFixed: boolean;
  issue_reporter_key?: string;
  chat_group?: string;
  create_at: number;
}
