// 实验巡检 URL Search Params（Quality 中主要是稳定性指标巡检）
import { ChatInfo, PlatformType, User } from '@pa/shared/dist/src/core';
import { IndicatorType, Priority } from '@shared/libra/common';
import { LibraFlightInfo } from '@shared/libra/LibraNewInfo';

export enum LibraPatrolUrlSearchParams {
  FlightId = 'flight_id',
}

export interface MergeDataDetails {
  confidence: Record<string, any>;
  relative_diff: Record<string, number>;
  hint: any;
  absolute_diff: Record<string, number>;
  value: number;
  mde: Record<string, any>;
  raw: any;
  p_val: Record<string, number>;
  inter: any;
  margin: Record<string, any>;
  eff: Record<string, any>;
}

export interface TransformResult {
  metric_id: number;
  metric_key: string;
  metric_name: string;
  metric_description: string;
  vid: number;
  value: number;
  p_value: Array<{ key: string; value: number }>;
  dim_id?: number;
  dim_name?: string;
  dim_value_id?: number;
  dim_value_name?: string;
}

export const adRatios = 0.2;
export const searchRatios = 0;

export const special_up_down_group = [70290];

export const lt_metric_array: number[] = [157118, 125275, 75223, 7020176, 75074, 7035420, 7034418, 7042455];

export const export_metric_array: number[] = [
  // 醒图
  157122, 157120, 157121, 157119, 157124,
  // CC
  7019959, 7019961, 7035872, 7019958,
  // 剪映PC
  125025,
  // CCPC
  7041363,
  // 剪映
  88208, 88207, 130219,
];

export const share_metric_array: number[] = [];

export interface MetricInfo {
  metric_id: number;
  metric_name: string;
  subscribe_users: User[];
  subscribe_chat_groups: ChatInfo[];
  P0_threshold: PatrolThreshold;
  P1_threshold: PatrolThreshold;
  P2_threshold: PatrolThreshold;
  business_name: string[];
  is_reversed: boolean;
}

export interface PatrolThreshold {
  need_p_value: boolean; // 是否显著
  is_up_direction: boolean; // 上升下降
  threshold: number; // 阈值, 百分比转换，5% => 0.05
}

export interface LibraPatrolConfig {
  libra_app_id: number;
  metric_group_name: string;
  metric_group_id: number;
  metric_list: MetricInfo[];
  dim_id: number;
  dim_values: string[];
  switch: boolean;
  platform: PlatformType;
  metric_patrol_type: IndicatorType;
  is_test: number;
}

type AppNameMapping = {
  [key: number]: string;
};

// 定义应用名称映射对象
export const patrolAppName: AppNameMapping = {
  255: '醒图',
  367: 'HyPic',
  147: '剪映',
  305: 'CapCut',
  399: '剪映PC',
  360: 'CapCutPC',
};

// 定义 Android 平台的指标映射类型
type AndroidCrashTransform = {
  anr_user: 'anr_count';
  native_crash_user: 'native_count';
  java_crash_user: 'java_count';
  start_crash_user: 'start_count';
  java_oom_user: 'java_oom_count';
  native_oom_user: 'native_oom_count';
  [key: string]: string;
};

// 定义 iOS 平台的指标映射类型
type IosCrashTransform = {
  oom_crash_user: 'oom_count';
  oom_user: 'oom_count';
  watch_dog_user: 'stuck_count';
  crash_user: 'crash_count';
  [key: string]: string;
};

// 定义完整的 crashTransform 类型
type CrashTransform = {
  android: AndroidCrashTransform;
  iOS: IosCrashTransform;
};

// 初始化 crashTransform 对象
export const crashTransform: CrashTransform = {
  android: {
    anr_user: 'anr_count',
    native_crash_user: 'native_count',
    java_crash_user: 'java_count',
    start_crash_user: 'start_count',
    java_oom_user: 'java_oom_count',
    native_oom_user: 'native_oom_count',
  },
  iOS: {
    oom_crash_user: 'oom_count',
    oom_user: 'oom_count',
    watch_dog_user: 'stuck_count',
    crash_user: 'crash_count',
  },
};

export interface CrashInfo {
  crash: any;
  details: LibraFlightInfo[];
  platform: string;
  libraAppId: number;
  version?: string;
}

type KeyToMetricKey = {
  anr: string;
  native: string;
  java: string;
  start: string;
  java_oom: string;
  native_oom: string;
  oom: string[];
  stuck: string;
  crash: string;
  [key: string]: string | string[]; // 添加字符串索引签名
};

// 初始化 keyToMetricKey 对象
export const keyToMetricKey: KeyToMetricKey = {
  anr: 'anr_user',
  native: 'native_crash_user',
  java: 'java_crash_user',
  start: 'start_crash_user',
  java_oom: 'java_oom_user',
  native_oom: 'native_oom_user',
  oom: ['oom_crash_user', 'oom_user'],
  stuck: 'watch_dog_user',
  crash: 'crash_user',
};

const RETOUCH_CALENDAR_ID = '<EMAIL>';
const LV_CALENDAR_ID = '<EMAIL>';
const CC_CALENDAR_ID = '<EMAIL>';

// 将日历 IDs 存储在一个对象中
export const CalendarIds: Record<number, string> = {
  255: RETOUCH_CALENDAR_ID,
  147: LV_CALENDAR_ID,
  305: CC_CALENDAR_ID,
};

// 应用版本信息类型定义
interface AppVersions {
  android: string[];
  ios: string[];
}

// 为每个应用定义其对应的版本信息
export const LibraPatrolAppVersion: Record<number, AppVersions> = {
  255: {
    android: ['7.1.0', '7.2.0', '7.3.0', '7.4.0', '7.4.1', '7.4.2', '7.4.3'],
    ios: ['7.1.0', '7.2.0', '7.2.1', '7.3.0', '7.4.0', '7.4.1'],
  },
  147: {
    android: ['9.6.0', '9.7.0', '9.8.0', '9.9.0', '10.0.0', '10.1.0'],
    ios: ['9.6.0', '9.7.0', '9.8.0', '9.9.0', '10.0.0', '10.1.0'],
  },
  305: {
    android: ['7.6.0', '7.7.0', '7.8.0', '7.9.0', '8.0.0', '8.1.0'],
    ios: ['7.6.0', '7.7.0', '7.8.0', '7.9.0', '8.0.0', '8.1.0'],
  },
};

// 定义平台映射类型
export type PlatformMapping = {
  android: number;
  ios: number;
};

// 定义 aid 转换映射类型
type AidTransform = {
  [key: number]: PlatformMapping;
};

// 定义 aid_transform 对象
export const aidTransform: AidTransform = {
  147: {
    android: 177502,
    ios: 177501,
  },
  1775: {
    android: 177502,
    ios: 177501,
  },
  305: {
    android: 300602,
    ios: 300601,
  },
  3006: {
    android: 300602,
    ios: 300601,
  },
  255: {
    android: 251502,
    ios: 251501,
  },
  2515: {
    android: 251502,
    ios: 251501,
  },
  399: {
    android: 2020092383, // windows
    ios: 2020092892, // mac
  },
  3704: {
    android: 370402,
    ios: 370401,
  },
  360: {
    android: 35928902, // mac
    ios: 35928901, // windows
  },
  359289: {
    android: 35928902, // mac
    ios: 35928901, // windows
  },
};

export const app_list_inland: number[] = [147, 255];
export const app_list_abroad: number[] = [305];
export const lv_cc_list: number[] = [147, 305];
export const pc_list: number[] = [399, 360];
export const pc_inland: number[] = [399];
const pc_abroad: number[] = [360];
export const inland_list: number[] = [147, 255, 399];
export const oversea_list: number[] = [305, 360];

// 二维数组
const app_type_list: number[][] = [app_list_inland, app_list_abroad, pc_list];

// 其他检查列表
const app_user_check_list: number[] = [147, 255, 305];
const app_commercial_check_list: number[] = [147, 255, 305];
const app_ad_check_list: number[] = [147, 255, 305];

export enum PatrolRatioType {
  crash = 1,
  feedback = 2,
  confirm = 3,
}

export interface PatrolRatio {
  ratio: {
    count: number;
    rate: number;
  }[];
  type: PatrolRatioType;
  platform: PlatformType;
  appId: number;
}

export const crashRatiosNew: PatrolRatio = {
  ratio: [
    { count: 30, rate: 0.8 },
    { count: 50, rate: 0.3 },
    { count: 80, rate: 0.12 },
    { count: 100, rate: 0.08 },
    { count: 150, rate: 0.05 },
    { count: 300, rate: 0.03 },
  ],
  type: PatrolRatioType.crash,
  platform: PlatformType.Android,
  appId: 147,
};

export const crashRatiosRetounch: PatrolRatio = {
  ratio: [
    { count: 50, rate: 0.5 },
    { count: 80, rate: 0.3 },
    { count: 120, rate: 0.15 },
    { count: 300, rate: 0.03 },
    { count: 400, rate: 0.02 },
  ],
  type: PatrolRatioType.crash,
  platform: PlatformType.Android,
  appId: 255,
};

export const crashRatiosNewiOS: PatrolRatio = {
  ratio: [
    { count: 100, rate: 0.5 },
    { count: 300, rate: 0.3 },
    { count: 500, rate: 0.12 },
    { count: 1000, rate: 0.05 },
  ],
  type: PatrolRatioType.crash,
  platform: PlatformType.iOS,
  appId: 147,
};

export const crashRatios: PatrolRatio = {
  ratio: [
    { count: 30, rate: 0.8 },
    { count: 80, rate: 0.3 },
    { count: 200, rate: 0.12 },
    { count: 500, rate: 0.1 },
  ],
  type: PatrolRatioType.crash,
  platform: PlatformType.Android,
  appId: 147,
};

export const crashRatiosDay: PatrolRatio = {
  ratio: [
    { count: 5, rate: 0.8 },
    { count: 20, rate: 0.3 },
    { count: 50, rate: 0.2 },
    { count: 100, rate: 0.1 },
    { count: 500, rate: 0.07 },
    { count: 1000, rate: 0.05 },
  ],
  type: PatrolRatioType.crash,
  platform: PlatformType.Android,
  appId: 147,
};

// 拆分 confirm 相关
export const crashRatiosConfirm: PatrolRatio = {
  ratio: [
    { count: 50, rate: 0.99 },
    { count: 100, rate: 0.5 },
    { count: 300, rate: 0.3 },
  ],
  type: PatrolRatioType.confirm,
  platform: PlatformType.Android,
  appId: 147,
};

export interface PatrolLibraBaseInfo {
  name: string;
  url: string;
  id: string;
  ownerInfo: string[];
  appId: number[];
  priority: string;
  meegoId?: string;
  basic_vid: string;
  data: any;
}

// 拆分 feedback 相关
export const feedbackRatios: PatrolRatio = {
  ratio: [
    { count: 15, rate: 1.2 },
    { count: 30, rate: 0.7 },
    { count: 80, rate: 0.3 },
    { count: 110, rate: 0.2 },
  ],
  type: PatrolRatioType.feedback,
  platform: PlatformType.Android,
  appId: 147,
};

export const feedbackRatiosPc: PatrolRatio = {
  ratio: [
    { count: 15, rate: 1.2 },
    { count: 30, rate: 0.7 },
    { count: 45, rate: 0.45 },
    { count: 80, rate: 0.3 },
    { count: 110, rate: 0.2 },
  ],
  type: PatrolRatioType.feedback,
  platform: PlatformType.PC,
  appId: 359289,
};

const Others = '不限';
const REWARD_FREE = 'reward_free';
const LAUNCH = 'launch';
const ALBUM = 'album';
const FEED = 'feed';
const EXPORT = 'export';
const DRAW = 'draw';
const REWARD_PAY_TEMP = 'reward_pay_temp';

export const metricAdSencesByLabel: {
  [appName: string]: {
    [category: string]: string[];
  };
} = {
  255: {
    ecology: [Others],
    tool: [Others],
    global: [Others],
    others: [Others],
    ad: ['开屏', '相册', 'feed流外流', '导出'],
  },
  147: {
    ecology: [
      'feed流内流',
      'feed流外流',
      '导出广告',
      '搜索结果页内流',
      '搜索结果页外流',
      '模板评论区',
      '激励视频',
      '相册广告',
      '首页banner',
      '开屏',
      Others,
    ],
    tool: ['激励视频', Others],
    global: ['开屏', Others],
    others: [Others],
    ad: ['开屏', '相册', 'feed流内流', 'feed流外流', '导出', '模板评论区', '激励'],
  },
  305: {
    ecology: [DRAW, EXPORT, FEED, ALBUM, LAUNCH, REWARD_FREE, Others],
    tool: ['reward', REWARD_FREE, Others],
    global: [LAUNCH, Others],
    others: [Others],
    ad: [LAUNCH, ALBUM, DRAW, FEED, EXPORT, REWARD_FREE, REWARD_PAY_TEMP],
  },
};

// 广告核心组所有指标: 醒图/剪映（英文相关）
export const adMetricListForEn: Set<string> = new Set([
  'ad_revenue_total',
  'ARPU_total',
  'ARPU_day',
  'AIPU_total',
  'AIPU_day',
  'CPM',
  'request_count_client',
  'request_success_count_client',
  'send_count',
  'click_count',
  'request_count_rate_client',
  'show_rate',
  'click_count_rate',
  '穿山甲-发送量',
  '穿山甲-请求成功率',
  '穿山甲-展现率',
  '穿山甲-点击率',
  'beizi-发送量',
  'beizi-请求成功率',
  'beizi-展现率',
  'beizi-点击率',
  'ranfeng-发送量',
  'ranfeng-请求成功率',
  'ranfeng-展现率',
  'ranfeng-点击率',
  '科大讯飞-发送量',
  '科大讯飞-请求成功率',
  '科大讯飞-展现率',
  '科大讯飞-点击率',
  '请求量(客户端)',
  '请求成功量(客户端)',
  '发送量',
  '点击量',
  '请求成功率(客户端)',
  '展现率',
  '点击率',
  '穿山甲-广告营收',
  '穿山甲-累计ARPU(单位:¥1/1000)',
  '穿山甲-累计AIPU',
  '穿山甲-CPM',
  '穿山甲-发送量',
  '穿山甲-请求成功率',
  '穿山甲-展现率',
  '穿山甲-点击率',
  '站内-广告营收',
  '站内-累计ARPU(单位:¥1/1000)',
  '站内-累计AIPU',
  '站内-CPM',
  '站内-发送量',
  '站内-请求成功率',
  '站内-展现率',
  '站内-点击率',
  '三方ADN-广告营收',
  '三方ADN-累计ARPU(单位:¥1/1000)',
  '三方ADN-累计AIPU',
  '三方ADN-CPM',
  '三方ADN-发送量',
  '三方ADN-请求成功率',
  '三方ADN-展现率',
  '三方ADN-点击率',
  '广告营收(API)',
  '广告ARPU(API)($1/1000)',
  '广告AIPU(API)',
  '广告CPM(API)',
  'SDK竞价广告ARPU(API)',
  'SDK竞价广告AIPU(API)',
  'SDK竞价广告CPM(API)',
  'SDK竞价广告ARPU(客户端)',
  'SDK竞价广告AIPU(客户端)',
  'SDK竞价广告CPM(客户端)',
  '请求量(客户端)',
  '请求成功量(客户端)',
  '点击量(客户端)',
  '请求成功率(客户端)',
  '点击率(客户端)',
]);

// 广告核心组所有指标: 醒图/剪映（非英文相关）
export const adMetricListForNotEn: Set<string> = new Set([
  'ad_revenue_total',
  'ARPU',
  'show_count',
  'CPM',
  'request_count',
  'request_success_count',
  'send_count',
  'click_count',
  'request_count_rate',
  'show_rate',
  'click_count_rate',
  '广告营收(API)',
  '广告ARPU(API)($1/1000)',
  '广告AIPU(API)',
  '广告CPM(API)',
  'SDK竞价广告ARPU(API)',
  'SDK竞价广告AIPU(API)',
  'SDK竞价广告CPM(API)',
  'SDK竞价广告ARPU(客户端)',
  'SDK竞价广告AIPU(客户端)',
  'SDK竞价广告CPM(客户端)',
  '请求量(客户端)',
  '请求成功量(客户端)',
  '点击量(客户端)',
  '请求成功率(客户端)',
  '点击率(客户端)',
]);

export const metricAdTransFromToAirplane = (str: string) => {
  if (str === '广告营收(预估)') {
    return 'ad_revenue_total';
  }
  if (str === 'ad_revenue') {
    return 'ad_revenue';
  }
  if (str === 'ARPU(预估,单位:$1/1000)') {
    return 'ARPU';
  }
  if (str === '展现量(预估)') {
    return 'show_count';
  }
  if (str === 'CPM(预估)') {
    return 'CPM';
  }
  if (str === '请求量') {
    return 'request_count';
  }
  if (str === '请求成功量') {
    return 'request_success_count';
  }
  if (str === '点击量') {
    return 'click_count';
  }
  if (str === '请求成功率') {
    return 'request_count_rate';
  }
  if (str === '展现率') {
    return 'show_rate';
  }
  if (str === '点击率') {
    return 'click_count_rate';
  }
  if (str === '广告总营收(单位:元)') {
    return 'ad_revenue_total';
  }
  if (str === '累计ARPU(单位:1/1000元)') {
    return 'ARPU_total';
  }
  if (str === '分天ARPU(单位:¥1/1000)') {
    return 'ARPU_day';
  }
  if (str === '累计AIPU') {
    return 'AIPU_total';
  }
  if (str === '分天AIPU') {
    return 'AIPU_day';
  }
  if (str === '请求量(客户端)') {
    return 'request_count_client';
  }
  if (str === '请求成功量(客户端)') {
    return 'request_success_count_client';
  }
  if (str === '发送量') {
    return 'send_count';
  }
  if (str === '请求成功率(客户端)') {
    return 'request_count_rate_client';
  }
  return str;
};

export function getMectricAdForScene(scene: string) {
  if (scene === '广告核心指标组CUPED[新]') {
    return [
      '广告总营收',
      '累计ARPU(单位:¥1/1000)',
      '分天ARPU(单位:¥1/1000)',
      '累计AIPU',
      '分天AIPU',
      'CPM',
      '请求量(客户端)',
      '请求成功量(客户端)',
      '发送量',
      '点击量',
      '请求成功率(客户端)',
      '展现率',
      '点击率',
      '穿山甲-广告营收',
      '穿山甲-累计ARPU(单位:¥1/1000)',
      '穿山甲-累计AIPU',
      '穿山甲-CPM',
      '穿山甲-发送量',
      '穿山甲-请求成功率',
      '穿山甲-展现率',
      '穿山甲-点击率',
      '站内-广告营收',
      '站内-累计ARPU(单位:¥1/1000)',
      '站内-累计AIPU',
      '站内-CPM',
      '站内-发送量',
      '站内-请求成功率',
      '站内-展现率',
      '站内-点击率',
      '三方ADN-广告营收',
      '三方ADN-累计ARPU(单位:¥1/1000)',
      '三方ADN-累计AIPU',
      '三方ADN-CPM',
      '三方ADN-发送量',
      '三方ADN-请求成功率',
      '三方ADN-展现率',
      '三方ADN-点击率',
    ];
  } else if (scene === '广告核心指标组CUPED【新】') {
    return [
      '广告营收(API)',
      '广告ARPU(API)($1/1000)',
      '广告AIPU(API)',
      '广告CPM(API)',
      'SDK竞价广告ARPU(API)',
      'SDK竞价广告AIPU(API)',
      'SDK竞价广告CPM(API)',
      'SDK竞价广告ARPU(客户端)',
      'SDK竞价广告AIPU(客户端)',
      'SDK竞价广告CPM(客户端)',
      '请求量(客户端)',
      '请求成功量(客户端)',
      '点击量(客户端)',
      '请求成功率(客户端)',
      '点击率(客户端)',
    ];
  }
  return [];
}

export function getMetricSearchList(name: string | undefined) {
  if (name === '搜索渗透' || name === '搜索渗透率') {
    return 'penetration_rate';
  }
  if (name === '搜索结果导出渗透' || name === '搜索结果导出渗透率') {
    return 'out_penetration_rate';
  }
  if (name === '搜索结果导出/搜索' || name === '搜索有导比') {
    return 'search_rate';
  }
  if (name === '人均搜索次数') {
    return 'per_capita_rate';
  }
  if (name === '人均搜索导出次数') {
    return 'per_capita_out_rate';
  }
  return '';
}

export type MetricResultType = {
  [vid: string]: {
    [metricGroupId: string]: {
      [metricName: string]: MetricItem;
    };
  };
};

export interface MetricItem {
  value: number;
  p_value: {
    key: string;
    value: number;
  }[];
}
