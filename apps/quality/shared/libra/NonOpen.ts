export interface Token {
  region: string;
  token: string;
}

export enum ManageType {
  /**
   * 服务端
   */
  Server = 'strategy',

  /**
   * AB客户端SDK
   */
  ABClientSDK = 'ab_client_sdk',

  /**
   * Settings普通客户端
   */
  product = 'product',

  SettingsClientNormal = 'settings_client_normal',

  /**
   * SettingsSDK
   */
  SettingsClientSDK = 'settings_client_sdk',

  /**
   * 本地分流
   */
  ABLocalClient = 'ab_local_client_sdk',
}

export interface Owner {
  id: number;
  name: string;
  avatar?: string;
}

/**
 * 创建实验-过滤条件-条件
 */
export interface FilterRuleCondition {
  logic: string;
  condition: {
    key: string;
    op: string;
    value?: boolean | string | number | number[] | string[] | { id: number; name: string }[];
    mod_value?: string | number;
    hash_suffix?: string;
    type: string;
    custom_filter: boolean;
    transformer?: string;
    source: string;
    property_type: string;
  };
}

/**
 * 创建实验-过滤条件
 */
export interface FilterRule {
  logic: string;
  conditions: FilterRuleCondition[];
}

export interface LibraCreateParams {
  /**
   * 是否仅校验
   */
  only_verification?: boolean;

  /**
   * 是否跳过校验
   */
  skip_verification?: boolean;
  /**
   * 实验类型
   */
  manage_type: ManageType;

  /**
   * 实验负责人
   */
  owners: Owner[];

  /**
   * 实验时长，单位s
   */
  duration: number;

  /**
   * 长期实验
   */
  is_long_time_flight?: boolean;

  /**
   * 实验流量%
   */
  version_resource: number;

  /**
   * 预定流量%
   */
  book_version_resource?: number;

  /**
   * 是否平滑生效
   */
  enable_gradual: boolean;

  /**
   * 平滑生效时间min
   */
  gradual_traffic_duration?: number;

  /**
   * 实验名称
   */
  name: string;

  /**
   * 实验描述
   */
  description?: string;

  /**
   * Libra定义的APP ID
   */
  app_id: number;

  ad_flight_hit_rules?: unknown[];

  /**
   * TODO
   * 1 ->
   */
  experiment_mode: number;

  feature_type?: number;

  /**
   * Libra定义的Product ID
   */
  product_id: number;

  /**
   * 流量层信息
   */
  layer_info: {
    /**
     * Libra定义的Product ID
     */
    product_id: number;

    /**
     * hash_strategy
     */
    hash_strategy: string;

    /**
     * 流量层ID
     */
    layer_id: number;
  };

  /**
   * 实验组信息
   */
  versions: {
    /**
     * 实验组名称
     */
    name: string;

    /**
     * 实验组类型
     *
     * TODO，enum
     */
    type: number;

    description?: string;

    /**
     * 实验配置
     */
    config: string;

    settings_keys?: {
      settings_item_id: string;
      key: string;
      value: string;
      type: string;
      desc?: string;
      prefix: string;
      parseValue: { enable: boolean };
      item_id: number;
    }[];
  }[];

  /**
   * 过滤条件
   */
  filter_rule?: FilterRule[];

  /**
   * 绑定需求
   */
  meego_info?: { meego_array: { meego_story: string; meego_project: string; meego_project_key: string }[] };

  /**
   * 实验预期
   */
  expectation?: string;

  /**
   * 指定PSM
   */
  specified_psms?: string[];

  /**
   * 生效机房
   */
  effected_regions?: string[];
}
