import { User } from '@pa/shared/dist/src/core';
import { SlardarPlatformType } from '@pa/shared/dist/src/appSettings/appSettings';

export type ExperimentDataType =
  | ExperimentCrashData
  | ExperimentGroupData
  | ExperimentFeedbackData
  | ExperimentLTData[];

export interface ExperimentGroupData {
  day: number; // 开启天数
  msg: string[]; // 异常类型
  detail: string; // 异常详情
}

export interface ExperimentLTData {
  vId: string;
  lt: {
    data: {
      name: string;
      value: number;
    }[];
    hasError: boolean;
  };
  export: {
    data: {
      name: string;
      value: number;
    }[];
    hasError: boolean;
  };
  share: {
    data: {
      name: string;
      value: number;
    }[];
    hasError: boolean;
  };
  other: {
    data: {
      name: string;
      value: number;
    }[];
    hasError: boolean;
  };
}

export interface ExperimentFeedbackData {
  [vId: string]: {
    value: number;
    hasError: boolean;
  };
}

export interface ExperimentCrashData {
  [vId: string]: {
    [crash_type: string]: {
      value: number;
      valueSum: number;
      url: string;
      hasError: boolean;
    };
  };
}

export enum HandleStatus {
  LongTerm = '长期跟进',
  Handling = '跟进中',
  Error = '误报',
  Closed = '已关闭',
  Suspend = '暂不处理',
  Waiting = '待处理',
}

export enum Priority {
  P0 = 'P0',
  P1 = 'P1',
  P2 = 'P2',
  P3 = 'P3',
}

export enum TestMeegoType {
  Crash = 'Crash',
  FeedBack = 'Feedback',
  GroupUser = 'GroupUser',
  LT = 'LT',
  AD = 'AD',
  Search = 'Search',
}

export const owners_emails: string[] = [
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
];

export enum IndicatorType {
  Crash = '0',
  FeedBack = '1',
  GroupUser = '2',
  LT = '3',
  AD = '4',
  Search = '5',
  EcologyLT = '6',
  Commerce = '7',
}

// 存储所有的实验点，使用groupId区分实验组别
export interface ExperimentData {
  time: number; // 数据更新时间
  value: number;
  valueSum: number;
  id: string; // hashId of type + appid + vId , 前端用三个信息拼id索引数据
  hasError: boolean;
  version: string; //
  url: string;
}

export enum CrashType {
  Anr = 'anr', // only Android
  JavaCrash = 'java_crash', // only Android
  NativeCrash = 'native_crash', // only Android
  OOM = 'OOM',
  NativeOOM = 'native_oom',
  LaunchCrash = 'launch_crash', // only Android
  Jam = 'jam', // only iOS
  Crash = 'crash', // only iOS
  Stuck = 'stuck', // only iOS
}

export const CrashType2Name: { [key: string]: string } = {
  anr: 'ANR', // only Android
  java_crash: 'JavaCrash', // only Android
  native_crash: 'NativeCrash', // only Android
  OOM: 'OOM',
  native_oom: 'NativeOOM',
  launch_crash: '启动', // only Android
  jam: 'JAM', // only iOS
  crash: 'Crash', // only iOS
  stuck: 'Stuck', // only iOS
};

export interface ExperimentFeedbackTable {
  rowId: number;
  name: string;
  url: string;
  id: string;
  createTime: number; // 纸飞机开始记录第一次实验数据的时间
  newErrorTime: number;
  safeTime?: number;
  ownerInfo: User[];
  vId: number; // 实验组id

  chatId?: string; // 记录实验群
  oncallId?: string;

  feedback?: {
    value: number;
    hasError: boolean;
  }; // 每个实验组对应的反馈数量

  compare_value?: number;
  compare_rate?: number;

  handleStatus: HandleStatus; // 前端编辑

  remark: string; // 前端可编辑

  priority: Priority;
}

export interface ExperimentGeneralTableInfo {
  rowId: number;
  name: string;
  url: string;
  id: string;
  createTime: number; // 纸飞机开始记录第一次实验数据的时间
  newErrorTime: number;
  ownerInfo: User[];
  vId: number; // 实验组id

  chatId?: string; // 记录实验群
  oncallId?: string;
  version: string;
  safeTime?: number;
  handleStatus: HandleStatus; // 前端编辑

  remark: string; // 前端可编辑

  priority: Priority;

  data: {
    vId: string;
    general: {
      data: {
        name: string;
        value: number;
      }[];
      hasError: boolean;
    };
  };
}

export interface GeneralRequestInfo {
  name: string; // 实验名称
  url: string; // 实验地址
  id: string; // 实验id
  ownerInfo: string[]; // 实验拥有者邮箱前缀
  appId: number[]; // 可包含双端
  priority: Priority;
  meegoId?: number;
  data: {
    vId: string;
    general: {
      data: {
        name: string;
        value: number;
      }[];
      hasError: boolean;
    };
  }[];
}

export interface ExperimentOtherInfo {
  id: number;
  handleStatus: HandleStatus;
  indicatorType: IndicatorType;
  remark: string;
  priority: Priority;
  appId: number;
  createTime: number; // 纸飞机开始记录第一次实验数据的时间
  newErrorTime: number; // 记录最近一次产生异常的时间，用于排序
  safeTime: number | undefined;
  data: any;
}

export interface ExperimentLTTable {
  rowId: number;
  name: string;
  url: string;
  id: string;
  createTime: number; // 纸飞机开始记录第一次实验数据的时间
  newErrorTime: number;
  safeTime?: number;
  ownerInfo: User[];
  vId: number; // 实验组id

  chatId?: string; // 记录实验群
  oncallId?: string;

  handleStatus: HandleStatus; // 前端编辑

  remark: string; // 前端可编辑

  priority: Priority;

  data: {
    vId: string;
    lt: {
      data: {
        name: string;
        value: number;
      }[];
      hasError: boolean;
    };
    export: {
      data: {
        name: string;
        value: number;
      }[];
      hasError: boolean;
    };
    share: {
      data: {
        name: string;
        value: number;
      }[];
      hasError: boolean;
    };
    other: {
      data: {
        name: string;
        value: number;
      }[];
      hasError: boolean;
    };
  };
}

export interface ExperimentGroupTable {
  rowId: string;
  name: string;
  url: string;
  id: string;
  createTime: number; // 纸飞机开始记录第一次实验数据的时间
  newErrorTime: number;
  safeTime?: number;
  ownerInfo: User[];
  chatId?: string; // 记录实验群
  oncallId?: string;
  day: number; // 开启天数
  msg: string[]; // 异常类型
  detail: string; // 异常详情
  handleStatus: HandleStatus; // 前端编辑
  remark: string; // 前端可编辑
  priority: Priority;
}

export interface ExperimentSearchTable {
  rowId: number;
  name: string;
  url: string;
  id: string;
  createTime: number; // 纸飞机开始记录第一次实验数据的时间
  newErrorTime: number;
  safeTime?: number;
  ownerInfo: User[];
  vId: number; // 实验组id

  chatId?: string; // 记录实验群
  oncallId?: string;

  handleStatus: HandleStatus; // 前端编辑

  remark: string; // 前端可编辑

  priority: Priority;

  data: {
    vId: string;
    // 搜索渗透率
    penetration_rate: {
      data: number;
      hasError: boolean;
    };
    // 搜索导出渗透率
    out_penetration_rate: {
      data: number;
      hasError: boolean;
    };
    // 搜索有导比
    search_rate: {
      data: number;
      hasError: boolean;
    };
    // 人均搜索数
    per_capita_rate: {
      data: number;
      hasError: boolean;
    };
    // 人均搜索导出数
    per_capita_out_rate: {
      data: number;
      hasError: boolean;
    };
  };
}

export interface ExperimentADTable {
  rowId: number;
  name: string;
  url: string;
  id: string;
  createTime: number; // 纸飞机开始记录第一次实验数据的时间
  newErrorTime: number;
  safeTime?: number;
  ownerInfo: User[];
  vId: number; // 实验组id

  chatId?: string; // 记录实验群
  oncallId?: string;

  handleStatus: HandleStatus; // 前端编辑

  remark: string; // 前端可编辑

  priority: Priority;

  data: {
    vId: string;
    // 广告营收
    ad_revenue: {
      data: number;
      hasError: boolean;
    };
    // ARPU($1/1000)
    ARPU: {
      data: number;
      hasError: number;
    };
    // 展示数
    show_count: {
      data: number;
      hasError: number;
    };
    CPM: {
      data: number;
      hasError: boolean;
    };
    // 请求量
    request_count: {
      data: number;
      hasError: boolean;
    };
    // 请求成功数
    request_success_count: {
      data: number;
      hasError: boolean;
    };
    // 点击数
    click_count: {
      data: number;
      hasError: boolean;
    };
    // 点击成功率
    request_count_rate: {
      data: number;
      hasError: boolean;
    };
    // 展示率
    show_rate: {
      value: number;
      hasError: boolean;
    };
    // 点击数
    click_count_rate: {
      data: number;
      hasError: boolean;
    };
  };
}

export interface ExperimentTableInfo {
  rowId: number;
  name: string;
  url: string;
  id: string;
  createTime: number; // 纸飞机开始记录第一次实验数据的时间
  newErrorTime: number;
  ownerInfo: User[];
  vId: number; // 实验组id
  contrastGroup?: string;
  vidSafeTime?: number;
  vIds: string[];

  chatId?: string; // 记录实验群
  oncallId?: string;
  version: string;
  safeTime?: number;
  handleStatus: HandleStatus; // 前端编辑
  isError?: boolean;
  remark: string; // 前端可编辑

  priority: Priority;
  meegoId?: number;
  meego_level?: number;
  meego_status?: string;
  meego_operator?: string[];
  data: {
    [crashType: string]: {
      value: number;
      valueSum: number;
      hasError: boolean;
      url: string;
    };
  };
}

export interface CrashRequestData {
  [vId: string]: {
    // 实验组id
    [type: string]: {
      // 错误类型
      data: {
        value: number; // 实验点数据
        valueSum: number; // 需要展示的总和
        version?: string; // version信息
        appId: number; // 用于区分Android/iOS
        hasError: boolean; // 是否为异常节点
        url: string;
      }[];
    };
  };
}

// 理为User类型，
export interface CrashRequestInfo {
  name: string; // 实验名称
  url: string; // 实验地址
  id: string; // 实验id
  ownerInfo: string[]; // 实验拥有者邮箱前缀
  appId: number[]; // 可包含双端
  priority: Priority;
  meegoId?: string;
  data: CrashRequestData;
}

export interface FeedbackRequestInfo {
  name: string; // 实验名称
  url: string; // 实验地址
  id: string; // 实验id
  ownerInfo: string[]; // 实验拥有者邮箱前缀
  appId: number[]; // 可包含双端
  priority: Priority;
  meegoId?: number;
  data: {
    vId: string;
    feedbackCount: number;
    hasError: boolean;
  }[];
}

export interface ExperimentCard {
  id: string;
  appId: number;
  indicatorType: IndicatorType;
  detail: string;
  priority: number;
  meego_level: number;
  meego_status: string;
  meegoId?: number;
}

export const Type2CardTitle: { [key: string]: string } = {
  0: '崩溃异常',
  1: '反馈异常',
  2: '进组人数异常',
  3: '核心指标异常',
  4: '广告指标异常',
  5: '搜索指标异常',
};

export const AppId2Name: {
  [key: string]: string;
} = {
  1775: '剪映',
  3006: 'CapCut',
  177502: '剪映-Android',
  177501: '剪映-iOS',
  300602: 'CapCut-Android',
  300601: 'CapCut-iOS',
  3704: '剪映-PC',
  359289: 'CapCut-PC',
  348188: 'CapCut-Web',
  548669: '剪映-Web',
  2515: '醒图',
  7356: 'Hypic',
  581595: '即梦',
  513695: '即梦-Web',
};

export const LTType2Name: {
  [key: string]: string;
} = {
  share: '分享',
  export: '导出',
  lt: 'LT',
  other: '其它',
};

export interface GroupRequestInfo {
  name: string; // 实验名称
  url: string; // 实验地址
  id: string; // 实验id
  ownerInfo: string[]; // 实验拥有者邮箱前缀
  appId: number[]; // 可包含双端
  priority: Priority;
  meegoId?: number;
  data: {
    day: number; // 开启天数
    msg: string[]; // 异常类型
    detail: string; // 异常详情
  };
}

export interface LTRequestInfo {
  name: string; // 实验名称
  url: string; // 实验地址
  id: string; // 实验id
  ownerInfo: string[]; // 实验拥有者邮箱前缀
  appId: number[]; // 可包含双端
  priority: Priority;
  meegoId?: number;
  data: {
    vId: string;
    lt: {
      data: {
        name: string;
        value: number;
      }[];
      hasError: boolean;
    };
    export: {
      data: {
        name: string;
        value: number;
      }[];
      hasError: boolean;
    };
    share: {
      data: {
        name: string;
        value: number;
      }[];
      hasError: boolean;
    };
    other: {
      data: {
        name: string;
        value: number;
      }[];
      hasError: boolean;
    };
  }[];
}

export interface ExperimentErrorInfo {
  id: string;
  appId: number;
  indicatorType: IndicatorType;
  detail: string[];
  priority: number;
  status: HandleStatus;
  newErrorTime: number;
  indicatorTitle?: string;
}

export enum DeviceLevel {
  ALL,
  LOW,
  HIGH,
}
