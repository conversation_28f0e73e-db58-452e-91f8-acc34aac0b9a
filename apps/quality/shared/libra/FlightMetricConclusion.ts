import { LibraVersionType } from '@shared/libra/LibraNewInfo';

export interface FlightMetricConclusionParams {
  start_date: string;
  end_date: string;
  metric_group: number;
  view_type: string;
  merge_type: string;
  selected_metric_ids: string;
  dim: number;
  dim_vals: string;
  mult_cmp_corr?: number;
  data_caliber?: number;
  combine?: number;
  data_region?: string;
  date?: string;
  need_fallback?: boolean;
}
export enum FlightMetricSignificantType {
  NotSignificant = 0, // 不显著
  SignificantlyPositive = 1, // 显著正向
  SignificantlyNegative = 2, // 显著负向
}
// 各实验组指标详情
export interface FlightSpecificVersionMetricDetail {
  vid: number;
  vname: string;
  vtype: LibraVersionType;
  absoluteValue: number;
  pValue?: number;
  significantType?: FlightMetricSignificantType;
  relativeDiff?: number; // 相对差
  absoluteDiff?: number; // 绝对差
  margin?: number; // 置信区间宽度
}
// 指标：日活/导出LT等等
export interface FlightSpecificMetricConclusion {
  metricName: string;
  metricId: string;
  versionDetails: FlightSpecificVersionMetricDetail[];
}
// 指标组：videocut active days CUPED/大盘核心指标CUPED。。。
export interface FlightMetricGroupConclusion {
  groupName: string;
  groupId: string;
  metricConclusions: FlightSpecificMetricConclusion[];
}

// 指标大类：APP必看/稳定性
export interface FlightMetricConclusionClass {
  className: string;
  classId: string;
  groups: FlightMetricGroupConclusion[];
}

// 指标结论汇总
export interface FlightMetricConclusion {
  flightId: number;
  classes: FlightMetricConclusionClass[];
}

export interface FlightConclusionClassConfig {
  classId: string;
  className: string;
  dim?: number;
  dim_vals?: string[];
  groups: {
    groupId: string;
    groupName: string;
    metrics: {
      metricId: string;
      metricName: string;
    }[];
  }[];
}
