// Generated by ts-to-zod
import { z } from 'zod';
import { ManageType } from './NonOpen';

export const tokenSchema = z.object({
  region: z.string(),
  token: z.string(),
});

export const manageTypeSchema = z.nativeEnum(ManageType);

export const ownerSchema = z.object({
  id: z.number(),
  name: z.string(),
  avatar: z.string().optional(),
});

export const filterRuleConditionSchema = z.object({
  logic: z.string(),
  condition: z.object({
    key: z.string(),
    op: z.string(),
    value: z
      .union([
        z.boolean(),
        z.string(),
        z.number(),
        z.array(z.number()),
        z.array(z.string()),
        z.array(
          z.object({
            id: z.number(),
            name: z.string(),
          }),
        ),
      ])
      .optional(),
    mod_value: z.union([z.string(), z.number()]).optional(),
    hash_suffix: z.string().optional(),
    type: z.string(),
    custom_filter: z.boolean(),
    transformer: z.string().optional(),
    source: z.string(),
    property_type: z.string(),
  }),
});

export const filterRuleSchema = z.object({
  logic: z.string(),
  conditions: z.array(filterRuleConditionSchema),
});

export const cubaCheckSchema = z.object({
  story_id: z.string(),
  ab_name: z.string(),
  business: z.string(),
  flow_total: z.number(),
  flow_reserved: z.number(),
  resource_divide_type: z.string(),
  platform: z.string(),
  client_start_version: z.string(),
  ab_duration: z.number(),
  countries: z.string(),
  layer_priority: z.number(),
  libra_app_id: z.number(),
});

export const libraCreateParamsSchema = z.object({
  only_verification: z.boolean().optional(),
  skip_verification: z.boolean().optional(),
  manage_type: manageTypeSchema,
  owners: z.array(ownerSchema),
  duration: z.number(),
  is_long_time_flight: z.boolean().optional(),
  version_resource: z.number(),
  book_version_resource: z.number().optional(),
  enable_gradual: z.boolean(),
  gradual_traffic_duration: z.number().optional(),
  name: z.string(),
  description: z.string().optional(),
  app_id: z.number(),
  ad_flight_hit_rules: z.array(z.unknown()).optional(),
  experiment_mode: z.number(),
  feature_type: z.number().optional(),
  product_id: z.number(),
  layer_info: z.object({
    product_id: z.number(),
    hash_strategy: z.string(),
    layer_id: z.number(),
  }),
  versions: z.array(
    z.object({
      name: z.string(),
      type: z.number(),
      description: z.string().optional(),
      config: z.string(),
      settings_keys: z
        .array(
          z.object({
            settings_item_id: z.string(),
            key: z.string(),
            value: z.string(),
            type: z.string(),
            desc: z.string().optional(),
            prefix: z.string(),
            parseValue: z.object({
              enable: z.boolean(),
            }),
            item_id: z.number(),
          }),
        )
        .optional(),
    }),
  ),
  filter_rule: z.array(filterRuleSchema).optional(),
  meego_info: z
    .object({
      meego_array: z.array(
        z.object({
          meego_story: z.string(),
          meego_project: z.string(),
          meego_project_key: z.string(),
        }),
      ),
    })
    .optional(),
  expectation: z.string().optional(),
  specified_psms: z.array(z.string()).optional(),
  effected_regions: z.array(z.string()).optional(),
  is_open_beta_combine: z.boolean().optional(),
  beta_combine_type: z.number().optional(),
  short_name: z.string().optional(),
  metrics: z
    .array(
      z.object({
        type: z.string(),
        metric_group_id: z.number(),
        name: z.string(),
        bundle_id: z.number(),
        metric_group_type: z.string(),
        status: z.number(),
        certification_type: z.string(),
        dimensions: z.array(z.any()),
        non_echo: z.boolean(),
      }),
    )
    .optional(),
  filter_user_list: z.number().optional(),
});
