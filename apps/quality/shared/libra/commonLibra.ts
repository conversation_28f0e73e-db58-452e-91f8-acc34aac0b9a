import { User } from '@pa/shared/dist/src/core';

export enum LibraRegion {
  UNKNOWN = -1, // 未知
  CN = 0, // 国内
  SG = 1, // 海外(新加坡)
  VA = 2, // 海外(美东)
}

export interface LibraToken {
  access_token: string;
  ttl: number; // 单位：秒
}

export interface LibraSecret {
  app_id: string;
  app_secret: string;
}

export interface LibraResult<T> {
  data?: T;
  message: string;
  code: number;
}

export interface ConflictExperiment {
  experiment_id: number;
  experiment_name: string;
  layer_type: number;
  mode: number;
  conflict_keys: string[];
  owners: {
    name: string;
    avatar: string;
  }[];
  region: string;
  status: number;
  start_time: number;
}

export interface CubaCheckValue {
  name: string;
  expect: any;
  actual: any;
}

export interface CubaCheckFailData {
  code: number;
  msg: string;
  data: CubaCheckValue[];
}

export interface LibraCreateFailData {
  can_skip: boolean;
  conflict_experiments: ConflictExperiment[];
  experiment_hint_rule: any;
  conflict_features: any;
}

export interface CreateLibraResult {
  experiments: { id: number; app_id: number; name: string }[];
}

export enum LibraAppIds {
  lv = '1775',
  lv_pc = '3704',
  lv_web = '548669',
  cc = '3006',
  cc_pc = '359289',
  cc_web = '348188',
  retouch = '2515',
  hypic = '7356',
}

// appIds：产品注册 appid，若有多个以逗号分隔  剪映:147 CapCut:305
export const isSGLibraByAppId = (appIds: string) =>
  appIds.includes(LibraAppIds.cc) ||
  appIds.includes(LibraAppIds.cc_web) ||
  appIds.includes(LibraAppIds.cc_pc) ||
  appIds.includes(LibraAppIds.hypic);

export const isCNLibraByAppId = (appIds: string) =>
  appIds.includes(LibraAppIds.lv) ||
  appIds.includes(LibraAppIds.lv_pc) ||
  appIds.includes(LibraAppIds.lv_web) ||
  appIds.includes(LibraAppIds.retouch);

export const isSGLibraByLibraAppId = (libraAppId: number) =>
  libraAppId === 305 || libraAppId === 381 || libraAppId === 360 || libraAppId === 367;

export const isCNLibraByLibraAppId = (libraAppId: number) =>
  libraAppId === 147 || libraAppId === 399 || libraAppId === 1071 || libraAppId === 255;

export const Ad_inland = 'ad_inland';
export const Ad_oversea = 'ad_oversea';
export const Subscribe_inland = 'subscribe_inland';
export const Subscribe_oversea = 'subscribe_oversea';

export const AdInlandPsmList = [
  'faceu.commerce.ad_api',
  'faceu.commerce.promotion_rpc',
  'videocut.ug.incentive_center',
];
export const AdOverseaPsmList = ['capcut.ug.incentive_center', 'faceu.strategy.api'];
export const SubscribeInlandPsmList = [
  'faceu.commerce.api',
  'faceu.subscribe.api',
  'faceu.commerce.benefit',
  'faceu.commerce.subscribe_service',
  'faceu.commerce.pms',
  'faceu.commerce.marketing',
  'faceu.commerce.strategy',
  'faceu.commerce.supply',
  'faceu.commerce.consumption',
  'faceu.commerce.benefit_v2',
  'faceu.commerce.user',
  'faceu.commerce.ad_api',
  'faceu.lv.business_api',
  'image.commerce.resource',
  'image.commerce.sku',
  'image.commerce.resource_benefit',
  'image.commerce.admin',
];
export const SubscribeOverseaPsmList = [
  'capcut.commerce.api',
  'capcut.commerce.subscribe',
  'capcut.commerce.pms',
  'hypic.commerce.api',
  'hypic.commerce.subscribe',
  'capcut.commerce.strategy',
  'capcut.commerce.trade_api',
  'capcut.commerce.trade',
  'capcut.commerce.resource_benefit',
  'capcut.commerce.supply',
  'capcut.commerce.daemon_i18n_conf',
  'image.commerce.resource_benefit',
  'capcut.commerce.credits',
  'capcut.commerce.consumption',
];

export const CommercialPsmMap = new Map([
  [Ad_inland, AdInlandPsmList],
  [Ad_oversea, AdOverseaPsmList],
  [Subscribe_inland, SubscribeInlandPsmList],
  [Subscribe_oversea, SubscribeOverseaPsmList],
]);

export enum LibraModel {
  lvSDK = '444',
  globalCapCutClient = '765',
}

export enum LibraControllerRule {
  AppIdRule = '商业化实验必须配置过滤条件app_id',
  VersionCodeSubscribeRule = '版本号配置错误，_version_code, _version_code_normal, _update_version_code后两位值>10则非法',
  VersionCodeAdRule = '广告版本号配置错误，1.android_vc:国内共9位且第5位为0，海外共8位;2.ios_vc,中间两位>10则非法',
  StrLowerRule = '商业化实验字符串参数，值包含英文，但未配置兼容小写规则',
  layerNameRule = '商业化实验配置的流量层不属于互斥域',
}

export enum LibraPlatform {
  android = 'android',
  iPhone = 'iphone',
  iOS = 'ios',
  iPad = 'ipad', // 生态的实验，ipad 也需要选择
}

export enum LibraStatus {
  stopped = 'stopped',
  running = 'running',
  testing = 'testing',
  suspended = 'suspended',
}
