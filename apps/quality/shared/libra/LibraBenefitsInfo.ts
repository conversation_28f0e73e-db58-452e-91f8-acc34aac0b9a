import { LibraNewInfo } from '@shared/libra/LibraNewInfo';
import { User } from '@pa/shared/dist/src/core';

export interface LibraBenefitsVidBenefits {
  // 指标id
  metric_id: number;
  // 指标名称
  metric_name: string;
  // 指标组id
  metric_group_id: number;
  // 指标组名称
  metric_group_name: string;
  // 显著性：1 正向显著 2 负向显著 0 不显著
  significance: number;
  // 相对差
  relative_diff: number;
  // 绝对差
  absolute_diff: number;
  // P值
  p_value: number;
  // 置信区间 [均值-margin,均值+margin]
  margin: number;
  // 检验灵敏度
  mde: number;
  // 指标值
  metric_value: number;
}

export interface LibraBenefitsVidInfo {
  // 实验组id
  vid: number;
  // 收益信息（数组）
  benefits: LibraBenefitsVidBenefits[];
}

// 实验收益信息（来自 Hive 表：image_dw.app_abtest_pa_flight_id_cn_df）
// https://data.bytedance.net/coral/datamap/detail?groupName=default&qualifiedName=HiveTable%3A%2F%2F%2Fimage_dw%2Fapp_abtest_pa_flight_id_cn_df%400&subTab=schema&tab=table_info#group=default
export interface LibraBenefitsInfo {
  // Libra 登记产品ID
  app_id: number;
  // Libra 登记产品
  app: string;
  // Libra 产品功能模块
  token: string;
  // 实验id
  flight_id: number;
  // 实验名称
  display_name: string;
  // 实验结束日期
  flight_end_date: string;
  // 实验开始日期
  flight_start_date: string;
  // 实验owner
  flight_owner: string;
  // 当前实验状态
  latest_flight_status: number;
  // 实验层
  flight_layer: string;
  // 实验流量
  flight_version_resource: number;
  // 实验分流策略
  flight_hash_strategy: string;
  // 实验tags
  flight_tags: string;
  // 实验组信息
  vid_infos: LibraBenefitsVidInfo[];
  // 查询日期
  search_date: string;
  // 区域（cn、sg、va）
  region: string;
}

// 将收益信息 LibraBenefitsInfo 平铺，以指标维度展示收益信息
export interface LibraBenefitsFlattenInfoByMetricId {
  // Libra 登记产品ID
  app_id: number;
  // Libra 登记产品
  app: string;
  // Libra 产品功能模块
  token: string;
  // 实验id
  flight_id: number;
  // 实验名称
  display_name: string;
  // 实验结束日期
  flight_end_date: string;
  // 实验开始日期
  flight_start_date: string;
  // 实验owner
  flight_owner: string;
  // 当前实验状态
  latest_flight_status: number;
  // 业务线
  business_line: string;
  // 实验层
  flight_layer: string;
  // 实验流量
  flight_version_resource: number;
  // 实验分流策略
  flight_hash_strategy: string;
  // 实验tags
  flight_tags: string;
  // 查询日期
  search_date: string;
  // 区域（cn、sg、va）
  region: string;
  // 实验组id
  vid: number;
  // 指标id
  metric_id: number;
  // 指标名称
  metric_name: string;
  // 指标组id
  metric_group_id: number;
  // 指标组名称
  metric_group_name: string;
  // 显著性：1 正向显著 2 负向显著 0 不显著
  significance: number;
  // 相对差
  relative_diff: number;
  // 绝对差
  absolute_diff: number;
  // P值
  p_value: number;
  // 置信区间 [均值-margin,均值+margin]
  margin: number;
  // 检验灵敏度
  mde: number;
  // 指标值
  metric_value: number;
  // 实验链接
  flight_url: string;
  // Meego 名称
  meego_name: string;
  // Meego 链接
  meego_url: string;
  // Meego PM
  meego_pm: string;
  // 跟车版本
  release_version: string;
}

// 将收益信息 LibraBenefitsInfo 平铺，以实验维度展示收益信息
export interface LibraBenefitsFlattenInfoByFlightId {
  // Libra 登记产品ID
  app_id: number;
  // Libra 登记产品
  app: string;
  // Libra 产品功能模块
  token: string;
  // 实验id
  flight_id: number;
  // 实验名称
  display_name: string;
  // 实验结束日期
  flight_end_date: string;
  // 实验开始日期
  flight_start_date: string;
  // 实验owner
  flight_owner: string;
  // 当前实验状态
  latest_flight_status: number;
  // 业务线
  business_line: string;
  // 实验层
  flight_layer: string;
  // 实验流量
  flight_version_resource: number;
  // 实验分流策略
  flight_hash_strategy: string;
  // 实验tags
  flight_tags: string;
  // 查询日期
  search_date: string;
  // 区域（cn、sg、va）
  region: string;
  // 实验链接
  flight_url: string;
  // Meego 名称
  meego_name: string;
  // Meego 类型
  meego_type: string;
  // Meego 链接
  meego_url: string;
  // Meego PM
  meego_pm: string;
  // 跟车版本
  release_version: string;
  // 大盘核心指标正向显著个数
  lt_positive_significant_metrics_count: number;
  // 大盘核心指标正向显著汇总
  lt_positive_significant_metrics_summary: string;
  // 大盘核心指标负向显著个数
  lt_negative_significant_metrics_count: number;
  // 大盘核心指标负向显著汇总
  lt_negative_significant_metrics_summary: string;
  // 业务核心指标正向显著个数
  biz_positive_significant_metrics_count: number;
  // 业务核心指标正向显著汇总
  biz_positive_significant_metrics_summary: string;
  // 业务核心指标负向显著个数
  biz_negative_significant_metrics_count: number;
  // 业务核心指标负向显著汇总
  biz_negative_significant_metrics_summary: string;
  // 核心性能指标正向显著个数
  perf_positive_significant_metrics_count: number;
  // 核心性能指标正向显著汇总
  perf_positive_significant_metrics_summary: string;
  // 核心性能指标负向显著个数
  perf_negative_significant_metrics_count: number;
  // 核心新能指标负向显著汇总
  perf_negative_significant_metrics_summary: string;
  // 实验关闭原因
  flight_stop_reason: string;
  // meego 团队信息
  meego_team_name: string;
  // 实验关闭归因（主要分类）
  flight_close_main_type_name: string;
  // 实验关闭归因（子分类）
  flight_close_sub_type_name: string;
  // 实验关闭归因（详细分类）
  flight_close_sub_type_detail_name: string;
  // 实验关闭归因（自定义原因）
  flight_close_custom_reason: string;
  // 实验关闭时间戳
  flight_close_timestamp: number;
}

// 日常查询 Hive 表用到的信息
export interface LibraDailyQueryHiveTableInfo {
  // 实验id
  flight_id: string;
  // 查询收益日期
  search_date: string;
  // 区域（cn、sg、va）
  region: string;
}

// 实验周报信息(单条实验收益信息)
export interface LibraWeeklyReportSingleItemInfo {
  libraInfo: LibraNewInfo;
  benefitsInfo?: LibraBenefitsInfo;
  // 大盘核心指标正向显著个数
  lt_positive_significant_metrics_count: number;
  // 大盘核心指标正向显著汇总
  lt_positive_significant_metrics_summary: string;
  // 大盘核心指标负向显著个数
  lt_negative_significant_metrics_count: number;
  // 大盘核心指标负向显著汇总
  lt_negative_significant_metrics_summary: string;
  // 业务核心指标正向显著个数
  biz_positive_significant_metrics_count: number;
  // 业务核心指标正向显著汇总
  biz_positive_significant_metrics_summary: string;
  // 业务核心指标负向显著个数
  biz_negative_significant_metrics_count: number;
  // 业务核心指标负向显著汇总
  biz_negative_significant_metrics_summary: string;
  // 核心性能指标正向显著个数
  perf_positive_significant_metrics_count: number;
  // 核心性能指标正向显著汇总
  perf_positive_significant_metrics_summary: string;
  // 核心性能指标负向显著个数
  perf_negative_significant_metrics_count: number;
  // 核心新能指标负向显著汇总
  perf_negative_significant_metrics_summary: string;
}

export interface LibraWeeklyReportSummaryInfo {
  // 周报概览（比如：检测到线上生效实验，共z个，其中a个核心指标负向，b个核心指标正向，c个无收益）
  overview: string;
  // 业务线
  business_line: string;
  // 周报统计开始时间
  start_date: string;
  // 周报统计结束时间
  end_date: string;
  // 周报生成时间
  generate_time: number;
  // 周报推送时间
  push_time?: number;
  // 周报推送人
  push_user_list?: User[];
  // 周报飞书链接
  lark_url?: string;
  // 所有实验列表
  all_flight_list: LibraWeeklyReportSingleItemInfo[];
  // 大盘核心指标正向实验列表
  lt_positive_significant_flight_list: LibraWeeklyReportSingleItemInfo[];
  // 大盘核心指标负向实验列表
  lt_negative_significant_flight_list: LibraWeeklyReportSingleItemInfo[];
  // 业务核心指标正向实验列表
  biz_positive_significant_flight_list: LibraWeeklyReportSingleItemInfo[];
  // 业务核心指标负向实验列表
  biz_negative_significant_flight_list: LibraWeeklyReportSingleItemInfo[];
  // 核心性能指标正向实验列表
  perf_positive_significant_flight_list: LibraWeeklyReportSingleItemInfo[];
  // 核心性能指标负向实验列表
  perf_negative_significant_flight_list: LibraWeeklyReportSingleItemInfo[];
  // 无显著性实验列表
  no_significant_flight_list: LibraWeeklyReportSingleItemInfo[];
}
