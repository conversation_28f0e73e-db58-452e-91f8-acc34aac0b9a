// Meego 度量信息（用于衡量交付效率）
export interface MeegoMetricInfo {
  // 需求 Id
  meegoId: number;
  // 需求名称
  meegoName: string;
  // 需求链接
  meegoUrl: string;
  // 需求类型
  meegoType: string;
  // 需求优先级
  meegoPriority: string;
  // 需求状态
  meegoStatus: string;
  // 需求业务线
  businessLine: string;
  // 上线应用
  releaseApps: string;
  // 上线版本
  releaseVersion: string;
  // 上线区域
  releaseRegions: string;
  // PM
  pmOwners: string;
  // tech owners
  techOwners: string;
  // 实验 Id
  flightIds: string;
  // 实验名称
  flightNames: string;
  // 实验链接
  flightUrls: string;
  // 实验对应产品名称
  flightAppNames: string;
  // Meego 虚拟团队
  meegoTeams: string;
  // 实验类型
  libraTypes: string;
  // 实验类型（正式、灰度、反转等）
  flightTypes: string;
  // 总进组用户数
  userNumbers: string;
  //  当前流量
  currentTraffics: string;
  // 正式实验最早的开始时间
  minFlightStartTime: string;
  // 正式实验最晚的全量时间
  maxFlightFullTime: string;
  // 实验全量周期（总天数）
  flightFullDurationTotalDays: number;
  // 实验全量周期（工作日天数）
  flightFullDurationOnlyWorkDays: number;
  // 实验持续时间（针对于还在开启中的实验）(总天数)
  flightContinuousDurationTotalDays: number;
  // 实验持续时间（针对于还在开启中的实验）工作日天数)
  flightContinuousDurationOnlyWorkDays: number;
  // 总实验个数
  flightCountTotal: number;
  // 正式实验个数
  flightTypeCountRelease: number;
  // 灰度实验个数
  flightTypeCountGray: number;
  // 反转实验个数
  flightTypeCountInvert: number;
  // 长期实验个数
  flightTypeCountLongTerm: number;
  // 其他实验个数
  flightTypeCountOther: number;
  // 首个实验开启时间（不管是正式还是灰度）
  firstFlightStartTime: string;
  // 最后实验关闭时间（不管是正式还是灰度）
  lastFlightEndTime: string;
  // 正在进行中的实验个数
  flightInProgressCount: number;
  // 已关闭或已结束的实验个数
  flightClosedCount: number;
  // 正式实验的关闭数量
  releaseFlightClosedCount: number;
  // 正式实验最早开启时间
  minReleaseFlightStartTime: string;
  // 正式实验最晚全量时间
  maxReleaseFlightFullTime: string;
  // 正式实验最晚关闭时间
  maxReleaseFlightEndTime: string; // 关闭不等同于全量，可能没有全量直接关闭了，此处也需要记录下
  // 反转、长期实验的最早开启时间
  minInvertOrLongTermFlightStartTime: string;
  // 反转、长期实验的最晚关闭时间
  maxInvertOrLongTermFlightEndTime: string;
  // 正式实验的异常重开次数（异常重开：代码相关&策略变更&其他）
  releaseFlightAbnormalReopenCount: number;
  // 正式实验中有问题的实验（异常重开：代码相关）
  releaseFlightWithCodeProblemCount: number;
  // 正式实验-超期实验个数（开启时间超过 2 个月）
  releaseFlightOverDueCount: number;
  // 灰度实验的异常重开次数（异常重开：代码相关&策略变更&其他）
  grayFlightAbnormalReopenCount: number;
  // 灰度实验中有问题的实验（异常重开：代码相关）
  grayFlightWithCodeProblemCount: number;
  // 没有实验关闭归因的数量（只针对已关闭的实验来统计）
  flightWithoutCloseAttributionCount: number;
  // 是否有异常重开的实验（包括灰度、正式）
  hasAbnormalReopenFlight: string; // 是、否
  // 异常重开率
  flightAbnormalReopenRate: string; // 百分比
  // 是否有代码相关异常的问题实验
  hasCodeProblemFlight: string; // 是、否
  // 代码相关异常问题实验的占比
  flightHasCodeProblemRate: string; // 百分比
  // 需求创建时间
  meegoCreateTime: string;
  // 需求初评完成时间
  initialRequirementsReviewEndTime: string;
  // 需求详评完成时间
  detailedRequirementsReviewEndTime: string;
  // 开发完成时间
  developmentEndTime: string;
  // 测试完成时间
  testingEndTime: string;
  // 封版完成时间
  codeFreezeEndTime: string;
  // iOS 提审时间
  iOSAppStoreReviewTime: string;
  // Android 提审时间
  androidAppStoreReviewTime: string;
  // 需求全量周期（总天数）
  meegoFullDurationTotalDays: number;
  // 需求全量周期（工作日天数）
  meegoFullDurationOnlyWorkDays: number;
  // 初评耗时（初评完成时间 - 需求创建时间）（总天数）
  initialRequirementsReviewDurationTotalDays: number;
  // 初评耗时（初评完成时间 - 需求创建时间）（工作日天数）
  initialRequirementsReviewDurationOnlyWorkDays: number;
  // 详评-开发完成耗时（开发完成时间 - 详情完成时间）（总天数）
  developEndDurationTotalDays: number;
  // 详评-开发完成耗时（开发完成时间 - 详情完成时间）（工作日天数）
  developEndDurationOnlyWorkDays: number;
  // 需求测试周期（测试完成时间 - 开发完成时间）（总天数）
  meegoTestDurationTotalDays: number;
  // 需求测试周期（测试完成时间 - 开发完成时间）（工作日天数）
  meegoTestDurationOnlyWorkDays: number;
  // iOS 发版周期（提审 - 封版）（总天数）
  iOSReleaseDurationTotalDays: number;
  // iOS 发版周期（提审 - 封版）（工作日天数）
  iOSReleaseDurationOnlyWorkDays: number;
  // Android 发版周期（提审 - 封版）（总天数）
  androidReleaseDurationTotalDays: number;
  // Android 发版周期（提审 - 封版）（工作日天数）
  androidReleaseDurationOnlyWorkDays: number;
}
