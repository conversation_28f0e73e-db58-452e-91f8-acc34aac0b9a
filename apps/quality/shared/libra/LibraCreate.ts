import { FilterRule, LibraCreateParams, ManageType, Token } from '@shared/libra/NonOpen';
import {
  getAppInfo,
  Layer,
  LIBRA_APP_ID_CAPCUT,
  LIBRA_APP_ID_LV,
  LIBRA_APP_ID_XINGTU,
  libraRegionFromAppId,
  OnlineVersion,
  ParamFilters,
} from '@shared/libra/NonOpenCommon';
import { Value } from '@douyinfe/semi-ui/lib/es/cascader';
import {
  batch_create_experiment,
  check_cuba,
  filter_global_app,
  flight_tag_app,
  gen_filter_code,
  param_filters_op,
  single_product_layer,
} from '@api/libraNonOpen';
import { operation } from 'retry';
import { QUALITY_HOST_CN_HTTPS, QUALITY_HOST_SG_HTTPS, User } from '@pa/shared/dist/src/core';
import { trim_suffix } from '@shared/utils/tools';
import { WorkItemInfo } from '@shared/typings/meego';
import {
  Business,
  BUSINESS_ID_COMMERCIAL_AD,
  BUSINESS_ID_COMMERCIAL_SUBSCRIPTION,
  queryBussinessByID,
} from '@shared/meego/common';
import {
  createLibraSuccess,
  getFlightList,
  hasExempted,
  isFullRelease,
  isThirdGray,
  queryFlightErrorList,
  recordAbnormalFlightReportInfo,
} from '@api/libra';
import { z } from 'zod';
import { LibraVersionPlatform } from '@shared/libra/libraInfo';
import { LibraPlatform } from '@shared/libra/commonLibra';
import { cc2lvVersion, versionCodeToMeegoVersion, versionName2Code } from '@shared/utils/version_utils';
import { current_region, Region } from '../../api/utils/region';
import { AppSettingId } from '@pa/shared/dist/src/appSettings/appSettings';
import { LibraFlightType } from '@shared/libra/LibraNewInfo';
import { owners_emails } from '@shared/libra/common';
import { useInject } from '@edenx/runtime/bff';
import { LibraNewInfoListService } from '../../api/service/libra/LibraNewInfoListService';
import axios from 'axios';

export type DynamicObject = {
  [key: string]: string | number | boolean | DynamicObject | DynamicObject[];
};

/**
 * 重试
 * @param args
 */
export const retry = async <T>(args: {
  job: () => Promise<T>;
  judge: (t: T) => Promise<boolean>;
  abort?: (t: T) => Promise<boolean>;
  retries?: number;
  factor?: number;
  minTimeout?: number;
  maxTimeout?: number;
}): Promise<T> => {
  const op = operation({
    retries: args.retries ?? 5,
    factor: args.factor ?? 1.5,
    minTimeout: args.minTimeout ?? 2000,
    maxTimeout: args.maxTimeout ?? 15000,
  });

  return await new Promise<T>((resolve, reject) => {
    op.attempt(async () => {
      try {
        const result = await args.job();
        if (await args.judge(result)) {
          resolve(result);
        } else if (args.abort !== undefined && (await args.abort(result))) {
          // Do nothing
        } else {
          throw new Error('to retry');
        }
      } catch (error) {
        if (error instanceof Error) {
          if (op.retry(error)) {
            return;
          }
        }
      }
    });
  });
};

export const getCNToken = (userEmail: string, callback: (result: Token | undefined) => void) => {
  retry<{ token?: string } | undefined>({
    job: async () => {
      const response = await fetch(
        `${QUALITY_HOST_CN_HTTPS}/api/quality/cn/get_titan_passport_id?email=${trim_suffix('@bytedance.com')(userEmail)}`,
      );
      if (response.ok) {
        return (await response.json()) as { token?: string };
      }
      return undefined;
    },
    judge: async data => data !== undefined,
  })
    .then(response => {
      callback(response?.token !== undefined ? { region: 'cn', token: response.token } : undefined);
    })
    .catch(_ => {
      callback(undefined);
    });
};

/**
 * 获取 CN 区域的 token
 * @param userEmail 用户邮箱
 * @returns 返回 CN 区域的 token 或 undefined
 */
export const getCNTokenAsync = async (userEmail: string): Promise<Token | undefined> => {
  try {
    const result = await retry<{ token?: string } | undefined>({
      job: async () => {
        try {
          const response = await axios.get(
            `${QUALITY_HOST_CN_HTTPS}/api/quality/cn/get_titan_passport_id?email=${trim_suffix('@bytedance.com')(userEmail)}`,
          );
          return response.data as { token?: string };
        } catch {
          return undefined;
        }
      },
      judge: async data => data !== undefined,
    });
    return result?.token !== undefined ? { region: 'cn', token: result.token } : undefined;
  } catch (_) {
    return undefined;
  }
};

export const getSGToken = (userEmail: string, callback: (result: Token | undefined) => void) => {
  retry<{ token?: string } | undefined>({
    job: async () => {
      const response = await fetch(
        `${QUALITY_HOST_SG_HTTPS}/api/quality/sg/get_titan_passport_id?email=${trim_suffix('@bytedance.com')(userEmail)}`,
      );
      if (response.ok) {
        return (await response.json()) as { token?: string };
      }
      return undefined;
    },
    judge: async data => {
      if (data !== undefined) {
        return true;
      }
      return false;
    },
  })
    .then(response => {
      callback(response?.token !== undefined ? { region: 'sg', token: response.token } : undefined);
    })
    .catch(_ => {
      callback(undefined);
    });
};

/**
 * 获取 SG 区域的 token
 * @param userEmail 用户邮箱
 * @returns 返回 SG 区域的 token 或 undefined
 */
export const getSGTokenAsync = async (userEmail: string): Promise<Token | undefined> => {
  try {
    const result = await retry<{ token?: string } | undefined>({
      job: async () => {
        try {
          const response = await axios.get(
            `${QUALITY_HOST_SG_HTTPS}/api/quality/sg/get_titan_passport_id?email=${trim_suffix('@bytedance.com')(userEmail)}`,
          );
          return response.data as { token?: string };
        } catch {
          return undefined;
        }
      },
      judge: async data => data !== undefined,
    });
    return result?.token !== undefined ? { region: 'sg', token: result.token } : undefined;
  } catch (_) {
    return undefined;
  }
};

export const getPrivilegedLibraAppIds = (
  isCN: boolean,
  tokens: Token[],
  callback: (result: number[] | undefined) => void,
) => {
  retry({
    job: async () =>
      (
        await filter_global_app({
          headers: {},
          data: {
            libra_app_id: isCN ? 147 : 305, // 仅代码国内 or 海外
            tokens,
          },
        })
      ).data,
    judge: async data => data !== undefined,
  })
    .then(data => {
      callback(data?.map(item => item.id));
    })
    .catch(_ => {
      callback(undefined);
    });
};

export interface ABItem {
  brief: string;
  data_type: number;
  is_using: number;
  item_id: number;
  item_name: string;
  param_demo_json: string;
  schema: string;
}

/**
 * 过滤条件-本地配置
 */
export interface Filter {
  key?: string;
  op?: string;
  value?: boolean | number | number[] | string | string[];
  modValue?: string | number;
  hashSuffix?: string;
  bundleId?: number[];
  useTransformer?: boolean;
  transformer?: string;
}

/**
 * 流量层-本地配置
 */
export interface ProductLayer {
  libraAppid: number;
  manageType: ManageType;

  id: number;
  name: string;
  layers?: Layer[];
  version_resource?: number;
  book_version_resource?: number;
}

export enum How2CreateFlight {
  Normal = 'normal',
  Copy = 'copy',
  Sub = 'sub',
  Template = 'template',
}

export interface How2CreateFlightParams {
  how: How2CreateFlight;
  isGrayAndRelease?: boolean;
  flightUrl?: string;
  isRelease?: boolean;
  copy?: { flightId: number; isCN: boolean };
}

/**
 * 创建实验-基础设置-配置参数
 */
export interface FlightBasicParams {
  libraAppid: number;
  name?: string;
  productLayers?: ProductLayer[];
  layerOrigin?: Value;
  only100Percentage?: boolean;
  effectedRegions?: string[];
  versionResourceOrigin?: string | number;
  bookVersionResourceOrigin?: string | number;
  paramFilters?: {
    libraAppid?: number;
    manageType?: ManageType;
    productID?: number;
    data?: ParamFilters[];
  };

  filters?: Filter[][];
  filterCode?: string;
}

/**
 * 创建实验-高级设置-配置参数
 */
export interface FlightAdvancedParams {
  owners?: User[];
  description?: string;
  expectation?: string;
  duration?: number;
  is_long_time_flight?: boolean;
  enable_gradual?: boolean;
  gradual_traffic_duration?: number;
  psm?: string[];
  ad_flight_hit_rules?: unknown[];
}

export interface LibraCreateTemplate {
  name: string;
  libraAppId: number;
  /**
   * 实验类型
   */
  manageType: ManageType;
  grayOrRelease: GrayOrRelease;
  business?: Business;
  businessName?: string;
  owners: User[];
  basicParams?: FlightBasicParams;
  /**
   * 指定PSM
   */
  psm?: string[];
  creatorEmail: string;
}

export enum GrayOrRelease {
  /**
   * 灰度
   */
  gray = 'gray',

  /**
   * 正式
   */
  release = 'release',
  /**
   * 灰度+正式
   */
  grayAndRelease = 'grayAndRelease',
}

export const FeatureType: {
  featureType: number;
  templateIds: number[];
}[] = [
  // 线上问题修复
  { featureType: 13, templateIds: [357652] },
  // 业务需求
  { featureType: 12, templateIds: [248986, 267727] },
  // 技术需求
  { featureType: 8, templateIds: [268036, 65620, 52564, 178261, 119851, 302004] },
];

export interface MeegoInfo {
  name: string;
  id: number;
  project_key: string;
  simple_name: string;
  business?: Business;
  libraAppId?: number[];
  templateId?: number;
  planingVersions?: number[];
  realVersions?: number[];
  onlineVersions?: OnlineVersion[];
}

export const SupportedApps: {
  label: string;
  fieldValue: string;
  libraAppid: number;
}[] = [
  { label: 'App-剪映', fieldValue: 'e0ecrfhkb', libraAppid: 147 },
  { label: 'App-CapCut', fieldValue: 'huzrzdapo', libraAppid: 305 },
];

export const getMeegoInfo = (info: WorkItemInfo): MeegoInfo => {
  const businessValue = info?.fields?.find(f => f.field_key === 'business')?.field_value;
  const supportedAppsValue = info?.fields?.find(f => f.field_key === 'supported_apps')?.field_value;
  const templateId = info?.fields?.find(f => f.field_key === 'template')?.field_value;
  const planingVersions = info?.fields?.find(f => f.field_key === 'planning_version')?.field_value;
  const realVersions = info?.fields?.find(f => f.field_key === 'field_1f5a50')?.field_value;

  return {
    name: info.name,
    id: info.id,
    project_key: info.project_key,
    simple_name: info.simple_name,
    business: businessValue !== undefined ? queryBussinessByID(businessValue as string) : undefined,
    libraAppId:
      supportedAppsValue !== undefined
        ? [...new Set((supportedAppsValue as { value: string }[]).map(item => item.value))]
            .filter(v => SupportedApps.find(s => s.fieldValue === v))
            .map(v => SupportedApps.find(s => s.fieldValue === v))
            ?.map(s => s?.libraAppid ?? 0)
        : undefined,
    templateId: templateId !== undefined ? (templateId as unknown as { id: number }).id : undefined,
    planingVersions: planingVersions !== undefined ? (planingVersions as number[]) : undefined,
    realVersions: realVersions !== undefined ? (realVersions as number[]) : undefined,
  };
};

export interface FlightCategoryParams {
  /**
   * Libra定义的App ID
   */
  libraAppid?: number[];

  meegoInfo?: MeegoInfo;

  /**
   * 实验类型
   */
  manageType?: ManageType;

  /**
   * 实验分配：灰度、正式
   */
  grayOrRelease?: GrayOrRelease;

  /**
   * 是否重开
   */
  isReopen?: boolean;

  /**
   * 重开原因
   */
  reopenReason?: string[];

  /**
   * 数据回收提醒频率(单位:天)
   */
  dataRecoveryRemind?: number[];

  /**
   * 是否跳过业务检测
   */
  skipBusinessCheck?: boolean;
}

export interface SettingsKey {
  type: string;
  key?: string;
  query?: string;
  ab?: {
    libraAppid: number;
    abItems: ABItem[];
  }[];
}

export interface SettingsValue {
  index?: number;
  desc?: string;
  value?: string;
}

export interface FlightGroupParams {
  index: number;
  name?: string;
  type?: number;
  description?: string;

  settingsValues?: SettingsValue[];

  config?: string;
}

export const isSettingFlight = (manageType?: ManageType) =>
  manageType !== undefined ? [ManageType.ABClientSDK, ManageType.SettingsClientSDK].includes(manageType) : false;
interface ProcessFilterRulesOptions {
  filterRules?: {
    logic: string;
    conditions: {
      logic: string;
      condition: {
        key: string;
        op: string;
        value: boolean | number | number[] | string | string[];
        type: string;
        transformer?: string;
        hash_suffix?: string;
        mod_value?: string | number;
      };
    }[];
  }[];
}
export const getNormalizedValueString = (value: any): string => {
  const normalizeValue = (v: any) => {
    if (typeof v === 'string') {
      return v.toLowerCase();
    } else if (v && typeof v === 'object' && 'name' in v) {
      return v.name.toLowerCase();
    }
    return v;
  };

  return Array.isArray(value) ? value.map(normalizeValue).join(',') : normalizeValue(value);
};

export const processFilterRules = (options: ProcessFilterRulesOptions, isFilterGrayToRelease: boolean): Filter[][] => {
  const { filterRules } = options;
  if (!filterRules) {
    return [];
  }
  if (isFilterGrayToRelease) {
    return filterRules.map(ruleOr =>
      ruleOr.conditions
        .filter(ruleAnd => {
          const key = ruleAnd.condition.key.toLowerCase();
          const { value } = ruleAnd.condition;
          const valueStr = getNormalizedValueString(value);

          const hasGreyChannel = key === 'channel' && valueStr.includes('grey_channel');
          const hasAndroidPlatform = ruleOr.conditions.some(rule_and => {
            const platformKey = rule_and.condition.key.toLowerCase();
            const platformValue = rule_and.condition.value;
            const platformValueStr = getNormalizedValueString(platformValue);
            return platformKey === 'device_platform' && platformValueStr.includes('android');
          });
          const hasVersionCodeLess = key.includes('version_code') && ['<', '<='].includes(ruleAnd.condition.op);

          return !(hasGreyChannel || (hasAndroidPlatform && hasVersionCodeLess));
        })
        .map(ruleAnd => {
          const { condition } = ruleAnd;
          const { value } = ruleAnd.condition;
          const valueStr = getNormalizedValueString(value);
          const isAppStoreChannel =
            condition.key === 'channel' && (valueStr.includes('appstore') || valueStr.includes('app store'));

          let { op } = condition;
          if (isAppStoreChannel && (op === 'not in' || op === '!=')) {
            op = op === 'not in' ? 'in' : '==';
          }

          return {
            key: condition.key,
            op,
            value: condition.value,
            transformer: condition.transformer ?? undefined,
            useTransformer: Boolean(condition.transformer),
            modValue: condition.mod_value,
            hashSuffix: condition.hash_suffix,
            bundleId: ['in_bundle', 'not in bundle'].includes(condition.op)
              ? (condition.value as unknown as { id: number }[]).map(d => d.id)
              : undefined,
          } as Filter;
        }),
    );
  } else {
    return filterRules.map(ruleOr =>
      ruleOr.conditions.map(
        ruleAnd =>
          ({
            key: ruleAnd.condition.key,
            op: ruleAnd.condition.op,
            value: ruleAnd.condition.value,
            transformer:
              ruleAnd.condition.transformer !== undefined && ruleAnd.condition.transformer !== null
                ? ruleAnd.condition.transformer
                : undefined,
            useTransformer:
              ruleAnd.condition.transformer !== undefined &&
              ruleAnd.condition.transformer !== null &&
              ruleAnd.condition.transformer.length > 0,
            modValue: ruleAnd.condition.mod_value,
            hashSuffix: ruleAnd.condition.hash_suffix,
            bundleId: ['in_bundle', 'not in bundle'].includes(ruleAnd.condition.op)
              ? (ruleAnd.condition.value as unknown as { id: number }[]).map(d => d.id)
              : undefined,
          }) as Filter,
      ),
    );
  }
};

/**
 * 生成FilterRule
 * @param filters
 * @param paramFilters
 */
export const generateFilterRule = (filters?: Filter[][], paramFilters?: ParamFilters[]): FilterRule[] | undefined => {
  if (filters !== undefined && paramFilters !== undefined) {
    return filters
      .map(filterOr => ({
        logic: '||',
        conditions: filterOr
          .map(filterAnd => {
            const paramFilter = paramFilters.find(pf => pf.key === filterAnd.key);
            let _value: boolean | number | number[] | string | string[] | { id: number; name: string }[] | undefined;
            if (paramFilter !== undefined && filterAnd.key !== undefined && filterAnd.op !== undefined) {
              // 过滤条件Value需要根据多个维度决定
              if (['not_in_bundle', 'in_bundle'].includes(filterAnd.op)) {
                _value = paramFilter.bundles
                  .filter(b => filterAnd.bundleId?.includes(b.id) === true)
                  .map(b => ({
                    id: b.id,
                    name: b.name,
                  }));
              } else {
                // int直接传string没问题，libra做了兼容
                // TODO timestamp、float、version
                _value = filterAnd.value;
              }

              return {
                logic: '&&',
                condition: {
                  key: filterAnd.key,
                  op: filterAnd.op,
                  value: _value,
                  mod_value: filterAnd.modValue,
                  hash_suffix: filterAnd.hashSuffix,
                  type: paramFilter.type,
                  custom_filter: false, // TODO, custom_filter这个值什么情况下是true
                  transformer: filterAnd.transformer,
                  source: paramFilter.source,
                  property_type: paramFilter.property_type,
                },
              };
            }
            return undefined;
          })
          .filter(c => c !== undefined),
      }))
      .filter(c => c !== undefined && c.conditions.length > 0);
  }
  return undefined;
};

/**
 * 生成SettingsDynamicObjects
 * @param paramsArray
 * @param settingsKeys
 *
 */
export const generateSettingsDynamicObjects = (paramsArray: FlightGroupParams[], settingsKeys: SettingsKey[]) => {
  // 过滤出字段非空的
  const settingsKeysNonNull = settingsKeys
    .filter(settingsKey => settingsKey.key !== undefined)
    .map(settingsKey => settingsKey as Required<SettingsKey>);

  return paramsArray.map(p => {
    const dynamicObject: DynamicObject = {};
    try {
      settingsKeysNonNull.forEach((k, i) => {
        const settingsValue = p.settingsValues?.find(v => v.index === i);
        if (settingsValue === undefined || settingsValue.value === undefined) {
          // Do nothing.
        } else if (k.type === 'string') {
          dynamicObject[k.key] = settingsValue.value;
        } else if (k.type === 'boolean') {
          dynamicObject[k.key] = settingsValue.value === 'true';
        } else if (k.type === 'number') {
          dynamicObject[k.key] = Number(settingsValue.value);
        } else if (k.type === 'object') {
          dynamicObject[k.key] = JSON.parse(settingsValue.value) as DynamicObject;
        } else if (k.type === 'array') {
          dynamicObject[k.key] = JSON.parse(settingsValue.value) as DynamicObject[];
        }
      });
    } catch (e) {
      console.error(e);
      return {} as DynamicObject;
    }
    return dynamicObject;
  });
};

/**
 * 获取选择的流量层
 * @param params
 */
export const getLayerSelected = (params: FlightBasicParams) => {
  const id = params?.layerOrigin
    ?.toString()
    ?.split(',')
    ?.map(s => parseInt(s, 10))?.[2];
  if (id) {
    return params?.productLayers?.flatMap(g => g.layers).find(l => l?.id === id);
  }

  return undefined;
};

export const versionVersionOriginToDecimal = (percent: string | number | undefined) => {
  if (!percent) {
    return undefined;
  }

  const numericValue = parseFloat(percent.toString());
  if (isNaN(numericValue)) {
    return undefined;
  }

  return parseFloat((numericValue / 100).toFixed(3));
};

export const maxPercentVersionResource = (params: FlightBasicParams) =>
  (getLayerSelected(params)?.available_traffic ?? 0) * 100;

export const maxPercentBookVersionResource = (params: FlightBasicParams) => {
  const r = (getLayerSelected(params)?.available_traffic ?? 1) * 100 - Number(params.versionResourceOrigin ?? '100');
  if (r >= 0) {
    return r;
  } else {
    return 0;
  }
};

export enum Type {
  /**
   * 代表创建实验成功
   */
  Success = 'success',

  /**
   * 提示但不阻塞
   */
  Warning = 'warning',

  /**
   * 代表检查项目有提示问题的
   */
  Deny = 'deny',

  /**
   * 代表阻塞创建或者实验创建失败的
   */
  Error = 'error',
}

/**
 * 具体一个事务的标识符
 */
export enum Identifier {
  IdentifierDefault = 'default',
  IdentifierLibraAppId = 'libraAppId',
  IdentifierBindMeego = 'bindMeego',
  IdentifierManageType = 'manageType',
  IdentifierReopenReason = 'reopenReason',
  IdentifierDataRecoveryRemind = 'dataRecoveryRemind',
  IdentifierOwner = 'owner',
  IdentifierDescription = 'description',
  IdentifierExpectation = 'expectation',
  IdentifierDuration = 'duration',
  IdentifierGradualTrafficDuration = 'gradualTrafficDuration',
  IdentifierName = 'name',
  IdentifierLayerOrigin = 'layerOrigin',
  IdenrifierVersionResourceOrigin = 'versionResourceOrigin',
  IdenrifierSettingsKey = 'settingsKey',
  IdentifierSettingValue = 'settingValue',
  IdentifierCubaCheckResult = 'cubaCheckResult',
  IdentifierThirdGrayCheck = 'thirdGrayCheck',
  IdentifierFullReleaseCheck = 'fullReleaseCheck',
}

export interface Transaction {
  type: Type;
  libraAppID?: number; // 非空则代表具体App相关，空则代表公共的
  identifier: Identifier;
  message: string;
  raw?: unknown;
  consumed?: boolean;
}

export interface CubaCheckParams {
  story_id: string;
  ab_name: string;
  business: string;
  flow_total: number;
  flow_reserved: number;
  resource_divide_type: string;
  platform: string;
  client_start_version: string;
  ab_duration: number;
  countries: string;
  layer_priority: number;
  libra_app_id: number;
}

export const groupByLibraAppId = (transactions: Transaction[]) =>
  Object.values(
    transactions.reduce(
      (acc, item) => {
        if (!acc[item.libraAppID ?? 0]) {
          acc[item.libraAppID ?? 0] = [];
        }
        acc[item.libraAppID ?? 0].push(item);
        return acc;
      },
      {} as Record<string, Transaction[]>,
    ),
  );

export interface CreateFlightParams {
  tokens: Token[];
  categoryParams: FlightCategoryParams;
  basicParamsArray: FlightBasicParams[];
  advancedParams: FlightAdvancedParams;
  flightGroupParams: FlightGroupParams[];
  settingsKeys: SettingsKey[];
  settingsDynamicObjects: DynamicObject[];
  onlyVerification?: boolean;
  skipVerification?: boolean;
  skipControl?: boolean;
  currentUserEmail?: string;
}

export abstract class AbstractBusinessChecker {
  protected abstract businessIds?: string[];
  protected abstract libraAppIds?: number[];
  protected abstract manageTypes?: ManageType[];

  protected abstract doCheck(
    transactions: Transaction[],
    libraCreateParams: LibraCreateParams,
    params: CreateFlightParams,
    business?: Business,
  ): void;

  check(
    transactions: Transaction[],
    libraCreateParams: LibraCreateParams,
    params: CreateFlightParams,
    business?: Business,
  ) {
    const { businessIds, manageTypes, libraAppIds } = this;

    while (business?.child && business.child.length > 0) {
      business = business.child[0];
    }

    if (
      (businessIds === undefined || businessIds.includes(business?.id ?? '')) &&
      (manageTypes === undefined || manageTypes.includes(libraCreateParams.manage_type)) &&
      (libraAppIds === undefined || libraAppIds.includes(libraCreateParams.app_id))
    ) {
      this.doCheck(transactions, libraCreateParams, params, business);
    }
  }
}

export abstract class AbstractCommercialChecker extends AbstractBusinessChecker {}

const businessChecker: AbstractBusinessChecker[] = [
  /**
   * 服务端海外机房默认信息sg+va
   */
  new (class extends AbstractCommercialChecker {
    protected businessIds = [BUSINESS_ID_COMMERCIAL_SUBSCRIPTION, BUSINESS_ID_COMMERCIAL_AD];
    protected libraAppIds = [LIBRA_APP_ID_CAPCUT];
    protected manageTypes = [ManageType.Server, ManageType.product, ManageType.SettingsClientSDK];

    protected doCheck(
      transactions: Transaction[],
      libraCreateParams: LibraCreateParams,
      params: CreateFlightParams,
      business?: Business,
    ): void {
      if (
        libraCreateParams.effected_regions !== undefined &&
        (!libraCreateParams.effected_regions?.includes('SG') || !libraCreateParams.effected_regions?.includes('VA'))
      ) {
        transactions.push({
          type: Type.Deny,
          identifier: Identifier.IdentifierDefault,
          libraAppID: libraCreateParams.app_id,
          message: '广告/订阅: 海外生效机房默认需要开 sg + va机房（所有机房）',
        });
      }
    }
  })(),
  /**
   * 有且只能有iphone/ios,不能选ipad
   */
  new (class extends AbstractCommercialChecker {
    protected businessIds = [BUSINESS_ID_COMMERCIAL_AD];
    protected libraAppIds = [LIBRA_APP_ID_LV, LIBRA_APP_ID_CAPCUT, LIBRA_APP_ID_XINGTU];
    protected manageTypes = [
      ManageType.Server,
      ManageType.product,
      ManageType.SettingsClientSDK,
      ManageType.ABLocalClient,
      ManageType.SettingsClientNormal,
    ];

    protected doCheck(
      transactions: Transaction[],
      libraCreateParams: LibraCreateParams,
      params: CreateFlightParams,
      business?: Business,
    ): void {
      libraCreateParams.filter_rule?.forEach(rule => {
        rule.conditions
          .filter(condition => condition.condition.key === 'device_platform')
          .forEach(condition => {
            const v = condition.condition.value?.toString().toLowerCase() ?? '';
            if (['ios', 'iphone', 'ipad'].some(sub => v.includes(sub))) {
              if (v.includes('ipad') || (!v.includes('ios') && !v.includes('iphone'))) {
                transactions.push({
                  type: Type.Deny,
                  identifier: Identifier.IdentifierDefault,
                  libraAppID: libraCreateParams.app_id,
                  message: '广告: 有且只能有iphone/ios,不能选ipad',
                });
              }
            }
          });
      });
    }
  })(),
  /**
   * 服务端psm非空
   */
  new (class extends AbstractCommercialChecker {
    protected businessIds = [BUSINESS_ID_COMMERCIAL_AD, BUSINESS_ID_COMMERCIAL_SUBSCRIPTION];
    protected libraAppIds = [LIBRA_APP_ID_LV, LIBRA_APP_ID_CAPCUT, LIBRA_APP_ID_XINGTU];
    protected manageTypes = [ManageType.Server];

    protected doCheck(
      transactions: Transaction[],
      libraCreateParams: LibraCreateParams,
      params: CreateFlightParams,
      business?: Business,
    ): void {
      if (libraCreateParams.specified_psms === undefined || libraCreateParams.specified_psms.length <= 0) {
        transactions.push({
          type: Type.Deny,
          identifier: Identifier.IdentifierDefault,
          libraAppID: libraCreateParams.app_id,
          message: '广告/订阅: 生效PSM一定不为空',
        });
      }
    }
  })(),
  /**
   * 国内广告PSM必填的三个
   */
  new (class extends AbstractCommercialChecker {
    protected businessIds = [BUSINESS_ID_COMMERCIAL_AD];
    protected libraAppIds = [LIBRA_APP_ID_LV, LIBRA_APP_ID_XINGTU];
    protected manageTypes = [ManageType.Server];

    protected doCheck(
      transactions: Transaction[],
      libraCreateParams: LibraCreateParams,
      params: CreateFlightParams,
      business?: Business,
    ): void {
      if (
        libraCreateParams.specified_psms === undefined ||
        libraCreateParams.specified_psms.length < 3 ||
        !['faceu.commerce.ad_api', 'faceu.feed.api', 'ad.pack.go_splash_api'].every(s =>
          libraCreateParams?.specified_psms?.includes(s),
        )
      ) {
        transactions.push({
          type: Type.Deny,
          identifier: Identifier.IdentifierDefault,
          libraAppID: libraCreateParams.app_id,
          message:
            '广告: 必须同时包含以下PSM，可有额外psm: faceu.commerce.ad_api(独立广告位相册、导出的流量入口) | faceu.feed.api(模版内外流广告流量入口) | ad.pack.go_splash_api(站内开屏广告流量入口)',
        });
      }
    }
  })(),
  /**
   * 对广告影响
   */
  new (class extends AbstractCommercialChecker {
    protected businessIds = [BUSINESS_ID_COMMERCIAL_AD];
    protected libraAppIds = [LIBRA_APP_ID_CAPCUT, LIBRA_APP_ID_LV];
    protected manageTypes = [
      ManageType.Server,
      ManageType.product,
      ManageType.SettingsClientSDK,
      ManageType.ABLocalClient,
      ManageType.SettingsClientNormal,
    ];

    protected doCheck(
      transactions: Transaction[],
      libraCreateParams: LibraCreateParams,
      params: CreateFlightParams,
      business?: Business,
    ): void {
      if (libraCreateParams.ad_flight_hit_rules === undefined) {
        transactions.push({
          type: Type.Deny,
          identifier: Identifier.IdentifierDefault,
          libraAppID: libraCreateParams.app_id,
          message: '广告: 广告报告，开启实验时必须选择',
        });
      }
    }
  })(),
  /**
   * 海外广告必填一个
   */
  new (class extends AbstractCommercialChecker {
    protected businessIds = [BUSINESS_ID_COMMERCIAL_AD];
    protected libraAppIds = [LIBRA_APP_ID_CAPCUT];
    protected manageTypes = [ManageType.Server];

    protected doCheck(
      transactions: Transaction[],
      libraCreateParams: LibraCreateParams,
      params: CreateFlightParams,
      business?: Business,
    ): void {
      if (
        libraCreateParams.specified_psms === undefined ||
        libraCreateParams.specified_psms.length < 1 ||
        !['faceu.strategy.api'].every(s => libraCreateParams?.specified_psms?.includes(s))
      ) {
        transactions.push({
          type: Type.Deny,
          identifier: Identifier.IdentifierDefault,
          libraAppID: libraCreateParams.app_id,
          message: '广告: 必须包含以下PSM faceu.strategy.api，可以有额外的psm',
        });
      }
    }
  })(),
  /**
   * app_id必填,且只能是1775/3006/2515
   */
  new (class extends AbstractCommercialChecker {
    protected businessIds = [BUSINESS_ID_COMMERCIAL_AD, BUSINESS_ID_COMMERCIAL_SUBSCRIPTION];
    protected libraAppIds = [LIBRA_APP_ID_CAPCUT, LIBRA_APP_ID_LV, LIBRA_APP_ID_XINGTU];
    protected manageTypes = [ManageType.Server];

    protected doCheck(
      transactions: Transaction[],
      libraCreateParams: LibraCreateParams,
      params: CreateFlightParams,
      business?: Business,
    ): void {
      const appId = getAppInfo(libraCreateParams.app_id)?.appId ?? 0;
      const rr = libraCreateParams?.filter_rule?.filter(r => {
        const c_appid = r.conditions.find(condition => condition.condition.key === 'app_id');
        if (c_appid === undefined || !c_appid.condition.value?.toString()?.includes(appId.toString())) {
          return false;
        }

        return true;
      });

      if (rr === undefined || rr.length === 0 || rr?.length !== libraCreateParams?.filter_rule?.length) {
        transactions.push({
          type: Type.Deny,
          identifier: Identifier.IdentifierDefault,
          libraAppID: libraCreateParams.app_id,
          message: '广告/订阅: 必配过滤条件appid，剪映=1775，Capcut=3006，醒图=2515',
        });
      }
    }
  })(),
  /**
   * 实验流程层
   */
  new (class extends AbstractCommercialChecker {
    protected businessIds = [BUSINESS_ID_COMMERCIAL_AD];
    protected libraAppIds = [LIBRA_APP_ID_CAPCUT, LIBRA_APP_ID_LV, LIBRA_APP_ID_XINGTU];
    protected manageTypes = [ManageType.Server];

    protected doCheck(
      transactions: Transaction[],
      libraCreateParams: LibraCreateParams,
      params: CreateFlightParams,
      business?: Business,
    ): void {
      const basicParams = params.basicParamsArray.find(b => b.libraAppid === libraCreateParams.app_id);
      if (basicParams === undefined || getLayerSelected(basicParams)?.domain?.id === undefined) {
        transactions.push({
          type: Type.Deny,
          identifier: Identifier.IdentifierDefault,
          libraAppID: libraCreateParams.app_id,
          message: '广告: 广告实验必须开在互斥组',
        });
      }
    }
  })(),
  /**
   * 广告版本号合法性-剪C
   *
   * 禁止使用_version_code和version_code过滤条件，广告双端分别使用ios_vc和android_vc这两个参数过滤版本
   * ios_vc是个点分版本号
   * android_vc是数字类型的版本号
   */
  new (class extends AbstractCommercialChecker {
    protected businessIds = [BUSINESS_ID_COMMERCIAL_AD];
    protected libraAppIds = [LIBRA_APP_ID_CAPCUT, LIBRA_APP_ID_LV];
    protected manageTypes = [ManageType.Server];

    protected doCheck(
      transactions: Transaction[],
      libraCreateParams: LibraCreateParams,
      params: CreateFlightParams,
      business?: Business,
    ): void {
      const e = libraCreateParams.filter_rule?.find(
        r => r.conditions.find(c => Boolean(['_version_code', 'version_code'].includes(c.condition.key))) !== undefined,
      );
      if (e !== undefined) {
        transactions.push({
          type: Type.Deny,
          identifier: Identifier.IdentifierDefault,
          libraAppID: libraCreateParams.app_id,
          message:
            '广告: 禁止使用_version_code和version_code过滤条件，广告双端分别使用ios_vc和android_vc这两个参数过滤版本',
        });
      }

      const verifyAndroidVersion = (appId: number, version: string): boolean => {
        switch (appId) {
          case LIBRA_APP_ID_LV:
            return version.split(',').find(v => v.length !== 9) === undefined;
          case LIBRA_APP_ID_CAPCUT:
            return version.split(',').find(v => v.length !== 8) === undefined;
          case LIBRA_APP_ID_XINGTU:
            return version.split(',').find(v => v.length !== 6) === undefined;
          default:
            return false;
        }
      };

      const verifyiOSVersion = (appId: number, version: string): boolean => {
        switch (appId) {
          case LIBRA_APP_ID_LV:
          case LIBRA_APP_ID_CAPCUT:
          case LIBRA_APP_ID_XINGTU:
            return version.split(',').find(v => v.split('.').length !== 4) === undefined;
          default:
            return false;
        }
      };

      libraCreateParams.filter_rule?.forEach(rule => {
        rule.conditions.forEach(c => {
          if (c.condition.key === 'android_vc') {
            if (!verifyAndroidVersion(libraCreateParams.app_id, c.condition.value?.toString() ?? '')) {
              transactions.push({
                type: Type.Deny,
                identifier: Identifier.IdentifierDefault,
                libraAppID: libraCreateParams.app_id,
                message: `广告: android_vc: ${c.condition.value?.toString()}配置有误`,
              });
            }
          }
        });
      });

      libraCreateParams.filter_rule?.forEach(rule => {
        rule.conditions.forEach(c => {
          if (c.condition.key === 'ios_vc') {
            if (!verifyiOSVersion(libraCreateParams.app_id, c.condition.value?.toString() ?? '')) {
              transactions.push({
                type: Type.Deny,
                identifier: Identifier.IdentifierDefault,
                libraAppID: libraCreateParams.app_id,
                message: `广告: ios_vc: ${c.condition.value?.toString()}配置有误`,
              });
            }
          }
        });
      });
    }
  })(),
  /**
   * 客户端版本号检测
   */
  new (class extends AbstractCommercialChecker {
    protected businessIds = undefined;
    protected libraAppIds = [LIBRA_APP_ID_CAPCUT, LIBRA_APP_ID_LV, LIBRA_APP_ID_XINGTU];
    protected manageTypes = [ManageType.SettingsClientSDK];

    protected doCheck(
      transactions: Transaction[],
      libraCreateParams: LibraCreateParams,
      params: CreateFlightParams,
      business?: Business,
    ): void {
      const verifyAndroidVersion = (appId: number, version: string): boolean => {
        switch (appId) {
          case LIBRA_APP_ID_LV:
            return version.split(',').find(v => v.length !== 9) === undefined;
          case LIBRA_APP_ID_CAPCUT:
            return version.split(',').find(v => v.length !== 8) === undefined;
          case LIBRA_APP_ID_XINGTU:
            return version.split(',').find(v => v.length !== 6) === undefined;
          default:
            return false;
        }
      };

      const verifyiOSVersion = (appId: number, version: string): boolean => {
        switch (appId) {
          case LIBRA_APP_ID_LV:
            return version.split(',').find(v => v.length !== 8) === undefined;
          case LIBRA_APP_ID_CAPCUT:
            return version.split(',').find(v => v.length !== 8) === undefined;
          case LIBRA_APP_ID_XINGTU:
            return version.split(',').find(v => v.length !== 6) === undefined;
          default:
            return false;
        }
      };

      libraCreateParams.filter_rule
        ?.filter(
          rule =>
            rule.conditions.find(
              c =>
                c.condition.key === 'device_platform' &&
                c.condition.value?.toString()?.toLowerCase()?.includes('android') === true,
            ) !== undefined,
        )
        ?.forEach(rule => {
          rule.conditions.forEach(c => {
            if (['version_code', '_version_code'].includes(c.condition.key)) {
              if (!verifyAndroidVersion(libraCreateParams.app_id, c.condition.value?.toString() ?? '')) {
                transactions.push({
                  type: Type.Deny,
                  identifier: Identifier.IdentifierDefault,
                  libraAppID: libraCreateParams.app_id,
                  message: `${c.condition.key}: ${c.condition.value?.toString()}配置有误`,
                });
              }
            }
          });
        });

      libraCreateParams.filter_rule
        ?.filter(
          rule =>
            rule.conditions.find(
              c =>
                c.condition.key === 'device_platform' &&
                c.condition.value?.toString()?.toLowerCase()?.includes('ios') === true,
            ) !== undefined,
        )
        ?.forEach(rule => {
          rule.conditions.forEach(c => {
            if (['version_code', '_version_code'].includes(c.condition.key)) {
              if (!verifyiOSVersion(libraCreateParams.app_id, c.condition.value?.toString() ?? '')) {
                transactions.push({
                  type: Type.Deny,
                  identifier: Identifier.IdentifierDefault,
                  libraAppID: libraCreateParams.app_id,
                  message: `${c.condition.key}: ${c.condition.value?.toString()}配置有误`,
                });
              }
            }
          });
        });
    }
  })(),
];

const businessCheck = (
  transactions: Transaction[],
  libraCreateParamses: LibraCreateParams[],
  params: CreateFlightParams,
  business?: Business,
) => {
  libraCreateParamses.forEach(libraCreateParamse => {
    businessChecker.forEach(checker => {
      checker.check(transactions, libraCreateParamse, params, business);
    });
  });
};

const cubaCheck = async (
  transactions: Transaction[],
  libraCreateParamses: LibraCreateParams[],
  params: CreateFlightParams,
  business?: Business,
) => {
  for (const libraCreateParams of libraCreateParamses) {
    const getAndroidStartVersion = (appId: number, version: string): string => {
      switch (appId) {
        case LIBRA_APP_ID_LV:
        case LIBRA_APP_ID_CAPCUT:
        case LIBRA_APP_ID_XINGTU:
          // 取数字前四位，然后用.拼接
          const all = version.split(',');
          if (all.length > 0) {
            const last = all[all.length - 1];
            // 取数字前四位，然后用.拼接, 1300->13.0.0
            return last.substring(0, 4).replace(/^(\d{2})(\d)(\d)$/, '$1.$2.$3');
          } else {
            return '';
          }
        default:
          return '';
      }
    };

    const getiOSStartVersion = (appId: number, version: string): string => {
      switch (appId) {
        case LIBRA_APP_ID_LV:
        case LIBRA_APP_ID_CAPCUT:
        case LIBRA_APP_ID_XINGTU:
          const all = version.split(',');
          if (all.length > 0) {
            const last = all[all.length - 1];
            // 按照.分割，只取分割后的三位
            const parts = last.split('.');
            if (parts.length >= 3) {
              return parts.slice(0, 3).join('.');
            } else {
              return '';
            }
          } else {
            return '';
          }
        default:
          return '';
      }
    };

    let iosStartVersion = '';
    let androidStartVersion = '';

    libraCreateParams.filter_rule?.forEach(rule => {
      rule.conditions.forEach(c => {
        if (c.condition.key === 'android_vc') {
          if (c.condition.op === '>=' || c.condition.op === '==') {
            androidStartVersion = getAndroidStartVersion(libraCreateParams.app_id, c.condition.value?.toString() ?? '');
          }
        }
      });
    });

    libraCreateParams.filter_rule?.forEach(rule => {
      rule.conditions.forEach(c => {
        if (c.condition.key === 'ios_vc') {
          if (c.condition.op === '>=' || c.condition.op === '=') {
            iosStartVersion = getiOSStartVersion(libraCreateParams.app_id, c.condition.value?.toString() ?? '');
          }
        }
      });
    });
    const divideType = libraCreateParams.layer_info.hash_strategy.includes('did') ? 'did' : 'uid';
    let platform = '';
    let tempPlatform = '';
    const allPlatform = ['ios', 'android'];
    libraCreateParams.filter_rule?.forEach(rule => {
      rule.conditions.forEach(c => {
        if (c.condition.key === 'device_platform') {
          if (c.condition.op === '==' && c.condition.value && Array.isArray(c.condition.value)) {
            const temp = c.condition.value
              .map(value => {
                if (typeof value === 'string') {
                  return value.toLowerCase();
                }
              })
              .join(',');
            if (tempPlatform === '') {
              tempPlatform = temp;
            } else {
              tempPlatform += `,${temp}`;
            }
          } else if (c.condition.op === '!=' && c.condition.value && Array.isArray(c.condition.value)) {
            if ((c.condition.value as any[]).every(item => typeof item === 'string')) {
              tempPlatform += allPlatform.filter(value => !(c.condition.value as string[]).includes(value)).join(',');
            }
          }
        }
      });
    });
    if (tempPlatform.includes('ios') && tempPlatform.includes('android')) {
      platform = 'android,ios';
    } else if (tempPlatform.includes('ios')) {
      platform = 'ios';
    } else if (tempPlatform.includes('android')) {
      platform = 'android';
    }
    const startVersion = platform.includes('android') ? androidStartVersion : iosStartVersion;
    let businessName = 'commerce_ad';
    let tempBusiness = business;
    if (tempBusiness !== undefined) {
      while (tempBusiness.child !== undefined && tempBusiness.child.length === 1) {
        tempBusiness = tempBusiness.child[0];
      }

      // 商业化-广告 默认打开对广告有影响
      if (tempBusiness.id === BUSINESS_ID_COMMERCIAL_SUBSCRIPTION) {
        businessName = 'commerce_subscription';
      }
    }
    const flightBasicParam = params.basicParamsArray.find(b => b.libraAppid === libraCreateParams.app_id);
    let priority = 0;
    if (flightBasicParam !== undefined) {
      priority = getLayerSelected(flightBasicParam)?.priority ?? 0;
    }
    const result = await check_cuba({
      data: {
        story_id: libraCreateParams.meego_info?.meego_array[0].meego_story ?? '',
        ab_name: libraCreateParams.name,
        business: businessName,
        flow_total: libraCreateParams.version_resource,
        flow_reserved: libraCreateParams.book_version_resource ?? 0,
        resource_divide_type: divideType,
        platform,
        client_start_version: startVersion,
        ab_duration: libraCreateParams.duration / (24 * 60 * 60),
        countries: libraCreateParams.effected_regions?.join(',') ?? '',
        layer_priority: priority,
        libra_app_id: libraCreateParams.app_id,
      },
    });
    if (result) {
      if (result.code < 0) {
        transactions.push({
          type: Type.Error,
          identifier: Identifier.IdentifierCubaCheckResult,
          libraAppID: libraCreateParams.app_id,
          message: JSON.stringify(result),
        });
      }
    }
  }
};

export const checkAfterThirdGray = async (version: string, platform: LibraPlatform) => {
  const appId = platform === LibraPlatform.android ? 177502 : 177501;
  const isAfter = await isThirdGray({
    data: {
      appId,
      version,
    },
  });
  return isAfter;
};

const thirdGrayCheck = async (
  transactions: Transaction[],
  libraCreateParamses: (LibraCreateParams | undefined)[],
  params: CreateFlightParams,
  business?: Business | undefined,
) => {
  const { meegoInfo } = params.categoryParams;
  if (meegoInfo !== undefined) {
    const hasExempt = await hasExempted({
      data: {
        meegoId: meegoInfo.id,
        owner_email: params.currentUserEmail ?? '',
      },
    });
    if (hasExempt.code === 0) {
      return;
    }
    if (!meegoInfo.onlineVersions) {
      return;
    }
    for (const version of meegoInfo.onlineVersions) {
      const lvVersion = version.appid === 1775 ? version.version : cc2lvVersion(version.version);
      const ret = await checkAfterThirdGray(
        lvVersion,
        version.platform === 'android' ? LibraPlatform.android : LibraPlatform.iOS,
      );
      if (ret) {
        transactions.push({
          type: Type.Deny,
          identifier: Identifier.IdentifierThirdGrayCheck,
          libraAppID: 147,
          message: `三灰之后不允许开新灰度实验（当前版本${lvVersion}）`,
          raw: lvVersion,
        });
      }
    }
  }
};

const fullReleaseCheck = async (
  transactions: Transaction[],
  libraCreateParamses: (LibraCreateParams | undefined)[],
  params: CreateFlightParams,
  business?: Business | undefined,
) => {
  const { meegoInfo } = params.categoryParams;
  if (meegoInfo !== undefined) {
    const hasExempt = await hasExempted({
      data: {
        meegoId: meegoInfo.id,
        owner_email: params.currentUserEmail ?? '',
      },
    });
    if (hasExempt.code === 0) {
      return;
    }
    if (!meegoInfo.onlineVersions) {
      return;
    }
    for (const version of meegoInfo.onlineVersions) {
      let appId = AppSettingId.LV_IOS;
      if (version.appid === 1775 && version.platform === 'android') {
        appId = AppSettingId.LV_ANDROID;
      } else if (version.appid === 3006 && version.platform === 'android') {
        appId = AppSettingId.CC_ANDROID;
      } else if (version.appid === 3006 && version.platform === 'ios') {
        appId = AppSettingId.CC_IOS;
      } else {
        continue;
      }
      const isFullReleased = await isFullRelease({
        data: {
          appId,
          version: version.version,
        },
      });
      if (!isFullReleased) {
        continue;
      }
      // 根据meego单查找已有的实验，如果没有灰度实验，不允许开正式实验
      let hasGray = false;
      const query = {
        'meegoInfo.id': meegoInfo.id,
      };
      const res = await getFlightList({
        data: {
          args: query,
        },
      });
      const flightList = res.data;
      if (!flightList || flightList.length === 0) {
        continue;
      }
      for (const flight of flightList) {
        if (!flight.flightInfo.type) {
          continue;
        }
        if (flight.flightInfo.type === LibraFlightType.Gray) {
          hasGray = true;
          return;
        }
      }
      if (!hasGray) {
        transactions.push({
          type: Type.Deny,
          identifier: Identifier.IdentifierFullReleaseCheck,
          libraAppID: 147,
          message: `该实验关联的meego单 ${meegoInfo.name} 没有灰度实验，不允许开正式实验`,
          raw: version.version,
        });
        return;
      }
      return;
    }
  }
};

export const createFlightV1 = async (
  paramsArrary: CreateFlightParams[],
  isGrayAndRelease: boolean,
): Promise<Transaction[]> => {
  const transactions: Transaction[] = [];
  const { tokens } = paramsArrary[0];
  const experiments: any[] = [];
  let idx = 0;
  for (const params of paramsArrary) {
    if (idx === 1 && isGrayAndRelease === false) {
      break;
    }

    const { onlyVerification, skipVerification, skipControl } = params;
    // 实验分类参数校验
    const { libraAppid, meegoInfo, manageType, dataRecoveryRemind, isReopen, reopenReason, skipBusinessCheck } =
      params.categoryParams;
    if (libraAppid === undefined || libraAppid.length <= 0) {
      transactions.push({
        type: Type.Deny,
        identifier: Identifier.IdentifierLibraAppId,
        message: `${isGrayAndRelease ? (idx === 0 ? '灰度：' : '正式：') : ''}请至少选择一个App`,
      });
    }
    if (meegoInfo === undefined) {
      transactions.push({
        type: Type.Deny,
        identifier: Identifier.IdentifierBindMeego,
        message: `${isGrayAndRelease ? (idx === 0 ? '灰度：' : '正式：') : ''}请绑定需求`,
      });
    }
    if (manageType === undefined) {
      transactions.push({
        type: Type.Deny,
        identifier: Identifier.IdentifierManageType,
        message: `${isGrayAndRelease ? (idx === 0 ? '灰度：' : '正式：') : ''}请选择实验类型`,
      });
    }
    if (isReopen === true && reopenReason === undefined) {
      transactions.push({
        type: Type.Deny,
        identifier: Identifier.IdentifierReopenReason,
        message: `${isGrayAndRelease ? (idx === 0 ? '灰度：' : '正式：') : ''}请选择重开原因`,
      });
    }
    if (dataRecoveryRemind === undefined || dataRecoveryRemind.length <= 0) {
      transactions.push({
        type: Type.Deny,
        identifier: Identifier.IdentifierDataRecoveryRemind,
        message: `${isGrayAndRelease ? (idx === 0 ? '灰度：' : '正式：') : ''}请选择数据回收提醒频率`,
      });
    }

    // 实验高级设置参数校验
    const {
      owners,
      description,
      expectation,
      duration,
      is_long_time_flight,
      enable_gradual,
      gradual_traffic_duration,
      psm,
      ad_flight_hit_rules,
    } = params.advancedParams;

    if (owners === undefined || owners.length <= 0) {
      transactions.push({
        type: Type.Deny,
        identifier: Identifier.IdentifierOwner,
        message: `${isGrayAndRelease ? (idx === 0 ? '灰度：' : '正式：') : ''}请选择实验Owner`,
      });
    }
    if (description === undefined || description.length <= 0) {
      transactions.push({
        type: Type.Deny,
        identifier: Identifier.IdentifierDescription,
        message: `${isGrayAndRelease ? (idx === 0 ? '灰度：' : '正式：') : ''}请填写实验描述`,
      });
    }
    if (expectation === undefined || expectation.length <= 0) {
      transactions.push({
        type: Type.Deny,
        identifier: Identifier.IdentifierExpectation,
        message: `${isGrayAndRelease ? (idx === 0 ? '灰度：' : '正式：') : ''}请填写实验预期`,
      });
    }
    if (duration === undefined) {
      transactions.push({
        type: Type.Deny,
        identifier: Identifier.IdentifierDuration,
        message: `${isGrayAndRelease ? (idx === 0 ? '灰度：' : '正式：') : ''}请填写实验时长`,
      });
    }
    if (enable_gradual === true && gradual_traffic_duration === undefined) {
      transactions.push({
        type: Type.Deny,
        identifier: Identifier.IdentifierGradualTrafficDuration,
        message: `${isGrayAndRelease ? (idx === 0 ? '灰度：' : '正式：') : ''}请填写平滑生效时间`,
      });
    }

    params.basicParamsArray.forEach(basicParams => {
      const id = basicParams.libraAppid;
      const { name, layerOrigin, versionResourceOrigin } = basicParams;
      if (name === undefined || name.length <= 0) {
        transactions.push({
          libraAppID: id,
          type: Type.Deny,
          identifier: Identifier.IdentifierName,
          message: `${isGrayAndRelease ? (idx === 0 ? '灰度：' : '正式：') : ''}请填写实验名称`,
        });
      }

      if (layerOrigin === undefined) {
        transactions.push({
          libraAppID: id,
          type: Type.Deny,
          identifier: Identifier.IdentifierLayerOrigin,
          message: `${isGrayAndRelease ? (idx === 0 ? '灰度：' : '正式：') : ''}请选择实验层`,
        });
      }

      if (versionResourceOrigin === undefined) {
        transactions.push({
          libraAppID: id,
          type: Type.Deny,
          identifier: Identifier.IdenrifierVersionResourceOrigin,
          message: `${isGrayAndRelease ? (idx === 0 ? '灰度：' : '正式：') : ''}请填写实验流量`,
        });
      }
    });
    idx++;
    if (
      transactions.find(t => t.type === Type.Deny || t.type === Type.Error) ||
      libraAppid === undefined ||
      meegoInfo === undefined ||
      manageType === undefined ||
      owners === undefined ||
      description === undefined ||
      expectation === undefined ||
      duration === undefined
    ) {
      continue;
    }

    const { flightGroupParams, settingsDynamicObjects, settingsKeys } = params;

    const libraCreateParamses = libraAppid
      ?.map((appId): LibraCreateParams | undefined => {
        const basicParams = params.basicParamsArray.find(v => v.libraAppid === appId);
        if (basicParams === undefined) {
          transactions.push({
            libraAppID: appId,
            type: Type.Deny,
            identifier: Identifier.IdentifierDefault,
            message: '请配置实验基础设置',
          });
          return undefined;
        }

        const { name, effectedRegions } = basicParams;
        if (name === undefined) {
          return undefined;
        }

        const version_resource = versionVersionOriginToDecimal(basicParams.versionResourceOrigin);
        if (version_resource === undefined) {
          transactions.push({
            libraAppID: appId,
            type: Type.Deny,
            identifier: Identifier.IdenrifierVersionResourceOrigin,
            message: '请填写实验流量',
          });
          return undefined;
        }

        const layerSelected = getLayerSelected(basicParams);
        if (layerSelected === undefined || layerSelected.product_id === undefined) {
          transactions.push({
            libraAppID: appId,
            type: Type.Deny,
            identifier: Identifier.IdentifierLayerOrigin,
            message: '请选择实验层',
          });
          return undefined;
        }

        const _isSettingFlight = isSettingFlight(manageType);

        if (skipVerification) {
          return {
            skip_verification: skipVerification,
            manage_type: manageType,
            owners: owners.map(v => ({
              id: Number(v.employee_no ?? 10001),
              name: v?.email?.split('@')[0] ?? '',
            })),
            duration: is_long_time_flight ? 3650 * 24 * 60 * 60 : duration,
            is_long_time_flight,
            version_resource,
            book_version_resource: versionVersionOriginToDecimal(basicParams.bookVersionResourceOrigin),
            enable_gradual: enable_gradual ?? false,
            gradual_traffic_duration,
            name,
            description,
            app_id: appId,
            ad_flight_hit_rules,
            experiment_mode: 1, // TODO
            feature_type: FeatureType.find(v => v.templateIds.includes(meegoInfo.templateId ?? 0))?.featureType,
            product_id: layerSelected.product_id,
            layer_info: {
              product_id: layerSelected.product_id,
              hash_strategy: layerSelected.hash_strategy,
              layer_id: layerSelected.id,
            },
            filter_rule: generateFilterRule(basicParams.filters, basicParams.paramFilters?.data),
            versions: flightGroupParams.map((p, index) => ({
              name: p.name ?? '',
              description: p.description,
              type: p.type ?? 0,
              config: _isSettingFlight
                ? JSON.stringify(settingsDynamicObjects.length > index ? settingsDynamicObjects[index] : {})
                : p.config ?? '',
              settings_keys: _isSettingFlight
                ? settingsKeys
                    .map((settingKey, ii) => {
                      const { key } = settingKey;
                      if (key === undefined || key.length <= 0) {
                        transactions.push({
                          libraAppID: appId,
                          type: Type.Deny,
                          identifier: Identifier.IdenrifierSettingsKey,
                          message: `存在空的Settings key`,
                        });
                        return undefined;
                      }
                      let item_id = settingKey.ab
                        ?.find(v => v.libraAppid === appId)
                        ?.abItems.find(item => item.item_name === settingKey.key)?.item_id;
                      if (item_id === undefined) {
                        // transactions.push({
                        //   libraAppID: appId,
                        //   type: Type.Deny,
                        //   identifier: Identifier.IdenrifierSettingsKey,
                        //   message: `${getAppInfo(appId)?.name}不存在Settings: ${key}`,
                        // });
                        // return undefined;
                        item_id = -1;
                      }

                      let value;
                      try {
                        value = (settingsDynamicObjects.length > index ? settingsDynamicObjects[index] : {})[key];
                      } catch (e) {
                        value = undefined;
                      }
                      if (value === undefined) {
                        transactions.push({
                          libraAppID: appId,
                          type: Type.Deny,
                          identifier: Identifier.IdentifierSettingValue,
                          message: `${key}没有填value`,
                        });
                        return undefined;
                      }

                      return {
                        settings_item_id: item_id.toString(),
                        key,
                        type: settingKey.type,
                        desc: p?.settingsValues?.[ii].desc ?? '',
                        value: JSON.stringify(value),
                        prefix: 'default',
                        parseValue: {
                          enable: true,
                        },
                        item_id: item_id ?? 0,
                      };
                    })
                    .filter(v => v !== undefined)
                : undefined,
            })),
            meego_info: {
              meego_array: [
                {
                  meego_story: meegoInfo.id.toString(),
                  meego_project: meegoInfo.simple_name,
                  meego_project_key: meegoInfo.project_key,
                },
              ],
            },
            expectation,
            specified_psms: psm,
            effected_regions: effectedRegions,
          };
        }

        return {
          only_verification: onlyVerification,
          manage_type: manageType,
          owners: owners.map(v => ({
            id: Number(v.employee_no ?? 10001),
            name: v?.email?.split('@')[0] ?? '',
          })),
          duration: is_long_time_flight ? 3650 * 24 * 60 * 60 : duration,
          is_long_time_flight,
          version_resource,
          book_version_resource: versionVersionOriginToDecimal(basicParams.bookVersionResourceOrigin),
          enable_gradual: enable_gradual ?? false,
          gradual_traffic_duration,
          name,
          description,
          app_id: appId,
          ad_flight_hit_rules,
          experiment_mode: 1, // TODO
          feature_type: FeatureType.find(v => v.templateIds.includes(meegoInfo.templateId ?? 0))?.featureType,
          product_id: layerSelected.product_id,
          layer_info: {
            product_id: layerSelected.product_id,
            hash_strategy: layerSelected.hash_strategy,
            layer_id: layerSelected.id,
          },
          filter_rule: generateFilterRule(basicParams.filters, basicParams.paramFilters?.data),
          versions: flightGroupParams.map((p, index) => ({
            name: p.name ?? '',
            description: p.description,
            type: p.type ?? 0,
            config: _isSettingFlight
              ? JSON.stringify(settingsDynamicObjects.length > index ? settingsDynamicObjects[index] : {})
              : p.config ?? '',
            settings_keys: _isSettingFlight
              ? settingsKeys
                  .map((settingKey, ii) => {
                    const { key } = settingKey;
                    if (key === undefined || key.length <= 0) {
                      transactions.push({
                        libraAppID: appId,
                        type: Type.Deny,
                        identifier: Identifier.IdenrifierSettingsKey,
                        message: `存在空的Settings key`,
                      });
                      return undefined;
                    }
                    let item_id = settingKey.ab
                      ?.find(v => v.libraAppid === appId)
                      ?.abItems.find(item => item.item_name === settingKey.key)?.item_id;
                    if (item_id === undefined) {
                      // transactions.push({
                      //   libraAppID: appId,
                      //   type: Type.Deny,
                      //   identifier: Identifier.IdenrifierSettingsKey,
                      //   message: `${getAppInfo(appId)?.name}不存在Settings: ${key}`,
                      // });
                      // return undefined;
                      item_id = -1;
                    }

                    let value;
                    try {
                      value = (settingsDynamicObjects.length > index ? settingsDynamicObjects[index] : {})[key];
                    } catch (e) {
                      value = undefined;
                    }
                    if (value === undefined) {
                      transactions.push({
                        libraAppID: appId,
                        type: Type.Deny,
                        identifier: Identifier.IdentifierSettingValue,
                        message: `${key}没有填value`,
                      });
                      return undefined;
                    }

                    return {
                      settings_item_id: item_id.toString(),
                      key,
                      type: settingKey.type,
                      desc: p?.settingsValues?.[ii].desc ?? '',
                      value:
                        settingKey.type === 'string' || settingKey.type === 'number' || settingKey.type === 'boolean'
                          ? value.toString()
                          : JSON.stringify(value),
                      prefix: 'default',
                      parseValue: {
                        enable: true,
                      },
                      item_id: item_id ?? 0,
                    };
                  })
                  .filter(v => v !== undefined)
              : undefined,
          })),
          meego_info: {
            meego_array: [
              {
                meego_story: meegoInfo.id.toString(),
                meego_project: meegoInfo.simple_name,
                meego_project_key: meegoInfo.project_key,
              },
            ],
          },
          expectation,
          specified_psms: psm,
          effected_regions: effectedRegions,
        };
      })
      .filter(v => v !== undefined);
    experiments.push(libraCreateParamses?.[0]);
    // 请求接口前判断是否
    if (transactions.find(t => t.type === Type.Deny || t.type === Type.Error) !== undefined) {
      transactions;
    }

    // 业务级别参数校验
    // 根据开工是否检测
    if (skipBusinessCheck !== true) {
      businessCheck(transactions, libraCreateParamses, params, meegoInfo.business);
    }

    if (!skipVerification && libraCreateParamses && !skipControl) {
      // 三灰校验
      await thirdGrayCheck(transactions, libraCreateParamses, params, meegoInfo.business);
      await fullReleaseCheck(transactions, libraCreateParamses, params, meegoInfo.business);
    }

    // 请求接口前判断是否
    if (transactions.find(t => t.type === Type.Deny || t.type === Type.Error) !== undefined) {
      transactions;
    }

    // cuba校验
    if (skipBusinessCheck !== true && !skipVerification) {
      // 商业化才需要cuba校验
      if (meegoInfo.business !== undefined) {
        const { child } = meegoInfo.business;
        if (child !== undefined && child.find(v => v.name === '商业化') !== undefined) {
          await cubaCheck(transactions, libraCreateParamses, params, meegoInfo.business);
        }
      }
    }
  }
  if (transactions.length > 0) {
    return transactions;
  }
  if (experiments.length === 2 && experiments[0] !== undefined && experiments[1] !== undefined) {
    experiments[0].is_open_beta_combine = true;

    experiments[1].is_open_beta_combine = true;
    experiments[1].beta_combine_type = 1;
    experiments[0].beta_combine_type = 2;
    experiments[1].short_name =
      paramsArrary[1].categoryParams.grayOrRelease === GrayOrRelease.release ? '正式实验' : '灰度实验';
    experiments[0].short_name =
      paramsArrary[0].categoryParams.grayOrRelease === GrayOrRelease.release ? '正式实验' : '灰度实验';
  }
  const results = await Promise.all([
    batch_create_experiment({
      headers: {},
      data: {
        libra_app_id: experiments[0].app_id,
        tokens,
        experiments,
      },
    }),
  ]);

  results.forEach((result, index0) => {
    if (result.code === 200) {
      if (result.data === null) {
        const appId = experiments[index0]?.app_id;
        transactions.push({
          libraAppID: appId,
          type: Type.Success,
          identifier: Identifier.IdentifierDefault,
          message: JSON.stringify(result.data),
          raw: result.data,
        });
      }
      result.data?.experiments?.forEach((flight, index1) => {
        const appId = experiments[index1]?.app_id;
        transactions.push({
          libraAppID: appId,
          type: Type.Success,
          identifier: Identifier.IdentifierDefault,
          message: JSON.stringify(result.data),
          raw: result.data,
        });
        // 实验创建成功信息落库
        const flightId = result.data?.experiments?.[index1]?.id;
        if (flightId !== undefined) {
          createLibraSuccess({
            data: {
              appId,
              region: libraRegionFromAppId(appId),
              flightId,
              extraInfo: {
                grayOrRelease: paramsArrary[index1].categoryParams.grayOrRelease ?? GrayOrRelease.gray,
                isReopen: paramsArrary[index1].categoryParams.isReopen ?? false,
                reopenReason: paramsArrary[index1].categoryParams.reopenReason ?? [],
                dataRecoveryRemind: paramsArrary[index1].categoryParams.dataRecoveryRemind ?? [],
              },
            },
          }).then(() => {
            if (paramsArrary[index1].skipControl) {
              // 跳过管控的时候需要记录异常信息
              recordAbnormalFlightReportInfo({
                data: {
                  meegoId: paramsArrary[index1].categoryParams.meegoInfo?.id ?? -1,
                  flightId,
                },
              });
            }
          });

          // 对广告有影响需额外配置
          if (paramsArrary[index1].advancedParams.ad_flight_hit_rules !== undefined) {
            flight_tag_app({
              headers: {},
              data: {
                libra_app_id: appId,
                tokens,
                flightId,
              },
            }).then();
          }
        }
      });
    } else {
      transactions.push({
        libraAppID: experiments[index0]?.app_id,
        type: Type.Error,
        identifier: Identifier.IdentifierDefault,
        message: JSON.stringify(result),
      });
    }
  });

  return transactions;
};

/**
 * 获取过滤参数
 * @param getToken
 * @param getFlightCategoryParams
 * @param getBasicParamsArray
 * @param setBasicParamsArray
 */
export const getParamFilters = (
  getToken: () => Token[],
  getFlightCategoryParams: () => FlightCategoryParams,
  getBasicParamsArray: () => FlightBasicParams[],
  setBasicParamsArray: React.Dispatch<React.SetStateAction<FlightBasicParams[]>>,
  setBasicParams?: React.Dispatch<React.SetStateAction<FlightBasicParams>> | undefined,
) => {
  const _tokens = getToken();
  const categoryParams = getFlightCategoryParams();
  const basicParamsArray = getBasicParamsArray();
  const _libraAppid = categoryParams.libraAppid;
  const _manageType = categoryParams.manageType;
  const _basicParamsArray = basicParamsArray !== undefined ? [...basicParamsArray] : undefined;
  if (_tokens.length > 0 && _libraAppid !== undefined && _manageType !== undefined && _basicParamsArray !== undefined) {
    _basicParamsArray.forEach(params => {
      const appID = params.libraAppid;
      const productID = getLayerSelected(params)?.product_id;
      if (productID === undefined) {
        return;
      }
      if (
        params.paramFilters?.data === undefined ||
        appID !== params.paramFilters?.libraAppid ||
        productID !== params.paramFilters?.productID ||
        _manageType !== params.paramFilters?.manageType
      ) {
        retry({
          job: async () =>
            await param_filters_op({
              headers: {},
              data: {
                libra_app_id: appID,
                tokens: _tokens,
                manage_type: _manageType,
                product_id: productID,
              },
            }),
          judge: async response => (response?.data?.filter?.length ?? 0) > 0,
          abort: async () =>
            _tokens !== getToken() ||
            _libraAppid !== getFlightCategoryParams().libraAppid ||
            _manageType !== getFlightCategoryParams().manageType ||
            _basicParamsArray !== getBasicParamsArray(),
        }).then(response => {
          setBasicParamsArray(prevState =>
            prevState.map(p =>
              p.libraAppid !== appID
                ? p
                : {
                    ...p,
                    paramFilters:
                      response !== undefined
                        ? {
                            libraAppid: appID,
                            productID,
                            manageType: _manageType,
                            data: response.data,
                          }
                        : undefined,
                  },
            ),
          );
          if (setBasicParams) {
            setBasicParams(prevState =>
              prevState.libraAppid !== appID
                ? prevState
                : {
                    ...prevState,
                    paramFilters:
                      response !== undefined
                        ? {
                            libraAppid: appID,
                            productID,
                            manageType: _manageType,
                            data: response.data,
                          }
                        : undefined,
                  },
            );
          }
        });
      }
    });
  }
};

/**
 * 拉取流量层
 * @param getToken
 * @param getManageType
 * @param getFlightBasicParams
 * @param onChange
 */
export const getProductLayer = (
  getToken: () => Token[],
  getManageType: () => ManageType | undefined,
  getFlightBasicParams: () => FlightBasicParams,
  onChange: (params: Partial<FlightBasicParams> & Pick<FlightBasicParams, 'libraAppid'>) => void,
) => {
  const _tokens = getToken();
  const _manageType = getManageType();
  const _params = getFlightBasicParams();
  const abort = async () =>
    !(_tokens === getToken() && _manageType !== getManageType() && _params === getFlightBasicParams());

  if (_tokens.length > 0 && _manageType !== undefined) {
    // 已有有流量层数据就不重复请求
    if (
      _params.productLayers !== undefined &&
      _params.productLayers?.length > 0 &&
      _params.productLayers[0].libraAppid === _params.libraAppid &&
      _params.productLayers[0].manageType === _manageType
    ) {
      return;
    }

    retry({
      job: async () =>
        await filter_global_app({
          headers: {},
          data: {
            libra_app_id: _params.libraAppid,
            tokens: _tokens,
            manage_type: _manageType,
          },
        }),
      judge: async response => response.code === 0,
      abort,
    }).then(response => {
      const info = response.data?.filter(v => v.id === _params.libraAppid);
      if (info !== undefined && info.length > 0) {
        const promises = info[0].products.map(p =>
          retry({
            job: async () =>
              single_product_layer({
                headers: {},
                data: {
                  tokens: _tokens,
                  manage_type: _manageType,
                  product_id: p.id,
                  libra_app_id: _params.libraAppid,
                },
              }),
            judge: async rsp => rsp !== undefined,
            abort,
          }),
        );

        Promise.all(promises).then(results => {
          const result = info[0].products.map((v, i) => ({
            libraAppid: _params.libraAppid,
            manageType: _manageType,

            id: v.id,
            name: v.name,
            layers: results[i].data?.layers,
          }));

          onChange({
            libraAppid: _params.libraAppid,
            productLayers: result,
          });
        });
      }
    });
  }
};

/**
 * 获取过滤条件代码
 * @param getToken
 * @param getManageType
 * @param getFlightBasicParams
 * @param onChange
 */
export const getFilterCode = (
  getToken: () => Token[],
  getManageType: () => ManageType | undefined,
  getFlightBasicParams: () => FlightBasicParams,
  onChange: (params: Partial<FlightBasicParams> & Pick<FlightBasicParams, 'libraAppid'>) => void,
) => {
  const _tokens = getToken();
  const _manageType = getManageType();
  const _params = getFlightBasicParams();
  const _filters = _params.filters;
  const _paramsFilters = _params.paramFilters?.data;
  const _filterRule = generateFilterRule(_filters, _paramsFilters);
  if (_tokens.length > 0 && _manageType !== undefined && _filterRule !== undefined) {
    retry({
      job: async () =>
        await gen_filter_code({
          headers: {},
          data: {
            libra_app_id: _params.libraAppid,
            tokens: _tokens,
            filter_rule: _filterRule,
          },
        }),
      judge: async response => response.code === 0,
      abort: async () =>
        !(_tokens === getToken() && _manageType === getManageType() && _filters === getFlightBasicParams().filters),
    }).then(results => {
      if (results.code === 0) {
        onChange({
          libraAppid: _params.libraAppid,
          filterCode: results?.data?.filter_code,
        });
      }
    });
  }
};

abstract class FiltersPrefill {
  businessId: string[];
  libraAppId: number[];
  manageType: ManageType[];
  constructor(businessId: string[], libraAppId: number[], manageType: ManageType[]) {
    this.businessId = businessId;
    this.libraAppId = libraAppId;
    this.manageType = manageType;
  }
  async prefill(grayOrRelease: GrayOrRelease, onlineVersions?: OnlineVersion[]): Promise<Filter[][]> {
    return [[]];
  }
}

const _FiltersPrefill: FiltersPrefill[] = [
  // 商业化-广告-剪映-服务端实验
  new (class extends FiltersPrefill {
    override async prefill(grayOrRelease: GrayOrRelease, onlineVersions?: OnlineVersion[]): Promise<Filter[][]> {
      const filters: Filter[][] = [];
      const androidVersion = onlineVersions?.find(v => v.appid === 1775 && v.platform === 'android')?.version;
      const iosVersion = onlineVersions?.find(v => v.appid === 1775 && v.platform === 'ios')?.version;
      if (grayOrRelease === GrayOrRelease.gray) {
        if (androidVersion !== undefined || iosVersion === undefined) {
          filters.push([
            { key: 'device_platform', op: '==', value: ['android'], transformer: 'lower', useTransformer: true },
            {
              key: 'android_vc',
              op: '\u003e=',
              value: androidVersion !== undefined ? `${androidVersion.replaceAll('.', '')}00000` : '132000000',
            },
            { key: 'app_id', op: '==', value: ['1775'] },
            { key: 'channel', op: 'in_bundle', bundleId: [4738], transformer: 'lower', useTransformer: true },
          ]);
        }
        if (iosVersion !== undefined || androidVersion === undefined) {
          filters.push([
            { key: 'device_platform', op: '==', value: ['ios', 'iphone'], transformer: 'lower', useTransformer: true },
            { key: 'ios_vc', op: '\u003e=', value: iosVersion !== undefined ? `${iosVersion}.0` : '********' },
            { key: 'app_id', op: '==', value: ['1775'] },
            { key: 'channel', op: 'in_bundle', bundleId: [4738], transformer: 'lower', useTransformer: true },
          ]);
        }
      } else {
        if (androidVersion !== undefined || iosVersion === undefined) {
          filters.push([
            { key: 'device_platform', op: '==', value: ['android'], transformer: 'lower', useTransformer: true },
            {
              key: 'android_vc',
              op: '\u003e=',
              value: androidVersion !== undefined ? `${androidVersion.replaceAll('.', '')}00000` : '132000000',
            },
            { key: 'app_id', op: '==', value: ['1775'] },
          ]);
        }
        if (iosVersion !== undefined || androidVersion === undefined) {
          filters.push([
            { key: 'device_platform', op: '==', value: ['ios', 'iphone'], transformer: 'lower', useTransformer: true },
            { key: 'ios_vc', op: '\u003e=', value: iosVersion !== undefined ? `${iosVersion}.0` : '********' },
            { key: 'app_id', op: '==', value: ['1775'] },
          ]);
        }
      }
      return filters;
    }
  })([BUSINESS_ID_COMMERCIAL_AD], [LIBRA_APP_ID_LV], [ManageType.Server]),
  // 商业化-广告-醒图-服务端实验
  new (class extends FiltersPrefill {
    override async prefill(grayOrRelease: GrayOrRelease, onlineVersions?: OnlineVersion[]): Promise<Filter[][]> {
      const filters: Filter[][] = [];
      const androidVersion = onlineVersions?.find(v => v.appid === 2515 && v.platform === 'android')?.version;
      const iosVersion = onlineVersions?.find(v => v.appid === 2515 && v.platform === 'ios')?.version;
      if (grayOrRelease === GrayOrRelease.gray) {
        if (androidVersion !== undefined || iosVersion === undefined) {
          filters.push([
            { key: 'device_platform', op: '==', value: ['android'], transformer: 'lower', useTransformer: true },
            {
              key: 'android_update_version_code',
              op: '\u003e=',
              value: androidVersion !== undefined ? `${androidVersion.replaceAll('.', '')}00` : '104000',
            },
            { key: 'app_id', op: '==', value: ['2515'] },
            { key: 'channel', op: 'in_bundle', bundleId: [4738], transformer: 'lower', useTransformer: true },
          ]);
        }
        if (iosVersion !== undefined || androidVersion === undefined) {
          filters.push([
            { key: 'device_platform', op: '==', value: ['ios', 'iphone'], transformer: 'lower', useTransformer: true },
            {
              key: 'ios_update_version_code',
              op: '\u003e=',
              value: iosVersion !== undefined ? `${iosVersion}.0` : '********',
            },
            { key: 'app_id', op: '==', value: ['2515'] },
            { key: 'channel', op: 'in_bundle', bundleId: [4738], transformer: 'lower', useTransformer: true },
          ]);
        }
      } else {
        filters.push([
          { key: 'device_platform', op: '==', value: ['android'], transformer: 'lower', useTransformer: true },
          {
            key: 'android_update_version_code',
            op: '\u003e=',
            value: androidVersion !== undefined ? `${androidVersion.replaceAll('.', '')}00` : '104000',
          },
          { key: 'app_id', op: '==', value: ['2515'] },
        ]);
        if (iosVersion !== undefined || androidVersion === undefined) {
          filters.push([
            { key: 'device_platform', op: '==', value: ['ios', 'iphone'], transformer: 'lower', useTransformer: true },
            {
              key: 'ios_update_version_code',
              op: '\u003e=',
              value: iosVersion !== undefined ? `${iosVersion}.0` : '********',
            },
            { key: 'app_id', op: '==', value: ['2515'] },
          ]);
        }
      }
      return filters;
    }
  })([BUSINESS_ID_COMMERCIAL_AD], [LIBRA_APP_ID_XINGTU], [ManageType.Server]),
  // 商业化-广告-CapCut-服务端实验
  new (class extends FiltersPrefill {
    override async prefill(grayOrRelease: GrayOrRelease, onlineVersions?: OnlineVersion[]): Promise<Filter[][]> {
      const filters: Filter[][] = [];
      const androidVersion = onlineVersions?.find(v => v.appid === 3006 && v.platform === 'android')?.version;
      const iosVersion = onlineVersions?.find(v => v.appid === 3006 && v.platform === 'ios')?.version;
      if (grayOrRelease === GrayOrRelease.gray) {
        if (androidVersion !== undefined || iosVersion === undefined) {
          filters.push([
            { key: 'app_id', op: '==', value: ['3006'] },
            { key: 'priority_region', op: '==', transformer: 'lower', useTransformer: true },
            { key: 'device_platform', op: '==', value: ['android'], transformer: 'lower', useTransformer: true },
            {
              key: 'android_vc',
              op: '\u003e=',
              value: androidVersion !== undefined ? `${androidVersion.replaceAll('.', '')}0000` : '13100000',
            },
            {
              key: 'android_vc',
              op: '\u003c',
              value: androidVersion !== undefined ? `${androidVersion.replaceAll('.', '')}1600` : '13101600',
            },
          ]);
        }
        if (iosVersion !== undefined || androidVersion === undefined) {
          filters.push([
            { key: 'app_id', op: '==', value: ['3006'] },
            { key: 'priority_region', op: '==', transformer: 'lower', useTransformer: true },
            { key: 'device_platform', op: '==', value: ['ios', 'iphone'], transformer: 'lower', useTransformer: true },
            { key: 'ios_vc', op: '\u003e=', value: iosVersion !== undefined ? `${iosVersion}.80` : '*********' },
            { key: 'ios_vc', op: '\u003c', value: iosVersion !== undefined ? `${iosVersion}.90` : '*********' },
          ]);
        }
      } else {
        if (androidVersion !== undefined || iosVersion === undefined) {
          filters.push([
            { key: 'app_id', op: '==', value: ['3006'] },
            { key: 'priority_region', op: '==', transformer: 'lower', useTransformer: true },
            { key: 'device_platform', op: '==', value: ['android'], transformer: 'lower', useTransformer: true },
            {
              key: 'android_vc',
              op: '\u003e=',
              value: androidVersion !== undefined ? `${androidVersion.replaceAll('.', '')}0000` : '13100000',
            },
          ]);
        }
        if (iosVersion !== undefined || androidVersion === undefined) {
          filters.push([
            { key: 'app_id', op: '==', value: ['3006'] },
            { key: 'priority_region', op: '==', transformer: 'lower', useTransformer: true },
            { key: 'device_platform', op: '==', value: ['ios', 'iphone'], transformer: 'lower', useTransformer: true },
            { key: 'ios_vc', op: '\u003e=', value: iosVersion !== undefined ? `${iosVersion}.00` : '*********' },
          ]);
        }
      }
      return filters;
    }
  })([BUSINESS_ID_COMMERCIAL_AD], [LIBRA_APP_ID_CAPCUT], [ManageType.Server]),

  // 商业化-订阅-剪映-服务端实验
  new (class extends FiltersPrefill {
    override async prefill(grayOrRelease: GrayOrRelease, onlineVersions?: OnlineVersion[]): Promise<Filter[][]> {
      const version =
        onlineVersions?.find(v => v.appid === 1775 && v.platform === 'android')?.version ??
        onlineVersions?.find(v => v.appid === 1775 && v.platform === 'ios')?.version;

      let vv: string | undefined;
      if (version !== undefined) {
        const v = version.split('.');
        if (v.length === 3) {
          vv = (parseInt(v[0], 10) * 10000 + parseInt(v[1], 10) * 100 + parseInt(v[2], 10)).toString(10);
        }
      }

      return [
        [
          { key: 'app_id', op: '==', value: ['1775'] },
          { key: '_version_code', op: '\u003e=', value: vv !== undefined ? vv : '140400' },
        ],
      ];
    }
  })([BUSINESS_ID_COMMERCIAL_SUBSCRIPTION], [LIBRA_APP_ID_LV], [ManageType.Server]),
  // 商业化-订阅-醒图-服务端实验
  new (class extends FiltersPrefill {
    override async prefill(grayOrRelease: GrayOrRelease, onlineVersions?: OnlineVersion[]): Promise<Filter[][]> {
      const version =
        onlineVersions?.find(v => v.appid === 2515 && v.platform === 'android')?.version ??
        onlineVersions?.find(v => v.appid === 2515 && v.platform === 'ios')?.version;

      let vv: string | undefined;
      if (version !== undefined) {
        const v = version.split('.');
        if (v.length === 3) {
          vv = (parseInt(v[0], 10) * 10000 + parseInt(v[1], 10) * 100 + parseInt(v[2], 10)).toString(10);
        }
      }

      return [
        [
          { key: 'app_id', op: '==', value: ['2515'] },
          { key: '_version_code', op: '\u003e=', value: vv !== undefined ? vv : '100400' },
        ],
      ];
    }
  })([BUSINESS_ID_COMMERCIAL_SUBSCRIPTION], [LIBRA_APP_ID_XINGTU], [ManageType.Server]),
  // 商业化-订阅-CapCut-服务端实验
  new (class extends FiltersPrefill {
    override async prefill(grayOrRelease: GrayOrRelease, onlineVersions?: OnlineVersion[]): Promise<Filter[][]> {
      const version =
        onlineVersions?.find(v => v.appid === 3006 && v.platform === 'android')?.version ??
        onlineVersions?.find(v => v.appid === 3006 && v.platform === 'ios')?.version;

      let vv: string | undefined;
      if (version !== undefined) {
        const v = version.split('.');
        if (v.length === 3) {
          vv = (parseInt(v[0], 10) * 10000 + parseInt(v[1], 10) * 100 + parseInt(v[2], 10)).toString(10);
        }
      }

      return [
        [
          { key: 'app_id', op: '==', value: ['3006'] },
          { key: '_version_code', op: '\u003e=', value: vv !== undefined ? vv : '120400' },
          { key: 'priority_region', op: '==', value: ['iq'], transformer: 'lower', useTransformer: true },
        ],
      ];
    }
  })([BUSINESS_ID_COMMERCIAL_SUBSCRIPTION], [LIBRA_APP_ID_CAPCUT], [ManageType.Server]),

  // 商业化-广告-剪映-客户端实验
  new (class extends FiltersPrefill {
    override async prefill(grayOrRelease: GrayOrRelease, onlineVersions?: OnlineVersion[]): Promise<Filter[][]> {
      const filters: Filter[][] = [];
      const androidVersion = onlineVersions?.find(v => v.appid === 1775 && v.platform === 'android')?.version;
      const iosVersion = onlineVersions?.find(v => v.appid === 1775 && v.platform === 'ios')?.version;

      let iosVv: string | undefined;
      if (iosVersion !== undefined) {
        const v = iosVersion.split('.');
        if (v.length === 3) {
          iosVv = (parseInt(v[0], 10) * 1000000 + parseInt(v[1], 10) * 1000 + parseInt(v[2], 10)).toString(10);
        }
      }

      if (androidVersion !== undefined || iosVv === undefined) {
        filters.push([
          { key: 'device_platform', op: '==', value: ['android'], transformer: 'lower', useTransformer: true },
          {
            key: '_version_code',
            op: '\u003e=',
            value: androidVersion !== undefined ? `${androidVersion.replaceAll('.', '')}00000` : '145000000',
          },
        ]);
      }
      if (iosVv !== undefined || androidVersion === undefined) {
        filters.push([
          { key: 'device_platform', op: '==', value: ['ios', 'iphone'], transformer: 'lower', useTransformer: true },
          {
            key: '_version_code',
            op: '\u003e=',
            value: iosVv !== undefined ? iosVv : '14005000',
          },
        ]);
      }
      return filters;
    }
  })([BUSINESS_ID_COMMERCIAL_AD], [LIBRA_APP_ID_LV], [ManageType.SettingsClientSDK]),
  // 商业化-广告-醒图-客户端实验
  new (class extends FiltersPrefill {
    override async prefill(grayOrRelease: GrayOrRelease, onlineVersions?: OnlineVersion[]): Promise<Filter[][]> {
      const filters: Filter[][] = [];
      const androidVersion = onlineVersions?.find(v => v.appid === 2515 && v.platform === 'android')?.version;
      const iosVersion = onlineVersions?.find(v => v.appid === 2515 && v.platform === 'ios')?.version;

      let iosVv: string | undefined;
      if (iosVersion !== undefined) {
        const v = iosVersion.split('.');
        if (v.length === 3) {
          iosVv = (parseInt(v[0], 10) * 10000 + parseInt(v[1], 10) * 100 + parseInt(v[2], 10)).toString(10);
        }
      }

      if (androidVersion !== undefined || iosVv === undefined) {
        filters.push([
          { key: 'device_platform', op: '==', value: ['android'], transformer: 'lower', useTransformer: true },
          {
            key: '_version_code',
            op: '\u003e=',
            value: androidVersion !== undefined ? `${androidVersion.replaceAll('.', '')}00` : '111000',
          },
        ]);
      }

      if (iosVv !== undefined || androidVersion === undefined) {
        filters.push([
          { key: 'device_platform', op: '==', value: ['ios', 'iphone'], transformer: 'lower', useTransformer: true },
          { key: '_version_code', op: '\u003e=', value: iosVv !== undefined ? iosVv : '110100' },
        ]);
      }

      return filters;
    }
  })([BUSINESS_ID_COMMERCIAL_AD], [LIBRA_APP_ID_XINGTU], [ManageType.SettingsClientSDK]),
  // 商业化-广告-CapCut-客户端实验
  new (class extends FiltersPrefill {
    override async prefill(grayOrRelease: GrayOrRelease, onlineVersions?: OnlineVersion[]): Promise<Filter[][]> {
      const filters: Filter[][] = [];
      const androidVersion = onlineVersions?.find(v => v.appid === 3006 && v.platform === 'android')?.version;
      const iosVersion = onlineVersions?.find(v => v.appid === 3006 && v.platform === 'ios')?.version;

      let iosVv: string | undefined;
      if (iosVersion !== undefined) {
        const v = iosVersion.split('.');
        if (v.length === 3) {
          iosVv = (parseInt(v[0], 10) * 1000000 + parseInt(v[1], 10) * 1000 + parseInt(v[2], 10)).toString(10);
        }
      }

      if (androidVersion !== undefined || iosVv === undefined) {
        filters.push([
          { key: 'device_platform', op: '==', value: ['android'], transformer: 'lower', useTransformer: true },
          { key: 'cc_region', op: '==', transformer: 'lower', useTransformer: true },
          {
            key: '_version_code',
            op: '\u003e=',
            value: androidVersion !== undefined ? `${androidVersion.replaceAll('.', '')}0000` : '11900000',
          },
        ]);
      }

      if (iosVv !== undefined || androidVersion === undefined) {
        filters.push([
          { key: 'device_platform', op: '==', value: ['ios', 'iphone'], transformer: 'lower', useTransformer: true },
          { key: 'cc_region', op: '==', transformer: 'lower', useTransformer: true },
          { key: '_version_code', op: '\u003e=', value: iosVv !== undefined ? iosVv : '11900000' },
        ]);
      }
      return filters;
    }
  })([BUSINESS_ID_COMMERCIAL_AD], [LIBRA_APP_ID_CAPCUT], [ManageType.SettingsClientSDK]),

  // 商业化-订阅-剪映-客户端实验
  new (class extends FiltersPrefill {
    override async prefill(grayOrRelease: GrayOrRelease, onlineVersions?: OnlineVersion[]): Promise<Filter[][]> {
      const filters: Filter[][] = [];
      const androidVersion = onlineVersions?.find(v => v.appid === 1775 && v.platform === 'android')?.version;
      const iosVersion = onlineVersions?.find(v => v.appid === 1775 && v.platform === 'ios')?.version;

      let iosVv: string | undefined;
      if (iosVersion !== undefined) {
        const v = iosVersion.split('.');
        if (v.length === 3) {
          iosVv = (parseInt(v[0], 10) * 1000000 + parseInt(v[1], 10) * 1000 + parseInt(v[2], 10)).toString(10);
        }
      }

      if (androidVersion !== undefined || iosVv === undefined) {
        filters.push([
          { key: 'device_platform', op: '==', value: ['android'], transformer: 'lower', useTransformer: true },
          {
            key: '_version_code',
            op: '\u003e=',
            value: androidVersion !== undefined ? `${androidVersion.replaceAll('.', '')}00000` : '146000000',
          },
        ]);
      }

      if (iosVv !== undefined || androidVersion === undefined) {
        filters.push([
          {
            key: 'device_platform',
            op: '==',
            value: ['ios', 'iphone', 'ipad'],
            transformer: 'lower',
            useTransformer: true,
          },
          { key: '_version_code', op: '\u003e=', value: iosVv !== undefined ? iosVv : '14006000' },
        ]);
      }
      return filters;
    }
  })([BUSINESS_ID_COMMERCIAL_SUBSCRIPTION], [LIBRA_APP_ID_LV], [ManageType.SettingsClientSDK]),
  // 商业化-订阅-醒图-客户端实验
  new (class extends FiltersPrefill {
    override async prefill(grayOrRelease: GrayOrRelease, onlineVersions?: OnlineVersion[]): Promise<Filter[][]> {
      const filters: Filter[][] = [];
      const androidVersion = onlineVersions?.find(v => v.appid === 2515 && v.platform === 'android')?.version;
      const iosVersion = onlineVersions?.find(v => v.appid === 2515 && v.platform === 'ios')?.version;

      let iosVv: string | undefined;
      if (iosVersion !== undefined) {
        const v = iosVersion.split('.');
        if (v.length === 3) {
          iosVv = (parseInt(v[0], 10) * 10000 + parseInt(v[1], 10) * 100 + parseInt(v[2], 10)).toString(10);
        }
      }

      if (androidVersion !== undefined || iosVv === undefined) {
        filters.push([
          { key: 'device_platform', op: '==', value: ['android'], transformer: 'lower', useTransformer: true },
          {
            key: '_version_code',
            op: '\u003e=',
            value: androidVersion !== undefined ? `${androidVersion.replaceAll('.', '')}00` : '111000',
          },
        ]);
      }

      if (iosVv !== undefined || androidVersion === undefined) {
        filters.push([
          {
            key: 'device_platform',
            op: '==',
            value: ['ios', 'iphone', 'ipad'],
            transformer: 'lower',
            useTransformer: true,
          },
          { key: '_version_code', op: '\u003e=', value: iosVv !== undefined ? iosVv : '110100' },
        ]);
      }
      return filters;
    }
  })([BUSINESS_ID_COMMERCIAL_SUBSCRIPTION], [LIBRA_APP_ID_XINGTU], [ManageType.SettingsClientSDK]),
  // 商业化-订阅-CapCut-客户端实验
  new (class extends FiltersPrefill {
    override async prefill(grayOrRelease: GrayOrRelease, onlineVersions?: OnlineVersion[]): Promise<Filter[][]> {
      const filters: Filter[][] = [];
      const androidVersion = onlineVersions?.find(v => v.appid === 3006 && v.platform === 'android')?.version;
      const iosVersion = onlineVersions?.find(v => v.appid === 3006 && v.platform === 'ios')?.version;

      let iosVv: string | undefined;
      if (iosVersion !== undefined) {
        const v = iosVersion.split('.');
        if (v.length === 3) {
          iosVv = (parseInt(v[0], 10) * 1000000 + parseInt(v[1], 10) * 1000 + parseInt(v[2], 10)).toString(10);
        }
      }

      if (androidVersion !== undefined || iosVv === undefined) {
        filters.push([
          { key: 'device_platform', op: '==', value: ['android'], transformer: 'lower', useTransformer: true },
          { key: 'cc_region', op: '==', transformer: 'lower', useTransformer: true },
          {
            key: '_version_code',
            op: '\u003e=',
            value: androidVersion !== undefined ? `${androidVersion.replaceAll('.', '')}0000` : '11900000',
          },
        ]);
      }

      if (iosVv !== undefined || androidVersion === undefined) {
        filters.push([
          {
            key: 'device_platform',
            op: '==',
            value: ['ios', 'iphone', 'ipad'],
            transformer: 'lower',
            useTransformer: true,
          },
          { key: 'cc_region', op: '==', transformer: 'lower', useTransformer: true },
          { key: '_version_code', op: '\u003e=', value: iosVv !== undefined ? iosVv : '11009000' },
        ]);
      }
      return filters;
    }
  })([BUSINESS_ID_COMMERCIAL_SUBSCRIPTION], [LIBRA_APP_ID_CAPCUT], [ManageType.SettingsClientSDK]),
];

export const getFiltersPrefill = async (
  business: Business | undefined,
  libraAppid: number,
  manageType: ManageType,
  grayOrRelease: GrayOrRelease,
  onlineVersions?: OnlineVersion[],
) => {
  let businessId: string;
  if (business !== undefined) {
    while (business.child !== undefined && business.child.length === 1) {
      business = business?.child[0];
    }
    businessId = business?.id;
  }
  return (
    _FiltersPrefill
      .filter(fill =>
        businessId === undefined && fill.businessId.length === 0 ? true : Boolean(fill.businessId.includes(businessId)),
      )
      .find(fill => fill.libraAppId.includes(libraAppid) && fill.manageType.includes(manageType))
      ?.prefill(grayOrRelease, onlineVersions) ?? ([[]] as Filter[][])
  );
};
