import { FilterRule } from '@shared/libra/flight';
import { User } from '@pa/shared/dist/src/core';
import { PaControlType } from '@shared/libra/libraControl';

export interface LibraMeego {
  meego_project: string; // "faceu"
  meego_story: string; // "13327913"
  meego_libra: string; // "7301192424255766556"
  meego_project_key: string; // "5cf8e2b94da26751ae438e6c"
}

/**
 * 从EventBus消费过来的libra_event事件内容
 * https://bytedance.larkoffice.com/docs/doccncMtpVCc7SPB2EuLKf3yFHf
 */
export interface LibraEvent {
  event: {
    event_type: string; // 事件名称
    event_influence: string; // 事件影响
    event_operator: string; // 事件操作人，邮箱前缀
    operation_reason: string; // 操作原因[OperationReason]
  };
  ts: string; // 事件时间,时间戳
  app_info: {
    // 产品信息
    app_ids: string; // 产品注册appid，若有多个以逗号分隔  剪映:147 CapCut:305
  };
  product_info: {
    // 功能模块信息
    token: string;
    product_id: string; // 功能模块id
  };
  layer_info: {
    // 实验层信息
    layer_type: string;
    layer_id: string; // 实验层id
    layer_display_name: string; // 实验层名称
    layer_name: string; // 实验层哈希名称，这个字段用于分流，与layer_display_name可能不同，建议不要使用该字段
  };
  flight_info: {
    // 实验信息
    description: string; // 实验描述
    expectation: string; // 实验预期
    end_time: string; // 实验过期时间
    flight_id: string; // 实验id
    flight_display_name: string; // 实验名称
    flight_type: string; // 实验类型-"settings_client_sdk"
    flight_name: string; // 实验哈希名称，这个字段用于分流，与flight_display_name不同，建议不要使用该字段,"9f618327acef1f27c1d29f9c28e50190"
    owner: string; // 实验owner，多个以逗号分隔
    start_time: string; // 实验开启时间
    status: string; // 实验状态，目前有running、stopped、testing、suspended四种
    traffic: string; // 实验流量-"12.0%"
    tags: string[]; // 实验标签
    start_ts: number; // 实验开启时间unix timestamp
    end_ts: number; // 实验结束时间unix timestamp
    flight_url: string; // 实验链接-"https://data.bytedance.net/libra/flight/2157535/edit"
    create_ts: number; // 实验创建时间unix timestamp
    create_time: string; // 实验创建时间-"2023-11-14 14:31:47"
    strategy_category: string; // 实验上线策略类别
    filter_rule: FilterRule[]; // 实验流量过滤条件，第一次解析只是一个json string
    enable_gradual: string; // 实验流量生效方式 平滑生效/立刻生效
    vm_type: string; // 分流逻辑-"正常分流"
    create_by_openapi: boolean; // 实验是否通过openapi创建
    meego_info: {
      meego_array: LibraMeego[]; // 第一次解析只是一个json string
    };
    numerical_traffic: number; // 0.12
  };
  version_info: string; // 注意这里为序列化后的string
  traffic_change_type: string; // 流量生效类型，目前只有increase/decrease
  traffic_change_strategy: TrafficChangeStrategy[];
  review_info: {
    review_launcher: string; // 邮箱前缀
    review_type: string; // review类型 (只在实验开启事件、实验信息变更事件展示)
    review_emit_event: string; // 触发review事件（只在实验review信息事件展示）
    review_id: number; // 实验review的具体id(只在实验开启事件、实验信息变更事件展示)
    reviewer: [{ string: string }]; // 开启实验的review情况(只在实验开启事件展示)
    // 卡点信息
    is_canary: boolean; // 实验是否在精准熔断(只在实验开启、实验流量变更、实验信息变更事件展示)
    is_traffic_canary: boolean; // 实验是否触发放量管控(只在实验开启、实验流量变更、实验信息变更事件展示)
    canary_traffic: string; // 放量管控流量（只有在触发放量管控为true展示）
    traffic_canary_time: string; // 放量管控时间（只有在触发放量管控为true展示）
    traffic_canary_skip: boolean; // 是否跳过放量管控 （只有在触发放量管控为true展示）
    test_user: boolean; // 实验是否有测试用户(只在实验开启事件展示)
    is_auto_stop: boolean; // 实验是否自动关闭(只在实验关闭事件展示)
  };
  change_info: string; // 实验信息变更事件（flight change info）包含的信息（只在实验信息变更事件中展示）
}

// 流量生效策略，这里类型是数组是因为后续平台会支持多阶段线性生效（目前只有一段）
export interface TrafficChangeStrategy {
  start_traffic: string; // 起始流量
  start_numerical_traffic: number; // 起始流量
  end_traffic: string; // 终止流量
  end_numerical_traffic: number; // 终止流量
  duration: number; // 放量时长，0表示立即生效，非0值表示线性生效时长（秒）
}

export interface LibraVersionPlatform {
  android: {
    isHit: boolean;
    minVersionCode: number;
    maxVersionCode: number;
    version: string;
    isBeta: boolean;
  };
  iphone: {
    isHit: boolean;
    minVersionCode: number;
    maxVersionCode: number;
    version: string;
    isBeta: boolean;
  };
}

export enum LibraEventType {
  // 实验开始
  FlightStart = 'flight start',
  // 实验结束
  FlightStop = 'flight stop',
  // 实验流量变更
  FlightChangeTraffic = 'flight change traffic',
  // 实验时长变更
  FlightChangeDuration = 'flight change duration',
  // 实验暂停
  FlightSuspend = 'flight suspend',
  // 实验继续
  FlightContinue = 'flight continue',
  // 实验重启
  FlightForceRestart = 'flight force restart',
  // 实验信息变更 （目前仅支持实验owner和配置发生变更时，才有该实验事件）
  FlightChangeInfo = 'flight change info',
  // 实验review信息（发起、通过、拒绝、跳过review）
  FlightReviewInfo = 'flight review info',
  // 实验全量
  FlightFull = 'flight full',
  // 实验创建
  FlightCreate = 'flight create',
  // 实验泳道灰度开启
  FlightLaneGrayStart = 'flight lane gray start',
  // 实验泳道线性扩量
  FlightLaneSmoothUpBound = 'flight lane smooth up bound',
  // 实验泳道灰度跳过
  FlightLaneGraySkip = 'flight lane gray skip',
  // 实验泳道灰度结束
  FlightLaneGrayEnd = 'flight lane gray end',
  // 实验泳道灰度暂停
  FlightLaneGrayStop = 'flight lane gray stop',
  // 实验组关闭
  FlightVersionDisable = 'flight version disable',
  // 发起launch review
  LaunchReviewStart = 'launch review start',
  // 发起LR上线登记&通知
  LaunchReviewRelease = 'Launch review release',
}

export const PaControlTypeDesc: {
  [key: string]: string;
} = {
  [PaControlType.GrayFlightDidNot100]: '灰度实验未开启100%流量',
  [PaControlType.AfterThridGray]: '三灰后开启灰度实验',
  [PaControlType.AfterFullRelease]: '全量后未经过灰度实验直接开启正式实验',
};

export const LibraEventDesc: {
  [key: string]: string;
} = {
  [LibraEventType.FlightStart]: '实验开启',
  [LibraEventType.FlightStop]: '实验关闭',
  [LibraEventType.FlightChangeTraffic]: '实验流量变更',
  [LibraEventType.FlightChangeDuration]: '实验时长变更',
  [LibraEventType.FlightSuspend]: '实验暂停',
  [LibraEventType.FlightContinue]: '实验继续',
  [LibraEventType.FlightForceRestart]: '实验重启',
  [LibraEventType.FlightChangeInfo]: '实验信息变更',
  [LibraEventType.FlightReviewInfo]: '实验review信息',
  [LibraEventType.FlightFull]: '实验全量',
  [LibraEventType.FlightCreate]: '实验创建',
  [LibraEventType.FlightLaneGrayStart]: '实验泳道灰度开启',
  [LibraEventType.FlightLaneSmoothUpBound]: '实验泳道线性扩量',
  [LibraEventType.FlightLaneGraySkip]: '实验泳道灰度跳过',
  [LibraEventType.FlightLaneGrayEnd]: '实验泳道灰度结束',
  [LibraEventType.FlightLaneGrayStop]: '实验泳道灰度暂停',
  [LibraEventType.FlightVersionDisable]: '实验组关闭',
  [LibraEventType.LaunchReviewStart]: '发起launch review',
  [LibraEventType.LaunchReviewRelease]: '发起LR上线登记&通知',
};

export const OperationReason: {
  [key: string]: string;
} = {
  CollectData: '实验下线-回收数据，准备推全',
  OnlineEmergency: '实验下线-线上应急(指标异常、用户反馈等)、关闭止损',
  UserNegativeFeedback: '实验下线-用户负向反馈',
  IndicatorNotMatch: '实验下线-指标不符合预期或需要继续迭代',

  ConfigError: '重开实验-实验配置错误',
  Bugfix: '重开实验-Bug修复或问题排查',
  RequirementChange: '重开实验-实验符合预期，需求有变更或配置需调优',
  FlightDesignChange: '重开实验-实验设计变更',
  NonSubjective: '重开实验-非主观因素',

  DependencyNotOnline: '依赖未上线(代码或三方配置等)',
  AvoidStuckDuringBanPeriod: '避免封禁期放量被流程卡住，提前走完审核流程',

  // 非Libra平台枚举
  FullRelease: '实验全量',
};

export interface LibraBaseInfo {
  appIds: string[];
  appNames: string[];
  flightId: string;
  flightName: string;
  flightUrl: string;
  flightOwners: User[];
  flightType: string; // 实验类型
  operator: string;
}
