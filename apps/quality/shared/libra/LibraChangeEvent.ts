import { LibraEventType, OperationReason, TrafficChangeStrategy } from '@shared/libra/libraInfo';
import { LibraNewInfo } from '@shared/libra/LibraNewInfo';

export interface LibraChangeEvent {
  eventType: LibraEventType;
  eventInfluence: string;
  eventOperator: string;
  operationReason: string;
  flightId: number;
  flightName: string;
  flightUrl: string;
  ts: number;
  trafficChangeType: string;
  trafficChangeStrategy: TrafficChangeStrategy[];
  isBigFlow: boolean; // 是否非小流量发布
  inPeakTime: boolean; // 是否在高峰期
  libraNewInfo?: LibraNewInfo;
  traffic?: string;
  eventId?: string; // 补充 event id，用于消息去重
}

export function getRealStopReason(operationReason: string) {
  return OperationReason[operationReason] || operationReason || OperationReason.FullRelease;
}
