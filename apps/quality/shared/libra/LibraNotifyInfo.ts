import { User } from '@pa/shared/dist/src/core';
import { LibraMeegoInfo, LibraNewInfo } from '@shared/libra/LibraNewInfo';
import { WorkItemInfo } from '@shared/typings/meego';
import { LibraEvent, LibraVersionPlatform } from '@shared/libra/libraInfo';

// 通知开启实验卡片类型
export enum LibraLaunchNotifyCardType {
  Gray = 1, // 开启灰度实验
  Release = 2, // 开启正式实验
  CloseGray = 3, // 关闭灰度实验
}

// 生成开启实验通知卡片的必要信息
export interface LibraLaunchNotifyCreateCardInfo {
  appId: number;
  version: string;
  fullVersionDesc: string;
  versionStageName: string;
  startTime: number;
  meegoInfo: LibraMeegoInfo;
  libraInfos: LibraNewInfo[];
  cardType: LibraLaunchNotifyCardType;
}

// 提醒开启实验的全部信息（包括：当前跟版的 Meego 需求以及对应的实验列表）
export interface LibraLaunchNotifyAllInfo {
  meegoInfoList: LibraMeegoInfo[];
  meegoAndLibraMap: Map<string, LibraNewInfo[]>;
}

// 提醒开启实验的卡片信息
export interface LibraLaunchNotifyCardInfo {
  appId: number;
  version: string; // 灰度版本，如“15.1.0”
  fullVersionDesc: string; // 版本完整表述，如“剪映-iOS-15.1.0”
  versionStageName: string; // 版本状态阶段，如“第1轮灰度”
  startTime: number; // 版本阶段开始时间
  meegoId: number;
  meegoName: string;
  meegoUrl: string;
  pmOwners: User[]; // 产品Owner
  techOwners: User[]; // 技术Owner
  libraInfo: LibraNewInfo; // 绑定的实验信息
}
export enum LibraCreateState {
  // 未创建
  Uncreated = 0,
  // 创建中
  Creating = 1,
  // 已创建
  Created = 2,
  // 创建失败
  Failed = 3,
}
// 提醒 Review 实验数据卡片信息
export interface LibraRemindDataReviewNotifyCardInfo {
  meegoId: number;
  meegoName: string;
  meegoUrl: string;
  pmOwners: User[]; // 产品Owner
  techOwners: User[]; // 技术Owner
  libraInfo: LibraNewInfo; // 绑定的实验信息
}

// 实验流量变更提醒卡片信息
export interface LibraTrafficChangeNotifyCardInfo {
  appIds: string[];
  appNames: string[];
  flightId: string;
  flightName: string;
  flightUrl: string;
  flightOwners: User[];
  traffic: string;
  trafficChangeType: string;
}
