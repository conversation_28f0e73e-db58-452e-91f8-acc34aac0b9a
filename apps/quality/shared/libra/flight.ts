import { LibraMeego } from '@shared/libra/libraInfo';

export interface FlightListRequest {
  app: number; // 筛选不同的应用
  product: number; // 筛选不同功能模块，不传或传-1表示全部
  layer: number; // 筛选不同的层，不传或传-1表示全部
  status: number; // 实验当前所处状态，默认-1全部，0已关闭, 1进行中, 2待调度, 3调试中, 4已暂停, 91同0
  time_type: number; // 查询时间的类型，默认1:不限制时间, 2:运行时间 3:开始时间 4:停止时间
  start_time: string; // 筛选的起始时间，标准日期格式：%Y-%m-%d+%H:%M:%S
  end_time: string; // 筛选的终止时间，标准日期格式：%Y-%m-%d+%H:%M:%S
  desc: string; // 实验名称/描述，部分匹配
  device_platform: string; // 请求参数里设置的客户端平台，iphone,ipad,android
  user_group_id: number; // 人员群组ID，使用前需要明确具体的ID
  config: string; // 配置参数key，部分匹配
  page: number; // 第几页，默认1
  page_size: number; // 每页条数，默认100
  tag: string; // 实验标签, 多个标签以,号隔开，最多3个
  user: string; // 实验owner，只支持单个，值为用户邮箱前缀
  type: string; // 实验类型, 如strategy、product、ad_user、ad_advertiser、ad_plan、interleaving、ab_client_sdk、settings_client_sdk、ab_local_client、gp_regression、settings_client_normal、web_sdk、ad_group
  test_user: string; // 实验测试用户
}

export interface FlightListResponse {
  flights: Flight[];
  page: LibraPage;
}

export interface Flight {
  layer_type: number; // 分流类型，枚举0: uid, 1: did, 2: rid, 3: union, 4: uuid, 5: cdid, 6: ssid, 7: webid, 8: pkid, 9: 纯uid分流
  status: number; // 实验状态，0已结束, 1进行中, 2待调度, 3测试中, 4已暂停, 91同0
  review_status: number; // review状态，0 测试中实验review已发布 或 运行中实验未发起review或review未通过或已撤回或已发布；1 测试中实验未发起review或review未通过或已撤回，运行中实验不会出现；2 review中
  app_id: number; // app的id
  app_name: string;
  combination_type: number;
  support_inherit: boolean;
  owner: string[];
  is_utc: boolean;
  id: number; // 实验id
  duration: number; // 实验持续天数
  filter_rule: FilterRule[]; // 过滤条件
  filter_function: string;
  release_key: string;
  support_reversal: boolean; // 是否支持反转
  type: string; // 实验类型
  product_name: string; // 功能模块名字
  inherit_type: number;
  product: string;
  description: string;
  short_name: string;
  tags: string[];
  layer_id: number; // 实验的实验层id
  layer_domain: LibraLayerDomain; // 实验流量层所属域
  versions: LibraVersion[]; // 实验分组
  create_time: string;
  start_time: string; // 开始时间
  end_time: string; // 结束时间
  show_release: number;
  filter_type: string; // 过滤类型
  reuse_type: number;
  is_favourite: number; // 是否被当前用户收藏了
  version_resource: number; // float 分流流量
  meego_info: {
    meego_array: LibraMeego[];
  };
  layer_name: string; // 流量层名字
  product_id: number;
  running_duration: number; // float 实验已运行时长（秒）
  reversal_type: number;
  inherit_key: string;
  name: string; // 实验名称
  settings_publish_status: number;
  has_more_flights: number;
  reversal_key: string;
  combination_key: string;
  show_continue: number;
  metric_scene: number;
  release_type: number;
  specified_psms: string[]; // 指定PSM
  modify_time: number; // float
  last_gradual_traffic: LastGradualTraffic;
  kind: string; // 实验类型
  beta_combine_key: string;
  beta_combine_type: number;
  layer_hash_strategy: string;
  settings_item_ids?: number[];
}

export interface FlightBaseUser {
  update_time: string;
  is_delay: number;
  is_realtime: number;
  sum_baseuser: number;
  baseuser: {
    vid: number;
    vname: string;
    baseuser: number;
  }[];
}

export const KIND_BETA_COMBINE = 'beta_combine';

export const BETA_COMBINE_TYPE_RELEASE = 1;

export const BETA_COMBINE_TYPE_GRAY = 2;

export interface LibraVersion {
  is_release: number; // 在实验结论页面，固化实验结论时，固化选择了哪个组，哪个组的is_release就是1
  status: number; // 状态， 0:已关闭, 1:有效
  test_user: string; // 测试用户
  description: string; // 描述
  config: string; // 配置参数，json序列化
  user_tag: string; // 用户分群
  settings_keys: string;
  type: number; // 0 对照组 1 实验组
  real_traffic: number; // 流量占比
  id: number; // 下发vid
  name: string; // 名称
  weight: number; // 流量权重比，仅针对流量不均等实验有意义，权重比值0 ~ 1000，实验每个version的weight加起来一定等于1000
  images: string[];
  debug_user_group_ids: string[]; // 内测用户群组
}

export interface LibraLayerDomain {
  layer_type: string; // 分流方式，例如did
  name: string; // 互斥域名，如剪映-广告-功能实验
  suffix_key: string;
  left_num: number;
  traffic_len: number;
  id: number; // 互斥域id
}

export interface FilterRule {
  _rcKey: string;
  logic: string;
  conditions: Condition[];
}

export interface Condition {
  logic: string;
  condition: ConditionItem;
}
export interface ConditionItem {
  hash_suffix?: any;
  mod_value?: any;
  transformer: string;
  disabled_des: string;
  value: number | string | number[] | string[] | ConditionBundleItem[];
  disabled: boolean;
  key: string;
  is_utc: boolean;
  type: string;
  op: string;
  source: string;
}

export interface ConditionBundleItem {
  id: number;
  name: string;
}

export interface LibraPage {
  total_items: number;
  current_page: number;
  total_page: number;
}

export interface FilterVersionInfo {
  op: string;
  value: number;
}

export interface LastGradualTraffic {
  status: number;
  start_traffic: number;
  effective_traffic: number;
  end_traffic: number;
  traffic_record: TrafficRecord[];
  end_time: number;
  duration: number;
  type: number;
}

export enum LastGradualStatus {
  Running = 1,
  Pause = 2,
  Finish = 3,
}

export interface TrafficRecord {
  start_time: number;
  start_traffic: number;
  end_time: number;
  end_traffic: number;
}

export interface BetaCombineGroup {
  beta_combine_key: string;
  gray_flight?: Flight;
  release_flight?: Flight;
  all_flights: Flight[];
}

export interface ActualTrafficRecord {
  version_resource: number;
  timestamp: number;
}

export interface DimInfo {
  vals: {
    id: number;
    name: string;
  }[];
  id: number;
  name: string;
}

export interface FlightReportDataResult {
  dim: DimInfo;
  merge_data: any;
}

export interface Application {
  app_ids: string; // 123,456
  id: number;
  name: string;
  products: {
    token: string;
    application_id: number;
    id: number;
    product_name: string;
  }[];
}

// 获取指标结果 API：https://data.bytedance.net/dataopen/datatester/document/2118
export interface FlightReportRequest {
  start_date: string;
  end_date: string;
  data_region?: string;
  metric_group: number;
  selected_metric_ids?: string;
  confidence_threshold?: number;
  mult_cmp_corr?: number;
  view_type: string;
  combine?: number;
  merge_type: string;
  force_show?: number;
  data_caliber?: number;
  capping_corr?: number;
  days_before?: number;
  need_fallback?: boolean;
}

export interface LibraMetric {
  name: string;
  id: number;
  description: string;
  metric_key: string;
}

export interface MetricGroupMetaData {
  support_merge_types: string[];
  id: number;
  support_series_types: string[];
  metrics: LibraMetric[];
  owners: string[];
  dims: {
    name: string;
    id: number;
    support_query_type: string[];
    vals: {
      name: string;
      id: number;
    };
  }[];
}

export interface FlightReportResponse {
  preAA_alarm: any;
  merge_type: string;
  marked_rows: string[];
  end_date: string;
  recent_attention_time: string;
  available_end_date: string;
  support_fallback: boolean;
  flight_id: number;
  total_start_time: string;
  available_start_date: string;
  miss_data_date: string[];
  need_fallback: number;
  miss_data_pack?: {
    tx: string[];
    other: string[];
    eu_ttp: string[];
  };
  n_group: number;
  has_stats: number;
  merge_data: any;
  time_series_data?: any;
  start_date: string;
  data_status: number;
}

export interface UserHit {
  version_ids: number[];
  start_time: string;
  end_time: string;
  did: number;
  uid: number;
  hash_strategy: number;
}
