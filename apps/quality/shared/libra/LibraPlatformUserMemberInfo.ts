import { User } from '@pa/shared/dist/src/core';

export enum LibraPlatformPermission {
  View = 1 << 0, // 001 可查看
  Edit = 1 << 1, // 010 可编辑
  Grant = 1 << 2, // 100 可授权
  Create = 1 << 3, // 1000 可创建实验
  StatisticsEdit = 1 << 4, // 10000 可编辑实验统计信息
}

export enum LibraPlatformUserMemberUpdateType {
  Add = 1,
  Update = 2,
  Delete = 3,
}
export enum LibraPlatformRoleType {
  Guest = 0, // 无权限
  NomalUser = LibraPlatformPermission.View | LibraPlatformPermission.Create, // 普通成员
  ProductUser = LibraPlatformPermission.View | LibraPlatformPermission.Create | LibraPlatformPermission.StatisticsEdit, // 产品成员
  Admin = LibraPlatformPermission.View |
    LibraPlatformPermission.Edit |
    LibraPlatformPermission.Grant |
    LibraPlatformPermission.Create |
    LibraPlatformPermission.StatisticsEdit, // 管理员身份可授权、可编辑、可查看 31
}

export interface LibraPlatformUserMemberInfo {
  roleType: LibraPlatformRoleType;
  userInfo: User;
}

export interface LibraPlatformBusinessLineMemberInfo {
  businessLine: string;
  roleType: LibraPlatformRoleType;
}
