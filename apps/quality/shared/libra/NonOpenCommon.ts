import { ManageType } from '@shared/libra/NonOpen';
import { LibraRegion } from '@shared/libra/commonLibra';

export interface LibraAppInfo {
  id: number;
  appId: number;
  name: string;
  regions: string[];
  support_apps_field_value: string;
  is_utc: number;
  privileged?: boolean;
}

export const LIBRA_APP_ID_LV = 147;
export const LIBRA_APP_ID_CAPCUT = 305;
export const LIBRA_APP_ID_XINGTU = 255;
export const LIBRA_APP_ID_HYPIC = 367;
export const LIBRA_APP_ID_DREAMINA = 992;

export const SupportAppInfo: LibraAppInfo[] = [
  {
    id: LIBRA_APP_ID_LV,
    appId: 1775,
    name: '剪映',
    support_apps_field_value: 'e0ecrfhkb',
    is_utc: 0,
    regions: ['cn'],
  },
  {
    id: LIBRA_APP_ID_CAPCUT,
    appId: 3006,
    name: 'CapCut',
    support_apps_field_value: 'huzrzdapo',
    is_utc: 1,
    regions: ['sg'],
  },
  {
    id: LIBRA_APP_ID_XINGTU,
    appId: 2515,
    name: '醒图',
    support_apps_field_value: 'cy8tz6j9t',
    is_utc: 0,
    regions: ['cn'],
  },
  {
    id: LIBRA_APP_ID_HYPIC,
    appId: 7356,
    name: 'Hypic',
    support_apps_field_value: 'octz78eqj',
    is_utc: 1,
    regions: ['sg'],
  },
  {
    id: LIBRA_APP_ID_DREAMINA,
    appId: 581595,
    name: '即梦',
    support_apps_field_value: '7mq_tuj8g',
    is_utc: 0,
    regions: ['cn'],
  },
];

export const getAppInfo = (libraAppID?: number) => SupportAppInfo.find(v => v.id === libraAppID);

export function libraRegionFromAppId(appId: number): LibraRegion {
  const appInfo = SupportAppInfo.find(app => app.id === appId);
  if (appInfo) {
    return appInfo.regions.includes('sg') ? LibraRegion.SG : LibraRegion.CN;
  }
  return LibraRegion.UNKNOWN;
}

/**
 * 计划/实际上车版本
 */
export interface OnlineVersion {
  appid: number;
  platform: string;
  version: string;
}

/**
 * 这里定义实验类型额外信息
 *
 * TODO 根据需要增加字段
 */
export const ManageTypeExtra: Record<
  ManageType,
  {
    name: string;
  }
> = {
  [ManageType.Server]: { name: '服务端' },
  [ManageType.ABClientSDK]: { name: '客户端-AB客户端SDK' },
  [ManageType.product]: { name: '客户端-普通客户端' },
  [ManageType.SettingsClientNormal]: { name: '客户端-Settings普通客户端' },
  [ManageType.SettingsClientSDK]: { name: '客户端-SettingsSDK' },
  [ManageType.ABLocalClient]: { name: '客户端-本地分流' },
};

/**
 * 流量层
 *
 * TODO 根据需要增加参数
 */
export interface Layer {
  id: number;
  app_id: number;
  product_id: number;
  name: string;
  type: number;
  hash_unit: string;
  visible: boolean;
  available_traffic: number;
  priority: number;
  reusable: boolean;
  support_interleaving: boolean;
  support_traffic_turn: boolean;
  hash_strategy: string;
  has_apply: boolean;
  ticket_id: number;
  domain?: { id: string };
}

/**
 * 过滤条件
 * TODO 根据需要增加字段
 */
export interface ParamFilters {
  name: string;
  key: string;
  type: string;
  source: string;
  property_type: string;
  attribute: string;
  op: {
    label: string;
    value: string;
    mode: string;
    desc: string;
  }[];
  transformer: { name: string; value: string; desc: string }[];
  options: { label: string; value: string }[];
  bundles: {
    id: number;
    name: string;
    param_filter_id: number;
    display_name: string;
  }[];
}

export interface TemplateLayer {
  product_id: number;
  layer: number;
}

/**
 * 查询实验信息
 *
 * TODO 根据需要补充字段
 */
export interface Experiment {
  id: number;
  name: string;
  owners: { name: string }[];
  meego_info?: {
    meego_array: { meego_project_key: string; meego_project: string; meego_story: string; meego_libra: string }[];
  };
  duration: number;
  specified_psms: string[];
  description?: string;
  expectation?: string;
  manage_type: ManageType;
  app_id: number;
  product_id: number;
  layer: number;
  version_resource: number;
  book_version_resource?: number;
  versions: {
    name: string;
    description?: string;
    type: number;
    config: string;
    settings_keys?: {
      key: string;
      type: string;
      desc?: string;
      value: string;
    }[];
  }[];
  filter_rule?: {
    logic: string;
    conditions: {
      logic: string;
      condition: {
        key: string;
        op: string;
        value: boolean | number | number[] | string | string[];
        type: string;
        transformer?: string;
        hash_suffix?: string; // TODO
        mod_value?: string | number; // TODO
      };
    }[];
  }[];
  is_utc?: boolean;
  is_long_time_flight?: boolean;
  ad_flight_hit_rules?: unknown[];
  layer_info?: any;
  metrics?: {
    type: string;
    metric_group_id: number;
    name: string;
    bundle_id: number;
    metric_group_type: string;
    status: number;
    certification_type: string;
    dimensions: any[];
    non_echo: boolean;
  }[];
  filter_user_list: number;
  // 本地状态
  _layerOriginCopyDone?: boolean;
  _settingsKeyCopyDOne?: boolean;
}

export interface History {
  date: number;
  user: {
    avatar: string;
    id: string | number | undefined;
    name: string;
  };
  action: string;
  action_detail: Record<string, any>;
  budget_description?: string;
  op_reason?: string;
  op_type?: string;
}
