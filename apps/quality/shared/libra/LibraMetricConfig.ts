// 大盘核心指标配置
export const LT_CORE_METRICS_CONFIG: Record<string, any> = {
  // 进组累计大盘LT
  LT: {
    '1775': {
      metric_id: 902503,
      metric_group_id: 75223,
    },
    '3006': {
      metric_id: 598688,
      metric_group_id: 7020176,
    },
    '2515': {
      metric_id: 1784137,
      metric_group_id: 136156,
    },
    '7356': {
      metric_id: 896883,
      metric_group_id: 7039327,
    },
    '3704': {
      metric_id: 1564236,
      metric_group_id: 125275,
    },
    '359289': {
      metric_id: 812119,
      metric_group_id: 7035420,
    },
  },
  // 导出 LT
  ExportLT30: {
    '1775': {
      metric_id: 1218545,
      metric_group_id: 75223,
    },
    '3006': {
      metric_id: 747566,
      metric_group_id: 7020176,
    },
    '2515': {
      metric_id: 1818630,
      metric_group_id: 136156,
    },
    '7356': {
      metric_id: 909277,
      metric_group_id: 7039327,
    },
    '3704': {
      metric_id: 1564239,
      metric_group_id: 125275,
    },
    '359289': {
      metric_id: 812754,
      metric_group_id: 7035420,
    },
  },
  // 大盘导出量
  AllExport_U: {
    '1775': {
      metric_id: 1072932,
      metric_group_id: 88207,
    },
    '3006': {
      metric_id: 595957,
      metric_group_id: 7019961,
    },
    '2515': {
      metric_id: 1780528,
      metric_group_id: 143843,
    },
    '7356': {
      metric_id: 897778,
      metric_group_id: 7041900,
    },
    '3704': {
      metric_id: 1561195,
      metric_group_id: 125025,
    },
    '359289': {
      metric_id: 890329,
      metric_group_id: 7041363,
    },
  },
  // 大盘导出渗透
  AllExportUser_User: {
    '1775': {
      metric_id: 1072964,
      metric_group_id: 88208,
    },
    '3006': {
      metric_id: 700177,
      metric_group_id: 7019959,
    },
    '2515': {
      metric_id: 1780527,
      metric_group_id: 143843,
    },
    '7356': {
      metric_id: 897777,
      metric_group_id: 7041900,
    },
    '3704': {
      metric_id: 1561197,
      metric_group_id: 125025,
    },
    '359289': {
      metric_id: 890331,
      metric_group_id: 7041363,
    },
  },
};

// 性能核心指标配置
export const PERFORMANCE_CORE_METRICS_CONFIG: Record<string, { metricId: string; metricName: string }[]> = {
  '1775': [
    {
      metricId: '1757495',
      metricName: '崩溃用户率',
    },
    {
      metricId: '1757496',
      metricName: '人均崩溃次数',
    },
    {
      metricId: '1757497',
      metricName: '卡死用户率',
    },
    {
      metricId: '1757498',
      metricName: '人均卡死次数',
    },
    {
      metricId: '1757499',
      metricName: 'oom用户率',
    },
    {
      metricId: '1757500',
      metricName: '人均oom次数',
    },
    {
      metricId: '1757501',
      metricName: '启动耗时',
    },
    {
      metricId: '1757502',
      metricName: '磁盘占用均值',
    },
    {
      metricId: '1757503',
      metricName: '低磁盘占比',
    },
    {
      metricId: '1757504',
      metricName: '工具端帧率',
    },
    {
      metricId: '1757505',
      metricName: '工具端低帧率占比',
    },
    {
      metricId: '1757506',
      metricName: '剪同款预览帧率',
    },
    {
      metricId: '1757507',
      metricName: '剪同款低帧率占比',
    },
    {
      metricId: '1757508',
      metricName: '工具端导出时长率',
    },
    {
      metricId: '1757509',
      metricName: '剪同款导出时长率',
    },
    {
      metricId: '1757510',
      metricName: '工具端导出成功率',
    },
    {
      metricId: '1757511',
      metricName: '剪同款导出成功率',
    },
    {
      metricId: '1757512',
      metricName: '定帧刷新耗时',
    },
    {
      metricId: '1757513',
      metricName: 'Seek耗时',
    },
    {
      metricId: '1757514',
      metricName: '素材导入耗时',
    },
    {
      metricId: '1757515',
      metricName: '草稿加载耗时',
    },
    {
      metricId: '1757516',
      metricName: '素材导入失败率',
    },
    {
      metricId: '1757517',
      metricName: '剪同款导入成功率',
    },
    {
      metricId: '1757518',
      metricName: '剪同款导入耗时',
    },
    {
      metricId: '1757519',
      metricName: '模板发布成功率',
    },
    {
      metricId: '1757520',
      metricName: '模板发布耗时',
    },
    {
      metricId: '1757521',
      metricName: '二创发布成功率',
    },
    {
      metricId: '1757522',
      metricName: '锚点成功率',
    },
    {
      metricId: '1757523',
      metricName: '锚点耗时',
    },
    {
      metricId: '1757524',
      metricName: '内流首帧耗时',
    },
    {
      metricId: '1757525',
      metricName: '播放失败率',
    },
  ],
  '3006': [
    {
      metricId: '904376',
      metricName: '崩溃用户率',
    },
    {
      metricId: '904377',
      metricName: '人均崩溃次数',
    },
    {
      metricId: '904378',
      metricName: '卡死用户率',
    },
    {
      metricId: '904379',
      metricName: '人均卡死次数',
    },
    {
      metricId: '904380',
      metricName: 'oom用户率',
    },
    {
      metricId: '904381',
      metricName: '人均oom次数',
    },
    {
      metricId: '904382',
      metricName: '启动耗时',
    },
    {
      metricId: '904383',
      metricName: '磁盘占用均值',
    },
    {
      metricId: '904384',
      metricName: '磁盘低于500M用户的比例',
    },
    {
      metricId: '904385',
      metricName: '工具端预览帧率',
    },
    {
      metricId: '904386',
      metricName: '工具端帧率低于20帧的占比',
    },
    {
      metricId: '904387',
      metricName: '剪同款预览帧率',
    },
    {
      metricId: '904388',
      metricName: '剪同款帧率低于20帧的占比',
    },
    {
      metricId: '904389',
      metricName: '工具端导出时长率',
    },
    {
      metricId: '904390',
      metricName: '剪同款导出时长率',
    },
    {
      metricId: '904391',
      metricName: '工具端导出成功率',
    },
    {
      metricId: '904392',
      metricName: '剪同款导出成功率',
    },
    {
      metricId: '904393',
      metricName: '定帧刷新耗时',
    },
    {
      metricId: '904394',
      metricName: 'Seek耗时',
    },
    {
      metricId: '904395',
      metricName: '素材导入耗时',
    },
    {
      metricId: '904396',
      metricName: '草稿加载耗时',
    },
    {
      metricId: '904397',
      metricName: '素材导入失败率',
    },
    {
      metricId: '904398',
      metricName: '剪同款导入成功率',
    },
    {
      metricId: '904399',
      metricName: '剪同款导入耗时',
    },
    {
      metricId: '904400',
      metricName: '模板发布成功率',
    },
    {
      metricId: '904401',
      metricName: '模板发布耗时',
    },
    {
      metricId: '904402',
      metricName: '二创发布成功率',
    },
    {
      metricId: '904403',
      metricName: '锚点冷起成功率',
    },
    {
      metricId: '904404',
      metricName: '锚点冷启耗时',
    },
    {
      metricId: '904405',
      metricName: '内流首帧耗时',
    },
    {
      metricId: '904406',
      metricName: '播放失败率',
    },
  ],
};

// 所有核心指标（包括大盘核心指标 + 业务核心指标）
export const ALL_CORE_METRICS_CONFIG: Record<string, any> = {
  '1775': {
    app_name: '剪映 App',
    metric_groups: [
      {
        metric_group_id: 75223,
        metric_group_name: 'videocut active days CUPED',
        metric_list: [
          {
            metric_id: 902503,
            metric_name: 'ActiveDays/U',
            metric_desc: 'SUM(active_days) / COUNT(1)',
          },
          {
            metric_id: 1218545,
            metric_name: '导出LT30',
            metric_desc: '近30天总导出天数/进组用户数',
          },
          {
            metric_id: 1218548,
            metric_name: '模板导出LT30',
            metric_desc: '近30天剪同款导出天数/进组用户数',
          },
          {
            metric_id: 1218552,
            metric_name: '工具端导出LT30',
            metric_desc: '近30天工具端导出天数/进组用户数',
          },
        ],
      },
      {
        metric_group_id: 88207,
        metric_group_name: '大盘核心指标CUPED',
        metric_list: [
          {
            metric_id: 1072932,
            metric_name: 'AllExport/U',
            metric_desc: '全用户总导出次数',
          },
          {
            metric_id: 1072940,
            metric_name: 'EditExport/U',
            metric_desc: '全用户人均工具端导出成功次数',
          },
          {
            metric_id: 1072937,
            metric_name: 'TemplateExport/U',
            metric_desc: '全用户人均剪同款导出成功次数',
          },
        ],
      },
      {
        metric_group_id: 88208,
        metric_group_name: '导出核心指标CUPED',
        metric_list: [
          {
            metric_id: 1072964,
            metric_name: 'AllExportUser/User',
            metric_desc: '总导出用户渗透',
          },
          {
            metric_id: 1072947,
            metric_name: 'TemplateExportUser/User',
            metric_desc: '剪同款导出完成渗透',
          },
          {
            metric_id: 1072953,
            metric_name: 'EditExportUser/User',
            metric_desc: '工具端点击导出渗透',
          },
        ],
      },
      {
        metric_group_id: 136356,
        metric_group_name: 'CapCut剪映_商业化_核心指标组',
        metric_list: [
          {
            metric_id: 1685939,
            metric_name: '整体ARPU（GMV口径）',
            metric_desc: '分子 = 进组用户进组后累计贡献的GMV，分母 = 进组uv',
          },
          {
            metric_id: 1685941,
            metric_name: '非广告ARPU（GMV口径）',
            metric_desc: '分子 = 进组用户进组后累计贡献的非广告产品GMV，分母 = 进组uv',
          },
        ],
      },
      {
        metric_group_id: 136385,
        metric_group_name: 'CapCut剪映_商业化_会员订阅',
        metric_list: [
          {
            metric_id: 1685973,
            metric_name: '会员付费uv转化',
            metric_desc: '分子 = count(distinct [进组用户特征]的is_vip_hist=1 的user_id） 分母 = 进组用户数',
          },
          {
            metric_id: 1685974,
            metric_name: '会员新增付费uv转化',
            metric_desc: '分子 = count(distinct [进组用户特征]的is_vip_first=1 的user_id） 分母 = 进组用户数',
          },
          {
            metric_id: 1685982,
            metric_name: '工具端小项 ARPU（MECE）',
            metric_desc: '分子 = [进组用户]的vip_gmv_usd_1d_edit 分母 = 进组用户',
          },
          {
            metric_id: 1685986,
            metric_name: '云空间ARPU（MECE）',
            metric_desc: '分子 = [进组用户]的vip_cloud_gmv_usd 分母 = 进组用户',
          },
        ],
      },
      {
        metric_group_id: 130219,
        metric_group_name: '导出LT-拆解导出方式CUPED',
        metric_list: [
          {
            metric_id: 1665364,
            metric_name: '推荐模板导出lt天数',
            metric_desc: 'SUM(IF(feed_export_cnt > 0, 1, 0)) / User',
          },
          {
            metric_id: 1617915,
            metric_name: '搜索模板导出LT天数',
            metric_desc: 'SUM(IF(search_template_export_success_cnt > 0, 1, 0)) / User',
          },
        ],
      },
      {
        metric_group_id: 136433,
        metric_group_name: '广告核心指标组CUPED',
        metric_list: [
          {
            metric_id: 1686699,
            metric_name: '累计ARPU(单位:¥1/1000)',
            metric_desc: '实验期间累计广告收入/实验期间累计进组用户数,sum(cost)/count(distinct id)',
          },
        ],
      },
      {
        metric_group_id: 131177,
        metric_group_name: '作者投稿与收入指标组CUPED',
        metric_list: [
          {
            metric_id: 1629144,
            metric_name: '全人均投稿量(可分发)',
            metric_desc: '进组用户人均投稿(为可分发投稿）',
          },
        ],
      },
      {
        metric_group_id: 129737,
        metric_group_name: '作者活跃指标组CUPED',
        metric_list: [
          {
            metric_id: 1623801,
            metric_name: '投稿活跃天天数',
            metric_desc: 'SUM(IF(is_publish_td > 0, 1, 0)) / User',
          },
        ],
      },
      {
        metric_group_id: 60764,
        metric_group_name: 'VC-CORE-搜索基础指标',
        metric_list: [
          {
            metric_id: 1716950,
            metric_name: '搜索结果导出渗透',
            metric_desc: 'COUNT(IF(search_export_cnt > 0, 1, NULL)) / COUNT(1)',
          },
          {
            metric_id: 1716951,
            metric_name: '人均导出次数',
            metric_desc: 'SUM(COALESCE(search_export_cnt,0)) / COUNT(IF(search_export_cnt > 0, 1, NULL))',
          },
        ],
      },
      {
        metric_group_id: 153442,
        metric_group_name: '工具首页改版-小工具/新建项目/草稿模块曝光、点击和导出',
        metric_list: [
          {
            metric_id: 1876284,
            metric_name: '小工具导出渗透',
            metric_desc:
              '导出首页小工具，包括一键成片、美颜美体、智能调色、防抖、视频降噪、超清视频、超清图片、字幕识别、AI配音、图文成片、脚本模板、营销视频、提词器、拍摄、录屏、一起拍、数字人、视频变速、AI扩图、智能抠图、AI作图、图片编辑、AI商品图、视频翻译',
          },
          {
            metric_id: 1876287,
            metric_name: '新建项目导出渗透',
            metric_desc: 'export_time',
          },
          {
            metric_id: 1877971,
            metric_name: '草稿导出渗透',
            metric_desc: 'home_page_draft_export',
          },
          {
            metric_id: 1876283,
            metric_name: '小工具点击渗透',
            metric_desc: 'edit_tool_action',
          },
          {
            metric_id: 1876286,
            metric_name: '新建项目点击渗透',
            metric_desc: 'click_home_new',
          },
          {
            metric_id: 1877959,
            metric_name: '草稿点击渗透',
            metric_desc: '点击剪辑和资产tab草稿',
          },
        ],
      },
      {
        metric_group_id: 124003,
        metric_group_name: '模板商业化核心指标组',
        metric_list: [
          {
            metric_id: 1547174,
            metric_name: 'arpu',
            metric_desc: '（付费模板gmv+草稿解锁模板gmv+商业模板gmv）/ 累计进组用户',
          },
        ],
      },
      {
        metric_group_id: 133275,
        metric_group_name: '作者发布模板（进组后发布）消费指标组CUPED',
        metric_list: [
          {
            metric_id: 1653789,
            metric_name: '导出量(30天)',
            metric_desc: 'sum(模板发布后30天导出量)/进组用户数，发布后30天',
          },
          {
            metric_id: 1653795,
            metric_name: '抖音拉活量(发布30天)',
            metric_desc: 'sum(模板发布后30天抖音拉活量)/进组用户数，30天',
          },
        ],
      },
      {
        metric_group_id: 149839,
        metric_group_name: '模板商业化收益（作者侧）CUPED',
        metric_list: [
          {
            metric_id: 1850126,
            metric_name: '模板GMV/U',
            metric_desc: '包含导出付费、草稿解锁、使用付费，不包含广告，不拆分会员和单次购买',
          },
          {
            metric_id: 1850127,
            metric_name: '模板订阅GMV/U',
            metric_desc: '包含导出付费、草稿解锁、使用付费，仅限会员收入',
          },
        ],
      },
    ],
  },
  '3006': {
    app_name: 'CapCut App',
    metric_groups: [
      {
        metric_group_id: 7020176,
        metric_group_name: '活跃天数（回到app）CUPED',
        metric_list: [
          {
            metric_id: 598688,
            metric_name: '活跃天数',
            metric_desc: 'SUM(1) / User',
          },
          {
            metric_id: 747566,
            metric_name: '人均导出天数',
            metric_desc: '人均导出天数',
          },
          {
            metric_id: 747558,
            metric_name: '人均剪同款导出天数',
            metric_desc: '人均剪同款导出天数',
          },
          {
            metric_id: 747560,
            metric_name: '人均工具端导出天数',
            metric_desc: '人均工具端导出天数',
          },
        ],
      },
      {
        metric_group_id: 7019961,
        metric_group_name: '大盘核心指标组CUPED',
        metric_list: [
          {
            metric_id: 595957,
            metric_name: 'AllExport/U',
            metric_desc: '全用户总导出次数',
          },
          {
            metric_id: 595965,
            metric_name: 'EditExport/U',
            metric_desc: '全用户人均工具端导出成功次数',
          },
          {
            metric_id: 595962,
            metric_name: 'TemplateExport/U',
            metric_desc: '全用户人均剪同款导出成功次数',
          },
        ],
      },
      {
        metric_group_id: 7019959,
        metric_group_name: '导出核心指标CUPED',
        metric_list: [
          {
            metric_id: 700177,
            metric_name: 'AllExportUser/User',
            metric_desc: '总导出用户渗透',
          },
          {
            metric_id: 700159,
            metric_name: 'TemplateExportUser/User',
            metric_desc: '剪同款导出完成渗透',
          },
          {
            metric_id: 700165,
            metric_name: 'EditExportUser/User',
            metric_desc: '工具端点击导出渗透',
          },
        ],
      },
      {
        metric_group_id: 7038648,
        metric_group_name: '【广告收入导致总体不可看】CapCut剪映_商业化_核心指标组',
        metric_list: [
          {
            metric_id: 855691,
            metric_name: '整体ARPU（GMV口径）',
            metric_desc: '分子 = 进组用户进组后累计贡献的GMV；分母 = 进组uv',
          },
          {
            metric_id: 855693,
            metric_name: '非广告ARPU（GMV口径）',
            metric_desc: '分子 = 进组用户进组后累计贡献的非广告产品GMV；分母 = 进组uv',
          },
        ],
      },
      {
        metric_group_id: 7038649,
        metric_group_name: 'CapCut剪映_商业化_会员订阅',
        metric_list: [
          {
            metric_id: 855704,
            metric_name: '会员付费uv转化',
            metric_desc: '分子 = count(distinct [进组用户特征]的is_vip_hist=1 的user_id）；分母 = 进组用户数',
          },
          {
            metric_id: 855705,
            metric_name: '会员新增付费uv转化',
            metric_desc: '分子 = count(distinct [进组用户特征]的is_vip_first=1 的user_id）；分母 = 进组用户数',
          },
          {
            metric_id: 855713,
            metric_name: '工具端小项 ARPU（MECE）',
            metric_desc: '分子 = [进组用户]的vip_gmv_usd_1d_edit；分母 = 进组用户',
          },
          {
            metric_id: 855717,
            metric_name: '云空间ARPU（MECE）',
            metric_desc: '分子 = [进组用户]的vip_cloud_gmv_usd；分母 = 进组用户',
          },
        ],
      },
      {
        metric_group_id: 7035872,
        metric_group_name: '导出LT-拆解导出方式CUPED',
        metric_list: [
          {
            metric_id: 818519,
            metric_name: '推荐模板导出lt Days',
            metric_desc: 'SUM(IF(is_template_feed_export_cnt > 0, 1, 0)) / User',
          },
          {
            metric_id: 818523,
            metric_name: '搜索模板导出LT Days',
            metric_desc: 'SUM(IF(is_template_search_export_cnt > 0, 1, 0)) / User',
          },
        ],
      },
      {
        metric_group_id: 7036620,
        metric_group_name: '广告核心指标组CUPED',
        metric_list: [
          {
            metric_id: 829317,
            metric_name: 'ARPU(预估,单位:$1/1000)',
            metric_desc:
              '=累计广告收入(预估)/累计进组用户数。累计广告收入(预估)=累计广告show(预估)*os/country/ad_scene对应大盘cpm',
          },
        ],
      },
      {
        metric_group_id: 7034801,
        metric_group_name: '【仅支持uid分流实验】作者指标组1.0',
        metric_list: [
          {
            metric_id: 861819,
            metric_name: '投稿量',
            metric_desc: '',
          },
          {
            metric_id: 861818,
            metric_name: '投稿活跃天',
            metric_desc: '',
          },
          {
            metric_id: 918516,
            metric_name: '全人均有效模板投稿量',
            metric_desc: '有效模板投稿pv/进组uv',
          },
          {
            metric_id: 802356,
            metric_name: '全球导出量30d',
            metric_desc: '发布后30d全球导出量，限制create_date>=最小进组时间',
          },
          {
            metric_id: 802358,
            metric_name: '全球TT拉活量7d',
            metric_desc: '限制create_date>=最小进组时间',
          },
        ],
      },
      {
        metric_group_id: 7043792,
        metric_group_name: '',
        metric_list: [
          {
            metric_id: 918730,
            metric_name: '模板GMV/U',
            metric_desc: '包含导出付费、草稿解锁、使用付费，不包含广告，不拆分会员和单次购买',
          },
          {
            metric_id: 918731,
            metric_name: '模板订阅GMV/U',
            metric_desc: '包含导出付费、草稿解锁、使用付费，仅限会员收入',
          },
        ],
      },
      {
        metric_group_id: 7042535,
        metric_group_name: '主框架-首页各模块点击与导出',
        metric_list: [
          {
            metric_id: 905280,
            metric_name: '小工具导出渗透',
            metric_desc:
              'export_edit_homepage_small_tool_zkj:导出首页小工具(for主框架首页改版评估)，包含Magic Video/Transcript-based editor/Smarts Ads等22个小工具',
          },
          {
            metric_id: 905282,
            metric_name: '新建项目导出渗透',
            metric_desc: 'export_time:导出耗时和结果',
          },
          {
            metric_id: 905284,
            metric_name: '草稿导出渗透',
            metric_desc: 'edit_tab_export_draft:工具tab导出草稿，包括剪辑草稿和剪同款草稿，拍摄器和Ads草稿导出埋点有误',
          },
          {
            metric_id: 905279,
            metric_name: '小工具点击渗透',
            metric_desc: 'edit_tool_action:剪辑tab操作',
          },
          {
            metric_id: 905281,
            metric_name: '新建项目点击渗透',
            metric_desc: 'click_home_new:点击新建项目',
          },
          {
            metric_id: 905283,
            metric_name: '草稿点击渗透',
            metric_desc: 'click_home_drafts_material:点击草稿箱中素材拉起编辑面板(草稿箱非管理状态下)',
          },
        ],
      },
      {
        metric_group_id: 7019958,
        metric_group_name: '模板核心指标CUPED',
        metric_list: [
          {
            metric_id: 743871,
            metric_name: '推荐导出渗透',
            metric_desc: '剪同款Feed导出成功uv/累计进组用户数',
          },
          {
            metric_id: 743873,
            metric_name: '推荐人均导出次数',
            metric_desc: '剪同款Feed导出成功pv/累计进组用户数',
          },
          {
            metric_id: 743867,
            metric_name: '搜索导出渗透率',
            metric_desc: '搜索结果页导出成功uv/累计进组用户数',
          },
          {
            metric_id: 743870,
            metric_name: '人均搜索导出次数',
            metric_desc: '搜索结果页导出成功pv/累计进组用户数',
          },
        ],
      },
      {
        metric_group_id: 7034418,
        metric_group_name: '付费/草稿解锁模板商业化核心指标组',
        metric_list: [
          {
            metric_id: 796710,
            metric_name: 'arpu',
            metric_desc: '（付费模板gmv+草稿解锁gmv）/ 累计进组用户',
          },
        ],
      },
    ],
  },
  '2515': {
    app_name: '醒图 App',
    metric_groups: [
      {
        metric_group_id: 136156,
        metric_group_name: '[回溯中]【大盘】活跃天数(回到APP) - CUPED',
        metric_list: [
          {
            metric_id: 1784137,
            metric_name: '活跃天数',
            metric_desc: 'SUM(IF(is_dau > 0, 1, 0)) / User',
          },
          {
            metric_id: 1818630,
            metric_name: '导出天数',
            metric_desc: 'SUM(IF(export_success_cnt > 0, 1, 0)) / User',
          },
        ],
      },
      {
        metric_group_id: 143843,
        metric_group_name: '【大盘】内容编辑核心指标组 - CUPED',
        metric_list: [
          {
            metric_id: 1780528,
            metric_name: 'Export/U',
            metric_desc: '导出全人均次数',
          },
          {
            metric_id: 1780527,
            metric_name: 'ExportUser/U',
            metric_desc: '导出渗透',
          },
        ],
      },
      {
        metric_group_id: 76257,
        metric_group_name: '醒图商业化指标组(大盘)',
        metric_list: [
          {
            metric_id: 917987,
            metric_name: '订阅渗透',
            metric_desc: '订阅渗透',
          },
          {
            metric_id: 1342222,
            metric_name: '主动付费渗透',
            metric_desc: '主动付费渗透',
          },
          {
            metric_id: 917995,
            metric_name: '全人均订阅净收入',
            metric_desc: '全人均订阅净收入',
          },
        ],
      },
      {
        metric_group_id: 136438,
        metric_group_name: '[迭代中]广告核心指标组CUPED',
        metric_list: [
          {
            metric_id: 1686732,
            metric_name: '累计ARPU(单位:¥1/1000)',
            metric_desc: '实验期间累计广告收入/实验期间累计进组用户数,sum(cost)/count(distinct id)',
          },
        ],
      },
      {
        metric_group_id: 157124,
        metric_group_name: 'Cuped-素材小项核心指标组',
        metric_list: [
          {
            metric_id: 1906242,
            metric_name: '素材导出渗透 | Material Export Penetration',
            metric_desc: '素材导出uv/活跃人数',
          },
          {
            metric_id: 1906243,
            metric_name: '素材导出全人均次数 | Average Material Exports per Capita',
            metric_desc: '素材导出pv/活跃人数',
          },
        ],
      },
      {
        metric_group_id: 157122,
        metric_group_name: 'Cuped-拆编辑器小项活跃导出天数',
        metric_list: [
          {
            metric_id: 1906174,
            metric_name: '人均素材导出天数 | Material Export Days',
            metric_desc: '素材导出天数/活跃uv',
          },
        ],
      },
      {
        metric_group_id: 157120,
        metric_group_name: 'Cuped-拆工具场景核心指标组',
        metric_list: [
          {
            metric_id: 1906196,
            metric_name: '模板导出渗透',
            metric_desc: '模板导出图片人数/活跃人数',
          },
          {
            metric_id: 1906197,
            metric_name: '模板导出全人均次数',
            metric_desc: '模板导出图片次数/活跃人数',
          },
        ],
      },
      {
        metric_group_id: 157121,
        metric_group_name: 'Cuped-拆工具场景活跃导出天数',
        metric_list: [
          {
            metric_id: 1906207,
            metric_name: '人均模板导出天数 | Template Export Days',
            metric_desc: '模板导出天数/活跃uv',
          },
        ],
      },
    ],
  },
  '7356': {
    app_name: 'Hypic App',
    metric_groups: [
      {
        metric_group_id: 7039327,
        metric_group_name: '【大盘】活跃天数(回到APP) - CUPED',
        metric_list: [
          {
            metric_id: 896883,
            metric_name: 'APP活跃 Days',
            metric_desc: 'SUM(IF(is_dau > 0, 1, 0)) / User',
          },
          {
            metric_id: 909277,
            metric_name: '导出 Days',
            metric_desc: 'SUM(IF(export_success_cnt > 0, 1, 0)) / User',
          },
        ],
      },
      {
        metric_group_id: 7041900,
        metric_group_name: '【大盘】内容编辑核心指标组 - CUPED',
        metric_list: [
          {
            metric_id: 897778,
            metric_name: 'Export/U',
            metric_desc: '导出全人均次数',
          },
          {
            metric_id: 897777,
            metric_name: 'ExportUser/U',
            metric_desc: '导出全人均次数',
          },
        ],
      },
      {
        metric_group_id: 7045810,
        metric_group_name: 'Cuped-素材小项核心指标组 | Cuped-Core Metrics for Material Sub-items',
        metric_list: [
          {
            metric_id: 937899,
            metric_name: '素材导出渗透 | Material Export Penetration',
            metric_desc: '素材导出uv/活跃人数',
          },
          {
            metric_id: 937900,
            metric_name: '素材导出全人均次数 | Average Material Exports per Capita',
            metric_desc: '素材导出pv/活跃人数',
          },
        ],
      },
      {
        metric_group_id: 7045805,
        metric_group_name: 'Cuped-拆编辑器小项活跃导出天数 | Cuped-Active Export Days by Editor Item',
        metric_list: [
          {
            metric_id: 937841,
            metric_name: '人均素材导出天数 | Material Export Days',
            metric_desc: '素材导出天数/活跃uv',
          },
        ],
      },
      {
        metric_group_id: 7045808,
        metric_group_name: 'Cuped-拆工具场景核心指标组 | Cuped-Core Metrics by Tool',
        metric_list: [
          {
            metric_id: 937863,
            metric_name: '模板导出渗透',
            metric_desc: '模板导出图片人数/活跃人数',
          },
          {
            metric_id: 937864,
            metric_name: '模板导出全人均次数',
            metric_desc: '模板导出图片次数/活跃人数',
          },
        ],
      },
      {
        metric_group_id: 7045807,
        metric_group_name: 'Cuped-拆工具场景活跃导出天数 | Cuped-Active Export Days by Tool',
        metric_list: [
          {
            metric_id: 937874,
            metric_name: '人均模板导出天数',
            metric_desc: '模板导出天数/活跃uv',
          },
        ],
      },
    ],
  },
  '3704': {
    app_name: '剪映 PC',
    metric_groups: [
      {
        metric_group_id: 125275,
        metric_group_name: '活跃天数_剪映专业版',
        metric_list: [
          {
            metric_id: 1564236,
            metric_name: 'is_dau天数',
            metric_desc: 'SUM(IF(is_dau > 0, 1, 0)) / User',
          },
          {
            metric_id: 1564239,
            metric_name: 'is_dau_export天数',
            metric_desc: 'SUM(IF(is_dau_export > 0, 1, 0)) / User',
          },
        ],
      },
      {
        metric_group_id: 125025,
        metric_group_name: '核心指标',
        metric_list: [
          {
            metric_id: 1561195,
            metric_name: '全用户人均导出成功次数',
            metric_desc: '全用户人均导出成功次数',
          },
          {
            metric_id: 1561197,
            metric_name: '导出成功渗透',
            metric_desc: '导出成功渗透',
          },
          {
            metric_id: 1561198,
            metric_name: 'arpu',
            metric_desc: 'GMV/进组用户数',
          },
        ],
      },
      {
        metric_group_id: 85632,
        metric_group_name: '货币化商业化-付费业务订单量及付费渗透',
        metric_list: [
          {
            metric_id: 1547001,
            metric_name: '会员渗透',
            metric_desc: 'vip_order_cnt_user_ratio',
          },
          {
            metric_id: 1547002,
            metric_name: '新增会员渗透',
            metric_desc: 'vip_first_buy_order_cnt_user_ratio',
          },
        ],
      },
    ],
  },
  '359289': {
    app_name: 'CapCut PC',
    metric_groups: [
      {
        metric_group_id: 7035420,
        metric_group_name: '活跃天数_capcut_pc',
        metric_list: [
          {
            metric_id: 812119,
            metric_name: 'is_dau天数',
            metric_desc: 'SUM(IF(is_dau > 0, 1, 0)) / User',
          },
          {
            metric_id: 812754,
            metric_name: 'is_dau_export天数',
            metric_desc: 'SUM(IF(is_dau_export > 0, 1, 0)) / User',
          },
        ],
      },
      {
        metric_group_id: 7041363,
        metric_group_name: '核心指标CUPED',
        metric_list: [
          {
            metric_id: 890329,
            metric_name: '全用户人均导出成功次数',
            metric_desc: '全用户人均导出成功次数',
          },
          {
            metric_id: 890331,
            metric_name: '导出成功渗透',
            metric_desc: '导出成功渗透',
          },
          {
            metric_id: 890332,
            metric_name: 'arpu',
            metric_desc: 'GMV/进组用户数',
          },
          {
            metric_id: 890333,
            metric_name: '付费转化率',
            metric_desc: '付费UV/进组用户数',
          },
        ],
      },
    ],
  },
  '1128': {
    app_name: '抖音 App',
    metric_groups: [
      {
        metric_group_id: 82662,
        metric_group_name: '抖音x影像-影像端日活数据V2',
        metric_list: [
          {
            metric_id: 1002824,
            metric_name: '剪映端日活天级渗透',
            metric_desc: '剪映DAU / 进组用户数',
          },
        ],
      },
      {
        metric_group_id: 114968,
        metric_group_name: '抖音/抖极/新火山-剪映投稿导出指标组',
        metric_list: [
          {
            metric_id: 1423980,
            metric_name: '剪映工具端投稿视频pv/au',
            metric_desc: '剪映工具端投稿视频数/进组用户数',
          },
          {
            metric_id: 1423982,
            metric_name: '剪映剪同款投稿视频pv/au',
            metric_desc: '剪映剪同款投稿视频数/进组用户数',
          },
          {
            metric_id: 1423974,
            metric_name: '剪同款成功导出pv/au',
            metric_desc: '剪同款成功导出视频次数/进组用户',
          },
          {
            metric_id: 1423972,
            metric_name: '工具端成功导出视频pv/au',
            metric_desc: '工具端成功导出视频次数/进组用户',
          },
        ],
      },
    ],
  },
  '1180': {
    app_name: 'TikTok App',
    metric_groups: [
      {
        metric_group_id: 7028097,
        metric_group_name: '7028097',
        metric_list: [
          {
            metric_id: 707473,
            metric_name: 'CC_Active_Users',
            metric_desc: 'CC_Active_Users',
          },
          {
            metric_id: 814302,
            metric_name: 'avg_template_export_cnt',
            metric_desc: 'avg_template_export_cnt',
          },
          {
            metric_id: 814303,
            metric_name: 'avg_tool_export_cnt',
            metric_desc: 'avg_tool_export_cnt',
          },
        ],
      },
      {
        metric_group_id: 7036820,
        metric_group_name: '7036820',
        metric_list: [
          {
            metric_id: 832043,
            metric_name: 'CapCut_edit_pub_cnt/User',
            metric_desc: 'CapCut_edit_pub_cnt/User',
          },
          {
            metric_id: 832044,
            metric_name: 'CapCut_template_pub_cnt/User',
            metric_desc: 'CapCut_template_pub_cnt/User',
          },
        ],
      },
      {
        metric_group_id: 7036831,
        metric_group_name: '7036831',
        metric_list: [
          {
            metric_id: 832263,
            metric_name: 'inspire_cctemplate_pubitem/dau',
            metric_desc: 'inspire_cctemplate_pubitem/dau',
          },
          {
            metric_id: 832264,
            metric_name: 'inspire_cctemplate_pubuv/dau',
            metric_desc: 'inspire_cctemplate_pubuv/dau',
          },
        ],
      },
    ],
  },
};

// 指标收益计算规则（端收益、模块收益、关键过程收益）
export const LIBRA_METRICS_BENEFITS_RULE_CONFIG: Record<string, any> = {
  PlatformRevenue: {
    体验类: {
      进组累计大盘LT: {
        '1775': {
          metric_id: 902503,
          metric_group_id: 75223,
        },
        '3006': {
          metric_id: 598688,
          metric_group_id: 7020176,
        },
        '2515': {
          metric_id: 1784137,
          metric_group_id: 136156,
        },
        '7356': {
          metric_id: 896883,
          metric_group_id: 7039327,
        },
        '3704': {
          metric_id: 1564236,
          metric_group_id: 125275,
        },
        '359289': {
          metric_id: 812119,
          metric_group_id: 7035420,
        },
      },
      导出LT: {
        '1775': {
          metric_id: 1218545,
          metric_group_id: 75223,
        },
        '3006': {
          metric_id: 747566,
          metric_group_id: 7020176,
        },
        '2515': {
          metric_id: 1818630,
          metric_group_id: 136156,
        },
        '7356': {
          metric_id: 909277,
          metric_group_id: 7039327,
        },
        '3704': {
          metric_id: 1564239,
          metric_group_id: 125275,
        },
        '359289': {
          metric_id: 812754,
          metric_group_id: 7035420,
        },
      },
      大盘导出量: {
        '1775': {
          metric_id: 1072932,
          metric_group_id: 88207,
        },
        '3006': {
          metric_id: 595957,
          metric_group_id: 7019961,
        },
        '2515': {
          metric_id: 1780528,
          metric_group_id: 143843,
        },
        '7356': {
          metric_id: 897778,
          metric_group_id: 7041900,
        },
        '3704': {
          metric_id: 1561195,
          metric_group_id: 125025,
        },
        '359289': {
          metric_id: 890329,
          metric_group_id: 7041363,
        },
      },
      大盘导出渗透: {
        '1775': {
          metric_id: 1072964,
          metric_group_id: 88208,
        },
        '3006': {
          metric_id: 700177,
          metric_group_id: 7019959,
        },
        '2515': {
          metric_id: 1780527,
          metric_group_id: 143843,
        },
        '7356': {
          metric_id: 897777,
          metric_group_id: 7041900,
        },
        '3704': {
          metric_id: 1561197,
          metric_group_id: 125025,
        },
        '359289': {
          metric_id: 890331,
          metric_group_id: 7041363,
        },
      },
    },
    商业化: {
      ARPU: {
        '1775': {
          metric_id: 1685939,
          metric_group_id: 136356,
        },
        '3006': {
          metric_id: 855691,
          metric_group_id: 7038648,
        },
        '2515': {
          metric_id: 0,
          metric_group_id: 0,
        },
        '7356': {
          metric_id: 0,
          metric_group_id: 0,
        },
        '3704': {
          metric_id: 1561198,
          metric_group_id: 125025,
        },
        '359289': {
          metric_id: 890332,
          metric_group_id: 7041363,
        },
      },
      广告ARPU: {
        '1775': {
          metric_id: 1686699,
          metric_group_id: 136433,
        },
        '3006': {
          metric_id: 829317,
          metric_group_id: 7036620,
        },
        '2515': {
          metric_id: 1686732,
          metric_group_id: 136438,
        },
        '7356': {
          metric_id: 0,
          metric_group_id: 0,
        },
        '3704': {
          metric_id: 0,
          metric_group_id: 0,
        },
        '359289': {
          metric_id: 0,
          metric_group_id: 0,
        },
      },
      非广告ARPU: {
        '1775': {
          metric_id: 1685941,
          metric_group_id: 136356,
        },
        '3006': {
          metric_id: 855693,
          metric_group_id: 7038648,
        },
        '2515': {
          metric_id: 917995,
          metric_group_id: 76257,
        },
        '7356': {
          metric_id: 0,
          metric_group_id: 0,
        },
        '3704': {
          metric_id: 0,
          metric_group_id: 0,
        },
        '359289': {
          metric_id: 0,
          metric_group_id: 0,
        },
      },
      会员付费转化: {
        '1775': {
          metric_id: 1685973,
          metric_group_id: 136385,
        },
        '3006': {
          metric_id: 855704,
          metric_group_id: 7038649,
        },
        '2515': {
          metric_id: 917987,
          metric_group_id: 76257,
        },
        '7356': {
          metric_id: 0,
          metric_group_id: 0,
        },
        '3704': {
          metric_id: 1547001,
          metric_group_id: 85632,
        },
        '359289': {
          metric_id: 890333,
          metric_group_id: 7041363,
        },
      },
      会员新增付费转化: {
        '1775': {
          metric_id: 1685974,
          metric_group_id: 136385,
        },
        '3006': {
          metric_id: 855705,
          metric_group_id: 7038649,
        },
        '2515': {
          metric_id: 1342222,
          metric_group_id: 76257,
        },
        '7356': {
          metric_id: 0,
          metric_group_id: 0,
        },
        '3704': {
          metric_id: 1547002,
          metric_group_id: 85632,
        },
        '359289': {
          metric_id: 0,
          metric_group_id: 0,
        },
      },
    },
    UG: {
      双持dau渗透: {
        '1128': {
          metric_id: 1002824,
          metric_group_id: 82662,
        },
        '1180': {
          metric_id: 707473,
          metric_group_id: 7028097,
        },
      },
    },
  },
  ModuleRevenue: {
    视频工具: {
      编辑器导出渗透: {
        '1775': {
          metric_id: 1072953,
          metric_group_id: 88208,
        },
        '3006': {
          metric_id: 700165,
          metric_group_id: 7019959,
        },
        '2515': {
          metric_id: 0,
          metric_group_id: 0,
        },
        '7356': {
          metric_id: 0,
          metric_group_id: 0,
        },
        '3704': {
          metric_id: 0,
          metric_group_id: 0,
        },
        '359289': {
          metric_id: 0,
          metric_group_id: 0,
        },
      },
      编辑器导出次数: {
        '1775': {
          metric_id: 1072940,
          metric_group_id: 88207,
        },
        '3006': {
          metric_id: 595965,
          metric_group_id: 7019961,
        },
        '2515': {
          metric_id: 0,
          metric_group_id: 0,
        },
        '7356': {
          metric_id: 0,
          metric_group_id: 0,
        },
        '3704': {
          metric_id: 0,
          metric_group_id: 0,
        },
        '359289': {
          metric_id: 0,
          metric_group_id: 0,
        },
      },
      编辑器导出LT: {
        '1775': {
          metric_id: 1218552,
          metric_group_id: 75223,
        },
        '3006': {
          metric_id: 747560,
          metric_group_id: 7020176,
        },
        '2515': {
          metric_id: 0,
          metric_group_id: 0,
        },
        '7356': {
          metric_id: 0,
          metric_group_id: 0,
        },
        '3704': {
          metric_id: 0,
          metric_group_id: 0,
        },
        '359289': {
          metric_id: 0,
          metric_group_id: 0,
        },
      },
    },
    模板: {
      模板导出渗透: {
        '1775': {
          metric_id: 1072947,
          metric_group_id: 88208,
        },
        '3006': {
          metric_id: 700159,
          metric_group_id: 7019959,
        },
        '2515': {
          metric_id: 0,
          metric_group_id: 0,
        },
        '7356': {
          metric_id: 0,
          metric_group_id: 0,
        },
        '3704': {
          metric_id: 0,
          metric_group_id: 0,
        },
        '359289': {
          metric_id: 0,
          metric_group_id: 0,
        },
      },
      模板导出次数: {
        '1775': {
          metric_id: 1072937,
          metric_group_id: 88207,
        },
        '3006': {
          metric_id: 595962,
          metric_group_id: 7019961,
        },
        '2515': {
          metric_id: 0,
          metric_group_id: 0,
        },
        '7356': {
          metric_id: 0,
          metric_group_id: 0,
        },
        '3704': {
          metric_id: 0,
          metric_group_id: 0,
        },
        '359289': {
          metric_id: 0,
          metric_group_id: 0,
        },
      },
      模板导出LT: {
        '1775': {
          metric_id: 1218548,
          metric_group_id: 75223,
        },
        '3006': {
          metric_id: 747558,
          metric_group_id: 7020176,
        },
        '2515': {
          metric_id: 0,
          metric_group_id: 0,
        },
        '7356': {
          metric_id: 0,
          metric_group_id: 0,
        },
        '3704': {
          metric_id: 0,
          metric_group_id: 0,
        },
        '359289': {
          metric_id: 0,
          metric_group_id: 0,
        },
      },
      搜索导出渗透: {
        '1775': {
          metric_id: 1716950,
          metric_group_id: 60764,
        },
        '3006': {
          metric_id: 743867,
          metric_group_id: 7019958,
        },
        '2515': {
          metric_id: 0,
          metric_group_id: 0,
        },
        '7356': {
          metric_id: 0,
          metric_group_id: 0,
        },
        '3704': {
          metric_id: 0,
          metric_group_id: 0,
        },
        '359289': {
          metric_id: 0,
          metric_group_id: 0,
        },
      },
      搜索导出次数: {
        '1775': {
          metric_id: 1716951,
          metric_group_id: 60764,
        },
        '3006': {
          metric_id: 743870,
          metric_group_id: 7019958,
        },
        '2515': {
          metric_id: 0,
          metric_group_id: 0,
        },
        '7356': {
          metric_id: 0,
          metric_group_id: 0,
        },
        '3704': {
          metric_id: 0,
          metric_group_id: 0,
        },
        '359289': {
          metric_id: 0,
          metric_group_id: 0,
        },
      },
      搜索导出LT: {
        '1775': {
          metric_id: 1617915,
          metric_group_id: 130219,
        },
        '3006': {
          metric_id: 818523,
          metric_group_id: 7035872,
        },
        '2515': {
          metric_id: 0,
          metric_group_id: 0,
        },
        '7356': {
          metric_id: 0,
          metric_group_id: 0,
        },
        '3704': {
          metric_id: 0,
          metric_group_id: 0,
        },
        '359289': {
          metric_id: 0,
          metric_group_id: 0,
        },
      },
      推荐导出渗透: {
        '1775': {
          metric_id: 0,
          metric_group_id: 0,
        },
        '3006': {
          metric_id: 743871,
          metric_group_id: 7019958,
        },
        '2515': {
          metric_id: 0,
          metric_group_id: 0,
        },
        '7356': {
          metric_id: 0,
          metric_group_id: 0,
        },
        '3704': {
          metric_id: 0,
          metric_group_id: 0,
        },
        '359289': {
          metric_id: 0,
          metric_group_id: 0,
        },
      },
      推荐导出次数: {
        '1775': {
          metric_id: 0,
          metric_group_id: 0,
        },
        '3006': {
          metric_id: 743873,
          metric_group_id: 7019958,
        },
        '2515': {
          metric_id: 0,
          metric_group_id: 0,
        },
        '7356': {
          metric_id: 0,
          metric_group_id: 0,
        },
        '3704': {
          metric_id: 0,
          metric_group_id: 0,
        },
        '359289': {
          metric_id: 0,
          metric_group_id: 0,
        },
      },
      推荐导出LT: {
        '1775': {
          metric_id: 1665364,
          metric_group_id: 130219,
        },
        '3006': {
          metric_id: 818519,
          metric_group_id: 7035872,
        },
        '2515': {
          metric_id: 0,
          metric_group_id: 0,
        },
        '7356': {
          metric_id: 0,
          metric_group_id: 0,
        },
        '3704': {
          metric_id: 0,
          metric_group_id: 0,
        },
        '359289': {
          metric_id: 0,
          metric_group_id: 0,
        },
      },
    },
    主框架: {
      小工具导出渗透: {
        '1775': {
          metric_id: 1876284,
          metric_group_id: 153442,
        },
        '3006': {
          metric_id: 905280,
          metric_group_id: 7042535,
        },
        '2515': {
          metric_id: 0,
          metric_group_id: 0,
        },
        '7356': {
          metric_id: 0,
          metric_group_id: 0,
        },
        '3704': {
          metric_id: 0,
          metric_group_id: 0,
        },
        '359289': {
          metric_id: 0,
          metric_group_id: 0,
        },
      },
      新建项目导出渗透: {
        '1775': {
          metric_id: 1876287,
          metric_group_id: 153442,
        },
        '3006': {
          metric_id: 905282,
          metric_group_id: 7042535,
        },
        '2515': {
          metric_id: 0,
          metric_group_id: 0,
        },
        '7356': {
          metric_id: 0,
          metric_group_id: 0,
        },
        '3704': {
          metric_id: 0,
          metric_group_id: 0,
        },
        '359289': {
          metric_id: 0,
          metric_group_id: 0,
        },
      },
      草稿导出渗透: {
        '1775': {
          metric_id: 1877971,
          metric_group_id: 153442,
        },
        '3006': {
          metric_id: 905284,
          metric_group_id: 7042535,
        },
        '2515': {
          metric_id: 0,
          metric_group_id: 0,
        },
        '7356': {
          metric_id: 0,
          metric_group_id: 0,
        },
        '3704': {
          metric_id: 0,
          metric_group_id: 0,
        },
        '359289': {
          metric_id: 0,
          metric_group_id: 0,
        },
      },
      小工具点击渗透: {
        '1775': {
          metric_id: 1876283,
          metric_group_id: 153442,
        },
        '3006': {
          metric_id: 905279,
          metric_group_id: 7042535,
        },
        '2515': {
          metric_id: 0,
          metric_group_id: 0,
        },
        '7356': {
          metric_id: 0,
          metric_group_id: 0,
        },
        '3704': {
          metric_id: 0,
          metric_group_id: 0,
        },
        '359289': {
          metric_id: 0,
          metric_group_id: 0,
        },
      },
      新建项目点击渗透: {
        '1775': {
          metric_id: 1876286,
          metric_group_id: 153442,
        },
        '3006': {
          metric_id: 905281,
          metric_group_id: 7042535,
        },
        '2515': {
          metric_id: 0,
          metric_group_id: 0,
        },
        '7356': {
          metric_id: 0,
          metric_group_id: 0,
        },
        '3704': {
          metric_id: 0,
          metric_group_id: 0,
        },
        '359289': {
          metric_id: 0,
          metric_group_id: 0,
        },
      },
      草稿点击渗透: {
        '1775': {
          metric_id: 1877959,
          metric_group_id: 153442,
        },
        '3006': {
          metric_id: 905283,
          metric_group_id: 7042535,
        },
        '2515': {
          metric_id: 0,
          metric_group_id: 0,
        },
        '7356': {
          metric_id: 0,
          metric_group_id: 0,
        },
        '3704': {
          metric_id: 0,
          metric_group_id: 0,
        },
        '359289': {
          metric_id: 0,
          metric_group_id: 0,
        },
      },
    },
    作者: {
      作者人均投稿量: {
        '1775': {
          metric_id: 1629144,
          metric_group_id: 131177,
        },
        '3006': {
          metric_id: 861819,
          metric_group_id: 7034801,
        },
        '2515': {
          metric_id: 0,
          metric_group_id: 0,
        },
        '7356': {
          metric_id: 0,
          metric_group_id: 0,
        },
        '3704': {
          metric_id: 0,
          metric_group_id: 0,
        },
        '359289': {
          metric_id: 0,
          metric_group_id: 0,
        },
      },
      作者人均投稿LT: {
        '1775': {
          metric_id: 1623801,
          metric_group_id: 129737,
        },
        '3006': {
          metric_id: 861818,
          metric_group_id: 7034801,
        },
        '2515': {
          metric_id: 0,
          metric_group_id: 0,
        },
        '7356': {
          metric_id: 0,
          metric_group_id: 0,
        },
        '3704': {
          metric_id: 0,
          metric_group_id: 0,
        },
        '359289': {
          metric_id: 0,
          metric_group_id: 0,
        },
      },
      作者人均有效投稿量: {
        '1775': {
          metric_id: 0,
          metric_group_id: 0,
        },
        '3006': {
          metric_id: 918516,
          metric_group_id: 7034801,
        },
        '2515': {
          metric_id: 0,
          metric_group_id: 0,
        },
        '7356': {
          metric_id: 0,
          metric_group_id: 0,
        },
        '3704': {
          metric_id: 0,
          metric_group_id: 0,
        },
        '359289': {
          metric_id: 0,
          metric_group_id: 0,
        },
      },
      作者人均导出量: {
        '1775': {
          metric_id: 1653789,
          metric_group_id: 133275,
        },
        '3006': {
          metric_id: 802356,
          metric_group_id: 7034801,
        },
        '2515': {
          metric_id: 0,
          metric_group_id: 0,
        },
        '7356': {
          metric_id: 0,
          metric_group_id: 0,
        },
        '3704': {
          metric_id: 0,
          metric_group_id: 0,
        },
        '359289': {
          metric_id: 0,
          metric_group_id: 0,
        },
      },
      作者人均拉活量: {
        '1775': {
          metric_id: 1653795,
          metric_group_id: 133275,
        },
        '3006': {
          metric_id: 802358,
          metric_group_id: 7034801,
        },
        '2515': {
          metric_id: 0,
          metric_group_id: 0,
        },
        '7356': {
          metric_id: 0,
          metric_group_id: 0,
        },
        '3704': {
          metric_id: 0,
          metric_group_id: 0,
        },
        '359289': {
          metric_id: 0,
          metric_group_id: 0,
        },
      },
    },
    醒H: {
      人像导出渗透: {
        '2515': {
          metric_id: 0,
          metric_group_id: 0,
        },
        '7356': {
          metric_id: 0,
          metric_group_id: 0,
        },
      },
      人像导出次数: {
        '2515': {
          metric_id: 0,
          metric_group_id: 0,
        },
        '7356': {
          metric_id: 0,
          metric_group_id: 0,
        },
      },
      人像导出LT: {
        '2515': {
          metric_id: 0,
          metric_group_id: 0,
        },
        '7356': {
          metric_id: 0,
          metric_group_id: 0,
        },
      },
      调节导出渗透: {
        '2515': {
          metric_id: 0,
          metric_group_id: 0,
        },
        '7356': {
          metric_id: 0,
          metric_group_id: 0,
        },
      },
      调节导出次数: {
        '2515': {
          metric_id: 0,
          metric_group_id: 0,
        },
        '7356': {
          metric_id: 0,
          metric_group_id: 0,
        },
      },
      调节导出LT: {
        '2515': {
          metric_id: 0,
          metric_group_id: 0,
        },
        '7356': {
          metric_id: 0,
          metric_group_id: 0,
        },
      },
      素材整体导出渗透: {
        '2515': {
          metric_id: 1906242,
          metric_group_id: 157124,
        },
        '7356': {
          metric_id: 937899,
          metric_group_id: 7045810,
        },
      },
      素材导出人均次数: {
        '2515': {
          metric_id: 1906243,
          metric_group_id: 157124,
        },
        '7356': {
          metric_id: 937900,
          metric_group_id: 7045810,
        },
      },
      素材导出LT: {
        '2515': {
          metric_id: 1906174,
          metric_group_id: 157122,
        },
        '7356': {
          metric_id: 937841,
          metric_group_id: 7045805,
        },
      },
      模板整体导出渗透: {
        '2515': {
          metric_id: 1906196,
          metric_group_id: 157120,
        },
        '7356': {
          metric_id: 937863,
          metric_group_id: 7045808,
        },
      },
      模板导出人均次数: {
        '2515': {
          metric_id: 1906197,
          metric_group_id: 157120,
        },
        '7356': {
          metric_id: 937864,
          metric_group_id: 7045808,
        },
      },
      模板导出LT: {
        '2515': {
          metric_id: 1906207,
          metric_group_id: 157121,
        },
        '7356': {
          metric_id: 937874,
          metric_group_id: 7045807,
        },
      },
    },
    商业化: {
      编辑器GMV: {
        '1775': {
          metric_id: 1685982,
          metric_group_id: 136385,
        },
        '3006': {
          metric_id: 855713,
          metric_group_id: 7038649,
        },
        '2515': {
          metric_id: 0,
          metric_group_id: 0,
        },
        '7356': {
          metric_id: 0,
          metric_group_id: 0,
        },
        '3704': {
          metric_id: 0,
          metric_group_id: 0,
        },
        '359289': {
          metric_id: 0,
          metric_group_id: 0,
        },
      },
      编辑器会员付费转化: {
        '1775': {
          metric_id: 0,
          metric_group_id: 0,
        },
        '3006': {
          metric_id: 0,
          metric_group_id: 0,
        },
        '2515': {
          metric_id: 0,
          metric_group_id: 0,
        },
        '7356': {
          metric_id: 0,
          metric_group_id: 0,
        },
        '3704': {
          metric_id: 0,
          metric_group_id: 0,
        },
        '359289': {
          metric_id: 0,
          metric_group_id: 0,
        },
      },
      编辑器会员新增付费转化: {
        '1775': {
          metric_id: 0,
          metric_group_id: 0,
        },
        '3006': {
          metric_id: 0,
          metric_group_id: 0,
        },
        '2515': {
          metric_id: 0,
          metric_group_id: 0,
        },
        '7356': {
          metric_id: 0,
          metric_group_id: 0,
        },
        '3704': {
          metric_id: 0,
          metric_group_id: 0,
        },
        '359289': {
          metric_id: 0,
          metric_group_id: 0,
        },
      },
      模板GMV: {
        '1775': {
          metric_id: 1547174,
          metric_group_id: 124003,
        },
        '3006': {
          metric_id: 796710,
          metric_group_id: 7034418,
        },
        '2515': {
          metric_id: 0,
          metric_group_id: 0,
        },
        '7356': {
          metric_id: 0,
          metric_group_id: 0,
        },
        '3704': {
          metric_id: 0,
          metric_group_id: 0,
        },
        '359289': {
          metric_id: 0,
          metric_group_id: 0,
        },
      },
      模板会员付费转化: {
        '1775': {
          metric_id: 0,
          metric_group_id: 0,
        },
        '3006': {
          metric_id: 0,
          metric_group_id: 0,
        },
        '2515': {
          metric_id: 0,
          metric_group_id: 0,
        },
        '7356': {
          metric_id: 0,
          metric_group_id: 0,
        },
        '3704': {
          metric_id: 0,
          metric_group_id: 0,
        },
        '359289': {
          metric_id: 0,
          metric_group_id: 0,
        },
      },
      模板会员新增付费转化: {
        '1775': {
          metric_id: 0,
          metric_group_id: 0,
        },
        '3006': {
          metric_id: 0,
          metric_group_id: 0,
        },
        '2515': {
          metric_id: 0,
          metric_group_id: 0,
        },
        '7356': {
          metric_id: 0,
          metric_group_id: 0,
        },
        '3704': {
          metric_id: 0,
          metric_group_id: 0,
        },
        '359289': {
          metric_id: 0,
          metric_group_id: 0,
        },
      },
      云GMV: {
        '1775': {
          metric_id: 1685986,
          metric_group_id: 136385,
        },
        '3006': {
          metric_id: 855717,
          metric_group_id: 7038649,
        },
        '2515': {
          metric_id: 0,
          metric_group_id: 0,
        },
        '7356': {
          metric_id: 0,
          metric_group_id: 0,
        },
        '3704': {
          metric_id: 0,
          metric_group_id: 0,
        },
        '359289': {
          metric_id: 0,
          metric_group_id: 0,
        },
      },
      云会员付费转化: {
        '1775': {
          metric_id: 0,
          metric_group_id: 0,
        },
        '3006': {
          metric_id: 0,
          metric_group_id: 0,
        },
        '2515': {
          metric_id: 0,
          metric_group_id: 0,
        },
        '7356': {
          metric_id: 0,
          metric_group_id: 0,
        },
        '3704': {
          metric_id: 0,
          metric_group_id: 0,
        },
        '359289': {
          metric_id: 0,
          metric_group_id: 0,
        },
      },
      云会员新增付费转化: {
        '1775': {
          metric_id: 0,
          metric_group_id: 0,
        },
        '3006': {
          metric_id: 0,
          metric_group_id: 0,
        },
        '2515': {
          metric_id: 0,
          metric_group_id: 0,
        },
        '7356': {
          metric_id: 0,
          metric_group_id: 0,
        },
        '3704': {
          metric_id: 0,
          metric_group_id: 0,
        },
        '359289': {
          metric_id: 0,
          metric_group_id: 0,
        },
      },
      醒HGMV: {
        '1775': {
          metric_id: 0,
          metric_group_id: 0,
        },
        '3006': {
          metric_id: 0,
          metric_group_id: 0,
        },
        '2515': {
          metric_id: 0,
          metric_group_id: 0,
        },
        '7356': {
          metric_id: 0,
          metric_group_id: 0,
        },
        '3704': {
          metric_id: 0,
          metric_group_id: 0,
        },
        '359289': {
          metric_id: 0,
          metric_group_id: 0,
        },
      },
      醒H会员付费转化: {
        '1775': {
          metric_id: 0,
          metric_group_id: 0,
        },
        '3006': {
          metric_id: 0,
          metric_group_id: 0,
        },
        '2515': {
          metric_id: 0,
          metric_group_id: 0,
        },
        '7356': {
          metric_id: 0,
          metric_group_id: 0,
        },
        '3704': {
          metric_id: 0,
          metric_group_id: 0,
        },
        '359289': {
          metric_id: 0,
          metric_group_id: 0,
        },
      },
      醒H会员新增付费转化: {
        '1775': {
          metric_id: 0,
          metric_group_id: 0,
        },
        '3006': {
          metric_id: 0,
          metric_group_id: 0,
        },
        '2515': {
          metric_id: 0,
          metric_group_id: 0,
        },
        '7356': {
          metric_id: 0,
          metric_group_id: 0,
        },
        '3704': {
          metric_id: 0,
          metric_group_id: 0,
        },
        '359289': {
          metric_id: 0,
          metric_group_id: 0,
        },
      },
      作者人均模板GMV: {
        '1775': {
          metric_id: 1850126,
          metric_group_id: 149839,
        },
        '3006': {
          metric_id: 918730,
          metric_group_id: 7043792,
        },
        '2515': {
          metric_id: 0,
          metric_group_id: 0,
        },
        '7356': {
          metric_id: 0,
          metric_group_id: 0,
        },
        '3704': {
          metric_id: 0,
          metric_group_id: 0,
        },
        '359289': {
          metric_id: 0,
          metric_group_id: 0,
        },
      },
      作者人均模板订阅GMV: {
        '1775': {
          metric_id: 1850127,
          metric_group_id: 149839,
        },
        '3006': {
          metric_id: 918731,
          metric_group_id: 7043792,
        },
        '2515': {
          metric_id: 0,
          metric_group_id: 0,
        },
        '7356': {
          metric_id: 0,
          metric_group_id: 0,
        },
        '3704': {
          metric_id: 0,
          metric_group_id: 0,
        },
        '359289': {
          metric_id: 0,
          metric_group_id: 0,
        },
      },
    },
    UG: {
      人均工具端投稿: {
        '1128': {
          metric_id: 1423980,
          metric_group_id: 114968,
        },
        '1180': {
          metric_id: 832043,
          metric_group_id: 7036820,
        },
      },
      人均剪同款投稿: {
        '1128': {
          metric_id: 1423982,
          metric_group_id: 114968,
        },
        '1180': {
          metric_id: 832044,
          metric_group_id: 7036820,
        },
      },
      人均剪同款导出: {
        '1128': {
          metric_id: 1423974,
          metric_group_id: 114968,
        },
        '1180': {
          metric_id: 814302,
          metric_group_id: 7028097,
        },
      },
      人均工具端导出: {
        '1128': {
          metric_id: 1423972,
          metric_group_id: 114968,
        },
        '1180': {
          metric_id: 814303,
          metric_group_id: 7028097,
        },
      },
      促跟投人均投稿: {
        '1128': {
          metric_id: 0,
          metric_group_id: 0,
        },
        '1180': {
          metric_id: 832263,
          metric_group_id: 7036831,
        },
      },
      促跟投投稿渗透: {
        '1128': {
          metric_id: 0,
          metric_group_id: 0,
        },
        '1180': {
          metric_id: 832264,
          metric_group_id: 7036831,
        },
      },
    },
  },
};
