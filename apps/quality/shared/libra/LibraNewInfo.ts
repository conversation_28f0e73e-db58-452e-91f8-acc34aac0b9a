import { LibraRegion } from '@shared/libra/commonLibra';
import { User } from '@pa/shared/dist/src/core';
import { DBExperimentListInfo } from '../../api/model/ExperimentListInfoTable';
import { FilterRule } from '@shared/libra/flight';
import { GrayOrRelease } from '@shared/libra/LibraCreate';

export enum LibraNewInfoEditaResultType {
  Success = 0,
  Conflict = 1,
  UnknownError = 2,
}

export enum LibraFlightType {
  // 正式实验
  Release = 0,
  // 灰度实验
  Gray = 1,
  // 反转实验
  Invert = 2,
  // 长期实验
  LongTerm = 3,
  // 容器实验
  Container = 4,
  // 策略实验
  Strategy = 5,
  // 活动实验
  Activity = 6,
  // 线下实验
  Offline = 7,
  // 其他
  Other = 8,
}

export enum LibraFlightDAApproveResultType {
  Unknown = 0, // 待评估
  Pass = 1, // 准出通过
  Block = 2, // 不通过
}

export enum LibraFlightStatus {
  Ended = 0, // 已结束
  InProgress = 1, // 进行中
  ToBeScheduled = 2, // 待调度
  InDebug = 3, // 调试中
  Paused = 4, // 已暂停
  ToBeScheduledEnded = 5, // 待调度结束
  Released = 91, // 已上线，效果等同于 0
}

export enum LibraVersionType {
  // 对照组
  Control = 0,
  // 实验组
  Experiment = 1,
}

export enum LibraLayerType {
  uid = 0,
  did = 1,
  rid = 2,
  union = 3,
  uuid = 4,
  cdid = 5,
  ssid = 6,
  webid = 7,
  pkid = 8,
  pureuid = 9,
}

export enum LibraReopenReasonType {
  StrategyChanged = 0, // 策略迭代
  ProductSettingsChanged = 1, // 产品配置变更
  RevenueNotMatchExpectation = 2, // 实验数据不理想
  FlightSettingsWrong = 3, // 实验配置错误
  Bugfix = 4, // 修复Bug重开
}

export enum LibraNotifyType {
  // 未知
  Unknown = -1,
  // 实验开启通知
  FlightStart = 0,
  // 流量变更通知
  FlightTrafficChanged = 1,
  // 实验下线通知
  FlightOffline = 2,
  // 实验全量通知
  FlightFullyLaunched = 3,
  // 实验重开通知
  FlightReopen = 4,
  // 实验暂停通知
  FlightPause = 5,
  // 实验继续通知
  FlightContinue = 6,
  // 开启灰度实验通知
  LaunchGray = 7,
  // 开启正式实验通知
  LaunchRelease = 8,
  // 发起实验 Review 通知
  StartReview = 9,
  // 实验 Review 通过通知
  ReviewApproved = 10,
  // 实验 Review 拒绝通知
  ReviewRejected = 11,
  // 实验 Review 跳过通知
  ReviewSkipped = 12,
  // 实验数据回收通知
  RemindDataReview = 13,
  // 关闭灰度实验通知
  CloseGray = 14,
  // 超期实验提醒
  Overdue = 15,
  // Setting实验固化通知
  SettingSolidify = 16,
  // 灰度实验开启100%流量通知
  Gray100Percent = 17,
  // 实验关闭归因完成填写通知
  DidFinishCloseAttribution = 18,
}

export enum LibraNotifyReceiverType {
  // 个人
  Person = 0,
  // 群
  Group = 1,
}

// 实验关闭主要原因（大分类）
export enum LibraFlightCloseMainType {
  // 实验重开
  Reopen = 0,
  // 实验全量
  FullRelease = 1,
  // 实验下线
  Offline = 2,
}

export const LibraFlightHasNoCloseAttribution = 0x1000;

// 实验关闭-重开子分类
export enum LibraFlightCloseReopenSubType {
  // 正常重开/关闭
  Normal = 0,
  // 代码相关异常
  CodeRelatedAnomaly,
  // 实验策略变动异常
  StrategyChangeAnomaly,
  // 其他异常(libra 平台问题或不明原因)
  OtherAnomaly,
}
// 查询时间范围类型
export enum LibraTimeQueryType {
  OnlyStartRange = 0,
  OnlyEndRange,
  BothStartAndEndRange,
}

// 实验关闭-重开子分类-详细分类
export enum LibraFlightCloseReopenSubTypeDetailType {
  // 若中途有新增分类，则基于 100/200/300/400 这样的规则递增。每个子分类占据 100 个枚举值区间。
  // 已经存在的枚举，不要修改它的值。
  /** 正常重开 **/
  // 灰度实验关闭
  Normal_CloseGray = 0,
  // 开发期间实验配置错误
  Normal_FlightInDevelopConfigError = 1,
  // 正式实验-收益负向关闭
  Normal_FlightInReleaseHasNegativeBenefits = 2,
  // 正式实验-有收益全量
  Normal_FlightInReleaseHasBenefitsForFullRelease = 3,
  // 开发期间实验策略变更（2025.03.12 新增）
  Normal_FlightInDevelopStrategyChange = 100,
  /** 代码相关异常 **/
  // 新增代码逻辑导致数据异常
  CodeRelatedAnomaly_NewAddedCodes = 4,
  // 历史代码逻辑导致数据异常
  CodeRelatedAnomaly_HistoryCodes = 5,
  // 正式实验期间-实验配置错误
  CodeRelatedAnomaly_FlightInReleaseConfigError = 6,
  // 曝光代码问题导致进组不均（2025.03.12 新增）
  CodeRelatedAnomaly_ExposureCodeProblem = 200,
  // 其他技术问题变动（2025.03.17 新增）
  CodeRelatedAnomaly_OtherTechRelatedChanges = 201,
  /** 实验策略变动异常 **/
  // 正式实验期间-策略变更（2025.03.17 ----废弃----）
  StrategyChangeAnomaly_FlightInReleaseStrategyChange = 7,
  // 其他（补充填写）（2025.03.12 ----废弃----）
  StrategyChangeAnomaly_Others = 8,
  // 实验需求变更-实验设计缺陷（2024.03.17 新增）
  StrategyChangeAnomaly_FlightDesignDefect = 300,
  // 实验需求变更- LR 引入（2024.03.17 新增）
  StrategyChangeAnomaly_LRProblem = 301,
  // 素材等上新运营配置耦合影响（2024.03.17 新增）
  StrategyChangeAnomaly_OperationConfigsImpact = 302,
  // 其他产品策略变动（2024.03.17 新增）
  StrategyChangeAnomaly_OtherProductStrategyChanges = 303,
  /** 其他异常(libra 平台问题或不明原因) **/
  // PreAA 或进组不均（2025.03.12 ----废弃----）
  OtherAnomaly_PreAAOrUnevenGroup,
  // 原因不明，重开观察继续分析
  OtherAnomaly_UnknownReasons = 10,
  // PreAA 数据异常（用户分组策略异常）（2025.03.12 新增）
  OtherAnomaly_PreAAProblem = 400,
  // Libra 系统原因导致的进组不均（2025.03.12 新增）
  OtherAnomaly_LibraProblem = 401,
  // 三方依赖导致异常（2024.03.17 新增）
  OtherAnomaly_ThirdPartyDependency = 402,
  // 其他（2024.03.17 新增）
  OtherAnomaly_Others = 403,
}

// 实验关闭-重开子分类-是否可前置拦截
export enum LibraFlightCloseReopenSubTypeCanPreInterceptType {
  // 否，无法前置拦截
  CanNotPreIntercept = 0,
  // 是，RD开发或自测阶段可拦截
  CanPreInterceptInDevelopmentPeriod = 1,
  // 是，QA新功能测试阶段可拦截
  CanPreInterceptInFeatureFuncTestPeriod = 2,
  // 是，QA集成/系统/上线前测试阶段可拦截
  CanPreInterceptInIntegrationPeriod = 3,
  // 是，实验管控配置校验可拦截
  CanPreInterceptInLibraConfigCheckPeriod = 4,
  // 是，线上巡检/监控可拦截
  CanPreInterceptInOnlineLibraPatrolPeriod = 5,
}

// 实验关闭-全量子分类
export enum LibraFlightCloseFullReleaseSubType {
  // 正常全量
  Normal = 0,
  // 超期全量
  Overdue = 1,
}

// 实验关闭-下线子分类
export enum LibraFlightCloseOfflineSubType {
  // 回收数据，准备推全
  CollectDataReadyToFullRelease = 0,
  // 线上应急（指标异常、用户反馈等），关闭止损
  OnlineEmergency = 1,
  // 用户负向反馈
  UserNegativeFeedback = 2,
  // 其他（补充填写）
  Others = 3,
  // 指标不符合预期或需要继续迭代
  IndicatorNotMatch = 4,
}

// 查询实验列表参数
// CN: https://data.bytedance.net/dataopen/libra/document/6
// SG: https://dataopen-sg.tiktok-row.net/dataopen/libra/document/6
export interface LibraListQueryParams {
  app: number;
  product?: number;
  layer?: number;
  status?: number;
  time_type?: number;
  start_time?: string;
  end_time?: string;
  desc?: string;
  device_platform?: string;
  user_group_id?: number;
  config?: string;
  version_key?: string;
  flights_key?: string;
  page?: number;
  page_size?: number;
  tag?: string;
  user?: string;
  type?: string;
  test_user?: string;
  strategy_category_id?: string;
}

/**
 * 实验类型
 */
export enum FlightType {
  /**
   * 服务端实验
   */
  Server = 'strategy',

  /**
   * 普通客户端实验
   */
  ClientNormal = 'product',

  /**
   * AB客户端SDK实验
   */
  AbClientSDK = 'ab_client_sdk',

  /**
   * Settings SDK实验
   */
  SettingsClientSDK = 'settings_client_sdk',

  /**
   * Settings普通客户端实验
   */
  SettingsClientNormal = 'settings_client_normal',

  /**
   * 客户端本地分流实验
   */
  AbLocalClient = 'ab_local_client',
}

/**
 * 实验类型名称
 */
export const FlightTypeName: { [key in FlightType]: string } = {
  [FlightType.Server]: '服务端实验',
  [FlightType.ClientNormal]: '普通客户端实验',
  [FlightType.AbClientSDK]: 'AB客户端SDK实验',
  [FlightType.SettingsClientSDK]: 'Settings SDK实验',
  [FlightType.SettingsClientNormal]: 'Settings普通客户端实验',
  [FlightType.AbLocalClient]: '客户端本地分流实验',
};

/**
 * 新增实验参数
 *
 * {@link https://data.bytedance.net/dataopen/libra/document/10 CN}
 *
 * {@link https://dataopen-sg.tiktok-row.net/dataopen/libra/document/10 SG}
 */
export interface CreateLibraFlightParams {
  /**
   * 实验名称
   */
  name: string;

  /**
   * 实验类型
   */
  type?: FlightType;

  /**
   * 实验层 id
   */
  layer_id: number;

  /**
   * 实验流量，不能为空并且最小刻度是0.001
   */
  version_resource: number;

  /**
   * 预定实验流量，默认为0，并且最小刻度是0.001
   */
  book_version_resource?: number;

  /**
   * 运行时长，以天为单位
   */
  duration: number;

  /**
   * 实验描述
   */
  description: string;

  /**
   * 实验预期
   */
  expectation?: string;

  /**
   * 请固定为 rule
   */
  filter_type: string;

  /**
   * 默认空array，数据格式参考线上开实验时请求格式
   * TODO
   */
  filter_rule: unknown[];

  /**
   * 默认为空，需要使用用户分群时，传用户分群的名字，名字需要通过libra平台用户分群管理列表查询得到
   */
  user_tag?: string;

  /**
   * 默认为0 0:指定用户分群为白名单 1:指定用户分群为黑名单
   */
  user_tag_is_black?: number;

  /**
   * 实验组配置
   * 至少需要一个实验分组，数据格式: {"name":"v1","type":1,"config":"","description":"","test_user":"","user_tag":"",weight:0} 对照组type为0，实验组type为1 test_user 为测试用户 user_tag 为用户分群 config 应该为jsonify的字符串 weight 取值范围为[0,1000]，int类型；表示不均等流量实验每个实验组流量的权重，累加必须等于1000，不传当作0；非不均等流量实验可不传
   */
  versions: {
    /**
     * 分组名称
     */
    name?: string;

    /**
     * 分组类型，对照组type为0，实验组type为1
     */
    type?: number;

    /**
     * 分组配置，json字符串
     */
    config?: string;

    /**
     * 分组描述信息
     */
    description?: string;

    /**
     * 测试用户，逗号分割
     */
    test_user?: string;

    /**
     * 用户分群ID，逗号分割
     */
    user_tag?: string;

    /**
     *
     * 取值范围为[0,1000]，int类型；表示不均等流量实验每个实验组流量的权重，累加必须等于1000，不传当作0；非不均等流量实验可不传
     */
    weight?: number;
  }[];

  /**
   * owner用户邮箱前缀
   */
  owner: string[];

  /**
   *
   * 实验标签，默认为空array
   */
  tags: string[];

  /**
   * 默认false, 为true时跳过创建，仅检查参数是否合法
   */
  skip_create?: boolean;

  /**
   *
   * 默认true，为true时会检查潜在的实验参数冲突，如果要忽略实验参数检查，请业务方自行确认风险。
   */
  check_conflict?: boolean;

  /**
   *
   * 默认true，为true时会检查特殊字符。false: 不检查
   */
  check_special_char?: boolean;

  /**
   *
   * 测试用户过filter, 默认为false
   */
  filter_test_user?: boolean;

  /**
   * normal: 普通试验，inherit: 父子实验，reversal: 反转实验。默认normal
   */
  kind?: 'normal' | 'inherit' | 'reversal';

  /**
   * 原实验组ID，kind非normal时需要传
   */
  parent_vid?: number;

  /**
   * 指定的PSM, 例如，['data.abtest.libra1', 'data.abtest.libra2']
   */
  specified_psms: string[];

  /**
   * 是否创建完开启，0:不开启，创建一个调试中的实验 1:开启（默认）
   */
  is_launch?: number;

  /**
   * 是否需要review。传True时，参数is_launch自动置0（即创建时不能同时开启实验）。需要页面发起审核并通过后，调用[开启实验]API才能开启实验
   */
  need_review?: boolean;

  /**
   * 是否需要diff检测，diff检测实验不能创建时启动(参数is_launch自动置0)
   */
  need_diff?: boolean;

  /**
   * 不传实验层，先从当前product下寻找可用（没有进行中的实验）的openapi实验层，没有就自动创建新实验层，默认false，和layer_id字段互斥
   */
  create_layer_auto?: boolean;

  /**
   * create_layer_auto=true时必传，分流策略，uid,did,rid,...如果需要用纯uid分流(不带uid_type的那种)，那么hash_strategy传uid_only
   */
  hash_strategy?: string;

  /**
   * create_layer_auto=true时必传，功能模块
   */
  product_id?: number;

  /**
   * 查询Tea指标过滤字段
   */
  extra?: {
    tea_filter_metric_groups: number[];
    tea_filter: {
      content_type: string;
      type: string;
      attr: string;
      value: number[];
      op: string;
    }[];
  };

  /**
   * 关注指标组, 支持按照指标组和指标组模板添加
   */
  watching_metric_groups: {
    metric_group_id: number;
    dimension_ids: number[];
    bundle_id?: number;
  }[];

  /**
   * 是否支持不均等流量，0：不支持(默认)， 1：支持
   */
  version_traffic_adjustable?: number;

  /**
   * 是否为mab实验，0: 不是mab实验（默认），1: 是mab实验
   */
  is_mab?: number;

  /**
   * mab实验需要传，标识mab实验关心的目标指标，dict里面包含的内容是目标指标的指标metric_id和指标组metric_group_id
   * TODO
   */
  mab_target_metric?: unknown[];

  /**
   * 生效机房，默认为空数组，用户也可以手动指定。空数组的效果是对当前分流控制面下所影响的所有机房都生效（如果是国内, effected_regions为空效果等同于['CN'];
   * 如果是新加坡，effected_regions为空效果等同于['SG', 'GCP']; 如果是美东，effected_regions为空效果等同于['VA', 'TX']
   * 如果需要开启EU_TTP的实验，需要把GCP 和 EU_TTP两个字段都穿进去 CN: 中国区 SG: 新加坡 GCP: gcp机房 VA: 美东机房 TX: TTP机房
   */
  effected_regions: string[];

  /**
   * 实验bingo标签，广告实验相关
   */
  ad_flight_hit_rules: string[];

  /**
   * 实验关联的meego需求
   */
  meego_info?: {
    meego_array?: {
      /**
       * 参考接口文档https://kahelpcenter.arcosite.bytedance.com/2hsn1l8c/3m33lu3p 该字段对应simple_name
       */
      meego_project: string;

      /**
       * meego id, 字符串格式
       */
      meego_story: string;

      /**
       *
       * 对应 project_key
       */
      meego_project_key: string;
    }[];
  };

  /**
   * 是否需要流量线性生效实验（默认值为false），如需流量线性生效，enable_gradual赋值true，且gradual_traffic满足下面格式
   */
  enable_gradual?: boolean;

  /**
   * 线性生效配置信息 示例：{"start_traffic": 0, "end_traffic": 0.5, "duration": 10} 该
   * 实验流量的平滑生效从流量0%线性生效到50%，用时10分钟 注意：开启实验start_traffic值为0，end_traffic为放量的最终流量，duration值的范围在（0，1440]
   */
  gradual_traffic?: {
    /**
     * 开始流量百分比
     */
    start_traffic?: number;

    /**
     *
     * 结束流量百分比
     */
    end_traffic?: number;

    /**
     * 用时，分钟
     */
    duration?: number;
  }[];

  /**
   * 流量来源，0-libra平台创编流量,1-普通用户请求openApi流量（默认值）,2-RM平台创编流量
   */
  req_from?: number;

  /**
   *
   * req_from = 0的时候，identity为平台登陆用户 req_from = 1的时候，identity为tenant req_from = 2的时候，调用平台可以自定义唯一标识
   */
  identity?: string;

  /**
   * 格式：广告标签化的标签id，多个id用英文逗号分隔; 作用：将决定开启实验后看到哪些广告报表,未来非广告实验不打标签的话，
   * 仅能看到少许且核心的广告数据 进度：当前处于灰度状态，如果有需要请联系machitao加入白名单
   */
  ad_tag_id_list?: string;

  /**
   * 判定当前实验是否对广告产生影响，有影响会展示更多的核心广告报表
   */
  ad_clear_effective_scenes?: boolean;
}

/**
 * 新增实验响应
 */
export interface CreateLibraFlightResponse {
  layer_id: number;
  flight_id: number;
  versions: unknown[];
}

// 流量信息
export interface LibraTrafficInfo {
  // 流量层
  trafficLayer: string;
  // 开始流量
  startTrafficValue: number;
  // 结束流量
  endTrafficValue: number;
  // 当前生效流量
  currentTrafficValue: number;
}

// 实验组信息
export interface LibraVersionInfo {
  // 实验组 ID
  vid: number;
  // 测试用户
  testUser: string[];
  // 类型（对照组、实验组）
  type: LibraVersionType;
  // 进组人数
  userNumber: number;
  // 实验组描述
  description: string;
  // 实验组名称
  vname: string;
  // 流量权重比，仅针对流量不均等实验有意义，权重比值0 ~ 1000，实验每个version的weight加起来一定等于1000
  weight: number;
}

export interface LibraFlightCloseAttributionInfo {
  // 主要分类
  mainType: LibraFlightCloseMainType;
  // 细分分类(实验重开)
  reopenSubType?: LibraFlightCloseReopenSubType;
  // 细分分类(实验全量)
  fullReleaseSubType?: LibraFlightCloseFullReleaseSubType;
  // 细分分类(实验下线)
  offlineSubType?: LibraFlightCloseOfflineSubType;
  // 自定义原因填写
  customReason?: string;
  // 当 LibraFlightCloseReopenSubType 时，对应的详细分类
  reopenSubTypeDetailType?: LibraFlightCloseReopenSubTypeDetailType;
  // 当 LibraFlightCloseReopenSubType 时，对应的是否可前置发现/拦截该问题
  reopenSubTypeCanPreInterceptType?: LibraFlightCloseReopenSubTypeCanPreInterceptType;
  // 更新时间
  updateTime?: number;
  // 更新人（邮箱）
  updateUser?: string;
}

export interface LibraFlightInfo {
  // 实验名称
  name: string;
  // 实验描述
  description: string;
  // 实验 ID
  id: number;
  // 实验组
  versions: LibraVersionInfo[];
  // 实验 Owner
  owners: User[];
  // 实验开始时间
  startTime: number;
  // 实验结束时间
  endTime: number;
  // 实验类型（灰度、正式、反转、长期等）
  type?: LibraFlightType;
  // 是否手动标记过实验类型（如果手动标记过，则后续不会再根据自动计算的实验类型进行覆盖）
  isManualMarkedType?: boolean;
  // 实验类型（Libra类型）
  libraType: string;
  // 实验状态（0已结束, 1进行中, 2待调度, 3测试中, 4已暂停, 91同0）
  status: LibraFlightStatus;
  // 流量信息
  trafficInfo: LibraTrafficInfo;
  // 分流类型（0: uid, 1: did, 2: rid, 3: union, 4: uuid, 5: cdid, 6: ssid, 7: webid, 8: pkid, 9: 纯uid分流）
  layerType: LibraLayerType;
  // 实验标签
  tags: string[];
  // 总进组人数
  userNumber: number;
  // 实验关闭原因归因
  closeAttributionInfo?: LibraFlightCloseAttributionInfo;
  // 重开归因
  reopenReasonType?: LibraReopenReasonType;
  // 重开详细原因
  reopenReasonDetail?: string;
  // 关闭原因
  stopReasonType?: LibraStopType;
  // 关闭详细原因
  stopReasonDetail?: string;
  // 关闭备注
  stopRemark?: string;
  // 关闭时间
  stopTime?: number;
  // 报告文档
  conclusionDocUrl?: string;
  // 报告文档是否正在生成
  conclusionDocGenerating?: boolean;
  // DA准出结论
  daConclusion?: LibraFlightDAApproveResultType;
  // 地区
  region: LibraRegion;
  // 过滤规则
  filterRule: FilterRule[];
  // 过滤类型
  filterType: string;
}

export interface LibraMeegoInfo {
  // 需求 ID
  id: number;
  // 需求名称
  name: string;
  // 需求链接
  url: string;
  // 业务线
  businessLine: string[];
  // 上线应用
  releaseApps: string[];
  // 跟车版本
  releaseVersion: string[];
  // 需求 PM
  pmOwners?: User[];
  // 技术 Owner
  techOwners?: User[];
  // DA
  daOwners?: User[];
  // meego群id
  chatId?: string;
  // 需求类型（产品需求、技术需求等）
  type?: string;
  // 业务 QA
  clientQAs?: User[];
  // 优先级
  priority?: string;
  // 需求状态
  status?: string;
  // 上线区域（多选）
  releaseRegions?: string[];
  // 是否需要 AB 实验
  needAB?: boolean;
  // AB 实验设计文档
  libraDesignDocUrl?: string;
}

export interface LibraPatrolInfo {
  // 稳定性指标
  stabilityMetrics?: DBExperimentListInfo[];
  // 业务核心指标
  businessCoreMetrics?: DBExperimentListInfo[];
  // 反馈指标
  feedbackMetrics?: DBExperimentListInfo[];
}

export interface LibraNotifyRecordInfo {
  // 通知类型
  type: LibraNotifyType;
  // 通知接收者类型
  receiverType: LibraNotifyReceiverType;
  // 通知接收者（飞书个人 OpenId 或者群 ChatId）
  receiverId: string;
  // 通知时间
  time: number;
  // 通知标题
  title?: string;
  // 通知内容
  content?: string;
}

export interface LibraForbidNotifyInfo {
  // 通知类型
  type?: LibraNotifyType;
  // 是否禁止
  isForbid?: boolean;
  // 禁止时间
  forbidTime?: number;
}

export enum LibraStopType {
  FullRelease = 1, // 全量
  Offline = 2, // 下线
  Reopen = 3, // 重开
}

export interface PaExtraInfo {
  /**
   * 实验分配：灰度、正式
   */
  grayOrRelease: GrayOrRelease;

  /**
   * 是否重开
   */
  isReopen: boolean;

  /**
   * 重开原因
   */
  reopenReason: string[];

  /**
   * 数据回收提醒频率(单位:天)
   */
  dataRecoveryRemind: number[];

  /**
   * 实验结束类型
   */
  stopType?: number;
  /**
   * 实验结束详细归因
   */
  stopDetailReason?: string;
}

// Meego 团队信息
export interface LibraMeegoTeamInfo {
  // 团队 ID
  teamId: number;
  // 团队名称
  teamName: string;
  // 团队管理员
  administrators: User[];
  // 团队成员
  members: User[];
  // Meego Project Key
  projectKey: string;
  // 对应的飞书群 Id
  larkChatId?: string;
}

// Meego 团队精简信息（用于包含在实验信息 LibraNewInfo 中）
export interface LibraMeegoTeamSimpleInfo {
  // 团队 ID
  teamId: number;
  // 团队名称
  teamName: string;
  // Meego Project Key
  projectKey: string;
  // 对应的飞书群 Id
  larkChatId?: string;
  // 是否为主责团队（该字段的更新，一般发生在实验关闭的时候，根据关闭人确定实验主责团队）
  isMainTeam?: boolean;
}

export interface SendToHostFlightInfo {
  flightId: number;
  flightName: string;
  ownersEmail?: string[];
}

export interface LibraNewInfo {
  // App名称
  appName: string;
  // Libra App ID
  libraAppId: number;
  // 实验信息
  flightInfo: LibraFlightInfo;
  // Meego 信息
  meegoInfo?: LibraMeegoInfo[];
  // Meego 团队信息（精简版）
  meegoTeamInfo?: LibraMeegoTeamSimpleInfo[];
  // 巡检信息
  patrolInfo?: LibraPatrolInfo;
  // 通知记录
  notifyRecordInfo?: LibraNotifyRecordInfo[];
  // 手动禁止通知信息
  forbidNotifyInfo?: LibraForbidNotifyInfo[];
  // 纸飞机创建实验所添加的额外信息
  extraInfo?: PaExtraInfo;
}

// 用于风神看板的实验列表
export interface LibraNewInfoForAeolus {
  // Libra 登记产品ID
  app_id: number;
  // Libra 登记产品
  app: string;
  // Libra 产品功能模块
  token: string;
  // 实验id
  flight_id: number;
  // 实验类型
  flight_type: string;
  // 实验名称
  display_name: string;
  // 实验结束日期
  flight_end_date: string;
  // 实验开始日期
  flight_start_date: string;
  // 实验owner
  flight_owner: string;
  // 当前实验状态
  latest_flight_status: number;
  // 业务线
  business_line: string;
  // 实验层
  flight_layer: string;
  // 实验流量
  flight_version_resource: number;
  // 实验分流策略
  flight_hash_strategy: string;
  // 实验tags
  flight_tags: string;
  // 查询日期
  search_date: string;
  // 区域（cn、sg、va）
  region: string;
  // 实验链接
  flight_url: string;
  // Meego 名称
  meego_name: string;
  // Meego Id
  meego_id: string;
  // Meego 类型
  meego_type: string;
  // Meego 链接
  meego_url: string;
  // Meego PM
  meego_pm: string;
  // 跟车版本
  release_version: string;
  // 实验关闭原因
  flight_stop_reason: string;
  // meego 团队信息
  meego_team_name: string;
  // meego 主责团队
  meego_main_team_name: string;
  // 实验关闭归因（主要分类）
  flight_close_main_type_name: string;
  // 实验关闭归因（子分类）
  flight_close_sub_type_name: string;
  // 实验关闭归因（详细分类）
  flight_close_sub_type_detail_name: string;
  // 实验关闭归因（重开-代码相关-是否前置拦截/召回）
  flight_close_reopen_can_pre_intercept_type: string;
  // 实验关闭归因（自定义原因）
  flight_close_custom_reason: string;
  // 实验关闭时间戳
  flight_close_timestamp: number;
}

export interface LibraOverdueNotifySetting {
  businessLine: string;
  chatId?: string;
  days: number;
}

export interface LibraInfoQueryProgress {
  queryId: string;
  region: LibraRegion;
  totalPage: number;
  searchArgs: LibraListQueryParams;
  isPending: boolean;
  isEnd: boolean;
  failedFlightIs: number[];
}
