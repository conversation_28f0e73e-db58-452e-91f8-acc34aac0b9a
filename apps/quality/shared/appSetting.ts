import { VersionStageInfo } from '@shared/releasePlatform/versionStage';

// export interface FeatureInfo {
//   featureName: '';
//   featurePath: string;
// }

// export interface AppSetting {
//   id: number; // 设置id，各端的id不同，前N位为APP，后两位为分端ID 总共位数为N+2
//   name: string; // APP名称
//   platform: PlatformType;
//   productType: string;
//   businessID: BusinessType; // 业务id
//   featureList?: FeatureInfo[];
//   time: number;
//   icon: string;
//   businessInfo: BusinessAppInfo;
// }

export interface VersionSetting {
  lvVersion: string; // 剪映大版本号：10.8.0
  ccVersion: string; // CapCut大版本号：8.8.0
  pcVersion: string; // PC对应的国内大版本号：4.5.5
  normalVersion: string; // 支持多业务线，后续将逐渐淡化lv\cc概念，统一使用自身的version
}

export interface VersionStageSetting {
  mainStage?: VersionStageInfo;
  subStage?: VersionStageInfo;
}
