import { VersionStageCheckList } from '@shared/releasePlatform/versionStageInfoCheckList';

export enum VersionStageStatus {
  OnProgress = 'OnProgress',
  Delay = 'Delay',
  Complete = 'Complete',
  NotStart = 'Not_Start',
}
export interface VersionPhase {
  stage_name: string;
  display_name: string;
  bits_calendar_segment: string; // bits空间中日历的segment名，通过此获取真实的日历信息
  sub_stages: VersionPhase[]; // 子轴节点
  status_rout: string[]; // 版本节点页面路由
  approval_config: [];
}

export interface VersionTimelineConfig {
  version_stages: VersionPhase[];
}

export interface VersionStageInfo {
  stage_name: string;
  display_name: string;
  start_time: number; // 预期开始时间
  real_start_time: number; // 实际开始时间
  end_time: number; // 预期结束时间
  real_end_time: number; // 实际结束时间
  status: string; // "OnProgress" | "Delay" | "Complete" ｜ "Not_Start"
  sub_stages: VersionStageInfo[]; // 子轴节点
  parent_stage_name?: string;
}
export interface VersionProcessInfo {
  version: string;
  app_id: number;
  version_stages: VersionStageInfo[];
  // eslint-disable-next-line @typescript-eslint/ban-ts-comment
  // @ts-ignore
  bmInfo: Record<number, any>;
  pcInfo?: any;
  meegoId: number;
}

export const currentStage = (info: VersionProcessInfo) => {
  let currentIndex = 0;
  for (const versionStage of info.version_stages) {
    if (versionStage.status === VersionStageStatus.OnProgress) {
      return {
        currentIndex,
        currentStage: versionStage,
      };
    }
    currentIndex++;
  }
  return undefined;
};

export interface VersionStageChecklistStatus {
  versionInfo: VersionProcessInfo;
  stageCheckStatus: { stageInfo: VersionStageInfo; checklist: VersionStageCheckList }[];
}
