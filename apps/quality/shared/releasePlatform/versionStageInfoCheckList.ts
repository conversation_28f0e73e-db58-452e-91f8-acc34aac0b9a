import { User } from '@pa/shared/dist/src/core';
import { CrashType } from '@shared/typings/slardar/crash/issueListSearch';

export enum CheckItemStatus {
  Blocked = 0, // 阻塞
  Exempt = 1, // 豁免
  TBD = 2, // 待定
}

export enum ItemType {
  FunctionalBug = 0, // Bug
  Slardar = 1, // 性能
  Business = 2, // 业务指标（目前不用）
  QABM = 3, // 人工项
  RDBM = 4,
  MetricData = 10, // 业务指标
}

export enum StabilityMetricsItemStatus {
  Blocked = 0,
  Exempted = 1,
  TBD = 2,
  Pass = 3,
}

export interface BaseItemInfo {}

export interface StabilityMetricItemInfo extends BaseItemInfo {
  slardar_info_id: string;
  crash_type: CrashType;
  status: StabilityMetricsItemStatus;
  owners: User[];
  owners_email?: string[];
  result_detail: string;
}

export enum BugResolveRatioItemStatus {
  Blocked = 0,
  Pass = 1,
  Exempted = 2,
  Assessment = 3,
}

export interface MetricItemInfo extends BaseItemInfo {
  check_item_id: string;
  businessName: string;
  owner: User;
  email: string;
  status: BugResolveRatioItemStatus;
  displayName: string;
  name: string;
  exempt_reason?: string;
  priority?: string;
  teaUrl?: string;
}

export interface VersionStageCheckItem {
  check_item_id: string;
  status: CheckItemStatus; // 0: 阻塞， 1: 豁免，2: 已解决
  description: string;
  owner: User | undefined;
  item_type: ItemType; // 功能性bug/崩溃/用户反馈/其他
  result_detail?: string;
  item_info: any; // 问题链接等信息
  department: string; // 业务线
}
export interface VersionStageCheckList {
  status: number; // 0: 阻塞, 1: 准出
  check_items: VersionStageCheckItem[];
  stage: string;
  version: string;
  app_id: number;
}

export interface SlardarItemInfo {
  slardar_info_id: string;
  crash_type: CrashType;
}
