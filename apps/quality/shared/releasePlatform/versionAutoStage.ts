import { CrashType } from '@shared/typings/slardar/crash/issueListSearch';
import { CheckItemStatus } from '@shared/releasePlatform/versionStageInfoCheckList';
import { SlardarPlatformType } from '@pa/shared/dist/src/appSettings/appSettings';
import { VersionType } from '@shared/utils/version_utils';

export interface VersionAutoStageExtraData {
  real_start_time: number; // 版本开始时间
  real_start_hour: number; // 人数超过 2w 的小时
  // stage_name: string;
  // subStage_name: string;
  // bm_release_group_chatId: string;
  per_version: string;
  per_version_code: string;
  per_version_type: VersionType;
  per_real_start_time: number;
  per_real_start_hour: number;
}

export interface VersionAutoStage {
  version: string;
  version_code: string;
  version_type: VersionType;
  crash_type: CrashType;
  status: CheckItemStatus; // 自动准出结果
  checked_count: number; // 是否已经检查完成
  app_id: number;
  platform: SlardarPlatformType;
  // stage: string;
  // sub_stage: string;
  auto_stage_info: { [key: string]: any };
  extra_data: VersionAutoStageExtraData;
}
