import { CrashType } from '@shared/typings/slardar/crash/issueListSearch';
import {
  MultiAttributionIssueListSortType,
  MultiAttributionScene,
  MultiAttributionType,
} from '@shared/multiAttribution/CommonModel';

export enum MultiAttributionURLQueryKey {
  /**
   * @type MultiAttributionScene
   */
  TAB = 'tab',
  /**
   * @type MultiAttributionType
   */
  TYPE = 'type',
  BASE_SLARDAR_URL = 'base_slardar_url',
  TARGET_SLARDAR_URL = 'target_slardar_url',
  /**
   * @type MultiAttributionIssueListSortType
   */
  ISSUE_LIST_SORT_TYPE = 'issue_sort_type',

  /**
   * 以下参数用于版本归因
   */
  CRASH_TYPE = 'crash_type',
  BASE_VERSION_CODE = 'base_version_code',
  TARGET_VERSION_CODE = 'target_version_code',
  BASE_UPDATE_VERSION_CODE = 'base_update_version_code',
  TARGET_UPDATE_VERSION_CODE = 'target_update_version_code',
  BASE_RELEASE_TIME = 'base_release_time',
  TARGET_RELEASE_TIME = 'target_release_time',
  BASE_START_TIME = 'base_start_time',
  TARGET_START_TIME = 'target_start_time',
  BASE_END_TIME = 'base_end_time',
  TARGET_END_TIME = 'target_end_time',
}

export class MultiAttributionURLQuery {
  [MultiAttributionURLQueryKey.TAB]: MultiAttributionScene;
  [MultiAttributionURLQueryKey.TYPE]: MultiAttributionType;
  [MultiAttributionURLQueryKey.BASE_SLARDAR_URL]: string;
  [MultiAttributionURLQueryKey.TARGET_SLARDAR_URL]: string;
  [MultiAttributionURLQueryKey.ISSUE_LIST_SORT_TYPE]: MultiAttributionIssueListSortType;
  [MultiAttributionURLQueryKey.CRASH_TYPE]?: CrashType;
  [MultiAttributionURLQueryKey.BASE_VERSION_CODE]: string;
  [MultiAttributionURLQueryKey.TARGET_VERSION_CODE]: string;
  [MultiAttributionURLQueryKey.BASE_UPDATE_VERSION_CODE]: string;
  [MultiAttributionURLQueryKey.TARGET_UPDATE_VERSION_CODE]: string;
  [MultiAttributionURLQueryKey.BASE_RELEASE_TIME]: number;
  [MultiAttributionURLQueryKey.TARGET_RELEASE_TIME]: number;
  [MultiAttributionURLQueryKey.BASE_START_TIME]: number;
  [MultiAttributionURLQueryKey.TARGET_START_TIME]: number;
  [MultiAttributionURLQueryKey.BASE_END_TIME]: number;
  [MultiAttributionURLQueryKey.TARGET_END_TIME]: number;

  static stringTypeKeys(): MultiAttributionURLQueryKey[] {
    return [
      MultiAttributionURLQueryKey.BASE_SLARDAR_URL,
      MultiAttributionURLQueryKey.TARGET_SLARDAR_URL,
      MultiAttributionURLQueryKey.BASE_VERSION_CODE,
      MultiAttributionURLQueryKey.TARGET_VERSION_CODE,
      MultiAttributionURLQueryKey.BASE_UPDATE_VERSION_CODE,
      MultiAttributionURLQueryKey.TARGET_UPDATE_VERSION_CODE,
    ];
  }

  static numberTypeKeys(): MultiAttributionURLQueryKey[] {
    return [
      MultiAttributionURLQueryKey.BASE_RELEASE_TIME,
      MultiAttributionURLQueryKey.TARGET_RELEASE_TIME,
      MultiAttributionURLQueryKey.BASE_START_TIME,
      MultiAttributionURLQueryKey.TARGET_START_TIME,
      MultiAttributionURLQueryKey.BASE_END_TIME,
      MultiAttributionURLQueryKey.TARGET_END_TIME,
    ];
  }

  constructor(searchParams: URLSearchParams) {
    MultiAttributionURLQuery.stringTypeKeys().forEach(typeKey => {
      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
      // @ts-ignore
      this[typeKey] = searchParams.get(typeKey) ?? '';
    });
    MultiAttributionURLQuery.numberTypeKeys().forEach(typeKey => {
      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
      // @ts-ignore
      this[typeKey] = Number(searchParams.get(typeKey)) ?? 0;
    });

    let tab = MultiAttributionScene.MARKET;
    const paramTab = searchParams.get(MultiAttributionURLQueryKey.TAB);
    if (paramTab && Object.values(MultiAttributionScene).includes(paramTab as MultiAttributionScene)) {
      tab = paramTab as MultiAttributionScene;
    }
    this[MultiAttributionURLQueryKey.TAB] = tab;

    let type = MultiAttributionType.COMPARE;
    const paramType = searchParams.get(MultiAttributionURLQueryKey.TYPE);
    if (paramType && Object.values(MultiAttributionType).includes(Number(paramType))) {
      type = Number(paramType) as MultiAttributionType;
    }
    this[MultiAttributionURLQueryKey.TYPE] = type;

    let sortType = MultiAttributionIssueListSortType.TARGET_RANKING;
    const paramSortType = searchParams.get(MultiAttributionURLQueryKey.ISSUE_LIST_SORT_TYPE);
    if (paramType && Object.values(MultiAttributionIssueListSortType).includes(Number(paramSortType))) {
      sortType = Number(paramSortType) as MultiAttributionIssueListSortType;
    }
    this[MultiAttributionURLQueryKey.ISSUE_LIST_SORT_TYPE] = sortType;

    const paramCrashType = searchParams.get(MultiAttributionURLQueryKey.CRASH_TYPE);
    if (paramCrashType && Object.values(CrashType).includes(paramCrashType as CrashType)) {
      this[MultiAttributionURLQueryKey.CRASH_TYPE] = paramCrashType as CrashType;
    }
  }
}
