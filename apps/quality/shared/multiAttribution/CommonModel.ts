/**
 * 多维归因类型
 */
export enum MultiAttributionType {
  /**
   * 对比分析
   */
  COMPARE = 1,
  /**
   * 多维分析
   */
  DIMENSION = 2,
}

/**
 * 多维归因场景
 */
export enum MultiAttributionScene {
  /**
   * 大盘
   */
  MARKET = 'market',
  /**
   * 单个issue
   */
  ISSUE = 'issue',
  /**
   * 版本归因
   */
  VERSION = 'version',
}

export enum MultiAttributionIssueListSortType {
  /**
   * 基准issueList的顺序
   */
  BASE_RANKING,
  /**
   * 目标issueList的顺序
   */
  TARGET_RANKING,
  /**
   * 异常用户率的变化率降序（劣化优先）
   */
  DELTA_CRASH_USER_RATE_PERCENT_DESC,
  /**
   * 异常用户率的变化率升序（优化优先）
   */
  DELTA_CRASH_USER_RATE_PERCENT_ASC,
  /**
   * 异常用户率的差值绝对值降序（劣化优先）
   */
  DELTA_CRASH_USER_RATE_DESC,
  /**
   * 异常用户率的差值绝对值升序（优化优先）
   */
  DELTA_CRASH_USER_RATE_ASC,
}

export function getMultiAttributionIssueListSortTypeName(type: MultiAttributionIssueListSortType) {
  switch (type) {
    case MultiAttributionIssueListSortType.BASE_RANKING:
      return '基准排序';
    case MultiAttributionIssueListSortType.TARGET_RANKING:
      return '目标排序';
    case MultiAttributionIssueListSortType.DELTA_CRASH_USER_RATE_PERCENT_DESC:
      return '异常用户率-劣化优先(变化率)';
    case MultiAttributionIssueListSortType.DELTA_CRASH_USER_RATE_PERCENT_ASC:
      return '异常用户率-优化优先(变化率)';
    case MultiAttributionIssueListSortType.DELTA_CRASH_USER_RATE_DESC:
      return '异常用户率-劣化优先(变化值)';
    case MultiAttributionIssueListSortType.DELTA_CRASH_USER_RATE_ASC:
      return '异常用户率-优化优先(变化值)';
    default:
      return '未知';
  }
}
export type MultiAttributionRequestIssueCount = 10 | 20 | 50 | 100 | 200 | 500;
