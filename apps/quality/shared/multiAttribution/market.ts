import { CrashListItem } from '@shared/typings/slardar/crash/issueListSearch';
import { FlexSeriesItem } from '@shared/typings/slardar/flex/querySeries';
import { MultiAttributionIssueListSortType } from '@shared/multiAttribution/CommonModel';

export interface MultiAttributionResponse<T> {
  code: number;
  message: string;
  data?: T;
}

export interface MultiAttributionIssueItem {
  rank: number;
  rawItem: CrashListItem;
  slardarUrl: string;
  crashCountRate: number;
  crashUserCountRate: number;
}

export interface MultiAttributionIssueListData {
  crashType: string;
  items: MultiAttributionIssueItem[];
  realTotalCount: number;
  totalCount: number;
  totalCrashCountRate: number;
  totalCrashUserCountRate: number;
  isDailyInterval: boolean;
  launchCountItem: FlexSeriesItem;
  launchUserCountItem: FlexSeriesItem;
  userRatioItem?: FlexSeriesItem;
  countRatioItem?: FlexSeriesItem;
}

export interface MultiAttributionComparedIssueItem {
  base?: MultiAttributionIssueItem;
  target?: MultiAttributionIssueItem;

  baseRank: number;
  baseCrashCount: number;
  baseCrashCountRate: number;
  baseCrashUserCount: number;
  baseCrashUserCountRate: number;

  targetRank: number;
  targetCrashCount: number;
  targetCrashCountRate: number;
  targetCrashUserCount: number;
  targetCrashUserCountRate: number;

  deltaRank: number;
  deltaCrashCount: number;
  deltaCrashCountRate: number;
  deltaCrashUserCount: number;
  deltaCrashUserCountRate: number;

  deltaCrashCountPercent: number;
  deltaCrashCountRatePercent: number;
  deltaCrashUserCountPercent: number;
  deltaCrashUserCountRatePercent: number;

  // for display
  baseCrashCountRateWithUnit: string;
  baseCrashUserCountRateWithUnit: string;
  targetCrashCountRateWithUnit: string;
  targetCrashUserCountRateWithUnit: string;

  deltaCrashCountRateWithUnit: string;
  deltaCrashUserCountRateWithUnit: string;

  deltaCrashCountPercentWithUnit: string;
  deltaCrashCountRatePercentWithUnit: string;
  deltaCrashUserCountPercentWithUnit: string;
  deltaCrashUserCountRatePercentWithUnit: string;
}

export interface MultiAttributionComparedIssueListData {
  items: MultiAttributionComparedIssueItem[];
  baseIssueListData: MultiAttributionIssueListData;
  targetIssueListData: MultiAttributionIssueListData;
}

export interface MultiAttributionComparedIssueSortResult {
  items: MultiAttributionComparedIssueItem[];
  sortType: MultiAttributionIssueListSortType;
}

function sortComparedIssueList(
  items: MultiAttributionComparedIssueItem[],
  sortType: MultiAttributionIssueListSortType,
): MultiAttributionComparedIssueSortResult {
  const sortedItems = [...items];
  sortedItems.sort((a, b): number => {
    switch (sortType) {
      case MultiAttributionIssueListSortType.BASE_RANKING:
        if (a.baseRank === 0 && b.baseRank === 0) {
          return a.targetRank - b.targetRank;
        } else if (a.baseRank === 0) {
          return 1;
        } else if (b.baseRank === 0) {
          return -1;
        }
        return a.baseRank - b.baseRank;
      case MultiAttributionIssueListSortType.TARGET_RANKING:
        if (a.targetRank === 0 && b.targetRank === 0) {
          return a.baseRank - b.baseRank;
        } else if (a.targetRank === 0) {
          return 1;
        } else if (b.targetRank === 0) {
          return -1;
        }
        return a.targetRank - b.targetRank;
      case MultiAttributionIssueListSortType.DELTA_CRASH_USER_RATE_PERCENT_DESC:
        if (a.baseCrashUserCount === 0 && b.baseCrashUserCount === 0) {
          // 两个都是新增问题 base(0) -> target(xxx)，按照target的rank排序
          return a.targetRank - b.targetRank;
        } else if (a.baseCrashUserCount === 0) {
          // a是新增问题，涨幅是+Infinity%
          return -1;
        } else if (b.baseCrashUserCount === 0) {
          // b是新增问题
          return 1;
        } else if (a.targetCrashUserCount === 0 && b.targetCrashUserCount === 0) {
          // 两个都是已经修复的问题，按照base的rank排序
          return b.baseRank - a.baseRank;
        } else if (a.targetCrashUserCount === 0) {
          return 1;
        } else if (b.targetCrashUserCount === 0) {
          return -1;
        } else {
          return b.deltaCrashUserCountRatePercent - a.deltaCrashUserCountRatePercent;
        }
      case MultiAttributionIssueListSortType.DELTA_CRASH_USER_RATE_PERCENT_ASC:
        if (a.baseCrashUserCount === 0 && b.baseCrashUserCount === 0) {
          // 两个都是新增问题 base(0) -> target(xxx)，按照target的rank排序
          return b.targetRank - a.targetRank;
        } else if (a.baseCrashUserCount === 0) {
          // a是新增问题，涨幅是+Infinity%
          return 1;
        } else if (b.baseCrashUserCount === 0) {
          // b是新增问题
          return -1;
        } else if (a.targetCrashUserCount === 0 && b.targetCrashUserCount === 0) {
          // 两个都是已经修复的问题，按照base的rank排序
          return a.baseRank - b.baseRank;
        } else if (a.targetCrashUserCount === 0) {
          return -1;
        } else if (b.targetCrashUserCount === 0) {
          return 1;
        } else {
          return a.deltaCrashUserCountRatePercent - b.deltaCrashUserCountRatePercent;
        }
      case MultiAttributionIssueListSortType.DELTA_CRASH_USER_RATE_DESC:
        if (a.deltaCrashUserCountRate === b.deltaCrashUserCountRate) {
          // 涨幅相同，按照rank排序
          return a.targetRank - b.targetRank;
        }
        return b.deltaCrashUserCountRate - a.deltaCrashUserCountRate;
      case MultiAttributionIssueListSortType.DELTA_CRASH_USER_RATE_ASC:
        if (a.deltaCrashUserCountRate === b.deltaCrashUserCountRate) {
          // 涨幅相同，按照rank排序
          return b.targetRank - a.targetRank;
        }
        return a.deltaCrashUserCountRate - b.deltaCrashUserCountRate;
      default:
        return 0;
    }
  });
  return {
    items: sortedItems,
    sortType,
  };
}

export const MarketTools = {
  sortComparedIssueList,
};
