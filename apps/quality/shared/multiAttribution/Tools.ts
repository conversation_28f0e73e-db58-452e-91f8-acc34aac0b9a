import { Condition } from '@shared/typings/slardar/crash/issueListSearch';
import { FlexFilter, FlexMeasure } from '@shared/typings/slardar/flex/querySeries';
import { MAIN_HOST_HTTPS, SlardarPlatformType } from '@pa/shared/dist/src/appSettings/appSettings';

export interface Response<T> {
  code: number;
  message: string;
  data?: T;
}

export interface ParsedSlardarMarketUrlData {
  aid: number;
  os: string;
  region: string;
  start_time: number;
  end_time: number;
  granularity: number;
  filters_conditions: Condition;
  filters_conditions_raw?: string;
  order_by: string;
  crash_time_type: string;
  crash_type: string;
  issue_id?: string;
  raw_params: string;
}

const SlardarQueryUserCountFilterList = [
  {
    label: 'App版本',
    filter_name: 'app_version',
    display_template: 'Symbol',
    filter_type: 'string_regex_range',
    filter_name_type: 0,
    comment: {
      link: '',
      comment: '',
      event_comment: '',
    },
  },
  // {
  // 	"label": "小版本号",
  // 	"filter_name": "update_version_code",
  // 	"display_template": "Symbol",
  // 	"filter_type": "string_regex_range",
  // 	"filter_name_type": 0,
  // 	"comment": {
  // 		"link": "",
  // 		"comment": "",
  // 		"event_comment": ""
  // 	}
  // },
  {
    label: '操作系统版本号',
    filter_name: 'os_version',
    display_template: 'Symbol',
    filter_type: 'string_regex_range',
    filter_name_type: 0,
    comment: {
      link: '',
      comment: '',
      event_comment: '',
    },
  },
  {
    label: '操作系统大版本号',
    filter_name: 'os_major_version',
    display_template: 'SimpleSymbol',
    filter_type: 'string',
    filter_name_type: 0,
    comment: {
      link: '',
      comment: '',
      event_comment: '',
    },
  },
  {
    label: '机型',
    filter_name: 'device_model',
    display_template: 'RegexAndNotRegexEqAndNeqSymbol',
    filter_type: 'regex',
    filter_name_type: 0,
    comment: {
      link: '',
      comment: '',
      event_comment: '',
    },
  },
  {
    label: 'ROM',
    filter_name: 'rom',
    display_template: 'RegexAndInAndNinSymbol',
    filter_type: 'regex',
    filter_name_type: 0,
    comment: {
      link: '',
      comment: '',
      event_comment: '',
    },
  },
  {
    label: '国家/地区',
    filter_name: 'region',
    display_template: 'SimpleSymbol',
    filter_type: 'regex',
    filter_name_type: 0,
    comment: {
      link: '',
      comment: '',
      event_comment: '',
    },
  },
  {
    label: '国家/地区(大写)',
    filter_name: 'region_upper',
    display_template: 'SimpleSymbol',
    filter_type: 'regex',
    filter_name_type: 0,
    comment: {
      link: '',
      comment: '',
      event_comment: '',
    },
  },
  {
    label: '渠道',
    filter_name: 'channel',
    display_template: 'RegexAndNotRegexEqAndNeqSymbol',
    filter_type: 'regex',
    filter_name_type: 0,
    comment: {
      link: '',
      comment: '',
      event_comment: '',
    },
  },
  {
    label: '设备类别',
    filter_name: 'device_category',
    display_template: 'SimpleSymbol',
    filter_type: 'string',
    filter_name_type: 0,
    comment: {
      link: '',
      comment: '',
      event_comment: '',
    },
  },
  {
    label: 'Aid',
    filter_name: 'aid',
    display_template: 'SimpleSymbol',
    filter_type: 'string',
    filter_name_type: 0,
    comment: {
      link: '',
      comment: '',
      event_comment: '',
    },
  },
];

// Slardar指标查询，查询用户量时，过滤条件有限
function getQuerySlardarUserCountFilterNameList(isIOS: boolean): string[] {
  const filterNameList = SlardarQueryUserCountFilterList.map(item => item.filter_name);
  if (isIOS) {
    filterNameList.push('raw_update_version_code');
  } else {
    filterNameList.push('update_version_code');
  }
  return filterNameList;
}

export default class Tools {
  private static parseUrl(url: string) {
    const parsedUrl = new URL(url);

    // 获取 scheme, host 和 pathname
    const scheme = parsedUrl.protocol.slice(0, -1); // 去掉末尾的 ':'
    const host = parsedUrl.hostname;
    const { pathname } = parsedUrl;

    // 获取 query 参数
    const queryParams: { [key: string]: string } = {};
    parsedUrl.searchParams.forEach((value, key) => {
      queryParams[key] = value;
    });

    // 获取 hash 参数
    const hashParams = parsedUrl.hash.startsWith('#') ? parsedUrl.hash.slice(1) : parsedUrl.hash;
    let hashPath = '';
    let hashQueryParams: { [key: string]: string } = {};

    if (hashParams.includes('?')) {
      const [path, query] = hashParams.split('?');
      hashPath = path || '';
      if (query) {
        hashQueryParams = Object.fromEntries(new URLSearchParams(query).entries());
      }
    }

    return {
      scheme,
      host,
      pathname,
      queryParams,
      hashParams,
      hashPath,
      hashQueryParams,
    };
  }

  static parseSlardarMarketUrl(url: string): Response<ParsedSlardarMarketUrlData> {
    const parsedUrl = this.parseUrl(url);

    const { host } = parsedUrl;
    const { pathname } = parsedUrl;
    if (host !== 'slardar.bytedance.net' && host !== 'slardar-us.bytedance.net') {
      return { code: -1, message: 'Host incorrect.' };
    }
    if (pathname !== '/node/app_detail/') {
      return { code: -1, message: 'Path incorrect.' };
    }

    const { aid } = parsedUrl.queryParams;
    const { os } = parsedUrl.queryParams;
    const { region } = parsedUrl.queryParams;
    const { hashPath } = parsedUrl;
    const params = JSON.parse(parsedUrl.hashQueryParams.params);
    const { start_time } = params;
    const { end_time } = params;
    const { granularity } = params;
    const { filters_conditions } = params;
    const { order_by } = params;
    const { crash_time_type } = params;
    const hashPathList = hashPath.split('/');
    const crash_type = hashPathList.length === 3 ? hashPathList[2] : hashPathList[3];
    const issue_id = hashPathList.length === 5 ? hashPathList[4] : undefined;
    return {
      code: 0,
      message: 'success',
      data: {
        aid: Number(aid),
        os,
        region,
        start_time: Number(start_time),
        end_time: Number(end_time),
        granularity: Number(granularity),
        filters_conditions,
        filters_conditions_raw: filters_conditions ? JSON.stringify(filters_conditions) : undefined,
        order_by,
        crash_time_type,
        crash_type,
        issue_id,
        raw_params: parsedUrl.hashQueryParams.params,
      },
    };
  }

  static isValidURL(url: string): boolean {
    try {
      new URL(url);
      return true;
    } catch (e) {
      return false;
    }
  }

  static buildMeasureObject(name: string, measure_name: string): FlexMeasure {
    return {
      type: 'monomial',
      formula: '',
      name,
      unit: {
        unit_type: 'number',
        unit: '1',
      },
      raw_measure_list: [
        {
          measure_name,
          filter_list: [],
        },
      ],
    };
  }

  static buildFilterList(
    filters_conditions: Condition,
    os: string,
    isOnlyUserCount: boolean,
    crash_type: string,
  ): FlexFilter[] {
    const { sub_conditions } = filters_conditions;
    if (sub_conditions === undefined || sub_conditions.length <= 0) {
      return [];
    }

    const isIOS = os === SlardarPlatformType.iOS;
    const filterNameList = getQuerySlardarUserCountFilterNameList(isIOS);
    const filterList: FlexFilter[] = [];

    for (const condition of sub_conditions) {
      if (condition.dimension === undefined) {
        continue;
      }
      if (isOnlyUserCount && !filterNameList.includes(condition.dimension)) {
        // 请求用户量的过滤条件有限
        continue;
      }
      if (condition.dimension === 'filters_map') {
        const dimension = isIOS ? 'filters' : `android_${crash_type}.filters`;
        // 自定义过滤字段
        filterList.push({
          filter_name: JSON.stringify({
            dimension: dimension,
            map_key: condition.map_key,
          }),
          op: condition.op,
          type: '',
          values: condition.value ? [condition.value] : condition.values,
        });
        continue;
      }
      if (isIOS && condition.dimension === 'raw_update_version_code') {
        filterList.push({
          filter_name: 'update_version_code',
          op: condition.op,
          type: condition.type,
          values: condition.values?.map(v => (v === 'DAU TOP3 version' ? 'top3' : v)),
        });
        continue;
      }
      filterList.push({
        filter_name: condition.dimension,
        op: condition.op,
        type: condition.type,
        values: condition.value ? [condition.value] : condition.values,
      });
    }
    return filterList;
  }

  static formatNumber(num: number): string {
    if (num >= 1000000000) {
      return `${(num / 1000000000).toFixed(2)}B`;
    } else if (num >= 1000000) {
      return `${(num / 1000000).toFixed(2)}M`;
    } else if (num >= 1000) {
      return `${(num / 1000).toFixed(2)}K`;
    } else {
      return num.toString();
    }
  }

  static fixNanNumber(num: number): number {
    if (isNaN(num)) {
      return 0;
    }
    return num;
  }

  static formatNumber2Percent(num: number): string {
    return `${(num * 100).toFixed(2)}%`;
  }

  static formatNumber2Permillage(num: number): string {
    return `${(num * 1000).toFixed(3)}‰`;
  }

  static getPaperAirplaneUrl() {
    if (process.env.NODE_ENV === 'development') {
      return 'http://localhost:8080';
    }
    return MAIN_HOST_HTTPS;
  }
}
