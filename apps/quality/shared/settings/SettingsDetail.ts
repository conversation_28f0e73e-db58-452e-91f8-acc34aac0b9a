export interface SettingsDetail {
  key: string;
  item_id: number;
  name: string;
  app_info: Appinfo[];
  status: number; // status === 0 时未生效。
  is_reviewing: boolean;
  is_deploying: boolean;
  need_qa: boolean;
  owner: Owner[];
  admin: Owner[];
  developer: any[];
  qa: any[];
  notify_user: any[];
  notify_group: any[];
  code: string;
  online_code: string;
  deploying_code: string;
  online_review_id: number;
  deploying_review_id: number;
  deploying_deploy_id: number;
  code_type: number;
  review: Review;
  deploy: Deploy;
  subscribed: boolean;
  create_time: number;
  modify_time: number;
  brief: string;
  description: string;
  draft: Draft;
  match_str: string;
  app: Appinfo;
  schema: string;
  version_static: boolean;
  online_version_static: boolean;
  flight_url: string;
  filter: string;
  tags?: any;
  bizlines?: any;
  bizlines_str: any[];
  scene: string;
  app_id: number;
  publish_ab_code: string;
  flight_id: number;
  flight_status: number;
  is_ab_item: boolean;
  collect_deploy_info: boolean;
  auto_sync_boe: boolean;
  draft_info: Draftinfo;
  online_info: Draftinfo;
  latest_info: Draftinfo;
  item_type: number;
  step: number;
}

export interface Draftinfo {
  code: string;
  item_filter: string;
  item_vars: string;
  extra: Extra;
}

export interface Draft {
  item_id: number;
  content: string;
  item_filter: string;
  item_vars: string;
  create_time: number;
  modify_time: number;
}

export interface Deploy {
  deploy_id: number;
  item_id: number;
  item_name: string;
  brief: string;
  status: number;
  review: Review;
  base_code: string;
  auto_next_step: boolean;
  create_time: number;
  modify_time: number;
  deployer: Owner;
  process: Process;
  op_info: Opinfo;
  item_status: number;
  app: Appinfo;
  deploy_set: any[];
  commits: Commits;
}

export interface Commits {
  commit_list: Commitlist[];
  error: boolean;
  reverse: boolean;
}

export interface Commitlist {
  review_id: number;
  commit_msg: string;
  apply_user: Owner;
  create_time: number;
  op_time: number;
}

export interface Opinfo {
  s_0?: any;
  s_1: S1;
  s_2: S1;
  s_3: S1;
  s_4: S1;
  s_5: S1;
  s_100: S1;
  s_101: S1;
}

export interface S1 {
  op_uname: string;
  op_time: number;
}

export interface Process {
  total: number;
  s_1: number;
  s_2: number;
  s_3: number;
  s_4: number;
  s_5: number;
}

export interface Review {
  review_id: number;
  item_id: number;
  item_name: string;
  status: number;
  item_status: number;
  code: string;
  ori_code: string;
  create_time: number;
  modify_time: number;
  uname: string;
  reviewer_list: Owner[];
  reviewer: Owner;
  rd_review_status: number;
  qa_reviewer_list: any[];
  qa_reviewer: Owner;
  qa_review_status: number;
  commit_msg: string;
  item_latest_review_id: number;
  code_type: number;
  app: Appinfo;
  reason: string;
  is_skip_ut: boolean;
  skip_reason: string;
  origin_info: Origininfo;
  now_info: Origininfo;
  online_info: Origininfo;
  latest_info: Origininfo;
  is_online_version: boolean;
  extra: Extra;
  prepare_to_deploy_status: number;
  allow_list?: any;
  review_list: Reviewlist[];
  cancel_reason: string;
  cancel_time: number;
  ut_result: string;
  api_version: number;
  ut_coverage: Utcoverage;
  deploy_id: number;
}

export interface Utcoverage {
  code_type: number;
  json_info?: any;
  python_info: string;
}

export interface Reviewlist {
  review_type: number;
  status: number;
  final_reviewer: Owner;
  comment: string;
  review_time: number;
  review_url: string;
  reviewers: Owner[];
}

export interface Extra {
  ab_solidified_version: boolean;
  proxy_ab_id: number;
  deploy_template_id: number;
  allow_list?: any;
}

export interface Origininfo {
  code: string;
  item_filter: string;
  item_vars: string;
}

export interface Owner {
  uname: string;
}

export interface Appinfo {
  id: number;
  biz_type: number;
  biz_id: number;
  name: string;
  cn_name: string;
  product_id: number;
  product_name: string;
  subscribed: boolean;
  share_biz_ids: number[];
  code_type_list: number[];
  app_admins: string[];
  app_ids: any[];
  check_url: string;
  app_notice: string;
  push_app_ids: any[];
  need_qa_review: boolean;
  create_time: number;
  modify_time: number;
  filter_rule: Filterrule;
}

export interface Filterrule {
  '0': _0;
}

export interface _0 {
  android: number;
  androidpad: number;
  ipad: number;
  iphone: number;
  pc: number;
}

// settings 实验相关详情
export interface UselessSettingItem {
  setting_id: number;
  setting_key: string;
  brief: string;
  status: string;
  publish_time: number;
  setting_link: string;
  owners: string[];
  admins: string[];
  is_ios_used: boolean;
  ios_admin: string;
  ios_admin_cn: string;
  ios_admin_group: string;
  is_android_used: boolean;
  android_admin: string;
  android_admin_cn: string;
  android_admin_group: string;
  ios_meego_id?: number;
  android_meego_id?: number;
}

export interface SettingAdmin {
  uname: string;
  cn_name: string;
  group: string;
}
