export interface SettingsABTestConfig {
  item_id: number;
  flight_id: number;
  flight_status: number;
  link: string;
  name: string;
  type: string;
  description: string;
  publish_type: number;
  publish_status: number;
  ab_filter: string;
  ab_filter_code: string;
  ab_params: SettingsABTestDetailParam[];
  desc: string;
  region: string;
}

export enum PublishStatus {
  Published = 0, // 已固化
  UnPublished = 1, // 未固化
}

export interface SettingsABTestDetailParam {
  name: string;
  vid: string;
  settings_key: SettingsABTestDetailSettingsKey[];
  publish_ab_code: string;
}

export interface SettingsABTestDetailSettingsKey {
  allowAdd: boolean;
  desc: string;
  item_id: number;
  key: string;
  parseValue: unknown;
  prefix: string;
  publish_ab_code: string;
  settings_item_id: number;
  type: string;
  value: string;
}
