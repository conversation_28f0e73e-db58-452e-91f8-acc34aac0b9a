import { PlatformType } from '@pa/shared/dist/src/core';
import { EventsItem } from '@shared/bits/calendar';
import { BmType } from '@shared/bits/bmInfo';

/**
 * 定义版本流程主表
 */
export interface VersionProcess {
  product: ProductType; // lv or cc
  productCN: string; // 剪映 or CapCut
  platform: PlatformType;
  version: string; // 11.0.0
  versionNum: number; // 1100
  versionCode: string; // 8900000(最新灰度轮次的小版本号)
  versionState: VersionState[]; // 当前版本状态
  calendarOriginal?: EventsItem; // 原始版本日历
  bmInfo: Record<number, BuildMasterInfo>;
  pcInfo?: PCInfo;
  over: boolean;
  meegoId?: number;
}

export enum LVProductType {
  lv = 'lv',
  cc = 'cc',
}

export enum RetouchProductType {
  retouch = 'retouch',
  hypic = 'hypic',
}

export type ProductType = string;

export interface VersionState {
  name: string;
  stage: VersionStage; // 版本阶段
  progress: ProgressState; // 当前阶段的进展
  versionCode: string; // android当前灰度轮次的小版本号，如果是其他Stage则为0;iOS长版本号
  executeInfo: ExecuteTime;
  release_rates?: string[];
}

/**
 * 各阶段执行时间记录：时间戳
 * 单位为秒
 */
export interface ExecuteTime {
  planStartTime: number;
  planEndTime: number;
  actualStartTime: number;
  actualEndTime: number;
}

export enum ProgressState {
  NotStart = 'NotStart',
  OnGoing = 'OnGoing',
  Complete = 'Complete',
  Failed = 'Failed',
}

/**
 * 定义不用的版本阶段
 * 其他阶段都插在系统测试和上线前中间，这个根据bits动态更新，不预先定义
 */

export enum VersionStage {
  develop = 'develop', // 开发阶段
  integration = 'integration', // 集成(封板)阶段
  integrationTest = 'integrationTest', // 集成测试
  normalGray = 'normalGray', // 普通灰度
  testFlight = 'testFlight', // testFlight灰度
  smallFlow = 'smallFlow', // 小流量
  systemTest = 'systemTest', // 系统测试
  strict = 'strict', // 上线前阶段
  submit = 'submit', // 提审阶段

  // 这个阶段android新增
  crowdTest = 'crowdTest',

  // 这两个阶段只有iOS有
  submitBeforeFirstGray = 'submitBeforeFirstGray', // 一灰前预审
  submitBeforeFulltestFlight = 'submitBeforeFulltestFlight', // 全量TF预审
}

export function isGrayStage(stage: VersionStage) {
  return stage === VersionStage.normalGray || stage === VersionStage.testFlight || stage === VersionStage.smallFlow;
}

/**
 * 版本订阅事件
 */
// export interface VersionSubscription {
//   product: string;
//   productCN: string;
//   platform: string;
//   version: string;
//   subscriptionType : SubscriptionType; // 订阅类型
//   observer: UserInfo[];
// }

export interface VersionSubscription {
  user_email: string;
  platform: string;
  subscriptions: SubscriptionDetail[];
  product: string;
  productCN: string;
  version: string;
}

export interface BuildMasterInfo {
  type: BmType;
  email: string; // 包含@bytedance.com
  nameCN: string;
  avatarUrl: string; // 头像URL
  openId: string; // larkId
}

/**
 * "lv_version": "11.0.0",
 * "lvpro_version": "4.5.0",
 * "ve_version": "14.7.0",
 * "draft_version": "81.0.0",
 * "cc_version": "9.0.0",
 * "ccpc_version": "2.5.0"
 */
export interface PCInfo {
  lv_version: string;
  lvpro_version: string;
  ve_version: string;
  draft_version: string;
  cc_version: string;
  ccpc_version: string;
  bmInfo: Record<number, BuildMasterInfo> | undefined;
}

/**
 * 支持订阅的消息类型
 */
export enum SubscriptionType {
  important = '重要通知', // 重要通知
  gray = '灰度信息同步', // 灰度信息同步
  grayDelay = '灰度延期风险同步', // 灰度延期风险同步
  submitDelay = '提审延期风险同步', // 提审延期风险同步
  smallFlowDelay = '小流量延期风险同步', // 小流量延期风险同步
  fullReleaseDelay = '版本全量延期同步', // 版本全量延期同步
  versionProgress = '版本节奏同步', // 版本节奏同步
  versionCreate = '增加小版本', // 增加小版本
  smallFlow = '小流量提审信息同步', // 小流量提审信息同步
  official = '提审信息同步', // 提审信息同步
  skip = '跳版信息同步', // 跳版信息同步
}

export interface SubscriptionDetail {
  type: SubscriptionType;
  is_subscribe: boolean;
}

export interface StageInfosItem {
  /**
   *stage名称（用户自定义）
   */
  stage_name: string;
  /**
   *	StageStatus_NOT_STARTED StageStatus = 1
   *	StageStatus_RUNNING     StageStatus = 2
   *	StageStatus_SUCCESS     StageStatus = 3
   *	StageStatus_FAILED      StageStatus = 4
   *	StageStatus_SKIPPED     StageStatus = 5
   *	StageStatus_STOPPED     StageStatus = 6
   */
  stage_status: number;
  /**
   *时间戳
   */
  start_time?: number;
  /**
   *时间戳
   */
  end_time?: number;
  stage_id: string;
  /**
   *StageType_CUSTOM                      StageType = 0 自定义
   *	StageType_BITS_INTEGRATION_REGRESSION StageType = 1  集成&回归
   *	StageType_BITS_PUBLIC_TEST            StageType = 2 测试
   *	StageType_BITS_GRAY                   StageType = 3 灰度
   *	StageType_BITS_OFFICIAL               StageType = 4 正式
   *
   */
  stage_type?: number;
}
