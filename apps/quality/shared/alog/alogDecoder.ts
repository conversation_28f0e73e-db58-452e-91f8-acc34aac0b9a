// import * as fs from 'fs';
// import * as readline from 'readline';
// import * as path from 'path';
import { Alog, AlogContent, AlogLevel, AlogLine } from '@pa/shared/dist/src/alog/models';

export default class AlogDecoder {
  // async decodeWithFilePath(filePath: string): Promise<Alog> {
  //   const dirname = path.dirname(filePath);
  //   const date = new Date();
  //   const newFileName = `${date.getTime()}.txt`; // 新的文件名
  //   const preprocessedFilePath = path.join(dirname, newFileName);
  //   try {
  //     await this.preprocessLogFile(filePath, preprocessedFilePath);
  //     return await this.innerDecodeWithFilePath(preprocessedFilePath);
  //   } finally {
  //     fs.rmSync(preprocessedFilePath, { recursive: false });
  //   }
  // }

  decodeWithLines(lines: string[]): Alog {
    const alog: Alog = {
      contents: [],
    };
    for (const line of lines) {
      this.decodeWithLine(alog, line);
    }
    return alog;
  }

  // private innerDecodeWithFilePath(filePath: string): Promise<Alog> {
  //   return new Promise<Alog>((res, rej) => {
  //     const alog: Alog = {
  //       contents: [],
  //     };
  //
  //     const readStream = fs.createReadStream(filePath);
  //     readStream.on('error', err => {
  //       readStream.destroy();
  //       rej(Error(`readStream on error ${err}`));
  //     });
  //     const rl = readline.createInterface({
  //       input: readStream,
  //       crlfDelay: Infinity,
  //     });
  //     rl.on('line', line => {
  //       if (line) {
  //         this.decodeWithLine(alog, line);
  //       }
  //     })
  //       .on('close', () => {
  //         readStream.destroy();
  //         res(alog);
  //       })
  //       .on('error', err => {
  //         readStream.destroy();
  //         rl.close();
  //         rej(Error(`readline on error ${err}`));
  //       });
  //   });
  // }
  //
  // private preprocessLogFile(filePath: string, newFilePath: string): Promise<void> {
  //   return new Promise<void>((res, rej) => {
  //     // 创建可读流
  //     const readStream = fs.createReadStream(filePath);
  //     readStream.on('error', err => {
  //       readStream.destroy();
  //       rej(Error(`readStream on error:${err}`));
  //     });
  //     // 创建可写流
  //     const writeStream = fs.createWriteStream(newFilePath);
  //     writeStream
  //       .on('error', err => {
  //         writeStream.destroy();
  //         rej(Error(`writeStream on error:${err}`));
  //       })
  //       .on('finish', () => {
  //         res();
  //       });
  //
  //     const rl = readline.createInterface({
  //       input: readStream,
  //       crlfDelay: Infinity,
  //     });
  //
  //     let lastLine = '';
  //     rl.on('line', line => {
  //       // // 去除每行的制表符
  //       if (lastLine === '') {
  //         lastLine = line;
  //       } else {
  //         if (line.includes('\t')) {
  //           const result = line.replace(/\t/g, '');
  //           lastLine += result;
  //         } else {
  //           writeStream.write(`${lastLine}\n`);
  //           writeStream.write(`${line}\n`);
  //           lastLine = '';
  //         }
  //       }
  //     })
  //       .on('close', () => {
  //         if (lastLine !== '') {
  //           writeStream.end(`${lastLine}\n`);
  //         }
  //         readStream.destroy();
  //       })
  //       .on('error', err => {
  //         readStream.destroy();
  //         writeStream.destroy();
  //         rl.close();
  //         rej(Error(`readline on error:${err}`));
  //       });
  //   });
  // }

  private decodeWithLine(alog: Alog, line: string) {
    const alogLine = this.decodeLine(line);
    if (alogLine === undefined) {
      return;
    }
    if (alog.contents.length === 0 || alogLine.body.includes('LV APP START!!!')) {
      const content: AlogContent = {
        lines: [],
      };
      alog.contents.push(content);
    }
    alog.contents[alog.contents.length - 1].lines.push(alogLine);
  }

  private decodeLine(line: string): AlogLine | undefined {
    if (line === '\n' || line.length === 0) {
      return undefined;
    }
    const pattern = /\[(.*?)\]/g;
    const matchResult = line.match(pattern);
    // [time][pid:tid][level][tag][file name, func, line] logbody
    let timeStamp = 0;
    let utc = '';
    let pid = 0;
    let tid = 0;
    let level = AlogLevel.Info;
    let tag = '';
    let fileName = '';
    let func = '';
    let lineNumber = 0;
    let logBody = '';
    if (matchResult && matchResult.length >= 5) {
      const contents: string[] = matchResult.map(match => match.slice(1, -1));
      try {
        [timeStamp, utc] = this.timeStringToTimeStamp(contents[0]);
        const pidAndTid = contents[1];
        const pidAndTidSplit = pidAndTid.split(':');
        if (pidAndTidSplit.length === 2) {
          pid = parseInt(pidAndTidSplit[0], 10);
          tid = parseInt(pidAndTidSplit[1], 10);
        }
        level = contents[2] as AlogLevel;
        tag = contents[3];
        const fileNameFuncLine = contents[4];
        const fileNameFuncLineSplit = fileNameFuncLine.split('，');
        if (fileNameFuncLineSplit.length === 3) {
          fileName = fileNameFuncLineSplit[0].trim();
          func = fileNameFuncLineSplit[1].trim();
          lineNumber = parseInt(fileNameFuncLineSplit[2].trim(), 10);
        }
        const bodyDelimiter = `${fileNameFuncLine}]`;
        const index = line.indexOf(bodyDelimiter);
        if (index > 0) {
          logBody = line.slice(index + bodyDelimiter.length).trim();
        }

        return {
          timeStamp,
          utc,
          pid,
          tid,
          level,
          tag,
          fileName,
          func,
          lineNumber,
          body: logBody,
        };
      } catch (e) {
        return {
          timeStamp,
          utc,
          pid,
          tid,
          level,
          tag,
          fileName,
          func,
          lineNumber,
          body: line,
        };
      }
    } else {
      return {
        timeStamp,
        utc,
        pid,
        tid,
        level,
        tag,
        fileName,
        func,
        lineNumber,
        body: line,
      };
    }
  }

  private timeStringToTimeStamp(timeString: string): [number, string] {
    // 解析字符串以提取日期和时间
    // 2024-09-13 +8.0 12:02:17.747
    const yyyymmdd = timeString.match(/\d{4}-\d{2}-\d{2}/);
    const utc = timeString.match(/\+\d{1,2}\.\d/);
    const hhmmssms = timeString.match(/\d{2}:\d{2}:\d{2}\.\d{3}/);

    if (yyyymmdd && yyyymmdd.length === 1 && hhmmssms && hhmmssms.length === 1) {
      const yyyymmddString = yyyymmdd[0];
      let yyyy = 0;
      let mm = 0;
      let ss = 0;
      const yyyymmddStringSplit = yyyymmddString.split('-');
      if (yyyymmddStringSplit.length === 3) {
        yyyy = parseInt(yyyymmddStringSplit[0], 10);
        mm = parseInt(yyyymmddStringSplit[1], 10);
        ss = parseInt(yyyymmddStringSplit[2], 10);
      }
      const date = new Date(yyyy, mm - 1, ss);
      const timezoneOffset = date.getTimezoneOffset();
      const hhmmssmsString = hhmmssms[0];
      const hhmmssmsStringSplit = hhmmssmsString.split(':');
      let hour = 0;
      let minute = 0;
      let second = 0;
      let millisecond = 0;
      if (hhmmssmsStringSplit.length === 3) {
        hour = parseInt(hhmmssmsStringSplit[0], 10);
        minute = parseInt(hhmmssmsStringSplit[1], 10);
        const secondMillisecondString = hhmmssmsStringSplit[2];
        const secondMillisecondStringSplit = secondMillisecondString.split('.');
        if (secondMillisecondStringSplit.length === 2) {
          second = parseInt(secondMillisecondStringSplit[0], 10);
          millisecond = parseInt(secondMillisecondStringSplit[1], 10);
        }
      }
      const utcStr = utc ? utc[0] : '+0';
      return [
        date.getTime() +
          (hour - this.getLogTimeZoneOffset(utcStr, timezoneOffset)) * 3600 * 1000 +
          minute * 60 * 1000 +
          second * 1000 +
          millisecond,
        utcStr,
      ];
    } else {
      return [0, '+0'];
    }
  }

  private getLogTimeZoneOffset(alogUtc: string, timezoneOffset: number): number {
    const alogUtcNumber = parseFloat(alogUtc); // 不要用 parseInt，因为还有 +8.5 半个时区的情况
    const timezoneOffsetNumber = timezoneOffset / 60;
    return alogUtcNumber + timezoneOffsetNumber;
  }
}
