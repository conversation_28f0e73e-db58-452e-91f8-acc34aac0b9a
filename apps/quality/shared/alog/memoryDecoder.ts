import { AlogContent, MemoryLogs } from '@pa/shared/dist/src/alog/models';
import { getStringWithRegExp, getEndTime } from '@shared/alog/utils';

const MemoryActionLogTag = 'TrackerAppService';

export default class MemoryDecoder {
  getAllMemoryLogs(alogContent: AlogContent, step: number, crashTime: number): MemoryLogs[] {
    const ret: MemoryLogs[] = [];
    let startTime = alogContent.lines[0].timeStamp;
    let endTime = getEndTime(alogContent);
    if (crashTime !== -1) {
      startTime = crashTime - 30 * 60 * 1000;
      endTime = crashTime;
    }
    for (let i = 0; i < alogContent.lines.length; i++) {
      const line = alogContent.lines[i];
      if (line.tag !== MemoryActionLogTag) {
        continue;
      }
      // 只要 crashTime 前 30 分钟
      if (crashTime !== -1 && line.timeStamp <= endTime && line.timeStamp >= startTime) {
        const appUsed = getStringWithRegExp(line.body, /monitor_memory_usage"\): (.*?)[,\]]/);
        const totalUsed = getStringWithRegExp(line.body, /monitor_total_memory"\): (.*?)[,\]]/);
        if (appUsed !== '' || totalUsed !== '') {
          const appUsedNumber = parseInt(appUsed, 10);
          const totalUsedNumber = parseInt(totalUsed, 10);
          if (!Number.isNaN(appUsedNumber)) {
            ret.push({
              timeStamp: line.timeStamp,
              lineNumber: i,
              appUsed: appUsedNumber,
              totalUsed: totalUsedNumber,
            });
          }
        }
      }
    }

    return this.reducedMemoryLogsData(
      ret.sort((a, b) => a.timeStamp - b.timeStamp),
      step,
      startTime,
      endTime,
    );
  }

  reducedMemoryLogsData(memoryLogs: MemoryLogs[], step: number, startTime: number, endTime: number): MemoryLogs[] {
    // 缩减数据，每step只取一个数据，并且取最大值
    const ret: MemoryLogs[] = [];
    if (memoryLogs.length === 0) {
      return ret;
    }
    const timeWithLogs: { [key: number]: MemoryLogs[] } = {};
    for (const memoryLog of memoryLogs) {
      const second = Math.floor(memoryLog.timeStamp / step) * step;
      if (timeWithLogs[second] === undefined) {
        timeWithLogs[second] = [];
      }
      timeWithLogs[second].push(memoryLog);
    }

    const maxTimeStamp = Math.floor((Math.ceil(endTime / 1000) * 1000) / step) * step;
    const minTimeStamp = Math.floor((Math.floor(startTime / 1000) * 1000) / step) * step;
    const timeArr: number[] = [];
    for (let i = minTimeStamp; i < maxTimeStamp; i += step) {
      timeArr.push(i);
    }
    // 检查哪个时间没数据，补齐
    if (timeWithLogs[timeArr[0]] === undefined) {
      timeWithLogs[timeArr[0]] = [
        {
          timeStamp: timeArr[0],
          lineNumber: 0,
          appUsed: 0,
          totalUsed: 0,
        },
      ];
    }
    for (let i = 1; i < timeArr.length; i++) {
      if (timeWithLogs[timeArr[i]] === undefined) {
        timeWithLogs[timeArr[i]] = timeWithLogs[timeArr[i - 1]];
      }
    }

    Object.entries(timeWithLogs).forEach(([key, value]) => {
      let maxLogs = value[0];
      for (let j = 1; j < value.length; j++) {
        if (value[j].appUsed > maxLogs.appUsed) {
          maxLogs = value[j];
        }
      }
      maxLogs.timeStamp = parseInt(key, 10);
      ret.push(maxLogs);
    });

    return ret.sort((a, b) => a.timeStamp - b.timeStamp);
  }

  getProblemTimes(memoryLogs: MemoryLogs[]): { startTime: number; endTime: number }[] {
    const ret: { startTime: number; endTime: number }[] = [];
    ret.push(...this.getProblemTimes1(memoryLogs));
    ret.push(...this.getProblemTimes2(memoryLogs));
    return ret;
  }

  getProblemTimes1(memoryLogs: MemoryLogs[]): { startTime: number; endTime: number }[] {
    // 找内存突起，当前点的内存水位，对比3秒前的内存水位，是否超过了 100 M
    //         /\
    //  ______/  \_______
    const ret: { startTime: number; endTime: number }[] = [];
    const threshold = 100;
    const lastSecond = 3 * 1000;
    const logsCount = memoryLogs.length;
    for (let i = 0; i < logsCount; i++) {
      const log = memoryLogs[i];
      let targetLog: MemoryLogs | undefined;
      for (let j = i - 1; j >= 0; j--) {
        // 5秒前的内存水位
        if (log.timeStamp - memoryLogs[j].timeStamp >= lastSecond) {
          targetLog = memoryLogs[j];
          break;
        }
      }

      if (targetLog !== undefined) {
        const diff = log.appUsed - targetLog.appUsed;
        if (diff > threshold) {
          // 当前的内存水位对比 3 秒前增加了 100 M
          // 取当前节点前10秒的数据
          ret.push({
            startTime: log.timeStamp - 10 * 1000, // 取10秒，5秒前开始涨了，可能是6秒前操作了什么
            endTime: log.timeStamp,
          });
        }
      }
    }

    return ret;
  }

  getProblemTimes2(memoryLogs: MemoryLogs[]): { startTime: number; endTime: number }[] {
    // 内存阶梯式增长，通过内存突起找不出来
    //            ________
    //         __/
    //  ______/
    const ret: { startTime: number; endTime: number }[] = [];
    const logsCount = memoryLogs.length;
    let startTimeMemoryLogs: MemoryLogs | undefined, endTimeMemoryLogs: MemoryLogs | undefined;
    let inGrowthPeriod = false;
    // 至少连续增长 10 秒，每秒至少增长 5 M，总增长至少 200 M
    const oneStepThreshold = 5;
    const minSecondStep = 10 * 1000;
    const threshold = 200;
    for (let i = 1; i < logsCount; i++) {
      const diff = memoryLogs[i].appUsed - memoryLogs[i - 1].appUsed;
      // 检查是否开始增长
      if (diff > oneStepThreshold && !inGrowthPeriod) {
        startTimeMemoryLogs = memoryLogs[i - 1];
        inGrowthPeriod = true;
      }

      // 如果已经在增长期，检查是否结束增长
      if (inGrowthPeriod) {
        // 结束增长
        if (diff <= 0) {
          endTimeMemoryLogs = memoryLogs[i - 1];
          inGrowthPeriod = false;
        }
      }

      // 如果遍历结束，但endTime未定义，则将endTime设置为数组的最后一个索引
      if (i === logsCount - 1 && inGrowthPeriod && endTimeMemoryLogs === undefined) {
        endTimeMemoryLogs = memoryLogs[logsCount - 1];
      }

      if (startTimeMemoryLogs !== undefined && endTimeMemoryLogs !== undefined) {
        // 总增长的值是否超过阈值
        if (
          endTimeMemoryLogs.appUsed - startTimeMemoryLogs.appUsed > threshold &&
          endTimeMemoryLogs.timeStamp - startTimeMemoryLogs.timeStamp > minSecondStep
        ) {
          ret.push({
            startTime: startTimeMemoryLogs.timeStamp,
            endTime: endTimeMemoryLogs.timeStamp,
          });
        }
        inGrowthPeriod = false;
        startTimeMemoryLogs = undefined;
        endTimeMemoryLogs = undefined;
      }
    }

    return ret;
  }
}
