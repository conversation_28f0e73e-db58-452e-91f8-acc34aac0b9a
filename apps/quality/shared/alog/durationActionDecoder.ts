import { AlogContent, DurationAction, DurationActionFilter, UserActionType } from '@pa/shared/dist/src/alog/models';
import { keyActionGetStringWithRegExp } from '@shared/alog/utils';

export default class DurationActionDecoder {
  getDurationActions(alogContent: AlogContent, tag: string, filters: DurationActionFilter[]): DurationAction[] {
    const ret: DurationAction[] = [];
    for (let i = 0; i < alogContent.lines.length; i++) {
      const line = alogContent.lines[i];
      if (line.tag !== tag) {
        continue;
      }
      for (const filter of filters) {
        const hitResult = line.body.match(filter.beginRegExp);
        if (hitResult && hitResult.length > 0) {
          let title = line.body;
          if (filter.titleRegExp) {
            title = keyActionGetStringWithRegExp(line, filter.titleRegExp);
          }
          let description = '';
          if (filter.descriptionRegExp) {
            description = keyActionGetStringWithRegExp(line, filter.descriptionRegExp);
          }
          ret.push({
            type: UserActionType.Duration,
            timeStamp: line.timeStamp,
            utc: line.utc,
            lineNumber: i,
            title,
            description,
            filter,
          });
          // 一行只能命中一个正则
          break;
        }
      }
    }

    // 找结束时间
    for (const action of ret) {
      for (const line of alogContent.lines) {
        if (line.tag !== tag) {
          continue;
        }
        const hitResult = line.body.match(action.filter.endRegExp);
        if (hitResult && hitResult.length > 0) {
          action.endTimeStamp = line.timeStamp;
        }
      }
    }
    return ret;
  }
}
