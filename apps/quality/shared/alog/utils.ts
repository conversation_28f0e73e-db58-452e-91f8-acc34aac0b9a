import {AlogContent, AlogLine} from "@pa/shared/dist/src/alog/models";

export function getStringWithRegExp(line: string, exp: RegExp): string {
  const matchResult = line.match(exp);
  let result = '';
  if (matchResult && matchResult.length >= 2) {
    result = matchResult[1];
  }
  return result;
}

export function keyActionGetStringWithRegExp(line: AlogLine, exp: RegExp): string {
  const matchResult = line.body.match(exp);
  let description = '';
  if (matchResult && matchResult.length > 0) {
    // 如果是全局捕获
    if (exp.global) {
      for (const matchResultElement of matchResult) {
        if (description === '') {
          description += matchResultElement;
        } else {
          description += ` ${matchResultElement}`;
        }
      }
    } else {
      if (matchResult.length > 1) {
        // 包含捕获组
        for (let j = 1; j < matchResult.length; j++) {
          if (description === '') {
            description += matchResult[j];
          } else {
            description += ` ${matchResult[j]}`;
          }
        }
      } else if (matchResult.length === 1) {
        // 不包含捕获组
        description = matchResult[0];
      }
    }
  }
  return description;
}

export function getEndTime(content: AlogContent): number {
  for (let i = content.lines.length - 1; i >= 0; i--) {
    const line = content.lines[i];
    if (line.timeStamp !== 0) {
      return line.timeStamp;
    }
  }
  return 0;
}