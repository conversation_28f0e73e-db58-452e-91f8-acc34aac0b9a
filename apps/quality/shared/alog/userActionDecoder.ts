import {
  ActionUserAction,
  AlogContent,
  EventUserAction,
  EventUserActionPhase,
  UserAction,
  UserActionLogTag,
  UserActionType,
  VCUserAction,
  VCUserActionType,
} from '@pa/shared/dist/src/alog/models';
import { getStringWithRegExp } from '@shared/alog/utils';

export default class UserActionDecoder {
  getAllUserActions(alogContent: AlogContent): UserAction[] {
    const ret: UserAction[] = [];
    for (let i = 0; i < alogContent.lines.length; i++) {
      const line = alogContent.lines[i];
      if (line.tag !== UserActionLogTag) {
        continue;
      }
      if (line.body.includes('[VC]')) {
        const lineString = `${line.body} `;
        const vcAction: VCUserAction = {
          type: UserActionType.VC,
          timeStamp: line.timeStamp,
          utc: line.utc,
          lineNumber: i,
          vcType: getStringWithRegExp(lineString, /action=(\S+)/) as VCUserActionType,
          name: getStringWithRegExp(lineString, /class=(\S+)/),
          rootVC: getStringWithRegExp(lineString, /rootVC=(\S+)/),
          topVC: getStringWithRegExp(lineString, /topVC=(\S+)/),
          // topVC: this.getStringWithRegExp(lineString, /topVC=(.*?)\s/)
        };
        if (vcAction.name === 'LVNavigationController') {
          continue;
        }
        if (vcAction.vcType === VCUserActionType.viewDidDisappear) {
          for (let j = ret.length - 1; j >= 0; j--) {
            const action = ret[j];
            if (action.type === UserActionType.VC) {
              const preVCAction = action as VCUserAction;
              if (preVCAction.vcType === VCUserActionType.viewDidAppear && preVCAction.name === vcAction.name) {
                preVCAction.endTimeStamp = line.timeStamp;
                break;
              }
            }
          }
        } else {
          ret.push(vcAction);
        }
      } else if (line.body.includes('[UIApp][Click]')) {
        const lineString = `${line.body} `;
        let actionAction: ActionUserAction;
        if (lineString.includes('possibleAction=点击')) {
          actionAction = {
            type: UserActionType.Action,
            timeStamp: line.timeStamp,
            utc: line.utc,
            lineNumber: i,
            actionSender: getStringWithRegExp(lineString, /view=(\S+)/),
            target: '',
            sel: '',
          };
        } else {
          actionAction = {
            type: UserActionType.Action,
            timeStamp: line.timeStamp,
            utc: line.utc,
            lineNumber: i,
            actionSender: getStringWithRegExp(lineString, /actionSender=(\S+)/),
            target: this.getActionTarget(lineString),
            sel: getStringWithRegExp(lineString, /sel=(\S+)/),
          };
        }
        ret.push(actionAction);
      } else if (line.body.includes('[UIApp]')) {
        const lineString = `${line.body} `;
        const eventAction: EventUserAction = {
          type: UserActionType.Event,
          timeStamp: line.timeStamp,
          utc: line.utc,
          lineNumber: i,
          view: getStringWithRegExp(lineString, /view=(\S+)/),
          cell: getStringWithRegExp(lineString, /cell=(\S+)/),
          touchIdx: getStringWithRegExp(lineString, /touchIdx=(\S+)/),
          phase: getStringWithRegExp(lineString, /phase=(\S+)/) as EventUserActionPhase,
          gestures: getStringWithRegExp(lineString, /phase=(\S+)/),
          possibleAction: getStringWithRegExp(lineString, /phase=(\S+)/),
        };
        if (eventAction.phase === EventUserActionPhase.ended || eventAction.phase === EventUserActionPhase.cancelled) {
          for (let j = ret.length - 1; j >= 0; j--) {
            const action = ret[j];
            if (action.type === UserActionType.Event) {
              const preEventAction = action as EventUserAction;
              if (preEventAction.phase === EventUserActionPhase.began) {
                preEventAction.endTimeStamp = line.timeStamp;
                break;
              }
            }
          }
        } else {
          ret.push(eventAction);
        }
      }
    }

    // 如果滑动的时候没有end，大概率是因为点击了collectionView 中的某个元素，这时候会生成一个滑动alog和一个点击alog，需要删掉滑动alog
    for (let i = ret.length - 1; i >= 0; i--) {
      const action = ret[i];
      if (action.type === UserActionType.Event) {
        const eventAction = action as EventUserAction;
        if (eventAction.endTimeStamp === undefined) {
          ret.splice(i, 1);
        }
      }
    }
    return ret;
  }

  getActionTarget(lineString: string) {
    const target = getStringWithRegExp(lineString, /target=(\S+)/);
    if (target !== undefined && target !== null && target !== '') {
      return target;
    }

    const cell = getStringWithRegExp(lineString, /cell=(\S+)/);
    if (cell !== undefined && cell !== null && cell !== '') {
      return cell;
    }

    const view = getStringWithRegExp(lineString, /view=(\S+)/);
    if (view !== undefined && view !== null && view !== '') {
      return view;
    }

    return '';
  }
}
