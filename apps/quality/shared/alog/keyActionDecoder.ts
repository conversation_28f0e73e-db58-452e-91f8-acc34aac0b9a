import { AlogContent, KeyAction, KeyActionFilter, UserActionType } from '@pa/shared/dist/src/alog/models';
import { keyActionGetStringWithRegExp } from '@shared/alog/utils';

export default class KeyActionDecoder {
  getKeyActions(alogContent: AlogContent, tag: string, filters: KeyActionFilter[]): KeyAction[] {
    const ret: KeyAction[] = [];
    for (let i = 0; i < alogContent.lines.length; i++) {
      const line = alogContent.lines[i];
      if (line.tag !== tag) {
        continue;
      }
      for (const filter of filters) {
        const hitResult = line.body.match(filter.hitRegExp);
        if (hitResult && hitResult.length > 0) {
          let title = line.body;
          if (filter.titleRegExp) {
            title = keyActionGetStringWithRegExp(line, filter.titleRegExp);
          }
          let description = '';
          if (filter.descriptionRegExp) {
            description = keyActionGetStringWithRegExp(line, filter.descriptionRegExp);
          }
          ret.push({
            type: UserActionType.Key,
            timeStamp: line.timeStamp,
            utc: line.utc,
            lineNumber: i,
            title,
            description,
            filter,
          });
          // 一行只能命中一个正则
          break;
        }
      }
    }

    return ret;
  }
}
