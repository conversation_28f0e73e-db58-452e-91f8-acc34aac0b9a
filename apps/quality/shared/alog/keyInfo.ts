import { Alog, UserAction, UserActionType } from '@pa/shared/dist/src/alog/models';
import UserActionDecoder from '@shared/alog/userActionDecoder';
import MemoryDecoder from '@shared/alog/memoryDecoder';
import { getKeyInfoFromUserActions } from '@pa/shared/dist/src/alog/alogUtils';

export function isUserActionBetweenTime(
  userAction: UserAction,
  beginTimeStamp: number, // 毫秒级
  endTimeStamp: number, // 毫秒级
  region: string,
): boolean {
  let crashTimeUtc = 0;
  if (region === 'cn') {
    // 客户端上报的 crash time 中，cn 是 +8 时区，cc 的都是 +0 时区
    crashTimeUtc = 8;
  }

  const alogUtc = Number(userAction.utc);
  // 日志里的时区有可能是 +0 +1 +3 +8
  const diffUtc = crashTimeUtc - alogUtc;
  const fixActionTimeStamp = userAction.timeStamp + diffUtc * 3600 * 1000;
  return fixActionTimeStamp >= beginTimeStamp && fixActionTimeStamp <= endTimeStamp;
}

export function getSuitableUserActions(alog: Alog | undefined, crashTime: number, crashType: string, region: string) {
  if (alog === undefined) {
    return [];
  }
  const userActionDecoder = new UserActionDecoder();
  const memoryDecoder = new MemoryDecoder();
  const suitableUserActions: UserAction[] = [];
  // 寻找合适的 userAction
  for (const content of alog.contents) {
    // 此 content 是否在 crash time 内
    if (
      content.lines.length > 1 &&
      content.lines[0].timeStamp <= crashTime * 1000 &&
      content.lines[content.lines.length - 1].timeStamp >= crashTime * 1000
    ) {
      const userActions = userActionDecoder.getAllUserActions(content);
      if (userActions.length <= 0) {
        continue;
      }
      let findAction = false;
      if (
        crashType === 'oom_crash' ||
        crashType === 'new_oom_crash' ||
        crashType === 'matrix' ||
        crashType === 'matrix_memorygraph'
      ) {
        // 寻找内存增长点
        const memoryLogs = memoryDecoder.getAllMemoryLogs(content, 1000, crashTime * 1000);
        const problemTimes = memoryDecoder.getProblemTimes(memoryLogs);
        for (const userAction of userActions) {
          for (const times of problemTimes) {
            if (isUserActionBetweenTime(userAction, times.startTime, times.endTime, region)) {
              suitableUserActions.push(userAction);
              findAction = true;
            }
          }
        }
      } else if (
        crashType === 'crash' ||
        crashType === 'other_system_kill_mach_port_exhausted' ||
        crashType === 'other_system_kill_vnode_exhausted' ||
        crashType === 'matrix_custom' ||
        crashType === 'ios_metricket_crash' ||
        crashType === 'ios_metricket_cpu' ||
        crashType === 'ios_metricket_disk'
      ) {
        const last3TimeStamp = crashTime - 3; // 秒级
        for (const userAction of userActions) {
          if (isUserActionBetweenTime(userAction, last3TimeStamp * 1000, (crashTime + 1) * 1000, region)) {
            // crashTime + 1 是因为要把 crash 的整秒都包含进来
            suitableUserActions.push(userAction);
            findAction = true;
          }
        }
      } else if (
        crashType === 'watch_dog' ||
        crashType === 'other_system_kill' ||
        crashType === 'other_system_kill_maybe_watchdog' ||
        crashType === 'smooth_v2' ||
        crashType === 'ios_metricket_hang' ||
        crashType === 'lag_drop_frame' ||
        crashType === 'ui_frozen'
      ) {
        const last30TimeStamp = crashTime - 30; // 秒级
        for (const userAction of userActions) {
          if (isUserActionBetweenTime(userAction, last30TimeStamp * 1000, (crashTime + 1) * 1000, region)) {
            // crashTime + 1 是因为要把 crash 的整秒都包含进来
            suitableUserActions.push(userAction);
            findAction = true;
          }
        }
      }
      if (!findAction) {
        // 没有找到行为，获取最后一个点击事件
        for (let i = userActions.length - 1; i >= 0; i--) {
          const userAction = userActions[i];
          if (userAction.type === UserActionType.Action) {
            suitableUserActions.push(userAction);
            findAction = true;
            break;
          }
        }
      }
    }
  }

  return suitableUserActions;
}

export function getKeyInfo(
  alog: Alog | undefined,
  crashTime: number,
  crashType: string,
  region: string,
): [string[], string[], UserAction[]] {
  if (alog === undefined) {
    return [[], [], []];
  }
  const suitableUserActions = getSuitableUserActions(alog, crashTime, crashType, region);

  // const keyClass: string[] = [];
  // const keyMethod: string[] = [];
  //
  // keyMethod.push(`${suitableUserActions.length}`);
  //
  // for (let i = 0; i < alog.contents.length; i++) {
  //   const content = alog.contents[i];
  //   if (
  //     content.lines.length > 1 &&
  //     content.lines[0].timeStamp <= crashTime * 1000 &&
  //     content.lines[content.lines.length - 1].timeStamp >= crashTime * 1000
  //   ) {
  //     const userActions = userActionDecoder.getAllUserActions(content);
  //     keyClass.push(`${i}-${userActions.length}`);
  //     keyClass.push(
  //       `${i}-${content.lines.length}-${content.lines[0].timeStamp}-${crashTime * 1000}-${content.lines[content.lines.length - 1].timeStamp}`,
  //     );
  //   } else {
  //     keyClass.push(
  //       `${i}-${content.lines.length}-${content.lines[0].timeStamp}-${crashTime * 1000}-${content.lines[content.lines.length - 1].timeStamp}`,
  //     );
  //   }
  // }
  //
  // return [keyClass, keyMethod];

  return [...getKeyInfoFromUserActions(suitableUserActions), suitableUserActions];
}
