// 字体加粗
export function bold(text: string): string {
  return `**${text}**`;
}

export function font(text: string, color: string): string {
  return `<font color=${color}>${text}</font>`;
}
// 这里重复调用接口可能有问题,概率不大,先简单实现
export class MarkDown {
  private text: string;
  constructor(text: string) {
    this.text = text;
  }

  url(url: string): MarkDown {
    this.text = `[${this.text}](${url})`;
    return this;
  }

  bold(): MarkDown {
    this.text = bold(this.text);
    return this;
  }
  redFont(): MarkDown {
    this.text = font(this.text, 'red');
    return this;
  }
  greenFont(): MarkDown {
    this.text = font(this.text, 'green');
    return this;
  }
  toString(): string {
    return this.text;
  }
}

export function markDown(text: string): MarkDown {
  return new MarkDown(text);
}
