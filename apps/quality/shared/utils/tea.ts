import { TeaIdType } from '@shared/typings/tea/metric';

export const TEA_URL_REGEX =
  /^https:\/\/(tea-va|data)\.bytedance\.net\/(tea|tea-next)\/(app|project)\/(\d+)\/event-analysis(\/result)?\/([a-z0-9]+)/;

export const buildTeaUrl = (
  is_new_tea: boolean,
  tea_app_id: number | string,
  tea_id: string,
  tea_id_type: TeaIdType,
) => {
  const url_type = is_new_tea ? 'tea-next/project' : 'tea/app';
  const tea_id_type_str = tea_id_type !== TeaIdType.REPORT_ID ? '/result' : '';
  const domain_prefix = ['509', '145611', '719619'].includes(tea_app_id.toString()) ? 'data' : 'tea-va';
  return `https://${domain_prefix}.bytedance.net/${url_type}/${tea_app_id}/event-analysis${tea_id_type_str}/${tea_id}`;
};
