import { SlardarPlatformType } from '@pa/shared/dist/src/appSettings/appSettings';
import { clone, compact, curry, eq, flow, negate, range, sample } from 'lodash';
import { Platform } from '@shared/typings/tea/metric';
import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import FileSaver from 'file-saver';
import { PlatformType } from '@pa/shared/dist/src/core';

export const wait = async (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

export const extractor = <T>(...it: T[]) => it;

export const stripDbField = <T>(it: T): Omit<T, '__v' | '_id' | '$__'> => {
  for (const prop in it) {
    if (['__v', '_id', '$__'].includes(prop)) {
      delete it[prop];
    } else if (typeof it[prop] === 'object') {
      console.log(prop);
      stripDbField(it[prop]);
    }
  }
  return it;
};

export const avg = (n: number[]): number => (n.length <= 0 ? 0 : n.reduce((a, b) => a + b) / Math.max(1, n.length));

export const floor_to = (n: number, ft: number): number => Math.floor(n / ft) * ft;

export const ceil_to = (n: number, ft: number): number => Math.ceil(n / ft) * ft;

export const concat_string = (s: string, t: string) => s + t;

export const add_prefix = curry(concat_string);

export const add_suffix = curry((suffix: string, s: string) => s + suffix);

export const has_prefix = curry((prefix: string, s: string) => s.startsWith(prefix));

export const has_suffix = curry((suffix: string, s: string) => s.endsWith(suffix));

export const trim_prefix = curry((prefix: string, s: string) => (s.startsWith(prefix) ? s.slice(prefix.length) : s));

export const trim_suffix = curry((suffix: string, s: string) =>
  s.endsWith(suffix) ? s.slice(0, s.length - suffix.length) : s,
);

export const add_prefix_ne = (prefix: string) => flow(trim_prefix(prefix), add_prefix(prefix));

export const add_suffix_ne = (suffix: string) => flow(trim_suffix(suffix), add_suffix(suffix));

export const apply_if =
  <T, R, D = undefined>(f: (a: T) => R, d?: D) =>
  (a: T | undefined | null): R | D | undefined =>
    a === undefined || a === null ? d : f(a);

export const async_value = async <T>(v: T) => v;

export const array_first_or_else = <T, E>(obj: T | T[], or_else?: E): T | E | undefined =>
  !obj ? undefined : Array.isArray(obj) ? (obj.length > 0 ? obj[0] : or_else) : (obj as T);

export type CommonOf<T1, T2> = {
  [K in {
    [k in keyof T1 & keyof T2]: T1[k] extends T2[k] | undefined ? (T2[k] extends T1[k] | undefined ? k : never) : never;
  }[keyof T1 & keyof T2]]: T1[K] | T2[K];
};

export const platform_eq = (
  s: SlardarPlatformType | PlatformType | Platform,
  t: SlardarPlatformType | PlatformType | Platform,
) => {
  const ss = typeof s === 'number' ? ((s as Platform) === Platform.Android ? 'Android' : 'iOS') : s;
  const ts = typeof t === 'number' ? ((t as Platform) === Platform.Android ? 'Android' : 'iOS') : t;
  return ss.toString() === ts.toString();
};

export const platformV2_eq = (s: Platform, t: SlardarPlatformType) => {
  switch (s) {
    case Platform.Android:
      return t === SlardarPlatformType.Android;
    case Platform.iOS:
      return t === SlardarPlatformType.iOS;
    default:
      return false;
  }
};

export const to_slardar_platform = (s: SlardarPlatformType | PlatformType | Platform) =>
  platform_eq(s, SlardarPlatformType.iOS) ? SlardarPlatformType.iOS : SlardarPlatformType.Android;

export const to_platform = (s: SlardarPlatformType | PlatformType | Platform) =>
  platform_eq(s, Platform.iOS) ? Platform.iOS : Platform.Android;

export type Empty = Record<string, never>;

// eslint-disable-next-line @typescript-eslint/no-empty-function
export const empty_promise = async () => {};

/**
 * 我怀着悲愤的心情写下这段东西
 */
export const excel_time_to_unix = (excel_time: number) => {
  const excel_date = Math.floor(excel_time);
  /**
   * 为什么 -2？
   * 因为
   * 1. 1900-01-01 是 1，有一天的偏移
   * 2. 1900-02-29 被认为是存在的，详见 https://answers.microsoft.com/en-us/msoffice/forum/all/bug-1900-feb-29th-is-a-valid-date-for-ms-excel/682400e0-708e-4dd7-aa22-d72bcbd16443
   */
  const real_time = dayjs('1900-01-01 00:00:00')
    .add(excel_date - 2, 'd')
    .add((excel_time - excel_date) * 86400, 's');
  return real_time.unix();
};

export const get_enum_value = <E extends Record<string, string | number>>(e: E, v: string | E[keyof E]): E[keyof E] => {
  const ek = e[v as keyof typeof e];
  return e[ek as keyof typeof e];
};

export const get_enum_name = <E extends Record<string, string | number>>(enumType: E, value: E[keyof E]): keyof E => {
  const enumEntries = Object.entries(enumType) as [keyof E, E[keyof E]][];
  const entry = enumEntries.find(([, enumValue]) => enumValue === value);
  return entry![0];
};

export const trim_query_from_url = (url: string): string => url.split('?')[0];

export const put_back_by = <T>(arr: T[], pred: (obj: T) => boolean): T[] =>
  arr.filter(negate(pred)).concat(arr.filter(pred));

export const eq_with = curry(eq);

// only fast for short strings!
export const count_char = (str: string, char: string): number => str.split('').filter(eq_with(char)).length;

export const invoke_and_return =
  <T>(func: (params: T) => void | Promise<void> | any): ((params: T) => T) =>
  (params: T) => {
    func(params);
    return params;
  };

export const dup_with = <T>(original: T[], target: number, transform: (a?: T) => T | undefined = it => it): T[] =>
  original.concat(
    compact(range(0, Math.max(target - original.length, 0)).map(() => transform(clone(sample(original))))),
  );

export const is_numeric = (s: string): boolean => !isNaN(parseFloat(s)) && isFinite(Number(s));

export type ConcatStr<T extends string, U extends string> = `${T}${U}`;

type PickStrings<T> = T extends string ? T : never;

export const generate_sorter = <T>(
  ...fields: (keyof T | ConcatStr<'#' | '~' | '#~' | '~#', PickStrings<keyof T>>)[]
): ((a: T, b: T) => number) => {
  const safeCompare = (x: any, y: any): number => {
    // Handle null and undefined
    if (x === null && y === null) {
      return 0;
    }
    if (x === null) {
      return -1;
    }
    if (y === null) {
      return 1;
    }

    // Handle string comparison using locale comparison
    if (typeof x === 'string' && typeof y === 'string') {
      if (is_numeric(x) && is_numeric(y)) {
        return Number(x) - Number(y);
      }
      return x.localeCompare(y);
    }

    // Handle numeric and boolean comparison, as well as any other primitive types
    return x < y ? -1 : x > y ? 1 : 0;
  };

  return (a: T, b: T) => {
    for (const field of fields) {
      const is_neg = field.toString().includes('~');
      const is_parse_int = field.toString().includes('#');
      const k = (is_neg || is_parse_int ? field.toString().slice(1) : field) as keyof T;

      // Use the safeCompare function for comparison
      const result = safeCompare(
        is_parse_int ? parseInt(a[k] as string, 10) : a[k],
        is_parse_int ? parseInt(b[k] as string, 10) : b[k],
      );
      if (result !== 0) {
        return is_neg ? -result : result;
      }
    }
    return 0; // All fields are equal
  };
};

export type KeysMatching<T, U> = {
  [K in keyof T]: T[K] extends U ? K : never;
}[keyof T];

export type PickByType<T, U> = Pick<T, KeysMatching<T, U>>;

export const get_day_start = (timestamp: number, utcOffset: number) => {
  dayjs.extend(utc);
  return dayjs.unix(timestamp).utcOffset(utcOffset).startOf('d').unix();
};

export const is_day_start = (timestamp: number, utcOffset: number) => get_day_start(timestamp, utcOffset) === timestamp;

export type FixedLengthArray<T, L extends number, ACC extends T[] = []> = ACC['length'] extends L
  ? ACC
  : FixedLengthArray<T, L, [T, ...ACC]>;

export const to_exp = (n: number): [number, number] => {
  const re = Math.floor(Math.log(n) / Math.log(10));
  const rn = n / Math.pow(10, re);
  return [rn, re];
};

export const get_axis_range = (n: number[]): [number, number] => {
  const max_val = Math.max(...n);
  const min_val = Math.min(...n);
  const exp = Math.pow(10, to_exp(max_val - min_val)[1]);
  const max_lim = Math.ceil(max_val / exp) * exp;
  const min_lim = Math.floor(min_val / exp) * exp;
  return [max_lim, min_lim];
};

export const get_enum_values = <E extends Record<string, string | number>>(enumType: E): E[keyof E][] =>
  Object.keys(enumType)
    .filter(key => isNaN(Number(key))) // Exclude reverse mappings for numeric enums
    .map(key => enumType[key as keyof typeof enumType]);

export const previous_monday = (t: number, offset: number) => {
  const d = dayjs.unix(t).utcOffset(offset);
  for (let i = 0; i < 10; i++) {
    if (d.subtract(i, 'd').day() === 1) {
      return d.subtract(i, 'd').startOf('d').unix();
    }
  }
  return 0;
};

export const save_as_csv = (content: string, filename: string) => {
  const BOM = new Uint8Array([0xef, 0xbb, 0xbf]);
  const blob = new Blob([BOM, content], { type: 'text/csv;charset=utf-8' });
  FileSaver.saveAs(blob, filename);
};

export const cartesian_product = <T, U>(a: T[], b: U[]): [T, U][] => a.flatMap(av => b.map(bv => [av, bv] as [T, U]));

export type ElementOf<T extends any[]> = T[number];

export function filterNotEmptyStrings(arr: (string | undefined)[]): string[] {
  return arr.filter((item): item is string => item !== undefined && item !== '');
}
