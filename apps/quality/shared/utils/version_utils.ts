import { count_char } from '@shared/utils/tools';
import { Dreamina_SLARDAR_APP_ID } from '../../api/service/slardar/shared';

export enum VersionType {
  GRAY = 1,
  ONLINE,
  SINGLE_GRAY,

  UNKNOWN = 10001,
  TF_GRAY,
  SMALL_TF,
  SMALL,
  FULL_TF,
  FULL,

  MAJOR_VERSION = 20001,
}

export const VersionTypeName: Readonly<Record<VersionType, string>> = {
  [VersionType.GRAY]: '灰度',
  [VersionType.ONLINE]: '正式',
  [VersionType.SINGLE_GRAY]: '独立灰度',
  [VersionType.UNKNOWN]: '未知',
  [VersionType.TF_GRAY]: 'TF 灰度',
  [VersionType.SMALL_TF]: '小流量 TF',
  [VersionType.SMALL]: '小流量',
  [VersionType.FULL_TF]: '全量 TF',
  [VersionType.FULL]: '正式',
  [VersionType.MAJOR_VERSION]: '大版本',
};

export function IosTypeOf(version_code: string, isCC: boolean): VersionType {
  const versions = version_code.split('.');
  const majorVersion = parseInt(versions[0], 10);
  const revisionVersion = parseInt(versions[2], 10);
  const buildVersion = parseInt(versions[versions.length - 1], 10);
  if (majorVersion >= 14 || (isCC && majorVersion >= 12)) {
    // 1400 版本以上规则
    if (buildVersion >= 80 && buildVersion <= 89 && revisionVersion === 0) {
      return VersionType.TF_GRAY;
    } else if (buildVersion >= 90 && buildVersion <= 94 && revisionVersion === 0) {
      return VersionType.SMALL_TF;
    } else if (buildVersion >= 95 && buildVersion <= 99 && revisionVersion === 0) {
      return VersionType.SMALL;
    } else if (buildVersion >= 10 && buildVersion <= 19 && revisionVersion === 1) {
      return VersionType.FULL_TF;
    } else if (buildVersion >= 95 && buildVersion <= 99 && revisionVersion === 1) {
      return VersionType.FULL;
    } else if (buildVersion >= 95 && buildVersion <= 99 && revisionVersion === 2) {
      return VersionType.FULL;
    } else if (buildVersion >= 1 && buildVersion <= 20 && revisionVersion === 5) {
      return VersionType.SINGLE_GRAY;
    } else {
      return VersionType.UNKNOWN;
    }
  } else {
    switch (Math.floor(buildVersion / 10)) {
      case 3: // 30-39
        return VersionType.TF_GRAY;
      case 5: // 50-59
        return VersionType.SMALL_TF;
      case 6: // 60-69
        return VersionType.SMALL;
      case 1: // 10-19
        return VersionType.FULL_TF;
      case 2:
        return VersionType.FULL;
      default:
        return VersionType.UNKNOWN;
    }
  }
}

export function DreaminaIosTypeOf(version_code: string): VersionType {
  const versions = version_code.split('.');
  const buildVersion = parseInt(versions[versions.length - 1], 10);
  if (buildVersion >= 80 && buildVersion <= 89) {
    return VersionType.TF_GRAY;
  } else if (buildVersion >= 90 && buildVersion <= 99) {
    return VersionType.FULL;
  } else {
    return VersionType.UNKNOWN;
  }
}

/**
 * 仅适用于剪映
 * @param version_code
 * @constructor
 */
export function AndroidTypeOf(version_code: string): VersionType {
  const comp = Number(version_code);
  if (comp % 10000 >= 1600 && comp % 10000 < 1610) {
    return VersionType.ONLINE;
  } else if (comp % 100 !== 0 || comp % 10000 >= 1610) {
    return VersionType.SINGLE_GRAY;
  }
  return VersionType.GRAY;
}

export function DreaminaAndroidTypeOf(version_code: string): VersionType {
  const comp = Number(version_code);
  if (comp % 10000 >= 1600) {
    return VersionType.ONLINE;
  } else if (comp % 100 !== 0) {
    return VersionType.SINGLE_GRAY;
  }
  return VersionType.GRAY;
}

/**
 * 获得上一个大版本（12.0.0 -> 11.9.0）
 * @param current_version
 */
export function get_previous_version(current_version: string): string {
  const part = current_version.split('.').map(it => parseInt(it, 10));
  part[1] = part[1] <= 0 ? 9 : part[1] - 1;
  part[0] -= part[1] === 9 ? 1 : 0;
  return part.join('.');
}

export function get_previous_version_dreamina(current_version: string): string {
  const part = current_version.split('.').map(it => parseInt(it, 10));
  part[2] = part[2] <= 0 ? 9 : part[2] - 1;
  part[1] -= part[2] === 9 ? 1 : 0;
  return part.join('.');
}

/**
 * 版本名 -> 最小版本号 for Android
 * old: 1190 0100
 * new: 1190 [0] 1600
 * 剪映版本号多一个 0，CC 版本号正常
 * @param versionName
 * @param isCN 是否为中国区（剪映）
 */
export function versionName2Code(versionName: string, isCN: boolean): number {
  // 11.9.0 -> [11][9][0]0000
  // 12.0.0 -> [12][0][0]<{0}>0000
  return versionName
    .split('.')
    .map(it => parseInt(it, 10))
    .reduce((sum, cur, cid, arr) => sum + cur * Math.pow(10, 6 - cid + (arr[0] >= 12 && isCN ? 1 : 0)), 0);
}

/**
 * 截去版本号中间多余的 0（如有）
 * 1200<0>0300 -> 12000300
 * @param version_code
 */
export function correctVersionCode(version_code: string): string {
  if (version_code.length < 9 || version_code.includes('.')) {
    return version_code;
  }
  return version_code.slice(0, -5) + version_code.slice(-4);
}

/**
 * 版本号 -> 版本名
 * @param input
 * @param ignorePatch 忽略小版本号
 */
export function versionCodeToVersion(input: string, ignorePatch = false): string {
  if (input.includes('.')) {
    // iOS
    return input.slice(0, input.lastIndexOf('.'));
  }
  // Android
  const versionCode = Number(correctVersionCode(input));
  if (versionCode < 1000000) {
    // 醒图是5~6位的小版本号,ex:100004
    const major = Math.floor(versionCode / 10000);
    const minor = Math.floor((versionCode / 1000) % 10);
    const patch = Math.floor((versionCode / 100) % 10);
    return ignorePatch ? `${major}.${minor}.0` : `${major}.${minor}.${patch}`;
  } else {
    // 剪映版本号
    const major = Math.floor(versionCode / 1000000);
    const minor = Math.floor((versionCode / 100000) % 10);
    const patch = Math.floor((versionCode / 10000) % 10);
    return ignorePatch ? `${major}.${minor}.0` : `${major}.${minor}.${patch}`;
  }
}

export function CompareVersionCode(left: string, right: string): number {
  if (left.includes('.')) {
    const leftParts = left.split('.');
    const rightParts = right.split('.');
    for (let i = 0; i < Math.max(leftParts.length, rightParts.length); i++) {
      const leftNum = i < leftParts.length ? parseInt(leftParts[i]) : 0;
      const rightNum = i < rightParts.length ? parseInt(rightParts[i]) : 0;

      if (leftNum < rightNum) {
        return -1;
      } else if (leftNum > rightNum) {
        return 1;
      }
    }
    return 0;
  } else {
    return parseInt(left) - parseInt(right);
  }
}

/**
 * 版本号 -> 版本名，但第 3 节恒为 0
 * @param input
 * @param ignorePatch
 */
export function versionCodeToMeegoVersion(input: string, ignorePatch = true): string {
  console.log(input);
  const ret = versionCodeToVersion(input);
  return ignorePatch ? `${ret.slice(0, ret.lastIndexOf('.'))}.0` : ret;
}

export function isMajorVersion(version: string): boolean {
  return count_char(version, '.') === 2;
}

export function isMinorVersion(version: string): boolean {
  return !isMajorVersion(version);
}

interface Version {
  major: number;
  minor: number;
  patch: number;
  original: string;
}

export function filterHighestVersions(versions: string[]): string[] {
  const parsedVersions: Version[] = versions.map(versionStr => {
    const parts = versionStr.split('.').map(Number);
    return { major: parts[0], minor: parts[1], patch: parts[2], original: versionStr };
  });

  const highestVersionMap: Map<string, Version> = new Map();

  // Fill the map with the highest patch version for each major.minor combination
  parsedVersions.forEach(version => {
    const key = `${version.major}.${version.minor}`;
    const currentHighestVersion = highestVersionMap.get(key);
    if (!currentHighestVersion || currentHighestVersion.patch < version.patch) {
      highestVersionMap.set(key, version);
    }
  });

  // Get the string representations of the highest versions
  return Array.from(highestVersionMap.values()).map(v => v.original);
}

/**
 * 11.0.0->9.0.0
 * @param lvVersion
 */
export function lv2ccVersion(lvVersion: string): string {
  const num = Number(lvVersion.substring(0, lvVersion.length - 4)) - 2;
  return num.toString().concat(lvVersion.substring(lvVersion.length - 4, lvVersion.length));
}

/**
 * 9.0.0->11.0.0
 * @param ccVersion
 */
export function cc2lvVersion(ccVersion: string): string {
  const num = Number(ccVersion.substring(0, ccVersion.length - 4)) + 2;
  return num.toString().concat(ccVersion.substring(ccVersion.length - 4, ccVersion.length));
}
