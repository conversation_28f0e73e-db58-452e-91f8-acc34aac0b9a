import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import { utcOffset } from '../../api/utils/region';

export function get_last_week_timestamp_range(current_timestamp: number): [number, number] {
  dayjs.extend(utc);
  const prev_mon = dayjs.unix(current_timestamp).utcOffset(utcOffset()).day(-6);
  const prev_sun = prev_mon.add(6, 'd').endOf('d');
  return [prev_mon.unix(), prev_sun.unix()];
}

export function get_last_month_timestamp_range(current_timestamp: number): [number, number] {
  dayjs.extend(utc);
  const month_start = dayjs.unix(current_timestamp).utcOffset(utcOffset()).subtract(1, 'month').startOf('month');
  const month_end = month_start.endOf('month');
  return [month_start.unix(), month_end.unix()];
}

export function get_last_day_timestamp_range(cur: number): [number, number] {
  dayjs.extend(utc);
  const d_s = dayjs.unix(cur).utcOffset(utcOffset()).subtract(1, 'd').startOf('d');
  const d_e = d_s.endOf('d');
  return [d_s.unix(), d_e.unix()];
}

export function get_latest_day_timestamp_range(cur: number, interval: number): [number, number] {
  dayjs.extend(utc);
  const r_s = dayjs.unix(cur).utcOffset(utcOffset()).subtract(interval, 'd').startOf('d');
  const r_e = dayjs.unix(cur).utcOffset(utcOffset()).subtract(1, 'd').endOf('d');
  return [r_s.unix(), r_e.unix()];
}

export function floor_to_nearest_minutes(cur: number, n = 5) {
  dayjs.extend(utc);
  const c = dayjs.unix(cur).utcOffset(utcOffset()).second(0);
  const tm = Math.floor(c.minute() / n) * n;
  return c.minute(tm).unix();
}

export function floor_to_nearest_hours(cur: number, n = 1) {
  dayjs.extend(utc);
  const c = dayjs.unix(cur).utcOffset(utcOffset()).startOf('h');
  const th = Math.floor(c.hour() / n) * n;
  return c.hour(th).unix();
}

export function floor_to_nearest_days(cur: number, n = 1) {
  dayjs.extend(utc);
  const c = dayjs.unix(cur).utcOffset(utcOffset()).startOf('d');
  const td = Math.floor(c.date() / n) * n;
  return c.date(td).unix();
}

function minutes_to_hours(minutes: number) {
  const hours = minutes / 60;
  return Math.round(hours);
}

function minutes_to_days(minutes: number) {
  const days = minutes / 60 / 24;
  return Math.round(days);
}

// n单位为min
export function floor_to_nearest(timestamp: number, n = 5) {
  if (n < 60) {
    return floor_to_nearest_minutes(timestamp, n);
  } else if (60 <= n && n < 24 * 60) {
    return floor_to_nearest_hours(timestamp, minutes_to_hours(n));
  } else {
    return floor_to_nearest_days(timestamp, minutes_to_days(n));
  }
}
