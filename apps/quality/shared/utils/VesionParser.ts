import { PlatformType } from '@pa/shared/dist/src/core';
import { cloneDeep } from 'lodash';

type VersionPart = {
  name: string;
  length: number;
};

class VersionTemplate {
  private versionPart: VersionPart[];
  constructor(versionPart: VersionPart[]) {
    this.versionPart = versionPart;
  }

  static build(versionPart: VersionPart[]) {
    return new VersionTemplate(versionPart);
  }

  // 分割长整数格式的版本号，例如 1340100
  splitNumericVersion(version: string): number[] {
    let index = 0;
    return this.versionPart.map(part => {
      const partValue = version.substring(index, index + part.length);
      index += part.length;
      return parseInt(partValue, 10);
    });
  }

  parseVersion(version: string): number[] {
    // 检查是否是数字类型
    const isNumeric = /^\d+$/.test(version);

    if (isNumeric) {
      // 如果是纯数字版本号，例如 1340100，按逻辑分割
      return this.splitNumericVersion(version);
    } else {
      // 否则按照 . 分割，例如 13.4.0.1
      return version.split('.').map(num => parseInt(num, 10));
    }
  }

  at(index: number) {
    return this.versionPart?.at(index);
  }

  size() {
    return this.versionPart?.length ?? 0;
  }
}

type AppId = number;

class VersionInfoConfig {
  static appId: number;
  static platformType: PlatformType;
  versionCodeTemplate: VersionTemplate;
  versionNameTemplate: VersionTemplate;
  static match(appID: AppId, platformType: PlatformType): boolean {
    return this.appId === appID && this.platformType === platformType;
  }
}

/** 定义版本号模板, 依据剪映和目前是{@link https://bytedance.larkoffice.com/wiki/FTc4wuln1iymE0kchq5cXcUNnrD}
 */
class VersionInfoForJianyingAndroid extends VersionInfoConfig {
  static appId = 177502;
  static platformType = PlatformType.Android;
  versionCodeTemplate = VersionTemplate.build([
    { name: 'major', length: 3 }, // 版本轮次
    { name: 'minor', length: 1 }, // 补丁轮次
    { name: 'patch', length: 3 }, // 灰度轮次
    { name: 'build', length: 2 }, // 独立灰度轮次
  ]);
  versionNameTemplate = VersionTemplate.build([
    { name: 'major', length: 2 }, // 版本轮次
    { name: 'minor', length: 1 }, // 补丁轮次
    { name: 'patch', length: 1 }, // 灰度轮次
  ]);
}

/**
 * IOS定义版本号模板, 依据剪映和目前是{@link https://bytedance.larkoffice.com/wiki/wikcnG2oxovtvKjihx41jcx4Vod}
 */
class VersionInfoForJianyingIos extends VersionInfoConfig {
  static appId = 177501;
  static platformType = PlatformType.Android;
  versionCodeTemplate = VersionTemplate.build([
    { name: 'major', length: 2 }, // 版本轮次
    { name: 'minor', length: 1 }, // 补丁轮次
    { name: 'patch', length: 1 }, // 灰度轮次
    { name: 'build', length: 2 }, // 独立灰度轮次
  ]);
  versionNameTemplate = VersionTemplate.build([
    { name: 'major', length: 2 }, // 版本轮次
    { name: 'minor', length: 1 }, // 补丁轮次
    { name: 'patch', length: 1 }, // 灰度轮次
  ]);
}

class EmptyVersionInfo extends VersionInfoConfig {
  static appId = -1;
  static platformType = PlatformType.init;
  static versionCodeTemplate = [];
  static versionNameTemplate = [];
}

class VersionInfoForCapcutAndroid extends VersionInfoConfig {
  static appId = 300602;
  static platformType = PlatformType.Android;
  static versionCodeTemplate = [
    { name: 'major', length: 3 }, // 版本轮次
    { name: 'minor', length: 1 }, // 补丁轮次
    { name: 'patch', length: 2 }, // 灰度轮次
    { name: 'build', length: 2 }, // 独立灰度轮次
  ];
}

class VersionInfoForCapcutIos extends VersionInfoConfig {
  static appId = 300601;
  static platformType = PlatformType.Android;
  static versionCodeTemplate = [
    { name: 'major', length: 2 }, // 版本轮次
    { name: 'minor', length: 1 }, // 补丁轮次
    { name: 'patch', length: 1 }, // 灰度轮次
    { name: 'build', length: 2 }, // 独立灰度轮次
  ];
}

export class VersionInfoFactory {
  static createVersionInfoConfig(appId: AppId, platformType: PlatformType): VersionInfoConfig {
    const VersionInfoConfigs = [
      VersionInfoForJianyingAndroid,
      VersionInfoForCapcutAndroid,
      VersionInfoForJianyingIos,
      VersionInfoForCapcutIos,
    ];
    const versionInfoConfig =
      VersionInfoConfigs.find(config => {
        config.match(appId, platformType);
      }) ?? VersionInfoForJianyingAndroid;
    return new versionInfoConfig();
  }
}

function parseVersion(version: string, template: VersionTemplate): number[] {
  return template.parseVersion(version);
}

function compareVersions(template: VersionTemplate) {
  return (v1: string, v2: string): number => {
    const v1Parts = parseVersion(v1, template);
    const v2Parts = parseVersion(v2, template);

    for (let i = 0; i < template.size(); i++) {
      const v1Part = v1Parts[i] || 0; // 如果 v1Part 没有值，则为 0
      const v2Part = v2Parts[i] || 0; // 如果 v2Part 没有值，则为 0
      if (v1Part < v2Part) {
        return -1;
      }

      if (v1Part > v2Part) {
        return 1;
      }
    }
    return 0;
  };
}

//  升序排列,如输入[141000300,141000200,141000100]，输出为[141000100,141000200,141000300]
export function sortVersionsAscent(versions: string[], template: VersionTemplate): string[] {
  return versions.sort(compareVersions(template));
}

function minusVersion(versionPart: number[], index: number, template: VersionTemplate): string {
  if (versionPart.length !== template.size()) {
    return '';
  }
  const versions = cloneDeep(versionPart);
  versions[index] -= 1;
  return versions.map((v, i) => v.toString().padStart(template.at(i)?.length ?? 0, '0')).join('');
}

export function minusSmallVersion(version: string, template: VersionTemplate) {
  return minusVersion(parseVersion(version, template), 2, template);
}

export function minusBigVersion(version: string, template: VersionTemplate) {
  return minusVersion(parseVersion(version, template), 0, template);
}

export function versionCode2version(versionCode: string, template: VersionTemplate): string | undefined {
  const versionParts = parseVersion(versionCode, template);
  if (versionParts.length === 0) {
    console.error(`versionCode2version:${JSON.stringify(versionParts)}`);
    return;
  }
  return versionParts.join('.');
}

// 获取同个大版本的上一个小版本,用于环比计算,如1410003000的上个小版本为1410002000
// export function getLastSmallVersion(
// 	versions: string[],
// 	version: string,
// 	template: VersionTemplate
// ): string | undefined {
// 	const targetVersion = minusSmallVersion(version, template);
// 	if (versions.find((v) => targetVersion === v) !== undefined) {
// 		console.log(`getLastSmallVersionMothOnMoth => ${targetVersion}`);
// 		return targetVersion;
// 	}
// 	const containVersion = versions.findIndex((v) => version === v) !== -1;
// 	console.log(`getLastSmallVersionMothOnMoth, version => ${version}, containVersion => ${containVersion}`);
// 	if (containVersion) {
// 		const versionsSortedAscent = sortVersionsAscent(versions, template);
// 		const index = versionsSortedAscent.findIndex((v) => version === v);
// 		console.log(
// 			`getLastSmallVersionMothOnMoth, index => ${index}, versionsSortedAscent => ${versionsSortedAscent[index - 1]}`
// 		);
// 		if (index > 0) {
// 			return versionsSortedAscent[index - 1];
// 		}
// 	}
// 	return undefined;
// }

// export function comparePerformance(baseVersion: string, compareVersion: string) {}

// // 获取上个大版本的同周期的小版本,用于同比计算,如1410003000对应的小版本为1400003000
// export function getLastSmallVersionYearOnYear(versions: string[], version: string): string {
//
// }
