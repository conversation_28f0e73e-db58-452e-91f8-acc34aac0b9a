export function myFilter<T, K extends keyof T>(array: T[], key: K, value: T[K]): T[] {
  return array.filter(item => item[key] === value);
}

export function myFilters<T, K extends keyof T>(array: T[], key: K, values: T[K][]): T[] {
  const valuesSet = new Set(values);
  return array.filter(item => valuesSet.has(item[key]));
}

export function myGroupsBy<T, K extends keyof T>(array: T[], key: K): Record<string, T[]> {
  return array.reduce(
    (result, currentValue) => {
      const groupKey = String(currentValue[key]);
      if (!result[groupKey]) {
        result[groupKey] = [];
      }
      result[groupKey].push(currentValue);
      return result;
    },
    {} as Record<string, T[]>,
  );
}

export function myTake<T, K extends keyof T>(array: T[], key: K): T[K][] {
  return array.map(item => item[key]);
}

// 定义类型
type Item = {
  [key: string | number | symbol]: any;
};

export function myGroupBy<T extends Item, K extends keyof T>(array: T[], key: K): Record<T[K], T> {
  return array.reduce(
    (acc, item) => {
      acc[item[key]] = item;
      return acc;
    },
    {} as Record<T[K], T>,
  );
}

export function myMerge() {}
