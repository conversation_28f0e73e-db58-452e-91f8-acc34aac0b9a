import { Condition } from '@shared/typings/slardar/crash/issueListSearch';
import { fail, HttpResponse, success } from '@shared/utils/api';

export interface ParsedUrlData {
  scheme: string;
  host: string;
  path: string;
  queryParams: { [key: string]: string };
  hashParams: string;
  hashPath: string;
  hashQueryParams: { [key: string]: string };
}

export function parseUrl(url: string): ParsedUrlData | undefined {
  const parsedUrl = new URL(url);
  const scheme = parsedUrl.protocol.slice(0, -1); // 去掉末尾的 ':'
  const host = parsedUrl.hostname;
  const path = parsedUrl.pathname;
  const queryParams = Object.fromEntries(parsedUrl.searchParams.entries());
  const hashParams = parsedUrl.hash.startsWith('#') ? parsedUrl.hash.slice(1) : parsedUrl.hash;
  let hashPath = '';
  let hashQueryParams: { [key: string]: string } = {};
  if (hashParams.includes('?')) {
    const [hp, qy] = hashParams.split('?');
    hashPath = hp || '';
    if (qy) {
      hashQueryParams = Object.fromEntries(new URLSearchParams(qy).entries());
    }
  } else {
    hashPath = hashParams;
  }
  return {
    scheme,
    host,
    path,
    queryParams,
    hashParams,
    hashPath,
    hashQueryParams,
  };
}

/**
 *   https://slardar-us.bytedance.net/node/app_detail/
 *   ?region=maliva&aid=3006&os=iOS&subregion=row&type=app&lang=zh
 *   #/abnormal/detail/crash/2f23f512044ddcdf676042d6f5e0ef76
 *   ?params={"start_time":1724729640,"end_time":1724816040,"granularity":3600,"filters_conditions":{"type":"and","sub_conditions":[]},"order_by":"user_descend","pgno":1,"pgsz":10,"crash_time_type":"insert_time","anls_dim":[],"token":"","token_type":0,"IssueActiveTab":"","event_index":1}
 */
export interface ParsedSlardarUrlData {
  host: string;
  aid: number;
  os: string;
  region: string;
  subregion: string;
  type: string;
  lang: string;
  crash_type: string;
  event_id?: string;
  issue_id?: string;
  start_time?: number;
  end_time?: number;
  granularity?: number;
  filters_conditions?: Condition;
  filters_conditions_raw?: string;
  order_by?: string;
  pgno?: number;
  pgsz?: number;
  crash_time_type?: string;
  anls_dim?: string[];
  token?: string;
  token_type?: number;
  IssueActiveTab?: string;
  event_index?: number;
}

/**
 *   https://slardar-us.bytedance.net/node/app_detail/
 *   ?region=maliva&aid=3006&os=iOS&subregion=row&type=app&lang=zh
 *   #/abnormal/detail/crash/2f23f512044ddcdf676042d6f5e0ef76
 *   ?params={"start_time":1724729640,"end_time":1724816040,"granularity":3600,"filters_conditions":{"type":"and","sub_conditions":[]},"order_by":"user_descend","pgno":1,"pgsz":10,"crash_time_type":"insert_time","anls_dim":[],"token":"","token_type":0,"IssueActiveTab":"","event_index":1}
 * @param url
 */
export function parseSlardarUrl(url: string): HttpResponse<ParsedSlardarUrlData> {
  const parsedUrl = parseUrl(url);
  if (!parsedUrl) {
    return fail('url is not valid');
  }
  const { host } = parsedUrl;
  if (host !== 'slardar.bytedance.net' && host !== 'slardar-us.bytedance.net') {
    return fail('Host incorrect.');
  }

  const { path } = parsedUrl;
  if (path !== '/node/app_detail/') {
    return fail('Path incorrect.');
  }

  let hashQueryParams;
  if (parsedUrl.hashQueryParams.params) {
    hashQueryParams = JSON.parse(parsedUrl.hashQueryParams.params);
  }

  const hashPathList = parsedUrl.hashPath.split('/');
  let crash_type = hashPathList.length === 3 ? hashPathList[2] : hashPathList[3];
  if (crash_type === 'other_system_kill_unknown') {
    // URL上是这个有 _unknown 后缀
    crash_type = 'other_system_kill'; // api 接口上需要的是这个，没有 _unknown 后缀，目前只发现未知强杀有这个问题
  }
  const issue_id = hashPathList.length >= 5 ? hashPathList[4] : undefined;
  const event_id = hashPathList.length >= 6 ? hashPathList[5] : undefined;
  const data: ParsedSlardarUrlData = {
    host,
    aid: Number(parsedUrl.queryParams.aid),
    os: parsedUrl.queryParams.os,
    region: parsedUrl.queryParams.region,
    subregion: parsedUrl.queryParams.subregion,
    type: parsedUrl.queryParams.type,
    lang: parsedUrl.queryParams.lang,
    crash_type,
    event_id,
    issue_id,
    start_time: hashQueryParams ? Number(hashQueryParams.start_time) : undefined,
    end_time: hashQueryParams ? Number(hashQueryParams.end_time) : undefined,
    granularity: hashQueryParams ? Number(hashQueryParams.granularity) : undefined,
    filters_conditions: hashQueryParams?.filters_conditions,
    filters_conditions_raw: hashQueryParams?.filters_conditions
      ? JSON.stringify(hashQueryParams.filters_conditions)
      : undefined,
    order_by: hashQueryParams?.order_by,
    pgno: hashQueryParams?.pgno,
    pgsz: hashQueryParams?.pgsz,
    crash_time_type: hashQueryParams?.crash_time_type,
    anls_dim: hashQueryParams?.anls_dim,
    token: hashQueryParams?.token,
    token_type: hashQueryParams?.token_type,
    IssueActiveTab: hashQueryParams?.IssueActiveTab,
    event_index: hashQueryParams?.event_index,
  };
  return success(data);
}

export function getUserActionClassFromAlog(alogData: string[]): string[] {
  const regex = /class=(\w+)/;
  const userActionClass = new Set<string>();
  for (const alog of alogData) {
    if (alog.includes('UserAction')) {
      const matches = alog.match(regex);
      if (matches && matches[1]) {
        userActionClass.add(matches[1]);
      }
    }
  }
  return Array.from(userActionClass);
}
