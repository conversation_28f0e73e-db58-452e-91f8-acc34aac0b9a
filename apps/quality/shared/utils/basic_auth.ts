import { bytecloudAsyncJwtService } from '@bytecloud/common-lib';
export function basic_auth(username: string, password: string): string {
  return Buffer.from(`${username}:${password}`).toString('base64');
}

export enum ReqRegion {
  CN,
  I18n,
}

const getFePpartition = (reqRegion: ReqRegion) => {
  switch (reqRegion) {
    case ReqRegion.CN:
      return 'cn';
    case ReqRegion.I18n:
      return 'i18n';
    default:
      return 'cn';
  }
};

export const getJwtToken = async (reqRegion: ReqRegion) => {
  const token = await bytecloudAsyncJwtService.getServiceByPartition(getFePpartition(reqRegion)).then(s => s.getJwt());
  return token;
};
