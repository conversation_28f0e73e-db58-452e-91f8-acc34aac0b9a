import {
  MeegoModuleConfig,
  ProcessIssueResult,
  SymboLicateAddressDetail,
} from '@shared/typings/slardar/crash/ttMleaksFinderData';
import { SlardarPlatformType } from '@pa/shared/dist/src/appSettings/appSettings';
import { CrashType } from '@shared/typings/slardar/crash/issueListSearch';
import SlardarService from '../../api/service/slardar/slardar';
import { getSlardarIssueLink } from '../../api/utils/slardarLink';
import { BytedLogger } from '@gulux/gulux/byted-logger';
import { autoTestCreateWrapper } from '../../api/service/meegoIssueCreateWrapper';
import MeegoService from '../../api/service/meego';
import { useInject } from '@edenx/runtime/bff';
import { GetSymboLicateAddressListResult } from '@shared/typings/slardar/crash/issueLocation';

export function getPlatformFromOS(os?: string): SlardarPlatformType {
  return os === 'iOS' ? SlardarPlatformType.iOS : SlardarPlatformType.Android;
}

export function getMeegoModuleConfig(aid: number): MeegoModuleConfig {
  if (aid === 1775) {
    return {
      modules: { value: 'fpm197out', label: 'Slardar稳定性' },
      category: { value: '0fsrpfd9a', label: '稳定性问题' },
    };
  }
  return {
    modules: { value: 'hmpn7hkrm', label: 'Slardar稳定性' },
    category: { value: '0fsrpfd9a', label: '稳定性问题' },
  };
}

function addEmailSuffix(email?: string): string {
  if (!email) {
    return '';
  }
  return email.endsWith('@bytedance.com') ? email : `${email}@bytedance.com`;
}

function createDuplicateResponse(meegoId: number): ProcessIssueResult {
  return {
    data: `https://meego.feishu.cn/faceu/issue/detail/${meegoId}`,
    message: '重复性提单，issueID已经在数据库中存在',
    ret: 10002,
  };
}

function updateOperatorFromIssueInfo(issueInfo: any, operator: string): string {
  if (issueInfo.data.managers && issueInfo.data.managers.length > 0) {
    operator = issueInfo.data.managers[0].endsWith('@bytedance.com')
      ? issueInfo.data.managers[0]
      : `${issueInfo.data.managers[0]}@bytedance.com`;
  }
  return operator;
}

function isIgnoreIssue(symbolResult: GetSymboLicateAddressListResult): boolean {
  const ignoreSymbols = ['UIViewController._magicTransition', '-[BUPlayableAd(Detect) startDetect]', 'in ByteInsight'];
  const ignorerRetainCycles = [
    'LRUCache',
    'OpenCombine',
    'MainTabBarController',
    'ActionTapGestureRecognizer',
    'SKStoreProductViewController',
    'BISettingsViewController',
    'FLEEX',
    'BICase',
    'DDParsecCollectionViewController',
    'LVVideoCompressTask',
    'NestableScrollView',
    'IGListAdapter',
    'Aspects',
    'LynxGestureVelocityTracker',
    'PipPlayerView',
    'LynxTouchHandler',
    'UgenFrameLayoutWidget',
  ];

  let ignore = false;
  do {
    if (symbolResult.symbol && symbolResult.symbol.length > 0) {
      ignore = symbolResult.symbol.some((item: SymboLicateAddressDetail) =>
        ignoreSymbols.some((ignoreSymbol: string) => item.symbol.includes(ignoreSymbol)),
      );
    }
    if (ignore) {
      break;
    }

    if (symbolResult.leaks_retain_cycle && symbolResult.leaks_retain_cycle.length > 0) {
      ignore = ignorerRetainCycles.some((ignoreRetainCycle: string) =>
        symbolResult.leaks_retain_cycle?.includes(ignoreRetainCycle),
      );
    }
    if (ignore) {
      break;
    }
    // eslint-disable-next-line no-constant-condition
  } while (0);

  return ignore;
}

function createIssueDetails(
  symbolResult: any,
  issueId: string,
  crashType: CrashType,
  { aid, platform, version }: { aid: number; platform: SlardarPlatformType; version: string },
): { issueTitle: string; issueLink: string } {
  const issueTitle = `TTMLeaksFinder自动提单-${issueId}-【${crashType}】-${symbolResult.leaks_cycle_key_class}`;
  const slardar_issue_link = getSlardarIssueLink(aid, crashType, platform, issueId, symbolResult.version || version);
  const consumption_link = 'https://bytedance.larkoffice.com/docx/OU4TdgIiKohNAAxTY0GciacZnge';
  const issueLink = `消费指引文档[${consumption_link}]-leaks_retain_cycle:${symbolResult.leaks_retain_cycle}${slardar_issue_link}`;
  return { issueTitle, issueLink };
}

async function createChatGroup({
  aid,
  reporter,
  issueId,
  crashType,
  platform,
  issueTitle,
  issueInfo,
  slardarService,
}: {
  aid: number;
  reporter: string;
  issueId: string;
  crashType: string;
  platform: SlardarPlatformType;
  issueTitle: string;
  issueInfo: any;
  slardarService: SlardarService;
}): Promise<string | undefined> {
  return await slardarService.joinSlardarChatAndInviteMeegoBot(
    aid,
    reporter,
    issueId,
    crashType,
    platform,
    issueTitle,
    issueInfo.data.id,
  );
}
async function getMajorMinorPatch(version: string | null | undefined): Promise<string> {
  // 处理空值或无效输入
  if (!version) {
    return '0.0.0'; // 返回默认版本号
  }

  // 按 "." 分割版本号
  const parts = version.split('.');

  // 如果部分数少于 3，补全为 "0"
  while (parts.length < 3) {
    parts.push('0');
  }

  // 取前三个部分
  return parts.slice(0, 3).join('.');
}

async function createMeegoIssue({
  issueId,
  issueTitle,
  platform,
  version,
  issueLink,
  operator,
  reporter,
  moduleConfig,
  chatId,
  logger,
}: {
  issueId: string;
  issueTitle: string;
  platform: SlardarPlatformType;
  version: string;
  issueLink: string;
  operator: string;
  reporter: string;
  moduleConfig: MeegoModuleConfig;
  chatId: string | undefined;
  logger: BytedLogger;
}): Promise<ProcessIssueResult> {
  const new_version = await getMajorMinorPatch(version);
  const res = await autoTestCreateWrapper(
    issueId,
    issueTitle,
    platform,
    new_version,
    new_version,
    issueLink,
    '2',
    reporter,
    'option_7',
    operator,
    'n6gelc1_2',
    platform === SlardarPlatformType.Android ? ['<EMAIL>', reporter] : [reporter, operator],
    moduleConfig.modules,
    chatId,
    moduleConfig.category,
  );
  logger.info(`[ttmleaks自动化测试提单]结束`, res);
  return {
    ret: res.ret,
    data: res.data ? `https://meego.feishu.cn/faceu/issue/detail/${res.data}` : undefined,
    error_msg: res.error_msg ? res.error_msg : undefined,
    operator: res.data ? res.operator : undefined,
    status: res.data ? res.status : undefined,
    priority: res.data ? Number(res.priority) : undefined,
  };
}

function createIgnoreResponse(issueId: string): ProcessIssueResult {
  return {
    ret: 10004, // 忽略的错误码
    message: `判断是忽略问题 issueId:${issueId}`,
  };
}

function createNoNewResponse(): ProcessIssueResult {
  return {
    ret: 10003, // 忽略的错误码
    message: `该问题不是新问题`,
  };
}

async function processIssue({
  issueId,
  issueDetail,
  platform,
  crashType,
  aid,
  reporter,
  rule,
  moduleConfig,
  meegoService,
  slardarService,
  logger,
}: {
  issueId: string;
  issueDetail: any;
  platform: SlardarPlatformType;
  crashType: CrashType;
  aid: number;
  reporter: string;
  rule: any;
  moduleConfig: MeegoModuleConfig;
  meegoService: MeegoService;
  slardarService: SlardarService;
  logger: BytedLogger;
}): Promise<ProcessIssueResult | undefined> {
  const owners = rule?.owners;
  let operator = addEmailSuffix(owners);

  logger.info(`【处理Issue】开始处理 IssueID:${issueId}, 崩溃类型:${crashType}, 负责人:${operator}`);

  const existMeegoId = await meegoService.existMeegoId({ platform, issue_id: issueId });
  if (existMeegoId) {
    logger.info(`【处理Issue】发现重复提单，已存在Meego单: ${existMeegoId}`);
    return createDuplicateResponse(existMeegoId);
  }

  logger.info(`【处理Issue】开始获取Issue详情信息...`);
  const issueInfo = await slardarService.getCrashInfoById(aid, issueId, platform, crashType);
  if (!issueInfo.data) {
    logger.error(`【处理Issue】获取Issue详情失败，IssueID: ${issueId}`);
    return;
  }

  if (issueInfo.data.status !== 'new_created') {
    logger.error(`【处理Issue】该问题不是新增问题，IssueID: ${issueId} status: ${issueInfo.data.status}`);
    return createNoNewResponse();
  }

  const version = (issueInfo.data.version ?? '').toString();
  const oldOperator = operator;
  operator = updateOperatorFromIssueInfo(issueInfo, operator);
  if (oldOperator !== operator) {
    logger.info(`【处理Issue】更新负责人: ${oldOperator} -> ${operator}`);
  }

  logger.info(`【处理Issue】开始获取符号化信息...`);
  const symbolResult = await slardarService.GetSymboLicateAddressList(
    aid,
    platform,
    issueId,
    issueDetail.window_start_time,
    issueDetail.window_end_time,
    1,
    1,
    crashType,
  );

  logger.info(`【处理Issue】符号化信息: ${JSON.stringify(symbolResult)}`);

  if (isIgnoreIssue(symbolResult)) {
    logger.info(`【处理Issue】根据符号化信息判定为可忽略问题`);
    return createIgnoreResponse(issueId);
  }

  logger.info(`【处理Issue】创建Issue详情...`);
  const { issueTitle, issueLink } = createIssueDetails(symbolResult, issueId, crashType, {
    aid,
    platform,
    version,
  });
  logger.info(`【处理Issue】Issue标题: ${issueTitle}`);

  logger.info(`【处理Issue】开始创建群聊...`);
  const chatId = await createChatGroup({
    aid,
    reporter,
    issueId,
    crashType,
    platform,
    issueTitle,
    issueInfo,
    slardarService,
  });
  logger.info(`【处理Issue】群聊创建${chatId ? '成功' : '失败'}, ChatID: ${chatId}`);

  logger.info(`【处理Issue】开始创建Meego工单...`);
  return await createMeegoIssue({
    issueId,
    issueTitle,
    platform,
    version: symbolResult.version || version,
    issueLink,
    operator,
    reporter,
    moduleConfig,
    chatId,
    logger,
  });
}

export async function processAlarmContexts(
  alarm: any,
  config: {
    platform: SlardarPlatformType;
    crashType: CrashType;
    aid: number;
    reporter: string;
    rule?: any;
    moduleConfig: MeegoModuleConfig;
    logger: BytedLogger;
  },
): Promise<ProcessIssueResult | undefined> {
  const { platform, crashType, aid, reporter, rule, moduleConfig, logger } = config;
  const meegoService = useInject(MeegoService);
  const slardarService = useInject(SlardarService);
  const contexts: any[] = alarm.alarm_context_v2.alarm_context_v2;

  logger.info(`【处理告警】开始处理告警上下文，共 ${Object.keys(contexts).length} 条数据`);

  for (const [_, nestedObject] of Object.entries(contexts)) {
    logger.info(`【处理告警】处理嵌套对象，包含 ${Object.keys(nestedObject).length} 个Issue`);
    for (const [issueId, issueDetail] of Object.entries(nestedObject)) {
      logger.info(`【处理告警】开始处理Issue: ${issueId}`);
      const result = await processIssue({
        issueId,
        issueDetail,
        platform,
        crashType,
        aid,
        reporter,
        rule,
        moduleConfig,
        meegoService,
        slardarService,
        logger,
      });

      if (result) {
        logger.info(`【处理告警】Issue处理完成，结果: ${JSON.stringify(result)}`);
        return result;
      }
    }
  }

  logger.info(`【处理告警】所有Issue处理完成`);
}
