import { current_region, Region } from '../../api/utils/region';

export function BitsAppId(isAndroid: boolean, isCN: boolean = current_region() !== Region.SG) {
  if (isCN) {
    return isAndroid ? 177502 : 177501;
  }
  return isAndroid ? 2020095699 : 2020095701;
}

export function HypicBitsAppId(isAndroid: boolean, isCN: boolean = current_region() !== Region.SG) {
  if (isCN) {
    return isAndroid ? 251502 : 251501;
  }
  return isAndroid ? 2020093924 : 2020093988;
}

export function DreaminaBitsAppId(isAndroid: boolean) {
  return isAndroid ? 2020095292 : 2020095291;
}
