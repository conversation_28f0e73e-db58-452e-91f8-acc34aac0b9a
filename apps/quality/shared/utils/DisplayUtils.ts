import { markDown } from '@shared/utils/lark_card_utils';
import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';

export function format(num?: number, fractionDigits = 2): string {
  if (num === undefined) {
    return '-';
  }
  if (fractionDigits <= 0) {
    return num.toString();
  }
  const fraction = Math.pow(10, fractionDigits);
  // 将数字乘以10的位数并四舍五入
  const roundedNum: number = Math.round(num * fraction);

  // 将结果除以100，并转换为两位小数的浮点数
  const result: number = roundedNum / fraction;
  if (result > 0) {
    return `+${result.toFixed(fractionDigits)}`;
  }
  return result.toFixed(fractionDigits);
}

export function formatWarningPercent(num?: number, fractionDigits = 2): string {
  if (num === undefined) {
    return '';
  }
  let prefix = '';
  if (num > 0) {
    prefix = '↑';
  } else if (num < 0) {
    prefix = '↓';
  }
  const suffix = '%';
  if (fractionDigits <= 0) {
    return `${prefix}${num.toString()}${suffix}`;
  }
  const fraction = Math.pow(10, fractionDigits);
  // 将数字乘以10的位数并四舍五入
  const roundedNum: number = Math.round(num * fraction);

  // 将结果除以100，并转换为两位小数的浮点数
  const result: number = roundedNum / fraction;
  return `${prefix}${result.toFixed(2)}${suffix}`;
}

function formatCompare(percentage?: number, absolute?: number): string {
  if (!absolute || !percentage) {
    return '';
  }
  const formatMarkDown = markDown(`${formatWarningPercent(percentage)}（${format(absolute)}）`).bold();
  if (absolute > 0) {
    return formatMarkDown.redFont().toString();
  } else if (absolute < 0) {
    return formatMarkDown.greenFont().toString();
  }
  return formatMarkDown.toString();
}

export function waringFormat(
  displayName?: string,
  currentValue?: number,
  compareVersion?: string,
  absolute?: number,
  percentage?: number,
): string {
  return (
    `${markDown(`${displayName}：${currentValue?.toFixed(2)}`).bold()}，` +
    `对比版本${compareVersion}，${formatCompare(percentage, absolute)}`
  );
}

export function waringFormats(
  displayName?: string,
  teaUrl?: string,
  currentValue?: number,
  compares?: {
    compareVersion?: string;
    absolute?: number;
    percentage?: number;
  }[],
): string {
  return (
    `${teaUrl ? markDown(`${displayName}`).url(teaUrl) : markDown(`${displayName}`).bold()}${markDown(
      `：${currentValue?.toFixed(2)}`,
    ).bold()}\n` +
    `${compares
      ?.map(compare => `对比版本${compare.compareVersion}，${formatCompare(compare.percentage, compare.absolute)}`)
      .join('\n')}`
  );
}

export function formatAlarmRecordTime(ts: number) {
  dayjs.extend(utc);
  return dayjs.unix(ts).utcOffset(8).format('YYYY-MM-DD HH:mm:ss');
}
