import { reportTeaEvent } from '@api/index';
import { message } from 'antd';
import { Button } from '@douyinfe/semi-ui';
import { DiscoverStag2Option, DiscoverStage } from '@shared/walle/consumeResult';
import { useModel } from '@edenx/runtime/model';
import UserSettingModule from '@/model/userSettingModel';
import { getVersionList } from '@api/mrProfiler';
import { meegoCreateSlardarBug } from '@api/meego';
import { IconButtonProps } from '@douyinfe/semi-ui/lib/es/iconButton';
import { ButtonProps } from '@douyinfe/semi-ui/lib/es/button';
import { NetworkCode } from '@pa/shared/dist/src/core';
import { useState } from 'react';

export enum ToolsName {
  MRProfilerTask = 'MRProfiler-任务列表',
  MRProfilerCodeChangeSearch = 'MRProfiler-代码变更-单点查询',
  MRProfilerCodeChangeIssue = 'MRProfiler-代码变更-issue查询',
  MeegoCreate = 'Meego建单',
  MeegoAutoAssign = 'meego自动分配-尝试分配',
  MeegoAutoAssignAttribution = 'meego自动分配-历史分配归因',
  QualityCompare = '版本质量对比',
  MarketMultiAttribution = '大盘归因',
  SingleMultiAttribution = '单issue归因',
  MemoryGraphDetail = 'MemoryGraph-issue详情',
  MemoryGraphAnalyze = 'MemoryGraph-分析最近一小时',
  LibraAttribution = '实验归因',
  LogAttribution = '日志归因',
}

type QualityToolsButtonProps = ButtonProps &
  IconButtonProps & {
    toolName: ToolsName; // 接收枚举类型
    title: string; // 按钮标题
    onClick: () => void; // 点击事件处理函数
  };

type CreatMeegoButtonProps = ButtonProps &
  IconButtonProps & {
    link: string;
    discoverVersion?: string;
    priority?: string;
    discoverStage?: string; // 发现阶段
    isConsume?: boolean; // 是否触发自动分配
    channel?: string;
    onClick: () => void; // 点击事件处理函数
  };

export const QualityToolsButton: React.FC<QualityToolsButtonProps> = ({ toolName, title, onClick }) => {
  const [userSettings] = useModel(UserSettingModule);
  const handleClick = () => {
    if (onClick) {
      onClick();
    }
    reportTeaEvent({
      data: {
        eventName: 'Quality_Click',
        params: {
          toolName,
          email: userSettings.info.email,
        },
      },
    }).then(r => {
      console.log('reporttea', r);
    });
  };

  return <Button onClick={handleClick}>{title}</Button>;
};

export const CreatMeegoButton: React.FC<CreatMeegoButtonProps> = ({
  link,
  discoverVersion,
  discoverStage,
  priority,
  channel,
  onClick,
}) => {
  const [buttonState, setButtonState] = useState<boolean>(false);
  const [userSettings] = useModel(UserSettingModule);
  const handleClick = async () => {
    if (onClick) {
      onClick();
    }
    setButtonState(true);
    const inputDiscoverStage = discoverStage ?? DiscoverStag2Option[DiscoverStage.GREY];
    const versionListTemp = await getVersionList();
    const result = await meegoCreateSlardarBug({
      data: {
        link,
        discoverVersion: discoverVersion ?? versionListTemp[0],
        priority: priority ?? '2',
        assignee: userSettings.info.email,
        discoverStage: inputDiscoverStage,
        creator: userSettings.info.email,
        isConsume: true,
        channel: channel ?? '5',
        isAutoCreated: false,
      },
    });
    setButtonState(false);
    if (result?.ret === NetworkCode.Success && result.data) {
      if (!result.IsConsumed) {
        message.error(`walle分配失败，使用兜底分配人`, 15);
      }
    } else {
      message.error(`建单失败,code=${result?.ret},msg=${result?.error_msg}`, 15);
    }
  };
  return (
    <Button loading={buttonState} onClick={handleClick}>
      创建meego单
    </Button>
  );
};
