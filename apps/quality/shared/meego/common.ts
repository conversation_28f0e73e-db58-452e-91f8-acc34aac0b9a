export interface Business {
  name: string;
  id: string;
  child?: Business[];
}

/**
 * 商业化-订阅
 */
export const BUSINESS_ID_COMMERCIAL_SUBSCRIPTION = '65b9eda733ab56f433a1f023';

/**
 * 商业化-广告
 */
export const BUSINESS_ID_COMMERCIAL_AD = '65b9edacb7f49c757876d9ec';

export const BusinessTree: Business = {
  name: 'faceu',
  id: 'faceu',
  child: [
    {
      name: '剪映',
      id: '6776752fbbf2933296718857',
      child: [
        {
          name: 'UGC与工具增长',
          id: '67767549bbf2933296718858',
        },
        {
          name: 'PGC',
          id: '677675576f82faee1f5393a6',
        },
        {
          name: '编辑器及特效产品',
          id: '67767561f63559f90ed76bba',
        },
        {
          name: '内容生态与分发',
          id: '677675a0f5a1bb6a2e8dc7bf',
        },
        {
          name: 'AI剪辑',
          id: '677675ab364345076aa3f53c',
        },
      ],
    },
    {
      name: 'CapCut',
      id: '6776737ba18fb84661bb8dff',
      child: [
        {
          id: '6776739c77b245481210662c',
          name: 'AI特效开放',
        },
        {
          name: '模板工具与分发',
          id: '677673be5ca08fb74bc288f1',
          child: [
            {
              name: '模板框架与体验',
              id: '6776741e2d9caf4610812327',
            },
            {
              name: '搜推分发',
              id: '6776742df63559f90ed76bb9',
            },
            {
              name: '生态基建',
              id: '677674327fb703229ca2da4b',
            },
          ],
        },
        {
          child: [
            {
              name: '文本编辑器',
              id: '6776743d9f18eb6e50451284',
            },
            {
              name: '多轨AI',
              id: '677674572d9e52de81ec0256',
            },
            {
              name: '基础编辑器',
              id: '6776745d77b245481210662d',
            },
            {
              name: 'CC智能成片',
              id: '677674af2d9caf4610812328',
            },
          ],
          name: '多轨工具',
          id: '677673ce2d9caf4610812326',
        },
        {
          name: '主框架与Web',
          id: '677673f824dc7e8b89bfe79c',
        },
        {
          id: '677673ff7fb703229ca2da4a',
          name: '营销策略',
        },
        {
          name: '内容与作者平台',
          id: '6776740a5ca08fb74bc288f2',
          child: [
            {
              name: '作者策略',
              id: '677675fe9f18eb6e50451285',
            },
            {
              name: '素材生产与分发',
              id: '67767607a55757a8d4b94508',
            },
          ],
        },
      ],
    },
    {
      name: '醒图Hypic',
      id: '65b78a72fb04e63ddcf27a17',
      child: [
        {
          name: 'AI创新',
          id: '65b9ed0cc04d337f60ee7319',
        },
        {
          id: '65b9eeec7cdb8b48915c2491',
          name: '人像&图片编辑',
        },
        {
          name: '平面排版',
          id: '65b9ed20eba4437e42ce5dfe',
        },
        {
          name: '编辑器主框架和策略',
          id: '65b9ed2f4c8011a004290e77',
        },
        {
          name: '主框架',
          id: '6776762e126944a586bcac3e',
        },
        {
          name: '工具',
          id: '6776764e5ca08fb74bc288f3',
        },
        {
          name: '模板',
          id: '677676536f82faee1f5393a7',
        },
      ],
    },
    {
      name: '即梦Dreamina',
      id: '65b78a95e41c13baa2df8b4f',
      child: [
        {
          id: '65b9eca9fe8e8a2ba7d76fe0',
          name: 'AI视频',
        },
        {
          name: 'AI生图',
          id: '65b9ecb04c8011a004290e76',
        },
        {
          name: 'AI主框架',
          id: '662db38f31651ae9817bc539',
        },
        {
          name: '用户社区',
          id: '6646cdeb780993bbc276e131',
        },
        {
          name: '内容平台',
          id: '66558be4dae2b977c61c6cc9',
        },
      ],
    },
    {
      name: 'vimo与素材库',
      id: '65b78aada51f65883399d216',
      child: [
        {
          name: 'vimo后台',
          id: '65b9ee3df8a874d45c1ccb1f',
        },
        {
          name: '版权平台',
          id: '65b9ef1fc6c5db507be6a501',
        },
      ],
    },
    {
      name: '商业化',
      id: '65b78a9fa8e10bb0037ca666',
      child: [
        {
          name: '经营分析',
          id: '65b9eda2a1f0cc1085a24eb6',
        },
        {
          name: '订阅',
          id: BUSINESS_ID_COMMERCIAL_SUBSCRIPTION,
        },
        {
          name: '广告',
          id: BUSINESS_ID_COMMERCIAL_AD,
        },
      ],
    },
    {
      name: '营销工具',
      id: '65b78aa73219a70ff778f2c8',
      child: [
        {
          name: '权益方案',
          id: '65b9edc7a1f0cc1085a24eb7',
        },
        {
          name: '产品核心能力',
          id: '65b9edf6c6c5db507be6a500',
        },
      ],
    },
    {
      name: 'UG',
      id: '65560a963e56a6f76f7cea91',
      child: [
        {
          name: '剪映UG',
          id: '6563ffca77eb6f2a21263505',
        },
        {
          name: 'Capcut UG',
          id: '6563ffe1d07a5e787da10841',
        },
        {
          name: 'Dreamina UG',
          id: '6564002deb4ff95df280d671',
        },
        {
          name: '抖T联动',
          id: '669644493d534011a03198b6',
        },
        {
          name: 'SEO增长',
          id: '669644665c5909fdc0d46191',
        },
        {
          name: '社媒增长',
          id: '669644783ca67333fd583e5c',
        },
      ],
    },
    {
      name: '安全合规',
      id: '65b78ad457483f37bf3e89a4',
      child: [
        {
          id: '65e00b2fb85ded2fdab32dac',
          name: '中国区-安全合规',
        },
        {
          name: '非中区-安全合规',
          id: '65e00b3a862597d04e0bc55a',
        },
      ],
    },
    {
      name: '多媒体',
      id: '666be9c578aa933062d07ccc',
    },
    {
      name: '基础技术',
      id: '65b78ac8eee88dcefd577896',
    },
    {
      name: '服务架构',
      id: '65e00b63191094c74c554474',
    },
    {
      name: 'QA空间',
      id: '66f3da80c6349c2505c01c80',
      child: [
        {
          name: '对比任务',
          id: '66f3de03d57c62fef983e818',
        },
      ],
    },
    {
      name: 'meego试用业务线',
      id: '65f18c7c989ea5091bd042df',
    },
    {
      name: '视频工具与素材',
      id: '65b78a636c17cc737cede334',
      child: [
        {
          name: '视频工具',
          id: '65b9f6ee05d6a08bc2573443',
        },
        {
          name: '素材工具',
          id: '65b9f6802fb32ede619478fb',
        },
        {
          id: '65b9f6f6b7f49c757876d9ed',
          name: '素材分发',
        },
        {
          name: 'Smart Edit',
          id: '65b9ec96bdb200810812e2fc',
        },
      ],
    },
    {
      name: '智能成片',
      id: '65b9ec9f40049dd5998b2d55',
    },
    {
      name: '内容生态',
      id: '65b78a8abb0b7440ee555b79',
      child: [
        {
          name: '作者',
          id: '65b9ed4805d6a08bc2573440',
        },
        {
          name: '模板框架与分发',
          id: '65b9ed4c19bb0ccfe30bf225',
        },
        {
          name: '模板工具',
          id: '65b9ed555d26d00f591dfec2',
        },
      ],
    },
    {
      name: '主框架&基础产品',
      id: '65b78ab324e6075bc99882e8',
      child: [
        {
          name: '主框架',
          id: '65b9ee5cfe8e8a2ba7d76fe1',
        },
        {
          name: '基础产品',
          id: '65b9ee62c545dcc9935ba741',
        },
        {
          name: '插件化',
          id: '65b9ee68f8a874d45c1ccb20',
        },
      ],
    },
    {
      name: '历史配置',
      id: '65b78a4d3c918b8fa4f50762',
      child: [
        {
          name: '【勿选】配置中',
          id: '65b78a2df45f2e99e19ecf30',
        },
        {
          name: '剪映',
          id: '5cf8e4e44da26751ae4391de',
        },
        {
          name: 'CapCut',
          id: '61d53a1e085b1c2110aca4dc',
        },
        {
          name: '剪映专业版',
          id: '5fb62e0f626f5c46995dad19',
        },
        {
          id: '60efd5c6d9fc6b0793a517a8',
          name: 'CapCut专业版',
        },
        {
          name: '剪映Web',
          id: '61d53a4ae1941fa494e17c58',
        },
        {
          name: 'CapCut Web',
          id: '637f2a0cbeb618d5538eb58b',
        },
        {
          name: '醒图',
          id: '5d24305aca21d325ad6e3d4a',
        },
        {
          name: 'Mweb',
          id: '64f0101ff0490a2f131bd1ea',
        },
        {
          name: '黑罐头',
          id: '5f64845a91e28cfc0f049338',
        },
        {
          name: 'FaceU',
          id: '5d0341b425dd203a42b20b95',
        },
        {
          name: 'Faceubot',
          id: '5d7f5a1e16866b01acb0bc23',
        },
        {
          name: '有咔',
          id: '5d0341d825dd203a42b3d5a7',
        },
        {
          name: '轻颜',
          id: '5d0341c825dd203a42b306c8',
        },
        {
          name: 'CC4B Ad Cube',
          id: '62f21b1ba7084c6367e69a04',
        },
        {
          name: 'CapCut电商',
          id: '65ae37503d94b7502ec1fc48',
        },
      ],
    },
  ],
};

export const queryBussinessByID = (id?: string): Business | undefined => {
  if (id === undefined) {
    return undefined;
  }
  const findNode = (node: Business): Business | undefined => {
    if (node.id === id) {
      return { ...node, child: undefined };
    }

    if (node.child) {
      for (const child of node.child) {
        const found = findNode(child);
        if (found) {
          return {
            ...node,
            child: [found],
          };
        }
      }
    }

    return undefined; // 没找到目标节点
  };

  return findNode(BusinessTree);
};

export const joinBusinessName = (business: Business): string | undefined => {
  const currentName = business.name; // 当前层的 name
  const childName = business.child?.[0] ? joinBusinessName(business.child?.[0]) : '';
  return currentName + (childName ? ` / ${childName} ` : '');
};
