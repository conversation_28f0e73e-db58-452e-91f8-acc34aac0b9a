import { IssueEventThreadBacktrace } from '@shared/typings/slardar/crash/issueLocation';

export interface MRAttrResultWrapperError {
  message?: string;
}

export interface MRAttrResultWrapper<T> {
  success: boolean;
  value?: T;
  error?: MRAttrResultWrapperError;
}

export const isValidBackTrace = (item: IssueEventThreadBacktrace) => {
  if (item.is_sys || item.file_path.length <= 0) {
    return false;
  }
  const uselessFilePrefixs = ['/Applications/Xcode', '<compiler-generated>'];
  for (const prefix of uselessFilePrefixs) {
    if (item.file_path.startsWith(prefix)) {
      return false;
    }
  }
  return true;
};

export const cutBackTraceFilePath = (item: IssueEventThreadBacktrace) => {
  // filePath处理
  const filePath = item.file_path;
  if (filePath.startsWith('external')) {
    const filePathArr = filePath.split('/');
    const filePathArrLen = filePathArr.length;
    if (filePathArrLen <= 2) {
      item.file_path = filePath;
    } else {
      item.file_path = filePathArr.slice(2, filePathArrLen).join('/');
    }
  } else {
    const deletePrefix = ['/Pods/', '/external/', '/local_pod/'];
    for (const prefix of deletePrefix) {
      if (filePath.includes(prefix)) {
        const regex = new RegExp(`^.*?${prefix}`);
        item.file_path = filePath.replace(regex, '');
        break;
      }
    }
    const deletePrefixBefore = ['CCCreator'];
    for (const prefix of deletePrefixBefore) {
      if (filePath.includes(`/${prefix}/`)) {
        const regex = new RegExp(`^.*?/${prefix}/`);
        item.file_path = filePath.replace(regex, '');
        item.file_path = `${prefix}/${item.file_path}`;
        break;
      }
    }
    // 删除掉组件名，比如到了这里应该是LVResourcePool/cpp/resource_pool/db/sqlite_impl/sqlite_database.cpp
    // 删除之后是cpp/resource_pool/db/sqlite_impl/sqlite_database.cpp，这是为了提高匹配成功率
    // todo：这个还是个比较trick的操作，未来应该提高数据库存储数据的准确性
    const filePathArr = item.file_path.split('/');
    const filePathArrLen = filePathArr.length;
    if (filePathArrLen > 1) {
      item.file_path = filePathArr.slice(1, filePathArrLen).join('/');
    }
  }
  // method处理
  const targetName = item.unit;
  let simple_method = item.method.replace(` (in ${targetName})`, '');
  if (
    (filePath.endsWith('.h') || filePath.endsWith('.cpp') || filePath.endsWith('.c') || filePath.endsWith('.mm')) &&
    !simple_method.startsWith('+') &&
    !simple_method.startsWith('-')
  ) {
    if (!simple_method.includes('::')) {
      // 删除第一个括号及其之后的内容
      const match = simple_method.match(/^([^(]+)\(/);
      if (match && match.length > 1) {
        const temp = match[1];
        simple_method = temp;
      }
      // 保留最后一个空格之后的数据
      const lastSpaceIndex = simple_method.lastIndexOf(' ');
      if (lastSpaceIndex !== -1) {
        simple_method = simple_method.substring(lastSpaceIndex + 1);
      }
    } else {
      // 这个正则的含义是：找到第一个括号且之前的字符是::连续字符的情况，然后保留括号之前的部分
      const regex = /.*?(::\w+)\(/;
      const match = regex.exec(simple_method);
      // 保留最后一个::的那部分，比如rp::ResourcePoolImpl::ResourcePoolImpl
      // 则保留成::ResourcePoolImpl；同样是为了提高匹配成功率
      if (match && match.length > 1) {
        const temp = match[1];
        simple_method = temp;
      }
    }
  }
  if (filePath.endsWith('.swift')) {
    const regex = /\b(\w+\.\w+)\(/g;
    const match = regex.exec(simple_method);
    if (match && match.length > 1) {
      const temp = match[1];
      simple_method = temp;
    }
  }
  item.simple_method = simple_method;
};
