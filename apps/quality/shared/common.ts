import { Platform } from '@shared/typings/tea/metric';
import { Condition } from '@shared/typings/slardar/crash/trend';
import { FlexFilter } from '@shared/typings/slardar/flex/querySeries';
import { PlatformType } from '@pa/shared/dist/src/core';

export enum DeviceLevel {
  ALL,
  LOW,
  HIGH,
}

// 对齐文档 https://bytedance.larkoffice.com/sheets/shtcnmAgQGDHOtjddhjpMUXSq45?sheet=X1ThcF
export const iOSLowDevices = [
  'iphone 5s',
  'iphone 5',
  'iphone 6s',
  'iphone 6 plus',
  'iphone 6',
  'iphone 6s plus',
  'iphone 7',
  'iphone 7 plus',
  'iphone 8',
  'iphone se',
];

export const iOSHighMiddleDevices = [
  'iphonexr',
  'iphonex',
  'iphonexs max',
  'iphonexs',
  'iphone 8 plus',
  'iphone 13',
  'iphone 12',
  'iphone 11',
  'iphone 11 pro max',
  'iphone 11 pro',
  'iphone 14 pro max',
  'iphone 14 pro',
  'iphone 13 pro max',
  'iphone 13 mini',
  'iphone 13 pro',
  'iphone 14',
  'iphone 12 pro max',
  'iphone 12 mini',
  'iphone 15',
  'iphone 15 pro max',
  'iphone 12 pro',
  'iphone 15 pro',
  'iphone 15 plus',
  'iphone 14 plus',
  'iphone se 2',
  'iphone se 3',
  'iphone 16',
  'iphone 16 pro max',
  'iphone 16 pro',
  'iphone 16 plus',
];

export const lowAndroidCondition = [
  {
    dimension: 'device_model_score',
    op: 'lte',
    value: '6',
    type: 'expression',
  } as Condition,
];

export const lowAndroidFilter = [
  {
    filter_name: 'device_model_score',
    op: 'lte',
    values: ['6'],
    type: '',
  } as FlexFilter,
];

export const highAndroidCondition = [
  {
    dimension: 'device_model_score',
    op: 'gt',
    value: '6',
    type: 'expression',
  } as Condition,
];

export const highAndroidFilter = [
  {
    filter_name: 'device_model_score',
    op: 'gt',
    values: ['6'],
    type: '',
  } as FlexFilter,
];

export const lowIosCondition = [
  {
    dimension: 'device_model',
    type: 'expression',
    op: 'in',
    values: iOSLowDevices,
  } as Condition,
];

export const lowIosFilter = [
  {
    filter_name: 'device_model',
    type: '',
    op: 'in',
    values: iOSLowDevices,
  } as FlexFilter,
];

export const highIosCondition = [
  {
    dimension: 'device_model',
    type: 'expression',
    op: 'in',
    values: iOSHighMiddleDevices,
  } as Condition,
];

export const highIosFilter = [
  {
    filter_name: 'device_model',
    type: '',
    op: 'in',
    values: iOSHighMiddleDevices,
  } as FlexFilter,
];

export function platformOf(p: PlatformType): Platform {
  switch (p) {
    case PlatformType.Android:
      return Platform.Android;
    case PlatformType.iOS:
      return Platform.iOS;
    case PlatformType.PC:
      return Platform.PC;
    default: // case Platform.Multi:
      return Platform.Multi;
  }
}
