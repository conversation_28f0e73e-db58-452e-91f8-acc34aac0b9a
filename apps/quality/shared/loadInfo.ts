import { BusinessConfig } from '@shared/businessConfigModel';
import { VersionSetting, VersionStageSetting } from '@shared/appSetting';
import { VersionProcessInfo } from '@shared/releasePlatform/versionStage';
import { VersionStageCheckItem } from '@shared/releasePlatform/versionStageInfoCheckList';
import { LoginInfo } from '@pa/shared/dist/src/oauthInfo';
import { AppSetting } from '@pa/shared/dist/src/appSettings/appSettings';

export interface LoadInfo {
  appInfo?: AppSetting;
  loginInfo?: LoginInfo;
  versionInfo?: VersionSetting;
  businessList?: BusinessConfig[];
  versionProcessModel?: VersionProcessInfo;
  stageInfo?: VersionStageSetting;
  checkItem?: VersionStageCheckItem;
}
