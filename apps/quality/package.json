{"name": "quality", "version": "1.0.0", "scripts": {"dev": "MY_REGION=cn doas -p toutiao.redis.paperairplane -t zti edenx dev", "build": "edenx build", "start": "edenx start", "serve": "edenx serve", "new": "edenx new", "lint": "edenx lint --fix", "lint:error": "edenx lint --quiet", "preview": "edenx deploy --mode preview", "deploy": "edenx deploy", "upgrade": "edenx upgrade"}, "dependencies": {"@ant-design/charts": "^1.4.3", "@ant-design/icons": "^5.3.0", "@ant-design/pro-components": "~2.6.49", "@ant-design/pro-form": "^2.24.4", "@ant-design/pro-table": "3.17.2", "@ant-design/pro-utils": "2.15.18", "@antv/g2": "^4.2.10", "@bytecloud/common-lib": "^7.30.2", "@byted-service/eventbus": "^2.9.1", "@byted-service/rocketmq": "^2.0.2", "@byted/bytedmongoose": "2.2.0", "@byted/serverless-cloudevents-sdk": "2.0.5", "@byted/typegoose": "8.3.0", "@dnd-kit/core": "6.1.0", "@dnd-kit/modifiers": "7.0.0", "@dnd-kit/sortable": "8.0.0", "@dnd-kit/utilities": "3.2.2", "@douyinfe/semi-foundation": "^2.52.3", "@douyinfe/semi-icons": "^2.52.3", "@douyinfe/semi-icons-lab": "^2.52.3", "@douyinfe/semi-illustrations": "^2.52.3", "@douyinfe/semi-ui": "^2.62.1", "@dp/tea-sdk-node": "^3.0.8", "@edenx/plugin-bff": "1.59.0", "@edenx/plugin-garfish": "1.59.0", "@edenx/plugin-gulux": "1.59.0", "@edenx/runtime": "1.59.0", "@gulux-bam/data_dp_bear": "1.0.155-1681823073446", "@gulux-bam/toutiao_client_code_stake_profile": "1.0.2-1721045298298", "@gulux/application-http": "2.1.1", "@gulux/gulux": "^1.8.0", "@gulux/plugin-async-local-storage": "^1.1.2", "@gulux/plugin-rocketmq-producer": "1.1.7", "@gulux/plugin-tcc": "^1.1.5", "@js-temporal/polyfill": "^0.4.2", "@larksuiteoapi/node-sdk": "^1.23.0", "@logsdk/node-plugin-http": "^3.4.0", "@monaco-editor/react": "^4.6.0", "@pa/backend": "workspace:*", "@pa/shared": "workspace:*", "@types/jsonwebtoken": "^9.0.3", "antd": "^5.14.2", "axios": "^1.6.7", "copy-to-clipboard": "^3.3.3", "cron-parser": "^4.9.0", "crypto-js": "4.2.0", "dayjs": "^1.11.10", "exceljs": "4.4.0", "file-saver": "^2.0.5", "fp-ts": "^2.16.1", "jsonwebtoken": "^9.0.2", "lodash": "^4.17.21", "loglevel": "1.9.2", "minimist": "^1.2.7", "node-fetch": "2.7.0", "rc-segmented": "2.2.2", "react": "~18.2.0", "react-dom": "~18.2.0", "react-html-parser": "^2.0.2", "react-router-dom": "^6.26.1", "react-syntax-highlighter": "^15.5.0", "react-virtualized": "^9.22.5", "recharts": "^2.12.7", "redlock": "4.2.0", "retry": "0.13.1", "styled-components": "^3.3.3", "type-fest": "2.15.0", "uuid": "10.0.0", "zod": "^3.22.3"}, "devDependencies": {"@byted-emo/config": "workspace:*", "@byted/eslint-config-eden": "^5.0.37", "@edenx/app-tools": "1.59.0", "@edenx/tsconfig": "1.59.0", "@gulux/plugin-byted-cloudevent": "^1.1.2", "@types/crypto-js": "4.2.2", "@types/file-saver": "^2.0.7", "@types/lodash": "^4.14.202", "@types/minimist": "^1.2.2", "@types/node": "18.6.2", "@types/node-fetch": "2.6.11", "@types/papaparse": "^5.3.9", "@types/react": "~18.2.20", "@types/react-dom": "^18.2.7", "@types/react-syntax-highlighter": "^15.5.7", "@types/react-virtualized": "^9.21.30", "@types/redlock": "^4.0.7", "@types/retry": "0.12.5", "@types/uuid": "^9.0.8", "eslint": "^8.57.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.1.3", "moment": "2.29.2", "prettier": "^3.2.5", "rimraf": "^5.0.1", "ts-node": "^10.9.1", "ts-to-zod": "^3.1.3", "tsconfig-paths": "^4.2.0", "typescript": "~5.5.4"}, "engines": {"node": ">=18.20.4"}, "private": true, "packageManager": "pnpm@8.6.12"}