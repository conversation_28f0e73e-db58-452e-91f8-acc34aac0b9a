#!/bin/bash
. /etc/profile

set -e

npm install -g @ies/eden-monorepo@latest --registry https://bnpm.byted.org
rm -rf ./output || true
rm -rf ./output_resource || true

emo install
emo build quality
cd apps/quality


# Build info
# COMMIT_MSG=$(git log -1 --pretty=format:'%s' | sed 's/"/\\\\\\\"/g')
# sed -i "s@DEV_COMMIT_MESSAGE_HERE@$COMMIT_MSG@g" ./src/pages/settings/buildInfo/index.tsx
# sed -i "s@DEV_COMMIT_MESSAGE_HERE@$COMMIT_MSG@g" ./api/lambda/airplaneConfig.ts

COMMIT_HASH=$(git rev-parse HEAD)
sed -i "s@DEV_COMMIT_HASH_HERE@$COMMIT_HASH@g" ./src/pages/settings/buildInfo/index.tsx || true
sed -i "s@DEV_COMMIT_HASH_HERE@$COMMIT_HASH@g" ./api/lambda/airplaneConfig.ts || true

BUILD_TIME=$(date +"%F %T")
sed -i "s@DEV_BUILD_TIME_HERE@$BUILD_TIME@g" ./src/pages/settings/buildInfo/index.tsx || true
sed -i "s@DEV_BUILD_TIME_HERE@$BUILD_TIME@g" ./api/lambda/airplaneConfig.ts || true

rm -rf node_modules/.cache
MY_REGION=cn emo run deploy
rm -rf node_modules/.cache
MY_REGION=sg emo run deploy
cd output
emo install
cd ..
mv output ../../
mv output_resource ../../
