import { SetURLSearchParams } from 'react-router-dom';
import { VersionSetting } from '@shared/appSetting';
import { AppSetting } from '@pa/shared/dist/src/appSettings/appSettings';

export function useCustomSearchParams(
  params: [URLSearchParams, SetURLSearchParams],
  app: { info: AppSetting },
  version: { info: VersionSetting },
): [URLSearchParams, (key: string, value: string | undefined) => void] {
  const [searchParams, setSearchParams] = params;
  const setCustomSearchParam = (key: string, value: string | undefined) => {
    if (value && searchParams.get(key) !== value) {
      searchParams.set('appid', app.info.id.toString());
      searchParams.set('version', version.info.normalVersion);
      searchParams.set(key, value);
      setSearchParams(searchParams);
    }
  };
  return [searchParams, setCustomSearchParam];
}
