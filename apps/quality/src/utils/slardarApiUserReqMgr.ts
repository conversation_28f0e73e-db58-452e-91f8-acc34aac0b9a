// 使用个人token请求slardar api接口

import fetch from 'node-fetch';
import { SlardarRequest } from '@shared/typings/slardar/request';
import { SlardarResponse, SlardarResponseData } from '@shared/typings/slardar/response';
import { add_prefix, add_suffix, wait } from '@shared/utils/tools';
import { parseSlardarUrl } from '@shared/utils/slardar';
import { pipe } from 'fp-ts/function';
import { IssueEventListResult, LvIssueEventThreadBacktraceWrapper } from '@shared/typings/slardar/crash/issueLocation';
import { QUALITY_HOST_SG_HTTPS } from '@pa/shared/dist/src/appSettings/appSettings';
import { cutBackTraceFilePath, isValidBackTrace, MRAttrResultWrapper } from '@shared/mrAttr/mrAttr';
import { getJwtToken, ReqRegion } from '@shared/utils/basic_auth';

type AllowedMethods = 'GET' | 'POST';

const SlardarCNHost = 'slardar.bytedance.net';
const SlardarOverseaHost = 'slardar-us.bytedance.net';

export default class slardarApiUserReqMgr {
  private SLARDAR_RETRIES = 5 as const;

  getSlardarHost = (reqRegion: ReqRegion) => {
    switch (reqRegion) {
      case ReqRegion.CN:
        return SlardarCNHost;
      case ReqRegion.I18n:
        return SlardarOverseaHost;
      default:
        return SlardarCNHost;
    }
  };

  slardarHostToReqRegion = (host: string) => {
    if (host === SlardarCNHost) {
      return ReqRegion.CN;
    }
    if (host === SlardarOverseaHost) {
      return ReqRegion.I18n;
    }
    return ReqRegion.CN;
  };

  generateHeader = (token: string): { [k: string]: string } => ({
    ...{ 'x-jwt-token': token },
    'Content-Type': 'application/json',
  });

  generateRequestParams = (method: AllowedMethods, body: any, token: string) => ({
    method,
    body: JSON.stringify(body),
    headers: this.generateHeader(token),
  });

  // 一个桥接接口，请求海外纸飞机的获取slardar数据的接口
  async requestLvOverseasSlardar(url: string, params: any) {
    const reqUrl = `${QUALITY_HOST_SG_HTTPS}/api/quality/sg/lv_internal_slardar/get_slardar_data`;
    let headers = {};
    if (process.env.TCE_ENV !== undefined && process.env.TCE_ENV.startsWith('ppe')) {
      headers = {
        'Content-Type': 'application/json',
        'x-tt-env': process.env.TCE_ENV,
      };
    }
    const do_fetch = (): Promise<any> =>
      fetch(reqUrl, { method: 'POST', body: JSON.stringify({ url, params }), headers })
        .then(it => it.text())
        .then((t): [string, any] => {
          try {
            return JSON.parse(t);
          } catch (e) {
            throw t;
          }
        })
        .catch(async reason => {
          console.log(reason);
        });
    return await do_fetch();
  }

  // 真正请求slardar的方法
  async requestSlardar(url: string, params: any): Promise<any> {
    const do_fetch = (retry_times: number): Promise<any> =>
      fetch(url, params)
        .then(it => it.text())
        .then((t): [string, any] => {
          try {
            return JSON.parse(t);
          } catch (e) {
            throw t;
          }
        })
        .catch(async reason => {
          console.log(reason);
          if (retry_times <= 0) {
            // 重试过多 直接失败了
            throw new Error(
              `Slardar 请求异常
							（已重试 ${this.SLARDAR_RETRIES - retry_times} 次）！\n` +
                `url：${url}\n` +
                `请求体：${JSON.stringify(params)}\n` +
                `返回消息：${reason}`,
            );
          }
          await wait(Math.floor(Math.random() * 2000) + 2000);
          return await do_fetch(retry_times - 1);
        });
    return await do_fetch(this.SLARDAR_RETRIES);
  }

  // 内部请求slardar的包装接口，内部逻辑请求slardar数据的话得用这个接口
  async lvReqSlardar<T extends SlardarResponseData>(
    reqRegion: ReqRegion,
    path: string,
    method: AllowedMethods,
    requestBody?: SlardarRequest,
  ): Promise<SlardarResponse<T>> {
    const url = pipe(reqRegion, this.getSlardarHost, add_prefix('https://'), add_suffix(path));
    const params = this.generateRequestParams(method, requestBody, await getJwtToken(reqRegion));
    const resp = await this.requestSlardar(url, params);
    // 国内纸飞机->海外纸飞机->海外slardar这条链路通不了
    // const resp = await this.requestLvOverseasSlardar(url, params);
    return resp as SlardarResponse<T>;
  }

  async getEventList(url: string): Promise<SlardarResponse<IssueEventListResult> | null> {
    const parsedSlardarUrlResp = parseSlardarUrl(url);
    if (!parsedSlardarUrlResp.data) {
      return null;
    }
    const parsedSlardarUrl = parsedSlardarUrlResp.data;
    return await this.lvReqSlardar<IssueEventListResult>(
      this.slardarHostToReqRegion(parsedSlardarUrlResp.data.host),
      '/api_v2/app/crash/event/list',
      'POST',
      {
        aid: parsedSlardarUrl.aid,
        crash_time_type: parsedSlardarUrl.crash_time_type ?? 'insert_time',
        crash_type: parsedSlardarUrl.crash_type,
        start_time: parsedSlardarUrl.start_time ?? 0,
        end_time: parsedSlardarUrl.end_time ?? 0,
        event_ids: [],
        filters_conditions: parsedSlardarUrl.filters_conditions,
        granularity: parsedSlardarUrl.granularity ?? 3600,
        pgno: parsedSlardarUrl.event_index ?? 1,
        pgsz: 1,
        sdk: false,
        issue_id: parsedSlardarUrl.issue_id ?? '',
        sub_issue_id: '',
        lang: parsedSlardarUrl.lang,
        os: parsedSlardarUrl.os,
        region: parsedSlardarUrl.region,
        subregion: parsedSlardarUrl.subregion,
        token: '',
        token_type: 0,
        versions_conditions: {},
      },
    );
  }

  async getBackTraceFromIssueUrl(url: string): Promise<MRAttrResultWrapper<LvIssueEventThreadBacktraceWrapper>> {
    if (!url) {
      return { success: false, error: { message: 'url is empty' } };
    }
    const slardarResp: SlardarResponse<IssueEventListResult> | null = await this.getEventList(url);
    if (slardarResp === null) {
      return {
        success: false,
        error: { message: `getEventList error` },
      };
    }
    if (slardarResp.errno !== 200) {
      return {
        success: false,
        error: { message: `getIssueEventList error: ${slardarResp.errmsg}` },
      };
    }
    if (!slardarResp.data.result) {
      return {
        success: false,
        error: { message: `result is empty. ${JSON.stringify(slardarResp)}` },
      };
    }

    // 卡死或者崩溃线程信息
    const mainThreadBacktrace = slardarResp.data.result[0].event_detail.main_thread.backtrace;
    const mainThreadBacktraceFilterSys = mainThreadBacktrace.filter(isValidBackTrace);
    mainThreadBacktraceFilterSys.map(cutBackTraceFilePath);
    // 其他线程信息
    const otherThreads = slardarResp.data.result[0].event_detail.other_threads;
    const otherThreadsBacktrace = otherThreads.flatMap(thread => thread.backtrace.filter(isValidBackTrace));
    otherThreadsBacktrace.map(cutBackTraceFilePath);
    // 补充卡死的其他线程信息
    const obtainStackMainThreadBacktrace = otherThreads.flatMap(thread => {
      if (thread.thread_name.includes('Obtained stacks of main thread')) {
        return thread.backtrace.filter(isValidBackTrace);
      } else {
        return [];
      }
    });
    mainThreadBacktraceFilterSys.push(...obtainStackMainThreadBacktrace);
    return {
      success: true,
      value: {
        crash_time: slardarResp.data.result[0].crash_time,
        device_id: slardarResp.data.result[0].device_id,
        back_traces: mainThreadBacktraceFilterSys,
      },
    };
  }

  async getAlogInfo(url: string, deviceId: string, time: string): Promise<MRAttrResultWrapper<string[]>> {
    const parsedSlardarUrlResp = parseSlardarUrl(url);
    if (!parsedSlardarUrlResp.data) {
      return {
        success: false,
        error: { message: 'url is empty' },
      };
    }
    const parsedSlardarUrl = parsedSlardarUrlResp.data;
    const slardarResp: SlardarResponse<string[]> | null = await this.lvReqSlardar<string[]>(
      this.slardarHostToReqRegion(parsedSlardarUrlResp.data.host),
      '/api_v2/app/log/get',
      'POST',
      {
        aid: parsedSlardarUrl.aid,
        device_id: deviceId,
        limit: 200,
        log_type: 'alog',
        os: parsedSlardarUrl.os,
        region: parsedSlardarUrl.region,
        sdk: false,
        time,
      },
    );
    if (slardarResp === null) {
      return {
        success: false,
        error: { message: `getEventList error` },
      };
    }
    if (slardarResp.errno !== 200) {
      return {
        success: false,
        error: { message: `getIssueEventList error: ${slardarResp.errmsg}` },
      };
    }
    if (!slardarResp.data) {
      return {
        success: false,
        error: { message: `data is empty. ${JSON.stringify(slardarResp)}` },
      };
    }
    return {
      success: true,
      value: slardarResp.data,
    };
  }
}
