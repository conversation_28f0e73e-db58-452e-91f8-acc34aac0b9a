import { Tag } from 'antd';
import React from 'react';
import { SlardarPlatformType } from '@pa/shared/dist/src/appSettings/appSettings';
import { AlarmVersion } from '@shared/typings/tea/metric';
import { VersionType, VersionTypeName } from '@shared/utils/version_utils';
import { queryRecentVersions, queryReleaseVersions } from '@api/version';

export function VersionTypeToTag(type?: VersionType) {
  const color = !type
    ? ''
    : [VersionType.FULL, VersionType.FULL_TF, VersionType.ONLINE].includes(type!)
      ? 'green'
      : [VersionType.SMALL, VersionType.SMALL_TF].includes(type!)
        ? 'blue'
        : [VersionType.TF_GRAY, VersionType.SINGLE_GRAY, VersionType.GRAY].includes(type!)
          ? 'gray'
          : type === VersionType.UNKNOWN
            ? 'red'
            : type === VersionType.MAJOR_VERSION
              ? 'purple'
              : '';
  return color === '' ? <></> : <Tag color={color}>{VersionTypeName[type!]}</Tag>;
}

export async function getVersions(
  appId: number,
  platform?: SlardarPlatformType,
  pageNum = 1,
  pageSize = 100,
): Promise<AlarmVersion[]> {
  const result = await queryRecentVersions({
    data: {
      appId,
      platform,
      pageNum,
      pageSize,
    },
  });
  return result.data
    .map(v => ({
      version: v.version,
      versionCode: v.version_code,
      versionType: v.version_type,
      timestamp: v.timestamp,
    }))
    ?.sort((a, b) => {
      const aNum = a.versionCode?.includes('.') ? Number(a.versionCode?.replace(/\./g, '')) : Number(a.versionCode);
      const bNum = b.versionCode?.includes('.') ? Number(b.versionCode?.replace(/\./g, '')) : Number(b.versionCode);
      return bNum - aNum;
    });
}

export async function getReleaseVersions(platform?: SlardarPlatformType, aid?: string): Promise<AlarmVersion[]> {
  const resp = await queryReleaseVersions({
    params: {
      platform,
    },
    query: {
      aid,
    },
  });
  return resp.map(v => ({
    version: v.version,
    versionCode: v.version_code,
    versionType: v.version_type,
    timestamp: v.timestamp,
  }));
}
