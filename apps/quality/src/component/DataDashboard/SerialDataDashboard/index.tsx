import { useEffect, useState } from 'react';
import { Button, DatePicker, Divider, Row, Space } from 'antd';
import dayjs, { Dayjs } from 'dayjs';
import { useModel } from '@edenx/runtime/model';
import AppSettingModule from '@/model/appSettingModel';
import UserSettingModule from '@/model/userSettingModel';
import { DownloadOutlined } from '@ant-design/icons';

const { RangePicker } = DatePicker;

type RangeValue = [Dayjs | null, Dayjs | null] | null;

const SerialDataDashboard = () => {
  const [appSettingState] = useModel(AppSettingModule);
  const [userSettingState] = useModel(UserSettingModule);
  const [dates, setDates] = useState<RangeValue>(null);
  const [value, setValue] = useState<[Dayjs, Dayjs]>([dayjs().subtract(7, 'd'), dayjs()]);
  const [exportLoading, setExportLoading] = useState(false);
  useEffect(() => {}, [appSettingState.info.platform]);

  return (
    <Space direction="vertical" style={{ width: '100%' }}>
      <Space direction="horizontal">
        <RangePicker
          showTime={{ format: 'HH:mm' }}
          format="YYYY-MM-DD HH:mm"
          value={dates || value}
          defaultValue={[dayjs().subtract(7, 'd'), dayjs()]}
          onOk={val => {
            if (val && val[0] && val[1]) {
              console.log('CHANGED');
              setValue([val[0], val[1]]);
            }
          }}
          disabledDate={cur =>
            !dates || !dates[0]
              ? cur.isAfter(dayjs().endOf('d'))
              : cur.isAfter(dayjs().endOf('d')) || cur.subtract(30, 'd').startOf('d').isAfter(dates[0])
          }
          onOpenChange={isOpen => setDates(isOpen ? [null, null] : null)}
          onCalendarChange={val => setDates(val)}
        />
        <Button
          onClick={async () => {}}
          type="primary"
          shape="round"
          icon={<DownloadOutlined />}
          loading={exportLoading}
        >
          导出数据
        </Button>
      </Space>
      <Divider />
      <Row gutter={[16, 24]} />
    </Space>
  );
};

export default SerialDataDashboard;
