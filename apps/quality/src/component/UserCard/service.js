// import 'whatwg-fetch';

import { credentials, handleResponse, headers, method, queryStringify } from './utils';

export class UserInfoService {
  constructor(uriPrefix) {
    this.uriPrefix = typeof uriPrefix !== 'undefined' ? uriPrefix : '';
  }

  userInfo(req) {
    const { domain, ...rest } = req;
    const query = queryStringify(rest);
    const uri = `${domain}${this.uriPrefix}/api/material_bff/openapi/user/user_info/${query}`;
    return fetch(uri, { method, headers, credentials }).then(handleResponse);
  }
}

export const userInfoServiceClient = new UserInfoService();
