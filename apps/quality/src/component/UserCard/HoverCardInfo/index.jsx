import { userInfoServiceClient } from '@/component/UserCard/service';
import { windowOpen } from '@/component/UserCard/utils';
import { Descriptions, Spin, Tooltip, Typography } from 'antd';
import Icon from 'antd/es/icon';
import * as React from 'react';

import { get } from 'lodash';

import { IconChevronLeft, IconFeishuLogo, IconUserCardPhone, IconUserCardVideo } from '@douyinfe/semi-icons';
import Styles from './index.module.scss';

const { Text } = Typography;

const descitionMap = {
  zh: {
    message: '飞书会话',
    voiceMessage: '语音通话',
    videoMessage: '视频通话',
    tenant: '租户',
    city: '城市',
    departmentName: '部门',
    leader: '直属上级',
    more: '更多',
    seeMore: '查看更多',
  },
  en: {
    message: 'Lark Chat',
    voiceMessage: 'Voice Calls',
    videoMessage: 'Video Calls',
    tenant: 'Tenant',
    city: 'City',
    departmentName: 'Department',
    leader: 'Leader',
    more: 'More',
    seeMore: 'View More',
  },
};

const cache = new Map();
export const domainMap = {
  cn: 'https://semi.bytedance.net',
  va: 'https://semi-us.bytedance.net',
  sg: 'https://semi-sg.bytedance.net',
};
export const GET_USER_INFO = '/api/material_bff/openapi/user/user_info/';

const UserCard = props => {
  const {
    controller,
    email,
    extraBtnList,
    locale = 'zh',
    mode = 'normal',
    idc = 'cn',
    description,
    extraDescriptionList,
    stopPropagation = false,
    onFailed,
  } = props;
  const [userData, setUserData] = React.useState({});
  const [loading, setLoading] = React.useState(false);
  const [loadingError, setLoadingError] = React.useState(false);
  const [userIndex, setUserIndex] = React.useState(0);
  const [userdataList, setUserdataList] = React.useState([]);

  // compatible property
  const finalExtraDescriptionList = extraDescriptionList;
  const finalExtraBtnList = extraBtnList;

  const innerController = React.useMemo(
    () => ({
      send: true,
      voice: true,
      video: true,
      ...controller,
    }),
    [controller],
  );
  const innerDescription = React.useMemo(
    () => ({
      tenant: true,
      department: true,
      city: true,
      leader: true,
      more: true,
      ...description,
    }),
    [description],
  );

  const openMessage = openId => {
    windowOpen(`lark://applink.feishu.cn/client/chat/open?openId=${openId}`);
  };
  const openVoice = (userId, isVoice) => {
    const dateTime = new Date().getTime();
    windowOpen(
      `lark://applink.feishu.cn/client/videochat/open?source=userProfile&action=invite&targetId=${userId}&isvoicecall=${isVoice}&trackSource=docs&time=${dateTime}`,
    );
  };

  // 防止还没渲染完成组件就被销毁了的内存泄漏warning
  const isUnmountRef = React.useRef();

  const getUserInfo = async (innerEmail, currentIndex = 0) => {
    if (!isUnmountRef.current) {
      setLoading(true);
    }
    const cacheKey = `${innerEmail}-${locale}-${idc}`;
    try {
      if (!cache.get(cacheKey)) {
        const domain = domainMap[idc] || domainMap.cn;

        await userInfoServiceClient
          .userInfo({
            domain,
            email: innerEmail,
            locale,
            mode: 'all',
          })
          .then(res => {
            const resData = get(res, 'data', {});
            const resCode = get(res, 'code');
            if (resCode === 0) {
              if (!isUnmountRef.current) {
                setLoadingError(false);
              }
              cache.set(cacheKey, resData);
              return resData;
            }
            if (!isUnmountRef.current) {
              setLoadingError(true);
            }
            return {};
          })
          .catch(() => {
            if (!isUnmountRef.current) {
              setLoadingError(true);
            }
          });
      }
      const data = cache.get(cacheKey) || {};
      // 处理结果
      if (!isUnmountRef.current) {
        setUserData(data);
        setUserIndex(currentIndex);
        setUserdataList(userdataList);
        setLoading(false);
      }
      userdataList[currentIndex] = data;
    } catch (e) {
      // 处理异常
      if (onFailed) {
        onFailed();
      }
      if (!isUnmountRef.current) {
        setLoading(false);
      }
    }
  };

  React.useEffect(() => {
    isUnmountRef.current = false;
    getUserInfo(email);
    return () => {
      isUnmountRef.current = true;
    };
  }, [email, locale]);

  const $messages = descitionMap[locale];
  const dataList = React.useMemo(() => {
    const tmpList = [];
    if (userData.tenant && innerDescription.tenant) {
      tmpList.push({
        key: $messages.tenant,
        value: userData.tenant,
      });
    }
    if (userData.departmentName && innerDescription.department) {
      tmpList.push({
        key: $messages.departmentName,
        value: userData.departmentName,
      });
    }
    if (userData.city && innerDescription.city) {
      tmpList.push({
        key: $messages.city,
        value: userData.city,
      });
    }
    if (userData.leader_name && innerDescription.leader) {
      tmpList.push({
        key: $messages.leader,
        value: (
          <Text
            style={{ fontWeight: 'normal' }}
            link={true}
            onClick={() => {
              getUserInfo(userData.leader, userIndex + 1);
            }}
          >
            {userData.leader_name}
          </Text>
        ),
      });
    }
    // 额外的参数内容
    if (finalExtraDescriptionList && finalExtraDescriptionList.length > 0) {
      tmpList.push(...finalExtraDescriptionList);
    }
    if (innerDescription.more) {
      tmpList.push({
        key: $messages.more,
        value: (
          <Text
            style={{ fontWeight: 'normal' }}
            link={{
              href: `https://people.bytedance.net/user/${userData.email}`,
              rel: 'noopener noreferrer',
              target: '_blank',
            }}
          >
            {$messages.seeMore}
          </Text>
        ),
      });
    }

    return tmpList;
  }, [userData, locale, userIndex, innerDescription, finalExtraDescriptionList]);

  const heightMap = {
    normal: '200px',
    simple: '160px',
  };

  return (
    <div className={Styles['semi-user-card-wrap']} onClick={e => stopPropagation && e.stopPropagation()}>
      <Spin spinning={loading} wrapperClassName="semi-user-card-spin">
        <div>
          <div
            className={Styles['semi-user-card-profile']}
            style={{
              backgroundImage: userData.avatar_url ? `url("${userData.avatar_url}")` : undefined,
              height: heightMap[mode],
            }}
          >
            <div
              className={Styles['semi-user-card-back']}
              style={{
                display: userIndex === 0 ? 'none' : 'inline-flex',
              }}
              onClick={e => {
                setUserIndex(userIndex - 1);
                setUserData(userdataList[userIndex - 1] || {});
                e.stopPropagation();
              }}
            >
              <IconChevronLeft />
            </div>
            <div className={Styles['semi-user-card-profile-information']}>
              <div className={Styles['profile-information-name']}>
                {userData.name}
                {userData.user_status === 1 && <span className={Styles['profile-information-name-tag']}> 停用 </span>}
              </div>
              <div className={Styles['profile-information-email']}>{userData.email}</div>
              {userData.user_status !== 1 && (
                <div className={Styles['profile-information-icon-wrap']}>
                  {innerController.send && (
                    <Tooltip content={$messages.message}>
                      <span className={Styles['icon-item']} onClick={() => openMessage(userData.open_id)}>
                        <IconFeishuLogo />
                      </span>
                    </Tooltip>
                  )}
                  {innerController.voice && (
                    <Tooltip content={$messages.voiceMessage}>
                      <span className={Styles['icon-item']} onClick={() => openVoice(userData.user_id, true)}>
                        <IconUserCardPhone />
                      </span>
                    </Tooltip>
                  )}
                  {innerController.video && (
                    <Tooltip content={$messages.videoMessage}>
                      <span className={Styles['icon-item']} onClick={() => openVoice(userData.user_id, false)}>
                        <IconUserCardVideo />
                      </span>
                    </Tooltip>
                  )}
                  {(finalExtraBtnList || []).map(extraBtn => {
                    let { icon } = extraBtn;
                    if (typeof icon === 'string') {
                      icon = <Icon type={extraBtn.icon} />;
                    }
                    return (
                      <Tooltip content={extraBtn.content} key={extraBtn.key}>
                        <span
                          className={Styles['icon-item']}
                          onClick={() => extraBtn.onClick && extraBtn.onClick(userData)}
                        >
                          {icon}
                        </span>
                      </Tooltip>
                    );
                  })}
                </div>
              )}
            </div>
          </div>
          {mode === 'normal' && userData.user_status !== 1 && (
            <div className={Styles['semi-user-card-information-list']}>
              <Descriptions align="left" data={dataList} />
            </div>
          )}
          {loadingError && (
            <div className={loadingError ? Styles['semi-user-card-error'] : ''}>
              名片加载失败
              <Text
                link={true}
                onClick={() => {
                  getUserInfo(email);
                }}
                style={{ marginLeft: 8 }}
              >
                刷新
              </Text>
            </div>
          )}
        </div>
      </Spin>
    </div>
  );
};

export default UserCard;
