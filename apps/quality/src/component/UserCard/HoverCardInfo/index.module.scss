.semi-user-card-wrap {
	position: relative;
	width: 280px;
	height: 100%;
	overflow: hidden;
}

.semi-user-card-error {
	background: #f9f9f9;
	opacity: 0.95;
	display: flex;
	align-items: center;
	justify-content: center;
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
}

.semi-user-card-profile {
	position: relative;
	width: 100%;
	height: 200px;
	background-repeat: no-repeat;
	background-size: cover;
	background-position: center;
}

.semi-user-card-back {
	position: absolute;
	top: 8px;
	left: 8px;
	height: 16px;
	width: 16px;
	display: inline-flex;
	justify-content: center;
	align-items: center;
	cursor: pointer;
	background: rgba(46, 50, 56, 0.05);
	border-radius: 3px;
	color: #fff;
}

.semi-user-card-profile-information {
	position: absolute;
	width: 100%;
	height: 140px;
	left: 0;
	bottom: 0;
	background: linear-gradient(180deg, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0.48) 100%);
	padding: 24px 16px 16px;
	box-sizing: border-box;
	display: flex;
	flex-direction: column;
	justify-content: flex-end;
}

.profile-information-name {
	display: flex;
	align-items: center;
	justify-content: flex-start;
	font-weight: 600;
	font-size: 24px;
	line-height: 32px;
	color: #fff;
	height: 32px;
}

.profile-information-name-tag {
	margin-left: 8px;
	color: rgba(28, 31, 35, 0.8);
	background: #e6e8ea;

	border-radius: 3px;
	padding: 2px 8px;
	font-size: 12px;
	line-height: 16px;
}

.profile-information-email {
	font-size: 14px;
	line-height: 20px;
	color: #fff;
}

.profile-information-icon-wrap {
	margin-top: 8px;
	display: flex;
}

.icon-item {
	width: 28px;
	height: 28px;
	background-color: rgba(255, 255, 255, 0.4);
	display: flex;
	align-items: center;
	justify-content: center;
	border-radius: 50%;
	margin-right: 12px;
	color: #fff;
	cursor: pointer;

	&:hover {
		background-color: rgba(255, 255, 255, 0.5);
	}

	&:active {
		background-color: rgba(255, 255, 255, 0.6);
	}

	&:last-child {
		margin-right: 0;
	}
}

.semi-user-card-information-list {
	// display:;
	padding: 16px;
	max-height: 420px;
	overflow: auto;
}

:global {
	.semi-user-card-spin {
		> .semi-spin-children {
			opacity: 0;
		}
	}

	.semi-spin-hidden {
		> .semi-spin-children {
			opacity: 1;
		}
	}
}
