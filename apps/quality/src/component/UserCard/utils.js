export const headers = { 'Content-Type': 'application/json' };
export const method = 'GET';
export const credentials = 'same-origin';

export function queryStringify(params, inBody) {
  const keys = [];

  function itemStringify(obj, prefix) {
    const type = Object.prototype.toString.call(obj);
    if (type === '[object Array]') {
      obj.forEach((item, key) => {
        itemStringify(item, `${prefix}[${key}]`);
      });
    } else if (type === '[object Object]') {
      for (const key in obj) {
        itemStringify(obj[key], `${prefix}[${key}]`);
      }
    } else if (type === '[object Date]') {
      keys.push(`${prefix}=${obj.toISOString()}`);
    } else if (type === '[object Null]') {
      keys.push(`${prefix}=`);
    } else if (type !== '[object Undefined]') {
      keys.push(`${prefix}=${encodeURIComponent(obj)}`);
    }
  }

  for (const k in params) {
    itemStringify(params[k], k);
  }

  const str = keys.join('&');
  return str && !inBody ? `?${str}` : str;
}

export function handleResponse(response) {
  if (response && response.status >= 200 && response.status < 300) {
    return response.json ? response.json() : response.text();
  } else {
    const error = new Error(response.statusText);
    error.response = response;
    throw error;
  }
}

export function windowOpen(url, newW) {
  if (newW) {
    return window.open(url);
  }
  let iframe = document.getElementById('_WINDOW_OPEN_LARK_');
  if (iframe) {
    iframe.src = url;
  } else {
    iframe = document.createElement('iframe');
    iframe.style.display = 'none';
    iframe.src = url;
    iframe.id = '_WINDOW_OPEN_LARK_';
    document.body.appendChild(iframe);
  }
  return null;
}
