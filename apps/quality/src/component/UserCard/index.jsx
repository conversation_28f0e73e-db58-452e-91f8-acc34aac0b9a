import HoverCardInfo from '@/component/UserCard/HoverCardInfo';
import { Avatar, Popover } from 'antd';
import * as React from 'react';

import Styles from './index.module.scss';

const SemiReactUserCard = ({
  simpleUserData,
  triggerType = 'click',
  popoverChildrenStyle = {},
  hideName = false,
  hideAvatar = false,
  ...rest
}) => {
  const { email } = rest;

  const renderItem = () => (
    <span className={Styles['semi-user-popover-children']} style={popoverChildrenStyle}>
      {!hideAvatar ? (
        simpleUserData?.avatarUrl ? (
          <Avatar
            style={{ marginRight: 4, ...simpleUserData?.style }}
            size="extra-small"
            src={simpleUserData.avatarUrl}
          />
        ) : (
          <Avatar
            style={{ marginRight: 4, ...simpleUserData?.style }}
            size="extra-small"
            shape="square"
            src="https://tosv.byted.org/obj/eden-internal/lm_wl_hxzlp/ljhwZthlaukjlkulzlp/epComponents/defaultAvatar.svg"
          />
        )
      ) : null}
      {!hideName && <span>{simpleUserData?.name || email}</span>}
    </span>
  );

  return email ? (
    <Popover
      style={{
        background: 'transparent',
        width: '280px',
        height: 'fit-content',
      }}
      trigger={triggerType}
      content={<HoverCardInfo {...rest} />}
      position="rightTop"
      {...rest}
    >
      {renderItem()}
    </Popover>
  ) : null;
};

export default SemiReactUserCard;
