import React from 'react';
import { User } from '@pa/shared/dist/src/core';
import { searchUser } from '@api/index';
import { ProFormSelect, ProFormSelectProps } from '@ant-design/pro-components';
import { Avatar, Image, Space } from 'antd';
import UserSettingModule from '@/model/userSettingModel';
import { useModel } from '@edenx/runtime/model';

const ProFormUserSelector: React.FC<ProFormSelectProps> = props => {
  const [userSettingState, userSettingActions] = useModel(UserSettingModule);
  let timeout: ReturnType<typeof setTimeout> | null,
    lastRequest: (value: User[] | PromiseLike<User[] | undefined> | undefined) => void;

  const search = (keyWord: string) =>
    new Promise<User[] | undefined>((resolve, reject) => {
      console.log(keyWord);
      if (timeout !== null) {
        clearTimeout(timeout);
      }
      if (lastRequest) {
        lastRequest(Promise.resolve([]));
      }
      lastRequest = resolve;
      timeout = setTimeout(() => {
        searchUser({ data: { value: keyWord, token: userSettingState.info.access_token } }).then(ret => {
          if (ret) {
            resolve(Promise.resolve(ret.data));
          }
        });
      }, 300);
    });
  return (
    <ProFormSelect.SearchSelect
      {...props}
      showSearch
      request={async ({ keyWords }) => {
        console.log(keyWords);
        if (!keyWords || keyWords === '') {
          return [];
        }
        const result = await search(keyWords);
        console.log(result);
        return result
          ? result.map(value => ({
              label: (
                <Space>
                  {value.avatar ? (
                    <Avatar size={18} src={<Image preview={false} src={(value.avatar as any).avatar_72} />} />
                  ) : (
                    <></>
                  )}
                  <span>{value.name}</span>
                </Space>
              ),
              value: value.open_id,
            }))
          : [];
      }}
      fieldProps={{
        mode: 'multiple',
        filterOption: false,
        resetAfterSelect: false,
      }}
    />
  );
};

export default ProFormUserSelector;
