import React, { useContext, useEffect, useMemo, useState } from 'react';
import { DBTeaMetric } from '../../../api/model/TeaMetricTable';
import { DBTeaMetricValue } from '../../../api/model/TeaMetricValueTable';
import {
  getTeaMetricExportIndex,
  getTeaSerialMetric,
  queryTeaOverallTableValues,
  saveTeaMetricExportIndex,
} from '@api/tea';
import dayjs from 'dayjs';
import { Business, BusinessName, TeaGranularity, TeaValueType } from '@shared/typings/tea/metric';
import { fromPairs, uniq } from 'lodash';
import { Button, Table, TableColumnsType, TablePaginationConfig } from 'antd';
import { generate_sorter, get_enum_values, invoke_and_return, to_platform } from '@shared/utils/tools';
import { ColumnType, type FilterValue } from 'antd/es/table/interface';
import DataTableFormat from '@/component/DataTableFormat';
import { useModel } from '@edenx/runtime/model';
import AppSettingModule from '@/model/appSettingModel';
import type { SyntheticListenerMap } from '@dnd-kit/core/dist/hooks/utilities';
import { HolderOutlined } from '@ant-design/icons';
import { arrayMove, SortableContext, useSortable, verticalListSortingStrategy } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { DndContext, DragEndEvent } from '@dnd-kit/core';
import { restrictToVerticalAxis } from '@dnd-kit/modifiers';
import { useLocalStorage } from '@/pages/quality/metric/version/utils/StoragesUtils';

export const generate_timestamp_str = (t: number, g: TeaGranularity) => {
  const d = dayjs.unix(t).add(8, 'hour');
  if (g === TeaGranularity.DAY_7) {
    const de = d.add(6, 'd');
    return `${d.format('MM-DD')} ~ ${de.format('MM-DD')}`;
  } else if (g === TeaGranularity.DAY_14) {
    const de = d.add(13, 'd');
    return `${d.format('MM-DD')} ~ ${de.format('MM-DD')}`;
  }
  return d.format('MM-DD');
};

interface TeaOverallRow {
  business: Business;
  businessModule?: string;
  displayName: string;
  id: string;
  type: TeaValueType;
  values: Record<number, number>;
}

interface RowContextProps {
  setActivatorNodeRef?: (element: HTMLElement | null) => void;
  listeners?: SyntheticListenerMap;
}

const RowContext = React.createContext<RowContextProps>({});

const DragHandle: React.FC = () => {
  const { setActivatorNodeRef, listeners } = useContext(RowContext);
  return (
    <Button
      type="text"
      size="small"
      icon={<HolderOutlined />}
      style={{ cursor: 'move' }}
      ref={setActivatorNodeRef}
      {...listeners}
    />
  );
};

interface RowProps extends React.HTMLAttributes<HTMLTableRowElement> {
  'data-row-key': string;
}

const Row: React.FC<RowProps> = props => {
  const { attributes, listeners, setNodeRef, setActivatorNodeRef, transform, transition, isDragging } = useSortable({
    id: props['data-row-key'],
  });

  const style: React.CSSProperties = {
    ...props.style,
    transform: CSS.Translate.toString(transform),
    transition,
    ...(isDragging ? { position: 'relative', zIndex: 9999 } : {}),
  };

  const contextValue = useMemo<RowContextProps>(
    () => ({ setActivatorNodeRef, listeners }),
    [setActivatorNodeRef, listeners],
  );

  return (
    <RowContext.Provider value={contextValue}>
      <tr {...props} ref={setNodeRef} style={style} {...attributes} />
    </RowContext.Provider>
  );
};

export interface TeaOverallTableProps {
  timestamp: number;
  granularity: TeaGranularity;
  isAllowSort?: boolean;
  isShowBusinessModule?: boolean;
  business?: Business;
  appId?: number;
}

interface Rule {
  id: string;
  index?: number;
}

const sortData = (data: TeaOverallRow[], rules: Rule[]): TeaOverallRow[] => {
  // 创建一个规则映射表，便于快速查找
  const ruleMap = new Map<string, number>();
  rules.forEach(rule => {
    if (rule.index !== undefined) {
      ruleMap.set(rule.id, rule.index);
    }
  });

  return data.sort((a, b) => {
    const aIndex = ruleMap.get(a.id);
    const bIndex = ruleMap.get(b.id);

    if (aIndex !== undefined && bIndex !== undefined) {
      return aIndex - bIndex; // 如果都存在 index，按照 index 排序
    } else if (aIndex !== undefined) {
      return -1; // 如果只有 a 存在 index，a 排在前
    } else if (bIndex !== undefined) {
      return 1; // 如果只有 b 存在 index，b 排在前
    } else {
      return BusinessName[a.business].localeCompare(BusinessName[b.business]); // 否则按照 business 的字母序排序
    }
  });
};

function transformRow<T>(r: T, rows: T[], canMerge: (cur: T, compare: T) => boolean, index: number) {
  const prevIndex = index - 1;
  if (prevIndex >= 0 && canMerge(r, rows[prevIndex])) {
    return {
      rowSpan: 0,
    };
  }
  let count = 1;
  for (let i = index + 1; i < rows.length; ++i) {
    if (canMerge(r, rows[i])) {
      count += 1;
      continue;
    }
    break;
  }
  return {
    rowSpan: count,
  };
}

const TeaOverallTable: React.FC<TeaOverallTableProps> = ({
  timestamp,
  granularity,
  isAllowSort,
  isShowBusinessModule,
  appId,
}) => {
  const [metrics, setMetrics] = useState<DBTeaMetric[]>([]);
  const [values, setValues] = useState<DBTeaMetricValue[]>([]);
  const [loading, setLoading] = useState(true);

  const [rows, setRows] = useState<TeaOverallRow[]>([]);

  const [{ info }] = useModel(AppSettingModule);
  const [businessFilters, setBusinessFilters] = useLocalStorage<string[] | undefined>(
    `${appId}_TeaOverallTable_Business`,
  );

  const onDragEnd = ({ active, over }: DragEndEvent) => {
    if (active.id !== over?.id) {
      setRows(prevState => {
        const activeIndex = prevState.findIndex(record => record.id === active?.id);
        const overIndex = prevState.findIndex(record => record.id === over?.id);
        return arrayMove(prevState, activeIndex, overIndex);
      });
    }
  };

  useEffect(() => {
    if (isAllowSort) {
      const configs = rows.map((row, index) => ({
        name: row.id,
        platform: to_platform(info.platform),
        index,
      }));
      saveTeaMetricExportIndex({
        data: {
          configs,
        },
      }).then();
    }
  }, [rows]);

  const update = async () => {
    setLoading(true);
    await Promise.all([
      getTeaSerialMetric({
        data: {
          platform: to_platform(info.platform),
        },
      }).then(invoke_and_return<DBTeaMetric[]>(setMetrics)),
      queryTeaOverallTableValues({
        params: {
          timestamp: timestamp.toString(),
        },
        data: {
          granularity,
          platform: to_platform(info.platform),
        },
      })
        .then(it => it.data?.values ?? [])
        .then(invoke_and_return<DBTeaMetricValue[]>(setValues)),
      getTeaMetricExportIndex({ data: { platform: to_platform(info.platform) } }).then(
        rsp =>
          rsp?.data?.map(it => ({
            id: it.name,
            index: it.index,
          })) ?? [],
      ),
    ]).then(([m, v, rules]) => {
      const x = m.map(
        (it): TeaOverallRow => ({
          business: it.Business,
          displayName: it.DisplayName,
          businessModule: it.BusinessModule,
          id: it.Name,
          type: it.ValueType,
          values: fromPairs(v.filter(cur => cur.metricId === it.Name).map(cur => [cur.timestamp, Number(cur.value)])),
        }),
      );
      // setRows(x.sort(generate_sorter("business", "displayName")));
      setRows(sortData(x, rules));
    });
    setLoading(false);
  };

  useEffect(() => {
    update();
  }, [info.platform]);

  const timestamps = uniq(values.map(it => it.timestamp))
    .sort((a, b) => b - a)
    .map((it): [number, string] => [it, generate_timestamp_str(it, granularity)]);

  const columns: TableColumnsType<TeaOverallRow> = [
    { key: 'sort', width: 10, hidden: !isAllowSort, render: () => <DragHandle /> },
    {
      title: '业务线',
      dataIndex: 'business',
      width: 150,
      filteredValue: businessFilters,
      filters: get_enum_values(Business).map(it => ({
        text: BusinessName[it],
        value: it,
      })),
      onFilter: (filterBusiness, config) => config.business === filterBusiness,
      render: (_, r) => <>{BusinessName[r.business]}</>,
      onCell: (r, idx) => {
        function isFilter(business: Business) {
          return (
            businessFilters === undefined ||
            businessFilters.length === 0 ||
            businessFilters?.includes(business.toString())
          );
        }

        return transformRow(
          r,
          rows,
          (current, compare) =>
            isFilter(current.business) && isFilter(compare.business) && current.business === compare.business,
          rows?.findIndex(row => row.id === r.id),
        );
      },
      fixed: 'left',
    },
    {
      title: '二级模块',
      dataIndex: 'businessModule',
      width: 200,
      hidden: !isShowBusinessModule,
      render: (businessModule, r) => <>{businessModule}</>,
      onCell: (r, idx) => {
        function isFilter(business: Business) {
          return (
            businessFilters === undefined ||
            businessFilters.length === 0 ||
            businessFilters?.includes(business.toString())
          );
        }

        function canMerge(current: TeaOverallRow, compare: TeaOverallRow) {
          return (
            isFilter(current.business) &&
            isFilter(compare.business) &&
            current.business === compare.business &&
            r.businessModule === compare.businessModule
          );
        }

        return transformRow(
          r,
          rows,
          canMerge,
          rows?.findIndex(row => row.id === r.id),
        );
      },
      fixed: 'left',
    },
    {
      title: '指标名',
      dataIndex: 'displayName',
      width: 250,
      fixed: 'left',
    },
    ...timestamps.map(
      ([ts, name]): ColumnType<TeaOverallRow> => ({
        title: name,
        dataIndex: ts,
        width: 140,
        render: (_, r) => (
          <>
            <DataTableFormat cur={r.values[ts]} cmp={r.values[ts - granularity * 60]} type={r.type} />
          </>
        ),
      }),
    ),
  ];

  const handleTableChange = (pagination: TablePaginationConfig, filters: Record<string, FilterValue | null>) => {
    setBusinessFilters(filters.business ? filters.business.map(it => it.toString()) : []);
  };

  return (
    <>
      <DndContext modifiers={[restrictToVerticalAxis]} onDragEnd={onDragEnd}>
        <SortableContext items={rows.map(i => i.id)} strategy={verticalListSortingStrategy}>
          <Table<TeaOverallRow>
            onChange={handleTableChange}
            bordered
            rowKey="id"
            columns={columns}
            components={{ body: { row: Row } }}
            dataSource={rows}
            loading={loading}
            pagination={false}
            style={{ width: '100%' }}
            scroll={{
              x: 'max-content',
            }}
          />
        </SortableContext>
      </DndContext>
    </>
  );
};

export default TeaOverallTable;
