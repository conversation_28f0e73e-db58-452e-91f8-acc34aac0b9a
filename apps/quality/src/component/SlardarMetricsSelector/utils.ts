import { FlexMeasureCategoryMetaItem, FlexMeasureMetaItem } from '@shared/typings/slardar/flex/meta';
import TreeValue from '@/component/treeValue';

function buildTree(r: FlexMeasureMetaItem[]): TreeValue[] {
  return r
    .filter(c => c.measure_meta_list || c.measure_name)
    .map(c =>
      c.measure_meta_list
        ? {
            value: `${c.key}${c.label}`,
            title: c.label!,
            selectable: false,
            children: buildTree(c.measure_meta_list),
          }
        : {
            value: c.measure_name!,
            title: c.label!,
            selectable: true,
            children: [],
          },
    );
}

export function slardarRespToTree(r: FlexMeasureCategoryMetaItem[]): TreeValue[] {
  return r
    .filter(c => c.measure_meta_list)
    .map(c => ({
      value: `${c.key}${c.label}`,
      title: c.label!,
      selectable: false,
      children: buildTree(c.measure_meta_list!),
    }));
}
