import React from 'react';
import TreeValue from '@/component/treeValue';
import { TreeSelect } from 'antd';

export interface SlardarMetricsSelectorProps {
  data: TreeValue[];
  onChange: (value: string[], label: React.ReactNode[], extra: any) => void;
  value: string[];
}

const SlardarMetricsSelector: React.FC<SlardarMetricsSelectorProps> = ({ data, onChange, value }) => (
  <TreeSelect
    showSearch
    placeholder={'请选择指标 ……'}
    onChange={onChange}
    treeData={data}
    treeLine
    style={{ width: '100%' }}
    value={value}
  />
);

export default SlardarMetricsSelector;
