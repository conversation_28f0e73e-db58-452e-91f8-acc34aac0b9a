import { Business, BusinessName, TeaValueType } from '@shared/typings/tea/metric';
import React, { useEffect, useState } from 'react';
import { DBTeaMetric } from '../../../api/model/TeaMetricTable';
import { getTeaGrayMetricValue, getTeaVersionedMetric } from '@api/tea';
import { DBTeaMetricValue } from '../../../api/model/TeaMetricValueTable';
import { Table, TableColumnsType } from 'antd';
import { ElementOf, generate_sorter, invoke_and_return, to_platform } from '@shared/utils/tools';
import DataTableFormat from '@/component/DataTableFormat';
import { useModel } from '@edenx/runtime/model';
import AppSettingModule from '@/model/appSettingModel';

export interface GrayTeaTableProps {
  versions: string[];
  // granularity: TeaGranularity;
  // now: number; // unix timestamp
}

interface GrayTeaRow {
  business: Business;
  displayName: string;
  id: string;
  type: TeaValueType;
  values: [string, number][];
}

const TeaGrayTable: React.FC<GrayTeaTableProps> = ({ versions }) => {
  const [metrics, setMetrics] = useState<DBTeaMetric[]>([]);
  const [values, setValues] = useState<DBTeaMetricValue[]>([]);
  const [loading, setLoading] = useState(false);
  const [rows, setRows] = useState<GrayTeaRow[]>([]);

  const [{ info }] = useModel(AppSettingModule);

  const update = async () => {
    setLoading(true);
    await Promise.all([
      getTeaVersionedMetric({
        data: {
          platform: to_platform(info.platform),
        },
      }).then(invoke_and_return<DBTeaMetric[]>(setMetrics)),
      getTeaGrayMetricValue({
        data: {
          versions,
        },
      }).then(invoke_and_return<DBTeaMetricValue[]>(setValues)),
    ]).then(([m, v]) => {
      const x = m.map(
        (it): GrayTeaRow => ({
          business: it.Business,
          displayName: it.DisplayName,
          id: it.Name,
          type: it.ValueType,
          values: v
            .filter(val => val.metricId === it.Name)
            .map(val => [val.versionCode, Number(val.value)] as [string, number]),
        }),
      );
      setRows(x.sort(generate_sorter('business', 'displayName')));
    });
    setLoading(false);
  };

  useEffect(() => {
    update();
  }, [versions]);

  const columns: TableColumnsType<GrayTeaRow> = [
    {
      title: '业务线',
      dataIndex: 'business',
      render: (_, r) => <>{BusinessName[r.business]}</>,
      onCell: (r, idx) => {
        const f = rows.findIndex(it => it.business === r.business);
        const nf = rows.findIndex(it => it.business > r.business);
        return {
          rowSpan: idx === f ? (nf > 0 ? nf : rows.length) - f : 0,
        };
      },
      width: '5%',
      fixed: 'left',
    },
    {
      title: '指标名',
      dataIndex: 'displayName',
      fixed: 'left',
    },
    ...versions.map((ver): ElementOf<TableColumnsType<GrayTeaRow>> => {
      const cmp_ver = versions.at(versions.findIndex(it => it === ver) + 1);
      return {
        title: ver,
        dataIndex: ver,
        render: (_, r) => (
          <DataTableFormat
            type={r.type}
            cur={r.values.find(([it]) => it === ver)?.[1]}
            cmp={r.values.find(([it]) => it === cmp_ver)?.[1]}
          />
        ),
      };
    }),
  ];

  return (
    <Table<GrayTeaRow>
      bordered
      columns={columns}
      dataSource={rows}
      loading={loading}
      pagination={false}
      style={{ width: '90%' }}
    />
  );
};

export default TeaGrayTable;
