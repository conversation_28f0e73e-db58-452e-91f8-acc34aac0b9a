import React, { useState } from 'react';
import { FormInstance, ProFormSelect, ProFormSelectProps } from '@ant-design/pro-components';
import { RequestOptionsType } from '@ant-design/pro-utils/es/typing';

export interface ProFormSelectorProps<T> extends ProFormSelectProps {
  mode?: 'multiple' | 'tags' | undefined; // 允许外部传入的mode属性
  form: FormInstance;
  buildSelectValue: (data: T) => RequestOptionsType;
  search: (keyWord: string) => Promise<T[]>;
}

const ProFormSelector = <T,>(props: ProFormSelectorProps<T>) => {
  const [lastSearchData, setLastSearchData] = useState<T[]>([]);
  const [lastKeyWords, setLastKeyWords] = useState<string>();

  return (
    <ProFormSelect.SearchSelect
      {...props}
      showSearch
      debounceTime={300}
      request={async ({ keyWords }) => {
        let result;
        if (!keyWords || keyWords === '' || keyWords === lastKeyWords) {
          result = lastSearchData;
        } else {
          result = await props.search(keyWords);
          setLastSearchData(result);
          setLastKeyWords(keyWords);
        }
        console.log('search');
        return result ? result.map(value => props.buildSelectValue(value)) : [];
      }}
      fieldProps={{
        mode: props.mode,
        filterOption: false,
        resetAfterSelect: false,
      }}
    />
  );
};

export default ProFormSelector;
