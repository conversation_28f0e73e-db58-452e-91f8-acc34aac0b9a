import React, { ReactNode, useEffect, useRef, useState } from 'react';

export interface LazyLoadingProps {
  dataLoadTrigger: () => Promise<void>;
  threshold?: number;
  children: ReactNode;
}

const LazyLoading: React.FC<LazyLoadingProps> = ({ dataLoadTrigger, threshold, children }) => {
  // should data be loaded now
  const [shouldLoadData, setShouldLoadData] = useState(false);

  useEffect(() => {
    if (shouldLoadData) {
      dataLoadTrigger().then(() => setShouldLoadData(false));
    }
  }, [shouldLoadData]);

  // ----- START USED BY LAZY LOADING -----

  const containerRef = useRef<any>(null);
  const [isVisibleBefore, setIsVisibleBefore] = useState(false);

  const callbackFunc = (entries: any) => {
    const [entry] = entries;
    if (!isVisibleBefore && entry.isIntersecting) {
      setShouldLoadData(true);
    }
    setIsVisibleBefore(isVisibleBefore || entry.isIntersecting);
  };

  const options = {
    root: null,
    rootMargin: '0px',
    threshold: threshold ?? 0.5,
  };

  useEffect(() => {
    const observer = new IntersectionObserver(callbackFunc, options);
    if (containerRef.current) {
      observer.observe(containerRef.current);
    }
    return () => {
      if (containerRef.current) {
        observer.unobserve(containerRef.current);
      }
    };
  }, [containerRef, options]);

  // ----- END USED BY LAZY LOADING -----
  return <div ref={containerRef}>{children}</div>;
};

export default LazyLoading;
