import React from 'react';
import { Typography } from 'antd';
import { TeaValueType, TeaValueUnit } from '@shared/typings/tea/metric';

const { Text } = Typography;

export interface DataTableFormatProps {
  cur?: number;
  cmp?: number;
  type: TeaValueType;
}

function fixFormat(number: number) {
  if (number === undefined) {
    return -1;
  }
  // Convert the number to a string with sufficient precision
  const numberStr = number.toString();

  // Check if the number contains a decimal point
  const decimalIndex = numberStr.indexOf('.');
  if (decimalIndex === -1) {
    return -1; // No decimal point found
  }

  for (let i = 0; i < decimalIndex; i++) {
    if (numberStr[i] !== '0') {
      return 1; // Position relative to the decimal point
    }
  }

  // Find the first non-zero digit after the decimal point
  for (let i = decimalIndex + 1; i < numberStr.length; i++) {
    if (numberStr[i] !== '0') {
      return i - decimalIndex; // Position relative to the decimal point
    }
  }

  return -1; // All digits after the decimal are zero
}

// cmp是上一次周期
const DataTableFormat: React.FC<DataTableFormatProps> = ({ cur, cmp, type }) => {
  if (!cur) {
    return <>-</>;
  }
  const value = cur * ([TeaValueType.SUCCESS_RATE, TeaValueType.FAILURE_RATE].includes(type) ? 100 : 1);
  const cur_str = value.toFixed(fixFormat(value) + 1) + TeaValueUnit[type];
  if (!cmp) {
    return <>{cur_str}</>;
  }
  const is_up = cur > cmp;
  const is_better = [TeaValueType.ELAPSED_TIME, TeaValueType.FAILURE_RATE].includes(type) ? !is_up : is_up;
  const delta_abs = Math.abs(cur / cmp - 1) * 100; // (cur - cmp / cmp)
  return (
    <>
      {cur_str}
      {' ('}
      <Text type={is_better ? 'success' : 'danger'}>
        {is_up ? '↗' : '↘'}
        {delta_abs.toFixed(2)}%
      </Text>
      {')'}
    </>
  );
};

export default DataTableFormat;
