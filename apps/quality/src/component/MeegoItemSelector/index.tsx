import { searchMeego } from '@api/index';
import React, { useEffect, useImperativeHandle, useState } from 'react';
import { Select, Space, Tag } from '@douyinfe/semi-ui';
import { WorkItemInfo } from '@shared/typings/meego';
import { debounce } from 'lodash';
import { ValidateStatus } from '@douyinfe/semi-ui/lib/es/_base/baseComponent';

const { Option } = Select;

// meegoList2以及selected2是用来控制显示的列表和选中的元素
interface MeegoItemSelectorProps {
  validateStatus?: ValidateStatus;
  placeholder: string;
  meegoSpace: string;
  onSelect: (value: WorkItemInfo) => void;
  initValue?: WorkItemInfo; // 新增可选初始值参数
  meegoList2?: WorkItemInfo[];
  selected2?: number | undefined;
  onMeegoList2Change?: (list: WorkItemInfo[]) => void;
  onSelected2Change?: (sel: number | undefined) => void;
}

export interface MeegoItemSelectorRef {
  clearValue: () => void;
}
// meegoList2和select2是外部传输进来的列表和命中，用来控制列表和选中
const MeegoItemSelector: React.ForwardRefRenderFunction<MeegoItemSelectorRef, MeegoItemSelectorProps> = (
  {
    validateStatus,
    onSelect,
    placeholder,
    meegoSpace,
    initValue,
    meegoList2,
    selected2,
    onMeegoList2Change,
    onSelected2Change,
  },
  ref,
) => {
  const [meegoList, setMeegoList] = useState<WorkItemInfo[]>([]);
  const [selected, setSelected] = useState<number>();
  const [loading, setLoading] = useState<boolean>(false);

  // 只要meegoList2/selected2未准备好，loading为true
  useEffect(() => {
    if (!meegoList2 || typeof selected2 === 'undefined' || meegoList2.length === 0) {
    } else {
      setMeegoList(meegoList2);
      setSelected(selected2);
      setLoading(false);
    }
  }, [meegoList2, selected2]);
  // 初始化时设置初始值
  useEffect(() => {
    if (initValue) {
      onMeegoList2Change?.([initValue]);
      onSelected2Change?.(0);
      setMeegoList([initValue]);
      setSelected(0); // 设置为第一个选项
    }
  }, [initValue, onMeegoList2Change, onSelected2Change]);
  useImperativeHandle(ref, () => ({
    clearValue: () => {
      onSelected2Change?.(undefined);
      setSelected(undefined);
    },
  }));

  const handleSearch = (value: string) => {
    if (value === '') {
      return;
    }
    setLoading(true);
    searchMeego({ data: { value, space: meegoSpace } }).then(ret => {
      if (ret.data) {
        onMeegoList2Change?.(ret.data);
        setMeegoList(ret.data);
      }
      setLoading(false);
    });
  };

  const onSelected = (value: any) => {
    const index = value as number;
    if (meegoList) {
      onSelect(meegoList[index]);
      onSelected2Change?.(index);
      setSelected(index);
    }
  };

  return (
    <Select
      filter
      remote
      style={{
        width: '100%',
      }}
      showClear={true}
      value={selected}
      validateStatus={validateStatus}
      onChange={onSelected}
      onSearch={debounce(handleSearch, 1000)}
      placeholder={placeholder}
      loading={loading}
      renderSelectedItem={(option: any) => <Tag>{option?.label}</Tag>}
    >
      {meegoList?.map((item, index) => (
        <Option key={index} value={index}>
          <Space>
            <span>{item.name}</span>
          </Space>
        </Option>
      ))}
    </Select>
  );
};

export default React.forwardRef(MeegoItemSelector);
