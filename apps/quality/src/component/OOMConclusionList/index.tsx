import { ProColumns, ProTable } from '@ant-design/pro-components';
import { getOOMIndicatorAnalyzeResult } from '@api/analyze';
import { ConclusionInfo } from '@shared/analyze/oom_indicator';
import { PlatformType } from '@pa/shared/dist/src/core';
import { CrashType } from '@shared/typings/slardar/crash/issueListSearch';
import { ReactNode, useEffect, useState } from 'react';
import parse from 'react-html-parser';

export interface OOMConclusionParams {
  target_version_code: string;
  base_version_code: string;
  platform: PlatformType;
  type: CrashType;
  is_open: boolean;
}

const OOMConclusionList: React.FC<OOMConclusionParams> = ({
  target_version_code,
  base_version_code,
  platform,
  type,
  is_open,
}) => {
  const [dataSource, setDataSource] = useState<ConclusionInfo[]>([]);
  const [loading, setLoading] = useState(false);

  const columns = (): ProColumns<ConclusionInfo>[] => {
    const baseColumns: ProColumns<ConclusionInfo>[] = [];
    baseColumns.push({
      title: '名称',
      dataIndex: 'name',
      key: 'name',
      width: '20%',
    });
    baseColumns.push({
      title: '详细信息',
      dataIndex: 'content',
      key: 'content',
      width: '60%',
      render: (dom: ReactNode) => {
        if (typeof dom === 'string') {
          return parse(dom);
        } else {
          return '';
        }
      },
    });
    baseColumns.push({
      title: '备注',
      dataIndex: 'exrta',
      key: 'extra',
      width: '20%',
      render: (dom: ReactNode) => {
        if (typeof dom === 'string') {
          return parse(dom);
        } else {
          return '';
        }
      },
    });
    return baseColumns;
  };

  const fetchData = async () => {
    setLoading(true);
    console.info(
      `OOMConclusionList fetchData: target_version_code=${target_version_code}, base_version_code=${base_version_code}, platform=${platform}, type=${type}`,
    );
    const resp = await getOOMIndicatorAnalyzeResult({
      data: {
        target_version_code,
        base_version_code,
        platform,
      },
    });
    const conclusions: ConclusionInfo[] = [];
    if (resp.code === 0 && resp.data !== null) {
      const data2 = resp.data;
      if (data2.common_result.conclusions !== null) {
        conclusions.push(...data2.common_result.conclusions);
      }
      if (type === CrashType.JavaOOM && data2.java_result.conclusions !== null) {
        conclusions.push(...data2.java_result.conclusions);
      } else if (type === CrashType.NativeOOM && data2.native_result.conclusions !== null) {
        conclusions.push(...data2.native_result.conclusions);
      }
    } else if (resp.data === null) {
      console.info(`OOMConclusionList fetchData result empty`);
    } else {
      console.error(`OOMConclusionList fetchData error: code=${resp.code}, message=${resp.message}`);
    }
    setLoading(false);
    setDataSource(conclusions);
  };

  useEffect(() => {
    if (is_open) {
      console.info(`OOMConclusionList: open`);
      fetchData();
    } else {
      console.info(`OOMConclusionList: close`);
    }
  }, [is_open]);

  return (
    <>
      <ProTable<ConclusionInfo>
        options={false}
        search={false}
        columns={columns()}
        pagination={{
          pageSize: 15,
        }}
        dataSource={dataSource}
        loading={{ spinning: loading }}
      />
    </>
  );
};

export default OOMConclusionList;
