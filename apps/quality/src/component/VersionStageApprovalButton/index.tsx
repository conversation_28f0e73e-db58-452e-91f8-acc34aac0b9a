import { CheckItemStatus, VersionStageCheckItem } from '@shared/releasePlatform/versionStageInfoCheckList';
import React, { useEffect, useState } from 'react';
import { Button, Drawer, Flex, Input, message, Select, Tooltip, Table } from 'antd';
import { useModel } from '@edenx/runtime/model';
import UserSettingModule from '@/model/userSettingModel';
import VersionInfoModule from '@/model/versionInfoModel';
import { CheckOutlined, EditFilled, SnippetsFilled } from '@ant-design/icons';
import { BmType } from '@shared/bits/bmInfo';
import { updateCheckItem } from '@api/releasePlatform';

export interface ApprovalButtonProps {
  checkItemInfo: VersionStageCheckItem;
  stage: string;
  detailTitle: string;
  enableTBD?: boolean;
  statusChanged: (value: CheckItemStatus) => void;
}

const { TextArea } = Input;

const VersionStageApprovalButton: React.FC<ApprovalButtonProps> = ({
  checkItemInfo,
  stage,
  detailTitle,
  enableTBD = false,
  statusChanged,
}) => {
  const [userSettingState] = useModel(UserSettingModule);
  const [drawerOpen, setDrawerOpen] = useState<boolean>();
  const [versionInfo] = useModel(VersionInfoModule);
  const [operatorEmails, setOperatorEmails] = useState<string[]>([]);
  const [updateItemLoading, setUpdateItemLoading] = useState<boolean>(false);
  const [detailEdit, setDetailEdit] = useState<boolean>(false);
  const [itemStatus, setItemStatus] = useState<CheckItemStatus>(CheckItemStatus.TBD);
  const [resultDetail, setResultDetail] = useState<string>('');

  useEffect(() => {
    const emails = ['<EMAIL>'];
    emails.push(checkItemInfo.owner?.email ?? '');
    if (versionInfo.info.bmInfo[BmType.rd]) {
      emails.push(versionInfo.info.bmInfo[BmType.rd].email);
    }
    if (versionInfo.info.bmInfo[BmType.qa]) {
      emails.push(versionInfo.info.bmInfo[BmType.qa].email);
    }
    setOperatorEmails(emails);
    setItemStatus(checkItemInfo.status);
    if (checkItemInfo.result_detail) {
      setResultDetail(checkItemInfo.result_detail);
    }
  }, [userSettingState.info, versionInfo.info, checkItemInfo]);

  const onItemInfoSubmit = () => {
    const newItemInfo = checkItemInfo;
    newItemInfo.status = itemStatus;
    newItemInfo.result_detail = resultDetail;
    checkItemInfo = newItemInfo;
    message.success('正在更新，请不要关闭页面', 5);
    setUpdateItemLoading(true);
    updateCheckItem({
      data: {
        appId: versionInfo.info.app_id,
        version: versionInfo.info.version,
        stage,
        checkItemId: checkItemInfo.check_item_id,
        updateData: newItemInfo,
      },
    })
      .then(res => {
        message.success('更新成功', 5);
        setUpdateItemLoading(false);
        setDetailEdit(false);
        statusChanged(itemStatus);
      })
      .catch(e => {
        message.error('更新失败，请联系BM/管理员', 5);
        setUpdateItemLoading(false);
        setDetailEdit(false);
      });
  };

  return (
    <>
      <Tooltip placement="top" title="查看准出详情，结论细节，编辑状态">
        <Button onClick={() => setDrawerOpen(true)} type={'primary'} icon={<SnippetsFilled />}>
          查看详情
        </Button>
      </Tooltip>
      <Drawer
        title={detailTitle}
        width={800}
        onClose={() => setDrawerOpen(false)}
        open={drawerOpen}
        extra={
          <Tooltip placement="top" title="编辑准出项的最新状态并提交，仅Owner可操作">
            <Button
              type="primary"
              icon={<EditFilled />}
              loading={updateItemLoading}
              disabled={!operatorEmails.includes(userSettingState.info.email)}
              onClick={() => setDetailEdit(!detailEdit)}
            >
              {detailEdit ? '取消编辑' : '编辑状态'}
            </Button>
          </Tooltip>
        }
      >
        <Flex vertical gap={32}>
          <Select
            defaultValue={itemStatus}
            style={{ width: 120 }}
            onChange={(value: CheckItemStatus) => setItemStatus(value)}
            options={[
              { value: CheckItemStatus.TBD, label: '待评估', disabled: !enableTBD },
              { value: CheckItemStatus.Blocked, label: '阻塞' },
              { value: CheckItemStatus.Exempt, label: '豁免' },
            ]}
            disabled={!detailEdit || updateItemLoading}
          />
          <TextArea
            showCount
            maxLength={200}
            onChange={(e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => setResultDetail(e.target.value)}
            placeholder={resultDetail.length > 0 ? undefined : '补充检查项具体准出结论(不超过200字)'}
            defaultValue={resultDetail.length > 0 ? resultDetail : undefined}
            style={{ height: 120, resize: 'none' }}
            disabled={!detailEdit || updateItemLoading}
          />
          {detailEdit ? (
            <Button type="primary" icon={<CheckOutlined />} loading={updateItemLoading} onClick={onItemInfoSubmit}>
              提交更新
            </Button>
          ) : (
            <></>
          )}
        </Flex>
      </Drawer>
    </>
  );
};

export default VersionStageApprovalButton;
