import React, { useEffect, useState } from 'react';
import { Descriptions, Divider, Result, Space, Table, Tag, Timeline, Tooltip } from 'antd';
import {
  ControlStatus,
  formatLibraTime,
  getFlightEventName,
  isBetaFlight,
  LibraControlRecord,
} from '@shared/libra/libraControl';
import Title from 'antd/lib/typography/Title';
import { ColumnsType } from 'antd/es/table';
import { MeegoInfo } from '@shared/typings/meego';
import { queryFlightDetail, queryFlightErrorList, queryFlightIssueList } from '@api/libra';
import { ActualTrafficRecord, Flight } from '@shared/libra/flight';
import { LibraVersionPlatform } from '@shared/libra/libraInfo';
import { LibraModel } from '@shared/libra/commonLibra';
import Link from 'antd/es/typography/Link';
import commonUtils from '@shared/utils/commonUtils';
import { isSG } from '@/utils/region';
import { useModel } from '@edenx/runtime/model';
import AppSettingModule from '@/model/appSettingModel';
import { ProCard } from '@ant-design/pro-components';
import { ExperimentErrorInfo } from '@shared/libra/common';
import EllipsisSpan from '@/component/EllipsisSpan';

export interface LibraControlRecordDetailProps {
  record?: LibraControlRecord;
}
const LibraControlRecordDetail: React.FC<LibraControlRecordDetailProps> = ({ record }) => {
  const [loading, setLoading] = useState(false);
  const [bugList, setBugList] = useState<MeegoInfo[]>([]);
  const [errorList, setErrorList] = useState<ExperimentErrorInfo[]>([]);
  const [appSettingState] = useModel(AppSettingModule);
  const [flightDetail, setFlightDetail] = useState<{
    flight: Flight | undefined;
    platform: LibraVersionPlatform;
    records: ActualTrafficRecord[];
  }>();
  useEffect(() => {
    setLoading(true);
    queryFlightIssueList({ data: { flight_id: record?.flight_id ?? '' } }).then(list => {
      if (list) {
        setBugList(list);
        setLoading(false);
      }
    });
    queryFlightErrorList({ data: { flight_id: record?.flight_id ?? '' } }).then(list => {
      if (list) {
        setErrorList(list);
        setLoading(false);
      }
    });
    queryFlightDetail({ data: { flight_id: record?.flight_id ?? '' } }).then(info => {
      setFlightDetail(info);
    });
  }, [record]);
  const bugColumns: ColumnsType<MeegoInfo> = [
    {
      title: '缺陷名称',
      dataIndex: 'name',
      key: 'name',
      width: '50%',
      render: (val, meego) => (
        <>
          <a
            onClick={() => {
              window.open(meego.link);
            }}
          >
            {val}
          </a>
        </>
      ),
    },
    {
      title: '解决状态',
      dataIndex: 'isFixed',
      key: 'isFixed',
      width: '20%',
      filters: [
        {
          text: '已解决',
          value: true,
        },
        {
          text: '未解决',
          value: false,
        },
      ],
      sorter: (a, b) => Number(a.isFixed) - Number(b.isFixed),
      defaultSortOrder: 'ascend',
      onFilter: (value, info) => info.isFixed === value,
      render: (_, info, index) => (info.isFixed ? <Tag color={'green'}>已解决</Tag> : <Tag color={'red'}>未解决</Tag>),
    },
    {
      title: '优先级',
      dataIndex: 'priority',
      key: 'priority',
      width: '15%',
      filters: [...new Set(bugList?.map(item => item.priority))].map(value => ({
        text: `P${value}`,
        value: `${value}`,
      })),
      sorter: (a, b) => a.priority - b.priority,
      defaultSortOrder: 'ascend',
      onFilter: (value, info) => info.priority === value,
      render: (_, info, index) => (
        <Tag color={info.priority === 0 ? 'red' : info.priority === 1 ? 'orange' : 'yellow'}>P{info.priority}</Tag>
      ),
    },
    {
      title: '经办人',
      dataIndex: 'issue_operator',
      key: 'issue_operator',
      width: '10%',
    },
  ];
  const errorColumns: ColumnsType<ExperimentErrorInfo> = [
    {
      title: '问题类型',
      dataIndex: 'indicatorTitle',
      key: 'name',
      width: '15%%',
    },
    {
      title: '解决状态',
      dataIndex: 'status',
      key: 'status',
      width: '15%',
      filters: [...new Set(errorList.map(item => item.status))].map(value => ({
        text: `${value}`,
        value: `${value}`,
      })),
      onFilter: (value, info) => info.status === value,
    },
    {
      title: '优先级',
      dataIndex: 'priority',
      key: 'priority',
      width: '15%',
      filters: [...new Set(errorList.map(item => item.priority))].map(value => ({
        text: `P${value}`,
        value: `${value}`,
      })),
      sorter: (a, b) => a.priority - b.priority,
      defaultSortOrder: 'ascend',
      onFilter: (value, info) => info.priority === value,
      render: (_, info, index) => (
        <Tag color={info.priority === 0 ? 'red' : info.priority === 1 ? 'orange' : 'yellow'}>P{info.priority}</Tag>
      ),
    },
    {
      title: '更新时间',
      dataIndex: 'newErrorTime',
      key: 'newErrorTime',
      width: '15%',
      render: (_, info, index) => formatLibraTime(info.newErrorTime / 1000),
    },
    {
      title: '问题详情',
      dataIndex: 'detail',
      key: 'detail',
      width: '40%',
    },
  ];
  return record ? (
    <Space direction={'vertical'} size={'small'} style={{ width: '100%' }}>
      <Title level={5}>实验详情</Title>
      <Descriptions title={false}>
        <Descriptions.Item label="实验id">
          <Link
            href={commonUtils.getLibraFlightUrl(flightDetail?.flight?.id?.toString() ?? '', isSG(appSettingState.info))}
          >
            {flightDetail?.flight?.id}
          </Link>
        </Descriptions.Item>
        <Descriptions.Item label="实验端类型">
          {([LibraModel.lvSDK, LibraModel.globalCapCutClient] as string[]).includes(
            flightDetail?.flight?.product_id?.toString() ?? '',
          )
            ? '客户端实验'
            : `其他实验-${flightDetail?.flight?.product_id}`}
        </Descriptions.Item>
        <Descriptions.Item label="是否灰度实验">{isBetaFlight(flightDetail?.platform).toString()}</Descriptions.Item>
        <Descriptions.Item label="计算值">
          <Tooltip title={JSON.stringify(flightDetail?.flight?.filter_rule)}>
            <EllipsisSpan lines={3}>{JSON.stringify(flightDetail?.platform)}</EllipsisSpan>
          </Tooltip>
        </Descriptions.Item>
        <Descriptions.Item label="历史放量记录">
          <Tooltip title={JSON.stringify(flightDetail?.flight?.last_gradual_traffic)}>
            <EllipsisSpan lines={3} style={{ whiteSpace: 'pre-line' }}>
              {flightDetail?.records && flightDetail?.records?.length > 0
                ? flightDetail?.records
                    ?.map(item => `${formatLibraTime(item.timestamp)} 放量 ${item.version_resource * 100}%`)
                    ?.join('\n')
                : JSON.stringify(flightDetail?.flight?.last_gradual_traffic) ?? '无'}
            </EllipsisSpan>
          </Tooltip>
        </Descriptions.Item>
        <Descriptions.Item label="实验关联settings_ids">
          {flightDetail?.flight?.settings_item_ids ?? '无'}
        </Descriptions.Item>
      </Descriptions>
      <Divider />
      <Title level={5}>本次管控记录</Title>
      <Timeline
        mode={'left'}
        style={{ marginLeft: '-650px' }}
        pending={record.status === ControlStatus.TakeOver ? '纸飞机接管中...' : false}
        items={[
          {
            color: 'green',
            label: formatLibraTime(record.create_ts),
            children: `${record.user} 在Libra执行实验操作：${getFlightEventName(record)}`,
          },
          {
            color: record.take_over ? 'red' : 'green',
            label: formatLibraTime(record.create_ts),
            children: (
              <>
                <b>纸飞机管控：</b>
                {record.take_over_reason} {record.take_over_logid ?? ''}
              </>
            ),
          },
          ...record.process_history.map(p => ({
            color: p.success ? 'green' : 'red',
            label: formatLibraTime(p.ts),
            children: (
              <>
                <b>{p.process_type}：</b> {p.reason} {p.logid ?? ''}
              </>
            ),
          })),
        ]}
      />
      <Divider />
      <Title level={5}>巡检问题</Title>
      <ProCard tabs={{}}>
        <ProCard.TabPane key="bug" tab={`关联Bug单 ${bugList.length}`}>
          <Table dataSource={bugList} columns={bugColumns} loading={loading} />
        </ProCard.TabPane>
        <ProCard.TabPane key="error" tab={`业务巡检问题 ${errorList.length}`}>
          <Table dataSource={errorList} columns={errorColumns} loading={loading} />
        </ProCard.TabPane>
      </ProCard>
    </Space>
  ) : (
    <Result status="warning" title="There are some problems with your operation." />
  );
};

export default LibraControlRecordDetail;
