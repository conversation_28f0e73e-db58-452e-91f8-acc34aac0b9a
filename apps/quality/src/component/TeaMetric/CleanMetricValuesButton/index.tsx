import { DBTeaMetric } from '../../../../api/model/TeaMetricTable';
import React, { useState } from 'react';
import { useModel } from '@edenx/runtime/model';
import AppSettingModule from '@/model/appSettingModel';
import UserSettingModule from '@/model/userSettingModel';
import { cleanTeaMetricValues } from '@api/tea';
import { Button, message, Popconfirm } from 'antd';
import { DeleteOutlined } from '@ant-design/icons';

interface CleanMetricValuesButtonProps {
  metric: DBTeaMetric;
  onRefresh?: () => void;
}
const CleanMetricValuesButton: React.FC<CleanMetricValuesButtonProps> = ({ metric, onRefresh }) => {
  const [loading, setLoading] = useState(false);
  const [{ info }] = useModel(AppSettingModule);
  const [userSettings] = useModel(UserSettingModule);

  const isAllowList = (email?: string) =>
    email && ['<EMAIL>'].includes(email);
  const enterLoading = (_metric: DBTeaMetric) => {
    setLoading(true);
    cleanTeaMetricValues({
      data: {
        platformType: info.platform,
        metricName: _metric.Name,
      },
    }).then(() => {
      setLoading(false);
      if (onRefresh) {
        onRefresh();
      }
    });
    setTimeout(() => {
      setLoading(false);
    }, 6000);
  };

  return (
    <Popconfirm
      title="确定要清除指标数值"
      onConfirm={() => {
        if (userSettings.info.email === metric.POC || isAllowList(userSettings.info.email)) {
          enterLoading(metric);
          message.info(`清除指标${metric.DisplayName}成功`);
        } else {
          message.error(`无权限清除${metric.DisplayName}, 请联系指标负责人${metric.POC}`);
        }
      }}
    >
      <Button loading={loading} danger style={{ marginLeft: 5 }} type="primary" icon={<DeleteOutlined />}>
        清空指标
      </Button>
    </Popconfirm>
  );
};

export default CleanMetricValuesButton;
