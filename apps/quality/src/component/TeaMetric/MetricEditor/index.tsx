import React, { useEffect, useState } from 'react';
import { Button, Form, Input, message, Select, Space, Tag } from 'antd';
import {
  BusinessName,
  TeaDimensionName,
  TeaGranularity,
  TeaGranularityName,
  TeaPriority,
  TeaPriorityName,
  TeaValueTypeName,
} from '@shared/typings/tea/metric';
import AppSettingModule from '@/model/appSettingModel';
import { useModel } from '@edenx/runtime/model';
import { isCN } from '@/utils/region';
import { TEA_URL_REGEX } from '@shared/utils/tea';
import { getMetricAndCommonFilter } from '@api/tea';
import { some, toPairs, zipWith } from 'lodash';
import { User } from '@pa/shared/dist/src/core';
import UserSelector from '@/component/UserSelector';
import { DefaultOptionType } from 'antd/lib/select';
import { trim_query_from_url } from '@shared/utils/tools';
import { v4 as uuidv4 } from 'uuid';

export interface MetricEditorContent {
  Name: string;
  DisplayName: string;
  Url: string;
  Granularity: string[]; // TeaGranularity[];
  RefreshRate: string; // TeaGranularity;
  Dimension: string; // TeaDimensionType;
  ValueType: string; // TeaValueType;
  Poc: string;
  Business: string; // Business;
  Priority: string; // TeaPriority;
  Metric: string;
  CommonFilter: string;
  BusinessModule?: string;
}

export interface MetricEditorProps {
  onFinish: (value: MetricEditorContent, poc: User | undefined) => Promise<any>;
  submitLoading: boolean;
  initialValue?: MetricEditorContent;
}

const MetricEditor: React.FC<MetricEditorProps> = ({ initialValue, submitLoading, onFinish }) => {
  const [form] = Form.useForm<MetricEditorContent>();

  const [{ info }] = useModel(AppSettingModule);
  // fixme: TEA 出新版了，可恶
  const urlPattern = isCN(info)
    ? /^https:\/\/data\.bytedance\.net\/tea-next\/project\/[0-9]+\/event-analysis(\/result)?\/([a-z0-9]+)/
    : /^https:\/\/tea-va\.bytedance\.net\/tea\/app\/[0-9]+\/event-analysis(\/result)?\/\d+/;

  const [metricOption, setMetricOption] = useState<DefaultOptionType[]>([]);
  const [commonFilterOption, setCommonFilterOption] = useState<DefaultOptionType[]>([]);

  const [poc, setPoc] = useState<User>();
  const [timer, setTimer] = useState<ReturnType<typeof setTimeout>>();
  const [enableSubmit, setEnableSubmit] = useState(false);

  const objectToOptions = <E extends Record<string, string | number>>(obj: Record<keyof E, string>) =>
    toPairs(obj).map(([k, v]) => ({
      value: k,
      label: <Tag>{v}</Tag>,
    }));

  const parseTeaUrl = async (url: string) => {
    if (!TEA_URL_REGEX.test(url)) {
      return;
    }
    const resp = await getMetricAndCommonFilter({ query: { url } });
    if (resp.code !== 0 || !resp.data) {
      console.log(resp);
      message.error('可选指标请求失败，请检查链接是否填写正确！');
      return;
    }
    zipWith(
      [setMetricOption, setCommonFilterOption],
      [resp.data.metric, resp.data.common_filter].map(l =>
        l.map(
          (it): DefaultOptionType => ({
            value: it.content,
            label: it.name,
          }),
        ),
      ),
      (f, d) => f(d),
    );
    message.success('TEA 链接解析成功！');
    setEnableSubmit(true);
  };

  useEffect(() => {
    if (initialValue) {
      setEnableSubmit(false);
      form.setFieldsValue(initialValue);
      parseTeaUrl(initialValue.Url);
    } else {
      form.setFieldValue('Name', uuidv4());
      setEnableSubmit(true);
    }
  }, [initialValue]);

  return (
    <Form
      form={form}
      name="metric-editor-form"
      onFinish={async v => {
        console.debug(`onFinish => ${JSON.stringify(v)}`);
        onFinish(v, poc)
          .then(() => form.resetFields())
          .then(() => message.success('成功！'))
          .catch(err => message.error(err.toString()));
      }}
      requiredMark="optional"
    >
      <Form.Item name="Name" label="指标 ID" rules={[{ required: true }]}>
        <Input allowClear disabled={Boolean(initialValue?.Name)} />
      </Form.Item>
      <Form.Item name="DisplayName" label="显示名称" rules={[{ required: true }]}>
        <Input allowClear />
      </Form.Item>
      <Form.Item name="Url" label="TEA 链接" rules={[{ required: true, pattern: urlPattern }]}>
        <Input
          allowClear
          onInput={async v => {
            if (timer) {
              clearTimeout(timer);
            }
            const newTimer = setTimeout(async () => {
              const str = trim_query_from_url((v.target as HTMLInputElement).value);
              await parseTeaUrl(str);
              form.resetFields(['Metric', 'CommonFilter']);
            }, 1000);

            setTimer(newTimer);
          }}
        />
      </Form.Item>
      <Form.Item name="ValueType" label="指标类型" rules={[{ required: true }]}>
        <Select placeholder="选择指标类型" options={objectToOptions(TeaValueTypeName)} />
      </Form.Item>
      <Form.Item name="Business" label="业务方向" rules={[{ required: true }]}>
        <Select placeholder="选择业务方向" options={objectToOptions(BusinessName)} />
      </Form.Item>
      <Form.Item name="BusinessModule" label="业务模块" rules={[{ required: false }]}>
        <Input allowClear />
      </Form.Item>
      <Form.Item
        name="Priority"
        label="优先级"
        rules={[{ required: true }]}
        initialValue={TeaPriority.WATCH.toString()}
      >
        <Select placeholder="选择优先级" options={objectToOptions(TeaPriorityName)} />
      </Form.Item>
      <Form.Item name="Granularity" label="粒度" rules={[{ required: true }]}>
        <Select
          mode="multiple"
          placeholder="选择数据量级"
          options={objectToOptions(TeaGranularityName).filter(it =>
            [
              TeaGranularity.HOUR_1.toString(),
              TeaGranularity.HOUR_24.toString(),
              TeaGranularity.DAY_7.toString(),
              TeaGranularity.DAY_14.toString(),
            ].includes(it.value),
          )}
          onChange={(value, option) => {
            if (!value || value.length <= 0) {
              form.setFieldValue('RefreshRate', undefined);
              return;
            }
            if (some(value, it => it === TeaGranularity.HOUR_1.toString())) {
              form.setFieldValue('RefreshRate', TeaGranularity.HOUR_1.toString());
            } else {
              form.setFieldValue('RefreshRate', TeaGranularity.HOUR_24.toString());
            }
          }}
        />
      </Form.Item>
      <Form.Item name="RefreshRate" label="刷新频率" rules={[{ required: true }]}>
        <Select
          disabled
          placeholder="选择刷新频率"
          options={objectToOptions(TeaGranularityName).filter(it =>
            [TeaGranularity.HOUR_1.toString(), TeaGranularity.HOUR_24.toString()].includes(it.value),
          )}
        />
      </Form.Item>
      <Form.Item name="POC_" label="POC" rules={[{ required: true }]} initialValue={{}}>
        <UserSelector
          onSelect={it => setPoc(it)}
          placeholder="输入指标POC名字"
          size="middle"
          initialEmail={initialValue?.Poc}
        />
      </Form.Item>
      <Form.Item name="Dimension" label="版本维度" rules={[{ required: true }]}>
        <Select placeholder="选择版本维度" options={objectToOptions(TeaDimensionName)} />
      </Form.Item>
      <Form.Item name="metrics" label="指标选择" rules={[{ required: true }]} initialValue={{}}>
        <Space.Compact>
          <Form.Item name="Metric" rules={[{ required: true }]}>
            <Select placeholder="选择指标" style={{ width: '256px' }} options={metricOption} />
          </Form.Item>
          <Form.Item name="CommonFilter" rules={[{ required: true }]}>
            <Select placeholder="选择公共筛选器" style={{ width: '192px' }} options={commonFilterOption} />
          </Form.Item>
        </Space.Compact>
      </Form.Item>
      <Form.Item>
        <Button type="primary" htmlType="submit" loading={submitLoading} disabled={!enableSubmit}>
          提交
        </Button>
      </Form.Item>
    </Form>
  );
};

export default MetricEditor;
