import React, { useState } from 'react';
import { PlusCircleOutlined } from '@ant-design/icons';
import { Button, ButtonProps, Drawer, DrawerProps } from 'antd';
import MetricEditor, { MetricEditorContent } from 'src/component/TeaMetric/MetricEditor';
import { createTeaMetric } from '@api/tea';
import { useModel } from '@edenx/runtime/model';
import AppSettingModule from '@/model/appSettingModel';
import { PlatformType, User } from '@pa/shared/dist/src/core';
import { Platform } from '@shared/typings/tea/metric';
import { metricEditorContentToAPI, validateEditorInput } from '@/component/TeaMetric/common';

export interface TeaMetricAddDrawerProps {
  onRefresh?: () => void;
}

const TeaMetricAddDrawer: React.FC<TeaMetricAddDrawerProps> = ({ onRefresh }) => {
  const [{ info }] = useModel(AppSettingModule);
  const [isOpen, setIsOpen] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const buttonProps: ButtonProps = {
    onClick: () => setIsOpen(true),
    icon: <PlusCircleOutlined />,
  };
  const drawerProps: DrawerProps = {
    title: '正在添加指标',
    onClose: () => setIsOpen(false),
    open: isOpen,
    bodyStyle: {
      paddingBottom: 80,
    },
  };

  const submitCreate = async (v: MetricEditorContent, poc: User | undefined) => {
    const err = validateEditorInput(v, poc);
    if (err || !poc?.email) {
      throw new Error(err);
    }
    setIsSubmitting(true);
    const { code, message } = await createTeaMetric({
      data: {
        name: v.Name,
        platform: info.platform === PlatformType.Android ? Platform.Android : Platform.iOS,
        poc: poc.email,
        ...metricEditorContentToAPI(v),
      },
    });
    setIsSubmitting(false);
    if (onRefresh) {
      onRefresh();
    }
    if (code !== 0) {
      throw new Error(message);
    }
    setIsOpen(false);
  };

  return (
    <>
      <Button type="primary" {...buttonProps}>
        新增指标
      </Button>
      <Drawer size="large" {...drawerProps}>
        <MetricEditor onFinish={submitCreate} submitLoading={isSubmitting} />
      </Drawer>
    </>
  );
};

export default TeaMetricAddDrawer;
