import { MetricEditorContent } from '@/component/TeaMetric/MetricEditor';
import { get_enum_value, trim_query_from_url } from '@shared/utils/tools';
import { Business, TeaDimensionType, TeaGranularity, TeaPriority, TeaValueType } from '@shared/typings/tea/metric';
import { User } from '@pa/shared/dist/src/core';
import { some } from 'lodash';
import { DBTeaMetric } from '../../../api/model/TeaMetricTable';
import { buildTeaUrl } from '@shared/utils/tea';

export const metricEditorContentToAPI = (v: MetricEditorContent) => ({
  url: trim_query_from_url(v.Url),
  display_name: v.DisplayName,
  granularity: v.Granularity.map(it => get_enum_value(TeaGranularity, it)),
  refresh_rate: get_enum_value(TeaGranularity, v.RefreshRate),
  dimension: get_enum_value(TeaDimensionType, v.Dimension),
  value_type: get_enum_value(TeaValueType, v.ValueType),
  business: get_enum_value(Business, v.Business),
  priority: get_enum_value(TeaPriority, v.Priority),
  metric: v.Metric,
  common_filter: v.CommonFilter,
  business_module: v.BusinessModule ?? undefined,
});

export const validateEditorInput = (v: MetricEditorContent, poc: User | undefined): string | undefined => {
  if (!poc || !poc.email) {
    return '未找到 POC';
  }
  if (some(v.Granularity, it => Number(it) < Number(v.RefreshRate))) {
    return '数据粒度不能小于采集频率';
  }
  return undefined;
};

export const teaMetricToEditor = (v: DBTeaMetric): MetricEditorContent => ({
  Name: v.Name,
  DisplayName: v.DisplayName,
  Url: buildTeaUrl(v.IsNewTea, v.TeaAppId, v.TeaId, v.TeaIdType),
  Granularity: v.Granularity.map(it => get_enum_value(TeaGranularity, it).toString()),
  RefreshRate: get_enum_value(TeaGranularity, v.RefreshRate).toString(),
  Dimension: get_enum_value(TeaDimensionType, v.DimensionType).toString(),
  ValueType: get_enum_value(TeaValueType, v.ValueType).toString(),
  Poc: v.POC,
  Business: get_enum_value(Business, v.Business).toString(),
  Priority: get_enum_value(TeaPriority, v.Priority).toString(),
  Metric: v.MetricValue,
  CommonFilter: v.CommonFilter,
  BusinessModule: v.BusinessModule,
});
