import React, { useState } from 'react';
import { DBTeaMetric } from '../../../../api/model/TeaMetricTable';
import { Button, ButtonProps, Drawer, DrawerProps } from 'antd';
import { EditOutlined } from '@ant-design/icons';
import MetricEditor, { MetricEditorContent } from '@/component/TeaMetric/MetricEditor';
import { User } from '@pa/shared/dist/src/core';
import { metricEditorContentToAPI, teaMetricToEditor, validateEditorInput } from '@/component/TeaMetric/common';
import { patchTeaMetric } from '@api/tea';

interface TeaMetricModifyDrawerProps {
  metric: DBTeaMetric;
  onRefresh?: () => void;
}

const TeaMetricModifyDrawer: React.FC<TeaMetricModifyDrawerProps> = ({ metric, onRefresh }) => {
  const [isOpen, setIsOpen] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const buttonProps: ButtonProps = {
    icon: <EditOutlined />,
    onClick: () => setIsOpen(true),
  };
  const drawerProps: DrawerProps = {
    title: `正在编辑指标 ${metric.Name}`,
    onClose: () => setIsOpen(false),
    open: isOpen,
    bodyStyle: {
      paddingBottom: 80,
    },
  };

  const submitModify = async (v: MetricEditorContent, poc: User | undefined) => {
    const err = validateEditorInput(v, poc);
    if (err || !poc?.email) {
      throw new Error(err);
    }
    setIsSubmitting(true);
    const { code, message } = await patchTeaMetric({
      params: {
        name: v.Name,
      },
      data: {
        poc: poc.email,
        ...metricEditorContentToAPI(v),
      },
    });
    setIsSubmitting(false);
    if (onRefresh) {
      onRefresh();
    }
    if (code !== 0) {
      throw new Error(message);
    }
    setIsOpen(false);
  };

  return (
    <>
      <Button type="primary" {...buttonProps}>
        修改
      </Button>
      <Drawer size="large" {...drawerProps}>
        <MetricEditor onFinish={submitModify} submitLoading={isSubmitting} initialValue={teaMetricToEditor(metric)} />
      </Drawer>
    </>
  );
};

export default TeaMetricModifyDrawer;
