import { Tooltip } from 'antd';
import { HTMLProps, ReactNode } from 'react';

type EllipsisProps = HTMLProps<HTMLSpanElement> & {
  lines: number;
  children: ReactNode;
};

const EllipsisSpan: React.FC<EllipsisProps> = ({ lines, children }) => (
  <Tooltip
    overlayStyle={{
      maxHeight: `${lines * 1.5 * 16}px`, // 根据最大行数计算最大高度
      overflow: 'auto',
    }}
    title={children}
    placement="top"
    trigger="hover"
  >
    <div
      style={{
        display: '-webkit-box',
        overflow: 'hidden',
        textOverflow: 'ellipsis',
        WebkitLineClamp: lines,
        WebkitBoxOrient: 'vertical',
      }}
    >
      {children}
    </div>
  </Tooltip>
);

export default EllipsisSpan;
