import { Button, ButtonProps, Modal } from 'antd';
import React, { useEffect, useState } from 'react';
import { pickBy } from 'lodash';
import { hasKaniPermission } from '@api/kani';
import resourceNameMap from '@shared/kani/resourceNameMap';
import { get_apply_url } from '@shared/kani/util';

type KaniButtonProps = ButtonProps & {
  resource_key: string;
  permission_name: string;
};

const KaniButton: React.FC<KaniButtonProps> = props => {
  const [loading, setLoading] = useState(false);
  const [internalLoading, setInternalLoading] = useState(Boolean(props.loading));

  useEffect(() => {
    setInternalLoading(loading || Boolean(props.loading));
  }, [loading, props.loading]);

  // 劫持 onClick
  const originalOnClick = props.onClick;
  const cleanedProps = pickBy<KaniButtonProps>(
    props,
    (_, k) => !['onClick', 'loading', 'resource_key', 'permission_name'].includes(k),
  );

  return (
    <Button
      {...cleanedProps}
      loading={internalLoading}
      onClick={async e => {
        console.log('Kani Button clicked!');
        setLoading(true);
        setInternalLoading(true);

        const kani_result = await hasKaniPermission({
          query: {
            resource_key: props.resource_key,
            permission_name: props.permission_name,
          },
          headers: {},
        });

        if (!kani_result.has_permission) {
          Modal.error({
            title: '暂无权限',
            content: (
              <>
                您（{kani_result.email}）暂无对 {resourceNameMap[props.resource_key]} 资源的 {props.permission_name}{' '}
                权限。您可以前往
                <a
                  href={get_apply_url(props.resource_key, props.permission_name)}
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  此处
                </a>
                申请该权限。敬请谅解。
              </>
            ),
          });
        } else if (originalOnClick) {
          originalOnClick(e);
        }

        setLoading(false);
        setInternalLoading(Boolean(props.loading));
      }}
    />
  );
};

export default KaniButton;
