import React, { useEffect, useState } from 'react';
import { BitsVersionLog } from '@shared/typings/bits';
import { queryBitsVersionAmount, queryBitsVersionLog } from '@api/bits';
import { PlatformType } from '@pa/shared/dist/src/core';
import { useModel } from '@edenx/runtime/model';
import AppSettingModule from '@/model/appSettingModel';
import { Descriptions, Tag } from 'antd';
import dayjs from 'dayjs';
import { BitsAppId } from '@shared/utils/bits';

const TIME_FORMAT = 'YYYY-MM-DD HH:mm:ss Z';

export interface BitsVersionInfoCardProps {
  version_code?: string;
  start_time: (x: number) => number;
  end_time: (x: number) => number;
}
function businessAppId2BitsAppId(businessAppId: number) {
  if (businessAppId === 300601) {
    return 2020092636;
  } else if (businessAppId === 300602) {
    return 2000001157;
  } else if (businessAppId === 244127338754) {
    return 2020095292;
  } else if (businessAppId === 225469550850) {
    return 2020095291;
  } else {
    return businessAppId;
  }
}

function businessAppId2BitsAppId_(businessAppId: number, version_code: string | undefined) {
  const isVersionGreaterOrEqual = (inputVersion: string, targetVersion: string) => {
    const inputParts = inputVersion.split('.').map(Number);
    const targetParts = targetVersion.split('.').map(Number);
    for (let i = 0; i < Math.max(inputParts.length, targetParts.length); i++) {
      const inputPart = inputParts[i] || 0;
      const targetPart = targetParts[i] || 0;
      if (inputPart > targetPart) {
        return true;
      }
      if (inputPart < targetPart) {
        return false;
      }
    }
    return true;
  };

  if (version_code) {
    // 处理 version_code >= 14400000 且 businessAppId 为 300602 的情况
    if (Number(version_code) >= 14400000 && businessAppId === 300602) {
      return 2020095699;
    }
    // 处理 version_code >= 14.4.0.0 且 businessAppId 为 300601 的情况
    if (isVersionGreaterOrEqual(version_code, '14.4.0.0') && businessAppId === 300601) {
      return 2020095701;
    }
  }

  if (businessAppId === 300601) {
    return 2020092636;
  } else if (businessAppId === 300602) {
    return 2000001157;
  } else if (businessAppId === 244127338754) {
    return 2020095292;
  } else if (businessAppId === 225469550850) {
    return 2020095291;
  } else {
    return businessAppId;
  }
}

const BitsVersionInfoCard: React.FC<BitsVersionInfoCardProps> = ({ version_code, start_time, end_time }) => {
  // 所显示的 bits 版本信息
  const [bitsInfo, setBitsInfo] = useState<BitsVersionLog>();

  // 放量情况，上面的不准，故增加一列。
  const [amountInfo, setAmountInfo] = useState(-1);

  const [{ info }] = useModel(AppSettingModule);
  const is_android = info.platform === PlatformType.Android;
  const is_cn = info.name === '剪映';

  const appId = businessAppId2BitsAppId_(info.id, version_code);
  const update_info = async (v: string) => {
    const bits_info = await queryBitsVersionLog({
      data: {
        // CC-Android 强制使用 GRAY。CC 在放量达到 100% 之前不会进入“正式”
        PageSize: 10,
        PageNum: 1,
        VersionCode: [v],
        AppId: appId,
      },
    });
    setBitsInfo(bits_info.Versions.reduce((a, b) => (a.PublishTime! < b.PublishTime! ? a : b)) as BitsVersionLog);

    if (!(is_android && is_cn && Number(v) % 10000 < 1600)) {
      return;
    }

    const amount = await queryBitsVersionAmount({
      query: {
        version_code: v,
      },
    });
    setAmountInfo(amount || -1);
  };

  useEffect(() => {
    if (version_code) {
      update_info(version_code);
    }
  }, [version_code]);

  if (!bitsInfo) {
    return <>请选择一个版本！</>;
  }
  return (
    <Descriptions key={bitsInfo.VersionCode} bordered title={'所选版本信息'} size={'small'}>
      <Descriptions.Item label={'版本名称'}>{bitsInfo.VersionName}</Descriptions.Item>
      <Descriptions.Item label={'VersionCode'}>{version_code}</Descriptions.Item>
      <Descriptions.Item label={'释放时间'}>{dayjs.unix(bitsInfo.PublishTime).format(TIME_FORMAT)}</Descriptions.Item>
      <Descriptions.Item label={'放量情况'}>
        {amountInfo <= 0 ? (
          <Tag color="grey">{is_android && is_cn ? '暂无数据' : '不适用'}</Tag>
        ) : // fixme
        is_android && Number(version_code) % 10000 < 1600 ? (
          amountInfo
        ) : (
          <Tag color="green">正式版本</Tag>
        )}
      </Descriptions.Item>
      <Descriptions.Item label={'时间段'}>
        {`${dayjs.unix(start_time(bitsInfo.PublishTime)).format(TIME_FORMAT)} ~ ${dayjs
          .unix(end_time(bitsInfo.PublishTime))
          .format(TIME_FORMAT)}`}
      </Descriptions.Item>
    </Descriptions>
  );
};

export default BitsVersionInfoCard;
