import React from 'react';
import { ChatInfo } from '@pa/shared/dist/src/core';
import { searchLarkChat } from '@api/index';
import { ProFormSelect, ProFormSelectProps } from '@ant-design/pro-components';
import { Avatar, Image, Space } from 'antd';

const ProFormLarkChatSelector: React.FC<ProFormSelectProps> = props => {
  let timeout: ReturnType<typeof setTimeout> | null,
    lastRequest: (value: ChatInfo[] | PromiseLike<ChatInfo[] | undefined> | undefined) => void;

  const search = (keyWord: string) =>
    new Promise<ChatInfo[] | undefined>((resolve, reject) => {
      console.log(keyWord);
      if (timeout !== null) {
        clearTimeout(timeout);
      }
      if (lastRequest) {
        lastRequest(Promise.resolve([]));
      }
      lastRequest = resolve;
      timeout = setTimeout(() => {
        searchLarkChat({ data: { keyWords: keyWord } }).then(ret => {
          resolve(Promise.resolve(ret));
        });
      }, 300);
    });
  return (
    <ProFormSelect.SearchSelect
      {...props}
      showSearch
      request={async ({ keyWords }) => {
        console.log(keyWords);
        if (!keyWords || keyWords === '') {
          return [];
        }
        const result = await search(keyWords);
        console.log(result);
        return result
          ? result.map(value => ({
              label: (
                <Space>
                  {value.avatar ? (
                    <Avatar size={18} src={<Image preview={false} src={value.avatar.toString()} />} />
                  ) : (
                    <></>
                  )}
                  <span>{value.name}</span>
                </Space>
              ),
              value: value.chat_id,
            }))
          : [];
      }}
      fieldProps={{
        mode: 'multiple',
        filterOption: false,
        resetAfterSelect: false,
      }}
    />
  );
};

export default ProFormLarkChatSelector;
