import HoverCardInfo from '@/component/UserCard/HoverCardInfo';
import * as React from 'react';
import { Popover, Tag } from '@douyinfe/semi-ui';

interface SemiUserCardProps {
  simpleUserData: any;
  email?: string;
}

const SemiUserCard: React.FC<SemiUserCardProps> = ({ simpleUserData, email }) => (
  <Popover showArrow={true} content={<HoverCardInfo email={email ?? ''} />}>
    <Tag avatarSrc={simpleUserData.avatarUrl} avatarShape="circle" size="large">
      {simpleUserData.name}
    </Tag>
  </Popover>
);

export default SemiUserCard;
