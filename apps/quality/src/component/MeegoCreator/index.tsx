import { Button, Input, message, Popconfirm, Select, Space, Switch, Tag } from 'antd';
import React, { useEffect, useState } from 'react';
import { CrashListInfo } from '@shared/typings/slardar/crash/issueListSearch';

import { checkAndUpdateBug, queryVersionInfo, updateSlardarCrashIssue } from '@api/slardar';
import { DiscoverStag2Option, DiscoverStage } from '@shared/walle/consumeResult';
import { NetworkCode } from '@pa/shared/dist/src/core';
import {
  BindIssueMeegoItem,
  meegoCreateSlardarBug,
  UpdateMeegoLevel,
  AutoJoinGroupChat,
  meegoOfflineSlardarBug,
} from '@api/meego';
import { MeegoStatus2Name, Status2Color } from '@shared/typings/meego';
import UserMessage from '@/component/UserMessage';
import { useModel } from '@edenx/runtime/model';
import UserSettingModule from '@/model/userSettingModel';
import { versionCodeToMeegoVersion, VersionType } from '@shared/utils/version_utils';
import { SlardarPlatformType } from '@pa/shared/dist/src/appSettings/appSettings';
import AppSettingModule from '@/model/appSettingModel';
import { CrashType2Url } from '@shared/typings/slardar/common';

const { Option } = Select;

interface Props {
  record: CrashListInfo;
}

const MeegoCreator: React.FC<Props> = ({ record }) => {
  const [showButton, setShowButton] = useState(!record.meego_url);
  const [url, setUrl] = useState(record.meego_url);
  const [loading, setLoading] = useState<boolean>(false);
  const [offlineloading, setOfflineLoading] = useState<boolean>(false);
  const [joinLoading, setJoinLoading] = useState<boolean>(false);
  const [userSettings] = useModel(UserSettingModule);
  const [appSettings] = useModel(AppSettingModule);
  const [priority, setPriority] = useState<number | undefined>(undefined);
  const [status, setStatus] = useState<string | undefined>(undefined);
  const [operator, setOperator] = useState<string | undefined>(undefined);
  const [isConsume, setIsConsume] = useState<boolean>(false);
  const [isBind, setIsBind] = useState<boolean>(false);
  const [bindUrl, setBindUrl] = useState<string>('');

  useEffect(() => {
    const autoCheckForMeego = async () => {
      const isOffline = 'version' in record && record.version && record.version.includes('-offline');
      if (isOffline && !record.meego_url) {
        setOfflineLoading(true);

        const res = await meegoOfflineSlardarBug({
          data: {
            aid: record.aid,
            platform: record.platform,
            version: record.version
              ? record.version.replace(/-offline/g, '')
              : versionCodeToMeegoVersion(record.version_code),
            issueId: record.issue_id,
          },
        });
        if (res && res.ret === NetworkCode.Success && res.data && record.version) {
          setUrl(res.data);
          setOperator(res.operator);
          setStatus(res.status);
          setPriority(res.priority);
          await updateSlardarCrashIssue({
            data: {
              aid: record.aid,
              issue_id: record.issue_id,
              meego_url: res.data,
              version_code: record.version_code,
              version: record.version.replace(/-offline/g, ''),
            },
          });
          // 兜底更新关联meego，防止meego单已存在
          await checkAndUpdateBug({
            data: {
              url: res.data,
              platform: record.platform,
              version_code: record.version_code,
              priority: record.issue_level,
              version: record.version.replace(/-offline/g, ''),
            },
          });
        }
        setOfflineLoading(false);
      }
    };
    autoCheckForMeego();
  }, [record]);

  const handleClick = async () => {
    setLoading(true);
    if (record.slardar_url && record.ranking !== -1) {
      console.log({
        issueLink: record.slardar_url,
        discoverVersion: record.version_code,
        priority: `P${record.issue_level}`,
        assignee: record.managers[0],
      });
      if (isBind && bindUrl) {
        const pattern = /detail\/(\d+)$/;
        const match = bindUrl.match(pattern);
        if (match && !isNaN(parseInt(match[1]))) {
          console.log(`匹配成功，数字为：${match[1]}`);
          try {
            const res = await BindIssueMeegoItem({
              data: {
                aid: record.aid,
                url: record.slardar_url,
                operator: userSettings.info.email,
                version: record.version
                  ? record.version.replace(/-offline/g, '')
                  : versionCodeToMeegoVersion(record.version_code, record.aid !== 581595),
                platform: record.platform,
                issue_id: record.issue_id,
                meegoId: parseInt(match[1]),
                issue_level: record.issue_level,
              },
            });
            if (res.ret === NetworkCode.Success && res.data) {
              setUrl(bindUrl);
              setOperator(res.operator);
              setStatus(res.status);
              setPriority(res.priority);
            } else {
              message.error(`建单失败,code=${res.ret},msg=${res.error_msg}`, 15);
            }
          } catch (e) {
            message.error(`绑单失败,err=${e}`, 15);
          }
        } else {
          message.error('请输入正确meego链接');
        }
      } else {
        const isOffline = 'version' in record && record.version && record.version.includes('-offline');
        let discoverStage = isOffline ? DiscoverStage.NEW_FEATURE : DiscoverStage.GREY;
        if (record.version_code && !isOffline) {
          const versionInfos = await queryVersionInfo({
            data: {
              aid: appSettings.info.businessInfo.aid,
              version_code: [record.version_code],
            },
          });
          console.log(
            `queryVersionInfo aid:${appSettings.info.businessInfo.aid} version_code:${record.version_code} resp: ${JSON.stringify(versionInfos)}`,
          );
          if (versionInfos.length > 0) {
            if ([VersionType.SMALL, VersionType.SMALL_TF].includes(versionInfos[0].version_type)) {
              discoverStage = DiscoverStage.SMALL;
            }
            if (versionInfos[0].version_type === VersionType.ONLINE) {
              discoverStage = DiscoverStage.ONLINE;
            }
          }
        }
        console.log(`meegoCreateSlardarBug with discoverStage:${discoverStage}`);
        try {
          const res = await meegoCreateSlardarBug({
            data: {
              link: record.slardar_url,
              discoverVersion: record.version
                ? record.version.replace(/-offline/g, '')
                : versionCodeToMeegoVersion(record.version_code, appSettings.info.businessInfo.aid !== 581595),
              priority: `${record.issue_level}`,
              assignee: record.managers[0] ?? '',
              discoverStage: DiscoverStag2Option[discoverStage],
              creator: userSettings.info.email,
              isConsume,
              isAutoCreated: false,
            },
          });
          console.log(`createBugRes`, res);
          if (res.ret === NetworkCode.Success && res.data) {
            setUrl(res.data);
            setOperator(res.operator);
            setStatus(res.status);
            setPriority(res.priority);
            if (res.data && !isOffline) {
              await updateSlardarCrashIssue({
                data: {
                  aid: record.aid,
                  issue_id: record.issue_id,
                  meego_url: res.data,
                  version_code: record.version_code,
                },
              });
              // 兜底更新关联meego，防止meego单已存在
              await checkAndUpdateBug({
                data: {
                  url: res.data,
                  platform: record.platform,
                  version_code: record.version_code,
                  priority: record.issue_level,
                },
              });
            }
            if (res.data && isOffline && record.version) {
              await updateSlardarCrashIssue({
                data: {
                  aid: record.aid,
                  issue_id: record.issue_id,
                  meego_url: res.data,
                  version_code: record.version_code,
                  version: record.version.replace(/-offline/g, ''),
                },
              });
              // 兜底更新关联meego，防止meego单已存在
              await checkAndUpdateBug({
                data: {
                  url: res.data,
                  platform: record.platform,
                  version_code: record.version_code,
                  priority: record.issue_level,
                  version: record.version.replace(/-offline/g, ''),
                },
              });
            }
            if (!res.IsConsumed) {
              message.error(`walle分配失败，使用兜底分配人`, 15);
            }
          } else {
            message.error(`建单失败,code=${res.ret},msg=${res.error_msg}`, 15);
          }
        } catch (e) {
          message.error(`建单失败,err=${e}`, 15);
        }
      }
      setLoading(false);
    }
    console.log('no url');
    setLoading(false);
  };
  // 定级PO/P1无分配者证明分配失败，前端不提供分配选项
  const PopTitle =
    record.managers.length <= 0 &&
    (record.issue_level > 2 || (record.platform === SlardarPlatformType.iOS && record.issue_level > 1)) &&
    ['new_created', 'be_processed_again'].includes(record.status) ? (
      <Space direction={'vertical'}>
        <Space>
          是否绑定已有提单:
          <Switch
            defaultChecked={isBind}
            onChange={value => {
              setIsBind(value);
              if (value) {
                setIsConsume(false);
              }
            }}
          />
        </Space>
        {isBind ? <Input placeholder={'输入meego链接'} onChange={e => setBindUrl(e.target.value)} /> : null}
        <Space>
          是否触发自动分配:
          <Switch disabled={isBind} defaultChecked={isConsume} onChange={value => setIsConsume(value)} />
        </Space>
      </Space>
    ) : (
      <Space direction={'vertical'}>
        <Space>
          是否绑定已有提单:
          <Switch
            defaultChecked={isBind}
            onChange={value => {
              setIsBind(value);
              if (value) {
                setIsConsume(false);
              }
            }}
          />
        </Space>
        {isBind ? <Input placeholder={'输入meego链接'} onChange={e => setBindUrl(e.target.value)} /> : null}
      </Space>
    );
  useEffect(() => {
    setShowButton(!url);
  }, [url]);
  return (
    <Space>
      {showButton ? (
        <Space>
          <Popconfirm title={PopTitle} onConfirm={handleClick} okText={isBind ? '绑定' : '新建'} cancelText="取消">
            <Button loading={loading}>关联meego</Button>
          </Popconfirm>
        </Space>
      ) : (
        <Space direction="vertical">
          <Space>
            <UserMessage
              emails={
                record.meego_operator
                  ? record.meego_operator.map(v => v.substring(0, v.indexOf('@')))
                  : [operator ? operator.substring(0, operator.indexOf('@')) : '']
              }
            />
            <Tag
              color={record.meego_status ? Status2Color[record.meego_status] : Status2Color[status ? status : 'OPEN']}
            >
              {record.meego_status ? MeegoStatus2Name[record.meego_status] : MeegoStatus2Name[status ? status : 'OPEN']}
            </Tag>
          </Space>
          <Space>
            <Button href={url} style={{ color: 'black' }} target={'_blank'}>
              跳转
            </Button>
            <Select
              defaultValue={
                record.meego_level !== undefined ? record.meego_level : priority !== undefined ? priority : 3
              }
              onChange={checked => {
                UpdateMeegoLevel({
                  data: {
                    meego_url: url,
                    priority: checked,
                    platform: record.platform,
                    version_code: record.version_code,
                    operator: userSettings.info.email,
                  },
                }).then(v => {
                  if (v.code === 'success') {
                    message.success('更改优先级成功');
                  } else {
                    message.error('更新优先级失败');
                  }
                });
                console.log(checked);
              }}
            >
              <Option value={0}>P0</Option>
              <Option value={1}>P1</Option>
              <Option value={2}>P2</Option>
              <Option value={3}>P3</Option>
            </Select>
          </Space>
        </Space>
      )}
      <Button
        loading={joinLoading}
        onClick={() => {
          console.log('record 信息:', record);
          console.log(record.crash_type);
          console.log(CrashType2Url[record.crash_type]);
          setJoinLoading(true);
          AutoJoinGroupChat({
            data: {
              aid: record.aid,
              operator: userSettings.info.email,
              issue_id: record.issue_id,
              crashType: CrashType2Url[record.crash_type],
              platform: record.platform,
              title: ' ',
            },
          }).then(res => {
            if (res && res.code === NetworkCode.Success) {
              message.success('已加入');
              window.open(`https://applink.feishu.cn/client/chat/open?openChatId=${res.chatId}`);
            } else {
              message.error('请刷新重试');
            }
            setJoinLoading(false);
          });
        }}
      >
        加群
      </Button>
    </Space>
  );
};

export default MeegoCreator;
