import React, { useEffect } from 'react';
import { querySlardarValue } from '@api/slardar';
import {
  CrashType2Url,
  getSlardarIssueParams,
  getSlardarLagParams,
  getSlardarUrl,
} from '@shared/typings/slardar/common';
import { useModel } from '@edenx/runtime/model';
import AppSettingModule from '@/model/appSettingModel';
import { fromPairs, groupBy, head, last, maxBy, range, toPairs } from 'lodash';
import BaseChartCard from '@/component/Charts/BaseChart/baseChartCard';
import { UserCountType } from '@shared/typings/chart';
import { gen_converter } from '@/component/Charts/shared';
import { DeviceLevel } from '@shared/common';
import { PlatformType } from '@pa/shared/dist/src/core';

interface SlardarChartProps {
  title: string;
  slardarInfoId: string;
  versions: (string | undefined)[];
  timeRange: [number, number];
  isAverage: boolean;
  baseTimestamp?: number;
  deviceLevel: DeviceLevel;
  aspectRatio?: number;
}

export interface SlardarValueType {
  hourFromVersion: number;
  value: number;
  version: string;
}

const SlardarChart: React.FC<SlardarChartProps> = ({
  title,
  slardarInfoId,
  versions,
  timeRange,
  isAverage,
  baseTimestamp,
  deviceLevel,
  aspectRatio,
}) => {
  const [appSettingState] = useModel(AppSettingModule);
  const fetchData = async (): Promise<[SlardarValueType[], UserCountType[]]> => {
    const data = await querySlardarValue({
      data: {
        aid: appSettingState.info.businessInfo.aid,
        slardarInfoId,
        versionCodes: versions.map(it => it?.toString() || '0'),
        baseVersion: head(versions)?.toString(),
        baseTimestamp,
        deviceLevel,
      },
    });
    const values = toPairs(groupBy(data.ValueList, it => it.VersionCode))
      .map(([v, d]) => {
        const maxHour = maxBy(d, 'HourFromVersion')?.HourFromVersion ?? 0;
        const valMap = fromPairs(d.map(it => [it.HourFromVersion, it.Value]));
        return range(1, 1 + maxHour).reduce((ret, cur) => {
          ret.push({
            hourFromVersion: cur,
            version: v,
            value: isAverage ? ((last(ret)?.value ?? 0) * (cur - 1) + (valMap[cur] ?? 0)) / cur : valMap[cur] ?? 0,
          });
          return ret;
        }, [] as SlardarValueType[]);
      })
      .flat();
    const counts = toPairs(groupBy(data.CountList, it => it.version))
      .map(([v, d]) => {
        const maxHour = maxBy(d, 'hourFromVersion')?.hourFromVersion ?? 0;
        const valMap = fromPairs(d.map(it => [it.hourFromVersion, it.count]));
        return range(1, 1 + maxHour).map(cur => ({
          hourFromVersion: cur,
          count: valMap[cur] ?? 0,
          version: v,
        }));
      })
      .flat();
    return [values, counts];
  };

  const get_title_link = () => {
    const [start_time, end_time] = timeRange;

    if (['Lag', 'SeriousLag'].includes(slardarInfoId)) {
      return getSlardarUrl(
        appSettingState.info.businessInfo.aid,
        CrashType2Url[slardarInfoId],
        appSettingState.info.platform.toString(),
        `perf_v2/smooth_v2`,
        getSlardarLagParams(start_time, end_time, versions[0]?.toString() || ''),
      );
    }

    return getSlardarUrl(
      appSettingState.info.businessInfo.aid,
      CrashType2Url[slardarInfoId],
      appSettingState.info.platform.toString(),
      `abnormal_list`,
      getSlardarIssueParams(
        start_time,
        end_time,
        head(versions)?.toString() || '',
        CrashType2Url[slardarInfoId],
        appSettingState.info.platform === PlatformType.iOS ? 'iOS' : 'Android',
        appSettingState.info.name === '剪映',
        ['JavaOOM', 'NativeOOM', 'OOMCrash'].includes(slardarInfoId),
      ),
    );
  };

  return (
    <BaseChartCard
      title={title}
      titleLink={get_title_link()}
      dataSource={fetchData}
      version_list={versions}
      colour_list={['#5C90F9', '#5AD8A5', '#F7BE14']}
      line_converter={gen_converter('hourFromVersion', 'value', 'version')}
      bar_converter={gen_converter('hourFromVersion', 'count', 'version')}
      config={{
        xAxis: {
          minLimit: 0,
          maxLimit: 72,
        },
      }}
      is_percent={[true, false]}
      aspect_ratio={aspectRatio}
    />
  );
};

export default SlardarChart;
