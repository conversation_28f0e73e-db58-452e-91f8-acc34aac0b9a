import React from 'react';
import { getTeaGrayMetricValue } from '@api/tea';
import { AlarmVersion, TeaGranularity, TeaValueType } from '@shared/typings/tea/metric';
import { generate_sorter, get_day_start, is_day_start } from '@shared/utils/tools';
import { isCN } from '@/utils/region';
import { useModel } from '@edenx/runtime/model';
import AppSettingModule from '@/model/appSettingModel';
import BaseChartCard from '@/component/Charts/BaseChart/baseChartCard';
import { DBTeaMetric } from '../../../../api/model/TeaMetricTable';
import { buildTeaUrl } from '@shared/utils/tea';
import { DBSlardarUserCount } from '../../../../api/model/SlardarUserCountTable';
import { ChartDataPoint } from '@/component/Charts/shared';

interface TeaVersionedChartProps {
  title: string;
  teaMetric: DBTeaMetric;
  versions: [AlarmVersion, AlarmVersion];
  granularity: TeaGranularity;
  userCount: DBSlardarUserCount[];
}

const TeaVersionedChart: React.FC<TeaVersionedChartProps> = ({
  title,
  teaMetric,
  versions,
  granularity,
  userCount,
}) => {
  const [appSettingState] = useModel(AppSettingModule);

  const get_base = (timestamp: number) =>
    granularity === TeaGranularity.HOUR_24
      ? get_day_start(timestamp, isCN(appSettingState.info) ? 8 : 0)
      : timestamp - (timestamp % 3600);

  const fetchData = async (): Promise<[ChartDataPoint[], ChartDataPoint[]]> => {
    const data = await getTeaGrayMetricValue({
      data: {
        versions: versions.map(it => it.versionCode),
        metric_ids: [teaMetric.Name],
        granularity,
      },
    });

    const validData = data.filter(it =>
      granularity === TeaGranularity.HOUR_24 ? is_day_start(it.timestamp, isCN(appSettingState.info) ? 8 : 0) : true,
    );

    // convert to relative timestamp
    const rData = validData
      .map(it => ({
        x: Math.ceil(
          (it.timestamp - get_base(versions.find(v => v.versionCode === it.versionCode)!.timestamp)) /
            (granularity * 60),
        ).toString(),
        y: Number(it.value),
        series: it.versionCode ?? '',
      }))
      .filter(it => Number(it.x) > 0 && Number(it.x) <= 72)
      .sort(generate_sorter('series', 'x'));

    const maxX = Math.max(...rData.map(it => parseInt(it.x, 10)));

    const validTimestamp = validData.map(it => it.timestamp);
    const uC = userCount
      .filter(it => validTimestamp.includes(it.timestamp))
      .map(it => ({
        x: Math.ceil(
          (it.timestamp - get_base(versions.find(v => v.versionCode === it.version_code)!.timestamp)) /
            (granularity * 60),
        ).toString(),
        y: Number(it.count),
        series: it.version_code ?? '',
      }))
      .filter(it => Number(it.x) > 0 && Number(it.x) <= Math.max(72, maxX))
      .sort(generate_sorter('series', 'x'));

    return [rData, uC];
  };

  return (
    <BaseChartCard
      title={title}
      titleLink={buildTeaUrl(teaMetric.IsNewTea, teaMetric.TeaAppId, teaMetric.TeaId, teaMetric.TeaIdType)}
      dataSource={fetchData}
      version_list={versions.map(it => it.versionCode)}
      line_converter={it => it}
      bar_converter={it => it}
      colour_list={['#5C90F9', '#5AD8A5']}
      is_percent={[[TeaValueType.SUCCESS_RATE, TeaValueType.FAILURE_RATE].includes(teaMetric.ValueType), false]}
    />
  );
};

export default TeaVersionedChart;
