import { KeysMatching } from '@shared/utils/tools';

export interface ChartRequiredProps<T> {
  data: T;
  isLoading: boolean;
}

export interface ChartProps<T> {
  title: string;
  titleLink: string;
  dataSource: () => Promise<T>;
}

export interface ChartDataPoint {
  x: string | number;
  y: number;
  series?: string;
}

export const number_formatter = (x: number, is_percent: boolean): string => {
  if (is_percent) {
    const disp = x * 1000;
    if (Math.abs(disp) < 1e-6) {
      return '0‰';
    }
    const digits = Math.max(Math.min(Math.ceil(-Math.log10(disp)), 5), 0);
    const converted = Number(disp.toFixed(digits));
    return `${disp.toFixed(digits + (Math.abs(converted - disp) < 1e-6 ? 0 : 1))}‰`;
  }
  if (x >= 10000) {
    const disp = x / 1000;
    return disp < 1000 ? `${disp.toFixed(disp < 10 ? 1 : 0)}k` : `${(disp / 1000).toFixed(disp < 10000 ? 1 : 0)}M`;
  }
  return x.toFixed(Math.abs(Math.round(x) - x) < 1e-3 ? 0 : 2);
};
export const gen_converter =
  <T>(
    x: Extract<keyof T, KeysMatching<T, number | string>>,
    y: Extract<keyof T, KeysMatching<T, number>>,
    series: Extract<keyof T, KeysMatching<T, string>>,
  ): ((t: T) => ChartDataPoint) =>
  (t: T) => ({
    x: (t[x] as number | string).toString(),
    y: t[y] as number,
    series: t[series] as string,
  });
