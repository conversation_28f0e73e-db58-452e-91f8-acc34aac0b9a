import { ChartDataPoint, ChartRequiredProps, number_formatter } from '@/component/Charts/shared';
import { BaseLineChartAdditionalProps } from '@/component/Charts/BaseLineChart/shared';
import { compact, last, some } from 'lodash';
import { Datum } from '@antv/g2/lib/interface';
import { Line, LineConfig } from '@ant-design/charts';
import { TooltipItem } from '@antv/g2/src/interface';
import { generate_sorter, get_axis_range } from '@shared/utils/tools';

export type BaseLineChartProps<T> = ChartRequiredProps<T[]> & BaseLineChartAdditionalProps<T>;

export const BaseLineChart = <T,>({
  data,
  isLoading,
  version_list,
  line_converter,
  config,
  colour_list,
  is_percent,
}: BaseLineChartProps<T>) => {
  const line_data = data.map(line_converter).sort(generate_sorter('x'));
  const minVer = last(compact(version_list)) ?? '';
  const colorFunc = (datum: Datum) => colour_list.at(version_list.findIndex(it => it === datum.series)) ?? '#000000';

  const [max_lim, min_lim] = get_axis_range(line_data.map(it => it.y));
  const has_series = some(line_data, it => it.series);

  const default_config: LineConfig = {
    data: line_data,
    xField: 'x',
    yField: 'y',
    loading: isLoading,
    autoFit: true,
    color: colorFunc,
    smooth: true,
    yAxis: {
      min: min_lim,
      max: max_lim,
      label: {
        formatter: (v: string) => number_formatter(Number(v), is_percent),
      },
    },
    tooltip: {
      customItems: (ti: TooltipItem[]) => {
        const noDiff = ti.length <= 1;
        const compVal = ti.map(it => it.data as ChartDataPoint).find(it => it.series === minVer)?.y;
        return ti
          .map((it): TooltipItem => {
            const fixed = {
              data: it.data,
              name: it.name,
              color: colour_list.at(version_list.findIndex(v => v === it.data.series)) ?? '#000000',
              marker: it.marker,
              mappingData: it.mappingData,
            };
            const val = Number(it.value);
            if (it.name === minVer || noDiff) {
              return {
                ...fixed,
                value: number_formatter(val, is_percent),
              };
            }
            const delta = val / (compVal ?? -1) - 1;
            return {
              ...fixed,
              value: `${number_formatter(val, is_percent)} (${delta <= 0 ? '' : '+'}${(delta * 100.0).toFixed(2)}%)`,
            };
          })
          .sort((a, b) => {
            const cmp = Number((b.value as string).includes('‰')) - Number((a.value as string).includes('‰'));
            return cmp !== 0 ? cmp : b.name.localeCompare(a.name);
          });
      },
    },
  };
  if (has_series) {
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-ignore
    default_config.seriesField = 'series';
  }
  const final_config = {
    ...default_config,
    ...config,
  };
  return <Line style={{ width: '100%', height: '100%' }} {...final_config} />;
};
