import React, { useState } from 'react';
import { DBTeaMetric } from '../../../../api/model/TeaMetricTable';
import { DBTeaMetricValue } from '../../../../api/model/TeaMetricValueTable';
import { queryTeaOverallChartValues } from '@api/tea';
import dayjs from 'dayjs';
import { Divider, Flex, message, Space } from 'antd';
import LazyLoading from '@/component/LazyLoading/lazyLoading';
import { StatisticCard } from '@ant-design/pro-components';
import { TeaValueType, TeaValueUnit } from '@shared/typings/tea/metric';
import { BaseLineChart } from '@/component/Charts/BaseLineChart/baseLineChart';
import { generate_sorter } from '@shared/utils/tools';
import { buildTeaUrl } from '@shared/utils/tea';
import UserShow from '@/component/UserShow';

const { Statistic } = StatisticCard;

export interface TeaOverallChartProps {
  teaMetric: DBTeaMetric;
}

const TeaOverallChart: React.FC<TeaOverallChartProps> = ({ teaMetric }) => {
  const [points, setPoints] = useState<DBTeaMetricValue[]>([]);
  const [current, setCurrent] = useState(0);
  const [previous, setPrevious] = useState(0);
  const [loading, setLoading] = useState(true);

  const is_percent = [TeaValueType.SUCCESS_RATE, TeaValueType.FAILURE_RATE].includes(teaMetric.ValueType);

  const fetchData = async () => {
    setLoading(true);
    const resp = await queryTeaOverallChartValues({
      params: {
        metricId: teaMetric.Name,
        timestamp: dayjs().unix().toString(),
      },
    });

    if (resp.data) {
      setPoints(resp.data.points.sort(generate_sorter('timestamp')));
      setCurrent(resp.data.current);
      setPrevious(resp.data.previous);
      setLoading(false);
    } else {
      message.error(`Error occurred when loading ${teaMetric.DisplayName}, ${resp.message}`);
    }
  };

  return (
    <LazyLoading dataLoadTrigger={fetchData}>
      <Flex align="center" style={{ height: '240px', border: '1px solid #ccc', borderRadius: '12px' }}>
        <div style={{ width: 'max-content', height: '200px' }}>
          <StatisticCard
            chartPlacement={'right'}
            statistic={{
              title: (
                <a
                  href={buildTeaUrl(teaMetric.IsNewTea, teaMetric.TeaAppId, teaMetric.TeaId, teaMetric.TeaIdType)}
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  {teaMetric.DisplayName}
                </a>
              ),
              value: is_percent ? current * 1000 : current,
              precision: 2,
              suffix: TeaValueUnit[teaMetric.ValueType],
              description: (
                <Space size={'small'} direction={'vertical'}>
                  <Space size={'small'} direction={'horizontal'}>
                    上周同期：
                    {`${(previous * (is_percent ? 1000 : 1)).toFixed(2)} ${TeaValueUnit[teaMetric.ValueType]}`}
                  </Space>
                  <Statistic
                    title="周同比"
                    value={`${((current / previous - 1) * 100).toFixed(3)}%`}
                    trend={current > previous ? 'up' : 'down'}
                  />
                  <UserShow email={teaMetric.POC} />
                </Space>
              ),
            }}
          />
        </div>
        <Divider type={'vertical'} style={{ height: '200px' }} />
        <div style={{ flexGrow: 1, width: 1, height: '200px', padding: '0 1% 0 1%' }}>
          <BaseLineChart
            data={points}
            line_converter={it => ({
              x: it.timestamp * 1000,
              y: Number(it.value),
              // series: "大盘",
            })}
            isLoading={loading}
            colour_list={['#3286EC']}
            is_percent={is_percent}
            version_list={[]}
            config={{
              xAxis: {
                type: 'time',
              },
            }}
          />
        </div>
      </Flex>
    </LazyLoading>
  );
};

export default TeaOverallChart;
