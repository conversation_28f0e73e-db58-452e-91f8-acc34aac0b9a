import React, { useState } from 'react';
import { Button, Modal, Space } from 'antd';
import { RedoOutlined, ZoomInOutlined } from '@ant-design/icons';
import { ProCard } from '@ant-design/pro-components';
import { BaseChart, BaseChartProps } from '@/component/Charts/BaseChart/baseChart';
import LazyLoading from '@/component/LazyLoading/lazyLoading';
import { ChartProps } from '@/component/Charts/shared';
import { BaseChartAdditionalProps } from '@/component/Charts/BaseChart/shared';

export type BaseChartCardProps<T, U> = ChartProps<[T[], U[]]> & BaseChartAdditionalProps<T, U>;

const BaseChartCard = <T, U>({ title, titleLink, dataSource, aspect_ratio, ...rest }: BaseChartCardProps<T, U>) => {
  const [tData, setTData] = useState<T[]>([]);

  const [uData, setUData] = useState<U[]>([]);

  // state of loading
  const [isLoading, setIsLoading] = useState(true);

  const [modalVisible, setModalVisible] = useState(false);

  const openModal = () => {
    setModalVisible(true);
  };

  const closeModal = () => {
    setModalVisible(false);
  };

  // data loading func
  const alterData = async () => {
    setIsLoading(true);
    const [td, ud] = await dataSource();
    setTData(td);
    setUData(ud);
    setIsLoading(false);
  };

  const chartProps: BaseChartProps<T, U> = {
    data: [tData, uData],
    isLoading,
    ...rest,
  };

  const proCardProps = {
    bordered: true,
    headerBordered: true,
    title: (
      <a href={titleLink} target="_blank" rel="noreferrer">
        {title}
      </a>
    ),
    extra: (
      <Space direction={'horizontal'}>
        <Button onClick={openModal}>
          <ZoomInOutlined />
        </Button>
        <Button onClick={alterData}>
          <RedoOutlined />
        </Button>
      </Space>
    ),
  };

  return (
    <LazyLoading dataLoadTrigger={alterData}>
      <ProCard {...proCardProps} size="small">
        <div style={{ aspectRatio: aspect_ratio ?? 13 / 7, height: '100%' }}>
          <BaseChart {...chartProps} />
        </div>
        <Modal
          width={'80%'}
          open={modalVisible}
          onCancel={closeModal}
          footer={null}
          title={
            <a href={titleLink} target="_blank" rel="noreferrer">
              {title}
            </a>
          }
        >
          <div style={{ aspectRatio: 16 / 7, height: '100%', width: '100%' }}>
            <BaseChart {...chartProps} />
          </div>
        </Modal>
      </ProCard>
    </LazyLoading>
  );
};
export default BaseChartCard;
