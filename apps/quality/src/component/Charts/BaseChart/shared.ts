import { DualAxesConfig } from '@ant-design/charts';
import { FixedLengthArray } from '@shared/utils/tools';
import { ChartDataPoint } from '@/component/Charts/shared';

export interface BaseChartAdditionalProps<T, U> {
  version_list: (string | undefined)[];
  line_converter: (t: T) => ChartDataPoint;
  bar_converter: (u: U) => ChartDataPoint;
  colour_list: string[];
  is_percent: FixedLengthArray<boolean, 2>;
  config?: Partial<DualAxesConfig>;
  aspect_ratio?: number;
}
