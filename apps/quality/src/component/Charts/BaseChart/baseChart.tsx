import React from 'react';
import { compact, fromPairs, groupBy, last, maxBy, some, toPairs } from 'lodash';
import { Datum } from '@antv/g2/lib/interface';
import { DualAxes, DualAxesConfig } from '@ant-design/charts';
import { TooltipItem } from '@antv/g2/src/interface';
import { ChartDataPoint, ChartRequiredProps, number_formatter } from '@/component/Charts/shared';
import { BaseChartAdditionalProps } from '@/component/Charts/BaseChart/shared';
import { generate_sorter } from '@shared/utils/tools';

interface ChartDataPointLine {
  x: string;
  yl: number;
  series?: string;
}

const line_conv = (p: ChartDataPoint): ChartDataPointLine => ({
  x: p.x.toString(),
  yl: p.y,
  series: p.series,
});

interface ChartDataPointBar {
  x: string;
  yb: number;
  series?: string;
}

const bar_conv = (p: ChartDataPoint): ChartDataPointBar => ({
  x: p.x.toString(),
  yb: p.y,
  series: p.series,
});

export type BaseChartProps<T, U> = ChartRequiredProps<[T[], U[]]> & BaseChartAdditionalProps<T, U>;

export const BaseChart = <T, U>({
  data: [line_data_raw, bar_data_raw],
  isLoading,
  version_list,
  line_converter,
  bar_converter,
  config,
  colour_list,
  is_percent: [line_percent, bar_percent],
}: BaseChartProps<T, U>) => {
  const bar_data = bar_data_raw.map(bar_converter).map(bar_conv).sort(generate_sorter('x'));
  const line_data = line_data_raw.map(line_converter).map(line_conv).sort(generate_sorter('x'));

  const minVer = last(compact(version_list)) ?? '';
  const has_series = some(bar_data, it => it.series) || some(line_data, it => it.series);

  const colorFunc = (datum: Datum) => colour_list.at(version_list.findIndex(it => it === datum.series)) ?? '#000000';

  const maxCnt = maxBy(bar_data, 'yb')?.yb ?? 0;
  const default_config: DualAxesConfig = {
    data: [line_data, bar_data],
    xField: 'x',
    yField: ['yl', 'yb'],
    loading: isLoading,
    autoFit: true,
    yAxis: [
      {
        label: {
          formatter: (v: string) => number_formatter(Number(v), line_percent),
        },
      },
      {
        min: 0,
        max: 4.75 * maxCnt,
        label: {
          formatter: (v: string) => number_formatter(Number(v), bar_percent),
        },
      },
    ],
    geometryOptions: [
      {
        geometry: 'line',
        color: colorFunc,
        smooth: true,
      },
      {
        geometry: 'column',
        isGroup: true,
        columnWidthRatio: 0.7,
        color: colorFunc,
      },
    ],
    tooltip: {
      customItems: (ti: TooltipItem[]) => {
        const noDiff = ti.length <= 1;
        const compVal = fromPairs(
          toPairs(
            groupBy(
              ti.map(it => it.data as ChartDataPointBar | ChartDataPointLine),
              it => ('yl' in it ? 'yl' : 'yb'),
            ),
          )
            .map(
              ([k, v]) =>
                [k, v.find(it => it.series === minVer)] as [string, ChartDataPointLine | ChartDataPointBar | undefined],
            )
            .filter(([, v]) => v !== undefined)
            .map(([k, v]) => [k, v![k as keyof typeof v] as number]),
        );
        return ti
          .map((it): TooltipItem => {
            const fixed = {
              data: it.data,
              name: it.name,
              color: colour_list.at(version_list.findIndex(v => v === it.data.series)) ?? '#000000',
              marker: it.marker,
              mappingData: it.mappingData,
            };
            const val = Number(it.value);
            if (it.name === minVer || noDiff) {
              return {
                ...fixed,
                value: number_formatter(val, 'yl' in it.data ? line_percent : bar_percent),
              };
            }
            const delta = val / compVal['yl' in it.data ? 'yl' : 'yb'] - 1;
            return {
              ...fixed,
              value: `${number_formatter(val, 'yl' in it.data ? line_percent : bar_percent)} (${delta <= 0 ? '' : '+'}${(
                delta * 100.0
              ).toFixed(2)}%)`,
            };
          })
          .sort((a, b) => {
            const cmp = 'yl' in a.data === 'yl' in b.data ? 0 : 'yl' in a.data ? -1 : 1;
            return cmp !== 0 ? cmp : b.name.localeCompare(a.name);
          });
      },
    },
  };
  if (has_series) {
    default_config.geometryOptions?.forEach(it => {
      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
      // @ts-ignore
      it.seriesField = 'series';
    });
  }
  const final_config: DualAxesConfig = {
    ...default_config,
    ...config,
  };
  return <DualAxes {...final_config} />;
};
