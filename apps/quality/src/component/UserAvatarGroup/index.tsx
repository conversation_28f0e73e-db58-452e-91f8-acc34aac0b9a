import HoverCardInfo from '@/component/UserCard/HoverCardInfo';
import * as React from 'react';

import { Avatar, AvatarGroup, Popover } from '@douyinfe/semi-ui';
import { User } from '@pa/shared/dist/src/core';
import UserCard from '@/component/UserCard';

const SemiReactUserGroup: React.FC<{ users: User[]; triggerType: 'click' | 'hover' }> = ({ users, triggerType }) =>
  users.length === 0 ? (
    <></>
  ) : users.length === 1 ? (
    <UserCard
      email={users[0].email}
      simpleUserData={{
        avatarUrl: typeof users[0].avatar === 'string' ? users[0].avatar : users[0].avatar?.avatar_240,
        name: users[0].name,
      }}
      triggerType="hover"
    />
  ) : (
    <AvatarGroup maxCount={4} size={'small'}>
      {users.map((user, index) => (
        <Popover
          style={{
            background: 'transparent',
            width: '280px',
            height: 'fit-content',
          }}
          trigger={triggerType}
          content={<HoverCardInfo email={user.email} />}
          position="rightTop"
        >
          <Avatar
            size={'small'}
            key={user.open_id}
            alt={user.name}
            src={typeof user?.avatar === 'string' ? user?.avatar : user?.avatar?.avatar_240} // 使用用户的avatar URL，如果没有则不显示
          />
        </Popover>
      ))}
    </AvatarGroup>
  );

export default SemiReactUserGroup;
