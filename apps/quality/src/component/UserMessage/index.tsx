import { Space } from 'antd';
import React, { useEffect, useState } from 'react';
import { Avatar, User } from '@pa/shared/dist/src/core';
import { getUserInfoByEmails } from '@api/index';
import UserCard from '@/component/UserCard';

interface Props {
  emails: string[];
}

const UserMessage: React.FC<Props> = ({ emails }) => {
  const createUserView = (user: User) => {
    const avatar = user.avatar as Avatar;
    return (
      <>
        <UserCard
          email={`${user.email}`}
          simpleUserData={{
            // name: user.name_cn,
            avatarUrl: avatar.avatar_72, // avatar_url,
          }}
          hideName={true}
          triggerType="hover"
        />
      </>
    );
  };
  const [users, setUsers] = useState<User[]>([]);
  const [loaded, setLoaded] = useState<boolean>(false);
  useEffect(() => {
    getUserInfoByEmails({ data: { emails } }).then(res => {
      if (res) {
        setUsers(res);
        setLoaded(true);
      }
    });
  }, []);
  return loaded ? (
    <>
      <Space size={6}>{users.map(user => createUserView(user))}</Space>
    </>
  ) : (
    <></>
  );
};

export default UserMessage;
