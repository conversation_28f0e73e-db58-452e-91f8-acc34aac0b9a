import { User } from '@pa/shared/dist/src/core';
import { getUserInfoByEmails } from '@api/index';
import { UserGlobalMemoryCache, UserGlobalRedisCache } from '@/component/UserShow/GlobalUserCache';
import { IAsyncCache } from '@/component/UserShow/GlobalCache';
import { logger } from '@/pages/quality/metric/version/utils/Logger';

const USE_MEMORY_EXPIRE_TIME = 30 * 60 * 1000; // 30分钟缓存有效期
const USE_REDIS_EXPIRE_TIME = 7 * 24 * 60 * 60; // 一周缓存有效期
const WAIT_TIME = 1000; // 用来做重复请求合并,当有相同的请求时候,delay一下
const REQUEST_TIMEOUT = 1500; // 请求超时后清除请求记录
const requestRecords = new Set<string>();

function delay(ms: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms));
}

// 发起action, 超时或者请求成功
async function request<T>(action: () => T, clear: () => void, expireTimeout: number): Promise<T> {
  const timer = setTimeout(() => {
    clear();
  }, expireTimeout);
  const result = await action();
  clearTimeout(timer);
  clear();
  return result;
}

export enum UserCacheType {
  redisOnly,
  memoryOnly,
}

interface UserCacheConfig {
  expire_time: number;
}

interface UserCacheHolder {
  userCacheImpl: IAsyncCache<User>;
  config: UserCacheConfig;
}

function getGlobalUserCacheHolder(cacheType: UserCacheType): UserCacheHolder {
  switch (cacheType) {
    case UserCacheType.memoryOnly:
      return {
        userCacheImpl: UserGlobalMemoryCache.getInstance(),
        config: { expire_time: USE_REDIS_EXPIRE_TIME },
      };
    case UserCacheType.redisOnly:
    default:
      return {
        userCacheImpl: UserGlobalRedisCache.getInstance(),
        config: { expire_time: USE_MEMORY_EXPIRE_TIME },
      };
  }
}

export async function getUser(
  email: string,
  cacheType: UserCacheType = UserCacheType.redisOnly,
): Promise<User | undefined> {
  if (!email || email.length === 0) {
    return;
  }

  const globalCacheHolder = getGlobalUserCacheHolder(cacheType);
  const globalCache = globalCacheHolder.userCacheImpl;
  async function getUserCache(): Promise<User | undefined> {
    return globalCache.get(email);
  }

  // 获取缓存,如果缓存
  const userCache = await getUserCache();
  if (userCache) {
    logger.debug(`use cache user email => ${email}`);
    return userCache;
  }

  // 用于合并请求,如果有正在发起的请求,delay一下,等待缓存写入
  if (requestRecords.has(email)) {
    logger.debug(`${email} is requesting, delay ${WAIT_TIME}ms`);
    await delay(WAIT_TIME);

    // 再次尝试获取缓存,如果缓存已经写入db
    const userCacheAgain = await getUserCache();
    if (userCacheAgain) {
      logger.debug(`use cache user email => ${email}`);
      return userCacheAgain;
    }
  }

  return await request(
    async () => {
      requestRecords.add(email);
      logger.info(`getUserInfoByEmails user email => ${email}`);
      const users = await getUserInfoByEmails({ data: { emails: [email] } });
      if (users && users.length >= 1) {
        await globalCache.set(email, users[0], globalCacheHolder.config.expire_time);
        return users[0];
      }
    },
    () => requestRecords.delete(email),
    REQUEST_TIMEOUT,
  );
}

export async function getUsers(
  emails: string[],
  cacheType: UserCacheType = UserCacheType.redisOnly,
): Promise<User[] | undefined> {
  const users = await Promise.all(emails.map(email => getUser(email, cacheType)));
  return users.filter((u): u is User => u !== undefined);
}
