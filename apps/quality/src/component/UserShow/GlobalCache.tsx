type CacheItem<T> = {
  data: T;
  expiry: number;
};

export interface IAsyncCache<T> {
  set: (key: string, data: T, ttl: number) => void;
  get: (key: string) => Promise<T | undefined>;
  delete: (key: string) => void;
  clear: () => void;
}

export class GlobalCache<T> implements IAsyncCache<T> {
  private cache: Map<string, CacheItem<any>>;

  constructor() {
    this.cache = new Map();
  }

  // 设置缓存项
  async set(key: string, data: T, ttl: number): Promise<void> {
    const expiry = Date.now() + ttl;
    this.cache.set(key, { data, expiry });
  }

  // 获取缓存项
  async get(key: string): Promise<T | undefined> {
    const item = this.cache.get(key);

    if (item) {
      if (Date.now() < item.expiry) {
        return item.data as T;
      } else {
        this.cache.delete(key); // 缓存过期，删除缓存项
      }
    }
  }

  // 删除缓存项
  delete(key: string): void {
    this.cache.delete(key);
  }

  // 清空缓存
  clear(): void {
    this.cache.clear();
  }
}
