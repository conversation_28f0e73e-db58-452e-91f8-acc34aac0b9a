import React, { useEffect, useState } from 'react';
import { Avatar, User } from '@pa/shared/dist/src/core';
import { getUserInfoByEmails } from '@api/index';
import UserCard from '@/component/UserCard';
import { Skeleton } from 'antd';
import { getUser } from '@/component/UserShow/UserCache';
interface UserShowProps {
  email: string;
}

const UserShow: React.FC<UserShowProps> = ({ email }) => {
  const [user, setUser] = useState<User>();
  const [loaded, setLoaded] = useState<boolean>(false);
  const useCache = false;

  useEffect(() => {
    // 使用缓存的情况下
    if (useCache) {
      getUser(email).then(u => {
        setUser(u);
        setLoaded(true);
      });
    } else {
      getUserInfoByEmails({ data: { emails: [email] } }).then(res => {
        if (res && res.length >= 1) {
          setUser(res[0]);
          setLoaded(true);
        }
      });
    }
  }, []);

  return loaded && user ? (
    <UserCard
      email={user.email}
      simpleUserData={{
        name: user.name,
        avatarUrl: (user.avatar as Avatar)?.avatar_72,
      }}
      hideName={false}
      triggerType={'hover'}
    />
  ) : (
    <Skeleton.Button active={true} size={'default'} />
  );
};

export default UserShow;
