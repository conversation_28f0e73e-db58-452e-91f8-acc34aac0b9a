import { GlobalCache, IAsyncCache } from '@/component/UserShow/GlobalCache';
import { User } from '@pa/shared/dist/src/core';
import { deleteAllLarkUser, deleteLarkUser, getLarkUser, setLarkUser } from '@api/larkUserCache';

// 内存缓存实现
export class UserGlobalMemoryCache extends GlobalCache<User> {
  private static instance: UserGlobalMemoryCache;

  private constructor() {
    super();
  }
  // 获取单例实例
  static getInstance(): UserGlobalMemoryCache {
    if (!UserGlobalMemoryCache.instance) {
      UserGlobalMemoryCache.instance = new UserGlobalMemoryCache();
    }
    return UserGlobalMemoryCache.instance;
  }
}

//  redis缓存
export class UserGlobalRedisCache implements IAsyncCache<User> {
  private static instance: UserGlobalRedisCache;
  private constructor() {}

  // 获取单例实例
  static getInstance(): UserGlobalRedisCache {
    if (!UserGlobalRedisCache.instance) {
      UserGlobalRedisCache.instance = new UserGlobalRedisCache();
    }
    return UserGlobalRedisCache.instance;
  }

  serialize(user: User): string {
    return JSON.stringify(user);
  }

  deserialize(userStr: string | null): User | undefined {
    return userStr && JSON.parse(userStr);
  }

  async set(email: string, user: User, ttl: number): Promise<void> {
    try {
      console.debug(`UserRedisCache => set:${user.email}`);
    } catch (e) {
      console.log(e);
    }
    await setLarkUser({ data: { key: email, user: this.serialize(user), ttl } });
  }

  async get(email: string): Promise<User | undefined> {
    const userStr: string | undefined = await getLarkUser({ data: { key: email } });
    if (userStr) {
      try {
        console.debug(`UserRedisCache => deserialize: email ${email}`);
        return this.deserialize(JSON.stringify(userStr));
      } catch (e) {
        console.log(e);
      }
    }
    return undefined;
  }

  async delete(email: string): Promise<void> {
    await deleteLarkUser({ data: { key: email } });
  }

  async clear(): Promise<void> {
    await deleteAllLarkUser({ data: {} });
  }
}
