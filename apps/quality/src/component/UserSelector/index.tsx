import { searchUser } from '@api/index';
import React, { useEffect, useImperativeHandle, useState } from 'react';
import { Avatar, Image, Select, Space } from 'antd';
import { InputStatus } from 'antd/lib/_util/statusUtils';
import { SizeType } from 'antd/es/config-provider/SizeContext';
import { User, UserList } from '@pa/shared/dist/src/core';
import { useModel } from '@edenx/runtime/model';
import UserSettingModule from '@/model/userSettingModel';

const { Option } = Select;

interface SelectorProps {
  onSelect: (value: User) => void;
  placeholder: string;
  status?: InputStatus;
  size?: SizeType;
  initialEmail?: string;
}

export interface UserSelectorRef {
  clearValue: () => void;
}

const UserSelector: React.ForwardRefRenderFunction<UserSelectorRef, SelectorProps> = (
  { onSelect, placeholder, status, size, initialEmail },
  ref,
) => {
  const [user, setUser] = useState<UserList>({ data: [] });
  const [selected, setSelected] = useState<number>();
  const [userSettingState] = useModel(UserSettingModule);

  useImperativeHandle(ref, () => ({
    clearValue: () => {
      setSelected(undefined);
    },
  }));

  let timeout: ReturnType<typeof setTimeout> | null = null;
  const search = (value: string, callback?: (u: User[] | undefined) => void | Promise<void>) => {
    if (value === '') {
      return;
    }
    if (timeout !== null) {
      clearTimeout(timeout);
    }
    timeout = setTimeout(() => {
      searchUser({ data: { value, token: userSettingState.info.access_token } })
        .then(ret => {
          setUser({ data: ret.data });
          return ret.data;
        })
        .then(u => {
          if (callback) {
            return callback(u);
          }
        });
    }, 300);
  };

  const onSelected = (value: number) => {
    if (user.data) {
      console.log('onSelected', user.data[value]);
      onSelect(user.data[value]);
      setSelected(value);
    }
  };

  useEffect(() => {
    if (initialEmail) {
      search(initialEmail, u => {
        setSelected(0);
        if (u) {
          onSelect(u[0]);
        }
      });
    }
  }, []);

  return (
    <Select
      showSearch
      style={{
        width: '100%',
      }}
      allowClear={true}
      size={size ? size : 'large'}
      status={status}
      value={selected}
      onSelect={onSelected}
      onSearch={search}
      placeholder={placeholder}
      filterOption={false}
    >
      {user.data?.map((item, index) => (
        <Option key={index} value={index}>
          <Space>
            {item.avatar ? <Avatar size="small" src={<Image src={(item.avatar as any).avatar_72} />} /> : <></>}
            <span>{item.name}</span>
          </Space>
        </Option>
      ))}
    </Select>
  );
};

export default React.forwardRef(UserSelector);
