import React, { useEffect, useState } from 'react';
import { DatePicker, Space, Switch, Tooltip } from 'antd';
import dayjs from 'dayjs';
import { IndicatorType, owners_emails } from '@shared/libra/common';
import { setExperimentSafe } from '@api/experiment';
import UserSettingModule from '@/model/userSettingModel';
import { useModel } from '@edenx/runtime/model';

const WhiteListBuilder: React.FC<{
  time: number | undefined;
  appId: number;
  indicatorType: IndicatorType;
  id: string;
  vids: string[];
  vid?: string;
  setUpdate: React.Dispatch<React.SetStateAction<number>>;
}> = ({ time, appId, indicatorType, id, vids, vid, setUpdate }) => {
  const [isOpen, setIsOpen] = useState(time ? time > new Date().valueOf() : false);
  const [selectedDate, setSelectedDate] = useState(time ? dayjs(time) : null);
  const [userSettingState] = useModel(UserSettingModule);
  const [disabled, setDisabld] = useState<boolean>(!owners_emails.includes(userSettingState.info.email));
  useEffect(() => {
    setDisabld(!owners_emails.includes(userSettingState.info.email));
  }, [userSettingState.info.email]);
  useEffect(() => {
    setIsOpen(time ? time > new Date().valueOf() : false);
    setSelectedDate(time ? dayjs(time) : null);
  }, [time]);
  const handleSwitchChange = (checked: boolean) => {
    if (!checked) {
      setExperimentSafe({
        data: {
          id,
          time: 0,
          appId,
          indicatorType,
          vids,
          vid,
        },
      }).then(v => {
        setUpdate(dayjs().valueOf());
      });
    }
    setIsOpen(checked);
  };

  const handleDateChange = (date: dayjs.Dayjs | null, dateString: string | string[]) => {
    if (date) {
      setExperimentSafe({
        data: {
          id,
          time: date.valueOf(),
          appId,
          indicatorType,
          vid,
          vids,
        },
      }).then(v => {
        setUpdate(dayjs().valueOf());
      });
    }
    setSelectedDate(date);
  };

  return (
    <Space>
      <Tooltip title={disabled ? '白名单设置咨询guozhi.kevin' : undefined}>
        <Switch checked={isOpen} onChange={handleSwitchChange} style={{ width: '100%' }} disabled={disabled} />
      </Tooltip>
      {isOpen && (
        <Tooltip title={disabled ? '白名单设置咨询guozhi.kevin' : undefined}>
          <DatePicker
            value={selectedDate}
            onChange={handleDateChange}
            style={{ width: isOpen ? '100%' : '0%' }}
            disabled={disabled}
          />
        </Tooltip>
      )}
    </Space>
  );
};
export default WhiteListBuilder;
