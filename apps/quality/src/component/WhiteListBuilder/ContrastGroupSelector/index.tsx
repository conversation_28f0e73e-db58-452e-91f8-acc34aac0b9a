import React, { useEffect, useState } from 'react';
import { Select, Space, Switch, Tooltip } from 'antd';
import { IndicatorType, owners_emails } from '@shared/libra/common';
import { setContrastExperimentGroup } from '@api/experiment';
import { useModel } from '@edenx/runtime/model';
import UserSettingModule from '@/model/userSettingModel';

const ContrastListSelector: React.FC<{
  defaultVid: string | undefined;
  appId: number;
  indicatorType: IndicatorType;
  id: string;
  vids: string[];
}> = ({ defaultVid, appId, indicatorType, id, vids }) => {
  const [isOpen, setIsOpen] = useState(Boolean(defaultVid));
  const [contrastGroup, setContrastGroup] = useState<string | undefined>(defaultVid);
  const [userSettingState] = useModel(UserSettingModule);
  const [disabled, setDisabld] = useState<boolean>(!owners_emails.includes(userSettingState.info.email));
  useEffect(() => {
    setDisabld(!owners_emails.includes(userSettingState.info.email));
  }, [userSettingState.info.email]);
  const handleSwitchChange = (checked: boolean) => {
    if (!checked) {
      setContrastExperimentGroup({
        data: {
          id,
          appId,
          indicatorType,
          contrastVid: undefined,
        },
      });
    }
    setIsOpen(checked);
  };

  useEffect(() => {
    if (isOpen && contrastGroup) {
      setContrastExperimentGroup({
        data: {
          id,
          appId,
          indicatorType,
          contrastVid: contrastGroup,
        },
      });
    }
  }, [contrastGroup]);
  return (
    <Space>
      <Tooltip title={disabled ? '白名单设置咨询guozhi.kevin' : undefined}>
        <Switch checked={isOpen} onChange={handleSwitchChange} style={{ width: '100%' }} disabled={disabled} />
      </Tooltip>
      {isOpen && (
        <Tooltip title={disabled ? '白名单设置咨询guozhi.kevin' : undefined}>
          <Select
            value={contrastGroup}
            style={{ width: 150 }}
            options={vids.map(v => ({ label: v, value: v }))}
            onChange={v => setContrastGroup(v)}
            disabled={disabled}
          />
        </Tooltip>
      )}
    </Space>
  );
};
export default ContrastListSelector;
