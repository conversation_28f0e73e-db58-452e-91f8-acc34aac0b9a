import React, { CSSProperties } from 'react';
import { message, TreeSelect } from 'antd';
import TreeValue from '@/component/treeValue';
import { VersionTypeToTag } from '@/utils/version';
import { VersionType } from '@shared/utils/version_utils';

export interface VersionSelectProps {
  data: TreeValue[];
  onChange: (value: string[] | string, label: React.ReactNode[], extra: any) => void;
  value: string[] | string;
  isSingle?: boolean;
  maxSelection?: number;
  style?: CSSProperties;
}

function addVersionTag(title: any, type?: VersionType) {
  return (
    <>
      {title} {VersionTypeToTag(type)}
    </>
  );
}

const VersionSelector: React.FC<VersionSelectProps> = ({ data, onChange, value, isSingle, maxSelection, style }) => {
  for (const version of data) {
    for (const versionCode of version.children) {
      versionCode.title = addVersionTag(versionCode.title, versionCode.type);
    }
  }
  return (
    <TreeSelect
      showSearch={false}
      placeholder={'请选择版本 ...'}
      allowClear
      onChange={(v, l, e) => {
        const len = [v].flat().length;
        if (!maxSelection || len <= maxSelection) {
          onChange(v, l, e);
        } else if (len > 2) {
          message.info(`选择多于2个版本时只展示数值波动！`);
        }
      }}
      treeData={data}
      treeLine
      popupMatchSelectWidth={false}
      treeCheckable={!isSingle}
      style={style ?? { width: '100%' }}
      value={value ? value : undefined}
    />
  );
};

export default VersionSelector;
