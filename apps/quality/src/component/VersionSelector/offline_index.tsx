import React, { CSSProperties } from 'react';
import { message, Tag, TreeSelect } from 'antd';
import TreeValue from '@/component/treeValue';

export interface VersionSelectProps {
  data: string[];
  onChange: (value: undefined | string, label: React.ReactNode[], extra: any) => void;
  value: undefined | string;
  isSingle?: boolean;
  maxSelection?: number;
  style?: CSSProperties;
}

const OfflineVersionSelector: React.FC<VersionSelectProps> = ({
  data,
  onChange,
  value,
  isSingle,
  maxSelection,
  style,
}) => {
  // 只取大版本数据
  const bigVersionData = data.map(version => ({
    title: <>{version}</>,
    value: version,
    key: version,
    selectable: true,
  }));

  return (
    <TreeSelect
      showSearch={false}
      placeholder={'请选择大版本 ...'}
      allowClear
      onChange={(v, l, e) => {
        const values = Array.isArray(v) ? v : [v];
        const len = values.length;
        if (!maxSelection || len <= maxSelection) {
          onChange(v, l, e);
        } else if (len > 2) {
          message.info(`选择多于2个版本时只展示数值波动！`);
        }
      }}
      treeData={bigVersionData}
      treeLine
      popupMatchSelectWidth={false}
      treeCheckable={!isSingle}
      style={style ?? { width: '100%' }}
      value={value ? value : undefined}
      treeDefaultExpandAll={true}
      treeNodeFilterProp="title"
    />
  );
};
export default OfflineVersionSelector;
