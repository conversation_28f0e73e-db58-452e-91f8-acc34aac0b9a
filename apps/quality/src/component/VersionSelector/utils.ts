import TreeValue from '@/component/treeValue';
import { AlarmVersion } from '@shared/typings/tea/metric';
import { IosTypeOf, VersionType } from '@shared/utils/version_utils';
import map from 'lodash/fp/map';
import compose from 'lodash/fp/compose';
import groupBy from 'lodash/fp/groupBy';
import { toPairs } from 'lodash';

export function fixCCIosVersionTypeUnknown(versionType: VersionType, versionCode: string) {
  if (versionType === VersionType.UNKNOWN) {
    return IosTypeOf(versionCode, true);
  } else {
    return versionType;
  }
}

export function buildTreeFromVersionList(versionList: AlarmVersion[]): TreeValue[] {
  return compose(
    map(([k, v]: [string, AlarmVersion[]]) => ({
      value: `${k.replaceAll('.', '')}_gross`,
      title: k,
      type: VersionType.UNKNOWN,
      selectable: false,
      children: v.map(it => ({
        value: it.versionCode,
        title: it.versionCode,
        type: fixCCIosVersionTypeUnknown(it.versionType, it.versionCode),
        selectable: true,
        children: [],
      })),
    })),
    toPairs,
    groupBy<AlarmVersion>(it => it.version),
  )(versionList);
}

export function buildTreeFromSimpleVersionList(versionList: AlarmVersion[]): TreeValue[] {
  return compose(
    map(([k, v]: [string, AlarmVersion[]]) => ({
      value: `${k}_gross`,
      title: k,
      key: k,
      type: VersionType.UNKNOWN,
      selectable: false,
      children: v.map(it => ({
        key: it.versionCode,
        value: it.versionCode,
        title: it.versionCode,
        type: fixCCIosVersionTypeUnknown(it.versionType, it.versionCode),
        selectable: true,
        children: [],
      })),
    })),
    toPairs,
    groupBy<AlarmVersion>(it => it.version),
  )(versionList);
}

export function buildTreeFromVersionListWithMajor(versionList: AlarmVersion[]): TreeValue[] {
  const ret = buildTreeFromVersionList(versionList);
  ret.forEach(it => {
    it.children.push({
      value: it.title,
      title: it.title,
      type: VersionType.MAJOR_VERSION,
      selectable: true,
      children: [],
    });
  });
  return ret;
}
