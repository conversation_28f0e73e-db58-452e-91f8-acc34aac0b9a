import React, { useEffect, useRef, useState } from 'react';
import { Button, Popover, Space, Tag } from 'antd';

const LabelEditor: React.FC<{
  labels: Array<{ label_id?: number; key?: string; value?: string }>;
  onSave: (newLabels: Array<{ label_id: number; key: string; value: string }>) => void;
}> = ({ labels, onSave }) => {
  // [!] 移除 isDeleting 状态，简化状态管理
  const [editing, setEditing] = useState(false);
  const [newLabels, setNewLabels] = useState(
    labels.map(l => ({
      label_id: l.label_id || Date.now(),
      key: l.key || 'new',
      value: l.value || '-',
    })),
  );
  const [inputVisible, setInputVisible] = useState(false);
  const [inputValue, setInputValue] = useState('');
  const [editingLabelId, setEditingLabelId] = useState<number | null>(null);

  const lastIdRef = useRef(0);
  const generateUniqueId = () => {
    lastIdRef.current += 1;
    return lastIdRef.current;
  };

  useEffect(() => {
    setNewLabels(
      labels.map(l => ({
        label_id: l.label_id || Date.now(),
        key: l.key || 'new',
        value: l.value || '-',
      })),
    );
    const validIds = labels.map(l => l.label_id).filter((id): id is number => typeof id === 'number');
    lastIdRef.current = validIds.length > 0 ? Math.max(...validIds) : 0;
  }, [labels]);

  const popoverRef = useRef<HTMLDivElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const useClickOutside = (ref: React.RefObject<HTMLElement>, callback: () => void) => {
    useEffect(() => {
      const handleClickOutside = (event: MouseEvent) => {
        const target = event.target as HTMLElement;
        if (target.closest('button')) {
          return;
        }
        if (containerRef.current?.contains(target)) {
          return;
        }
        if (ref.current?.contains(target)) {
          return;
        }
        callback();
      };
      document.addEventListener('mousedown', handleClickOutside);
      return () => document.removeEventListener('mousedown', handleClickOutside);
    }, [ref, callback]);
  };

  useClickOutside(popoverRef, () => {
    if (editing) {
      setEditing(false);
      setEditingLabelId(null);
    }
  });

  const handleEditComplete = () => {
    onSave(newLabels);
    setEditing(false);
    setEditingLabelId(null);
  };

  const handleAddLabel = () => {
    if (!inputValue.trim()) {
      return;
    }

    const newLabel = {
      label_id: generateUniqueId(),
      key: 'new',
      value: inputValue.trim(),
    };

    const updatedLabels = [...newLabels, newLabel];
    setNewLabels(updatedLabels);
    setInputVisible(false);
    setInputValue('');
    // onSave(updatedLabels);
  };

  const handleDeleteLabel = (labelId: number) => {
    const updatedLabels = newLabels.filter(label => label.label_id !== labelId);
    setNewLabels(updatedLabels);
    setEditingLabelId(null);
    // 判断是否需要关闭编辑状态
    // if (updatedLabels.length === 0) {
    //   setEditing(false);
    // }
    // 调用保存
    // onSave(updatedLabels);
  };

  const handleAddButtonClick = (event: React.MouseEvent) => {
    event.preventDefault();
    event.stopPropagation();
    setEditing(true);
    setInputVisible(true);
    requestAnimationFrame(() => {
      const input = document.querySelector<HTMLInputElement>('.label-input');
      input?.focus();
    });
  };

  return (
    <div ref={containerRef}>
      <Space direction="vertical">
        <Space wrap>
          {newLabels.length === 0 && !editing ? (
            <Tag style={{ cursor: 'pointer', fontStyle: 'italic', color: '#999' }} onClick={() => setEditing(true)}>
              -
            </Tag>
          ) : (
            newLabels.map(label => (
              <Popover
                key={label.label_id}
                content={
                  <div
                    ref={popoverRef}
                    onClick={e => e.stopPropagation()}
                    style={{ padding: '8px', display: 'flex', flexDirection: 'column', gap: '8px' }}
                  >
                    <input
                      value={label.value}
                      style={{ width: '100%' }}
                      onChange={e => {
                        const updated = newLabels.map(item =>
                          item.label_id === label.label_id ? { ...item, value: e.target.value } : item,
                        );
                        setNewLabels(updated);
                      }}
                    />
                    <Button
                      danger
                      size="small"
                      style={{ width: '100%' }}
                      onClick={e => {
                        e.stopPropagation();
                        handleDeleteLabel(label.label_id);
                      }}
                    >
                      删除
                    </Button>
                  </div>
                }
                open={editing && editingLabelId === label.label_id}
                trigger="click"
                onOpenChange={visible => {
                  if (visible) {
                    setEditingLabelId(label.label_id);
                  } else {
                    setEditingLabelId(null);
                  }
                }}
              >
                <Tag
                  style={{
                    cursor: 'pointer',
                    backgroundColor: '#f0f0f0',
                    maxWidth: 100,
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                  }}
                  onClick={() => {
                    setEditing(true);
                    setEditingLabelId(label.label_id);
                  }}
                >
                  {label.value}
                </Tag>
              </Popover>
            ))
          )}
          {inputVisible && (
            <input
              className="label-input"
              autoFocus
              style={{ width: 80 }}
              value={inputValue}
              onChange={e => setInputValue(e.target.value)}
              onKeyPress={e => {
                if (e.key === 'Enter') {
                  handleAddLabel();
                }
              }}
              onBlur={() => {
                if (inputValue.trim()) {
                  handleAddLabel();
                }
                setInputVisible(false);
              }}
            />
          )}
        </Space>
        {editing && (
          <Space>
            <Button data-add-button type="link" size="small" onClick={handleAddButtonClick}>
              + 添加
            </Button>
            <Button type="primary" size="small" onClick={handleEditComplete}>
              保存
            </Button>
          </Space>
        )}
      </Space>
    </div>
  );
};
export default LabelEditor;
