import React, { useState } from 'react';
import type { ProColumns } from '@ant-design/pro-components';
import { EditableProTable } from '@ant-design/pro-components';
import { forEach } from 'lodash';
import { CrashListInfo, CrashType } from '@shared/typings/slardar/crash/issueListSearch';
import { SlardarPlatformType } from '@pa/shared/dist/src/appSettings/appSettings';
import Link from 'antd/es/typography/Link';
import { useModel } from '@edenx/runtime/model';
import AppSettingModule from '@/model/appSettingModel';
import { Button, Drawer, message, Popconfirm, Select, Space, Switch, Tag, Tooltip } from 'antd';
import UserMessage from '@/component/UserMessage';
import { CrashType2Url, getReasonUrl, getSlardarIssueParams, getSlardarUrl } from '@shared/typings/slardar/common';
import {
  autoCreatBug,
  autoUpdateLevel,
  GrayNotificationTest,
  querySlardarCrashList,
  queryVersionInfo,
  sendWarningMessage,
  updateIssueLabels,
  updateSlardarWarn,
} from '@api/slardar';
import MeegoCreator from '@/component/MeegoCreator';
import UserSettingModule from '@/model/userSettingModel';
import { QueryOrBuildMeegoView } from '@api/meego';
import { versionCodeToMeegoVersion, VersionType } from '@shared/utils/version_utils';
import OOMConclusionList from '../OOMConclusionList';
import { NetworkCode, PlatformType } from '@pa/shared/dist/src/core';
import { getOver2TimeStamp, getPreVersionCode } from '@api/version';
import { DeviceLevel } from '@shared/common';
import LabelEditor from '@/component/SlardarCrashList/updateLabelsUtil';

const { Option } = Select;

interface IssueListParams {
  crashType: CrashType;
  versionCode: string;
  baseVersionCode: string;
  start_time: number;
  end_time: number;
  deviceLevel: DeviceLevel;
}

interface IssueListTableParams {
  update: boolean;
  isDeteriorate: boolean;
  isFilterOOM: boolean;
  isNew: boolean;
  isMemoryGraph: boolean;
  deviceLevel: DeviceLevel;
}

// const titleInput = useRef<InputRef>(null);

// const searchInput = useRef<InputRef>(null);
const SlardarCrashList: React.FC<IssueListParams> = ({
  crashType,
  versionCode,
  baseVersionCode,
  start_time,
  end_time,
  deviceLevel,
}) => {
  const [pageSize, setPageSize] = useState<number>(20);
  const [appSettingState, appSettingActions] = useModel(AppSettingModule);
  const [userSettings] = useModel(UserSettingModule);
  const [isWarning, setIsWarning] = useState<boolean>(false);
  const [dataSource, setDataSource] = useState<readonly CrashListInfo[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isUpdate, setIsUpdate] = useState(false);
  const [total, setTotal] = useState<number>(0);
  const [isMemoryGraph, setIsMemoryGraph] = useState<boolean>(false);
  const [update, setUpdate] = useState<boolean>(false);
  const [isDeteriorate, setIsDeteriorate] = useState<boolean>(false);
  const [isFilterOOM, setIsFilterOOM] = useState<boolean>(false);
  const [isNew, setIsNew] = useState<boolean>(false);
  const [meegoViewing, setMeegoViewing] = useState<boolean>(false);
  const [oomAnalyzeResultOpen, setOOMAnalyzeResultOpen] = useState(false);
  const [gettingReasonUrl, setGettingReasonUrl] = useState(false);
  const [isNotifying, setIsNotifying] = useState(false);
  const getGrayNotificationParam = {
    data: {
      aid: appSettingState.info.businessInfo.aid,
      version_code: versionCode,
    },
  };

  const handleGetReasonButtonClick = () => {
    setGettingReasonUrl(true);
    queryVersionInfo({
      data: {
        aid: appSettingState.info.businessInfo.aid,
        version_code: [baseVersionCode, versionCode],
      },
    }).then(async versions => {
      console.log(`find with version ${versionCode} - ${baseVersionCode} :${JSON.stringify(versions)}`);
      let baseVersion = versions.find(value => value.version_code === baseVersionCode);
      const targetVersion = versions.find(value => value.version_code === versionCode);
      if (
        targetVersion &&
        baseVersion === undefined &&
        (baseVersionCode === '0' || baseVersionCode === '' || baseVersionCode === undefined || baseVersionCode === null)
      ) {
        const newBaseVersion = await getPreVersionCode({
          data: {
            app_aid: appSettingState.info.businessInfo.aid,
            platform: targetVersion.platform,
            version_code: targetVersion.version_code,
            version: targetVersion.version,
          },
        });
        console.log(`found preversion ${JSON.stringify(newBaseVersion)}`);
        if (newBaseVersion) {
          baseVersion = newBaseVersion;
        }
      }

      if (baseVersion && targetVersion) {
        console.log(
          `found with baseVersion ${JSON.stringify(baseVersion)} targetVersion: ${JSON.stringify(targetVersion)}`,
        );
        const baseReleaseTime = baseVersion.timestamp;
        const targetReleaseTime = targetVersion.timestamp;
        let baseStartTime = baseReleaseTime;
        let targetStartTime = targetReleaseTime;
        const versionTypes = [VersionType.SMALL, VersionType.TF_GRAY];
        if (versionTypes.includes(targetVersion.version_type) && targetVersion.platform === SlardarPlatformType.iOS) {
          const newBaseStartTime = await getOver2TimeStamp({
            data: {
              app_aid: baseVersion.aid,
              version_start_timeStamp: baseVersion.timestamp,
              platform: baseVersion.platform,
              version_code: baseVersion.version_code,
              over_count: baseVersion.aid === 1775 || baseVersion.aid === 3006 ? 20000 : 50000,
            },
          });
          if (newBaseStartTime !== -1) {
            baseStartTime = newBaseStartTime;
          }
          console.log(`oyq base start time ${newBaseStartTime}`);

          const newTargetStartTime = await getOver2TimeStamp({
            data: {
              app_aid: targetVersion.aid,
              version_start_timeStamp: targetVersion.timestamp,
              platform: targetVersion.platform,
              version_code: targetVersion.version_code,
              over_count: baseVersion.aid === 1775 || baseVersion.aid === 3006 ? 20000 : 50000,
            },
          });
          if (newTargetStartTime !== -1) {
            targetStartTime = newTargetStartTime;
          }
          console.log(`oyq target start time ${newTargetStartTime}`);
        }

        const url = await getReasonUrl(
          appSettingState.info.businessInfo.app_id,
          crashType,
          baseVersion,
          targetVersion,
          baseStartTime,
          targetStartTime,
        );
        window.open(url, '_blank');
        setGettingReasonUrl(false);
      } else {
        setGettingReasonUrl(false);
        console.log(`cant not found version:${JSON.stringify(versions)}`);
      }
    });
  };

  const AutoCreateBug = () => {
    setIsLoading(true);
    autoCreatBug({
      data: {
        aid: appSettingState.info.businessInfo.aid,
        versionCode,
        platform:
          appSettingState.info.platform === PlatformType.Android
            ? SlardarPlatformType.Android
            : SlardarPlatformType.iOS,
        creator: userSettings.info.email,
        access_token: userSettings.info.access_token,
      },
    }).then(res => {
      setIsLoading(false);
      if (res.code === 1) {
        message.success('提单成功');
      } else {
        message.error(`提单失败 msg${res.msg}`);
      }
      setUpdate(!update);
    });
  };

  const AutoUpdateLevel = () => {
    setIsUpdate(true);
    autoUpdateLevel({
      data: {
        aid: appSettingState.info.businessInfo.aid,
        version_code: versionCode,
        platform:
          appSettingState.info.platform === PlatformType.Android
            ? SlardarPlatformType.Android
            : SlardarPlatformType.iOS,
        crash_type: crashType,
        device_level: deviceLevel,
      },
    }).then(res => {
      setIsUpdate(false);
      if (res.code === NetworkCode.Success) {
        message.success('更新成功');
        setUpdate(!update);
      } else {
        message.error(`更新失败，msg:${res.err}`);
      }
    });
  };

  const BuildMeegoView = () => {
    setMeegoViewing(true);
    QueryOrBuildMeegoView({
      data: {
        aid: appSettingState.info.businessInfo.aid,
        version: versionCodeToMeegoVersion(versionCode, appSettingState.info.businessInfo.aid !== 581595), // 即梦不需要修改patch
        platform:
          appSettingState.info.platform === PlatformType.Android
            ? SlardarPlatformType.Android
            : SlardarPlatformType.iOS,
      },
    }).then(value => {
      if (value.err_msg || !value.view_id) {
        message.error(value.err_msg ? value.err_msg : '存在被删除缺陷单，联系zhangyue.zz123解决');
      } else {
        window.open(`https://meego.feishu.cn/faceu/issueView/${value.view_id}`, '_blank');
      }
      setMeegoViewing(false);
    });
  };

  const OpenOOMAnalyzeResult = () => {
    setOOMAnalyzeResultOpen(true);
  };

  const OnOOMAnalyzeResultClose = () => {
    setOOMAnalyzeResultOpen(false);
  };

  const AutoWarning = () => {
    setIsWarning(true);
    sendWarningMessage({
      data: {
        aid: appSettingState.info.businessInfo.aid,
        versionCode,
        start_time,
        end_time,
        platform:
          appSettingState.info.platform === PlatformType.Android
            ? SlardarPlatformType.Android
            : SlardarPlatformType.iOS,
      },
    }).then(value => {
      console.log(value.bm, value.chatId);
      setIsWarning(false);
      message.success('播报成功');
    });
  };

  const handleNotificationClick = async () => {
    try {
      setIsNotifying(true);
      await GrayNotificationTest(getGrayNotificationParam);
      message.success('通报成功');
    } catch (error) {
      message.error(`通报失败：${error instanceof Error ? error.message : '未知错误'}`);
    } finally {
      setIsNotifying(false);
    }
  };

  const columns: ProColumns<CrashListInfo>[] = [
    {
      title: 'Issue详情',
      editable: false,
      dataIndex: 'crash_file',
      width: '15%',
      fixed: 'left',
      render: (_, row) => (
        <>
          <Link href={row?.slardar_url ? row.slardar_url : undefined} style={{ color: 'blue' }} target={'_blank'}>
            {row.crash_file}
          </Link>
          <br />
          <>
            {crashType !== CrashType.NativeCrash &&
            crashType !== CrashType.NativeOOM &&
            crashType !== CrashType.OOMCrash &&
            row.crash_line_number
              ? `line ${row.crash_line_number}`
              : ''}
          </>
          <br />
          <>{row.crash_clazz}</>
          <br />
          <>{row.crash_reason.substring(0, 200)}</>
        </>
      ),
    },
    {
      title: '处理人',
      editable: false,
      dataIndex: 'managers',
      width: '7%',
      fixed: 'left',
      render: (_, row) => <UserMessage emails={row.managers} />,
    },
    {
      title: 'slardar状态',
      editable: false,
      valueEnum: {
        new_created: { text: '新创建' },
        close: { text: '已关闭' },
        be_processed_again: { text: '重新打开' },
        doing: { text: '跟进中' },
        long: { text: '长期跟进' },
        done: { text: '已处理' },
        unassigned: { text: '未处理' },
      },
      filters: true,
      onFilter: false,
      dataIndex: 'status',
      width: '8%',
      align: 'center',
      fixed: 'left',
      // render: (_, row) => <>{row.status}</>,
      render: (_, row) => {
        if (row.status === 'new_created') {
          return <Tag color="yellow">新创建 </Tag>;
        } else if (row.status === 'close') {
          return <Tag color="blue">已关闭 </Tag>;
        } else if (row.status === 'be_processed_again') {
          return <Tag color="pink">重新打开 </Tag>;
        } else if (row.status === 'doing') {
          return <Tag color="purple">跟进中 </Tag>;
        } else if (row.status === 'long') {
          return <Tag color="green">长期跟进 </Tag>;
        } else if (row.status === 'done') {
          return <Tag color="purple">已处理 </Tag>;
        } else if (row.status === 'unassigned') {
          return <Tag color="red">未处理 </Tag>;
        }
      },
    },
    {
      title: '标签',
      editable: false,
      width: '8%',
      align: 'center',
      fixed: 'left',
      dataIndex: 'labels',
      hidden: ![CrashType.JavaMemLeak, CrashType.JavaSmallInstance].includes(crashType),
      render: (_, row) => {
        if (row.labels) {
          return (
            <Space direction={'vertical'}>
              {row.labels.map(label => (
                <Tag key={label.label_id}>{`${label.key}:${label.value}`}</Tag>
              ))}
            </Space>
          );
        } else {
          return <></>;
        }
      },
    },
    {
      title: '次数',
      editable: false,
      width: '5%',
      align: 'center',
      fixed: 'left',
      dataIndex: 'count',
      render: (_, row) => {
        if (row.platform === SlardarPlatformType.iOS) {
          return (
            <Space direction={'vertical'}>
              <>{row.count}</>
              <Tag color={'yellow'}>{row.count_rate ? parseFloat(row.count_rate).toFixed(2) : 0}‰</Tag>
            </Space>
          );
        } else {
          return <>{row.count}</>;
        }
      },
    },
    {
      title: '用户数量',
      editable: false,
      align: 'center',
      width: '10%',
      fixed: 'left',
      dataIndex: 'users',
      sorter: crashType === CrashType.JavaSmallInstance,
      render: (_, row) => {
        let rankingTag;
        if (row.baseRanking && row.baseRanking !== row.ranking) {
          rankingTag = (
            <Tag color={'blue'}>
              {`${row.baseRanking}->`}Top{row.ranking}
            </Tag>
          );
        }
        if (!row.baseRanking) {
          rankingTag = (
            <Tag color={'blue'}>
              {`New->`}Top{row.ranking}
            </Tag>
          );
        }
        const base_rate = row.base_user_rate ? parseFloat(parseFloat(row.base_user_rate).toFixed(4)) : 0;
        const final_rate = base_rate && row.user_rate ? ((parseFloat(row.user_rate) - base_rate) / base_rate) * 100 : 0;
        let final_show_rate = final_rate > 1000 ? `1000+%` : `${final_rate.toFixed(0)}%`;
        if (base_rate === 0) {
          final_show_rate = `1000+%`;
        }
        return (
          <Space direction={'vertical'}>
            <>{row.users}</>
            {rankingTag}
            <Tag color={'yellow'}>
              本次:{row.user_rate ? parseFloat(row.user_rate).toFixed(4) : 0}‰(
              <span
                style={{
                  color: `${
                    row.user_rate && row.base_user_rate && row.base_user_rate > row.user_rate ? 'green' : 'red'
                  }`,
                }}
              >
                {final_show_rate}
              </span>
              )
            </Tag>
            <Tag color={'yellow'}>上次:{base_rate ? `${base_rate}‰` : '无'}</Tag>
          </Space>
        );
      },
    },
    {
      title: '平均次数',
      width: '5%',
      fixed: 'left',
      editable: false,
      align: 'center',
      dataIndex: ['instance_num', 'users'],
      hidden: crashType !== CrashType.JavaSmallInstance,
      render: (_, row) => {
        const size = (row.instance_num ?? 0) / row.users;
        if (size > 1024) {
          return <>{`${(size / 1024).toFixed(2)}k`}</>;
        } else {
          return <>{size.toFixed(0)}</>;
        }
      },
    },
    {
      title: '平均内存',
      width: '5%',
      fixed: 'left',
      editable: false,
      align: 'center',
      dataIndex: ['memory_object_size', 'users'],
      hidden: crashType !== CrashType.JavaSmallInstance,
      render: (_, row) => {
        const size = (row.memory_object_size ?? 0) / row.users;
        if (size > 1024) {
          return <>{`${(size / 1024).toFixed(2)}MiB`}</>;
        } else {
          return <>{`${size.toFixed(0)}KiB`}</>;
        }
      },
    },
    {
      title: '播报',
      width: '4%',
      fixed: 'left',
      editable: false,
      align: 'center',
      dataIndex: 'is_warning',
      hidden: true,
      render: (_, record) => (
        <Switch
          key={`${update}`}
          defaultChecked={record.is_warning}
          onChange={checked => {
            updateSlardarWarn({
              data: {
                aid: record.aid,
                crashType: record.crash_type,
                versionCode: record.version_code,
                issue_id: record.issue_id,
                platform: record.platform,
                is_warning: checked,
              },
            }).then(() => console.log('update success'));
            console.log(checked);
          }}
        />
      ),
    },
    {
      title: '分布详情',
      editable: false,
      width: '8%',
      fixed: 'left',
      align: 'center',
      dataIndex: 'start_os_version',
      render: (_, row) => {
        if (row.platform === SlardarPlatformType.iOS) {
          const Tags = [];
          if (row.remarkable_dimensions) {
            for (const remark of row.remarkable_dimensions) {
              Tags.push(
                <Tooltip title={remark.percent ? `${(remark.percent * 100).toFixed(2)}%` : `undefined`}>
                  <Tag style={{ backgroundColor: '#f0f0f0', color: 'black' }}>{remark.value}</Tag>
                </Tooltip>,
              );
            }
          }
          return (
            <Space direction={'vertical'}>
              {`${row.start_os_version}-${row.end_os_version}`}
              {Tags}
            </Space>
          );
        }
        return (
          <>
            {row.start_os_version} - {row.end_os_version}{' '}
          </>
        );
      },
    },
    {
      title: 'labels',
      editable: false,
      width: '8%',
      fixed: 'left',
      align: 'center',
      render: (_, row) => (
        <LabelEditor
          labels={row.labels ?? []}
          onSave={newLabels => {
            console.log('保存issue:', row.issue_id, newLabels);
            const reqData = {
              data: {
                aid: appSettingState.info.businessInfo.aid,
                issue_id: row.issue_id,
                platform: appSettingState.info.platform.toString(),
                labels: newLabels,
              },
            };
            // 转换 platform 为 SlardarPlatformType 类型
            const platform =
              appSettingState.info.platform === PlatformType.Android
                ? SlardarPlatformType.Android
                : SlardarPlatformType.iOS;
            const newReqData = {
              ...reqData,
              data: {
                ...reqData.data,
                platform,
              },
            };
            updateIssueLabels(newReqData)
              .then(() => message.success('标签更新成功'))
              .catch(e => message.error(`更新失败: ${e.message}`));
          }}
        />
      ),
    },
    {
      title: '纸飞机定级',
      width: '10%',
      fixed: 'left',
      align: 'center',
      editable: false,
      valueEnum: {
        0: { text: 'P0' },
        1: { text: 'P1' },
        2: { text: 'P2' },
      },
      filters: true,
      onFilter: false,
      dataIndex: 'issue_level',
      render: (text, record) => {
        const color = (issueLevel: number) => {
          switch (issueLevel) {
            case 0:
              return 'red';
            case 1:
              return 'yellow';
            case 2:
              return 'blue';
            case 3:
              return 'gray';
            default:
              return 'gray';
          }
        };
        if (record.issue_level_reason) {
          const reason = record.issue_level_reason.split('|').map(value => (
            <Tag key={value} color={color(record.issue_level)}>
              {value}
            </Tag>
          ));
          return (
            <Space direction={'vertical'}>
              <Tag color={color(record.issue_level)}>P{record.issue_level}</Tag>
              {reason}
            </Space>
          );
        } else {
          return <Tag color={color(record.issue_level)}>P{record.issue_level}</Tag>;
        }
      },
    },
    {
      title: '关联meego',
      dataIndex: 'meego_url',
      width: '10%',
      editable: false,
      render: (_, record) => <MeegoCreator record={record} />,
    },
  ];

  const toolbar = {
    filter: (
      <div>
        <Space size={16}>
          {crashType === CrashType.OOMCrash ? (
            <Tooltip title={'是否过滤MemoryGraph'}>
              <Switch
                checkedChildren="已过滤"
                unCheckedChildren="未过滤"
                title={''}
                defaultChecked={isMemoryGraph}
                onChange={data => {
                  setIsMemoryGraph(data);
                }}
                style={{ width: '100px' }}
              />
            </Tooltip>
          ) : (
            <></>
          )}
          {appSettingState.info.platform === PlatformType.iOS ? (
            <Switch
              checkedChildren="劣化优先"
              unCheckedChildren="默认排序"
              defaultChecked={isDeteriorate}
              onChange={data => {
                if (data) {
                  setPageSize(100);
                } else {
                  setPageSize(20);
                }
                setIsDeteriorate(data);
              }}
              style={{ width: '100px' }}
            />
          ) : (
            <Space>
              {[CrashType.JavaCrash, CrashType.NativeCrash].includes(crashType) ? (
                <Switch
                  checkedChildren="排除OOM"
                  unCheckedChildren="包含OOM"
                  defaultChecked={isFilterOOM}
                  onChange={data => {
                    setIsFilterOOM(data);
                  }}
                  style={{ width: '100px' }}
                />
              ) : (
                <></>
              )}
              <Switch
                checkedChildren="仅看新增"
                unCheckedChildren="历史&新增"
                defaultChecked={isNew}
                onChange={data => {
                  setIsNew(data);
                }}
                style={{ width: '100px' }}
              />
            </Space>
          )}
          {appSettingState.info.businessInfo.aid !== 581595 ? (
            <>
              <Button color={'blue'} loading={gettingReasonUrl} onClick={handleGetReasonButtonClick}>
                一键归因
              </Button>
              <Button color={'blue'} onClick={handleNotificationClick} loading={isNotifying}>
                一键通报
              </Button>
            </>
          ) : (
            <></>
          )}
          <Button
            id={`StabilityIssueList_${start_time}_${end_time}_${appSettingState.info.id}`}
            key={appSettingState.info.id}
            href={getSlardarUrl(
              appSettingState.info.businessInfo.aid,
              CrashType2Url[crashType],
              appSettingState.info.platform.toString(),
              [CrashType.JavaMemLeak, CrashType.JavaSmallInstance].includes(crashType)
                ? `perf_v2/memory/${crashType === CrashType.JavaMemLeak ? 'activity' : 'instance'}`
                : `abnormal_list`,
              getSlardarIssueParams(
                start_time,
                end_time,
                versionCode,
                CrashType2Url[crashType],
                appSettingState.info.platform === PlatformType.iOS ? 'iOS' : 'Android',
                Math.floor(appSettingState.info.id / 100) === 1775,
                [CrashType.OOMCrash, CrashType.JavaOOM, CrashType.NativeOOM].includes(crashType),
                crashType,
              ),
            )}
            target={'_blank'}
            color={'blue'}
          >
            跳转 Slardar
          </Button>
          {appSettingState.info.businessInfo.aid !== 581595 ? (
            <Button key="broadcast" onClick={AutoWarning} color={'blue'} loading={isWarning}>
              一键播报必解问题
            </Button>
          ) : (
            <></>
          )}
          {/*
					<Popconfirm title="确定要执行此操作吗？" onConfirm={AutoCreateBug} okText="确定" cancelText="取消">
						<Button loading={isLoading}>P0/P1 问题一键提单</Button>
					</Popconfirm>
*/}
          <Popconfirm
            title="此操作会立即从Slardar上同步数据并触发定级&提单，确认执行？"
            onConfirm={AutoUpdateLevel}
            okText="确定"
            cancelText="取消"
          >
            <Button key="broadcast" color={'bule'} loading={isUpdate}>
              刷新数据和定级
            </Button>
          </Popconfirm>
          <Button key="broadcast" onClick={BuildMeegoView} color={'bule'} loading={meegoViewing}>
            Meego 视图
          </Button>
          {appSettingState.info.platform === PlatformType.Android &&
          [CrashType.JavaOOM, CrashType.NativeOOM].includes(crashType) ? (
            <Button key="broadcast" onClick={OpenOOMAnalyzeResult} color={'bule'}>
              OOM分析
            </Button>
          ) : (
            <></>
          )}
        </Space>
      </div>
    ),
  };
  return (
    <>
      <EditableProTable<CrashListInfo, IssueListTableParams>
        columns={columns}
        value={dataSource}
        rowKey="issue_id"
        toolbar={toolbar}
        params={{ update, isDeteriorate, isFilterOOM, isNew, isMemoryGraph, deviceLevel }}
        pagination={{
          pageSize,
          pageSizeOptions: [20, 40, 60, 80],
          onShowSizeChange: (current, size) => {
            setPageSize(size);
          },
          showSizeChanger: true,
        }}
        onLoad={(data: CrashListInfo[]) => {
          setDataSource(data);
        }}
        recordCreatorProps={false}
        request={async (params, sorter, filters) => {
          if (filters.issue_level) {
            filters.issue_level = filters.issue_level.map(res => Number(res));
          }
          const filtersInfo: {
            columnKey: string;
            filter: any[];
          }[] = [];
          // const { current, pageSize } = params;
          forEach(filters, (filter, key) => {
            if (filter) {
              filtersInfo.push({
                columnKey: key,
                filter: filter.map(value => value),
              });
            }
          });
          const validSorter: Record<string, 'ascend' | 'descend'> = {};
          if (sorter) {
            for (const key in sorter) {
              const sortOrder = sorter[key];
              if (sortOrder) {
                validSorter[key] = sortOrder;
              }
            }
          }
          const res = await querySlardarCrashList({
            data: {
              aid: appSettingState.info.businessInfo.aid,
              pageSize: params.pageSize ? params.pageSize : 20,
              current: params.current ? params.current : 1,
              platform:
                appSettingState.info.platform === PlatformType.Android
                  ? SlardarPlatformType.Android
                  : SlardarPlatformType.iOS,
              versionCode,
              start_time,
              end_time,
              baseVersionCode,
              crashType,
              product: appSettingState.info.name,
              isDeteriorate,
              filterOOM: isFilterOOM,
              filters: filtersInfo,
              isNew,
              isMemoryGraph,
              deviceLevel: params.deviceLevel,
              sorter: validSorter,
            },
          });
          setTotal(res.total);
          setDataSource(res.data);
          return { data: res.data, total: res.total, success: true };
        }}
        onChange={setDataSource}
      />
      <Drawer title={`OOM分析结果`} width={900} onClose={OnOOMAnalyzeResultClose} open={oomAnalyzeResultOpen}>
        <OOMConclusionList
          target_version_code={versionCode}
          base_version_code={baseVersionCode}
          platform={appSettingState.info.platform}
          type={crashType}
          is_open={oomAnalyzeResultOpen}
        />
      </Drawer>
    </>
  );
};

export default SlardarCrashList;
