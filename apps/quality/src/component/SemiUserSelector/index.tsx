import { User } from '@pa/shared/dist/src/core';
import React, { CSSProperties, useEffect, useState } from 'react';
import { searchUser } from '@api/index';
import { Avatar, Select, Tag } from '@douyinfe/semi-ui';
import { useModel } from '@edenx/runtime/model';
import UserSettingModule from '@/model/userSettingModel';
import { debounce } from 'lodash';

const SemiUserSelector: React.FC<{
  initUsers: User[];
  multi: boolean;
  updateSelectedUsers: (user: User[]) => void;
  style?: CSSProperties;
  disabled?: boolean;
  visibleChanged?: (visible: boolean) => void;
  defaultOpenUserList?: boolean;
  placeholder?: string;
}> = ({ initUsers, placeholder, multi, updateSelectedUsers, style, disabled, visibleChanged, defaultOpenUserList }) => {
  const [userOptions, setUserOptions] = useState<User[]>([]);
  const [loading, setLoading] = useState(false);
  const [selectedUsers, setSelectedUsers] = useState<User[]>([]);
  const [searchedUsers, setSearchedUsers] = useState<User[]>([]);

  const [userSettingState] = useModel(UserSettingModule);

  useEffect(() => {
    setSelectedUsers(initUsers);
  }, [initUsers]);

  useEffect(() => {
    if (!multi && selectedUsers.length > 1) {
      setSelectedUsers(selectedUsers ? (selectedUsers[0] ? [selectedUsers[0]] : []) : []);
      updateSelectedUsers(selectedUsers ? (selectedUsers[0] ? [selectedUsers[0]] : []) : []);
    }
  }, [multi]);

  useEffect(() => {
    selectedUsers.forEach(it => {
      const existedOption = userOptions.find(it2 => it2.open_id === it.open_id);
      if (!existedOption) {
        userOptions.push(it);
      }
    });
    setUserOptions(userOptions.concat([]));
  }, [selectedUsers]);

  useEffect(() => {
    const usersToBeAdd: User[] = [];
    selectedUsers.forEach(it => {
      const existedOption = searchedUsers.find(it2 => it2.open_id === it.open_id);
      if (!existedOption) {
        usersToBeAdd.push(it);
      }
    });
    setUserOptions(searchedUsers.concat(usersToBeAdd));
  }, [searchedUsers]);

  const search = (inputValue: string) => {
    if (inputValue === '') {
      return;
    }
    setLoading(true);
    searchUser({ data: { value: inputValue, token: userSettingState.info.access_token } }).then(ret => {
      if (ret.data) {
        setSearchedUsers(ret.data);
      }
      setLoading(false);
    });
  };

  const renderSelectedItem = (optionNode: Record<string, any>) => {
    const userInfo = selectedUsers.find(it => it.open_id === optionNode.open_id);
    return (
      <div style={{ display: 'flex', alignItems: 'center' }}>
        <Tag
          avatarSrc={typeof userInfo?.avatar === 'string' ? userInfo?.avatar : userInfo?.avatar?.avatar_240}
          avatarShape="circle"
          size="large"
        >
          {userInfo?.name ?? ''}
        </Tag>
      </div>
    );
  };
  // eslint-disable-next-line @typescript-eslint/ban-ts-comment
  // @ts-ignore
  const renderMultipleWithCustomTag = (optionNode: Record<string, any>, { onClose }) => {
    const userInfo = selectedUsers.find(it => it.open_id === Object.values(optionNode)[0]);
    const content = (
      <Tag
        avatarSrc={typeof userInfo?.avatar === 'string' ? userInfo?.avatar : userInfo?.avatar?.avatar_240}
        avatarShape="circle"
        size="large"
        closable={true}
        onClose={onClose}
      >
        {userInfo?.name}
      </Tag>
    );
    return {
      isRenderInTag: false,
      content,
    };
  };

  const renderCustomOption = (item: User) => {
    const optionStyle = {
      display: 'flex',
      paddingLeft: 24,
      paddingTop: 10,
      paddingBottom: 10,
    };
    return (
      <Select.Option value={item.open_id} style={optionStyle} showTick={true} {...item} key={item.open_id}>
        <Avatar size="small" src={typeof item?.avatar === 'string' ? item?.avatar : item?.avatar?.avatar_240} />
        <div style={{ marginLeft: 8 }}>
          <div style={{ fontSize: 14 }}>{item.name}</div>
          <div style={{ color: 'var(--color-text-2)', fontSize: 12, lineHeight: '16px', fontWeight: 'normal' }}>
            {item.open_id}
          </div>
        </div>
      </Select.Option>
    );
  };

  return (
    <Select
      placeholder={placeholder ?? '请选择或搜索用户'}
      maxTagCount={2}
      filter={true}
      remote={false}
      loading={loading}
      onSearch={debounce(search, 1000)}
      multiple={multi}
      disabled={disabled}
      style={style}
      showClear={true}
      showRestTagsPopover={true}
      expandRestTagsOnClick={true}
      defaultOpen={defaultOpenUserList}
      renderSelectedItem={multi ? renderMultipleWithCustomTag : renderSelectedItem}
      value={multi ? selectedUsers?.map(it => it.open_id) : selectedUsers ? selectedUsers[0]?.open_id : undefined}
      onChange={v => {
        if (multi) {
          const selected = userOptions.filter(it => (v as string[])?.includes(it.open_id ?? ''));
          if (selected) {
            setSelectedUsers(selected);
            updateSelectedUsers(selected);
          }
        } else {
          const selected = userOptions.find(it => it.open_id === v);
          setSelectedUsers(selected ? [selected] : []);
          updateSelectedUsers(selected ? [selected] : []);
        }
      }}
      onDropdownVisibleChange={visible => {
        visibleChanged ? visibleChanged(visible) : {};
      }}
    >
      {userOptions.map(it => renderCustomOption(it))}
    </Select>
  );
};

export default SemiUserSelector;
