import React from 'react';
import { Descriptions, Typography } from '@douyinfe/semi-ui';
import { IconArrowDown, IconArrowUp } from '@douyinfe/semi-icons';
import { crashType2Title } from '@shared/typings/slardar/common';
import Tools from '@shared/multiAttribution/Tools';
import { MultiAttributionComparedIssueListData } from '@shared/multiAttribution/market';

function getDescriptionsDiffView(base: number, target: number) {
  const delta = target - base;
  let arrowIcon, deltaView;
  if (delta !== 0) {
    deltaView = (
      <Typography.Text type={`${delta > 0 ? 'danger' : 'success'}`} style={{ marginLeft: '8px' }}>
        {Tools.formatNumber2Permillage(Math.abs(delta))}
      </Typography.Text>
    );
    if (delta > 0) {
      arrowIcon = <IconArrowUp size={'small'} style={{ color: 'red', marginLeft: '4px' }} />;
    } else if (delta < 0) {
      arrowIcon = <IconArrowDown size={'small'} style={{ color: 'green', marginLeft: '4px' }} />;
    }
  }
  return (
    <div>
      {`${Tools.formatNumber2Permillage(base)} -> ${Tools.formatNumber2Permillage(target)}`}
      {deltaView}
      {arrowIcon}
    </div>
  );
}

function getDescriptionsData(comparedIssueListData: MultiAttributionComparedIssueListData) {
  const base = comparedIssueListData.baseIssueListData;
  const target = comparedIssueListData.targetIssueListData;
  const { isDailyInterval } = base;

  let baseLaunchCount = 0;
  let targetLaunchCount = 0;
  let baseLaunchUserCount = 0;
  let targetLaunchUserCount = 0;
  let baseCountRatio = 0;
  let targetCountRatio = 0;
  let baseUserRatio = 0;
  let targetUserRatio = 0;
  if (isDailyInterval) {
    baseLaunchUserCount = base.launchUserCountItem.avg ?? 0;
    targetLaunchUserCount = target.launchUserCountItem.avg ?? 0;
    baseLaunchCount = base.launchCountItem.avg ?? 0;
    targetLaunchCount = target.launchCountItem.avg ?? 0;
    baseCountRatio = base.countRatioItem?.avg ?? 0;
    targetCountRatio = target.countRatioItem?.avg ?? 0;
    baseUserRatio = base.userRatioItem?.avg ?? 0;
    targetUserRatio = target.userRatioItem?.avg ?? 0;
  } else {
    baseLaunchCount = base.launchCountItem.time_rollup_data ?? 0;
    targetLaunchCount = target.launchCountItem.time_rollup_data ?? 0;
    baseLaunchUserCount = base.launchUserCountItem.time_rollup_data ?? 0;
    targetLaunchUserCount = target.launchUserCountItem.time_rollup_data ?? 0;
    baseCountRatio = base.countRatioItem?.time_rollup_data ?? 0;
    targetCountRatio = target.countRatioItem?.time_rollup_data ?? 0;
    baseUserRatio = base.userRatioItem?.time_rollup_data ?? 0;
    targetUserRatio = target.userRatioItem?.time_rollup_data ?? 0;
  }

  return [
    {
      key: '异常类型',
      value: `${crashType2Title[base.crashType]}`,
    },
    {
      key: `${isDailyInterval ? '日均启动次数' : '启动次数(指标值)'}`,
      value: `${Tools.formatNumber(baseLaunchCount)} -> ${Tools.formatNumber(targetLaunchCount)}`,
    },
    {
      key: `${isDailyInterval ? '日均启动用户数' : '启动用户数(指标值)'}`,
      value: `${Tools.formatNumber(baseLaunchUserCount)} -> ${Tools.formatNumber(targetLaunchUserCount)}`,
    },
    {
      key: `${isDailyInterval ? '日均异常率' : '异常率(指标值)'}`,
      value: getDescriptionsDiffView(baseCountRatio, targetCountRatio),
    },
    {
      key: `${isDailyInterval ? '日均异常率' : '异常率'}(Top${base.totalCount}issue的累加值)`,
      value: getDescriptionsDiffView(base.totalCrashCountRate, target.totalCrashCountRate),
    },
    {
      key: `${isDailyInterval ? '日均异常用户率' : '异常用户率(指标值)'}`,
      value: getDescriptionsDiffView(baseUserRatio, targetUserRatio),
    },
    {
      key: `${isDailyInterval ? '日均异常用户率' : '异常用户率'}(Top${base.totalCount}issue的累加值)`,
      value: getDescriptionsDiffView(base.totalCrashUserCountRate, target.totalCrashUserCountRate),
    },
  ];
}

export interface MultiAttributionIssueSummaryProps {
  comparedIssueListData: MultiAttributionComparedIssueListData;
}

export const MultiAttributionIssueSummary: React.FC<MultiAttributionIssueSummaryProps> = props => {
  const { comparedIssueListData } = props;
  const descriptionsStyle = {
    padding: '10px',
    width: '100%',
  };
  return (
    <Descriptions row size={'small'} style={descriptionsStyle} data={getDescriptionsData(comparedIssueListData)} />
  );
};
