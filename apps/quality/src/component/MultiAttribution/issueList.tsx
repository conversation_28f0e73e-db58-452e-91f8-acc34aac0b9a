import React from 'react';
import { ColumnProps } from '@douyinfe/semi-ui/lib/es/table';
import { Empty, Space, Table, Tag, Typography, Dropdown, Button, Tooltip } from '@douyinfe/semi-ui';
import Tools from '@shared/multiAttribution/Tools';
import { MultiAttributionIssueListSorter } from '@/component/MultiAttribution/issueListSorter';
import {
  MultiAttributionComparedIssueItem,
  MultiAttributionComparedIssueListData,
  MultiAttributionResponse,
} from '@shared/multiAttribution/market';
import { MultiAttributionIssueListSortType, MultiAttributionType } from '@shared/multiAttribution/CommonModel';
import { TagColor } from '@douyinfe/semi-ui/lib/es/tag';
import { MultiAttributionIssueSummary } from '@/component/MultiAttribution/IssueSummaryView';
import { MultiAttributionURLQueryKey } from '@shared/multiAttribution/urlQuery';

export interface MultiAttributionIssueListProps {
  data?: MultiAttributionResponse<MultiAttributionComparedIssueListData>;
  sortType: MultiAttributionIssueListSortType;
  onClickSortType?: (sortType: MultiAttributionIssueListSortType) => void;
}

const columns: ColumnProps<MultiAttributionComparedIssueItem>[] = [
  {
    title: 'issue详情',
    dataIndex: 'crash_detail',
    align: 'left',
    width: '70px',
    render: (_, row) => {
      const title = row.base ? row.base.rawItem.title : row.target?.rawItem.title;
      const detail = row.base ? row.base.rawItem.event_detail : row.target?.rawItem.event_detail;
      let baseDropDownItem, targetDropDownItem;
      if (row.base) {
        baseDropDownItem = (
          <Typography.Text link={{ href: `${row.base?.slardarUrl}`, target: '_blank' }}>
            基准Slardar链接
          </Typography.Text>
        );
      } else {
        baseDropDownItem = <Typography.Text>基准Slardar链接：无</Typography.Text>;
      }
      if (row.target) {
        targetDropDownItem = (
          <Typography.Text link={{ href: `${row.target?.slardarUrl}`, target: '_blank' }}>
            目标Slardar链接
          </Typography.Text>
        );
      } else {
        targetDropDownItem = <Typography.Text>目标Slardar链接：无</Typography.Text>;
      }
      return (
        <Space vertical spacing={2} align={'start'}>
          <Dropdown
            render={
              <Dropdown.Menu>
                <Dropdown.Item>{baseDropDownItem}</Dropdown.Item>
                <Dropdown.Item>{targetDropDownItem}</Dropdown.Item>
              </Dropdown.Menu>
            }
          >
            <Typography.Paragraph link style={{ width: 300, wordWrap: 'break-word' }}>
              {title}
            </Typography.Paragraph>
          </Dropdown>
          <Typography.Paragraph
            ellipsis={{
              rows: 3,
              showTooltip: { type: 'popover', opts: { style: { width: 300, wordWrap: 'break-word' } } },
            }}
            style={{ width: 300 }}
          >
            {detail}
          </Typography.Paragraph>
        </Space>
      );
    },
  },
  {
    title: 'Top位置',
    dataIndex: 'crash_ranking',
    align: 'center',
    // width: "10%",
    render: (_, row) => {
      let summaryTag;
      if (row.baseCrashCount === 0) {
        summaryTag = <Tag color={'red'}>新增</Tag>;
      } else if (row.targetCrashCount === 0) {
        summaryTag = <Tag color={'green'}>修复</Tag>;
      } else {
        if (row.deltaRank > 10) {
          summaryTag = <Tag color={'green'}>突降</Tag>;
        } else if (row.deltaRank < -10) {
          summaryTag = <Tag color={'red'}>突增</Tag>;
        } else if (row.deltaRank === 0) {
          summaryTag = <Tag color={'white'}>持平</Tag>;
        }
      }
      // eslint-disable-next-line one-var
      let deltaRankTag;
      if (row.deltaRank === 0) {
        deltaRankTag = <Tag color={'white'}>{`Top${row.baseRank}`}</Tag>;
      } else {
        deltaRankTag = (
          <Tag color={'white'}>
            {`${row.baseRank === 0 ? '无' : `Top${row.baseRank}`}`}
            {' -> '}
            {`${row.targetRank === 0 ? '无' : `Top${row.targetRank}`}`}
          </Tag>
        );
      }
      return (
        <Space vertical spacing={5}>
          {deltaRankTag}
          {summaryTag}
        </Space>
      );
    },
  },
  {
    title: '异常次数/异常率',
    dataIndex: 'crash_count',
    align: 'center',
    // width: "15%",
    render: (_, row) => {
      let tagColor: TagColor = 'white';
      // if (row.deltaCrashCount > 1000) {
      // 	tagColor = "red";
      // } else if (row.deltaCrashCount < -1000) {
      // 	tagColor = "green";
      // }
      let prefix = row.deltaCrashCount > 0 ? '+' : '';
      const countRow = (
        <Space>
          <>
            {`${Tools.formatNumber(row.baseCrashCount)} -> `}
            {Tools.formatNumber(row.targetCrashCount)}
          </>
          <Tag color={tagColor}>{`${prefix}${row.deltaCrashCount}`}</Tag>
          <Tag color={tagColor}>{`${prefix}${row.deltaCrashCountPercentWithUnit}`}</Tag>
        </Space>
      );

      tagColor = 'white';
      if (row.deltaCrashCountRatePercent > 0.2 || row.deltaCrashCountRatePercentWithUnit.includes('Infinity')) {
        tagColor = 'red';
      } else if (row.deltaCrashCountRatePercent < -0.2) {
        tagColor = 'green';
      }
      prefix = row.deltaCrashCountRate > 0 ? '+' : '';
      const countRateRow = (
        <Space>
          <p>
            {`${row.baseCrashCountRateWithUnit} -> `}
            {row.targetCrashCountRateWithUnit}
          </p>
          <Tag color={'white'}>{`${prefix}${row.deltaCrashCountRateWithUnit}`}</Tag>
          <Tag color={tagColor}>{`${prefix}${row.deltaCrashCountRatePercentWithUnit}`}</Tag>
        </Space>
      );
      return (
        <Space vertical spacing={2}>
          {countRow}
          {countRateRow}
        </Space>
      );
    },
  },
  {
    title: '异常用户数/异常用户率',
    dataIndex: 'crash_user_count',
    align: 'center',
    render: (_, row) => {
      let tagColor: TagColor = 'white';
      // if (row.deltaCrashUserCount > 1000) {
      // 	tagColor = "red";
      // } else if (row.deltaCrashUserCount < -1000) {
      // 	tagColor = "green";
      // }
      let prefix = row.deltaCrashUserCount > 0 ? '+' : '';
      const countRow = (
        <Space>
          <>
            {`${Tools.formatNumber(row.baseCrashUserCount)} -> `}
            {Tools.formatNumber(row.targetCrashUserCount)}
          </>
          <Tag color={tagColor}>{`${prefix}${row.deltaCrashUserCount}`}</Tag>
          <Tag color={tagColor}>{`${prefix}${row.deltaCrashUserCountPercentWithUnit}`}</Tag>
        </Space>
      );

      tagColor = 'white';
      if (row.deltaCrashUserCountRatePercent > 0.2 || row.deltaCrashUserCountRatePercentWithUnit.includes('Infinity')) {
        tagColor = 'red';
      } else if (row.deltaCrashUserCountRatePercent < -0.2) {
        tagColor = 'green';
      }
      prefix = row.deltaCrashUserCountRate > 0 ? '+' : '';
      const countRateRow = (
        <Space>
          <p>
            {`${row.baseCrashUserCountRateWithUnit} -> `}
            {row.targetCrashUserCountRateWithUnit}
          </p>
          <Tag color={'white'}>{`${prefix}${row.deltaCrashUserCountRateWithUnit}`}</Tag>
          <Tag color={tagColor}>{`${prefix}${row.deltaCrashUserCountRatePercentWithUnit}`}</Tag>
        </Space>
      );
      return (
        <Space vertical spacing={2}>
          {countRow}
          {countRateRow}
        </Space>
      );
    },
  },
  {
    title: '多维分析',
    dataIndex: 'multi_attribution_analysis',
    align: 'center',
    render: (_, row) => (
      <Tooltip content={'点击开始分析该issue的多维度信息'}>
        <Button
          theme="borderless"
          type="primary"
          size="default"
          onClick={event => {
            const host = Tools.getPaperAirplaneUrl();
            const urlPath = `${host}/quality/version/multiAttribution?tab=issue`;
            const baseSlardarUrl = row.base?.slardarUrl;
            const targetSlardarUrl = row.target?.slardarUrl;
            if (baseSlardarUrl && targetSlardarUrl) {
              const url = `${urlPath}&${MultiAttributionURLQueryKey.TYPE}=${MultiAttributionType.COMPARE}&${MultiAttributionURLQueryKey.BASE_SLARDAR_URL}=${encodeURIComponent(baseSlardarUrl)}&${MultiAttributionURLQueryKey.TARGET_SLARDAR_URL}=${encodeURIComponent(targetSlardarUrl)}`;
              window.open(url);
            } else if (baseSlardarUrl) {
              const url = `${urlPath}&${MultiAttributionURLQueryKey.TYPE}=${MultiAttributionType.DIMENSION}&${MultiAttributionURLQueryKey.BASE_SLARDAR_URL}=${encodeURIComponent(baseSlardarUrl)}`;
              window.open(url);
            } else if (targetSlardarUrl) {
              const url = `${urlPath}&${MultiAttributionURLQueryKey.TYPE}=${MultiAttributionType.DIMENSION}&${MultiAttributionURLQueryKey.BASE_SLARDAR_URL}=${encodeURIComponent(targetSlardarUrl)}`;
              window.open(url);
            }
          }}
        >
          多维分析
        </Button>
      </Tooltip>
    ),
  },
];

export const MultiAttributionIssueList: React.FC<MultiAttributionIssueListProps> = props => {
  const { data, sortType, onClickSortType } = props;
  if (data === undefined || data.data === undefined) {
    return <div style={{ padding: 10 }}>暂无结果：请输入链接后，点击「开始分析」按钮</div>;
  }
  if (data.code !== 0) {
    return <div style={{ padding: 10 }}>{data.message}</div>;
  }

  return (
    <>
      <MultiAttributionIssueSummary comparedIssueListData={data.data} />
      <MultiAttributionIssueListSorter sortType={props.sortType} onClickSortType={props.onClickSortType} />
      <Table
        columns={columns}
        dataSource={data.data.items}
        pagination={false}
        resizable
        bordered
        empty={<Empty description={'暂无数据'} />}
      />
    </>
  );
};
