import React from 'react';
import { Radio, RadioGroup, Space } from '@douyinfe/semi-ui';
import { MultiAttributionType } from '@shared/multiAttribution/CommonModel';
import { useSearchParams } from '@edenx/runtime/router';
import { MultiAttributionURLQueryKey } from '@shared/multiAttribution/urlQuery';

export interface MultiAttributionTypeViewProps {
  disabled: boolean;
  multiAttributionType: MultiAttributionType;
  setMultiAttributionType: (type: MultiAttributionType) => void;
}

export const MultiAttributionTypeView: React.FC<MultiAttributionTypeViewProps> = props => {
  const [searchParams, setSearchParams] = useSearchParams();
  const { multiAttributionType, setMultiAttributionType } = props;
  return (
    <Space>
      归因分析类型：
      <RadioGroup
        disabled={props.disabled}
        onChange={(e: any) => {
          setMultiAttributionType(e.target.value);
          searchParams.set(MultiAttributionURLQueryKey.TYPE, e.target.value);
          setSearchParams(searchParams);
        }}
        value={multiAttributionType}
        aria-label="归因分析类型"
        name="attribution-type-radio-group"
      >
        <Radio value={MultiAttributionType.COMPARE}>对比分析</Radio>
        <Radio value={MultiAttributionType.DIMENSION}>多维分析</Radio>
      </RadioGroup>
    </Space>
  );
};
