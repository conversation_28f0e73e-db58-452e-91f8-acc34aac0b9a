import React from 'react';
import {
  getMultiAttributionIssueListSortTypeName,
  MultiAttributionIssueListSortType,
} from '@shared/multiAttribution/CommonModel';
import { Button, Dropdown } from '@douyinfe/semi-ui';
import { IconChevronDown } from '@douyinfe/semi-icons';

export interface MultiAttributionIssueListSorterProps {
  sortType: MultiAttributionIssueListSortType;
  onClickSortType?: (sortType: MultiAttributionIssueListSortType) => void;
}

export const MultiAttributionIssueListSorter: React.FC<MultiAttributionIssueListSorterProps> = props => {
  const sortOptions = [
    MultiAttributionIssueListSortType.BASE_RANKING,
    MultiAttributionIssueListSortType.TARGET_RANKING,
    MultiAttributionIssueListSortType.DELTA_CRASH_USER_RATE_PERCENT_DESC,
    MultiAttributionIssueListSortType.DELTA_CRASH_USER_RATE_PERCENT_ASC,
    MultiAttributionIssueListSortType.DELTA_CRASH_USER_RATE_DESC,
    MultiAttributionIssueListSortType.DELTA_CRASH_USER_RATE_ASC,
  ];
  return (
    <Dropdown
      style={{ marginLeft: 'auto' }}
      trigger={'click'}
      position={'bottom'}
      showTick={true}
      clickToHide={true}
      render={
        <Dropdown.Menu>
          {sortOptions.map((option, index) => (
            <Dropdown.Item
              key={index}
              active={props.sortType === option}
              onClick={e => {
                if (props.onClickSortType) {
                  props.onClickSortType(option);
                }
              }}
            >
              {getMultiAttributionIssueListSortTypeName(option)}
            </Dropdown.Item>
          ))}
        </Dropdown.Menu>
      }
    >
      <Button icon={<IconChevronDown />} iconPosition={'right'} theme={'outline'} type={'tertiary'}>
        {`排序方式：${getMultiAttributionIssueListSortTypeName(props.sortType)}`}
      </Button>
    </Dropdown>
  );
};
