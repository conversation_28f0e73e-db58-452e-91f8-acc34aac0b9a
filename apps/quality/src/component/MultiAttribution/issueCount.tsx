import { MultiAttributionRequestIssueCount } from '@shared/multiAttribution/CommonModel';
import { Button, Dropdown } from '@douyinfe/semi-ui';
import { IconChevronDown } from '@douyinfe/semi-icons';
import React from 'react';

export interface MultiAttributionRequestIssueCountSelectorProps {
  disabled: boolean;
  requestIssueCount: MultiAttributionRequestIssueCount;
  onSelectedRequestIssueCount: (requestIssueCount: MultiAttributionRequestIssueCount) => void;
}

export const MultiAttributionRequestIssueCountSelector: React.FC<
  MultiAttributionRequestIssueCountSelectorProps
> = props => {
  const countOptions: MultiAttributionRequestIssueCount[] = [10, 20, 50, 100, 200, 500];
  return props.disabled ? (
    <Button disabled={true} icon={<IconChevronDown />} iconPosition={'right'} theme={'outline'} type={'tertiary'}>
      {`请求Top${props.requestIssueCount}的issue`}
    </Button>
  ) : (
    <Dropdown
      trigger={'click'}
      position={'bottom'}
      showTick={true}
      clickToHide={true}
      render={
        <Dropdown.Menu>
          {countOptions.map((option, index) => (
            <Dropdown.Item
              key={index}
              active={props.requestIssueCount === option}
              onClick={e => {
                props.onSelectedRequestIssueCount(option);
              }}
            >
              {option.toString()}
            </Dropdown.Item>
          ))}
        </Dropdown.Menu>
      }
    >
      <Button icon={<IconChevronDown />} iconPosition={'right'} theme={'outline'} type={'tertiary'}>
        {`请求Top${props.requestIssueCount}的issue`}
      </Button>
    </Dropdown>
  );
};
