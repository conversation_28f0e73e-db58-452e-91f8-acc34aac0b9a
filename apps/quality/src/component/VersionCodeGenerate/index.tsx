import React, { useEffect, useRef, useState } from 'react';
import { Button, Form, Toast, Card, Tag, Space, Banner, Typography, Notification } from '@douyinfe/semi-ui';
import copy from 'copy-to-clipboard';
import { CopyOutlined } from '@ant-design/icons';
import SyntaxHighlighter from 'react-syntax-highlighter';
import { Filter, FlightBasicParams, generateFilterRule, getCNToken } from '@shared/libra/LibraCreate';
import { gen_filter_code } from '@api/libraNonOpen';
import { Token } from '@shared/libra/NonOpen';
import { useModel } from '@edenx/runtime/model';
import UserSettingModule from '@/model/userSettingModel';
import { ParamFilters } from '@shared/libra/NonOpenCommon';
import { BaseFormApi } from '@douyinfe/semi-foundation/lib/es/form/interface';
import { QUALITY_HOST_CN_HTTPS } from '@pa/shared/dist/src/core';
import { trim_suffix } from '@shared/utils/tools';

export interface VersionCodeGenerateProps {
  app?: VCToolApp;
  version?: string;
  platform?: VCToolPlatform;
  libraType?: VCToolLibraType;
  tokens?: Token[];
  onCopyRules?: (filters: Filter[][]) => boolean;
  flightBasicParams?: FlightBasicParams;
}

export enum VCToolPlatform {
  Both = 'Android & iOS',
  Android = 'Android',
  iOS = 'iOS',
  Server = 'Server',
}

export enum VCToolLibraType {
  Both = '全版本',
  Gray = '灰度实验',
  Release = '正式实验',
}

export enum VCToolApp {
  LV = '剪映',
  Capcut = 'Capcut',
  Dreamina = '即梦',
  Retouch = '醒图',
}

export const App2Id: Record<VCToolApp, number> = {
  剪映: 1775,
  Capcut: 3006,
  即梦: 3704,
  醒图: 2515,
};

export enum VCCodeType {
  VersionCode = '_version_code(普通)',
  Commerce = 'android_vc/ios_vc(商业化)',
}

/**
 * 正常来说需要通过param_filters_op接口来拿，但是由于目前这里仅用到几个固定的params，直接写死
 */
const simpleParamFilters = [
  {
    key: 'device_platform',
    type: 'string',
    source: 'libra',
    property_type: 'common_param',
  },
  {
    key: '_version_code',
    type: 'function',
    source: 'libra',
    property_type: '',
  },
  {
    key: 'channel',
    type: 'string',
    source: 'libra',
    property_type: 'common_param',
    bundles: [
      {
        id: 4738,
        param_filter_id: 0,
        display_name: '灰度渠道',
        name: 'grey_channel',
      },
    ],
  },
  {
    key: 'android_vc',
    type: 'int',
    source: 'libra',
    property_type: 'common_param',
  },
  {
    key: 'ios_vc',
    type: 'version',
    source: 'libra',
    property_type: 'common_param',
  },
  {
    key: 'app_id',
    type: 'int',
    source: 'libra',
    property_type: 'common_param',
  },
] as ParamFilters[];

const VersionCodeGenerate: React.FC<VersionCodeGenerateProps> = props => {
  const [userSettings] = useModel(UserSettingModule);
  const [tokens, setTokens] = useState<Token[]>([]);

  const [version, setVersion] = useState<string>();
  const [app, setApp] = useState<VCToolApp>(VCToolApp.LV);
  const [platform, setPlatform] = useState<VCToolPlatform>(VCToolPlatform.Both);
  const [libraType, setLibraType] = useState<VCToolLibraType>(VCToolLibraType.Both);
  const [codeType, setCodeType] = useState<VCCodeType>(VCCodeType.VersionCode);
  const [grayStage, setGrayStage] = useState<number>(0);

  const [validVersion, setValidVersion] = useState(false);
  const [androidVersionCode, setAndroidVersionCode] = useState<number>();
  const [iosVersionCode, setIosVersionCode] = useState<number>();
  const [filterCode, setFilterCode] = useState('');
  const [libraFilters, setLibraFilters] = useState<Filter[][]>([]);

  const formApi = useRef<BaseFormApi>();

  const getParamFilters = () => props.flightBasicParams?.paramFilters?.data ?? simpleParamFilters;

  useEffect(() => {
    if (props.version) {
      setVersion(props.version);
      formApi.current?.setValue('version', props.version);
    }
    if (props.app) {
      setApp(props.app);
      formApi.current?.setValue('app', props.app);
    }
    if (props.platform) {
      setPlatform(props.platform);
      formApi.current?.setValue('platform', props.platform);
    }
    if (props.libraType) {
      setLibraType(props.libraType);
      formApi.current?.setValue('libraType', props.libraType);
    }
    if (props.tokens) {
      setTokens(props.tokens);
    } else {
      getCNToken(userSettings.info.email, token => {
        if (token?.token) {
          setTokens(prevState => [...prevState.filter(t => t.region !== token?.region), token]);
        } else {
          Notification.warning({
            title: '获取国内Token失败',
            duration: 0,
            content: (
              <>
                请确保已在Libra前端页登录，否则无法生成过滤条件。
                <Typography.Text
                  link={{ href: 'https://data.bytedance.net/libra/new-flight/coding', target: '_blank' }}
                >
                  点我快速登录
                </Typography.Text>
              </>
            ),
          });
        }
      });
    }
  }, []);

  // 计算小版本号
  useEffect(() => {
    setValidVersion(false);
    if (version && version?.length > 5) {
      let versionCode = parseInt(version.replaceAll('.', ''), 10) * (app === '剪映' ? 100000 : 10000);
      if (versionCode > 0 && !isNaN(versionCode)) {
        if (grayStage > 0) {
          versionCode += grayStage * 100;
        }
        setAndroidVersionCode(versionCode);
        const versionCode2 = parseInt(
          version
            .split('.')
            .map(it => it.padStart(3, '0'))
            .join(''),
          10,
        );
        if (versionCode2 > 0 && !isNaN(versionCode2)) {
          setIosVersionCode(versionCode2);
          setValidVersion(true);
        }
      }
    }
    if (version && app === VCToolApp.Dreamina) {
      // 以下是即梦的计算逻辑
      if (version.split('.').length < 3 || version.split('.').find(it => it.length > 1)) {
        setValidVersion(false);
        return;
      }
      const androidCode = `${version.split('.').join('')}0000`;
      setAndroidVersionCode(parseInt(androidCode));
      const iosCode = (() => {
        const parts = version.split('.');
        return (
          parseInt(parts[0]) * Math.pow(10, 6) +
          parseInt(parts[1]) * Math.pow(10, 3) +
          parseInt(parts[2]) * Math.pow(10, 0)
        );
      })();
      setIosVersionCode(iosCode);
      setValidVersion(true);
    }
    if (version && app === VCToolApp.Retouch) {
      if (version.split('.').length < 3) {
        setValidVersion(false);
        return;
      }
      setAndroidVersionCode(parseInt(`${version.split('.').join('')}00`));
      const versionCode2 = parseInt(
        version
          .split('.')
          .map(it => it.padStart(2, '0'))
          .join(''),
        10,
      );
      if (versionCode2 > 0 && !isNaN(versionCode2)) {
        setIosVersionCode(versionCode2);
        setValidVersion(true);
      }
    }
  }, [version, app, grayStage, codeType]);

  const generateClientFilters = () => {
    const filters: Filter[][] = [];
    if (!androidVersionCode || !iosVersionCode) {
      return filters;
    }
    if (platform === VCToolPlatform.Android || platform === VCToolPlatform.Both) {
      const filterAndroid: Filter[] = [];
      filters.push(filterAndroid);
      filterAndroid.push({
        key: 'device_platform',
        op: '==',
        value: ['android'],
        transformer: 'lower',
        useTransformer: true,
      });

      const releaseCode = Math.floor(androidVersionCode / 10000) * 10000 + 1600;
      filterAndroid.push({
        key: '_version_code',
        op: '>=',
        value: `${libraType === VCToolLibraType.Release ? releaseCode : androidVersionCode}`,
      });
      if (libraType === VCToolLibraType.Gray) {
        filterAndroid.push({
          key: '_version_code',
          op: '<',
          value: `${releaseCode}`,
        });
      }
    }
    if (platform === VCToolPlatform.iOS || platform === VCToolPlatform.Both) {
      const filterIOS: Filter[] = [];
      filters.push(filterIOS);
      filterIOS.push({
        key: 'device_platform',
        op: '==',
        value: ['ios', 'iphone', 'ipad'],
        transformer: 'lower',
        useTransformer: true,
      });
      filterIOS.push({
        key: codeType === VCCodeType.Commerce ? 'ios_vc' : '_version_code',
        op: '>=',
        value: `${iosVersionCode}`,
      });
      if (libraType === VCToolLibraType.Gray) {
        filterIOS.push({
          key: 'channel',
          op: '!=',
          value: [`App Store`],
        });
      } else if (libraType === VCToolLibraType.Release) {
        filterIOS.push({
          key: 'channel',
          op: '==',
          value: [`App Store`],
        });
      }
    }
    return filters;
  };

  const generateServerFilters = () => {
    const filters: Filter[][] = [];
    if (!androidVersionCode || !iosVersionCode || !version) {
      return filters;
    }
    if (codeType === VCCodeType.Commerce) {
      // android_vc/ios_vc
      const filterAndroid: Filter[] = [];
      filters.push(filterAndroid);
      filterAndroid.push({
        key: 'app_id',
        op: '==',
        value: [App2Id[app]],
      });
      filterAndroid.push({
        key: 'device_platform',
        op: '==',
        value: ['android'],
        transformer: 'lower',
        useTransformer: true,
      });
      filterAndroid.push({
        key: 'android_vc',
        op: '>=',
        value: `${androidVersionCode}`,
      });
      if (libraType === VCToolLibraType.Gray) {
        filterAndroid.push({
          key: 'channel',
          op: 'in_bundle',
          transformer: 'lower',
          useTransformer: true,
          bundleId: [
            getParamFilters()
              .find(it => it.key === 'channel')
              ?.bundles?.find(it => it.name === 'grey_channel')?.id ?? 0,
          ],
        });
      }
      const filterIOS: Filter[] = [];
      filters.push(filterIOS);
      filterIOS.push({
        key: 'app_id',
        op: '==',
        value: [App2Id[app]],
      });
      filterIOS.push({
        key: 'device_platform',
        op: '==',
        value: ['ios', 'iphone', 'ipad'],
        transformer: 'lower',
        useTransformer: true,
      });
      filterIOS.push({
        key: 'ios_vc',
        op: '>=',
        value: `${version}.0`,
      });
      if (libraType === VCToolLibraType.Gray) {
        filterIOS.push({
          key: 'channel',
          op: 'in_bundle',
          transformer: 'lower',
          useTransformer: true,
          bundleId: [
            getParamFilters()
              .find(it => it.key === 'channel')
              ?.bundles?.find(it => it.name === 'grey_channel')?.id ?? 0,
          ],
        });
      }
    } else {
      // server的_version_code不需要分端
      const parts = version.split('.');
      const serverVersionCode = Number(parts[0]) * 10000 + Number(parts[1]) * 100 + Number(parts[2]);
      const filterCommon: Filter[] = [
        {
          key: 'app_id',
          op: '==',
          value: [App2Id[app]],
        },
        {
          key: '_version_code',
          op: '>=',
          value: serverVersionCode,
        },
      ];
      filters.push(filterCommon);
    }
    return filters;
  };

  // 计算过滤条件
  useEffect(() => {
    if (!androidVersionCode || !iosVersionCode) {
      return;
    }
    let filters: Filter[][] = [];
    if (platform !== VCToolPlatform.Server) {
      // 客户端实验默认用version_code
      setCodeType(VCCodeType.VersionCode);
      formApi.current?.setValue('codeType', VCCodeType.VersionCode);
      filters = generateClientFilters();
    } else {
      // 服务端使用version_code的实验不区分灰度、正式
      if (codeType === VCCodeType.VersionCode) {
        setLibraType(VCToolLibraType.Both);
        formApi.current?.setValue('libraType', VCToolLibraType.Both);
      }
      filters = generateServerFilters();
    }
    setLibraFilters(filters);
    const _filterRule = generateFilterRule(filters, getParamFilters());
    if (!_filterRule || _filterRule.length === 0 || tokens.length === 0) {
      return;
    }
    gen_filter_code({
      headers: {},
      data: {
        libra_app_id: 147,
        tokens,
        filter_rule: _filterRule ?? [],
      },
    }).then(res => {
      if (res.code === 0 && res.data?.filter_code) {
        setFilterCode(res.data.filter_code);
      } else {
        setFilterCode('');
      }
    });
  }, [androidVersionCode, iosVersionCode, platform, libraType, tokens, codeType]);

  const copyText = (text: string) => {
    copy(text);
    Toast.success(`复制成功 ${text}`);
  };

  const onClickApply = () => {
    if (props.onCopyRules) {
      const res = props.onCopyRules(libraFilters);
      Toast.success(res ? `一键应用成功` : `应用失败`);
    }
  };

  return (
    <Form
      labelCol={{ span: 3 }}
      wrapperCol={{ span: 8 }}
      labelPosition={'left'}
      labelAlign={'right'}
      getFormApi={api => (formApi.current = api)}
    >
      <iframe
        src={`${QUALITY_HOST_CN_HTTPS}/api/quality/cn/get_titan_passport_id?email=${trim_suffix('@bytedance.com')(userSettings.info.email)}`}
        title="CN Quality For Cookie"
        width="1px"
        height="1px"
        frameBorder="0"
      />
      <Form.Input
        field={'version'}
        label={'版本号'}
        placeholder="支持三段式和四段式，例如 14.5.0 / *********"
        initValue={version}
        onChange={e => setVersion(e)}
        rules={[{ required: true, message: '请输入版本号' }]}
        required={true}
      />
      <Form.RadioGroup
        field="app"
        label="App"
        initValue={app}
        onChange={v => setApp(v.target.value)}
        rules={[{ required: true }]}
        disabled={props.app !== undefined}
      >
        {Object.values(VCToolApp).map(value => (
          <Form.Radio value={value} key={value}>
            {value}
          </Form.Radio>
        ))}
      </Form.RadioGroup>
      <Form.RadioGroup
        field="platform"
        label="实验端"
        initValue={platform}
        rules={[{ required: true }]}
        onChange={v => setPlatform(v.target.value)}
      >
        {Object.values(VCToolPlatform).map(value => (
          <Form.Radio value={value} key={value}>
            {value}
          </Form.Radio>
        ))}
      </Form.RadioGroup>
      {platform === VCToolPlatform.Server && (
        <Form.RadioGroup
          field="codeType"
          label="版本号类型"
          initValue={codeType}
          rules={[{ required: true }]}
          onChange={v => setCodeType(v.target.value)}
        >
          {Object.values(VCCodeType).map(value => (
            <Form.Radio value={value} key={value}>
              {value}
            </Form.Radio>
          ))}
        </Form.RadioGroup>
      )}
      <Form.RadioGroup
        field="libraType"
        label="实验类型"
        initValue={libraType}
        rules={[{ required: true }]}
        disabled={platform === VCToolPlatform.Server && codeType === VCCodeType.VersionCode}
        onChange={v => setLibraType(v.target.value)}
      >
        {Object.values(VCToolLibraType).map(value => (
          <Form.Radio value={value} key={value}>
            {value}
          </Form.Radio>
        ))}
      </Form.RadioGroup>

      {libraType === VCToolLibraType.Gray &&
        [VCToolPlatform.Android, VCToolPlatform.Both].includes(platform) &&
        [VCToolApp.LV, VCToolApp.Capcut].includes(app) && (
          <Form.Select
            field="grayStage"
            label="最低灰度轮次"
            initValue={grayStage}
            rules={[{ required: true }]}
            onChange={v => setGrayStage(v as number)}
          >
            {Array.from({ length: 7 }, (_, i) => i).map(value => (
              <Form.Select.Option value={value} key={value}>
                {value} 灰
              </Form.Select.Option>
            ))}
          </Form.Select>
        )}
      <Form.Slot label="小版本号参考">
        <Card bordered={true} style={{ width: 400, fontSize: 15 }}>
          {validVersion && androidVersionCode && iosVersionCode ? (
            <Space vertical align={'start'}>
              <span>
                <b>{`${app} ${version} `}</b>
                转换成小版本号为：
              </span>
              <span>
                <Tag color={'grey'}>Android</Tag>
                {` ${androidVersionCode} `}
                <CopyOutlined
                  onClick={() => copyText(androidVersionCode.toString())}
                  style={{
                    color: '#1677ff',
                  }}
                />
              </span>
              <span>
                <Tag color="grey">iOS</Tag>
                {` ${iosVersionCode} `}
                <CopyOutlined
                  onClick={() => copyText(iosVersionCode.toString())}
                  style={{
                    color: '#1677ff',
                  }}
                />
              </span>
            </Space>
          ) : (
            <span>请输入正确的大版本号~</span>
          )}
        </Card>
      </Form.Slot>

      <Form.Slot label="实验过滤规则参考">
        <Banner
          fullMode={false}
          type="info"
          bordered
          icon={null}
          closeIcon={null}
          description={
            <>
              实验过滤规则详见:
              <Typography.Text
                link={{ href: 'https://bytedance.larkoffice.com/wiki/wikcn3FSeBpV6tP78qanqPVAd3b', target: '_blank' }}
              >
                剪映&CapCut A/B 实验流程SOP
              </Typography.Text>
            </>
          }
        />
        <Card bordered={true} style={{ maxWidth: 800, marginTop: 10 }} bodyStyle={{ padding: 0 }}>
          {validVersion && (
            <CopyOutlined
              onClick={() => copyText(filterCode)}
              style={{
                color: '#1677ff',
                position: 'absolute',
                top: 70,
                right: 10,
              }}
            />
          )}
          <SyntaxHighlighter
            language="python"
            wrapLines={true}
            wrapLongLines={true}
            customStyle={{
              padding: 16,
              margin: 0,
              fontSize: 13,
            }}
          >
            {validVersion ? filterCode : '请输入正确的大版本号'}
          </SyntaxHighlighter>
        </Card>
      </Form.Slot>
      {props.onCopyRules && (
        <Button type={'primary'} theme="solid" style={{ margin: 40 }} onClick={onClickApply}>
          一键复制过滤规则
        </Button>
      )}
    </Form>
  );
};

export default VersionCodeGenerate;
