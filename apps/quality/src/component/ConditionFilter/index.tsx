import React, { ReactNode, useEffect, useState } from 'react';
import {
  Button,
  Checkbox,
  Dropdown,
  Input,
  InputNumber,
  Select,
  Space,
  SplitButtonGroup,
  Tag,
  TreeSelect,
  Typography,
} from '@douyinfe/semi-ui';
import { IconChevronUpDown, IconClear, IconPlusCircle, IconTreeTriangleDown } from '@douyinfe/semi-icons';
import SemiUserSelector from '@/component/SemiUserSelector';
import {
  ConditionType,
  EnumFilterConfig,
  EnumFilterStatus,
  FilterConfig,
  FilterGroupType,
  FilterStatus,
  FilterType,
  NumberFilterConfig,
  NumberFilterStatus,
  TextFilterConfig,
  TextFilterStatus,
  TreeEnumFilterConfig,
  TreeEnumFilterStatus,
  UserFilterStatus,
} from '@shared/utils/conditionFilter';
import { OptionProps } from '@douyinfe/semi-ui/lib/es/select';
import { DropDownMenuItem } from '@douyinfe/semi-ui/lib/es/dropdown';

const { Text } = Typography;

const FilterType2ConditionTypeMap: Record<number, ConditionType[]> = {
  0: [
    ConditionType.ConditionTypeIn,
    ConditionType.ConditionTypeNotIn,
    ConditionType.ConditionTypeEqual,
    ConditionType.ConditionTypeNotEqual,
    ConditionType.ConditionTypeNotNull,
    ConditionType.ConditionTypeNull,
  ],
  1: [
    ConditionType.ConditionTypeIn,
    ConditionType.ConditionTypeNotIn,
    ConditionType.ConditionTypeEqual,
    ConditionType.ConditionTypeNotEqual,
    ConditionType.ConditionTypeNotNull,
    ConditionType.ConditionTypeNull,
  ],
  2: [
    ConditionType.ConditionTypeContain,
    ConditionType.ConditionTypeNotContain,
    ConditionType.ConditionTypeEqual,
    ConditionType.ConditionTypeNotEqual,
  ],
  3: [
    ConditionType.ConditionTypeEqual,
    ConditionType.ConditionTypeNotEqual,
    ConditionType.ConditionTypeLT,
    ConditionType.ConditionTypeLTE,
    ConditionType.ConditionYypeGT,
    ConditionType.ConditionTypeGTE,
  ],
  4: [
    ConditionType.ConditionTypeIn,
    ConditionType.ConditionTypeNotIn,
    ConditionType.ConditionTypeEqual,
    ConditionType.ConditionTypeNotEqual,
    ConditionType.ConditionTypeNotNull,
    ConditionType.ConditionTypeNull,
  ],
};

const ConditionType2ConditionName: Record<number, string> = {
  0: '等于',
  1: '不等于',
  2: '包含',
  3: '不包含',
  4: '大于',
  5: '大于等于',
  6: '小于',
  7: '小于等于',
  8: '存在选项属于',
  9: '全部选项均不属于',
  10: '为空',
  11: '不为空',
};

const TreeEnumSelector: React.FC<{
  config: TreeEnumFilterConfig;
  status: TreeEnumFilterStatus;
  updateValue: (values: string[]) => void;
  multi?: boolean;
}> = ({ config, status, updateValue, multi }) => {
  const [selectedValues, setSelectedValues] = useState<string[]>();

  useEffect(() => {
    setSelectedValues(status?.selectedValues);
  }, [status]);

  useEffect(() => {
    if (!multi && (selectedValues?.length ?? 0 > 1)) {
      setSelectedValues(selectedValues ? (selectedValues[0] ? [selectedValues[0]] : undefined) : undefined);
      updateValue(selectedValues ? (selectedValues[0] ? [selectedValues[0]] : []) : []);
    }
  }, [multi]);

  return (
    <TreeSelect
      value={multi ? selectedValues : selectedValues ? selectedValues[0] : undefined}
      dropdownStyle={{ maxHeight: 400, overflow: 'auto' }}
      style={{ width: 250 }}
      treeData={config.filterOptions}
      placeholder={'请选择目标值'}
      multiple={multi}
      maxTagCount={2}
      filterTreeNode={true}
      searchPlaceholder={'请输入关键字开始搜索'}
      onChange={values => {
        if (multi) {
          updateValue(values as string[]);
          setSelectedValues((values as string[]) ?? []);
        } else {
          updateValue(values ? ([values] as string[]) : []);
          setSelectedValues(values ? ([values] as string[]) : []);
        }
      }}
      showFilteredOnly={true}
    />
  );
};

const NumberFilterInput: React.FC<{
  config: NumberFilterConfig;
  status: NumberFilterStatus;
  updateValue: (value?: number) => void;
}> = ({ config, status, updateValue }) => {
  const [inputValue, setInputValue] = useState<number | undefined>();

  useEffect(() => {
    setInputValue(status.inputNumber);
  }, [status]);

  return (
    <InputNumber
      defaultValue={'请输入数字'}
      value={inputValue}
      innerButtons={true}
      style={{ width: 250 }}
      onNumberChange={value => {
        setInputValue(value);
        updateValue(value);
      }}
    />
  );
};

const TextFilterInputer: React.FC<{
  config: TextFilterConfig;
  status: TextFilterStatus;
  updateValues: (values: string[]) => void;
}> = ({ config, status, updateValues }) => {
  const [values, setValues] = useState<string[]>([]);

  useEffect(() => {
    setValues(status.inputText ?? []);
  }, [status]);

  return (
    <Input
      showClear={true}
      placeholder={'请输入目标值'}
      value={values[0] ?? undefined}
      style={{ width: 250 }}
      onChange={value => {
        setValues(value.length > 0 ? [value] : []);
        updateValues(value.length > 0 ? [value] : []);
      }}
    />
  );
};

const EnumFilterSelector: React.FC<{
  config: EnumFilterConfig;
  status: EnumFilterStatus;
  updateValues: (values: (string | number)[]) => void;
  multi?: boolean;
}> = ({ config, status, updateValues, multi = true }) => {
  const [selectedValues, setSelectedValues] = useState<(string | number)[]>();

  useEffect(() => {
    setSelectedValues(status?.selectedValues);
  }, [status]);

  useEffect(() => {
    if (!multi && (selectedValues?.length ?? 0 > 1)) {
      setSelectedValues(selectedValues ? (selectedValues[0] ? [selectedValues[0]] : undefined) : undefined);
      updateValues(selectedValues ? (selectedValues[0] ? [selectedValues[0]] : []) : []);
    }
  }, [multi]);

  // eslint-disable-next-line @typescript-eslint/ban-ts-comment
  // @ts-ignore
  const renderMultipleWithCustomTag = (optionNode: Record<string, any>, { onClose }) => {
    const option = config.filterOptions.find(it => it.value === optionNode.value);
    const content = (
      <Tag closable={true} onClose={onClose} style={{ maxWidth: 80 }}>
        {option?.label}
      </Tag>
    );
    return {
      isRenderInTag: false,
      content,
    };
  };

  const renderSelectedItem = (optionNode: Record<string, any>) => {
    const option = config.filterOptions.find(it => it.value === optionNode.value);
    const content = <Tag style={{ maxWidth: 80 }}>{option?.label}</Tag>;
    return content;
  };

  const searchLabel = (inpueValue: string, option: OptionProps) => {
    const optionValue = option.value;
    const optionLabel = config.filterOptions.find(it => it.value === optionValue)?.label;
    return optionLabel?.includes(inpueValue) ?? false;
  };

  return (
    <Select
      placeholder={'请选择目标值'}
      optionList={(config as EnumFilterConfig)?.filterOptions?.map(it => ({
        value: it.value,
        label: (
          <Text
            ellipsis={{
              showTooltip: {
                opts: { content: it.label },
              },
            }}
          >
            {it.label}
          </Text>
        ),
      }))}
      value={multi ? selectedValues : selectedValues ? selectedValues[0] : undefined}
      style={{ width: 250 }}
      showRestTagsPopover={true}
      expandRestTagsOnClick={true}
      maxTagCount={2}
      multiple={multi}
      filter={searchLabel}
      renderSelectedItem={multi ? renderMultipleWithCustomTag : renderSelectedItem}
      onChange={value => {
        if (multi) {
          updateValues(value as (string | number)[]);
          setSelectedValues((value as (string | number)[]) ?? []);
        } else {
          updateValues(value ? ([value] as (string | number)[]) : []);
          setSelectedValues(value ? ([value] as (string | number)[]) : []);
        }
      }}
    />
  );
};

const FilterItem: React.FC<{
  onClose: () => void;
  filterConfigs: FilterConfig[];
  filterStatus?: FilterStatus;
  updateFilterStatus: (status: FilterStatus) => void;
}> = ({ onClose, filterConfigs, filterStatus, updateFilterStatus }) => {
  const [filterConfigOptions, setFilterConfigOptions] = useState<{ value: string; label: ReactNode }[]>([]);
  const [conditionTypeOptions, setConditionTypeOptions] = useState<{ value: ConditionType; label: ReactNode }[]>([]);
  const [selectedFilterConfig, setSelectedFilterConfig] = useState<FilterConfig>();
  const [selectedConditionType, setSelectedConditionType] = useState<ConditionType>();
  const [filterInputRender, setFilterInputRender] = useState<ReactNode>();

  const [curFilterStatus, setCurFilterStatus] = useState<FilterStatus>();

  const updateCurFilterStatus = (newStatus: FilterStatus) => {
    const newSelectedConfig = filterConfigs.find(it => it.filterId === newStatus?.config?.filterId);
    setSelectedFilterConfig(newSelectedConfig);
    setSelectedConditionType(newStatus?.conditionType);
    updateFilterStatus(newStatus);
    setCurFilterStatus(newStatus);
  };

  useEffect(() => {
    if (!selectedFilterConfig) {
      setFilterInputRender(
        <Select
          placeholder={'请选择目标值'}
          optionList={[]}
          value={undefined}
          style={{ width: 250 }}
          onSelect={(value, option) => {
            console.log('empty');
          }}
        />,
      );
      return;
    }
    switch (selectedFilterConfig.filterType) {
      case FilterType.FilterTypeEnum:
        setFilterInputRender(
          <EnumFilterSelector
            config={selectedFilterConfig as EnumFilterConfig}
            status={filterStatus as EnumFilterStatus}
            updateValues={values => {
              const newStatus = {
                config: selectedFilterConfig,
                conditionType: selectedConditionType,
                selectedValues: values,
              } as EnumFilterStatus;
              updateFilterStatus(newStatus);
              setCurFilterStatus(newStatus);
            }}
            multi={
              selectedConditionType !== ConditionType.ConditionTypeEqual &&
              selectedConditionType !== ConditionType.ConditionTypeNotEqual
            }
          />,
        );
        return;
      case FilterType.FilterTypeUser:
        setFilterInputRender(
          <SemiUserSelector
            initUsers={(filterStatus as UserFilterStatus)?.selectedUsers ?? []}
            style={{ width: 250 }}
            updateSelectedUsers={user => {
              const newStatus = {
                config: selectedFilterConfig,
                conditionType: selectedConditionType,
                selectedUsers: user,
              } as UserFilterStatus;
              updateFilterStatus(newStatus);
              setCurFilterStatus(newStatus);
            }}
            multi={
              selectedConditionType !== ConditionType.ConditionTypeEqual &&
              selectedConditionType !== ConditionType.ConditionTypeNotEqual
            }
          />,
        );
        return;
      case FilterType.FilterTypeText:
        setFilterInputRender(
          <TextFilterInputer
            config={selectedFilterConfig as TextFilterConfig}
            status={filterStatus as TextFilterStatus}
            updateValues={value => {
              updateCurFilterStatus({
                config: selectedFilterConfig,
                conditionType: selectedConditionType,
                inputText: value,
              } as TextFilterStatus);
            }}
          />,
        );
        return;
      case FilterType.FilterTypeNumber:
        setFilterInputRender(
          <NumberFilterInput
            config={selectedFilterConfig as NumberFilterConfig}
            status={filterStatus as NumberFilterStatus}
            updateValue={value => {
              updateCurFilterStatus({
                config: selectedFilterConfig,
                conditionType: selectedConditionType,
                inputNumber: value,
              } as NumberFilterStatus);
            }}
          />,
        );
        return;
      case FilterType.FilterTypeTreeEnum:
        setFilterInputRender(
          <TreeEnumSelector
            config={selectedFilterConfig as TreeEnumFilterConfig}
            status={filterStatus as TreeEnumFilterStatus}
            updateValue={values => {
              const newStatus = {
                config: selectedFilterConfig,
                conditionType: selectedConditionType,
                selectedValues: values,
              } as TreeEnumFilterStatus;
              updateFilterStatus(newStatus);
              setCurFilterStatus(newStatus);
            }}
            multi={
              selectedConditionType !== ConditionType.ConditionTypeEqual &&
              selectedConditionType !== ConditionType.ConditionTypeNotEqual
            }
          />,
        );
        return;
      default:
        setFilterInputRender(
          <Select
            placeholder={'请选择目标值'}
            optionList={[]}
            value={undefined}
            style={{ width: 250 }}
            onSelect={(value, option) => {
              console.log('empty');
            }}
          />,
        );
        return;
    }
  }, [selectedFilterConfig, filterStatus, selectedConditionType]);

  useEffect(() => {
    const options = filterConfigs.map(it => ({
      value: it.filterId,
      label: <Text>{it.filterName}</Text>,
    }));
    setFilterConfigOptions(options);
  }, [filterConfigs]);

  useEffect(() => {
    const newSelectedConfig = filterConfigs.find(it => it.filterId === filterStatus?.config?.filterId);
    setSelectedFilterConfig(newSelectedConfig);
    setSelectedConditionType(filterStatus?.conditionType);
    setCurFilterStatus(filterStatus);
  }, [filterStatus]);

  useEffect(() => {
    if (!selectedFilterConfig) {
      setConditionTypeOptions([]);
      return;
    }
    const conditions = FilterType2ConditionTypeMap[selectedFilterConfig.filterType];
    const options = conditions.map(it => ({
      value: it,
      label: ConditionType2ConditionName[it],
    }));
    setConditionTypeOptions(options);
  }, [selectedFilterConfig]);

  const searchLabel = (inpueValue: string, option: OptionProps) => {
    const optionValue = option.value;
    const filterConfig = filterConfigs.find(it => it.filterId === optionValue);
    return filterConfig?.filterName?.includes(inpueValue) ?? false;
  };

  return (
    <Space align={'start'}>
      <Select
        placeholder={'请选择筛选项'}
        optionList={filterConfigOptions}
        value={selectedFilterConfig?.filterId}
        style={{ width: 200 }}
        onSelect={(value, option) => {
          if ((value as string) === selectedFilterConfig?.filterId) {
            return;
          }
          const config = filterConfigs.find(it => it.filterId === (value as string));
          const conditions = config ? FilterType2ConditionTypeMap[config.filterType] : undefined;
          updateCurFilterStatus({
            config: filterConfigs.find(it => it.filterId === (value as string)),
            conditionType: conditions ? conditions[0] : undefined,
          } as FilterStatus);
        }}
        filter={searchLabel}
      />
      <Select
        placeholder={'运算符'}
        optionList={conditionTypeOptions}
        value={selectedConditionType}
        style={{ width: 160 }}
        onSelect={(value, option) => {
          if ((value as number) === selectedConditionType) {
            return;
          }
          if (curFilterStatus) {
            curFilterStatus.conditionType = value as ConditionType;
            updateCurFilterStatus(curFilterStatus);
          }
          filterStatus = curFilterStatus;
        }}
      />
      {curFilterStatus?.conditionType === ConditionType.ConditionTypeNotNull ||
      curFilterStatus?.conditionType === ConditionType.ConditionTypeNull ? (
        <></>
      ) : (
        filterInputRender
      )}
      <Button
        icon={<IconClear />}
        theme={'borderless'}
        style={{ color: 'rgba(var(--semi-grey-5), 1)' }}
        onClick={onClose}
      />
    </Space>
  );
};

const FilterItemGroup: React.FC<{
  itemIds: string[];
  deleteItem: (id: string) => void;
  type: FilterGroupType;
  updateItem: (id: string, filterStatus: FilterStatus) => void;
  filterStatus: FilterStatus[];
  itemId2FilterStatusIdx: { [key: string]: number };
  filterConfigs: FilterConfig[];
  updateGroupType: (newType: FilterGroupType) => void;
}> = ({
  itemIds,
  deleteItem,
  type,
  updateItem,
  filterStatus,
  itemId2FilterStatusIdx,
  filterConfigs,
  updateGroupType,
}) => {
  const [groupType, setGroupType] = useState<FilterGroupType>();

  useEffect(() => {
    setGroupType(type);
  }, [type]);

  useEffect(() => {
    if (groupType !== undefined) {
      updateGroupType(groupType);
    }
  }, [groupType]);

  return (
    <Space>
      {itemIds.length > 1 ? (
        <Button
          theme={'borderless'}
          icon={<IconChevronUpDown />}
          iconPosition={'right'}
          style={{ color: 'rgba(var(--semi-grey-8), 1)' }}
          onClick={() =>
            groupType === FilterGroupType.And ? setGroupType(FilterGroupType.Or) : setGroupType(FilterGroupType.And)
          }
        >
          {groupType === FilterGroupType.And ? '且' : '或'}
        </Button>
      ) : (
        <></>
      )}
      <Space vertical={true} style={{ paddingBottom: 10 }} align={'start'}>
        {itemIds.map(id => (
          <FilterItem
            onClose={() => deleteItem(id)}
            key={id}
            filterConfigs={filterConfigs}
            updateFilterStatus={status => updateItem(id, status)}
            filterStatus={filterStatus[itemId2FilterStatusIdx[id]]}
          />
        ))}
      </Space>
    </Space>
  );
};

const ConditionFilter: React.FC<{
  filterConfigs: FilterConfig[];
  filterStatus: FilterStatus[];
  updateStatus: (statusArray: FilterStatus[]) => void;
  onApply: (statusArray: FilterStatus[]) => void;
  updateType: (type: FilterGroupType) => void;
  updateFloatCheckStatus?: (checked: boolean) => void;
  floatCheckStatus?: boolean;
  filterType?: FilterGroupType;
  spliteButtonMenu?: DropDownMenuItem[];
  spliteButtonVisibleChanged?: (visible: boolean) => void;
  onclickSpliteMenuOutSide?: (e: React.MouseEvent) => void;
}> = ({
  filterStatus,
  filterConfigs,
  updateStatus,
  onApply,
  updateType,
  filterType = FilterGroupType.And,
  spliteButtonMenu,
  floatCheckStatus,
  updateFloatCheckStatus,
  spliteButtonVisibleChanged,
  onclickSpliteMenuOutSide,
}) => {
  const [itemIds, setItemIds] = useState<string[]>([]);
  const [newItemId, setNewItemId] = useState<number>(0);
  const [itemId2StatusIdx, setItemId2StatusIdx] = useState<{ [key: string]: number }>({});

  const [curStatusArray, setCurStatusArray] = useState<FilterStatus[]>([]);

  const refreshMapInfo = (newStatusArray: FilterStatus[]) => {
    const newItemIds: string[] = [];
    let id = 0;
    const newidxMap: { [key: string]: number } = {};
    newStatusArray.forEach((it, index) => {
      newItemIds.push(id.toString());
      newidxMap[id.toString()] = index;
      id++;
    });
    setItemIds(newItemIds);
    setNewItemId(id);
    setItemId2StatusIdx(newidxMap);
  };

  useEffect(() => {
    setCurStatusArray(filterStatus);
    refreshMapInfo(filterStatus);
  }, [filterStatus]);

  const updateItem = (id: string, status: FilterStatus) => {
    filterStatus[itemId2StatusIdx[id]] = status;
    updateStatus(filterStatus);
    setCurStatusArray(filterStatus.concat([]));
  };

  const addItem = () => {
    const newStatus = {} as FilterStatus;
    const newStatusArray = curStatusArray.concat([newStatus]);
    updateStatus(newStatusArray);
    setCurStatusArray(newStatusArray);
    refreshMapInfo(newStatusArray);
  };

  const removeItem = (id: string) => {
    let newStatus: FilterStatus[] = [];
    filterStatus.splice(itemId2StatusIdx[id], 1);
    updateStatus(filterStatus);
    newStatus = newStatus.concat(filterStatus);
    setCurStatusArray(newStatus);
    refreshMapInfo(newStatus);
  };

  return (
    <div style={{ minWidth: 500, padding: 20 }}>
      <div style={{ display: 'flex', justifyContent: 'space-between' }}>
        <Text strong>设置筛选条件</Text>
        {updateFloatCheckStatus ? (
          <Checkbox
            onChange={e => {
              updateFloatCheckStatus(e.target.checked ?? false);
            }}
            defaultChecked={floatCheckStatus}
          >
            显示已终止数据
          </Checkbox>
        ) : (
          <></>
        )}
      </div>
      <div style={{ height: 10 }} />
      <Space vertical={true} style={{ paddingBottom: 10 }} align={'start'}>
        <FilterItemGroup
          itemIds={itemIds}
          deleteItem={id => removeItem(id)}
          filterStatus={curStatusArray}
          filterConfigs={filterConfigs}
          itemId2FilterStatusIdx={itemId2StatusIdx}
          updateItem={updateItem}
          type={filterType}
          updateGroupType={updateType}
        />
      </Space>
      <div style={{ display: 'flex', justifyContent: 'space-between' }}>
        <Space spacing={5}>
          <Button icon={<IconPlusCircle />} theme="solid" type="primary" onClick={addItem}>
            添加筛选条件
          </Button>
          <Button
            theme="borderless"
            type="primary"
            onClick={() => {
              setItemIds([]);
              setItemId2StatusIdx({});
              updateStatus([]);
              setCurStatusArray([]);
            }}
          >
            清空全部条件
          </Button>
        </Space>
        {spliteButtonMenu ? (
          <SplitButtonGroup>
            <Button theme="solid" type="primary" onClick={() => onApply(filterStatus)}>
              应用
            </Button>
            <Dropdown
              menu={spliteButtonMenu}
              trigger="hover"
              position="bottomRight"
              onVisibleChange={spliteButtonVisibleChanged}
              onClickOutSide={onclickSpliteMenuOutSide}
            >
              <Button
                style={{ background: 'var(--semi-color-primary-hover)', padding: '8px 4px' }}
                theme="solid"
                type="primary"
                icon={<IconTreeTriangleDown />}
              />
            </Dropdown>
          </SplitButtonGroup>
        ) : (
          <Button theme="solid" type="primary" onClick={() => onApply(filterStatus)}>
            应用
          </Button>
        )}
      </div>
    </div>
  );
};

export default ConditionFilter;
