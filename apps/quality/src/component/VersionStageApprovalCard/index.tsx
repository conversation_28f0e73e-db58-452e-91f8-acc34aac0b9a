import React, { useEffect, useState } from 'react';
import { CheckItemStatus, VersionStageCheckItem } from '@shared/releasePlatform/versionStageInfoCheckList';
import UserCard from '@/component/UserCard';
import { Col, Descriptions, Radio, RadioChangeEvent, Space, Typography } from 'antd';
import { updateCheckItem, VersionStageCheckItemSchema } from '@api/releasePlatform';
import { z } from 'zod';
import { useModel } from '@edenx/runtime/model';
import AppSettingModule from '@/model/appSettingModel';
import VersionInfoModule from '@/model/versionInfoModel';
import { head } from 'lodash';
import { CheckCircleTwoTone, CloseCircleTwoTone } from '@ant-design/icons';
import VersionStageApprovalButton from '@/component/VersionStageApprovalButton';

export interface ApprovalCardPros {
  checkItemInfo: VersionStageCheckItem;
  stage: string;
}
const { Title, Text } = Typography;
const VersionStageApprovalCard: React.FC<ApprovalCardPros> = ({ checkItemInfo, stage }) => {
  const [approvalStatus, setApprovalStatus] = useState<CheckItemStatus>(CheckItemStatus.Blocked);
  const [approvalTitle, setApprovalTitle] = useState<string>('');
  const [detailTitle, setDetailTitle] = useState<string>('');
  const [appSetting] = useModel(AppSettingModule);
  const [versionModel] = useModel(VersionInfoModule);
  const avatarUrl = () =>
    typeof checkItemInfo.owner?.avatar === 'string'
      ? checkItemInfo.owner?.avatar
      : checkItemInfo.owner?.avatar?.avatar_240;
  const options = [
    { label: '阻塞', value: CheckItemStatus.Blocked },
    { label: '豁免', value: CheckItemStatus.Exempt },
  ];

  useEffect(() => {
    let currentStage = head(
      versionModel.info.version_stages.filter(
        versionStage =>
          versionStage.stage_name === stage || versionStage.sub_stages.find(it => it.stage_name === stage),
      ),
    );
    if (!currentStage) {
      return;
    }
    currentStage = currentStage.sub_stages.find(it => it.stage_name === stage) ?? currentStage;
    const title = `${versionModel.info.version}-${currentStage?.display_name ?? ''}阶段-${checkItemInfo.description}准出情况`;
    const title2 = `${versionModel.info.version}-${currentStage?.display_name ?? ''}阶段-${checkItemInfo.description}准出详情`;
    setApprovalTitle(title);
    setDetailTitle(title2);
    setApprovalStatus(checkItemInfo.status);
  }, [checkItemInfo, stage, versionModel]);

  const approvalRadioOnChanged = ({ target: { value } }: RadioChangeEvent) => {
    setApprovalStatus(value);
    const newItemInfo = checkItemInfo;
    newItemInfo.status = value as CheckItemStatus;
    updateCheckItem({
      data: {
        appId: appSetting.info.id,
        version: versionModel.info.version,
        stage,
        checkItemId: checkItemInfo.check_item_id,
        updateData: newItemInfo,
      },
    }).then(r => {
      console.log(r);
    });
  };
  return (
    <div className="space-align-container">
      <Text strong>{approvalTitle}</Text>
      <div className="space-align-block">
        <Space style={{ width: '100%' }} size={'middle'} align={'baseline'}>
          <>
            <Space align={'center'}>
              <Text strong>指标Owner: </Text>
              <UserCard
                email={checkItemInfo.owner?.email ?? ''}
                simpleUserData={{
                  avatarUrl: avatarUrl() ?? '',
                  name: `${checkItemInfo.owner?.name ?? ''}`,
                }}
                triggerType="hover"
              />
            </Space>
            <Text strong>准出结论: </Text>
            <Space align={'baseline'}>
              {approvalStatus ? (
                <>
                  <CheckCircleTwoTone twoToneColor={'#52c41a'} style={{ fontSize: 20 }} />
                  <Title type={'success'} level={5}>
                    豁免
                  </Title>
                </>
              ) : (
                <>
                  <CloseCircleTwoTone twoToneColor={'A63636FF'} style={{ fontSize: 20 }} />
                  <Title type={'danger'} level={5}>
                    阻塞
                  </Title>
                </>
              )}
            </Space>
          </>
          <VersionStageApprovalButton
            checkItemInfo={checkItemInfo}
            stage={stage}
            detailTitle={detailTitle}
            statusChanged={status => setApprovalStatus(status)}
          />
        </Space>
      </div>
    </div>
  );
};
export default VersionStageApprovalCard;
