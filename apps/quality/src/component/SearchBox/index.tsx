import { FilterConfirmProps } from 'antd/es/table/interface';
import { Button, Input, Space } from 'antd';
import { SearchOutlined } from '@ant-design/icons';
import React from 'react';

export interface SearchBoxProps {
  setSelectedKeys: (selectedKeys: React.Key[]) => void;
  selectedKeys: string[];
  confirm: (param?: FilterConfirmProps | undefined) => void;
  clearFilters: (() => void) | undefined;
  setSearch: React.Dispatch<React.SetStateAction<string>>;
}

const SearchTextBox: React.FC<SearchBoxProps> = ({
  setSelectedKeys,
  selectedKeys,
  confirm,
  clearFilters,
  setSearch,
}) => {
  const handleSearch = () => {
    confirm();
    setSearch(selectedKeys[0]);
  };
  const handleReset = () => {
    clearFilters?.();
    setSearch('');
    handleSearch();
  };
  return (
    <div style={{ padding: 8 }}>
      <Input
        placeholder={`Search Title`}
        value={selectedKeys[0]}
        onChange={e => setSelectedKeys(e.target.value ? [e.target.value] : [])}
        onPressEnter={() => handleSearch()}
        style={{ marginBottom: 8, display: 'block' }}
      />
      <Space>
        <Button
          type="primary"
          onClick={() => handleSearch()}
          icon={<SearchOutlined />}
          size="small"
          style={{ width: 90 }}
        >
          Search
        </Button>
        <Button onClick={() => clearFilters && handleReset()} size="small" style={{ width: 90 }}>
          Reset
        </Button>
      </Space>
    </div>
  );
};
export default SearchTextBox;
