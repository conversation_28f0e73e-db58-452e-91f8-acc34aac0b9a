import React, { useEffect, useState } from 'react';
import { EditableProTable, ProColumns } from '@ant-design/pro-components';
import { DBLibraInfo } from '../../../../api/model/LibraInfoTable';
import { useModel } from '@edenx/runtime/model';
import AppSettingModule from '@/model/appSettingModel';
import VersionSettingModel from '@/model/versionSettingModel';
import { searchLibraInfoList, updateLibraInfo } from '@api/libra';
import { message, Tag } from 'antd';
import Link from 'antd/es/typography/Link';
import { LibraStatus } from '@shared/libra/commonLibra';
import { keyBy, map } from 'lodash';
import { NetworkCode } from '@pa/shared/dist/src/core';

const LibraInfoTable: React.FC = () => {
  const [appSettingState] = useModel(AppSettingModule);
  const [versionSettingState] = useModel(VersionSettingModel);
  const [editableKeys, setEditableRowKeys] = useState<React.Key[]>([]);
  const [libraInfoList, setLibraInfoList] = useState<readonly DBLibraInfo[]>([]);
  const [refresh, setRefresh] = useState<boolean>(false);
  const [tableLoading, setTableLoading] = useState<boolean>(false);
  const [pinFirst, setPinFirst] = useState<boolean>(true);
  const buildSelectOptions = (enumData: typeof LibraStatus) =>
    keyBy(
      map(enumData, (value, key) => ({
        text: value,
        status: key,
      })),
      value => value.text,
    );
  const columns: ProColumns<DBLibraInfo>[] = [
    {
      title: '序号',
      rowScope: 'row',
      width: '2%',
      align: 'center',
      dataIndex: 'index',
      valueType: 'index',
      fixed: pinFirst,
    },
    {
      title: '实验名称',
      width: '8%',
      editable: false,
      fixed: pinFirst,
      render: (value, item, index) => (
        <Link href={item.flight_url} target={'_blank'}>
          {item.name}
        </Link>
      ),
    },
    {
      title: '产品',
      width: '2%',
      align: 'center',
      valueType: 'textarea',
      dataIndex: 'product',
    },
    {
      title: '平台',
      width: '2%',
      align: 'center',
      valueType: 'textarea',
      dataIndex: 'platform',
    },
    {
      title: '版本',
      width: '2%',
      align: 'center',
      valueType: 'textarea',
      dataIndex: 'version',
    },
    {
      title: '当前放量',
      width: '2%',
      align: 'center',
      valueType: 'textarea',
      render: (value, item) => <p>{item.numerical_traffic * 100}%</p>,
    },
    {
      title: '状态',
      width: '2%',
      align: 'center',
      dataIndex: 'status',
      valueType: 'select',
      valueEnum: buildSelectOptions(LibraStatus),
      filters: [...new Set(libraInfoList?.map(value => value.status))].map(value => ({
        text: value,
        value,
      })),
      onFilter: (value: React.Key | boolean, record) => record.status === value,
      render: (_, row) => {
        if (row.status === LibraStatus.stopped) {
          return <Tag color="error">stopped</Tag>;
        } else if (row.status === LibraStatus.running) {
          return <Tag color="success">running</Tag>;
        } else if (row.status === LibraStatus.testing) {
          return <Tag color="processing">testing</Tag>;
        } else if (row.status === LibraStatus.suspended) {
          return <Tag color="processing">suspended</Tag>;
        } else {
          return <></>;
        }
      },
    },
    {
      title: '负责人',
      width: '5%',
      align: 'center',
      valueType: 'textarea',
      dataIndex: 'owner',
    },
    {
      title: '阶段放量',
      width: '5%',
      align: 'center',
      valueType: 'textarea',
      render: (value, item) => <p>{JSON.stringify(item.stageTraffic)}</p>,
    },
    {
      title: '未放量提醒',
      width: '5%',
      align: 'center',
      valueType: 'textarea',
      render: (value, item) => <p>{JSON.stringify(item.pushTimesByDay)}</p>,
    },
    {
      title: '观察提醒',
      width: '5%',
      align: 'center',
      valueType: 'textarea',
      render: (value, item) => <p>{JSON.stringify(item.pushTimesByStage)}</p>,
    },
    {
      title: '操作',
      valueType: 'option',
      align: 'center',
      width: '5%',
      render: (z, record, _, action) => {
        if (record.status !== LibraStatus.stopped && record.status !== LibraStatus.running) {
          return [
            <a
              key="editable"
              onClick={() => {
                action?.startEditable?.(record.id);
              }}
            >
              编辑
            </a>,
          ];
        } else {
          return [];
        }
      },
    },
  ];

  useEffect(() => {
    message.success(`${JSON.stringify(versionSettingState.info)}`).then();
  }, [appSettingState, versionSettingState]);

  return (
    <>
      <EditableProTable<DBLibraInfo>
        columns={columns}
        value={libraInfoList}
        loading={tableLoading}
        rowKey="id"
        scroll={{
          x: 2000,
          y: 770,
          scrollToFirstRowOnChange: true,
        }}
        recordCreatorProps={false}
        params={{
          product: appSettingState.info.name === '剪映' ? 'lv' : 'cc',
          platform: appSettingState.info.platform,
          version:
            appSettingState.info.name === '剪映'
              ? versionSettingState.info.lvVersion
              : versionSettingState.info.ccVersion,
          refresh,
        }}
        request={async params => {
          if (params.product !== '' && params.platform !== '' && params.version !== '') {
            setTableLoading(true);
            const fetchResult = await searchLibraInfoList({
              data: { product: params.product, platform: params.platform, version: params.version },
            });
            setTableLoading(false);
            return {
              data: fetchResult,
              success: true,
            };
          } else {
            return {
              success: false,
            };
          }
        }}
        onChange={setLibraInfoList}
        editable={{
          type: 'multiple',
          editableKeys,
          actionRender: (row, _, defaultDos) => {
            if (row.status !== LibraStatus.stopped && row.status !== LibraStatus.running) {
              return [defaultDos.save, defaultDos.cancel];
            } else {
              return [];
            }
          },
          onSave: async (_, record) => {
            console.log(record);
            const result = await updateLibraInfo({
              data: {
                id: record.id,
                product: appSettingState.info.name === '剪映' ? 'lv' : 'cc',
                platform: appSettingState.info.platform,
                version:
                  appSettingState.info.name === '剪映'
                    ? versionSettingState.info.lvVersion
                    : versionSettingState.info.ccVersion,
                status: record.status,
              },
            });
            if (result.code !== NetworkCode.Success) {
              await message.error(result.message);
            } else {
              await message.success(`更新成功:status=${record.status}`);
            }
          },
          onChange: setEditableRowKeys,
        }}
      />
    </>
  );
};

export default LibraInfoTable;
