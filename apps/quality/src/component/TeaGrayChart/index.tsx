import { AlarmVersion, Business, BusinessName, TeaGranularity } from '@shared/typings/tea/metric';
import { useEffect, useState } from 'react';
import { DBTeaMetric } from '../../../api/model/TeaMetricTable';
import { getTeaVersionedMetric } from '@api/tea';
import { get_enum_value, to_platform } from '@shared/utils/tools';
import TeaVersionedChart from '@/component/Charts/TeaVersionedChart';
import { Col, Collapse, CollapseProps, Row, Skeleton } from 'antd';
import { groupBy, toPairs } from 'lodash';
import { queryUserCount } from '@api/slardar';
import { DBSlardarUserCount } from '../../../api/model/SlardarUserCountTable';
import { useModel } from '@edenx/runtime/model';
import AppSettingModule from '@/model/appSettingModel';

export interface GrayTeaChartProps {
  versions: [AlarmVersion, AlarmVersion];
  granularity: TeaGranularity;
}

const TeaGrayChart: React.FC<GrayTeaChartProps> = ({ versions, granularity }) => {
  const [metrics, setMetrics] = useState<DBTeaMetric[]>([]);
  const [userCount, setUserCount] = useState<DBSlardarUserCount[]>([]);
  const [loading, setLoading] = useState(true);

  const [{ info }] = useModel(AppSettingModule);

  const update = async () => {
    setLoading(true);
    await Promise.all([
      getTeaVersionedMetric({
        data: {
          platform: to_platform(info.platform),
        },
      }).then(setMetrics),
      queryUserCount({
        data: {
          aid: info.businessInfo.aid,
          versions: versions.map(it => it.versionCode),
        },
      }).then(setUserCount),
    ]);
    setLoading(false);
  };

  useEffect(() => {
    update();
  }, []);

  const items: CollapseProps['items'] = toPairs(groupBy(metrics, it => it.Business)).map(([k, v]) => ({
    key: get_enum_value(Business, k),
    label: BusinessName[get_enum_value(Business, k)],
    children: (
      <Row gutter={[16, 24]}>
        {v.map(it => (
          <Col span={8} key={it.Name}>
            <TeaVersionedChart
              title={it.DisplayName}
              teaMetric={it}
              versions={versions}
              granularity={granularity}
              userCount={userCount}
            />
          </Col>
        ))}
      </Row>
    ),
  }));

  return loading ? (
    <Skeleton style={{ width: '100%' }} active />
  ) : (
    <Collapse style={{ width: '100%' }} items={items} defaultActiveKey={Business.CLOUD_SPACE} />
  );
};

export default TeaGrayChart;
