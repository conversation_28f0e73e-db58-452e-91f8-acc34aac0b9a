import React, { useEffect, useState } from 'react';
import AppSettingModule from '@/model/appSettingModel';
import { useModel } from '@edenx/runtime/model';
import { ProForm } from '@ant-design/pro-form';
import { message } from 'antd';
import KaniButton from '@/component/KaniButton/kaniButton';
import { queryAirplaneConfigItem, updateAirplaneConfigItem } from '@api/airplaneConfig';
import { Issue, buildSettingItem } from '@/pages/settings/slardar/autoLevel/SettingsItemFactory';
import { AirplaneConfig, VERSION_CONFIG, VersionConfigKeys } from '@shared/aircraftConfiguration';

const AutoLevelSettings: React.FC = () => {
  const [appSettingState] = useModel(AppSettingModule);
  const [form] = ProForm.useForm();
  const [messageApi, contextHolder] = message.useMessage();
  const [loadingData, setLoadingData] = useState<boolean>(true);
  const initFromData = async () => {
    const msgKey = 'initFromData';
    setLoadingData(true);
    messageApi.open({ key: msgKey, type: 'loading', content: '加载数据中，请勿刷新页面' });
    const configData: Issue[] = await queryAirplaneConfigItem({
      data: {
        businessId: '1775',
        configName: VersionConfigKeys.autoLevelBlackList,
        platform: appSettingState.info.platform,
      },
    });
    form.setFieldValue(VersionConfigKeys.autoLevelBlackList, configData);
    setLoadingData(false);
    messageApi.destroy(msgKey);
  };

  useEffect(() => {
    initFromData();
  }, [form, appSettingState]);

  const transform = (input: any) => {
    const result: { [key: string]: any } = {};
    Object.keys(input).forEach(key => {
      const value = input[key];
      if (Array.isArray(value)) {
        // 如果是数组，替换数组中每个对象为它的 data 属性
        result[key] = value.map(item => item.value);
      } else if (typeof value === 'object' && value !== null && 'value' in value) {
        // 如果是对象，并且包含 data 属性，使用 data 属性替换整个对象
        result[key] = JSON.parse(value.value);
      } else {
        // 如果既不是数组也不是含有 data 属性的对象，保持原样
        result[key] = value;
      }
    });

    return result;
  };

  const transformObjectToArray = (obj: any) =>
    Object.keys(obj).map(key => {
      const dataConfig = VERSION_CONFIG[key];
      const data: AirplaneConfig = {
        businessId: '1775',
        name: key,
        data: obj[key],
        configType: dataConfig.configType,
      };
      if (dataConfig.requestPlatform) {
        data.platform = appSettingState.info.platform;
      }
      return data;
    });

  const handleSubmit = async (_: any) => {
    const values = form.getFieldsValue();
    // 这里可以添加将表单数据发送到后端的代码
    const msgKey = 'handleSubmit';
    messageApi.open({ key: msgKey, type: 'loading', content: '修改中，请勿刷新页面' });
    const data = transform(values);
    const configList = transformObjectToArray(data);
    await updateAirplaneConfigItem({ data: configList });
    messageApi.open({ key: msgKey, type: 'success', content: '修改成功' });
    setTimeout(() => {
      messageApi.destroy(msgKey);
    }, 3000);
  };
  return (
    <>
      {contextHolder}
      <ProForm
        onFinish={handleSubmit}
        form={form}
        submitter={{
          render: props => [
            <KaniButton
              disabled={loadingData}
              resource_key="paper_airplane_config_version"
              permission_name="Change"
              key="submit"
              type="primary"
              onClick={() => form.submit()}
            >
              提交
            </KaniButton>,
          ],
        }}
      >
        {buildSettingItem(form, 'autoLevelBlackList', VERSION_CONFIG.autoLevelBlackList, appSettingState.info.platform)}
      </ProForm>
    </>
  );
};

export default AutoLevelSettings;
