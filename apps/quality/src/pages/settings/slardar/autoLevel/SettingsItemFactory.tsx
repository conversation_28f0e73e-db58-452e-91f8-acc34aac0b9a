import React from 'react';
import { Avatar, Image, Space } from 'antd';
import { pick } from 'lodash';
import { searchLarkChat, searchUser } from '@api/index';
import { FormInstance } from '@ant-design/pro-components';
import { ChatInfo, User } from '@pa/shared/dist/src/core';
import ProFormSelector from '@/component/ProFormSelector';
import { ConfigType, SettingsConfigItem } from '@shared/aircraftConfiguration';

export interface Issue {
  os?: string;
  crash_type?: string;
  issue_id?: string;
}

function parseFromLink(link: string) {
  const idRegex = /os=(\w+).*\/detail\/(\w+?)\/(\w+)/g;
  const m = idRegex.exec(link);
  if (m && m.length >= 4) {
    return {
      os: m[1],
      crash_type: m[2],
      issue_id: m[3],
    };
  }
  return {};
}

export const buildLarkChatValue = (chatInfo: ChatInfo) => ({
  label: (
    <Space align={'center'}>
      {chatInfo.avatar ? <Avatar size={18} src={<Image preview={false} src={chatInfo.avatar.toString()} />} /> : <></>}
      <span>{chatInfo.name}</span>
    </Space>
  ),
  key: chatInfo.chat_id,
  value: JSON.stringify(pick(chatInfo, ['avatar', 'chat_id', 'name'])),
});
export const buildUserSelectValue = (user: User) => ({
  label: (
    <Space>
      {user.avatar ? <Avatar size={18} src={<Image preview={false} src={user.avatar.toString()} />} /> : <></>}
      <span>{user.name}</span>
    </Space>
  ),
  key: user.open_id,
  value: JSON.stringify(user),
});
export const buildSlardarIssueSelectValue = (issue: Issue) => ({
  label: (
    <Space>
      <span>{issue.issue_id}</span>
    </Space>
  ),
  key: issue.issue_id,
  value: issue.issue_id,
});

const searchUserList = async (keyWard: string): Promise<User[]> => {
  const result = await searchUser({ data: { value: keyWard, token: '' } });
  return result.data || [];
};

const searchLarkChatList = async (keyWard: string): Promise<ChatInfo[]> => {
  const result = await searchLarkChat({ data: { keyWords: keyWard } });
  return result || [];
};

const searchIssue = async (keyWord: string): Promise<Issue[]> => {
  const issue = parseFromLink(keyWord);
  if ('issue_id' in issue) {
    return [issue];
  } else {
    return [];
  }
};

const buildUserSelect = (
  form: FormInstance,
  name: string,
  config: SettingsConfigItem,
  platform: string,
): React.ReactNode => (
  <ProFormSelector<User>
    buildSelectValue={buildUserSelectValue}
    search={searchUserList}
    name={name}
    form={form}
    key={name}
    label={`${config.label}${config.requestPlatform ? `（平台相关配置, 当前平台:${platform}）` : ''}`}
    placeholder={config.placeholder}
    mode={config.configType === ConfigType.user_array ? 'multiple' : undefined}
    rules={config.rules}
  />
);

const buildChatSelector = (
  form: FormInstance,
  name: string,
  config: SettingsConfigItem,
  platform: string,
): React.ReactNode => (
  <ProFormSelector<ChatInfo>
    buildSelectValue={buildLarkChatValue}
    search={searchLarkChatList}
    name={name}
    form={form}
    key={name}
    label={`${config.label}${config.requestPlatform ? `（平台相关配置, 当前平台:${platform}）` : ''}`}
    placeholder={config.placeholder}
    mode={config.configType === ConfigType.chat_array ? 'multiple' : undefined}
    rules={config.rules}
  />
);

const buildSlardarIssueListSelector = (
  form: FormInstance,
  name: string,
  config: SettingsConfigItem,
  platform: string,
): React.ReactNode => (
  <ProFormSelector<Issue>
    buildSelectValue={buildSlardarIssueSelectValue}
    search={searchIssue}
    name={name}
    form={form}
    key={name}
    label={`${config.label}${config.requestPlatform ? `（平台相关配置, 当前平台:${platform}）` : ''}`}
    placeholder={config.placeholder}
    mode={config.configType === ConfigType.slardar_issue_list ? 'multiple' : undefined}
    rules={config.rules}
  />
);

export const buildSettingItem = (
  form: FormInstance,
  configName: string,
  config: SettingsConfigItem,
  platform: string,
): React.ReactNode => {
  if (config.configType === ConfigType.user_array || config.configType === ConfigType.user) {
    return buildUserSelect(form, configName, config, platform);
  }
  if (config.configType === ConfigType.chat || config.configType === ConfigType.chat_array) {
    return buildChatSelector(form, configName, config, platform);
  }
  if (config.configType === ConfigType.slardar_issue_list) {
    return buildSlardarIssueListSelector(form, configName, config, platform);
  }
};
