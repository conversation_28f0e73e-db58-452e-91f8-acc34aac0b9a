import { add_prefix } from '@shared/utils/tools';
import { Card, Col, Row } from 'antd';
import { useEffect, useState } from 'react';
import { queryBackendVersionInfo } from '@api/airplaneConfig';

export interface BuildInfo {
  commit_hash: string;
  commit_message: string;
  build_time: string;
  region?: string;
}

const fe_build: Readonly<BuildInfo> = {
  commit_hash: 'DEV_COMMIT_HASH_HERE',
  commit_message: 'DEV_COMMIT_MESSAGE_HERE',
  build_time: 'DEV_BUILD_TIME_HERE',
};

const commit_hash_to_url = add_prefix('https://code.byted.org/faceu-android/bits-ci/commit/');

const BuildInfoCard: React.FC = () => {
  const [backendInfo, setBackendInfo] = useState<BuildInfo>();

  useEffect(() => {
    queryBackendVersionInfo().then((it: BuildInfo) => setBackendInfo(it));
  }, []);

  return (
    <Row gutter={16}>
      <Col span={12}>
        <Card title={'前端构建信息'} bordered>
          构建时间： {fe_build.build_time.startsWith('DEV_') ? '开发版' : fe_build.build_time} <br />
          Commit 信息： {fe_build.commit_message.startsWith('DEV_') ? '开发版' : fe_build.commit_message} <br />
          Commit Hash：{' '}
          {fe_build.commit_hash.startsWith('DEV_') ? (
            '开发版'
          ) : (
            <a href={commit_hash_to_url(fe_build.commit_hash)}>{fe_build.commit_hash}</a>
          )}
        </Card>
      </Col>{' '}
      <Col span={12}>
        <Card title={'后端构建信息'} bordered loading={backendInfo === undefined}>
          构建时间： {backendInfo?.build_time.startsWith('DEV_') ? '开发版' : backendInfo?.build_time} <br />
          Commit 信息： {backendInfo?.commit_message.startsWith('DEV_') ? '开发版' : backendInfo?.commit_message} <br />
          Commit Hash：{' '}
          {backendInfo?.commit_hash.startsWith('DEV_') ? (
            '开发版'
          ) : (
            <a href={commit_hash_to_url(backendInfo?.commit_hash ?? '')}>{backendInfo?.commit_hash}</a>
          )}
          <br />
          后端部署区域：{backendInfo?.region}
        </Card>
      </Col>
    </Row>
  );
};

export default BuildInfoCard;
