import React, { useEffect, useState } from 'react';
import { Table, Input, TagGroup, Tag, Popover, Toast, Typography, Space, Button, Spin, Empty } from '@douyinfe/semi-ui';
import type { ColumnProps } from '@douyinfe/semi-ui/lib/es/table';
import { LibraMeegoTeamInfo } from '@shared/libra/LibraNewInfo';
import {
  addMeegoTeamInfoToLibraList,
  fetchBusinessMemberList,
  fetchUserMemberList,
  getMeegoTeamList,
  getRoleTypeByEmail,
  updateChatIdOfMeegoTeamInfo,
  updateMeegoTeamList,
} from '@api/libra';
import { MeegoProjectKeyFaceU, MeegoTeamIds } from '@shared/libra/libraManageUtils';
import SemiReactUserGroup from '@/component/UserAvatarGroup';
import { User } from '@pa/shared/dist/src/core';
import { IconEdit } from '@douyinfe/semi-icons';
import { LibraPlatformPermission, LibraPlatformRoleType } from '@shared/libra/LibraPlatformUserMemberInfo';
import { useModel } from '@edenx/runtime/model';
import UserSettingModule from '@/model/userSettingModel';
import { IllustrationConstruction, IllustrationConstructionDark } from '@douyinfe/semi-illustrations';
const { Text } = Typography;

const MeegoTeamChatIdEditor: React.FC<{ teamInfo: LibraMeegoTeamInfo; onSaveEdit: () => void }> = ({
  teamInfo,
  onSaveEdit,
}) => {
  const [isEditing, setIsEditing] = useState(false);
  const [inputValue, setInputValue] = useState(teamInfo.larkChatId);
  const [loading, setLoading] = useState(false);

  const checkChatIdValid = (chatId: string | undefined) => {
    if (!isEditing) {
      return true;
    }
    if (!chatId) {
      return false;
    }
    // 检查 chatId 是否合法
    const reg = /^oc_[0-9a-zA-Z]{32}$/;
    return reg.test(chatId);
  };

  return (
    <div>
      <Space vertical={false}>
        <Input
          value={inputValue}
          disabled={!isEditing}
          showClear={true}
          maxLength={35}
          onChange={(value: string, e: React.ChangeEvent<HTMLInputElement>) => {
            setInputValue(value);
          }}
          placeholder="请输入飞书群 chat_id"
          validateStatus={checkChatIdValid(inputValue) ? 'success' : 'error'}
        />
        <Button
          loading={loading}
          disabled={!checkChatIdValid(inputValue) && isEditing}
          onClick={async () => {
            if (isEditing) {
              // 保存编辑
              setLoading(true);
              // 更新 Meego Team Info
              const result = await updateChatIdOfMeegoTeamInfo({
                data: {
                  teamId: teamInfo.teamId,
                  chatId: inputValue ?? '',
                },
              });
              if (result.code === 0) {
                Toast.success(`更新成功！chat_id: ${inputValue}`);
                setLoading(false);
                setIsEditing(!isEditing);
                onSaveEdit();
              } else {
                Toast.error(`更新失败！error: ${result.message}`);
                setLoading(false);
                setIsEditing(!isEditing);
              }
            } else {
              // 进入编辑模式
              setIsEditing(!isEditing);
            }
          }}
        >
          {isEditing ? '保存' : '编辑'}
        </Button>
        {isEditing && (
          <Button
            onClick={() => {
              setIsEditing(false);
              setInputValue(teamInfo.larkChatId ?? '');
            }}
          >
            取消编辑
          </Button>
        )}
      </Space>
    </div>
  );
};

const MeegoTeamManagePage: React.FC = () => {
  const [roleTypeLoading, setRoleTypeLoading] = useState(false);
  const [roleType, setRoleType] = useState<LibraPlatformRoleType>(LibraPlatformRoleType.Guest);
  const [userSettingState] = useModel(UserSettingModule);
  const [meegoTeamList, setMeegoTeamList] = useState<LibraMeegoTeamInfo[]>([]);
  const [loading, setLoading] = useState(false);
  const [libraListQueryStr, setLibraListQueryStr] = useState('');
  const [addMeegoTeamInfoToLibraListLoading, setAddMeegoTeamInfoToLibraListLoading] = useState(false);

  const refreshTeamList = async () => {
    setLoading(true);
    await getMeegoTeamList({
      data: {
        teamIds: MeegoTeamIds,
        projectKey: MeegoProjectKeyFaceU,
      },
    })
      .then(res => {
        if (!res) {
          throw new Error('fetch meego team list failed!');
        }
        setMeegoTeamList(res);
        setLoading(false);
      })
      .catch(err => {
        Toast.error(err.message);
        setLoading(false);
      });
  };

  useEffect(() => {
    setRoleTypeLoading(true);
    if (userSettingState.info.email) {
      getRoleTypeByEmail({
        data: {
          email: userSettingState.info.email,
        },
      })
        .then(async res => {
          if (res?.code === 0) {
            setRoleType(res.roleType);
            // 如果是授权的用户，则拉取一下团队信息
            if (res.roleType & LibraPlatformPermission.Grant) {
              await refreshTeamList();
            }
          }
          setRoleTypeLoading(false);
        })
        .catch(err => {
          Toast.error(`获取用户权限失败，error: ${err.message}`);
          setRoleTypeLoading(false);
        });
    }
  }, [userSettingState.info]);

  const columns: ColumnProps<LibraMeegoTeamInfo>[] = [
    {
      title: '团队ID',
      dataIndex: 'teamId',
      width: 120,
    },
    {
      title: '团队名称',
      dataIndex: 'teamName',
      render: text => <Tag>{text}</Tag>,
    },
    {
      title: '管理员',
      dataIndex: 'administrators',
      render: (text, record, index, options) => (
        // <TagGroup
        //   maxTagCount={2}
        //   tagList={record.administrators.map(admin => ({
        //     color: 'blue',
        //     children: admin.email,
        //   }))}
        // />
        <SemiReactUserGroup users={record.administrators as User[]} triggerType={'hover'} />
      ),
    },
    {
      title: '成员',
      dataIndex: 'members',
      render: users => `${users.length}人`,
      // render: (text, record, index, options) => {
      //   const displayMembers: string[] = [];
      //   record.members?.forEach(member => {
      //     if (member.email && !displayMembers.includes(member.email)) {
      //       displayMembers.push(member.email);
      //     } else if (member.name && !displayMembers.includes(member.name)) {
      //       displayMembers.push(member.name);
      //     }
      //   });
      //   return <Text>{displayMembers.join('\n')}</Text>;
      // },
    },
    {
      title: 'Meego 空间',
      dataIndex: 'projectKey',
    },
    {
      title: '飞书群ID',
      dataIndex: 'larkChatId',
      render: (text, record, index, options) => (
        <MeegoTeamChatIdEditor teamInfo={record} onSaveEdit={() => refreshTeamList()} />
      ),
    },
  ];

  const handleUpdateMeegoTeamInfo = async () => {
    setLoading(true);
    const result = await updateMeegoTeamList({
      data: {
        teamIds: MeegoTeamIds,
        projectKey: MeegoProjectKeyFaceU,
      },
    });
    if (result.code === 0) {
      Toast.success('从 Meego 更新团队信息成功！');
      refreshTeamList();
      setLoading(false);
    } else {
      Toast.error(`从 Meego 更新团队信息失败！error: ${result.message}`);
      setLoading(false);
    }
  };

  const isValidJSON = (str: string) => {
    try {
      const parsed = JSON.parse(str);
      return parsed !== null && (typeof parsed === 'object' || Array.isArray(parsed));
    } catch (e) {
      return false;
    }
  };

  const handleAddMeegoTeamInfoToLibraList = async () => {
    let query = {};
    if (libraListQueryStr.length > 0) {
      if (!isValidJSON(libraListQueryStr)) {
        Toast.error('query 格式不正确！请检查 query 格式是否正确');
        return;
      }
      try {
        query = JSON.parse(libraListQueryStr);
      } catch (err) {
        Toast.error('query 解析失败！请检查 query 格式是否正确');
        return;
      }
    }

    setAddMeegoTeamInfoToLibraListLoading(true);
    addMeegoTeamInfoToLibraList({
      data: {
        teamIds: MeegoTeamIds,
        projectKey: MeegoProjectKeyFaceU,
        query,
      },
    })
      .then(res => {
        if (!res) {
          throw new Error('add meego team info to libra list failed!');
        }
        if (res.code === 0) {
          Toast.success('更新实验列表 Meego 团队信息成功！');
          setAddMeegoTeamInfoToLibraListLoading(false);
        } else {
          Toast.error(`更新实验列表 Meego 团队信息失败！error: ${res.message}`);
          setAddMeegoTeamInfoToLibraListLoading(false);
        }
      })
      .catch(err => {
        Toast.error(`更新实验列表 Meego 团队信息失败！error: ${err.message}`);
        setAddMeegoTeamInfoToLibraListLoading(false);
      });
  };

  return (
    <Spin spinning={roleTypeLoading}>
      {roleTypeLoading ? (
        <></>
      ) : (
        <>
          {!(roleType & LibraPlatformPermission.Grant) && (
            <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '80vh' }}>
              <Empty
                image={<IllustrationConstruction style={{ width: 150, height: 150 }} />}
                darkModeImage={<IllustrationConstructionDark style={{ width: 150, height: 150 }} />}
                title={'未授权'}
                description="请联系管理员"
              />
            </div>
          )}
          {Boolean(roleType & LibraPlatformPermission.Grant) && (
            <div>
              <Space vertical={true} align={'start'} style={{ width: '100%' }}>
                <Space vertical={false} style={{ width: '100%', justifyContent: 'space-around' }}>
                  <Space vertical={false} align={'center'} style={{ width: '100%', justifyContent: 'start' }}>
                    <Button onClick={handleUpdateMeegoTeamInfo} loading={loading}>
                      从 Meego 更新团队信息
                    </Button>
                  </Space>
                  <Space vertical={false} align={'center'} style={{ width: '100%', justifyContent: 'end' }}>
                    <Space vertical={false} style={{ border: '1px solid #ddd', borderRadius: '4px', padding: '12px' }}>
                      <Button onClick={handleAddMeegoTeamInfoToLibraList} loading={addMeegoTeamInfoToLibraListLoading}>
                        批量刷新实验 Meego 团队信息
                      </Button>
                      <Input
                        style={{ width: 300 }}
                        value={libraListQueryStr}
                        showClear={true}
                        placeholder="可选：query查询，更新指定实验的团队信息"
                        onChange={value => {
                          setLibraListQueryStr(value);
                        }}
                      />
                    </Space>
                  </Space>
                </Space>
                <Table
                  rowKey="teamId"
                  loading={loading}
                  columns={columns}
                  dataSource={meegoTeamList}
                  pagination={{ pageSize: 10 }}
                  bordered
                />
              </Space>
            </div>
          )}
        </>
      )}
    </Spin>
  );
};

export default MeegoTeamManagePage;
