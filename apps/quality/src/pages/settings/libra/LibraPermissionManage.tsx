import React, { useEffect, useState } from 'react';
import {
  LibraPlatformBusinessLineMemberInfo,
  LibraPlatformPermission,
  LibraPlatformRoleType,
  LibraPlatformUserMemberInfo,
} from '@shared/libra/LibraPlatformUserMemberInfo';
import {
  Banner,
  Button,
  Empty,
  Input,
  Modal,
  Popconfirm,
  Radio,
  RadioGroup,
  Space,
  Spin,
  Table,
  TabPane,
  Tabs,
  Toast,
  Typography,
} from '@douyinfe/semi-ui';
import { useModel } from '@edenx/runtime/model';
import UserSettingModule from '@/model/userSettingModel';
import {
  fetchBusinessMemberList,
  fetchUserMemberList,
  getRoleTypeByEmail,
  updateLibraBusinessMember,
  updateLibraUserMember,
} from '@api/libra';
import { User } from '@pa/shared/dist/src/core';
import { RadioChangeEvent } from '@douyinfe/semi-foundation/lib/es/radio/radioInnerFoundation';
import { IconDelete, IconEdit, IconUserAdd } from '@douyinfe/semi-icons';
import SemiUserSelector from '@/component/SemiUserSelector';
import { ColumnProps } from '@douyinfe/semi-ui/lib/es/table';
import UserCard from '@/component/UserCard';
import { LibraRoleType2DisplayNameMap } from '@shared/libra/libraManageUtils';
import { IllustrationConstruction, IllustrationConstructionDark } from '@douyinfe/semi-illustrations';

const { Text } = Typography;

enum PermissionMemberType {
  SingleUser = 0,
  BusinessLine = 1,
}

const AddNewMemberButton: React.FC<{
  memberType: PermissionMemberType; // 0: 用户，1：业务线
  initUserMember?: User;
  initBusinessLineName?: string;
  initRoleType?: LibraPlatformRoleType;
  onUpdateComplete: () => void;
  isEdit?: boolean;
}> = ({ memberType, initUserMember, initBusinessLineName, onUpdateComplete, isEdit, initRoleType }) => {
  const [selectedRoleType, setSelectedRoleType] = useState<LibraPlatformRoleType | undefined>();
  const [selectedUser, setSelectedUser] = useState<User | undefined>(initUserMember);
  const [businessLineName, setBusinessLineName] = useState(initBusinessLineName ?? '');
  const [confirmVisible, setConfirmVisible] = useState(false);
  const onRoleSelectChange = (e: RadioChangeEvent) => {
    setSelectedRoleType(e.target.value);
  };

  useEffect(() => {
    setSelectedUser(initUserMember);
  }, [initUserMember]);

  useEffect(() => {
    setBusinessLineName(initBusinessLineName ?? '');
  }, [initBusinessLineName]);

  useEffect(() => {
    setSelectedRoleType(initRoleType);
  }, [initRoleType]);

  const handleConfirmOK = async () => {
    if (selectedRoleType === undefined) {
      Toast.error('请选择权限类型！');
      return;
    }
    if (memberType === PermissionMemberType.SingleUser) {
      if (!selectedUser) {
        Toast.error('请选择用户！');
        return;
      }
      await updateLibraUserMember({
        data: {
          userEmail: selectedUser.email ?? '',
          roleType: selectedRoleType,
        },
      });
    }
    if (memberType === PermissionMemberType.BusinessLine) {
      if (!businessLineName.length) {
        Toast.error('请输入部门名称！');
        return;
      }
      await updateLibraBusinessMember({
        data: {
          businessLine: businessLineName,
          roleType: selectedRoleType,
        },
      });
    }
    onUpdateComplete();
    setConfirmVisible(false);
  };

  const handleConfirmCancel = () => {
    setConfirmVisible(false);
  };

  const showConfirm = () => {
    setConfirmVisible(true);
  };

  useEffect(() => {
    setSelectedUser(initUserMember);
    setSelectedRoleType(initRoleType);
    setBusinessLineName(initBusinessLineName ?? '');
  }, [confirmVisible]);

  return (
    <>
      {!isEdit && (
        <Button icon={<IconUserAdd />} type={'primary'} theme="solid" onClick={showConfirm}>
          {memberType === 0 ? '添加成员' : '添加业务线'}
        </Button>
      )}
      {isEdit && <Button icon={<IconEdit />} onClick={showConfirm} />}
      <Modal
        title={memberType === PermissionMemberType.SingleUser ? '添加成员' : '添加业务线'}
        visible={confirmVisible}
        onOk={handleConfirmOK}
        onCancel={handleConfirmCancel}
        closeOnEsc={true}
      >
        <Space vertical={true} spacing={'tight'} style={{ width: '100%' }} align="start">
          {memberType === 0 && (
            <>
              <Text strong>
                用户 <span style={{ color: 'red' }}>*</span>
              </Text>
              <SemiUserSelector
                initUsers={selectedUser ? [selectedUser] : []}
                disabled={Boolean(initUserMember)}
                style={{ width: '100%' }}
                multi={false}
                updateSelectedUsers={users => {
                  if (!users || users.length === 0) {
                    return;
                  }
                  const user = users[0];
                  if (!user.email) {
                    Toast.error('选择的用户邮箱信息为空！');
                    return;
                  }
                  setSelectedUser(user);
                }}
              />
            </>
          )}
          {memberType === PermissionMemberType.BusinessLine && (
            <>
              <Text strong>
                业务线 <span style={{ color: 'red' }}>*</span>
              </Text>
              <Input
                style={{ width: '100%' }}
                value={businessLineName}
                disabled={Boolean(initBusinessLineName)}
                onChange={e => {
                  setBusinessLineName(e);
                }}
              />
            </>
          )}
          <Text strong>角色</Text>
          <RadioGroup
            onChange={onRoleSelectChange}
            value={selectedRoleType}
            aria-label="选择角色"
            name="demo-radio-group"
            style={{ width: '100%' }}
          >
            <Radio value={LibraPlatformRoleType.Guest}>访客</Radio>
            <Radio value={LibraPlatformRoleType.NomalUser}>普通用户</Radio>
            <Radio value={LibraPlatformRoleType.ProductUser}>产品成员</Radio>
            <Radio value={LibraPlatformRoleType.Admin} disabled={memberType === PermissionMemberType.BusinessLine}>
              管理员
            </Radio>
          </RadioGroup>
        </Space>
      </Modal>
    </>
  );
};

const LibraPermissionManage: React.FC = () => {
  const [roleType, setRoleType] = useState<LibraPlatformRoleType>(LibraPlatformRoleType.Guest);
  const [loading, setLoading] = useState(false);
  const [memberListLoading, setMemberListLoading] = useState(false);
  const [userMemberList, setUserMemberList] = useState<LibraPlatformUserMemberInfo[]>([]);
  const [businessMemberList, setBusinessMemberList] = useState<LibraPlatformBusinessLineMemberInfo[]>([]);

  const [userSettingState] = useModel(UserSettingModule);

  useEffect(() => {
    setLoading(true);
    if (userSettingState.info.email) {
      getRoleTypeByEmail({
        data: {
          email: userSettingState.info.email,
        },
      }).then(res => {
        if (res?.code === 0) {
          setRoleType(res.roleType);
        }
        setLoading(false);
      });
    }
  }, [userSettingState.info]);

  const refreshUserRoleType = async () => {
    setLoading(true);
    if (userSettingState.info.email) {
      getRoleTypeByEmail({
        data: {
          email: userSettingState.info.email,
        },
      }).then(res => {
        if (res?.code === 0) {
          setRoleType(res.roleType);
        }
        setLoading(false);
      });
    }
  };

  const refreshMemberList = async () => {
    setMemberListLoading(true);
    await fetchUserMemberList({
      data: {},
    }).then(res => {
      if (res?.code === 0) {
        setUserMemberList(res.userMemberList);
      }
    });
    await fetchBusinessMemberList({
      data: {},
    }).then(res => {
      if (res?.code === 0) {
        setBusinessMemberList(res.businessMemberList);
      }
    });
    setMemberListLoading(false);
  };
  useEffect(() => {
    refreshMemberList();
  }, []);

  const userMemberColumns: ColumnProps<LibraPlatformUserMemberInfo>[] = [
    {
      title: '用户名',
      dataIndex: 'email',
      key: 'email',
      width: '60%',
      render: (_: any, record: LibraPlatformUserMemberInfo) => (
        <UserCard
          email={record.userInfo.email}
          triggerType="hover"
          simpleUserData={{
            avatarUrl:
              typeof record.userInfo.avatar === 'string' ? record.userInfo.avatar : record.userInfo.avatar?.avatar_240,
            name: record.userInfo.name ?? '',
          }}
        />
      ),
    },
    {
      title: '角色权限',
      key: 'roleType',
      width: '20%',
      render: (_: any, record: LibraPlatformUserMemberInfo) => (
        <Text>{LibraRoleType2DisplayNameMap[record.roleType]}</Text>
      ),
    },
    {
      title: '操作',
      key: 'operation',
      width: '20%',
      render: (_: any, record: LibraPlatformUserMemberInfo) => (
        <Space>
          <AddNewMemberButton
            memberType={PermissionMemberType.SingleUser}
            onUpdateComplete={() => {
              refreshMemberList();
              refreshUserRoleType();
            }}
            isEdit={true}
            initUserMember={record.userInfo}
            initRoleType={record.roleType}
          />
          <Popconfirm
            title={'确定删除此成员权限？'}
            onConfirm={async () => {
              await updateLibraUserMember({
                data: {
                  userEmail: record.userInfo.email ?? '',
                  roleType: LibraPlatformRoleType.Guest,
                },
              });
              refreshMemberList();
              refreshUserRoleType();
            }}
          >
            <Button icon={<IconDelete />} type={'danger'} />
          </Popconfirm>
        </Space>
      ),
    },
  ];

  const businessMemberColumns: ColumnProps<LibraPlatformBusinessLineMemberInfo>[] = [
    {
      title: '业务线',
      dataIndex: 'businessLine',
      key: 'businessLine',
      width: '60%',
      render: (_: any, record: LibraPlatformBusinessLineMemberInfo) => <Text>{record.businessLine}</Text>,
    },
    {
      title: '业务权限',
      key: 'roleType',
      width: '20%',
      render: (_: any, record: LibraPlatformBusinessLineMemberInfo) => (
        <Text>{LibraRoleType2DisplayNameMap[record.roleType]}</Text>
      ),
    },
    {
      title: '操作',
      key: 'operation',
      width: '20%',
      render: (_: any, record: LibraPlatformBusinessLineMemberInfo) => (
        <Space>
          <AddNewMemberButton
            memberType={PermissionMemberType.BusinessLine}
            onUpdateComplete={() => {
              refreshMemberList();
              refreshUserRoleType();
            }}
            isEdit={true}
            initRoleType={record.roleType}
            initBusinessLineName={record.businessLine}
          />
          <Popconfirm
            title={'确定删除此成员权限？'}
            onConfirm={async () => {
              await updateLibraBusinessMember({
                data: {
                  businessLine: record.businessLine,
                  roleType: LibraPlatformRoleType.Guest,
                },
              });
              refreshMemberList();
              refreshUserRoleType();
            }}
          >
            <Button icon={<IconDelete />} type={'danger'} />
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <Spin spinning={loading || memberListLoading}>
      {loading || memberListLoading ? (
        <></>
      ) : (
        <>
          {!(roleType & LibraPlatformPermission.Grant) && (
            <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '80vh' }}>
              <Empty
                image={<IllustrationConstruction style={{ width: 150, height: 150 }} />}
                darkModeImage={<IllustrationConstructionDark style={{ width: 150, height: 150 }} />}
                title={'未授权'}
                description="请联系管理员"
              />
            </div>
          )}
          {Boolean(roleType & LibraPlatformPermission.Grant) && (
            <div>
              <div style={{ width: '100%' }}>
                <Banner
                  type={'info'}
                  closeIcon={null}
                  style={{ width: '100%' }}
                  fullMode={false}
                  bordered={false}
                  description={
                    <Text size={'small'} style={{ color: 'rgba(var(--semi-light-blue-7), 1)' }}>
                      权限说明：
                      <br />
                      1. 访客（无任何权限）；
                      <br />
                      2. 普通成员（可创建实验、可查看实验列表）；
                      <br />
                      3. 产品成员（可创建实验、可查看实验列表、实验统计）；
                      <br />
                      4. 管理员（可授权、可编辑实验字段、可创建实验、可查看实验列表、实验统计）；
                    </Text>
                  }
                />
              </div>
              <Tabs type="line">
                <TabPane tab="成员管理" itemKey="1">
                  <Space vertical={true} spacing={'tight'} style={{ width: '100%' }} align="start">
                    <div style={{ width: '100%', display: 'flex', justifyContent: 'flex-end' }}>
                      {/* 内部的子元素靠右对齐 */}
                      <AddNewMemberButton
                        memberType={PermissionMemberType.SingleUser}
                        onUpdateComplete={() => {
                          refreshMemberList();
                        }}
                      />
                    </div>
                    <Table
                      columns={userMemberColumns}
                      dataSource={userMemberList}
                      pagination={{
                        pageSize: 10,
                      }}
                      style={{ width: '100%' }}
                      bordered={true}
                    />
                  </Space>
                </TabPane>
                <TabPane tab="业务线管理" itemKey="2">
                  <Space vertical={true} spacing={'tight'} style={{ width: '100%' }} align="start">
                    <div style={{ width: '100%', display: 'flex', justifyContent: 'flex-end' }}>
                      {/* 内部的子元素靠右对齐 */}
                      <AddNewMemberButton
                        memberType={PermissionMemberType.BusinessLine}
                        onUpdateComplete={() => refreshMemberList()}
                      />
                    </div>
                    <Table
                      columns={businessMemberColumns}
                      dataSource={businessMemberList}
                      pagination={{
                        pageSize: 10,
                      }}
                      style={{ width: '100%' }}
                      bordered={true}
                    />
                  </Space>
                </TabPane>
              </Tabs>
            </div>
          )}
        </>
      )}
    </Spin>
  );
};

export default LibraPermissionManage;
