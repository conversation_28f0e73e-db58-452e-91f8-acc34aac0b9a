import React, { useEffect, useState } from 'react';
import { LibraPlatformPermission, LibraPlatformRoleType } from '@shared/libra/LibraPlatformUserMemberInfo';
import { useModel } from '@edenx/runtime/model';
import UserSettingModule from '@/model/userSettingModel';
import { addMeegoTeamInfoToLibraList, getFlightFullPeriod, getRoleTypeByEmail } from '@api/libra';
import { Button, Empty, Input, Space, Spin, Table, Toast, Typography, TextArea } from '@douyinfe/semi-ui';
import { IllustrationConstruction, IllustrationConstructionDark } from '@douyinfe/semi-illustrations';
import { MeegoProjectKeyFaceU, MeegoTeamIds } from '@shared/libra/libraManageUtils';
const { Text } = Typography;

const LibraManageDebugPage: React.FC = () => {
  const [roleTypeLoading, setRoleTypeLoading] = useState(false);
  const [roleType, setRoleType] = useState<LibraPlatformRoleType>(LibraPlatformRoleType.Guest);
  const [userSettingState] = useModel(UserSettingModule);
  const [loading, setLoading] = useState(false);
  const [libraFullPeriodQueryStr, setLibraFullPeriodQueryStr] = useState('');
  const [fetchLibraFullPeriodLoading, setFetchLibraFullPeriodLoading] = useState(false);
  const [parseResult, setParseResult] = useState('');

  useEffect(() => {
    setRoleTypeLoading(true);
    if (userSettingState.info.email) {
      getRoleTypeByEmail({
        data: {
          email: userSettingState.info.email,
        },
      })
        .then(async res => {
          if (res?.code === 0) {
            setRoleType(res.roleType);
            // 如果是授权的用户，则拉取一下团队信息
            if (res.roleType & LibraPlatformPermission.Grant) {
              // 可以展示一下 UI
            }
          }
          setRoleTypeLoading(false);
        })
        .catch(err => {
          Toast.error(`获取用户权限失败，error: ${err.message}`);
          setRoleTypeLoading(false);
        });
    }
  }, [userSettingState.info]);

  const isValidJSON = (str: string) => {
    try {
      const parsed = JSON.parse(str);
      return parsed !== null && (typeof parsed === 'object' || Array.isArray(parsed));
    } catch (e) {
      return false;
    }
  };

  // 获取 Libra 实验全量周期
  const handleFetchLibraFullPeriod = async () => {
    let query = {};
    if (libraFullPeriodQueryStr.length > 0) {
      if (!isValidJSON(libraFullPeriodQueryStr)) {
        Toast.error('query 格式不正确！请检查 query 格式是否正确');
        return;
      }
      try {
        query = JSON.parse(libraFullPeriodQueryStr);
      } catch (err) {
        Toast.error('query 解析失败！请检查 query 格式是否正确');
        return;
      }
    }

    setFetchLibraFullPeriodLoading(true);
    getFlightFullPeriod({
      data: {
        libraStartTimestamp: 1735660800,
        libraStopTimestamp: 1743436799,
      },
    })
      .then(res => {
        if (!res) {
          throw new Error('add meego team info to libra list failed!');
        }
        if (res.code === 0) {
          Toast.success('获取实验全量周期数据成功！');
          setFetchLibraFullPeriodLoading(false);
          setParseResult(JSON.stringify(res.data));
        } else {
          Toast.error(`获取实验全量周期数据失败！error: ${res.message}`);
          setFetchLibraFullPeriodLoading(false);
        }
      })
      .catch(err => {
        Toast.error(`获取实验全量周期数据失败！error: ${err.message}`);
        setFetchLibraFullPeriodLoading(false);
      });
  };

  return (
    <Spin spinning={roleTypeLoading}>
      {roleTypeLoading ? (
        <></>
      ) : (
        <>
          {!(roleType & LibraPlatformPermission.Grant) && (
            <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '80vh' }}>
              <Empty
                image={<IllustrationConstruction style={{ width: 150, height: 150 }} />}
                darkModeImage={<IllustrationConstructionDark style={{ width: 150, height: 150 }} />}
                title={'未授权'}
                description="请联系管理员"
              />
            </div>
          )}
          {Boolean(roleType & LibraPlatformPermission.Grant) && (
            <div>
              <Space vertical={true} align={'start'} style={{ width: '100%' }}>
                <Space vertical={false} style={{ width: '100%', justifyContent: 'space-around' }}>
                  <Space vertical={false} align={'center'} style={{ width: '100%', justifyContent: 'start' }}>
                    <Space vertical={false} style={{ border: '1px solid #ddd', borderRadius: '4px', padding: '12px' }}>
                      <Button onClick={handleFetchLibraFullPeriod} loading={fetchLibraFullPeriodLoading}>
                        查询实验全量周期
                      </Button>
                      <Input
                        style={{ width: 300 }}
                        value={libraFullPeriodQueryStr}
                        showClear={true}
                        placeholder="可选：query查询，指定查询的时间区间"
                        onChange={value => {
                          setLibraFullPeriodQueryStr(value);
                        }}
                      />
                    </Space>
                  </Space>
                </Space>
                <TextArea value={parseResult} />
              </Space>
            </div>
          )}
        </>
      )}
    </Spin>
  );
};

export default LibraManageDebugPage;
