import React, { useEffect, useRef, useState } from 'react';
import {
  ItemType,
  SlardarItemInfo,
  StabilityMetricItemInfo,
  VersionStageCheckItem,
  VersionStageCheckList,
} from '@shared/releasePlatform/versionStageInfoCheckList';
import { useModel } from '@edenx/runtime/model';
import VersionInfoModule from '@/model/versionInfoModel';
import { SlardarPlatformType } from '@pa/shared/dist/src/appSettings/appSettings';
import { querySlardarInfo } from '@api/slardar';
import AppSettingModule from '@/model/appSettingModel';
import { SlardarInfoForGraph } from '@/pages/versionProcess/graySlardar/shared';
import { getVersions } from '@/utils/version';
import { AlarmVersion } from '@shared/typings/tea/metric';
import { useSearchParams } from '@edenx/runtime/router';
import { queryPreviousVersion } from '@api/version';
import dayjs from 'dayjs';
import minMax from 'dayjs/plugin/minMax';
import { Col, DatePicker, Divider, Row, Space, Switch, Tabs, Tag } from 'antd';
import VersionSelector from '@/component/VersionSelector';
import { buildTreeFromVersionList } from '@/component/VersionSelector/utils';
import BitsVersionInfoCard from '@/component/BitsVersionInfoCard';
import { versionCodeToVersion } from '@shared/utils/version_utils';
import { head } from 'lodash';
import SlardarCheckPage from '@/pages/versionReleaseCenter/versionStageSlardarMetrics/slardarCheckPage';
import { CrashType } from '@shared/typings/slardar/crash/issueListSearch';
import SlardarCrashList from '@/component/SlardarCrashList';
import CheckItemModule from '@/model/CheckItemModel';
import { DeviceLevel } from '@shared/common';
import { PlatformType } from '@pa/shared/dist/src/core';

export interface SlardarPerformanceOverviewProp {
  checkList: VersionStageCheckList;
  checkItem: VersionStageCheckItem;
}

function startTime(publishTime: number) {
  return dayjs.unix(publishTime).add(1, 'h').startOf('h');
}

function endTime(publishTime: number) {
  dayjs.extend(minMax);
  return dayjs.min(startTime(publishTime).add(3, 'd'), dayjs()) || dayjs();
}

interface IssueListParams {
  versionCode: string;
  baseVersionCode: string;
  start_time: number;
  end_time: number;
  deviceLevel: DeviceLevel;
  check_item: VersionStageCheckItem;
}

const IssueList: React.FC<IssueListParams> = ({
  versionCode,
  baseVersionCode,
  start_time,
  end_time,
  deviceLevel,
  check_item,
}) => {
  const [{ info }] = useModel(AppSettingModule);
  const [checkItemState] = useModel(CheckItemModule);
  const [CrashItem, setCrashItem] = useState<{ displayName: string; crashType: CrashType }[]>([]);
  useEffect(() => {
    querySlardarInfo({
      data: { aid: info.businessInfo.aid, platform: info.platform.toString() },
    }).then(resp => {
      const itemInfo = check_item.item_info as StabilityMetricItemInfo;
      // const Item = resp.InfoList.map((item) => ({
      // 	displayName: item.DisplayName,
      // 	crashType: item.Id as CrashType,
      // }));
      // 只保留crashType和info的crashType一样的部分
      console.log('item_info:', itemInfo);
      if (!itemInfo) {
        return;
      }
      const filteredList = resp.InfoList.filter(item => (item.Id as CrashType) === itemInfo.crash_type);
      const Item = filteredList.map(item => ({
        displayName: item.DisplayName,
        crashType: item.Id as CrashType,
      }));
      console.log('Item:', Item);
      setCrashItem(Item);
    });
  }, [info, checkItemState, check_item]);
  return (
    <Tabs
      onChange={(key: string) => {
        console.log(`issueListChange:${key}`);
      }}
      key={versionCode}
      type="card"
    >
      {CrashItem.map(item => (
        <Tabs.TabPane key={item.crashType} tab={item.displayName}>
          <SlardarCrashList
            start_time={start_time}
            end_time={end_time}
            crashType={item.crashType}
            versionCode={versionCode}
            baseVersionCode={baseVersionCode}
            deviceLevel={deviceLevel}
          />
        </Tabs.TabPane>
      ))}
    </Tabs>
  );
};

const SlardarPerformance: React.FC<SlardarPerformanceOverviewProp> = ({ checkList, checkItem }) => {
  // 版本列表
  const [versions, setVersions] = useState<AlarmVersion[]>([]);

  // 已经选择的“版本”
  const [selected, setSelected] = useState<string | undefined>(undefined);

  // 已经选择的“最佳版本”
  const [baseVersion, setBaseVersion] = useState<string>('');

  const [selectedBest, setSelectedBest] = useState<string | undefined>(undefined);
  const [currentVersionInfo] = useModel(VersionInfoModule);
  const [appSettingState] = useModel(AppSettingModule);
  const [checkMetrics, setCheckMetrics] = useState<
    { slardarInfo: SlardarInfoForGraph; checkItem: VersionStageCheckItem }[]
  >([]);
  const [slardarInfos, setSlardarInfos] = useState<SlardarInfoForGraph[]>([]);
  const [qualityCheckItems, setQualityCheckItems] = useState<
    { label: string; key: string; children: React.ReactNode }[]
  >([]);
  const [qualityCheckItem, setQualityCheckItem] = useState<React.ReactNode>([]);
  const [isAverage, setIsAverage] = useState(appSettingState.info.platform === PlatformType.Android);
  // 自定义放量时间
  const [customPublishTime, setCustomPublishTime] = useState<number | undefined>();

  const [searchParams, setSearchParams] = useSearchParams();
  const hasInit = useRef(false);
  const currentChecklist = useRef(checkList);
  const [versionProcessInfo, versionProcessAction] = useModel(VersionInfoModule);
  const [checkItemState] = useModel(CheckItemModule);
  const updateSelected = async (sel_str: string, sel_version: string) => {
    setSelected(sel_str);
    const res = await queryPreviousVersion({
      data: { aid: appSettingState.info.businessInfo.aid, version: sel_version, versionCode: sel_str },
    });
    if (res?.version_code) {
      setBaseVersion(res.version_code);
      console.log(`setBaseVersion not empty: ${baseVersion}`);
    } else {
      setBaseVersion('');
      console.log(`setBaseVersion: ${baseVersion}`);
    }
    console.log(`updateBase: ${baseVersion}`);
  };
  useEffect(() => {
    if (!hasInit.current) {
      return;
    }
    let change = false;
    if (selected) {
      searchParams.set('selected', selected);
      change = true;
    }
    if (selectedBest) {
      searchParams.set('best', selectedBest);
      change = true;
    }
    if (change) {
      setSearchParams(searchParams);
      console.log(`updateSearchParamsSlardar ${searchParams.toString()}`);
    }
  }, [selected, selectedBest]);
  const updateSlardarInfo = async () => {
    const resp = await querySlardarInfo({
      data: { aid: appSettingState.info.businessInfo.aid, platform: appSettingState.info.platform.toString() },
    });
    return resp.InfoList.map(
      it =>
        ({
          metricName: it.Id,
          displayName: it.DisplayName,
        }) as SlardarInfoForGraph,
    );
  };
  useEffect(() => {
    const versionPromise = getVersions(
      appSettingState.info.businessInfo.aid,
      appSettingState.info.platform === PlatformType.iOS ? SlardarPlatformType.iOS : SlardarPlatformType.Android,
    );
    const slardarInfoPromise = updateSlardarInfo();
    Promise.all([versionPromise, slardarInfoPromise]).then(async ([version, slardarInfo]) => {
      setVersions(version);
      let paramSelected = searchParams.get('selected');
      if (paramSelected === null || paramSelected === undefined) {
        const paramVersoin = searchParams.get('version');
        if (paramVersoin) {
          const targetVersions = version.filter(v => v.version === paramVersoin);
          if (targetVersions.length > 0) {
            targetVersions.sort((a, b) => b.versionCode.localeCompare(a.versionCode));
            paramSelected = targetVersions[0].versionCode;
          }
        }
      }
      const paramBest = searchParams.get('best');
      const findVersion = paramSelected ? version.find(v => v.versionCode.toString() === paramSelected) : undefined;
      hasInit.current = true;
      if (findVersion) {
        await updateSelected(findVersion.versionCode, findVersion.version);
      } else {
        await updateSelected(version[0].versionCode, version[0].version);
      }
      if (paramBest) {
        setSelectedBest(paramBest);
      }
      setSlardarInfos(slardarInfo.sort((a, b) => a.displayName.localeCompare(b.displayName)));
    });
  }, [appSettingState.info]);

  const onVersionChange = async (v: string | string[]) => {
    const ver_str = Array.isArray(v) ? v[0] : v;
    await updateSelected(ver_str, versionCodeToVersion(ver_str));
    console.log(`onVersionChange selected: ${selected}, base: ${baseVersion}, best: ${selectedBest}`);
  };

  const onBestVersionChange = async (v: string | string[]) => {
    const ver_str = Array.isArray(v) ? v[0] : v;
    setSelectedBest(ver_str);
  };
  const getVersionTimestamp = (version_code?: string) =>
    versions.find(it => it.versionCode === version_code)?.timestamp ?? 0;

  useEffect(() => {
    if (baseVersion === undefined) {
      return;
    }
    const checkInfo: { slardarInfo: SlardarInfoForGraph; checkItem: VersionStageCheckItem }[] = [];
    if (!checkList.check_items) {
      return;
    }
    slardarInfos.map(info => {
      const matchedItems = checkList.check_items
        .filter(item => item.item_type === ItemType.Slardar)
        .filter(item => item.check_item_id === checkItem.check_item_id)
        .filter(item => (item.item_info as SlardarItemInfo).slardar_info_id === info.metricName);
      const matchedItem = head(matchedItems);
      if (matchedItem) {
        checkInfo.push({ slardarInfo: info, checkItem: matchedItem });
      }
    });
    const checkItems = checkInfo.map(checkMetric => ({
      label: checkMetric.slardarInfo.displayName,
      key: checkMetric.slardarInfo.metricName,
      children: (
        <SlardarCheckPage
          versions={[selected, baseVersion, selectedBest]}
          metrics={checkMetric.slardarInfo}
          checkItem={checkMetric.checkItem}
          range={[startTime(getVersionTimestamp(selected)).unix(), endTime(getVersionTimestamp(selected)).unix()]}
          isAverage={isAverage}
          baseTimestamp={customPublishTime}
        />
      ),
    }));
    const checkMetric = head(checkInfo);
    if (checkMetric) {
      setQualityCheckItem(
        <SlardarCheckPage
          versions={[selected, baseVersion, selectedBest]}
          metrics={checkMetric.slardarInfo}
          checkItem={checkMetric.checkItem}
          range={[startTime(getVersionTimestamp(selected)).unix(), endTime(getVersionTimestamp(selected)).unix()]}
          isAverage={isAverage}
          baseTimestamp={customPublishTime}
        />,
      );
    }
  }, [checkMetrics, slardarInfos, checkList, checkItem, selected, baseVersion, selectedBest]);

  const buildCrashIssueList = () => {
    if (!selected) {
      return <>请选择一个版本！</>;
    }
    return (
      <IssueList
        versionCode={selected}
        baseVersionCode={selectedBest ?? '0'}
        start_time={startTime(getVersionTimestamp(selected)).unix()}
        end_time={endTime(getVersionTimestamp(selected)).unix()}
        deviceLevel={DeviceLevel.ALL}
        check_item={checkItem}
      />
    );
  };

  return (
    <>
      <Row gutter={[16, 16]}>
        <Col span={24}>
          <Space size={'middle'}>
            选择版本：
            <VersionSelector
              data={buildTreeFromVersionList(versions)}
              onChange={onVersionChange}
              value={selected!}
              isSingle={true}
            />
            对比最佳版本：
            <VersionSelector
              data={buildTreeFromVersionList(versions)}
              onChange={onBestVersionChange}
              value={selectedBest!}
              isSingle={true}
            />
            <Switch
              checkedChildren="求和平均"
              unCheckedChildren="原始数据"
              defaultChecked={isAverage}
              onChange={data => setIsAverage(data)}
            />
          </Space>
        </Col>
        <Col span={24}>
          <BitsVersionInfoCard
            key={selected?.toString() ?? 'undefined'}
            version_code={selected}
            start_time={it => startTime(it).unix()}
            end_time={it => endTime(it).unix()}
          />
        </Col>
      </Row>
      <Divider />
      <Space direction="horizontal">
        自定义版本放量时间：
        <DatePicker showTime onChange={v => setCustomPublishTime(v ? v.unix() : undefined)} />
        <Tag color={customPublishTime ? 'green' : 'red'}>
          {customPublishTime
            ? `当前自定义放量时间：${dayjs.unix(customPublishTime).format('YYYY-MM-DD HH:mm:ss')}`
            : '未采用自定义放量时间'}
        </Tag>
      </Space>
      <Divider />
      {qualityCheckItem}
      <Divider />
      {selected ? (
        <IssueList
          versionCode={selected}
          baseVersionCode={selectedBest ?? '0'}
          start_time={startTime(getVersionTimestamp(selected)).unix()}
          end_time={endTime(getVersionTimestamp(selected)).unix()}
          deviceLevel={DeviceLevel.ALL}
          check_item={checkItem}
        />
      ) : (
        <>请选择一个版本！</>
      )}
    </>
  );
};
export default SlardarPerformance;
