import React, { useEffect, useState } from 'react';
import VersionStageApprovalCard from '@/component/VersionStageApprovalCard';
import { SlardarInfoForGraph } from '@/pages/versionProcess/graySlardar/shared';
import Slardar<PERSON>hart from '@/component/Charts/SlardarChart';
import { Col, Divider, Row, Tabs } from 'antd';
import { SlardarItemInfo, VersionStageCheckItem } from '@shared/releasePlatform/versionStageInfoCheckList';
import SlardarCrashList from '@/component/SlardarCrashList';
import { useModel } from '@edenx/runtime/model';
import VersionStageSettingModule from '@/model/versionStageSettingModel';
import { put_back_by } from '@shared/utils/tools';
import AppSettingModule from '@/model/appSettingModel';
import { CrashType } from '@shared/typings/slardar/crash/issueListSearch';
import { querySlardarInfo } from '@api/slardar';
import { DeviceLevel } from '@shared/common';

export interface SlardarCheckPageProp {
  versions: (string | undefined)[];
  metrics: SlardarInfoForGraph;
  checkItem: VersionStageCheckItem;
  range: [number, number];
  isAverage: boolean;
  baseTimestamp?: number;
}

const SlardarCheckPage: React.FC<SlardarCheckPageProp> = ({
  versions,
  metrics,
  range,
  isAverage,
  baseTimestamp,
  checkItem,
}) => {
  const [stageSettingState] = useModel(VersionStageSettingModule);
  const pv = versions.map(it => it?.toString() || '');
  const [appSettingStatus] = useModel(AppSettingModule);
  const testMetrics = [metrics];
  const graphs = put_back_by(testMetrics, it => it.metricName.endsWith('Lag')).map(it => (
    <Col
      span={16}
      key={`${appSettingStatus.info.platform}-${it.metricName}-${isAverage}-${pv.join('')}-${baseTimestamp || 0}`}
    >
      <SlardarChart
        title={it.displayName}
        slardarInfoId={it.metricName}
        versions={versions}
        timeRange={range}
        isAverage={isAverage}
        baseTimestamp={baseTimestamp}
        deviceLevel={DeviceLevel.ALL}
        aspectRatio={30 / 7}
      />
    </Col>
  ));
  return (
    <>
      <Row gutter={[16, 24]} key={`SlardarGraph_${pv.join('')}`}>
        {graphs}
      </Row>
    </>
  );
  // return (
  // 	<>
  // 		<div style={{ height: "10%", width: "80%" }}>
  // 			<SlardarChart
  // 				title={metrics.displayName}
  // 				slardarInfoId={metrics.metricName}
  // 				versions={versions}
  // 				timeRange={range}
  // 				isAverage={isAverage}
  // 				baseTimestamp={baseTimestamp}
  // 				deviceLevel={DeviceLevel.ALL}
  // 				aspectRatio={30 / 7}
  // 			/>
  // 		</div>
  // 		<Divider />
  // 		<SlardarCrashList
  // 			crashType={(checkItem.item_info as SlardarItemInfo).crash_type}
  // 			versionCode={versions[0] ?? ""}
  // 			baseVersionCode={versions[2] ?? ""}
  // 			start_time={range[0]}
  // 			end_time={range[1]}
  // 			deviceLevel={DeviceLevel.ALL}
  // 		/>
  // 	</>
  // );
};

export default SlardarCheckPage;
