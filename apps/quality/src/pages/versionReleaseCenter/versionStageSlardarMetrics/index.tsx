import React, { useEffect, useState } from 'react';
import { useModel } from '@edenx/runtime/model';
import VersionInfoModule from '@/model/versionInfoModel';
import { findChecklist } from '@api/releasePlatform';
import { VersionStageCheckItem, VersionStageCheckList } from '@shared/releasePlatform/versionStageInfoCheckList';
import { SlardarInfoForGraph } from '@/pages/versionProcess/graySlardar/shared';
import AppSettingModule from '@/model/appSettingModel';
import VersionStageSettingModule from '@/model/versionStageSettingModel';
import SlardarPerformance from '@/pages/versionReleaseCenter/versionStageSlardarMetrics/slardarPerformance';
import CheckItemModule from '@/model/CheckItemModel';

const SardarMetricsApproval: React.FC = () => {
  const [currentVersionInfo] = useModel(VersionInfoModule);
  const [currentCheckList, setCurrentCheckList] = useState<VersionStageCheckList>();
  const [appSettingState] = useModel(AppSettingModule);
  const [slardarInfos, setSlardarInfos] = useState<SlardarInfoForGraph[]>([]);
  const [versionProcessInfo, versionProcessAction] = useModel(VersionInfoModule);
  const [stageSettingState] = useModel(VersionStageSettingModule);
  const [checkItemState] = useModel(CheckItemModule);
  const [checkItem, setCheckItem] = useState<VersionStageCheckItem>();

  useEffect(() => {
    findChecklist({
      data: {
        appId: currentVersionInfo.info.app_id,
        version: currentVersionInfo.info.version,
        stage: stageSettingState.info.subStage
          ? stageSettingState.info.subStage.stage_name
          : stageSettingState.info.mainStage?.stage_name ?? '',
      },
    }).then(checkList => {
      if (!checkList) {
        return;
      }
      setCurrentCheckList(checkList);
    });
  }, [currentVersionInfo.info, appSettingState.info, stageSettingState.info]);

  useEffect(() => {
    for (const item of currentCheckList?.check_items ?? []) {
      if (item.check_item_id === checkItemState.info.check_item_id) {
        setCheckItem(item);
      }
    }
  }, [currentCheckList, checkItemState.info]);

  return (
    <>
      <hr />
      <SlardarPerformance
        checkList={currentCheckList ?? ({} as VersionStageCheckList)}
        checkItem={checkItem ?? checkItemState.info}
      />
    </>
  );
};
export default SardarMetricsApproval;
