import React, { useEffect, useRef, useState } from 'react';
import {
  ItemType,
  SlardarItemInfo,
  VersionStageCheckItem,
  VersionStageCheckList,
} from '@shared/releasePlatform/versionStageInfoCheckList';
import { useModel } from '@edenx/runtime/model';
import VersionInfoModule from '@/model/versionInfoModel';
import { SlardarPlatformType } from '@pa/shared/dist/src/appSettings/appSettings';
import { querySlardarInfo } from '@api/slardar';
import AppSettingModule from '@/model/appSettingModel';
import { SlardarInfoForGraph } from '@/pages/versionProcess/graySlardar/shared';
import { getVersions } from '@/utils/version';
import { AlarmVersion } from '@shared/typings/tea/metric';
import { useSearchParams } from '@edenx/runtime/router';
import { queryPreviousVersion } from '@api/version';
import QualityCheckPage from '@/pages/versionReleaseCenter/versionStageMetricsApproval/qualityCheckPage';
import dayjs from 'dayjs';
import minMax from 'dayjs/plugin/minMax';
import { Col, DatePicker, Divider, Row, Space, Switch, Tabs, Tag } from 'antd';
import VersionSelector from '@/component/VersionSelector';
import { buildTreeFromVersionList } from '@/component/VersionSelector/utils';
import BitsVersionInfoCard from '@/component/BitsVersionInfoCard';
import { versionCodeToVersion } from '@shared/utils/version_utils';
import { head, noop } from 'lodash';
import { PlatformType } from '@pa/shared/dist/src/core';

export interface QualityAndPerformanceOverviewProp {
  checkList: VersionStageCheckList;
}

function startTime(publishTime: number) {
  return dayjs.unix(publishTime).add(1, 'h').startOf('h');
}

function endTime(publishTime: number) {
  dayjs.extend(minMax);
  return dayjs.min(startTime(publishTime).add(3, 'd'), dayjs()) || dayjs();
}

const QualityAndPerformance: React.FC<QualityAndPerformanceOverviewProp> = ({ checkList }) => {
  // 版本列表
  const [versions, setVersions] = useState<AlarmVersion[]>([]);

  // 已经选择的“版本”
  const [selected, setSelected] = useState<string | undefined>(undefined);

  // 已经选择的“最佳版本”
  const [baseVersion, setBaseVersion] = useState<string>('');

  const [selectedBest, setSelectedBest] = useState<string | undefined>(undefined);
  const [currentVersionInfo] = useModel(VersionInfoModule);
  const [appSettingState] = useModel(AppSettingModule);
  const [checkMetrics, setCheckMetrics] = useState<
    { slardarInfo: SlardarInfoForGraph; checkItem: VersionStageCheckItem }[]
  >([]);
  const [slardarInfos, setSlardarInfos] = useState<SlardarInfoForGraph[]>([]);
  const [qualityCheckItems, setQualityCheckItems] = useState<
    { label: string; key: string; children: React.ReactNode }[]
  >([]);
  const [isAverage, setIsAverage] = useState(appSettingState.info.platform === PlatformType.Android);
  // 自定义放量时间
  const [customPublishTime, setCustomPublishTime] = useState<number | undefined>();

  const [searchParams, setSearchParams] = useSearchParams();
  const hasInit = useRef(false);
  const currentChecklist = useRef(checkList);
  const [versionProcessInfo, versionProcessAction] = useModel(VersionInfoModule);

  const updateSelected = async (sel_str: string, sel_version: string) => {
    setSelected(sel_str);
    setBaseVersion('');
    const res = await queryPreviousVersion({
      data: { aid: appSettingState.info.businessInfo.aid, version: sel_version, versionCode: sel_str },
    });
    if (res?.version_code) {
      setBaseVersion(res.version_code);
    }
  };
  useEffect(() => {
    if (!hasInit.current) {
      return;
    }
    let change = false;
    if (selected) {
      searchParams.set('selected', selected);
      change = true;
    }
    if (selectedBest) {
      searchParams.set('best', selectedBest);
      change = true;
    }
    if (change) {
      setSearchParams(searchParams);
      console.log(`updateSearchParamsSlardar ${searchParams.toString()}`);
    }
  }, [selected, selectedBest]);
  const updateSlardarInfo = async () => {
    const resp = await querySlardarInfo({
      data: { aid: appSettingState.info.businessInfo.aid, platform: appSettingState.info.platform.toString() },
    });
    return resp.InfoList.map(
      it =>
        ({
          metricName: it.Id,
          displayName: it.DisplayName,
        }) as SlardarInfoForGraph,
    );
  };
  useEffect(() => {
    const versionPromise = getVersions(
      appSettingState.info.businessInfo.aid,
      appSettingState.info.platform === PlatformType.iOS ? SlardarPlatformType.iOS : SlardarPlatformType.Android,
    );
    const slardarInfoPromise = updateSlardarInfo();
    Promise.all([versionPromise, slardarInfoPromise]).then(async ([version, slardarInfo]) => {
      setVersions(version);
      const paramSelected = searchParams.get('selected');
      const paramBest = searchParams.get('best');
      const findVersion = paramSelected ? version.find(v => v.versionCode.toString() === paramSelected) : undefined;
      hasInit.current = true;
      if (findVersion) {
        await updateSelected(findVersion.versionCode, findVersion.version);
      } else {
        console.log('setting selected!');
        await updateSelected(version[0].versionCode, version[0].version);
      }
      if (paramBest) {
        setSelectedBest(paramBest);
      }
      setSlardarInfos(slardarInfo.sort((a, b) => a.displayName.localeCompare(b.displayName)));
    });
  }, [appSettingState.info]);

  const onVersionChange = async (v: string | string[]) => {
    const ver_str = Array.isArray(v) ? v[0] : v;
    await updateSelected(ver_str, versionCodeToVersion(ver_str));
  };

  const onBestVersionChange = async (v: string | string[]) => {
    const ver_str = Array.isArray(v) ? v[0] : v;
    setSelectedBest(ver_str);
  };
  const getVersionTimestamp = (version_code?: string) =>
    versions.find(it => it.versionCode === version_code)?.timestamp ?? 0;

  useEffect(() => {
    const checkInfo: { slardarInfo: SlardarInfoForGraph; checkItem: VersionStageCheckItem }[] = [];
    if (!checkList.check_items) {
      return;
    }
    slardarInfos.map(info => {
      const matchedItems = checkList.check_items
        .filter(item => item.item_type === ItemType.Slardar)
        .filter(item => (item.item_info as SlardarItemInfo).slardar_info_id === info.metricName);
      const matchedItem = head(matchedItems);
      if (matchedItem) {
        checkInfo.push({ slardarInfo: info, checkItem: matchedItem });
      }
    });
    const checkItems = checkInfo.map(checkMetric => ({
      label: checkMetric.slardarInfo.displayName,
      key: checkMetric.slardarInfo.metricName,
      children: (
        <QualityCheckPage
          versions={[selected, baseVersion, selectedBest]}
          metrics={checkMetric.slardarInfo}
          checkItem={checkMetric.checkItem}
          range={[startTime(getVersionTimestamp(selected)).unix(), endTime(getVersionTimestamp(selected)).unix()]}
          isAverage={isAverage}
          baseTimestamp={customPublishTime}
        />
      ),
    }));
    setQualityCheckItems(checkItems);
  }, [checkMetrics, slardarInfos, checkList]);

  return (
    <>
      <Row gutter={[16, 16]}>
        <Col span={24}>
          <Space size={'middle'}>
            选择版本：
            <VersionSelector
              data={buildTreeFromVersionList(versions)}
              onChange={onVersionChange}
              value={selected!}
              isSingle={true}
            />
            对比最佳版本：
            <VersionSelector
              data={buildTreeFromVersionList(versions)}
              onChange={onBestVersionChange}
              value={selectedBest!}
              isSingle={true}
            />
            <Switch
              checkedChildren="求和平均"
              unCheckedChildren="原始数据"
              defaultChecked={isAverage}
              onChange={data => setIsAverage(data)}
            />
          </Space>
        </Col>
        <Col span={24}>
          <BitsVersionInfoCard
            key={selected?.toString() ?? 'undefined'}
            version_code={selected}
            start_time={it => startTime(it).unix()}
            end_time={it => endTime(it).unix()}
          />
        </Col>
      </Row>
      <Divider />
      <Space direction="horizontal">
        自定义版本放量时间：
        <DatePicker showTime onChange={v => setCustomPublishTime(v ? v.unix() : undefined)} />
        <Tag color={customPublishTime ? 'green' : 'red'}>
          {customPublishTime
            ? `当前自定义放量时间：${dayjs.unix(customPublishTime).format('YYYY-MM-DD HH:mm:ss')}`
            : '未采用自定义放量时间'}
        </Tag>
      </Space>
      <Divider />
      <Tabs onChange={noop} type={'line'} items={qualityCheckItems} />
      <Divider />
    </>
  );
};
export default QualityAndPerformance;
