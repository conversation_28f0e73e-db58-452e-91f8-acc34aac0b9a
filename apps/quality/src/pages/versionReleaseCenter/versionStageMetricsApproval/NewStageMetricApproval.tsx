import React, { useEffect, useState } from 'react';
import MetricsCoreVersionCheckList from '@/pages/quality/metric/version/components/MetricsCoreVersionCheckList';
import QualitySingleCheckList from '@/pages/quality/metric/version/components/QualitySingleCheckList';
import { Divider, Tabs } from 'antd';
import { noop, toNumber } from 'lodash';
import dayjs from 'dayjs';
import QualityAlarmOrderRecordList from '@/pages/quality/metric/version/components/order/QualityAlarmOrderRecordList';

export interface NewStageMetricApprovalProps {
  name: string; // 这里的name是id
  version?: string; // 版本号
  appId: number; // appId,区分平台和应用
}

const NewStageMetricApproval: React.FC<NewStageMetricApprovalProps> = ({ name, version, appId }) => {
  const [displayName, setDisplayName] = useState('性能指标准出');
  const [qualityCheckItems, setQualityCheckItems] = useState<
    { label: string; key: string; children: React.ReactNode }[]
  >([]);

  useEffect(() => {
    const checkItems = [
      {
        label: '指标下钻分析',
        key: 'SingleTeaMetric',
        children: (
          <QualitySingleCheckList
            metricId={name}
            inStartTime={dayjs().subtract(160, 'd').unix()}
            inEndTime={dayjs().unix()}
          />
        ),
      },
    ];
    setQualityCheckItems(checkItems);
  }, []);

  return (
    <>
      <MetricsCoreVersionCheckList
        metricName={name}
        versionCode={'143000300'}
        version={version}
        _compareVersionCode={'143000200'}
        _compareVersion={version}
      />
      <QualityAlarmOrderRecordList hiddenSearch={false} enablePagination={false} metricIds={[name]} />
      <Divider />
      <Tabs onChange={noop} type={'line'} items={qualityCheckItems} />
    </>
  );
};

export default NewStageMetricApproval;
