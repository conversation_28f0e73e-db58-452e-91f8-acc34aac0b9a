import React, { useEffect, useState } from 'react';
import QualityAndPerformance from '@/pages/versionReleaseCenter/versionStageMetricsApproval/qualityAndPerformance';
import BusinessReport from '@/pages/versionReleaseCenter/versionStageMetricsApproval/businessReport';
import { Tabs } from 'antd';
import { noop } from 'lodash';
import { useModel } from '@edenx/runtime/model';
import VersionInfoModule from '@/model/versionInfoModel';
import { findChecklist } from '@api/releasePlatform';
import { VersionStageCheckList } from '@shared/releasePlatform/versionStageInfoCheckList';
import { currentStage } from '@shared/releasePlatform/versionStage';
import { getVersions } from '@/utils/version';
import { querySlardarInfo } from '@api/slardar';
import { SlardarInfoForGraph } from '@/pages/versionProcess/graySlardar/shared';
import AppSettingModule from '@/model/appSettingModel';
import VersionStageSettingModule from '@/model/versionStageSettingModel';

const VersionStageMetricsApproval: React.FC = () => {
  const [currentVersionInfo] = useModel(VersionInfoModule);
  const [currentCheckList, setCurrentCheckList] = useState<VersionStageCheckList>();
  const [appSettingState] = useModel(AppSettingModule);
  const [slardarInfos, setSlardarInfos] = useState<SlardarInfoForGraph[]>([]);
  const [versionProcessInfo, versionProcessAction] = useModel(VersionInfoModule);
  const [stageSettingState] = useModel(VersionStageSettingModule);

  useEffect(() => {
    findChecklist({
      data: {
        appId: currentVersionInfo.info.app_id,
        version: currentVersionInfo.info.version,
        stage: stageSettingState.info.subStage
          ? stageSettingState.info.subStage.stage_name
          : stageSettingState.info.mainStage?.stage_name ?? '',
      },
    }).then(checkList => {
      if (!checkList) {
        return;
      }
      setCurrentCheckList(checkList);
    });
  }, [currentVersionInfo.info, appSettingState.info, stageSettingState.info]);

  const items = [
    {
      label: '性能指标',
      key: 'Stability',
      children: <QualityAndPerformance checkList={currentCheckList ?? ({} as VersionStageCheckList)} />,
    },
    {
      label: '业务指标',
      key: 'Business',
      children: <BusinessReport />,
    },
  ];

  return <Tabs onChange={noop} type={'line'} items={items} />;
};
export default VersionStageMetricsApproval;
