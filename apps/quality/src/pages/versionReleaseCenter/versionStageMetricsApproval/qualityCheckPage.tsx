import React from 'react';
import VersionStageApprovalCard from '@/component/VersionStageApprovalCard';
import { SlardarInfoForGraph } from '@/pages/versionProcess/graySlardar/shared';
import Slardar<PERSON>hart from '@/component/Charts/SlardarChart';
import { Divider } from 'antd';
import { SlardarItemInfo, VersionStageCheckItem } from '@shared/releasePlatform/versionStageInfoCheckList';
import SlardarCrashList from '@/component/SlardarCrashList';
import { useModel } from '@edenx/runtime/model';
import VersionStageSettingModule from '@/model/versionStageSettingModel';
import { DeviceLevel } from '@shared/common';

export interface QualityCheckPageProp {
  versions: (string | undefined)[];
  metrics: SlardarInfoForGraph;
  checkItem: VersionStageCheckItem;
  range: [number, number];
  isAverage: boolean;
  baseTimestamp?: number;
}
const QualityCheckPage: React.FC<QualityCheckPageProp> = ({
  versions,
  metrics,
  range,
  isAverage,
  baseTimestamp,
  checkItem,
}) => {
  const [stageSettingState] = useModel(VersionStageSettingModule);
  return (
    <>
      <VersionStageApprovalCard
        checkItemInfo={checkItem}
        stage={
          stageSettingState.info.subStage
            ? stageSettingState.info.subStage.stage_name
            : stageSettingState.info.mainStage?.stage_name ?? ''
        }
      />
      <Divider />
      <div style={{ height: '10%', width: '50%' }}>
        <SlardarChart
          title={metrics.displayName}
          slardarInfoId={metrics.metricName}
          versions={versions}
          timeRange={range}
          isAverage={isAverage}
          baseTimestamp={baseTimestamp}
          deviceLevel={DeviceLevel.ALL}
        />
      </div>
      <Divider />
      <SlardarCrashList
        crashType={(checkItem.item_info as SlardarItemInfo).crash_type}
        versionCode={versions[0] ?? ''}
        baseVersionCode={versions[2] ?? ''}
        start_time={range[0]}
        end_time={range[1]}
        deviceLevel={DeviceLevel.ALL}
      />
    </>
  );
};

export default QualityCheckPage;
