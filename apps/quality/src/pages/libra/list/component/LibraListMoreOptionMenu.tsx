import React, { useState } from 'react';
import { LibraNewInfo } from '@shared/libra/LibraNewInfo';
import { Button, Dropdown, Space, Toast } from '@douyinfe/semi-ui';
import { IconMore } from '@douyinfe/semi-icons';
import FlightRefreshMeegoInfoButton from '@/pages/libra/list/component/FlightRefreshMeegoInfoButton';

const LibraListMoreOptionMenu: React.FC<{
  libraInfo: LibraNewInfo;
  onLibraInfoUpdate: (newLibraInfo: LibraNewInfo) => void;
}> = ({ libraInfo, onLibraInfoUpdate }) => {
  console.log('aaa');
  return (
    <Space>
      <Dropdown
        trigger={'hover'}
        position={'bottomLeft'}
        clickToHide={true}
        render={
          <Dropdown.Menu>
            <Dropdown.Item key={'key1'}>
              <FlightRefreshMeegoInfoButton libraInfo={libraInfo} onLibraInfoUpdate={onLibraInfoUpdate} />
            </Dropdown.Item>
          </Dropdown.Menu>
        }
      >
        <Button icon={<IconMore />} theme="borderless" style={{ color: 'rgba(var(--semi-grey-5), 1)' }} />
      </Dropdown>
    </Space>
  );
};

export default LibraListMoreOptionMenu;
