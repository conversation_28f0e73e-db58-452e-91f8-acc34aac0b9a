import { LibraNewInfoTablePageStatus } from '@shared/libra/LibraNewInfoTableColumnSetting';
import React, { useEffect, useState } from 'react';
import { Button, Select, Spin, Tag } from '@douyinfe/semi-ui';
import { flightTagColorArray } from '@shared/libra/libraManageUtils';
import { IconEditStroked } from '@douyinfe/semi-icons';
import { LibraPlatformPermission } from '@shared/libra/LibraPlatformUserMemberInfo';

const FlightTagSelectior: React.FC<{
  initValue?: string;
  options: { value: string; label: string }[];
  onChange: (value?: string) => Promise<void>;
  pageStatus: LibraNewInfoTablePageStatus;
  editPermission: LibraPlatformPermission;
  updatePageStatus?: (pageStatus: LibraNewInfoTablePageStatus) => void;
}> = ({ initValue, onChange, pageStatus, options, editPermission }) => {
  const [selectState, setSelectState] = useState(false);
  const [focus, setFocus] = useState(false);
  const [loading, setLoading] = useState<boolean>(false);
  const [editable, setEditable] = useState(true);
  const optionRenderList = options.map((it, index) => ({
    value: it.value,
    label: (
      <Tag
        color={flightTagColorArray[index % flightTagColorArray.length]}
        style={{ maxWidth: '100%' }}
        size={'large'}
        key={it.value}
      >
        {it.label}
      </Tag>
    ),
  }));

  useEffect(() => {
    if (pageStatus.userRoleType) {
      setEditable(Boolean(pageStatus.userRoleType & editPermission));
    }
  }, [pageStatus, editPermission]);

  return (
    <div
      style={{
        width: '100%',
        height: '100%',
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        padding: 8,
      }}
      onMouseEnter={() => {
        setFocus(editable);
      }}
      onMouseLeave={() => {
        setFocus(false);
      }}
    >
      {(selectState || loading) && (
        <Spin spinning={loading} style={{ width: '100%' }}>
          <Select
            showClear={true}
            placeholder={'请选择'}
            optionList={optionRenderList}
            value={initValue}
            multiple={false}
            expandRestTagsOnClick={true}
            showRestTagsPopover={true}
            style={{ width: '100%' }}
            onSelect={(value, option) => {
              if (value === initValue) {
                setSelectState(false);
                return;
              }
              setLoading(true);
              onChange(value as string).then(res => {
                setLoading(false);
              });
            }}
            onDropdownVisibleChange={visible => {
              if (!visible) {
                setSelectState(false);
                setFocus(false);
              }
            }}
            onClear={() => {
              setLoading(true);
              onChange(undefined).then(res => {
                setLoading(false);
              });
            }}
            defaultOpen={true}
          />
        </Spin>
      )}
      {!selectState && !loading && (
        <div
          style={{
            padding: 6,
            display: 'flex',
            alignItems: 'center',
            width: '100%',
            position: 'relative',
          }}
        >
          <div
            style={{
              width: '100%',
            }}
          >
            {optionRenderList.find(it => it.value === initValue)?.label}
          </div>
          <div
            style={{
              padding: 6,
              zIndex: 1,
              width: '100%',
              position: 'absolute',
            }}
          >
            {focus && editable && (
              <Button
                icon={<IconEditStroked />}
                onClick={() => {
                  setSelectState(true);
                }}
                theme="outline"
                style={{
                  float: 'right',
                  backgroundColor: 'rgba(var(--semi-grey-0), 1)',
                }}
              />
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default FlightTagSelectior;
