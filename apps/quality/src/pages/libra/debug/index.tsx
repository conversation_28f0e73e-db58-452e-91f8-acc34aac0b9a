import React, { ReactNode, useEffect, useState } from 'react';
import { testQueryLVAllFlightList } from '@api/libra';
import { Space, Table, Tag, Toast, Typography } from '@douyinfe/semi-ui';
import { Flight } from '@shared/libra/flight';
import { TagColor } from '@douyinfe/semi-ui/lib/es/tag';
const { Text, Title } = Typography;

const LibraDebugPage: React.FC = () => {
  const [flightList, setFlightList] = useState<Flight[] | undefined>([]);
  const [loading, setLoading] = useState(false);

  const flightTypeDisplayName = (type: string) => {
    if (type === 'strategy') {
      return '服务端实验';
    }
    if (type === 'product') {
      return '普通客户端实验';
    }
    if (type === 'ab_client_sdk') {
      return 'AB客户端SDK实验';
    }
    if (type === 'settings_client_sdk') {
      return 'Settings SDK实验';
    }
    if (type === 'settings_client_normal') {
      return 'Settings普通客户端实验';
    }
    if (type === 'ab_local_client') {
      return '客户端本地分流实验';
    }
    if (type === 'gp_regression') {
      return '自动调参实验';
    }
    if (type === 'web_sdk') {
      return 'Web端实验';
    }
    if (type === 'ad_group') {
      return '广告组实验';
    }
    if (type === 'ad_user') {
      return '广告用户实验';
    }
    if (type === 'ad_advertiser') {
      return '广告主实验';
    }
    if (type === 'ad_plan') {
      return '广告计划实验';
    }
    if (type === 'interleaving') {
      return '搜索interleaving实验';
    }
    return type;
  };

  const flightStatusDisplayName = (status: number) => {
    if (status === 0 || status === 91) {
      return '已结束';
    }
    if (status === 1) {
      return '进行中';
    }
    if (status === 2) {
      return '待调度';
    }
    if (status === 3) {
      return '调试中';
    }
    if (status === 4) {
      return '已暂停';
    }
    return '';
  };

  const colorMap: Record<number, TagColor> = {
    0: 'green',
    1: 'blue',
    2: 'yellow',
    3: 'orange',
    4: 'purple',
    91: 'green',
  };

  const getTagColor = (value: number) => colorMap[value] || 'default';

  const formatTimeDifference = (start_date: string, end_date: string) => {
    const startDate = new Date(start_date);
    const endDate = new Date(end_date);

    // 计算时间差，以毫秒为单位
    const diffMilliseconds = endDate.getTime() - startDate.getTime();

    if (diffMilliseconds < 0) {
      // throw new Error('The end date must be later than the start date');
      return '';
    }

    // 将毫秒转换为分钟、小时和天
    const diffMinutes = Math.floor(diffMilliseconds / (1000 * 60));
    const diffHours = diffMilliseconds / (1000 * 60 * 60);
    const diffDays = diffMilliseconds / (1000 * 60 * 60 * 24);

    if (diffMinutes < 60) {
      return `${diffMinutes}分钟`;
    } else if (diffHours < 24) {
      // 去除小数点后为 .0 的情况
      const hours = diffHours.toFixed(1);
      return hours.endsWith('.0') ? `${parseInt(hours)}小时` : `${hours}小时`;
    } else {
      // 去除小数点后为 .0 的情况
      const days = diffDays.toFixed(1);
      return days.endsWith('.0') ? `${parseInt(days)}天` : `${days}天`;
    }
  };

  const columns = [
    {
      title: '实验 ID',
      key: 'id',
      width: '160px',
      render: (_: any, record: Flight) => (
        <Space vertical={true} align={'start'}>
          <Text>{record.id}</Text>
          <Text>{flightTypeDisplayName(record.type)}</Text>
        </Space>
      ),
    },
    {
      title: '状态',
      key: 'status',
      render: (_: any, record: Flight) => (
        <>
          <Tag color={getTagColor(record.status)}>{flightStatusDisplayName(record.status)}</Tag>
        </>
      ),
    },
    {
      title: '实验名称/描述/标签',
      key: 'name',
      render: (_: any, record: Flight) => (
        <Space vertical={true} align={'start'}>
          {/* { @FIXME: 此处 URL 不能写死，要区分 CN 和 SG}*/}
          <Text link={{ href: `https://data.bytedance.net/libra/flight/${record.id}/report/main`, target: '_blank' }}>
            {record.name}
          </Text>
          <Text>{record.description}</Text>
        </Space>
      ),
    },
    {
      title: '基本信息',
      key: 'basic_info',
      render: (_: any, record: Flight) => (
        <Space vertical={true} align={'start'}>
          {/* { @FIXME: 此处 App 不能写死}*/}
          <Text style={{ fontSize: '14px', fontWeight: 'bold' }}>{'剪映'}</Text>
          <Text>流量层：{record.layer_name}</Text>
          <Tag>流量：{`${Math.round(record.version_resource * 100)}%`}</Tag>
        </Space>
      ),
    },
    {
      title: '实验 Owner',
      key: 'owners',
      render: (_: any, record: Flight) => (
        <Space vertical={true} align={'start'}>
          {record.owner.map((owner: string) => (
            <Tag>{owner}</Tag>
          ))}
        </Space>
      ),
    },
    {
      title: '时间',
      key: 'time',
      width: '180px',
      render: (_: any, record: Flight) => (
        <Space vertical={true} align={'start'}>
          <Text>{formatTimeDifference(record.start_time, record.end_time)}</Text>
          <Text size={'small'} style={{ color: 'gray' }}>
            {record.start_time}
          </Text>
          <Text size={'small'} style={{ color: 'gray' }}>
            {record.end_time}
          </Text>
        </Space>
      ),
    },
  ];
  useEffect(() => {
    if (loading) {
      return;
    }
    setLoading(true);
    testQueryLVAllFlightList({ data: {} })
      .then(res => {
        if (res === undefined || res === null) {
          new Error(`response is invalid!`);
          return;
        }
        Toast.success(`拉取成功，data.length=${res.flights.length}`);
        setFlightList(res?.flights);
        setLoading(false);
      })
      .catch(err => {
        setLoading(false);
        Toast.error(`拉取发生错误：${err}`);
      });
  }, []);

  return (
    <div>
      <Table
        columns={columns}
        loading={loading}
        dataSource={flightList}
        pagination={{
          pageSize: 10,
        }}
        style={{ width: '100%' }}
        bordered={true}
      />
    </div>
  );
};

export default LibraDebugPage;
