import {
  ColumnCustomStatus,
  StoryRevenueTableColumnConfig,
  StoryRevenueTableColumnGroup,
} from '@shared/storyRevenueReviewPlatform/StoryRevenueUserSettings/StoryRevenueTableColumnUserSetting';
import React, { ReactNode, useEffect, useState } from 'react';
import { ColumnProps, RowSelection } from '@douyinfe/semi-ui/lib/es/table';
import { Banner, Button, Card, Col, Row, SideSheet, Space, Table, Typography } from '@douyinfe/semi-ui';
import { IconEditStroked } from '@douyinfe/semi-icons';

const { Text } = Typography;

const CustomTableFooter: React.FC<{ reset: () => Promise<void>; apply: () => Promise<void> }> = ({ reset, apply }) => {
  const [applyLoading, setApplyLoading] = useState(false);
  const [resetLoading, setResetLoading] = useState(false);

  return (
    <div style={{ float: 'right' }}>
      <Space vertical={false}>
        <Button
          onClick={() => {
            setResetLoading(true);
            reset().then(res => {
              setResetLoading(false);
            });
          }}
        >
          重置
        </Button>
        <Button
          theme="solid"
          type="primary"
          onClick={() => {
            setApplyLoading(true);
            apply().then(res => {
              setApplyLoading(false);
            });
          }}
          loading={applyLoading}
        >
          保存
        </Button>
      </Space>
    </div>
  );
};

const CustomTableColumnList: React.FC<{
  group: StoryRevenueTableColumnGroup;
  onSelectStateChanged: (status: ColumnCustomStatus) => void;
}> = ({ group, onSelectStateChanged }) => {
  const [columnProps, setColumnProps] = useState<ColumnProps<StoryRevenueTableColumnConfig>[]>();
  const [selectedKeys, setSelectedKeys] = useState<(string | number)[] | undefined>([]);
  const [sortedConfigs, setSortedConfigs] = useState<StoryRevenueTableColumnConfig[]>([]);
  const [lockedKeys, setLockedKeys] = useState<(string | number)[] | undefined>([]);

  const [customStatus, setCustomStatus] = useState<ColumnCustomStatus>({});
  const [originColumnConfig, setOriginColumnConfig] = useState<{ [key: string]: StoryRevenueTableColumnConfig }>({});

  useEffect(() => {
    const sorted = group.configs.sort((a, b) => {
      if (a.lock_enable && !b.lock_enable) {
        return -1;
      } else if (!a.lock_enable && b.lock_enable) {
        return 1;
      } else if (a.enable && !b.enable) {
        return -1;
      } else if (!a.enable && b.enable) {
        return 1;
      } else {
        return 0;
      }
    });
    setSortedConfigs(sorted.filter(it => it.column_id !== 'operation'));
    const newColumn: ColumnProps<StoryRevenueTableColumnConfig>[] = [
      {
        title: `字段名称（${group.group_name}）`,
        dataIndex: 'column_id',
        render: (text, record, index) => <Text size={'small'}>{record.column_name}</Text>,
      },
    ];
    setColumnProps(newColumn);
    const originConfig: { [key: string]: StoryRevenueTableColumnConfig } = {};
    sorted.forEach(it => {
      originConfig[it.column_id] = it;
    });
    setOriginColumnConfig(originConfig);
  }, [group]);

  useEffect(() => {
    setSelectedKeys(sortedConfigs.filter(c => c.enable).map(c => c.column_id));
    setLockedKeys(sortedConfigs.filter(c => c.lock_enable).map(c => c.column_id));
  }, [sortedConfigs]);

  // useEffect(() => {
  // 	onSelectStateChanged(customStatus);
  // }, [customStatus]);

  const selection: RowSelection<StoryRevenueTableColumnConfig> = {
    getCheckboxProps: record => ({
      disabled: record.lock_enable,
    }),
    selectedRowKeys: selectedKeys,
    onSelect: (record, selected) => {
      if (!record) {
        return;
      }
      const newCustomStatus: ColumnCustomStatus = {};
      Object.keys(customStatus).forEach(key => {
        newCustomStatus[key] = customStatus[key];
      });
      newCustomStatus[record.column_id] = {
        originEnable: originColumnConfig[record.column_id].enable,
        selectedEnable: selected === true,
      };
      setCustomStatus(newCustomStatus);
      onSelectStateChanged(newCustomStatus);
    },
    onSelectAll: (selected, selectedRows) => {
      const newCustomStatus: ColumnCustomStatus = {};
      Object.keys(customStatus).forEach(key => {
        newCustomStatus[key] = customStatus[key];
      });
      sortedConfigs
        .filter(it => !it.lock_enable)
        .forEach(it => {
          newCustomStatus[it.column_id] = {
            originEnable: originColumnConfig[it.column_id].enable,
            selectedEnable: selected === true,
          };
        });
      setCustomStatus(newCustomStatus);
      onSelectStateChanged(newCustomStatus);
    },
    onChange: (selectedRowKeys, selectedRows) => {
      const selectedKeySet = new Set(selectedRowKeys?.concat(lockedKeys ?? []));
      setSelectedKeys(Array.from(selectedKeySet));
    },
  };
  return (
    <Table
      columns={columnProps}
      dataSource={sortedConfigs}
      rowKey={'column_id'}
      expandIcon={false}
      bordered={true}
      rowSelection={selection}
      pagination={false}
      size={'small'}
    />
  );
};

const CustomTableColumnPanelButton: React.FC<{
  userSetting: StoryRevenueTableColumnGroup[];
  onSettingApply: (status: { [key: string]: ColumnCustomStatus }) => Promise<void>;
}> = ({ userSetting, onSettingApply }) => {
  const [columnConfigGroups, setColumnConfigGroups] = useState<StoryRevenueTableColumnGroup[]>([]);
  const [columnSelectTable, setColumnSelectTable] = useState<ReactNode[]>([]);
  const [visible, setVisible] = useState(false);

  const [groupedCustomStatus, setGroupedCustonStatus] = useState<{ [key: string]: ColumnCustomStatus }>({});
  const change = () => {
    setVisible(!visible);
  };

  useEffect(() => {
    setColumnConfigGroups(userSetting);
  }, [userSetting]);
  useEffect(() => {
    if (columnConfigGroups.length === 0) {
      return;
    }
    const tableRenders: ReactNode[] = [];
    const colSpan = 24 / columnConfigGroups.length;
    columnConfigGroups.forEach((group, index) => {
      tableRenders.push(
        <Col span={colSpan} key={group.group_id}>
          <CustomTableColumnList
            group={group}
            onSelectStateChanged={status => {
              const newStatusGroup: { [key: string]: ColumnCustomStatus } = {};
              Object.keys(groupedCustomStatus).forEach(key => {
                newStatusGroup[key] = groupedCustomStatus[key];
              });
              newStatusGroup[group.group_id] = status;
              setGroupedCustonStatus(newStatusGroup);
            }}
          />
        </Col>,
      );
    });
    setColumnSelectTable(tableRenders);
  }, [columnConfigGroups, groupedCustomStatus]);

  return (
    <>
      <Button onClick={change} icon={<IconEditStroked />} style={{ float: 'right' }}>
        自定义字段
      </Button>
      <SideSheet
        title={'自定义字段'}
        visible={visible}
        width={1100}
        onCancel={change}
        footer={
          <CustomTableFooter
            reset={async () => {
              if (Object.keys(groupedCustomStatus).length === 0) {
                return;
              }
              const effectiveGroups: StoryRevenueTableColumnGroup[] = [];
              userSetting.forEach(it => {
                effectiveGroups.push({
                  group_id: it.group_id,
                  group_name: it.group_name,
                  configs: it.configs.concat([]),
                });
              });
              setColumnConfigGroups(effectiveGroups);
              setGroupedCustonStatus({});
            }}
            apply={async () => {
              await onSettingApply(groupedCustomStatus);
              setVisible(false);
            }}
          />
        }
      >
        <Space vertical={true} spacing={'tight'} style={{ width: '100%' }}>
          <div
            style={{
              height: 35,
              overflow: 'hidden',
              width: '100%',
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              borderRadius: '3px',
            }}
          >
            <Banner
              type={'info'}
              closeIcon={null}
              style={{ width: '100%' }}
              fullMode={false}
              bordered={false}
              description={
                <Text size={'small'} style={{ color: 'rgba(var(--semi-light-blue-7), 1)' }}>
                  自定义选择感兴趣的字段，保存后列表会自动刷新
                </Text>
              }
            />
          </div>
          <Card bodyStyle={{ padding: 0 }} style={{ width: '100%' }}>
            <Row>{columnSelectTable}</Row>
          </Card>
        </Space>
      </SideSheet>
    </>
  );
};

export default CustomTableColumnPanelButton;
