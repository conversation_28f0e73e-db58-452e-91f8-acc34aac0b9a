import React, { ReactNode, useEffect, useState } from 'react';
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>ton,
  Space,
  Tooltip,
  SideSheet,
  Banner,
  Card,
  Row,
  Table,
  Form,
  Input,
  ArrayField,
  Toast,
  Tag,
  useFormApi,
  Modal,
  RadioGroup,
  Radio,
  AutoComplete,
  Tabs,
  TabPane,
  Empty,
} from '@douyinfe/semi-ui';

import {
  StoryRevenueRoleType,
  StoryRevenueUserMemberInfo,
  StoryRevenueUserMemberUpdateType,
} from '@shared/storyRevenueReviewPlatform/StoryRevenueUserMemberInfo';
import { StoryRevenueUserMemberService } from '../../../../api/service/storyRevenueReviewPlatform/StoryRevenueUserMemberService';
import { StoryRevenueTaskInfo } from '@shared/storyRevenueReviewPlatform/StoryRevenueTaskInfo';
import { StoryRevenueReviewPeriodInfo } from '@shared/storyRevenueReviewPlatform/StoryRevenueReviewPeriodInfo';
import UserCard from '@/component/UserCard';
import {
  createStoryRevenueAuthorizedBusinessLine,
  createStoryRevenueBusinessLineMember,
  createStoryRevenueUserMember,
  deleteStoryRevenueAuthorizedBusinessLine,
  deleteStoryRevenueBusinessLineMember,
  deleteStoryRevenueUserMember,
  queryStoryRevenueAuthorizedBusinessLineList,
  queryStoryRevenueBusinessLineMemberList,
  queryStoryRevenueUserMemberList,
  updateStoryRevenueAuthorizedBusinessLine,
  updateStoryRevenueBusinessLineMember,
  updateStoryRevenueUserMember,
} from '@api/storyRevenueReviewPlatform';
import {
  IconEdit,
  IconPlusCircleStroked,
  IconUserSetting,
  IconUserAdd,
  IconAlertTriangle,
  IconDelete,
  IconSearch,
} from '@douyinfe/semi-icons';
import SemiUserSelector from '@/component/SemiUserSelector';
import { User, Avatar } from '@pa/shared/dist/src/core';
import { RadioChangeEvent } from '@douyinfe/semi-foundation/lib/es/radio/radioInnerFoundation';
import { StoryRevenueBusinessLineMemberInfo } from '@shared/storyRevenueReviewPlatform/StoryRevenueBusinessLineMemberInfo';
import { AutoCompleteItems } from '@douyinfe/semi-ui/lib/es/autoComplete';
import TabBar from '@douyinfe/semi-ui/lib/es/tabs/TabBar';
import { StoryRevenueAuthorizedBusinessLineInfo } from '@shared/storyRevenueReviewPlatform/StoryRevenueAuthorizedBusinessLineInfo';
const { Text } = Typography;

const DefaultAuthorizedBusinessLine: string[] = [
  'Data-剪映CapCut-数据科学',
  '剪映CapCut-内容与付费-用户增长',
  '剪映Capcut-内容与付费-商业化-订阅产品',
  '剪映CapCut-内容与付费-内容与分发',
  '剪映CapCut-内容与付费-商业化',
  '剪映CapCut-创意工具-视频编辑器',
  '剪映CapCut-内容与付费-营销工具',
  '剪映CapCut-创意工具-图像编辑器',
  '剪映CapCut-创意工具-AI图生成',
  '剪映CapCut-创意工具-主框架与基础产品',
  '剪映CapCut-创意工具-智能成片和视频生成',
  'Open Platform-Biz Incubator-CapCut',
  '产品研发和工程架构-剪映CapCut',
  '产品研发和工程架构-中台-Tech PMO-剪映CapCut',
];

const MemberManagePanelButton: React.FC<{
  periodInfo?: StoryRevenueReviewPeriodInfo;
  onVisibleChange: (visible: boolean) => void;
  onUserMemberUpdate: (user: StoryRevenueUserMemberInfo, updateType: StoryRevenueUserMemberUpdateType) => void;
  onClose?: () => void;
}> = ({ periodInfo, onVisibleChange, onUserMemberUpdate, onClose }) => {
  const [userMemberList, setUserMemberList] = useState<StoryRevenueUserMemberInfo[]>([]);
  const [businessLineList, setBusinessLineList] = useState<StoryRevenueBusinessLineMemberInfo[]>([]);
  const [allAuthorizedBusinessLineList, setAllAuthorizedBusinessLineList] = useState<
    StoryRevenueAuthorizedBusinessLineInfo[]
  >([]);
  const [loading, setLoading] = useState(true);
  const [visible, setVisible] = useState(false);
  const [shouldRefresh, setShouldRefresh] = useState(true);
  const [shouldRefreshBusinessLine, setShouldRefreshBusinessLine] = useState(true);
  const [shouldRefreshAuthorizedBusinessLine, setShouldRefreshAuthorizedBusinessLine] = useState(true);

  const handleUpdateUserSuccess = (
    member: StoryRevenueUserMemberInfo,
    updateType: StoryRevenueUserMemberUpdateType,
  ) => {
    // 刷新数据
    setShouldRefresh(true);
    onUserMemberUpdate(member, updateType);
  };

  const handleUpdateBusinessLineSuccess = (businessLineMember: StoryRevenueBusinessLineMemberInfo) => {
    // 刷新数据
    setShouldRefreshBusinessLine(true);
  };

  const handleUpdateAuthorizedBusinessLineSuccess = (businessLineMember: StoryRevenueAuthorizedBusinessLineInfo) => {
    // 刷新数据
    setShouldRefreshAuthorizedBusinessLine(true);
  };

  // 用户列表
  useEffect(() => {
    if (!visible || !shouldRefresh) {
      return;
    }
    // 展示时，需要刷新数据
    setLoading(true);
    queryStoryRevenueUserMemberList({ data: {} })
      .then(res => {
        if (res === undefined || res === null) {
          new Error(`response is invalid!`);
          return;
        }

        // 刷新列表
        setUserMemberList(res);
        setLoading(false);
        setShouldRefresh(false);
      })
      .catch(err => {
        Toast.error(`拉取成员列表失败！${err}`);
        setLoading(false);
        setShouldRefresh(false);
      });
  }, [visible, shouldRefresh]);

  // 业务列表
  useEffect(() => {
    if (!visible || !shouldRefreshBusinessLine) {
      return;
    }
    // 展示时，需要刷新数据
    setLoading(true);
    queryStoryRevenueBusinessLineMemberList({ data: {} })
      .then(res => {
        if (res === undefined || res === null) {
          new Error(`response is invalid!`);
          return;
        }

        // 刷新列表
        setBusinessLineList(res);
        setLoading(false);
        setShouldRefreshBusinessLine(false);
      })
      .catch(err => {
        Toast.error(`拉取业务列表失败！${err}`);
        setLoading(false);
        setShouldRefreshBusinessLine(false);
      });
  }, [visible, shouldRefreshBusinessLine]);

  // 所有被授权的业务列表
  useEffect(() => {
    if (!visible || !shouldRefreshAuthorizedBusinessLine) {
      return;
    }
    // 展示时，需要刷新数据
    setLoading(true);
    queryStoryRevenueAuthorizedBusinessLineList({ data: {} })
      .then(res => {
        if (res === undefined || res === null) {
          new Error(`response is invalid!`);
          return;
        }

        // 刷新列表
        setAllAuthorizedBusinessLineList(res);
        setLoading(false);
        setShouldRefreshAuthorizedBusinessLine(false);
      })
      .catch(err => {
        Toast.error(`拉取所有被授权的业务列表失败！${err}`);
        const defaultList = [];
        for (const item of DefaultAuthorizedBusinessLine) {
          defaultList.push({
            businessLine: item,
          } as StoryRevenueBusinessLineMemberInfo);
        }
        setAllAuthorizedBusinessLineList(defaultList);
        setLoading(false);
        setShouldRefreshAuthorizedBusinessLine(false);
      });
  }, [visible, shouldRefreshAuthorizedBusinessLine]);

  const roleTypeDisplayName = (roleType: StoryRevenueRoleType): string => {
    switch (roleType) {
      case StoryRevenueRoleType.Guest:
        return '访客';
      case StoryRevenueRoleType.NormalUser:
        return '普通用户';
      case StoryRevenueRoleType.ProductManager:
        return 'PM';
      case StoryRevenueRoleType.DataAnalyst:
        return 'DA';
      case StoryRevenueRoleType.Admin:
        return '管理员';
      case StoryRevenueRoleType.PMO:
        return 'PMO';
      default:
        return '未知身份';
    }
  };

  const AddNewBusinessLineMember: React.FC<{
    onUpdateBusinessLineSuccess?: (businessLineMember: StoryRevenueBusinessLineMemberInfo) => void;
  }> = ({ onUpdateBusinessLineSuccess }) => {
    const [selectedRoleType, setSelectedRoleType] = useState(StoryRevenueRoleType.NormalUser);
    const [selectedBusinessLineMember, setSelectedBusinessLineMember] = useState<StoryRevenueBusinessLineMemberInfo>();
    const [selectedBusinessLine, setSelectedBusinessLine] = useState<string>();
    const [confirmVisible, setConfirmVisible] = useState(false);
    const [searchRelatedStringData, setSearchRelatedStringData] = useState<string[]>([]);
    const [inputValue, setInputValue] = useState('');

    const onRoleSelectChange = (e: RadioChangeEvent) => {
      if (selectedBusinessLineMember) {
        selectedBusinessLineMember.roleType = e.target.value;
      }
      setSelectedRoleType(e.target.value);
    };

    const handleConfirmOK = () => {
      // 确实是否有选中
      if (!selectedBusinessLineMember) {
        Toast.error('请选择业务线！');
        return;
      }

      // 更新数据库
      createStoryRevenueBusinessLineMember({ data: selectedBusinessLineMember })
        .then(res => {
          if (!res || !res.data) {
            throw new Error(`response is invalid!`);
          }

          if (!res.isNew) {
            // 已经存在，不重复创建
            Toast.warning('业务线已经存在！');
            setConfirmVisible(false);
            return;
          }

          Toast.success('添加业务线成功！');
          setConfirmVisible(false);
          if (onUpdateBusinessLineSuccess) {
            onUpdateBusinessLineSuccess(selectedBusinessLineMember);
          }
        })
        .catch(err => {
          Toast.error(`添加业务线失败！error: ${err.message}`);
          setConfirmVisible(false);
        });
    };

    const handleConfirmCancel = () => {
      setConfirmVisible(false);
    };

    const showConfirm = () => {
      setConfirmVisible(true);
    };

    const authorizedBusinessLineArray = () => allAuthorizedBusinessLineList.map(item => item.businessLine);

    const handleInputSearch = (value: string) => {
      if (value.length === 0) {
        setSearchRelatedStringData(authorizedBusinessLineArray());
        return;
      }

      let result: string[] = [];
      if (value) {
        result = authorizedBusinessLineArray().filter(item => item.toLowerCase().includes(value.toLowerCase()));
      }
      setSearchRelatedStringData(result);
    };

    const handleInputValueChange = (value: string | number) => {
      if (typeof value === 'number') {
        setInputValue(value.toString());
      } else {
        setInputValue(value);
      }
    };

    const handleInputSelect = (value: AutoCompleteItems) => {
      if (typeof value !== 'number' && typeof value !== 'string') {
        return;
      }

      const member = {
        businessLine: value.toString(),
        roleType: selectedRoleType,
      } as StoryRevenueBusinessLineMemberInfo;
      setSelectedBusinessLine(value.toString());
      setSelectedBusinessLineMember(member);
    };

    const handleInputFocus = () => {
      if (inputValue.length === 0) {
        setSearchRelatedStringData(authorizedBusinessLineArray());
      }
    };

    useEffect(() => {
      // 重新弹出时，已选中的用户信息情况
      setSelectedBusinessLineMember(undefined);
      setSelectedBusinessLine(undefined);
      setSearchRelatedStringData([]);
      setInputValue('');
      setSelectedRoleType(StoryRevenueRoleType.NormalUser);
    }, [confirmVisible]);

    return (
      <>
        <Button icon={<IconUserAdd />} type={'primary'} theme="solid" onClick={showConfirm}>
          添加业务线
        </Button>
        <Modal
          title={'添加业务线'}
          visible={confirmVisible}
          onOk={handleConfirmOK}
          onCancel={handleConfirmCancel}
          closeOnEsc={true}
        >
          <Space vertical={true} spacing={'tight'} style={{ width: '100%' }} align="start">
            <Text strong>
              业务线 <span style={{ color: 'red' }}>*</span>
            </Text>
            <AutoComplete
              data={searchRelatedStringData}
              value={inputValue}
              showClear
              prefix={<IconSearch />}
              placeholder="请输入业务线... "
              onSearch={handleInputSearch}
              onChange={handleInputValueChange}
              onSelect={handleInputSelect}
              onFocus={handleInputFocus}
              style={{ width: 350 }}
              emptyContent={<Empty style={{ padding: 12 }} description="未找到相关业务线" />}
            />
            <Text strong>角色</Text>
            <RadioGroup
              onChange={onRoleSelectChange}
              value={selectedRoleType}
              aria-label="选择角色"
              name="demo-radio-group"
            >
              <Radio value={StoryRevenueRoleType.Guest}>访客</Radio>
              <Radio value={StoryRevenueRoleType.NormalUser}>普通用户</Radio>
              <Radio value={StoryRevenueRoleType.ProductManager}>PM</Radio>
              <Radio value={StoryRevenueRoleType.DataAnalyst}>DA</Radio>
              <Radio value={StoryRevenueRoleType.Admin} disabled={true}>
                管理员
              </Radio>
            </RadioGroup>
          </Space>
        </Modal>
      </>
    );
  };

  const UpdateBusinessLineMember: React.FC<{
    memberInfo: StoryRevenueBusinessLineMemberInfo;
    onUpdateBusinessLineSuccess?: (member: StoryRevenueBusinessLineMemberInfo) => void;
  }> = ({ memberInfo, onUpdateBusinessLineSuccess }) => {
    const [selectedRoleType, setSelectedRoleType] = useState(memberInfo.roleType);
    const [confirmVisible, setConfirmVisible] = useState(false);
    const onRoleSelectChange = (e: RadioChangeEvent) => {
      setSelectedRoleType(e.target.value);
    };

    const handleConfirmOK = () => {
      if (memberInfo.roleType === selectedRoleType) {
        // 角色信息没有任何变化，直接返回
        setConfirmVisible(false);
        return;
      }

      // 更新数据库
      updateStoryRevenueBusinessLineMember({ data: { ...memberInfo, roleType: selectedRoleType } })
        .then(res => {
          if (res.code !== 0) {
            throw new Error(`${res.message}`);
          }

          Toast.success('修改业务线成功！');
          setConfirmVisible(false);
          if (onUpdateBusinessLineSuccess) {
            onUpdateBusinessLineSuccess({ ...memberInfo, roleType: selectedRoleType });
          }
        })
        .catch(err => {
          Toast.error(`修改业务线失败！error: ${err.message}`);
          setConfirmVisible(false);
        });
    };

    const handleConfirmCancel = () => {
      setConfirmVisible(false);
    };

    const showConfirm = () => {
      setConfirmVisible(true);
    };

    return (
      <>
        <Button icon={<IconEdit />} onClick={showConfirm} />
        <Modal
          title={'修改业务线'}
          visible={confirmVisible}
          onOk={handleConfirmOK}
          onCancel={handleConfirmCancel}
          closeOnEsc={true}
        >
          <Space vertical={true} spacing={'tight'} style={{ width: '100%' }} align="start">
            <Text strong>
              业务线 <span style={{ color: 'red' }}>*</span>
            </Text>
            <AutoComplete
              disabled={true}
              value={memberInfo.businessLine}
              showClear
              prefix={<IconSearch />}
              placeholder="业务线... "
              style={{ width: 350 }}
            />
            <Text strong>角色</Text>
            <RadioGroup
              onChange={onRoleSelectChange}
              value={selectedRoleType}
              aria-label="选择角色"
              name="demo-radio-group"
            >
              <Radio value={StoryRevenueRoleType.Guest}>访客</Radio>
              <Radio value={StoryRevenueRoleType.NormalUser}>普通用户</Radio>
              <Radio value={StoryRevenueRoleType.ProductManager}>PM</Radio>
              <Radio value={StoryRevenueRoleType.DataAnalyst}>DA</Radio>
              <Radio value={StoryRevenueRoleType.Admin} disabled={true}>
                管理员
              </Radio>
            </RadioGroup>
          </Space>
        </Modal>
      </>
    );
  };

  const DeleteBusinessLineMember: React.FC<{
    memberInfo: StoryRevenueBusinessLineMemberInfo;
    onUpdateBusinessLineSuccess?: (member: StoryRevenueBusinessLineMemberInfo) => void;
  }> = ({ memberInfo, onUpdateBusinessLineSuccess }) => {
    const [selectedRoleType, setSelectedRoleType] = useState(memberInfo.roleType);
    const [confirmVisible, setConfirmVisible] = useState(false);
    const onRoleSelectChange = (e: RadioChangeEvent) => {
      setSelectedRoleType(e.target.value);
    };

    const handleConfirmOK = () => {
      // 更新数据库
      deleteStoryRevenueBusinessLineMember({ data: memberInfo })
        .then(res => {
          if (res.code !== 0) {
            throw new Error(`${res.message}`);
          }

          Toast.success('删除业务线成功！');
          setConfirmVisible(false);
          if (onUpdateBusinessLineSuccess) {
            onUpdateBusinessLineSuccess(memberInfo);
          }
        })
        .catch(err => {
          Toast.error(`删除业务线失败！error: ${err.message}`);
          setConfirmVisible(false);
        });
    };

    const handleConfirmCancel = () => {
      setConfirmVisible(false);
    };

    const showConfirm = () => {
      setConfirmVisible(true);
    };

    return (
      <>
        <Button icon={<IconDelete />} type={'danger'} onClick={showConfirm} />
        <Modal
          icon={<IconAlertTriangle style={{ color: 'red', fontSize: 24 }} />}
          title={'删除业务线'}
          visible={confirmVisible}
          onOk={handleConfirmOK}
          onCancel={handleConfirmCancel}
          closeOnEsc={true}
        >
          <Space vertical={true} spacing={'tight'} style={{ width: '100%' }} align="start">
            <Text>即将删除业务线，请谨慎操作。</Text>
            <br />
            <Text strong>
              业务线 <span style={{ color: 'red' }}>*</span>
            </Text>
            <AutoComplete
              disabled={true}
              value={memberInfo.businessLine}
              showClear
              prefix={<IconSearch />}
              placeholder="业务线... "
              style={{ width: 350 }}
            />
            <Text strong>角色</Text>
            <RadioGroup
              onChange={onRoleSelectChange}
              value={selectedRoleType}
              aria-label="选择角色"
              name="demo-radio-group"
              disabled={true}
            >
              <Radio value={StoryRevenueRoleType.Guest}>访客</Radio>
              <Radio value={StoryRevenueRoleType.NormalUser}>普通用户</Radio>
              <Radio value={StoryRevenueRoleType.ProductManager}>PM</Radio>
              <Radio value={StoryRevenueRoleType.DataAnalyst}>DA</Radio>
              <Radio value={StoryRevenueRoleType.Admin} disabled={true}>
                管理员
              </Radio>
            </RadioGroup>
          </Space>
        </Modal>
      </>
    );
  };

  const AddAuthorizedBusinessLine: React.FC<{
    onUpdateBusinessLineSuccess?: (businessLineInfo: StoryRevenueAuthorizedBusinessLineInfo) => void;
  }> = ({ onUpdateBusinessLineSuccess }) => {
    const [confirmVisible, setConfirmVisible] = useState(false);
    const [inputValue, setInputValue] = useState('');

    const handleConfirmOK = () => {
      if (inputValue.trim().length === 0) {
        Toast.error('业务线名称不能为空！');
        return;
      }

      // 更新数据库
      createStoryRevenueAuthorizedBusinessLine({
        data: {
          businessLine: inputValue.trim(),
        },
      })
        .then(res => {
          if (!res || !res.data) {
            throw new Error(`response is invalid!`);
          }

          if (!res.isNew) {
            // 已经存在，不重复创建
            Toast.warning('业务线已经存在！');
            setConfirmVisible(false);
            return;
          }

          Toast.success('添加业务线成功！');
          setConfirmVisible(false);
          if (onUpdateBusinessLineSuccess) {
            onUpdateBusinessLineSuccess({
              businessLine: res.data.businessLine,
            } as StoryRevenueAuthorizedBusinessLineInfo);
          }
        })
        .catch(err => {
          Toast.error(`添加业务线失败！error: ${err.message}`);
          setConfirmVisible(false);
        });
    };

    const handleConfirmCancel = () => {
      setConfirmVisible(false);
    };

    const showConfirm = () => {
      setConfirmVisible(true);
    };

    const handleInputValueChange = (value: string) => {
      setInputValue(value);
    };

    useEffect(() => {
      setInputValue('');
    }, [confirmVisible]);

    return (
      <>
        <Button icon={<IconUserAdd />} type={'primary'} theme="solid" onClick={showConfirm}>
          添加业务线
        </Button>
        <Modal
          title={'添加业务线'}
          visible={confirmVisible}
          onOk={handleConfirmOK}
          onCancel={handleConfirmCancel}
          closeOnEsc={true}
        >
          <Space vertical={true} spacing={'tight'} style={{ width: '100%' }} align="start">
            <Text strong>
              业务线 <span style={{ color: 'red' }}>*</span>
            </Text>
            <Input maxLength={100} placeholder="请输入业务线... " showClear onChange={handleInputValueChange} />
          </Space>
        </Modal>
      </>
    );
  };

  const UpdateAuthorizedBusinessLine: React.FC<{
    businessLine: string;
    onUpdateBusinessLineSuccess?: (businessLineInfo: StoryRevenueAuthorizedBusinessLineInfo) => void;
  }> = ({ businessLine, onUpdateBusinessLineSuccess }) => {
    const [confirmVisible, setConfirmVisible] = useState(false);
    const [inputValue, setInputValue] = useState(businessLine);

    const handleConfirmOK = () => {
      if (inputValue.trim().length === 0) {
        Toast.error('业务线名称不能为空！');
        return;
      }

      if (inputValue.trim() === businessLine) {
        Toast.error('业务线名称未发生改动！');
        return;
      }

      // 更新数据库
      const updatedBusinessLine = inputValue.trim();
      // 先删除
      deleteStoryRevenueAuthorizedBusinessLine({
        data: {
          businessLine,
        },
      })
        .then(async res => {
          if (res.code !== 0) {
            throw new Error(`${res.message}`);
          }

          // 再更新
          const result = await createStoryRevenueAuthorizedBusinessLine({
            data: { businessLine: updatedBusinessLine },
          });

          if (!result || !result.data) {
            throw new Error(`response is invalid!`);
          }

          if (!result.isNew) {
            // 已经存在，不重复创建
            Toast.warning('业务线已经存在！');
            setConfirmVisible(false);
            return;
          }

          Toast.success('更新业务线成功！');
          setConfirmVisible(false);
          if (onUpdateBusinessLineSuccess) {
            onUpdateBusinessLineSuccess({
              businessLine: updatedBusinessLine,
            } as StoryRevenueAuthorizedBusinessLineInfo);
          }
        })
        .catch(err => {
          Toast.error(`更新业务线失败！error: ${err.message}`);
          setConfirmVisible(false);
        });
    };

    const handleConfirmCancel = () => {
      setConfirmVisible(false);
    };

    const showConfirm = () => {
      setConfirmVisible(true);
    };

    const handleInputValueChange = (value: string) => {
      setInputValue(value);
    };

    return (
      <>
        <Button icon={<IconEdit />} onClick={showConfirm} />
        <Modal
          title={'更新业务线'}
          visible={confirmVisible}
          onOk={handleConfirmOK}
          onCancel={handleConfirmCancel}
          closeOnEsc={true}
        >
          <Space vertical={true} spacing={'tight'} style={{ width: '100%' }} align="start">
            <Text strong>
              业务线 <span style={{ color: 'red' }}>*</span>
            </Text>
            <Input
              maxLength={100}
              placeholder="请输入业务线... "
              showClear
              defaultValue={businessLine}
              onChange={handleInputValueChange}
            />
          </Space>
        </Modal>
      </>
    );
  };

  const DeleteAuthorizedBusinessLine: React.FC<{
    businessLine: string;
    onUpdateBusinessLineSuccess?: (businessLineInfo: StoryRevenueAuthorizedBusinessLineInfo) => void;
  }> = ({ businessLine, onUpdateBusinessLineSuccess }) => {
    const [confirmVisible, setConfirmVisible] = useState(false);

    const handleConfirmOK = () => {
      // 更新数据库
      deleteStoryRevenueAuthorizedBusinessLine({
        data: {
          businessLine,
        },
      })
        .then(res => {
          if (res.code !== 0) {
            throw new Error(`${res.message}`);
          }

          Toast.success('删除业务线成功！');
          setConfirmVisible(false);
          if (onUpdateBusinessLineSuccess) {
            onUpdateBusinessLineSuccess({
              businessLine,
            } as StoryRevenueAuthorizedBusinessLineInfo);
          }
        })
        .catch(err => {
          Toast.error(`删除业务线失败！error: ${err.message}`);
          setConfirmVisible(false);
        });
    };

    const handleConfirmCancel = () => {
      setConfirmVisible(false);
    };

    const showConfirm = () => {
      setConfirmVisible(true);
    };

    return (
      <>
        <Button icon={<IconDelete />} type={'danger'} onClick={showConfirm} />
        <Modal
          title={'删除业务线'}
          visible={confirmVisible}
          onOk={handleConfirmOK}
          onCancel={handleConfirmCancel}
          closeOnEsc={true}
        >
          <Space vertical={true} spacing={'tight'} style={{ width: '100%' }} align="start">
            <Text strong>
              业务线 <span style={{ color: 'red' }}>*</span>
            </Text>
            <Input disabled={true} placeholder="请输入业务线... " showClear defaultValue={businessLine} />
          </Space>
        </Modal>
      </>
    );
  };

  const AddNewMember: React.FC<{
    onUpdateUserSuccess?: (member: StoryRevenueUserMemberInfo, updateType: StoryRevenueUserMemberUpdateType) => void;
  }> = ({ onUpdateUserSuccess }) => {
    const [selectedRoleType, setSelectedRoleType] = useState(StoryRevenueRoleType.NormalUser);
    const [selectedMember, setSelectedMember] = useState<StoryRevenueUserMemberInfo>();
    const [selectedUser, setSelectedUser] = useState<User>();
    const [confirmVisible, setConfirmVisible] = useState(false);
    const onRoleSelectChange = (e: RadioChangeEvent) => {
      if (selectedMember) {
        selectedMember.roleType = e.target.value;
      }
      setSelectedRoleType(e.target.value);
    };

    const handleConfirmOK = () => {
      // 确实是否有选中
      if (!selectedMember) {
        Toast.error('请选择用户！');
        return;
      }

      // 更新数据库
      createStoryRevenueUserMember({ data: selectedMember })
        .then(res => {
          if (!res || !res.data) {
            throw new Error(`response is invalid!`);
          }

          if (!res.isNew) {
            // 已经存在，不重复创建
            Toast.warning('用户已经存在！');
            setConfirmVisible(false);
            return;
          }

          Toast.success('添加用户成功！');
          setConfirmVisible(false);
          if (onUpdateUserSuccess) {
            onUpdateUserSuccess(selectedMember, StoryRevenueUserMemberUpdateType.Add);
          }
        })
        .catch(err => {
          Toast.error(`添加用户失败！error: ${err.message}`);
          setConfirmVisible(false);
        });
    };

    const handleConfirmCancel = () => {
      setConfirmVisible(false);
    };

    const showConfirm = () => {
      setConfirmVisible(true);
    };

    useEffect(() => {
      // 重新弹出时，已选中的用户信息情况
      setSelectedMember(undefined);
      setSelectedUser(undefined);
      setSelectedRoleType(StoryRevenueRoleType.NormalUser);
    }, [confirmVisible]);

    return (
      <>
        <Button icon={<IconUserAdd />} type={'primary'} theme="solid" onClick={showConfirm}>
          添加成员
        </Button>
        <Modal
          title={'添加成员'}
          visible={confirmVisible}
          onOk={handleConfirmOK}
          onCancel={handleConfirmCancel}
          closeOnEsc={true}
        >
          <Space vertical={true} spacing={'tight'} style={{ width: '100%' }} align="start">
            <Text strong>
              用户 <span style={{ color: 'red' }}>*</span>
            </Text>
            <SemiUserSelector
              initUsers={selectedUser ? [selectedUser] : []}
              style={{ width: 250 }}
              multi={false}
              updateSelectedUsers={users => {
                if (!users || users.length === 0) {
                  return;
                }
                const user = users[0];
                if (!user.email) {
                  Toast.error('选择的用户邮箱信息为空！');
                  return;
                }

                let avatar;
                if (typeof user.avatar === 'string') {
                  avatar = user.avatar;
                } else if (typeof user.avatar === 'object') {
                  avatar = user.avatar.avatar_72;
                }

                const member = {
                  email: user.email,
                  roleType: selectedRoleType,
                  name: user.name,
                  avatar,
                  openId: user.open_id,
                } as StoryRevenueUserMemberInfo;
                setSelectedUser(user);
                setSelectedMember(member);
              }}
            />
            <Text strong>角色</Text>
            <RadioGroup
              onChange={onRoleSelectChange}
              value={selectedRoleType}
              aria-label="选择角色"
              name="demo-radio-group"
            >
              <Radio value={StoryRevenueRoleType.Guest}>访客</Radio>
              <Radio value={StoryRevenueRoleType.NormalUser}>普通用户</Radio>
              <Radio value={StoryRevenueRoleType.ProductManager}>PM</Radio>
              <Radio value={StoryRevenueRoleType.DataAnalyst}>DA</Radio>
              <Radio value={StoryRevenueRoleType.PMO}>PMO</Radio>
              <Radio value={StoryRevenueRoleType.Admin}>管理员</Radio>
            </RadioGroup>
          </Space>
        </Modal>
      </>
    );
  };

  const UpdateMember: React.FC<{
    memberInfo: StoryRevenueUserMemberInfo;
    onUpdateUserSuccess?: (member: StoryRevenueUserMemberInfo, updateType: StoryRevenueUserMemberUpdateType) => void;
  }> = ({ memberInfo, onUpdateUserSuccess }) => {
    const [selectedRoleType, setSelectedRoleType] = useState(memberInfo.roleType);
    const [confirmVisible, setConfirmVisible] = useState(false);
    const onRoleSelectChange = (e: RadioChangeEvent) => {
      setSelectedRoleType(e.target.value);
    };

    const handleConfirmOK = () => {
      if (memberInfo.roleType === selectedRoleType) {
        // 角色信息没有任何变化，直接返回
        setConfirmVisible(false);
        return;
      }

      // 更新数据库
      updateStoryRevenueUserMember({ data: { ...memberInfo, roleType: selectedRoleType } })
        .then(res => {
          if (res.code !== 0) {
            throw new Error(`${res.message}`);
          }

          Toast.success('修改用户成功！');
          setConfirmVisible(false);
          if (onUpdateUserSuccess) {
            onUpdateUserSuccess({ ...memberInfo, roleType: selectedRoleType }, StoryRevenueUserMemberUpdateType.Update);
          }
        })
        .catch(err => {
          Toast.error(`修改用户失败！error: ${err.message}`);
          setConfirmVisible(false);
        });
    };

    const handleConfirmCancel = () => {
      setConfirmVisible(false);
    };

    const showConfirm = () => {
      setConfirmVisible(true);
    };

    const selectedUser = () =>
      ({
        email: memberInfo.email,
        avatar: memberInfo.avatar,
        name: memberInfo.name,
        open_id: memberInfo.openId,
      }) as User;

    return (
      <>
        <Button icon={<IconEdit />} onClick={showConfirm} />
        <Modal
          title={'修改成员'}
          visible={confirmVisible}
          onOk={handleConfirmOK}
          onCancel={handleConfirmCancel}
          closeOnEsc={true}
        >
          <Space vertical={true} spacing={'tight'} style={{ width: '100%' }} align="start">
            <Text strong>
              用户 <span style={{ color: 'red' }}>*</span>
            </Text>
            <SemiUserSelector
              initUsers={[selectedUser()]}
              style={{ width: 250 }}
              multi={false}
              disabled={true}
              updateSelectedUsers={users => {
                console.log(`selected users: ${users}`);
              }}
            />
            <Text strong>角色</Text>
            <RadioGroup
              onChange={onRoleSelectChange}
              value={selectedRoleType}
              aria-label="选择角色"
              name="demo-radio-group"
            >
              <Radio value={StoryRevenueRoleType.Guest}>访客</Radio>
              <Radio value={StoryRevenueRoleType.NormalUser}>普通用户</Radio>
              <Radio value={StoryRevenueRoleType.ProductManager}>PM</Radio>
              <Radio value={StoryRevenueRoleType.DataAnalyst}>DA</Radio>
              <Radio value={StoryRevenueRoleType.PMO}>PMO</Radio>
              <Radio value={StoryRevenueRoleType.Admin}>管理员</Radio>
            </RadioGroup>
          </Space>
        </Modal>
      </>
    );
  };

  const DeleteMember: React.FC<{
    memberInfo: StoryRevenueUserMemberInfo;
    onUpdateUserSuccess?: (member: StoryRevenueUserMemberInfo, updateType: StoryRevenueUserMemberUpdateType) => void;
  }> = ({ memberInfo, onUpdateUserSuccess }) => {
    const [selectedRoleType, setSelectedRoleType] = useState(memberInfo.roleType);
    const [confirmVisible, setConfirmVisible] = useState(false);
    const onRoleSelectChange = (e: RadioChangeEvent) => {
      setSelectedRoleType(e.target.value);
    };

    const handleConfirmOK = () => {
      // 更新数据库
      deleteStoryRevenueUserMember({ data: memberInfo })
        .then(res => {
          if (res.code !== 0) {
            throw new Error(`${res.message}`);
          }

          Toast.success('删除用户成功！');
          setConfirmVisible(false);
          if (onUpdateUserSuccess) {
            onUpdateUserSuccess(memberInfo, StoryRevenueUserMemberUpdateType.Delete);
          }
        })
        .catch(err => {
          Toast.error(`删除用户失败！error: ${err.message}`);
          setConfirmVisible(false);
        });
    };

    const handleConfirmCancel = () => {
      setConfirmVisible(false);
    };

    const showConfirm = () => {
      setConfirmVisible(true);
    };

    const selectedUser = () =>
      ({
        email: memberInfo.email,
        avatar: memberInfo.avatar,
        name: memberInfo.name,
        open_id: memberInfo.openId,
      }) as User;

    return (
      <>
        <Button icon={<IconDelete />} type={'danger'} onClick={showConfirm} />
        <Modal
          icon={<IconAlertTriangle style={{ color: 'red', fontSize: 24 }} />}
          title={'删除成员'}
          visible={confirmVisible}
          onOk={handleConfirmOK}
          onCancel={handleConfirmCancel}
          closeOnEsc={true}
        >
          <Space vertical={true} spacing={'tight'} style={{ width: '100%' }} align="start">
            <Text>即将删除成员，请谨慎操作。</Text>
            <br />
            <Text strong>
              用户 <span style={{ color: 'red' }}>*</span>
            </Text>
            <SemiUserSelector
              initUsers={[selectedUser()]}
              style={{ width: 250 }}
              multi={false}
              disabled={true}
              updateSelectedUsers={users => {
                console.log(`selected users: ${users}`);
              }}
            />
            <Text strong>角色</Text>
            <RadioGroup
              onChange={onRoleSelectChange}
              value={selectedRoleType}
              aria-label="选择角色"
              name="demo-radio-group"
              disabled={true}
            >
              <Radio value={StoryRevenueRoleType.Guest}>访客</Radio>
              <Radio value={StoryRevenueRoleType.NormalUser}>普通用户</Radio>
              <Radio value={StoryRevenueRoleType.ProductManager}>PM</Radio>
              <Radio value={StoryRevenueRoleType.DataAnalyst}>DA</Radio>
              <Radio value={StoryRevenueRoleType.Admin}>管理员</Radio>
            </RadioGroup>
          </Space>
        </Modal>
      </>
    );
  };

  const columns = [
    {
      title: '用户名',
      dataIndex: 'email',
      key: 'email',
      width: '60%',
      render: (_: any, record: StoryRevenueUserMemberInfo) => (
        <UserCard
          email={record.email}
          triggerType="hover"
          simpleUserData={{
            avatarUrl: record.avatar ?? '',
            name: record.name ?? '',
          }}
        />
      ),
    },
    {
      title: '角色权限',
      key: 'roleType',
      width: '20%',
      render: (_: any, record: StoryRevenueUserMemberInfo) => <Text>{roleTypeDisplayName(record.roleType)}</Text>,
    },
    {
      title: '操作',
      key: 'operation',
      width: '20%',
      render: (_: any, record: StoryRevenueUserMemberInfo) => (
        <Space>
          <UpdateMember memberInfo={record} onUpdateUserSuccess={handleUpdateUserSuccess} />
          <DeleteMember memberInfo={record} onUpdateUserSuccess={handleUpdateUserSuccess} />
        </Space>
      ),
    },
  ];

  const columnsBusinessLine = [
    {
      title: '业务线',
      dataIndex: 'businessLine',
      key: 'businessLine',
      width: '60%',
      render: (_: any, record: StoryRevenueBusinessLineMemberInfo) => <Text>{record.businessLine}</Text>,
    },
    {
      title: '业务权限',
      key: 'roleType',
      width: '20%',
      render: (_: any, record: StoryRevenueBusinessLineMemberInfo) => (
        <Text>{roleTypeDisplayName(record.roleType)}</Text>
      ),
    },
    {
      title: '操作',
      key: 'operation',
      width: '20%',
      render: (_: any, record: StoryRevenueBusinessLineMemberInfo) => (
        <Space>
          <UpdateBusinessLineMember memberInfo={record} onUpdateBusinessLineSuccess={handleUpdateBusinessLineSuccess} />
          <DeleteBusinessLineMember memberInfo={record} onUpdateBusinessLineSuccess={handleUpdateBusinessLineSuccess} />
        </Space>
      ),
    },
  ];

  const columnsAuthorizedBusinessLine = [
    {
      title: '业务线',
      dataIndex: 'businessLine',
      key: 'businessLine',
      width: '60%',
      render: (_: any, record: StoryRevenueAuthorizedBusinessLineInfo) => <Text>{record.businessLine}</Text>,
    },
    {
      title: '操作',
      key: 'operation',
      width: '20%',
      render: (_: any, record: StoryRevenueAuthorizedBusinessLineInfo) => (
        <Space>
          <UpdateAuthorizedBusinessLine
            businessLine={record.businessLine}
            onUpdateBusinessLineSuccess={handleUpdateAuthorizedBusinessLineSuccess}
          />
          <DeleteAuthorizedBusinessLine
            businessLine={record.businessLine}
            onUpdateBusinessLineSuccess={handleUpdateAuthorizedBusinessLineSuccess}
          />
        </Space>
      ),
    },
  ];

  const handleSideSheetVisibleChange = () => {
    setVisible(!visible);
    onVisibleChange(!visible);
  };

  const CustomTableFooter: React.FC = () => (
    <div style={{ float: 'right' }}>
      <Space vertical={false} spacing="medium">
        <Button disabled={loading} onClick={handleSideSheetVisibleChange}>
          关闭
        </Button>
      </Space>
    </div>
  );

  return (
    <div>
      <Tooltip content="权限管理">
        <Button
          icon={<IconUserSetting />}
          theme="borderless"
          style={{ color: 'rgba(var(--semi-grey-5), 1)' }}
          onClick={handleSideSheetVisibleChange}
        />
      </Tooltip>
      <SideSheet
        title={'权限管理'}
        visible={visible}
        footer={<CustomTableFooter />}
        onCancel={handleSideSheetVisibleChange}
        width={650}
      >
        <div>
          <div style={{ width: '100%' }}>
            <Banner
              type={'info'}
              closeIcon={null}
              style={{ width: '100%' }}
              fullMode={false}
              bordered={false}
              description={
                <Text size={'small'} style={{ color: 'rgba(var(--semi-light-blue-7), 1)' }}>
                  权限说明：
                  <br />
                  1. 访客（无查看权限）；普通用户（可查看）
                  <br />
                  2. PM（可查看、可编辑）；DA（可查看、可编辑、可导出）
                  <br />
                  3. 管理员（可查看、可编辑、可导出、可授权）
                </Text>
              }
            />
          </div>
          <Tabs type="line">
            <TabPane tab="成员管理" itemKey="1">
              <Space vertical={true} spacing={'tight'} style={{ width: '100%' }} align="start">
                <div style={{ width: '100%', display: 'flex', justifyContent: 'flex-end' }}>
                  {/* 内部的子元素靠右对齐 */}
                  <AddNewMember onUpdateUserSuccess={handleUpdateUserSuccess} />
                </div>
                <Table
                  columns={columns}
                  dataSource={userMemberList}
                  pagination={{
                    pageSize: 10,
                  }}
                  style={{ width: '100%' }}
                  bordered={true}
                />
              </Space>
            </TabPane>
            <TabPane tab="业务线管理" itemKey="2">
              <Space vertical={true} spacing={'tight'} style={{ width: '100%' }} align="start">
                <div style={{ width: '100%', display: 'flex', justifyContent: 'flex-end' }}>
                  {/* 内部的子元素靠右对齐 */}
                  <AddNewBusinessLineMember onUpdateBusinessLineSuccess={handleUpdateBusinessLineSuccess} />
                </div>
                <Table
                  columns={columnsBusinessLine}
                  dataSource={businessLineList}
                  pagination={{
                    pageSize: 10,
                  }}
                  style={{ width: '100%' }}
                  bordered={true}
                />
              </Space>
            </TabPane>
            <TabPane tab="业务线清单" itemKey="3">
              <Space vertical={true} spacing={'tight'} style={{ width: '100%' }} align="start">
                <div style={{ width: '100%', display: 'flex', justifyContent: 'flex-end' }}>
                  {/* 内部的子元素靠右对齐 */}
                  <AddAuthorizedBusinessLine onUpdateBusinessLineSuccess={handleUpdateAuthorizedBusinessLineSuccess} />
                </div>
                <Table
                  columns={columnsAuthorizedBusinessLine}
                  dataSource={allAuthorizedBusinessLineList}
                  pagination={{
                    pageSize: 10,
                  }}
                  style={{ width: '100%' }}
                  bordered={true}
                />
              </Space>
            </TabPane>
          </Tabs>
        </div>
      </SideSheet>
    </div>
  );
};

export default MemberManagePanelButton;
