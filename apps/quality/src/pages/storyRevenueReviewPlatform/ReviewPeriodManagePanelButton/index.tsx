import React, { ReactNode, useEffect, useState } from 'react';
import {
  Typography,
  Button,
  Space,
  Tooltip,
  SideSheet,
  Table,
  Toast,
  Modal,
  RadioGroup,
  Radio,
  Input,
  Tag,
  Select,
} from '@douyinfe/semi-ui';

import { StoryRevenueRoleType } from '@shared/storyRevenueReviewPlatform/StoryRevenueUserMemberInfo';
import {
  MEEGO_BENEFITS_REVIEW_FIELD,
  StoryRevenueReviewPeriodInfo,
  StoryRevenueReviewPeriodUpdateType,
} from '@shared/storyRevenueReviewPlatform/StoryRevenueReviewPeriodInfo';
import {
  createStoryRevenueReviewPeriodInfo,
  deleteStoryRevenueReviewPeriod,
  queryStoryRevenueReviewPeriodInfoList,
  updateStoryRevenueReviewPeriod,
} from '@api/storyRevenueReviewPlatform';
import { IconEdit, IconAlertTriangle, IconDelete, IconLayers, IconPlus } from '@douyinfe/semi-icons';
import { QueryMeegoViewWorkItemList } from '@api/meego';
import { RadioChangeEvent } from '@douyinfe/semi-foundation/lib/es/radio/radioInnerFoundation';
const { Text } = Typography;

// Meego 视图链接格式正则匹配
const MEEGO_STORY_VIEW_URL_REG_PATTERN =
  /^https:\/\/meego\.larkoffice\.com\/faceu\/storyView\/([a-zA-Z0-9_\-]{9})(\?.*)?$/;

// 默认的 Meego Review 周期
const DEFAULT_MEEGO_REVIEW_PERIOD_VALUE = '6evrt9kov';

const ReviewPeriodManagePanelButton: React.FC<{
  onVisibleChange: (visible: boolean) => void;
  onPeriodUpdate: (period: StoryRevenueReviewPeriodInfo, updateType: StoryRevenueReviewPeriodUpdateType) => void;
  onClose?: () => void;
}> = ({ onVisibleChange, onPeriodUpdate, onClose }) => {
  const [periodInfoList, setPeriodInfoList] = useState<StoryRevenueReviewPeriodInfo[]>([]);
  const [loading, setLoading] = useState(true);
  const [visible, setVisible] = useState(false);
  const [shouldRefresh, setShouldRefresh] = useState(true);

  const handleUpdatePeriodSuccess = (
    period: StoryRevenueReviewPeriodInfo,
    updateType: StoryRevenueReviewPeriodUpdateType,
  ) => {
    // 刷新数据
    setShouldRefresh(true);
    onPeriodUpdate(period, updateType);
  };

  // 用户列表
  useEffect(() => {
    if (!visible || !shouldRefresh) {
      return;
    }
    // 展示时，需要刷新数据
    setLoading(true);
    queryStoryRevenueReviewPeriodInfoList({ data: {} })
      .then(res => {
        if (res === undefined || res === null) {
          new Error(`response is invalid!`);
          return;
        }
        // 排序一下，将 Default 周期放到前面
        res.sort((a, b) => (b.isDefault ? 1 : 0) - (a.isDefault ? 1 : 0));
        // 刷新列表
        setPeriodInfoList(res);
        setLoading(false);
        setShouldRefresh(false);
      })
      .catch(err => {
        Toast.error(`拉取 Review 周期列表失败！${err}`);
        setLoading(false);
        setShouldRefresh(false);
      });
  }, [visible, shouldRefresh]);

  const AddNewPeriod: React.FC<{
    onUpdatePeriodSuccess?: (
      period: StoryRevenueReviewPeriodInfo,
      updateType: StoryRevenueReviewPeriodUpdateType,
    ) => void;
  }> = ({ onUpdatePeriodSuccess }) => {
    const [selectedPeriod, setSelectedPeriod] = useState<StoryRevenueReviewPeriodInfo>();
    const [confirmVisible, setConfirmVisible] = useState(false);
    const [editingMeegoUrl, setEditingMeegoUrl] = useState('');
    const [editingPeriodName, setEditingPeriodName] = useState('');
    const [editingPeriodId, setEditingPeriodId] = useState('');
    const [meegoUrlValid, setMeegoUrlValid] = useState(false);
    const [periodNameValid, setPeriodNameValid] = useState(false);
    const [requesting, setRequesting] = useState(false);
    const [asDefaultPeriod, setAsDefaultPeriod] = useState(0);
    const [syncDataFromOtherPeriod, setSyncDataFromOtherPeriod] = useState(1); // 数据跨周期同步
    const [existedDefaultPeriod, setExistedDefaultPeriod] = useState<StoryRevenueReviewPeriodInfo | undefined>();
    const [selectedMeegoReviewPeriodValue, setSelectedMeegoReviewPeriodValue] = useState(
      DEFAULT_MEEGO_REVIEW_PERIOD_VALUE,
    );

    const handleConfirmOK = () => {
      setRequesting(true);

      // 创建前，需要判断是否已经存在
      queryStoryRevenueReviewPeriodInfoList({
        data: {
          reviewPeriodId: editingPeriodId,
        },
      })
        .then(res => {
          if (res && res.length > 0) {
            // 已经存在，提示用户
            Toast.error('Review 周期已存在，无法创建！');
            setRequesting(false);
            return;
          }

          // 更新数据库
          const meegoReviewFiledValue = MEEGO_BENEFITS_REVIEW_FIELD.field_c4fe21.field_value.find(
            item => item.value === selectedMeegoReviewPeriodValue,
          );
          const newPeriod = {
            reviewPeriodId: editingPeriodId,
            reviewPeriodName: editingPeriodName,
            status: 0,
            meegoViewUrl: editingMeegoUrl,
            locked: false,
            isDefault: asDefaultPeriod === 1,
            meegoReviewPeriodInfo: {
              field_key: 'field_c4fe21',
              field_value: {
                label: meegoReviewFiledValue ? meegoReviewFiledValue.label : '',
                value: selectedMeegoReviewPeriodValue,
              },
            },
            enableSyncDataFromOtherPeriod: syncDataFromOtherPeriod === 1,
          } as StoryRevenueReviewPeriodInfo;
          createStoryRevenueReviewPeriodInfo({ data: newPeriod })
            .then(result => {
              if (!result) {
                throw new Error(`response is invalid!`);
              }

              Toast.success('新建 Review 周期成功！');
              // 如果是默认周期，需要将其他默认周期的 isDefault 置为 false
              if (asDefaultPeriod === 1 && existedDefaultPeriod) {
                updateStoryRevenueReviewPeriod({
                  data: { ...existedDefaultPeriod, isDefault: false },
                }).then(updateRes => {
                  if (updateRes.code === 0) {
                    // 更新下默认周期
                    setExistedDefaultPeriod(newPeriod);
                  }
                });
              }
              setConfirmVisible(false);
              setRequesting(false);
              setSelectedPeriod(newPeriod);
              if (onUpdatePeriodSuccess) {
                onUpdatePeriodSuccess(newPeriod, StoryRevenueReviewPeriodUpdateType.Add);
              }
            })
            .catch(err => {
              Toast.error(`新建 Review 周期失败！error: ${err.message}`);
              setConfirmVisible(false);
              setRequesting(false);
            });
        })
        .catch(err => {
          Toast.error(`创建 Review 周期失败！${err}`);
          setRequesting(false);
        });
    };

    const handleConfirmCancel = () => {
      setConfirmVisible(false);
    };

    const showConfirm = () => {
      setConfirmVisible(true);
    };

    useEffect(() => {
      // 重新弹出时，重置已填写信息
      setSelectedPeriod(undefined);
      setEditingMeegoUrl('');
      setEditingPeriodName('');
      setEditingPeriodId('');
      setMeegoUrlValid(false);
      setPeriodNameValid(false);
      setRequesting(false);
      setAsDefaultPeriod(0);
      setSyncDataFromOtherPeriod(1);
      setExistedDefaultPeriod(undefined);
      setSelectedMeegoReviewPeriodValue(DEFAULT_MEEGO_REVIEW_PERIOD_VALUE);
    }, [confirmVisible]);

    const handleMeegoUrlEditChange = (val: string) => {
      const valTrimmed = val.trim();
      // 输入框内容变化处理
      setEditingMeegoUrl(valTrimmed);
      // 校验 Meego URL 格式是否合法
      const match = valTrimmed.match(MEEGO_STORY_VIEW_URL_REG_PATTERN);
      if (match) {
        // 设置 Review 周期 ID
        setEditingPeriodId(match[1]);
        setMeegoUrlValid(true);
      } else {
        setMeegoUrlValid(false);
      }
    };

    const handlePeriodNameEditChange = (val: string) => {
      const valTrimmed = val.trim();
      // 输入框内容变化处理
      setEditingPeriodName(valTrimmed);
      if (valTrimmed.length > 0) {
        setPeriodNameValid(true);
      } else {
        setPeriodNameValid(false);
      }
    };

    const onDefaultPeriodSelectChange = (e: RadioChangeEvent) => {
      setAsDefaultPeriod(e.target.value);
      const firstDefault = periodInfoList.find(item => item.isDefault);
      setExistedDefaultPeriod(firstDefault);
    };

    const onSyncDataFromOtherPeriodSelectChange = (e: RadioChangeEvent) => {
      setSyncDataFromOtherPeriod(e.target.value);
    };

    const onMeegoReviewPeriodSelectChange = (
      value: string | number | any[] | Record<string, any> | undefined,
      option: Record<string, any>,
    ) => {
      setSelectedMeegoReviewPeriodValue(value as string);
    };

    return (
      <>
        <Button icon={<IconPlus />} type={'primary'} theme="solid" onClick={showConfirm}>
          新建 Review 周期
        </Button>
        <Modal
          width={600}
          title={'添加成员'}
          visible={confirmVisible}
          onOk={handleConfirmOK}
          onCancel={handleConfirmCancel}
          closeOnEsc={true}
          okButtonProps={{ disabled: !meegoUrlValid || !periodNameValid || requesting }}
        >
          <Space vertical={true} spacing={'tight'} style={{ width: '100%' }} align="start">
            <Space vertical={false}>
              <Text strong>
                Meego 视图链接 <span style={{ color: 'red' }}>*</span>
              </Text>
              <Button
                disabled={!meegoUrlValid || requesting}
                theme="outline"
                size="small"
                onClick={() => {
                  setRequesting(true);
                  QueryMeegoViewWorkItemList({
                    data: {
                      project_key: 'faceu',
                      view_id: editingPeriodId,
                      page_size: 1,
                      page_num: 1,
                    },
                  }).then(res => {
                    setRequesting(false);
                    if (res.err_code !== 0) {
                      Toast.error(`解析 Meego 视图链接失败！${res.err_msg}`);
                      return;
                    }
                    const meegoViewName = res.data.name;
                    if (meegoViewName.length === 0) {
                      Toast.error(`解析 Meego 视图链接失败！视图名称为空`);
                      return;
                    }
                    setEditingPeriodName(meegoViewName);
                    setPeriodNameValid(true);
                  });
                }}
              >
                解析链接获取周期名称
              </Button>
            </Space>
            <Input
              placeholder="Meego 视图链接"
              validateStatus={meegoUrlValid ? 'default' : 'error'}
              value={editingMeegoUrl}
              onChange={handleMeegoUrlEditChange}
            />
            <Text strong>
              Review 周期名称 <span style={{ color: 'red' }}>*</span>
            </Text>
            <Input
              placeholder="Review 周期名称"
              validateStatus={periodNameValid ? 'default' : 'error'}
              value={editingPeriodName}
              onChange={handlePeriodNameEditChange}
            />
            <Text strong>Review 周期 ID</Text>
            <Input placeholder="Review 周期 ID" value={editingPeriodId} disabled={true} />
            <Text strong>设置为默认视图</Text>
            <RadioGroup
              onChange={onDefaultPeriodSelectChange}
              value={asDefaultPeriod}
              aria-label="设置默认视图"
              name="set_default_period_group"
            >
              <Radio
                value={1}
                extra={
                  asDefaultPeriod === 1 && existedDefaultPeriod ? (
                    <div>
                      <Text style={{ color: 'rgba(var(--semi-grey-5), 1)' }}>当前默认视图：</Text>
                      <Text link={{ href: existedDefaultPeriod.meegoViewUrl, target: '_blank' }}>
                        {existedDefaultPeriod.reviewPeriodName}
                      </Text>
                      <Text style={{ color: 'rgba(var(--semi-grey-5), 1)' }}>。若选择“是”，则会替换该默认视图。</Text>
                    </div>
                  ) : (
                    ''
                  )
                }
              >
                是
              </Radio>
              <Radio value={0}>否</Radio>
            </RadioGroup>
            <Space vertical={false}>
              <Text strong>数据跨周期同步</Text>
              <Tag color={'blue'} size={'small'}>
                <Text
                  underline
                  link={{ href: 'https://bytedance.larkoffice.com/docx/HNGydhzirogC3fxW94acskwknNh', target: '_blank' }}
                  style={{ fontSize: 10 }}
                >
                  建议开启
                </Text>
              </Tag>
            </Space>
            <RadioGroup
              onChange={onSyncDataFromOtherPeriodSelectChange}
              value={syncDataFromOtherPeriod}
              aria-label="设置数据跨周期同步"
              name="set_sync_data_from_other_period"
            >
              <Radio value={1}>是</Radio>
              <Radio value={0}>否</Radio>
            </RadioGroup>
            <Text strong>Meego Review 周期</Text>
            <Select
              defaultValue={selectedMeegoReviewPeriodValue}
              style={{ width: 120 }}
              onSelect={onMeegoReviewPeriodSelectChange}
            >
              <Select.Option value="xyuifq81f">2025Q1</Select.Option>
              <Select.Option value="6evrt9kov">2025Q2</Select.Option>
              <Select.Option value="0ud45968i">2025Q3</Select.Option>
              <Select.Option value="n6v46hkt0">2025Q4</Select.Option>
            </Select>
          </Space>
        </Modal>
      </>
    );
  };

  const UpdatePeriod: React.FC<{
    periodInfo: StoryRevenueReviewPeriodInfo;
    onUpdatePeriodSuccess?: (
      periodInfo: StoryRevenueReviewPeriodInfo,
      updateType: StoryRevenueReviewPeriodUpdateType,
    ) => void;
  }> = ({ periodInfo, onUpdatePeriodSuccess }) => {
    const [confirmVisible, setConfirmVisible] = useState(false);
    const [editingPeriodName, setEditingPeriodName] = useState(periodInfo.reviewPeriodName);
    const [periodNameValid, setPeriodNameValid] = useState(true);
    const [requesting, setRequesting] = useState(false);
    const [asDefaultPeriod, setAsDefaultPeriod] = useState(periodInfo.isDefault ? 1 : 0);
    const [syncDataFromOtherPeriod, setSyncDataFromOtherPeriod] = useState(
      periodInfo.enableSyncDataFromOtherPeriod ? 1 : 0,
    ); // 数据跨周期同步
    const [editLocked, setEditLocked] = useState(periodInfo.locked ? 1 : 0);
    const [dsLocked, setDSLocked] = useState(periodInfo.dsLocked ? 1 : 0);
    const [existedDefaultPeriod, setExistedDefaultPeriod] = useState<StoryRevenueReviewPeriodInfo | undefined>();
    const [selectedMeegoReviewPeriodValue, setSelectedMeegoReviewPeriodValue] = useState(
      periodInfo.meegoReviewPeriodInfo ? periodInfo.meegoReviewPeriodInfo.field_value.value : 'unknown',
    );

    const handleConfirmOK = () => {
      setRequesting(true);

      // 更新前，需要确定已选择 Meego Review 周期
      if (selectedMeegoReviewPeriodValue === 'unknown') {
        Toast.error('请选择确定的 Meego Review 周期！');
        setRequesting(false);
        return;
      }

      // 没有任何变化，直接返回
      if (
        editingPeriodName === periodInfo.reviewPeriodName &&
        asDefaultPeriod === (periodInfo.isDefault ? 1 : 0) &&
        syncDataFromOtherPeriod === (periodInfo.enableSyncDataFromOtherPeriod ? 1 : 0) &&
        editLocked === (periodInfo.locked ? 1 : 0) &&
        dsLocked === (periodInfo.dsLocked ? 1 : 0) &&
        selectedMeegoReviewPeriodValue ===
          (periodInfo.meegoReviewPeriodInfo ? periodInfo.meegoReviewPeriodInfo.field_value.value : 'unknown')
      ) {
        setRequesting(false);
        setConfirmVisible(false);
        return;
      }

      // 如果数据已存档，但此时想取消编辑锁定，是不允许的（数据存档后，编辑锁定一定为 true）
      if (dsLocked === 1 && editLocked === 0) {
        Toast.error('已选择数据存档，不允许取消编辑锁定！');
        setRequesting(false);
        return;
      }

      // 更新数据库
      // 如果是数据存档，则编辑锁定也要置为 true
      let isEditLocked = editLocked === 1;
      if (dsLocked === 1) {
        isEditLocked = true;
      }
      const meegoReviewFiledValue = MEEGO_BENEFITS_REVIEW_FIELD.field_c4fe21.field_value.find(
        item => item.value === selectedMeegoReviewPeriodValue,
      );
      const updatedPeriodInfo = {
        ...periodInfo,
        reviewPeriodName: editingPeriodName,
        isDefault: asDefaultPeriod === 1,
        locked: isEditLocked,
        dsLocked: dsLocked === 1,
        meegoReviewPeriodInfo: {
          field_key: 'field_c4fe21',
          field_value: {
            label: meegoReviewFiledValue ? meegoReviewFiledValue.label : '',
            value: selectedMeegoReviewPeriodValue,
          },
        },
        enableSyncDataFromOtherPeriod: syncDataFromOtherPeriod === 1,
      } as StoryRevenueReviewPeriodInfo;
      updateStoryRevenueReviewPeriod({ data: updatedPeriodInfo })
        .then(async res => {
          if (res.code !== 0) {
            throw new Error(`${res.message}`);
          }

          if (updatedPeriodInfo.isDefault && existedDefaultPeriod) {
            // 如果新设置为默认周期，则需要将其他默认周期的 isDefault 置为 false
            await updateStoryRevenueReviewPeriod({ data: { ...existedDefaultPeriod, isDefault: false } });
          }

          Toast.success('修改 Review 周期成功！');
          setRequesting(false);
          setConfirmVisible(false);
          if (onUpdatePeriodSuccess) {
            onUpdatePeriodSuccess(updatedPeriodInfo, StoryRevenueReviewPeriodUpdateType.Update);
          }
        })
        .catch(err => {
          Toast.error(`修改 Review 周期失败！error: ${err.message}`);
          setRequesting(false);
          setConfirmVisible(false);
        });
    };

    const handleConfirmCancel = () => {
      setConfirmVisible(false);
    };

    const showConfirm = () => {
      setConfirmVisible(true);
    };

    useEffect(() => {
      if (!confirmVisible) {
        // 弹窗消失时，重置已修改信息
        setEditingPeriodName(periodInfo.reviewPeriodName);
        setPeriodNameValid(true);
        setRequesting(false);
        setExistedDefaultPeriod(undefined);
        setAsDefaultPeriod(periodInfo.isDefault ? 1 : 0);
        setSyncDataFromOtherPeriod(periodInfo.enableSyncDataFromOtherPeriod ? 1 : 0);
        setEditLocked(periodInfo.locked ? 1 : 0);
        setDSLocked(periodInfo.dsLocked ? 1 : 0);
        setSelectedMeegoReviewPeriodValue(
          periodInfo.meegoReviewPeriodInfo ? periodInfo.meegoReviewPeriodInfo.field_value.value : 'unknown',
        );
      }
    }, [confirmVisible]);

    const handlePeriodNameEditChange = (val: string) => {
      const valTrimmed = val.trim();
      // 输入框内容变化处理
      setEditingPeriodName(valTrimmed);
      if (valTrimmed.length > 0) {
        setPeriodNameValid(true);
      } else {
        setPeriodNameValid(false);
      }
    };

    const onDefaultPeriodSelectChange = (e: RadioChangeEvent) => {
      setAsDefaultPeriod(e.target.value);
      const firstDefault = periodInfoList.find(item => item.isDefault);
      setExistedDefaultPeriod(firstDefault);
    };

    const onSyncDataFromOtherPeriodSelectChange = (e: RadioChangeEvent) => {
      setSyncDataFromOtherPeriod(e.target.value);
    };

    const onEditLockedSelectChange = (e: RadioChangeEvent) => {
      setEditLocked(e.target.value);
    };

    const onDSLockedSelectChange = (e: RadioChangeEvent) => {
      setDSLocked(e.target.value);
    };

    const onMeegoReviewPeriodSelectChange = (
      value: string | number | any[] | Record<string, any> | undefined,
      option: Record<string, any>,
    ) => {
      setSelectedMeegoReviewPeriodValue(value as string);
    };

    return (
      <>
        <Button icon={<IconEdit />} onClick={showConfirm} />
        <Modal
          title={'修改 Review 周期'}
          width={660}
          visible={confirmVisible}
          onOk={handleConfirmOK}
          onCancel={handleConfirmCancel}
          closeOnEsc={true}
          okButtonProps={{ disabled: !periodNameValid || requesting }}
        >
          <Space vertical={true} spacing={'tight'} style={{ width: '100%' }} align="start">
            <Text strong>Meego 视图链接</Text>
            <Input placeholder="Meego 视图链接" value={periodInfo.meegoViewUrl} disabled={true} />
            <Text strong>Review 周期名称</Text>
            <Input
              placeholder="Review 周期名称"
              validateStatus={periodNameValid ? 'default' : 'error'}
              value={editingPeriodName}
              onChange={handlePeriodNameEditChange}
            />
            <Text strong>Review 周期 ID</Text>
            <Input placeholder="Review 周期 ID" value={periodInfo.reviewPeriodId} disabled={true} />

            <Space vertical={false}>
              <Text strong>设置为默认视图</Text>
              {asDefaultPeriod === 1 && existedDefaultPeriod && !periodInfo.isDefault ? (
                <div>
                  <Text style={{ color: 'rgba(var(--semi-red-4), 1)' }}>若选“是”，现有默认视图 </Text>
                  <Text link={{ href: existedDefaultPeriod.meegoViewUrl, target: '_blank' }}>
                    {existedDefaultPeriod.reviewPeriodName}
                  </Text>
                  <Text style={{ color: 'rgba(var(--semi-red-4), 1)' }}> 将被替换</Text>
                </div>
              ) : (
                ''
              )}
              {asDefaultPeriod === 0 && periodInfo.isDefault ? (
                <div>
                  <Text style={{ color: 'rgba(var(--semi-red-4), 1)' }}>
                    当前已是默认视图，若选“否”则会取消该默认视图
                  </Text>
                </div>
              ) : (
                ''
              )}
            </Space>
            <RadioGroup
              onChange={onDefaultPeriodSelectChange}
              value={asDefaultPeriod}
              aria-label="设置默认视图"
              name="set_default_period_group"
            >
              <Radio value={1}>是</Radio>
              <Radio value={0}>否</Radio>
            </RadioGroup>
            <Space vertical={false}>
              <Text strong>数据跨周期同步</Text>
              <Tag color={'blue'} size={'small'}>
                <Text
                  underline
                  link={{ href: 'https://bytedance.larkoffice.com/docx/HNGydhzirogC3fxW94acskwknNh', target: '_blank' }}
                  style={{ fontSize: 10 }}
                >
                  建议开启
                </Text>
              </Tag>
            </Space>
            <RadioGroup
              onChange={onSyncDataFromOtherPeriodSelectChange}
              value={syncDataFromOtherPeriod}
              aria-label="设置数据跨周期同步"
              name="set_sync_data_from_other_period"
            >
              <Radio value={1}>是</Radio>
              <Radio value={0}>否</Radio>
            </RadioGroup>
            <Space vertical={false}>
              <Text strong>是否编辑锁定</Text>
              {editLocked === 1 && !periodInfo.locked && (
                <Text style={{ color: 'rgba(var(--semi-red-4), 1)' }}>
                  编辑锁定后，需求收益相关信息将无法继续编辑，请谨慎操作
                </Text>
              )}
              {editLocked === 0 && periodInfo.locked && (
                <Text style={{ color: 'rgba(var(--semi-red-4), 1)' }}>当前为编辑锁定状态，请谨慎进行解锁操作</Text>
              )}
            </Space>
            <RadioGroup
              onChange={onEditLockedSelectChange}
              value={editLocked}
              aria-label="修改编辑锁定状态"
              name="set_edit_locked"
            >
              <Radio value={1}>是</Radio>
              <Radio value={0}>否</Radio>
            </RadioGroup>
            <Space vertical={false}>
              <Text strong>是否数据存档</Text>
              {dsLocked === 1 && !periodInfo.dsLocked && (
                <Text style={{ color: 'rgba(var(--semi-red-4), 1)' }}>
                  数据存档后，需求收益相关信息将无法继续编辑和自动同步，请谨慎操作
                </Text>
              )}
              {dsLocked === 0 && periodInfo.dsLocked && (
                <Text style={{ color: 'rgba(var(--semi-red-4), 1)' }}>当前为数据存档状态，请谨慎进行解档操作</Text>
              )}
            </Space>
            <RadioGroup
              onChange={onDSLockedSelectChange}
              value={dsLocked}
              aria-label="修改数据存档状态"
              name="set_ds_locked"
            >
              <Radio value={1}>是</Radio>
              <Radio value={0}>否</Radio>
            </RadioGroup>
            <Text strong>Meego Review 周期</Text>
            <Select
              defaultValue={selectedMeegoReviewPeriodValue}
              style={{ width: 120 }}
              onSelect={onMeegoReviewPeriodSelectChange}
              validateStatus={selectedMeegoReviewPeriodValue === 'unknown' ? 'error' : 'default'}
            >
              <Select.Option value="xyuifq81f">2025Q1</Select.Option>
              <Select.Option value="6evrt9kov">2025Q2</Select.Option>
              <Select.Option value="0ud45968i">2025Q3</Select.Option>
              <Select.Option value="n6v46hkt0">2025Q4</Select.Option>
              <Select.Option value="unknown">未知</Select.Option>
            </Select>
          </Space>
        </Modal>
      </>
    );
  };

  const DeletePeriod: React.FC<{
    periodInfo: StoryRevenueReviewPeriodInfo;
    onUpdatePeriodSuccess?: (
      periodInfo: StoryRevenueReviewPeriodInfo,
      updateType: StoryRevenueReviewPeriodUpdateType,
    ) => void;
  }> = ({ periodInfo, onUpdatePeriodSuccess }) => {
    const [confirmVisible, setConfirmVisible] = useState(false);
    const [requesting, setRequesting] = useState(false);
    const [selectedMeegoReviewPeriodValue, setSelectedMeegoReviewPeriodValue] = useState(
      periodInfo.meegoReviewPeriodInfo ? periodInfo.meegoReviewPeriodInfo.field_value.value : 'unknown',
    );

    const handleConfirmOK = () => {
      setRequesting(true);
      // 删除 Review 周期
      deleteStoryRevenueReviewPeriod({ data: periodInfo })
        .then(res => {
          if (res.code !== 0) {
            throw new Error(`${res.message}`);
          }

          Toast.success('删除 Review 周期成功！');
          setConfirmVisible(false);
          setRequesting(false);
          if (onUpdatePeriodSuccess) {
            onUpdatePeriodSuccess(periodInfo, StoryRevenueReviewPeriodUpdateType.Delete);
          }
        })
        .catch(err => {
          Toast.error(`删除 Review 周期失败！error: ${err.message}`);
          setConfirmVisible(false);
          setRequesting(false);
        });
    };

    const handleConfirmCancel = () => {
      setConfirmVisible(false);
    };

    const showConfirm = () => {
      setConfirmVisible(true);
    };

    return (
      <>
        <Button icon={<IconDelete />} type={'danger'} onClick={showConfirm} />
        <Modal
          icon={<IconAlertTriangle style={{ color: 'red', fontSize: 24 }} />}
          title={'删除 Review 周期'}
          width={660}
          visible={confirmVisible}
          onOk={handleConfirmOK}
          onCancel={handleConfirmCancel}
          closeOnEsc={true}
          okButtonProps={{ disabled: requesting }}
        >
          <Space vertical={true} spacing={'tight'} style={{ width: '100%' }} align="start">
            <Tag color="red">提示：删除有风险，操作需谨慎。</Tag>
            <br />
            <Text strong>Meego 视图链接</Text>
            <Input placeholder="Meego 视图链接" value={periodInfo.meegoViewUrl} disabled={true} />
            <Text strong>Review 周期名称</Text>
            <Input placeholder="Review 周期名称" value={periodInfo.reviewPeriodName} disabled={true} />
            <Text strong>是否为默认视图</Text>
            <RadioGroup
              disabled={true}
              value={periodInfo.isDefault ? 1 : 0}
              aria-label="设置默认视图"
              name="set_default_period_group"
            >
              <Radio value={1}>是</Radio>
              <Radio value={0}>否</Radio>
            </RadioGroup>
            <Text strong>数据跨周期同步</Text>
            <RadioGroup
              disabled={true}
              value={periodInfo.enableSyncDataFromOtherPeriod ? 1 : 0}
              aria-label="设置数据跨周期同步"
              name="set_sync_data_from_other_period"
            >
              <Radio value={1}>是</Radio>
              <Radio value={0}>否</Radio>
            </RadioGroup>
            <Text strong>是否编辑锁定</Text>
            <RadioGroup
              disabled={true}
              value={periodInfo.locked ? 1 : 0}
              aria-label="修改编辑锁定状态"
              name="set_edit_locked"
            >
              <Radio value={1}>是</Radio>
              <Radio value={0}>否</Radio>
            </RadioGroup>
            <Text strong>是否数据存档</Text>
            <RadioGroup
              disabled={true}
              value={periodInfo.dsLocked ? 1 : 0}
              aria-label="修改数据存档状态"
              name="set_ds_locked"
            >
              <Radio value={1}>是</Radio>
              <Radio value={0}>否</Radio>
            </RadioGroup>
          </Space>
          <Text strong>Meego Review 周期</Text>
          <br />
          <Select defaultValue={selectedMeegoReviewPeriodValue} style={{ width: 120 }} disabled={true}>
            <Select.Option value="xyuifq81f">2025Q1</Select.Option>
            <Select.Option value="6evrt9kov">2025Q2</Select.Option>
            <Select.Option value="0ud45968i">2025Q3</Select.Option>
            <Select.Option value="n6v46hkt0">2025Q4</Select.Option>
            <Select.Option value="unknown">未知</Select.Option>
          </Select>
        </Modal>
      </>
    );
  };

  const columns = [
    {
      title: 'Review 周期 ID',
      dataIndex: 'reviewPeriodId',
      key: 'reviewPeriodId',
      width: '136px',
      render: (_: any, record: StoryRevenueReviewPeriodInfo) => <Text>{record.reviewPeriodId}</Text>,
    },
    {
      title: 'Review 周期名称',
      dataIndex: 'reviewPeriodName',
      key: 'reviewPeriodName',
      width: '246px',
      render: (_: any, record: StoryRevenueReviewPeriodInfo) => {
        if (record.isDefault) {
          return (
            <Space vertical={false}>
              <Text style={{ width: '200px' }} strong>
                {record.reviewPeriodName}
              </Text>
              <Tag color={'green'} type={'solid'}>
                默认
              </Tag>
            </Space>
          );
        } else {
          return <Text>{record.reviewPeriodName}</Text>;
        }
      },
    },
    {
      title: 'Meego 视图链接',
      dataIndex: 'meegoViewUrl',
      key: 'meegoViewUrl',
      render: (_: any, record: StoryRevenueReviewPeriodInfo) => (
        <Text link={{ href: record.meegoViewUrl, target: '_blank' }}>{record.meegoViewUrl}</Text>
      ),
    },
    {
      title: '编辑已锁定',
      dataIndex: 'locked',
      key: 'locked',
      width: '110px',
      render: (_: any, record: StoryRevenueReviewPeriodInfo) => {
        if (record.locked) {
          return <Tag color={'green'}>是</Tag>;
        } else {
          return <Tag color={'blue'}>否</Tag>;
        }
      },
    },
    {
      title: '数据已存档',
      dataIndex: 'locked',
      key: 'locked',
      width: '110px',
      render: (_: any, record: StoryRevenueReviewPeriodInfo) => {
        if (record.dsLocked) {
          return <Tag color={'green'}>是</Tag>;
        } else {
          return <Tag color={'blue'}>否</Tag>;
        }
      },
    },
    {
      title: '操作',
      key: 'operation',
      width: '100px',
      render: (_: any, record: StoryRevenueReviewPeriodInfo) => (
        <Space>
          <UpdatePeriod periodInfo={record} onUpdatePeriodSuccess={handleUpdatePeriodSuccess} />
          <DeletePeriod periodInfo={record} onUpdatePeriodSuccess={handleUpdatePeriodSuccess} />
        </Space>
      ),
    },
  ];

  const handleSideSheetVisibleChange = () => {
    setVisible(!visible);
    onVisibleChange(!visible);
  };

  const CustomTableFooter: React.FC = () => (
    <div style={{ float: 'right' }}>
      <Space vertical={false} spacing="medium">
        <Button disabled={loading} onClick={handleSideSheetVisibleChange}>
          关闭
        </Button>
      </Space>
    </div>
  );

  return (
    <div>
      <Tooltip content="周期管理">
        <Button
          icon={<IconLayers />}
          theme="borderless"
          style={{ color: 'rgba(var(--semi-grey-5), 1)' }}
          onClick={handleSideSheetVisibleChange}
        />
      </Tooltip>
      <SideSheet
        title={'周期管理'}
        visible={visible}
        footer={<CustomTableFooter />}
        onCancel={handleSideSheetVisibleChange}
        width={1200}
      >
        <div>
          <Space vertical={true} spacing={'tight'} style={{ width: '100%' }} align="start">
            <div style={{ width: '100%', display: 'flex', justifyContent: 'flex-end' }}>
              {/* 内部的子元素靠右对齐 */}
              <AddNewPeriod onUpdatePeriodSuccess={handleUpdatePeriodSuccess} />
            </div>
            <Table
              columns={columns}
              dataSource={periodInfoList}
              pagination={{
                pageSize: 7,
              }}
              style={{ width: '100%' }}
              bordered={true}
            />
          </Space>
        </div>
      </SideSheet>
    </div>
  );
};

export default ReviewPeriodManagePanelButton;
