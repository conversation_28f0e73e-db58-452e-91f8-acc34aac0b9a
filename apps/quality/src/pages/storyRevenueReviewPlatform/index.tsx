import React, { useEffect, useState } from 'react';
import { Banner, Button, Empty, Space, Spin, Toast, Tooltip, Typography } from '@douyinfe/semi-ui';
import { IllustrationNoAccess, IllustrationNoAccessDark } from '@douyinfe/semi-illustrations';
import { useModel } from '@edenx/runtime/model';
import UserSettingModule from '@/model/userSettingModel';
import StoryRevenueReviewTable from '@/pages/storyRevenueReviewPlatform/reviewTable';
import StoryRevenueHomepageHeader from '@/pages/storyRevenueReviewPlatform/StoryRevenueHomepageHeader';
import {
  StoryRevenueReviewPeriodInfo,
  StoryRevenueReviewPeriodUpdateType,
} from '@shared/storyRevenueReviewPlatform/StoryRevenueReviewPeriodInfo';
import { StoryRevenueTaskInfo } from '@shared/storyRevenueReviewPlatform/StoryRevenueTaskInfo';
import {
  batchGetUserInfosByEmails,
  getUserDepartmentNameByEmail,
  getUserInfoByEmail,
  queryStoryRevenueBusinessLineMemberList,
  queryStoryRevenueUserMemberList,
  storyRevenueLogger,
} from '@api/storyRevenueReviewPlatform';
import {
  StoryRevenueRoleType,
  StoryRevenueUserMemberInfo,
  StoryRevenueUserMemberUpdateType,
} from '@shared/storyRevenueReviewPlatform/StoryRevenueUserMemberInfo';
import { IconLoading, IconHelpCircle } from '@douyinfe/semi-icons';
import SemiReactUserGroup from '@/component/UserAvatarGroup';
import { User } from '@pa/shared/dist/src/core';
const { Text } = Typography;

const StoryRevenueReviewPlatformHomePage: React.FC = () => {
  const [userSettingState] = useModel(UserSettingModule);
  const [administers, setAdministrators] = useState<User[]>();
  const [selectedPeriod, setSelectedPeriod] = useState<StoryRevenueReviewPeriodInfo>();
  const [allPeriods, setAllPeriods] = useState<StoryRevenueReviewPeriodInfo[]>([]);
  const [storyList, setStoryList] = useState<StoryRevenueTaskInfo[]>([]);
  const [userRoleType, setUserRoleType] = useState<StoryRevenueRoleType>(StoryRevenueRoleType.Guest); // 用户角色类型，枚举值：["admin", "reviewer", "operator"]
  const [shouldRefresh, setShouldRefresh] = useState(true);
  const handleStoryListChange = (newestStoryList: StoryRevenueTaskInfo[]) => {
    setStoryList(newestStoryList);
  };
  const [loading, setLoading] = useState(true);
  const [defaultTab, setDefaultTab] = useState<string>();

  const getMaxPermission = (roleTypeA: StoryRevenueRoleType, roleTypeB: StoryRevenueRoleType): StoryRevenueRoleType =>
    roleTypeA > roleTypeB ? roleTypeA : roleTypeB;

  const handleNotAuthorized = () => {
    // 无访问权限，请求管理者信息，展示无权限页面
    batchGetUserInfosByEmails({
      data: {
        emails: ['<EMAIL>', '<EMAIL>'],
      },
    }).then(userInfos => {
      if (!userInfos || userInfos.length === 0) {
        throw new Error(`response is invalid!`);
      }

      setAdministrators(userInfos);
      setUserRoleType(StoryRevenueRoleType.Guest);
      setLoading(false);
      setShouldRefresh(false);
    });
  };

  // 处理成员权限变化（添加、更新、删除用户）
  const handleUserMemberUpdate = (user: StoryRevenueUserMemberInfo, updateType: StoryRevenueUserMemberUpdateType) => {
    // 如果是更新到自己的权限，需要页面全局刷新一下
    if (user.email === userSettingState.info.email) {
      setShouldRefresh(true);
    }
  };

  // 处理 Review 周期变化（添加、更新、删除周期）
  const handleReviewPeriodUpdate = (
    reviewPeriod: StoryRevenueReviewPeriodInfo,
    updateType: StoryRevenueReviewPeriodUpdateType,
  ) => {
    setShouldRefresh(true);
  };

  //  处理咨询按钮
  const handleHelp = () => {
    window.open(
      'https://applink.larkoffice.com/client/chat/chatter/add_by_link?link_token=b2dp9bd3-cac7-4abe-8fc0-03c234ffd9de',
      '_blank',
    );
  };

  // 是否显示“数据更新期间的提醒 Banner”
  const shouldShowDatabaseUpdatingBanner = () => {
    const now = new Date();
    const hours = now.getHours();
    const minutes = now.getMinutes();

    // 02:30 ~ 10:00 期间就显示 Banner
    return (hours === 2 && minutes >= 30) || (hours > 2 && hours < 10) || (hours === 10 && minutes === 0);
  };

  const shouldShowReviewPeriodUpdatingBanner = () => selectedPeriod?.updating;

  // 数据更新期间的提醒 Banner
  const onDatabaseUpdatingBanner = () => (
    <Space vertical={false}>
      <Text>提醒：每天 02:30 ~ 10:00 会进行 Libra 实验收益数据的更新，在此期间编辑数据有可能丢失，请谨慎操作。</Text>
      <Text
        link={{
          href: 'https://bytedance.larkoffice.com/wiki/WzoPwBdUiiW6pGkJoeDcoye0nbd#FU17diIGLoHeFoxBc35cw4cgngh',
          target: '_blank',
        }}
      >
        查看数据刷新说明
      </Text>
    </Space>
  );

  const printLog = (logContent: string) => {
    storyRevenueLogger({
      data: {
        logContent,
      },
    });
  };
  useEffect(() => {
    const searchParams = new URLSearchParams(window.location.search);
    if (searchParams.get('filterMine') === 'true') {
      setDefaultTab('mine');
    }
  }, []);
  useEffect(() => {
    if (!shouldRefresh) {
      return;
    }
    setLoading(true);
    const { email } = userSettingState.info;
    printLog(`start to queryStoryRevenueUserMemberList, email: ${email}`);
    // 优先查询个人权限
    queryStoryRevenueUserMemberList({
      data: {
        email,
      },
    })
      .then(async memberResult => {
        // 再查询组织权限（两个权限取最大权限）
        const departmentInfo = await getUserDepartmentNameByEmail({ data: { email } });
        const departmentName = departmentInfo ? departmentInfo.departmentName : '';
        if (!departmentName || departmentName.length === 0) {
          // 取不到组织架构信息，以个人权限为准
          if (memberResult && memberResult.length > 0 && memberResult[0].roleType !== StoryRevenueRoleType.Guest) {
            setUserRoleType(memberResult[0].roleType);
            setLoading(false);
            setShouldRefresh(false);
            printLog(
              // eslint-disable-next-line max-len
              `did finish queryStoryRevenueUserMemberList, memberResult: ${JSON.stringify(memberResult)}, empty departmentName`,
            );
            return;
          } else {
            handleNotAuthorized();
            printLog(
              // eslint-disable-next-line max-len
              `[Not Authorized] did finish queryStoryRevenueUserMemberList, memberResult: ${JSON.stringify(memberResult)}(empty or guest role), empty departmentName`,
            );
            return;
          }
        }
        let businessLineResult;
        if (departmentName && departmentName.length > 0) {
          businessLineResult = await queryStoryRevenueBusinessLineMemberList({
            data: {
              businessLine: departmentName,
            },
          });
        }

        if (!memberResult && !businessLineResult) {
          // 个人权限和组织权限都没有
          handleNotAuthorized();
          printLog(
            // eslint-disable-next-line max-len
            `[Not Authorized] did finish queryStoryRevenueUserMemberList, empty memberResult, empty businessLineResult`,
          );
          return;
        }

        if (businessLineResult && businessLineResult.length > 0) {
          if (!memberResult || memberResult.length === 0) {
            // 取组织权限
            setUserRoleType(businessLineResult[0].roleType);
            setLoading(false);
            setShouldRefresh(false);
            printLog(
              // eslint-disable-next-line max-len
              `did finish queryStoryRevenueUserMemberList, empty memberResult, businessLineResult: ${JSON.stringify(businessLineResult)}`,
            );
            return;
          }
        }

        if (memberResult && memberResult.length > 0) {
          if (!businessLineResult || !Array.isArray(businessLineResult) || businessLineResult.length === 0) {
            // 取个人权限
            if (memberResult[0].roleType === StoryRevenueRoleType.Guest) {
              handleNotAuthorized();
              printLog(
                // eslint-disable-next-line max-len
                `[Not Authorized] did finish queryStoryRevenueUserMemberList, memberResult: ${JSON.stringify(memberResult)}(guest role), empty businessLineResult`,
              );
            } else {
              setUserRoleType(memberResult[0].roleType);
              setLoading(false);
              setShouldRefresh(false);
              printLog(
                // eslint-disable-next-line max-len
                `did finish queryStoryRevenueUserMemberList, memberResult: ${JSON.stringify(memberResult)}, empty businessLineResult`,
              );
            }
            return;
          }
        }

        if (memberResult && memberResult.length > 0 && businessLineResult && businessLineResult.length > 0) {
          // 取两者最大权限
          const maxPermission = getMaxPermission(memberResult[0].roleType, businessLineResult[0].roleType);
          setUserRoleType(maxPermission);
          setLoading(false);
          setShouldRefresh(false);
          printLog(
            // eslint-disable-next-line max-len
            `did finish queryStoryRevenueUserMemberList, memberResult: ${JSON.stringify(memberResult)}, businessLineResult: ${JSON.stringify(businessLineResult)}, maxPermission: ${maxPermission}`,
          );
          return;
        }

        // 兜底：没有权限
        handleNotAuthorized();
        printLog(
          // eslint-disable-next-line max-len
          `[Not Authorized] did finish queryStoryRevenueUserMemberList, memberResult: ${JSON.stringify(memberResult)}, businessLineResult: ${JSON.stringify(businessLineResult)}, guaranteed handling`,
        );
      })
      .catch(err => {
        Toast.error(`获取用户信息失败：${err}，请联系管理员！`);
        setLoading(false);
        setShouldRefresh(false);
        printLog(
          // eslint-disable-next-line max-len
          `queryStoryRevenueUserMemberList error: ${err.message}`,
        );
      });
  }, [userSettingState, shouldRefresh]);

  if (loading) {
    return (
      <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
        <Spin indicator={<IconLoading />} />
      </div>
    );
  }

  if (userRoleType === StoryRevenueRoleType.Guest) {
    return (
      <>
        <Empty
          image={<IllustrationNoAccess style={{ width: 150, height: 150 }} />}
          darkModeImage={<IllustrationNoAccessDark style={{ width: 150, height: 150 }} />}
          title={'无权限访问'}
          description="您暂时没有权限访问需求收益 Review 平台，请联系管理员："
        />
        <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
          {administers && <SemiReactUserGroup users={administers} triggerType={'hover'} />}
        </div>
      </>
    );
  }

  return (
    <>
      {shouldShowReviewPeriodUpdatingBanner() && (
        <Banner
          type="info"
          closeIcon={null}
          description="平台管理员正在刷新当前 Review 周期数据，暂时无法编辑。请稍后（预计 3~5 min）手动刷新重试..."
        />
      )}
      {shouldShowDatabaseUpdatingBanner() && (
        <Banner type="warning" closeIcon={null} description={onDatabaseUpdatingBanner()} />
      )}
      <StoryRevenueHomepageHeader
        userRoleType={userRoleType}
        userEmail={userSettingState.info.email}
        storyList={storyList}
        currentSelectedPeriod={selectedPeriod}
        onPeriodSelectChange={info => setSelectedPeriod(info)}
        onAllPeriodsUpdate={infos => setAllPeriods(infos)}
        onUserMemberUpdate={handleUserMemberUpdate}
        onPeriodUpdate={handleReviewPeriodUpdate}
      />
      <StoryRevenueReviewTable
        userRoleType={userRoleType}
        period={selectedPeriod}
        allPeriodInfo={allPeriods}
        onStoryListChange={handleStoryListChange}
        defaultTab={defaultTab}
      />
      <div style={{ position: 'fixed', bottom: '20px', right: '20px', zIndex: 1000 }}>
        <Tooltip content="咨询&帮助">
          <Button
            style={{ borderRadius: '50%' }}
            icon={<IconHelpCircle style={{ color: 'rgba(var(--semi-grey-3), 1)' }} />}
            onClick={handleHelp}
          />
        </Tooltip>
      </div>
    </>
  );
};
export default StoryRevenueReviewPlatformHomePage;
