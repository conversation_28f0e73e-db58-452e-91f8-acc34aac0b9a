import React from 'react';
import { Space } from '@douyinfe/semi-ui';

const StoryBenefitsDashboardPage: React.FC = () => (
  <Space vertical={true} style={{ width: '100%', height: '100%' }}>
    <iframe
      style={{ width: '100%', height: '1000px' }}
      src="https://data.bytedance.net/aeolus/pages/external/dashboard/1042317?appId=10001&feature=%7B%22dashboard%22%3A%7B%22showTitle%22%3Afalse%2C%22showOwner%22%3Afalse%2C%22showActions%22%3Afalse%2C%22sheetType%22%3A%22all%22%2C%22sheetList%22%3A%5B%5D%2C%22showHeader%22%3Afalse%2C%22showFavor%22%3Afalse%2C%22actions%22%3A%5B%5D%2C%22report%22%3A%7B%22showTitle%22%3Atrue%2C%22showTips%22%3Atrue%2C%22showToolbar%22%3Atrue%2C%22showHeader%22%3Atrue%2C%22editable%22%3Atrue%2C%22enableEnterVizQuery%22%3Atrue%2C%22actions%22%3A%5B%22excelExport%22%2C%22FullScreen%22%2C%22imgExport%22%2C%22copyToDashboard%22%2C%22embed%22%2C%22comment%22%2C%22viewReport%22%2C%22createMonitor%22%2C%22UserContract%22%2C%22CustomFields%22%2C%22Refresh%22%5D%7D%2C%22enableComment%22%3Atrue%2C%22sheet%22%3A%7B%7D%7D%7D&inline=true&sheetId=1297067"
    />
  </Space>
);

export default StoryBenefitsDashboardPage;
