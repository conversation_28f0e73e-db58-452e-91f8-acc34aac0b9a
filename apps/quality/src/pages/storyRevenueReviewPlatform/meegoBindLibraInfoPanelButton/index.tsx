import React, { ReactNode, useEffect, useState } from 'react';
import {
  Typography,
  Button,
  Space,
  Tooltip,
  SideSheet,
  Banner,
  Card,
  Row,
  Table,
  Form,
  Input,
  ArrayField,
  Toast,
  Tag,
  useFormApi,
} from '@douyinfe/semi-ui';
import {
  StoryRevenueBindLibraResultType,
  StoryRevenueConvertCopyUserToUser,
  StoryRevenueTaskInfo,
  StoryRevenueTaskSubExperimentBindType,
  StoryRevenueTaskSubExperimentInfo,
  StoryRevenueTaskSubExperimentStatus,
} from '@shared/storyRevenueReviewPlatform/StoryRevenueTaskInfo';
import { IconMinusCircle, IconPlusCircle, IconPlusCircleStroked, IconUnlink } from '@douyinfe/semi-icons';
import { StoryRevenueMeegoLibraMapInfo } from '@shared/storyRevenueReviewPlatform/StoryRevenueMeegoLibraMapInfo';
import UserCard from '@/component/UserCard';
import SemiReactUserGroup from '@/component/UserAvatarGroup';
import MeegoBindLibraInput from '@/pages/storyRevenueReviewPlatform/reviewTable/components/MeegoBindLibraInput';
import { FormApi } from '@douyinfe/semi-ui/lib/es/form';
import { TagArray } from '@/pages/storyRevenueReviewPlatform/reviewTable/ReviewTableColumnObjectFactory';
import { updateStoryRevenueTask, updateStoryRevenueTaskBindLibraInfo } from '@api/storyRevenueReviewPlatform';
import { useModel } from '@edenx/runtime/model';
import UserSettingModule from '@/model/userSettingModel';
const { Text } = Typography;

interface InputFieldValue {
  libraUrl: string;
  fieldName: string;
}

interface CustomTableFooterProps {
  onPanelCancel: () => void;
  onPanelConfirm: () => void;
  loading: boolean;
  canSaveBindResult: boolean;
  inputInEditing: boolean;
  libraFlightIdInParsing: boolean;
}

const CustomTableFooter: React.FC<CustomTableFooterProps> = ({
  onPanelCancel,
  onPanelConfirm,
  loading,
  canSaveBindResult,
  inputInEditing,
  libraFlightIdInParsing,
}) => (
  <div style={{ float: 'right' }}>
    <Space vertical={false} spacing="medium">
      <Button disabled={loading} onClick={onPanelCancel}>
        取消
      </Button>
      <Button
        disabled={loading || !canSaveBindResult || inputInEditing || libraFlightIdInParsing}
        onClick={onPanelConfirm}
        theme="solid"
        type="primary"
      >
        保存
      </Button>
    </Space>
  </div>
);

const HasBoundSubExperimentListTable: React.FC<{
  originTaskInfo: StoryRevenueTaskInfo;
  newTaskInfo: StoryRevenueTaskInfo;
  onCancelBindLibraInfo: (flightId: string) => void;
}> = ({ originTaskInfo, newTaskInfo, onCancelBindLibraInfo }) => {
  const { experimentInfo } = newTaskInfo;
  const subExperimentList = experimentInfo?.subExperimentList;

  const isNewAddedLibraInfo = (record: StoryRevenueTaskSubExperimentInfo): boolean => {
    const { libraFlightId } = record;
    const originSubExperimentList = originTaskInfo.experimentInfo?.subExperimentList;
    if (originSubExperimentList === undefined || originSubExperimentList.length === 0) {
      return true;
    }
    for (const originSubExperiment of originSubExperimentList) {
      if (originSubExperiment.libraFlightId === libraFlightId) {
        return false;
      }
    }
    return true;
  };

  const libraStatusTagArray = (status: StoryRevenueTaskSubExperimentStatus) => {
    const reviewTypeMap: Record<number, string> = {};
    reviewTypeMap[StoryRevenueTaskSubExperimentStatus.Ended] = '已结束';
    reviewTypeMap[StoryRevenueTaskSubExperimentStatus.InProgress] = '进行中';
    reviewTypeMap[StoryRevenueTaskSubExperimentStatus.ToBeScheduled] = '待调度';
    reviewTypeMap[StoryRevenueTaskSubExperimentStatus.InDebug] = '调试中';
    reviewTypeMap[StoryRevenueTaskSubExperimentStatus.Paused] = '已暂停';
    reviewTypeMap[StoryRevenueTaskSubExperimentStatus.ToBeScheduledEnded] = '待调度结束';
    reviewTypeMap[StoryRevenueTaskSubExperimentStatus.Released] = '已上线';
    const optionList = Object.values(reviewTypeMap);
    return <TagArray tagNames={[reviewTypeMap[status]]} />;
  };

  const columns = [
    {
      title: '实验 ID',
      dataIndex: 'libraFlightId',
      key: 'libraFlightId',
      render: (_: any, record: StoryRevenueTaskSubExperimentInfo) => {
        const isNew = isNewAddedLibraInfo(record);
        return (
          <Space>
            <Text
              link={{
                href: record.libraUrl,
                target: '_blank',
              }}
            >
              {record.libraFlightId}
            </Text>
            {isNew && (
              <Tag size="small" color="green">
                {' '}
                New{' '}
              </Tag>
            )}
          </Space>
        );
      },
    },
    {
      title: '实验名称',
      dataIndex: 'libraTitle',
      key: 'libraTitle',
    },
    {
      title: '实验 Owner',
      dataIndex: 'libraOwners',
      key: 'libraOwners',
      render: (_: any, record: StoryRevenueTaskSubExperimentInfo) => {
        if (record.libraOwners === undefined || record.libraOwners.length === 0) {
          return <></>;
        }
        if (record.libraOwners.length === 1) {
          const owner = record.libraOwners[0];
          return (
            <UserCard
              email={owner?.email}
              simpleUserData={{
                avatarUrl: typeof owner?.avatar === 'string' ? owner?.avatar : owner?.avatar?.avatar_240,
                name: owner?.name,
              }}
              triggerType="hover"
            />
          );
        }
        return (
          <div style={{ display: 'flex', justifyContent: 'space-between' }}>
            <SemiReactUserGroup users={StoryRevenueConvertCopyUserToUser(record.libraOwners)} triggerType={'hover'} />
            {/* {owners.map((user, index) => UserAvatar({ checkUser: user }))} */}
          </div>
        );
      },
    },
    {
      title: '实验状态',
      dataIndex: 'status',
      key: 'status',
      render: (_: any, record: StoryRevenueTaskSubExperimentInfo) => libraStatusTagArray(record.status),
    },
    {
      title: '操作',
      key: 'operation',
      render: (_: any, record: StoryRevenueTaskSubExperimentInfo) => {
        const isNew = isNewAddedLibraInfo(record);
        const isPaperAirplaneAdd = record.bindType === StoryRevenueTaskSubExperimentBindType.FromPaperAirplane;
        return (
          <Space>
            {(isNew || isPaperAirplaneAdd) && (
              <Button
                icon={<IconUnlink />}
                onClick={() => {
                  onCancelBindLibraInfo(record.libraFlightId);
                }}
              >
                解绑
              </Button>
            )}
          </Space>
        );
      },
    },
  ];
  return (
    <Table
      columns={columns}
      dataSource={subExperimentList}
      pagination={false}
      size="small"
      style={{ width: '100%' }}
      bordered={true}
    />
  );
};

const MeegoBindLibraInfoPanelButton: React.FC<{
  taskInfo: StoryRevenueTaskInfo;
  onVisibleChange: (visible: boolean) => void;
  onConfirm?: (newTaskInfo: StoryRevenueTaskInfo) => void;
  onCancel?: () => void;
}> = ({ taskInfo, onVisibleChange, onConfirm, onCancel }) => {
  const [userSettingState] = useModel(UserSettingModule);
  const [visible, setVisible] = useState(false);
  const [newTaskInfo, setNewTaskInfo] = useState<StoryRevenueTaskInfo>(taskInfo);
  const [myFormApi, setMyFormApi] = useState<FormApi<any>>();
  const [inputFieldValue, setInputFieldValue] = useState<InputFieldValue[]>([
    {
      libraUrl: '',
      fieldName: 'newBoundLibraFlights[0][libraUrl]',
    },
  ]);
  const [loading, setLoading] = useState(false);
  const [libraFlightIdInParsing, setLibraFlightIdInParsing] = useState(false);
  const [inputInEditing, setInputInEditing] = useState(false);
  const [isConfirm, setIsConfirm] = useState(false);
  const [canSaveBindResult, setCanSaveBindResult] = useState(false);

  useEffect(() => {
    if (!visible) {
      return;
    }

    // 当 Task Info 发生变化时，和原始的 taskInfo 比较下，有变化，则“保存”按钮可以操作
    const newExperimentInfo = newTaskInfo.experimentInfo;
    const originExperimentInfo = taskInfo.experimentInfo;
    const newExperimentIds: string[] = [];
    if (newExperimentInfo?.subExperimentList) {
      for (const subExperiment of newExperimentInfo.subExperimentList) {
        newExperimentIds.push(subExperiment.libraFlightId);
      }
    }

    const originExperimentIds: string[] = [];
    if (originExperimentInfo?.subExperimentList) {
      for (const subExperiment of originExperimentInfo.subExperimentList) {
        originExperimentIds.push(subExperiment.libraFlightId);
      }
    }

    if (newExperimentIds.length !== originExperimentIds.length) {
      setCanSaveBindResult(true);
      return;
    }

    const sortedArr1 = [...newExperimentIds].sort();
    const sortedArr2 = [...originExperimentIds].sort();
    const theSame = sortedArr1.every((value, index) => value === sortedArr2[index]);
    setCanSaveBindResult(!theSame);
  }, [newTaskInfo]);

  const handleSideSheetVisibleChange = () => {
    if (!visible) {
      // 做一些清理工作
      setInputFieldValue([
        {
          libraUrl: '',
          fieldName: 'newBoundLibraFlights[0][libraUrl]',
        },
      ]);
      if (!isConfirm) {
        // 若不是保存操作，则重置为原始的 taskInfo
        setNewTaskInfo({ ...taskInfo });
      } else {
        // 如果是 confirm 状态，则重置一下
        setIsConfirm(false);
      }
      myFormApi?.reset();
    }
    setVisible(!visible);
    onVisibleChange(!visible);
  };

  const handleLibraFlightIdParsed = (
    newInfo: StoryRevenueTaskInfo,
    index: number,
    value: string,
    newParsedFlightId: string,
    fieldName: string,
  ) => {
    // 处理 Input 完成 Libra Id 的解析
    setNewTaskInfo(newInfo);
    inputFieldValue[index].libraUrl = value;
    inputFieldValue[index].fieldName = fieldName;
    setInputFieldValue(inputFieldValue);
  };

  // Libra 信息正在解析中
  const handleLibraFlightIdInParsing = (inParsing: boolean) => {
    setLibraFlightIdInParsing(inParsing);
  };

  const handleInputInEditing = (isEditing: boolean) => {
    setInputInEditing(isEditing);
  };

  const handleGetFormApi = (formApi: FormApi<any>) => {
    setMyFormApi(formApi);
  };

  // 正则表达式，匹配 Libra 链接
  const urlRegex =
    /^https:\/\/(data.bytedance|libra-sg.tiktok-row|libra-va.tiktok-row)\.net\/libra\/flight\/(\d{7,8})(\/.*)?$/;

  // 已添加的 Libra 实验进行解绑
  const handleCancelBindLibraInfo = (flightId: string, shouldClearField = true) => {
    if (!newTaskInfo.experimentInfo?.subExperimentList) {
      return;
    }
    const subExperimentList = [...newTaskInfo.experimentInfo.subExperimentList];
    const { length } = subExperimentList;
    for (let i = 0; i < length; i++) {
      const subExperiment = subExperimentList[i];
      if (subExperiment.libraFlightId !== flightId) {
        continue;
      }
      subExperimentList.splice(i, 1);
      break;
    }
    const updatedNewTaskInfo = {
      ...newTaskInfo,
      experimentInfo: {
        subExperimentCount: subExperimentList.length,
        subExperimentList,
      },
    };
    setNewTaskInfo(updatedNewTaskInfo);

    if (myFormApi === undefined || !shouldClearField) {
      return;
    }

    const fieldValues = myFormApi.getValues();
    const newBoundLibraFlights = fieldValues.newBoundLibraFlights || [];
    if (newBoundLibraFlights.length === 0) {
      return;
    }

    // 重置相应的 Form.Input
    for (const item of newBoundLibraFlights) {
      const inputValue = item as InputFieldValue;
      const { libraUrl, fieldName } = inputValue;
      const match = libraUrl.match(urlRegex);
      if (!match) {
        continue;
      }
      const parsedFlightId = match[2] as string;
      if (parsedFlightId === flightId) {
        myFormApi.reset([fieldName]);
        const indexReg = /\[(\d+)\]/;
        const matchIndex = fieldName.match(indexReg);
        const index = matchIndex ? matchIndex[1] : '';
        if (index.length > 0 && parseInt(index, 10) < inputFieldValue.length) {
          // 更新一下 state
          inputFieldValue[parseInt(index, 10)].libraUrl = '';
          setInputFieldValue(inputFieldValue);
        }
      }
    }
  };

  const checkFlightIdExistedInDataSource = (flightId: string) => {
    const subExperimentList = newTaskInfo.experimentInfo?.subExperimentList || [];
    for (const subExperiment of subExperimentList) {
      if (subExperiment.libraFlightId === flightId) {
        return true;
      }
    }
    return false;
  };

  const NestedLibraFields = (formApi: FormApi<any>) => {
    const rowStyle = {
      marginTop: 12,
      marginLeft: 12,
    };
    return (
      <ArrayField field="newBoundLibraFlights" initValue={inputFieldValue}>
        {({ add, arrayFields, addWithInitValue }) => (
          <React.Fragment>
            {arrayFields.map(({ field, key, remove }, i) => (
              <div style={{ display: 'flex' }} key={key}>
                <MeegoBindLibraInput
                  taskInfo={newTaskInfo}
                  field={`${field}[libraUrl]`}
                  label={`链接 ${i + 1} `}
                  labelPosition={'left'}
                  showClear={true}
                  initValue={inputFieldValue[i].libraUrl}
                  style={{ width: 700, marginRight: 12 }}
                  rules={[{ required: true, message: 'Libra 实验链接不能为空' }]}
                  index={i}
                  onLibraFlightIdParsed={handleLibraFlightIdParsed}
                  onLibraFlightIdInParsing={handleLibraFlightIdInParsing}
                  onEditing={handleInputInEditing}
                  checkFlightIdExistedInDataSource={checkFlightIdExistedInDataSource}
                  formApi={formApi}
                  trigger={['change']}
                />
                <Button
                  type="danger"
                  theme="borderless"
                  style={rowStyle}
                  icon={<IconMinusCircle />}
                  disabled={arrayFields.length <= 1 || libraFlightIdInParsing}
                  onClick={() => {
                    remove();
                    const val = inputFieldValue[i].libraUrl;
                    inputFieldValue.splice(i, 1);
                    setInputFieldValue(inputFieldValue);
                    // 移除这个 FlightID
                    const match = val.match(urlRegex);
                    if (match) {
                      const flightId = match[2] as string;
                      handleCancelBindLibraInfo(flightId, false);
                    }
                  }}
                />
                <Button
                  icon={<IconPlusCircle />}
                  style={rowStyle}
                  disabled={i !== arrayFields.length - 1 || i >= 9 || libraFlightIdInParsing}
                  onClick={() => {
                    const addEmptyValue = {
                      libraUrl: '',
                      fieldName: `newBoundLibraFlights[${i + 1}][libraUrl]`,
                    };
                    addWithInitValue(addEmptyValue);
                    // inputFieldValue.splice(i, 0, addEmptyValue);
                    inputFieldValue.push(addEmptyValue);
                    setInputFieldValue(inputFieldValue);
                  }}
                />
              </div>
            ))}
          </React.Fragment>
        )}
      </ArrayField>
    );
  };

  const experimentBannerDisplayText = () => {
    if (newTaskInfo.experimentInfo === undefined) {
      return '暂未绑定任何实验';
    }
    return `已绑定的实验(${newTaskInfo.experimentInfo?.subExperimentCount})，说明：仅有通过纸飞机绑定的 Libra 实验可以解绑，若已在 Libra 平台绑定 Meego 则无法解绑`;
  };

  // 取消绑定结果
  const handlePanelCancel = () => {
    handleSideSheetVisibleChange();
    if (onCancel) {
      onCancel();
    }
  };

  // 保存绑定结果
  const handlePanelConfirm = () => {
    // 网络请求，更新数据
    setLoading(true);
    updateStoryRevenueTaskBindLibraInfo({
      data: {
        meegoId: newTaskInfo.meegoInfo.id,
        periodId: newTaskInfo.reviewPeriodId,
        userEmail: userSettingState.info.email,
        lastManuallyBindLibraTime: newTaskInfo.lastManuallyBindLibraTime,
        experimentInfo: newTaskInfo.experimentInfo,
        originExperimentInfo: taskInfo.experimentInfo,
      },
    })
      .then(result => {
        if (result.code !== StoryRevenueBindLibraResultType.Success) {
          Toast.error(result.message);
          setLoading(false);
          return;
        }
        if (!result.newTaskInfo) {
          throw new Error('数据返回为空');
        }
        setLoading(false);
        setIsConfirm(true);
        const updatedTaskInfo = result.newTaskInfo;
        setNewTaskInfo(updatedTaskInfo);
        // 回调给外部
        if (onConfirm) {
          onConfirm(updatedTaskInfo);
        }
        // 状态清空
        handleSideSheetVisibleChange();
      })
      .catch(err => {
        Toast.error(`绑定 Libra 实验保存失败！需求：${newTaskInfo.meegoInfo.name}，error: ${err.message}}`);
        setLoading(false);
      });
  };

  return (
    <div>
      <Tooltip content="绑定 Libra 实验">
        <Button
          theme="borderless"
          type="primary"
          onClick={handleSideSheetVisibleChange}
          icon={<IconPlusCircleStroked />}
        />
      </Tooltip>
      <SideSheet
        title={'绑定 Libra 实验'}
        visible={visible}
        width={1100}
        onCancel={handleSideSheetVisibleChange}
        footer={
          <CustomTableFooter
            onPanelCancel={handlePanelCancel}
            onPanelConfirm={handlePanelConfirm}
            loading={loading}
            canSaveBindResult={canSaveBindResult}
            inputInEditing={inputInEditing}
            libraFlightIdInParsing={libraFlightIdInParsing}
          />
        }
      >
        <Space vertical={true} spacing={'tight'} style={{ width: '100%' }}>
          <div
            style={{
              height: 35,
              overflow: 'hidden',
              width: '100%',
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              borderRadius: '3px',
            }}
          >
            <Banner
              type={'info'}
              closeIcon={null}
              style={{ width: '100%' }}
              fullMode={false}
              bordered={false}
              description={
                <Text size={'small'} style={{ color: 'rgba(var(--semi-light-blue-7), 1)' }}>
                  1. 填入 Libra 链接后使输入框失焦，会自定识别实验信息；2. 通过 “+” 或 “-” 增加和删除链接；3.
                  绑定完成后，点击右下角“保存”才能生效。
                </Text>
              }
            />
          </div>
          <Card bodyStyle={{ padding: 0 }} style={{ width: '100%' }}>
            <Row>
              <Form disabled={loading} getFormApi={handleGetFormApi} allowEmpty={true}>
                {({ formState, values, formApi }) => (
                  <div style={{ width: '100%', padding: 10 }}>
                    <Space
                      vertical={true}
                      style={{
                        width: '100%',
                        justifyContent: 'flex-start',
                        alignItems: 'flex-start',
                      }}
                    >
                      <div
                        style={{
                          width: '100%',
                          position: 'relative',
                        }}
                      >
                        <Form.Input
                          field="meegoUrl"
                          label="需求名称"
                          disabled={true}
                          style={{ width: '100%' }}
                          rules={[{ required: true, message: '需求名称不能为空' }]}
                        />
                        <Text
                          strong={true}
                          style={{ fontSize: 14, position: 'absolute', top: 42, left: 0 }}
                          link={{ href: taskInfo.meegoInfo.url, target: '_blank' }}
                        >
                          {taskInfo.meegoInfo.name}
                        </Text>
                      </div>
                    </Space>
                    <Typography.Text strong style={{ flexBasis: '100%' }}>
                      待绑定的 Libra 实验链接
                    </Typography.Text>
                    <Card
                      style={{
                        width: '100%',
                        margin: '12px 0 8px 0',
                      }}
                    >
                      {NestedLibraFields(formApi)}
                    </Card>
                  </div>
                )}
              </Form>
            </Row>
          </Card>
          <div
            style={{
              height: 35,
              overflow: 'hidden',
              width: '100%',
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              borderRadius: '3px',
            }}
          >
            <Banner
              type={'info'}
              icon={null}
              closeIcon={null}
              style={{ width: '100%' }}
              fullMode={false}
              bordered={false}
              description={
                <Text size={'small'} style={{ color: 'rgba(var(--semi-light-blue-7), 1)' }}>
                  {experimentBannerDisplayText()}
                </Text>
              }
            />
          </div>
          <Card bodyStyle={{ padding: 0 }} style={{ width: '100%' }}>
            <Row>
              <HasBoundSubExperimentListTable
                originTaskInfo={taskInfo}
                newTaskInfo={newTaskInfo}
                onCancelBindLibraInfo={handleCancelBindLibraInfo}
              />
            </Row>
          </Card>
        </Space>
      </SideSheet>
    </div>
  );
};

export default MeegoBindLibraInfoPanelButton;
