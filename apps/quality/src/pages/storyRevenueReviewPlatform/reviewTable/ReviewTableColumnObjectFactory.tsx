import { ColumnProps } from '@douyinfe/semi-ui/lib/es/table';
import {
  StoryRevenueConvertCopyUserToUser,
  StoryRevenueTaskInfo,
  StoryRevenueTaskRevenueConclusionInfo,
  StoryRevenueTaskRevenueExpectationType,
  StoryRevenueTaskRevenueReviewType,
  StoryRevenueTaskRevenueSelfAssessmentType,
  StoryRevenueTaskRevenueSignificanceInfo,
  StoryRevenueTaskSubExperimentInfo,
  StoryRevenueTaskSubExperimentStatus,
} from '@shared/storyRevenueReviewPlatform/StoryRevenueTaskInfo';
import { Button, Popover, Select, Space, Spin, Tag, TagGroup, Toast, Tooltip, Typography } from '@douyinfe/semi-ui';
import UserCard from '@/component/UserCard';
import SemiReactUserGroup from '@/component/UserAvatarGroup';
import React, { ReactNode, useEffect, useState } from 'react';
import { TagColor } from '@douyinfe/semi-ui/lib/es/tag';
import { IconEditStroked, IconInfoCircle } from '@douyinfe/semi-icons';
import { StoryRevenueReviewPeriodInfo } from '@shared/storyRevenueReviewPlatform/StoryRevenueReviewPeriodInfo';
import {
  getMeegoFieldValueAndLabel,
  TableColumnConfigMap,
  taskInfoEditable,
} from '@shared/storyRevenueReviewPlatform/StoryRevenuePlatformUtils';
import { RevenueConclusionCell } from '@/pages/storyRevenueReviewPlatform/reviewTable/components/RevenueConclusionCell';
import TableTextEditCell from '@/pages/storyRevenueReviewPlatform/reviewTable/components/TableTextEditCell';
import RelatedFlightLinkCell from '@/pages/storyRevenueReviewPlatform/reviewTable/components/RelatedFlightLinkCell';
import StoryRevenueSelfAssementEmptyCell from '@/pages/storyRevenueReviewPlatform/reviewTable/components/StoryRevenueSelfAssementEmptyCell';
import { StoryRevenueTableColumnConfig } from '@shared/storyRevenueReviewPlatform/StoryRevenueUserSettings/StoryRevenueTableColumnUserSetting';
import ShowStoryRevenueInfoEditLogButton from '@/pages/storyRevenueReviewPlatform/reviewTable/components/ShowStoryRevenueInfoEditLogButton';
import { StoryRevenueRoleType } from '@shared/storyRevenueReviewPlatform/StoryRevenueUserMemberInfo';
import EditableUserGroupCell from '@/pages/storyRevenueReviewPlatform/reviewTable/components/EditableUserGroupCell';
import RevenueReviewDocCell from '@/pages/storyRevenueReviewPlatform/reviewTable/components/RevenueReviewDocCell';
import FeatureRevenueSelector from '@/pages/storyRevenueReviewPlatform/reviewTable/components/FeatureRevenueSelector';
import FeatureOperationCell from '@/pages/storyRevenueReviewPlatform/reviewTable/components/FeatureOperationCell';
import FillInCompletedCell from '@/pages/storyRevenueReviewPlatform/reviewTable/components/fillInCompletedCell';

const { Text } = Typography;

const RevenueExpectionSelector: React.FC<{
  taskInfo: StoryRevenueTaskInfo;
  saveValue: (newValue?: StoryRevenueTaskRevenueExpectationType) => Promise<void>;
  editable: boolean;
}> = ({ taskInfo, saveValue, editable }) => {
  const [selectState, setSelectState] = useState(false);
  const [focus, setFocus] = useState(false);
  const [selectedValue, setSelectedValue] = useState<StoryRevenueTaskRevenueExpectationType>();
  const [loading, setLoading] = useState<boolean>(false);

  useEffect(() => {
    setSelectedValue(taskInfo.revenueInfo?.expectationType);
  }, [taskInfo]);

  const optionList = [
    {
      value: StoryRevenueTaskRevenueExpectationType.MeetExpectations,
      label: (
        <Tag style={{ display: 'flex', maxWidth: '100%' }} color={'blue'}>
          符合预期
        </Tag>
      ),
    },
    {
      value: StoryRevenueTaskRevenueExpectationType.ExceedExpectations,
      label: (
        <Tag style={{ display: 'flex', maxWidth: '100%' }} color={'green'}>
          超出预期
        </Tag>
      ),
    },
    {
      value: StoryRevenueTaskRevenueExpectationType.BelowExpectations,
      label: (
        <Tag style={{ display: 'flex', maxWidth: '100%' }} color={'pink'}>
          不及预期
        </Tag>
      ),
    },
  ];
  return (
    <div
      style={{
        width: '100%',
        height: '100%',
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        padding: 8,
        border: selectState ? '2px solid rgba(var(--semi-blue-7), 1)' : undefined,
      }}
      onMouseEnter={() => {
        setFocus(true);
      }}
      onMouseLeave={() => {
        setFocus(false);
      }}
    >
      {selectState ? (
        <Select
          showClear={true}
          loading={loading}
          placeholder={'请选择'}
          optionList={optionList}
          onClear={() => {
            setSelectedValue(undefined);
          }}
          value={selectedValue}
          expandRestTagsOnClick={true}
          showRestTagsPopover={true}
          style={{ width: '100%' }}
          onDropdownVisibleChange={visible => {
            if (!visible) {
              setSelectState(false);
              setFocus(false);
            }
          }}
          onChange={value => {
            if (value === undefined) {
              setSelectedValue(undefined);
              setLoading(true);
              saveValue(undefined).then(res => {
                setLoading(false);
              });
              setSelectState(false);
              setFocus(false);
            }
          }}
          onSelect={(value, option) => {
            setSelectedValue(value as StoryRevenueTaskRevenueExpectationType);
            setLoading(true);
            saveValue(value as StoryRevenueTaskRevenueExpectationType).then(res => {
              setLoading(false);
            });
          }}
          defaultOpen={true}
        />
      ) : (
        <div
          style={{
            padding: 6,
            display: 'flex',
            alignItems: 'center',
            width: '100%',
            position: 'relative',
          }}
        >
          <div
            style={{
              maxWidth: '100%',
            }}
          >
            <Spin spinning={loading}>{optionList.find(it => it.value === selectedValue)?.label}</Spin>
          </div>
          <div
            style={{
              padding: 6,
              zIndex: 1,
              width: '100%',
              position: 'absolute',
            }}
          >
            {focus && editable ? (
              <Button
                icon={<IconEditStroked />}
                onClick={() => {
                  setSelectState(true);
                }}
                theme="outline"
                style={{
                  float: 'right',
                  backgroundColor: 'rgba(var(--semi-grey-0), 1)',
                }}
              />
            ) : (
              <></>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export const tagColorArray: TagColor[] = [
  'amber',
  'blue',
  'cyan',
  'green',
  'indigo',
  'light-blue',
  'light-green',
  'lime',
  'orange',
  'pink',
  'purple',
  'red',
  'teal',
  'violet',
  'yellow',
];

export const TagArray: React.FC<{ tagNames: string[] }> = ({ tagNames }) => {
  const convertChineseStringToNumber = (str: string): number => {
    let sum = 0;
    for (const strElement of str) {
      sum += strElement.charCodeAt(0);
    }
    return sum;
  };
  return (
    <Space vertical={false} style={{ width: '95%' }}>
      <TagGroup
        maxTagCount={2}
        style={{ display: 'flex', alignItems: 'center', width: '100%' }}
        popoverProps={{ trigger: 'hover' }}
        showPopover={true}
        tagList={tagNames.map((value, index) => ({
          color: tagColorArray[convertChineseStringToNumber(value) % tagColorArray.length],
          children: value,
        }))}
      />
    </Space>
  );
};

export const TaskFeatureNameCell: React.FC<{ taskInfo: StoryRevenueTaskInfo }> = ({ taskInfo }) => {
  const [focus, setFocus] = useState(false);
  const [panelVisible, setPanelVisible] = useState(false);

  const handleMouseEnter = () => {
    if (panelVisible) {
      // 如果面板可见，则不处理
      return;
    }
    setFocus(true);
  };

  const handleMouseLeave = () => {
    if (panelVisible) {
      // 如果面板可见，则不处理
      return;
    }
    setFocus(false);
  };

  return (
    <div
      onMouseEnter={() => handleMouseEnter()}
      onMouseLeave={() => handleMouseLeave()}
      style={{
        width: '100%',
        height: '100%',
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        padding: 8,
      }}
    >
      <div
        style={{
          padding: 6,
          display: 'flex',
          alignItems: 'center',
          width: '100%',
          position: 'relative',
        }}
      >
        <div
          style={{
            width: '100%',
            height: '100%',
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
          }}
        >
          <Text link={{ href: taskInfo.meegoInfo.url, target: '_blank' }} ellipsis={true}>
            {taskInfo.meegoInfo.name}
          </Text>
          {taskInfo.terminated ? (
            <Tag color="grey" style={{ width: 56, marginLeft: '8px' }}>
              已终止
            </Tag>
          ) : (
            <></>
          )}
          {focus ? (
            <div style={{ float: 'right', height: '80%' }}>
              <ShowStoryRevenueInfoEditLogButton
                taskInfo={taskInfo}
                onVisibleChange={visible => {
                  setPanelVisible(visible);
                  setFocus(visible);
                }}
              />
            </div>
          ) : (
            <></>
          )}
        </div>
      </div>
    </div>
  );
};

export const EventResponseTagSelector: React.FC<{
  taskInfo: StoryRevenueTaskInfo;
  editable: boolean;
  options: { value: string; label: string; disabled?: boolean }[];
  defaultValue?: string[] | string;
  multi?: boolean;
  showClose?: boolean;
  keyPath?: string;
  columnConfig?: StoryRevenueTableColumnConfig;
  saveValue: (newValue: string[]) => Promise<void>;
}> = ({
  taskInfo,
  editable,
  options,
  defaultValue,
  multi = false,
  saveValue,
  showClose = true,
  keyPath,
  columnConfig,
}) => {
  const [selectState, setSelectState] = useState(false);
  const [focus, setFocus] = useState(false);
  const [values, setValues] = useState<string[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  useEffect(() => {
    if (Array.isArray(defaultValue)) {
      setValues(defaultValue);
    } else {
      setValues(defaultValue ? [defaultValue] : []);
    }
  }, [defaultValue]);

  const optionRenderList = options.map((it, index) => ({
    value: it.value,
    label: (
      <Tag
        color={it.disabled ? 'grey' : tagColorArray[index % tagColorArray.length]}
        style={{ maxWidth: '100%' }}
        size={'large'}
        key={it.value}
      >
        {it.label}
      </Tag>
    ),
    disabled: it.disabled !== undefined ? it.disabled : false,
  }));
  return (
    <div
      style={{
        width: '100%',
        height: '100%',
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        padding: 8,
        border: selectState ? '2px solid rgba(var(--semi-blue-7), 1)' : undefined,
      }}
      onMouseEnter={() => {
        setFocus(editable);
      }}
      onMouseLeave={() => {
        setFocus(false);
      }}
    >
      {selectState ? (
        <Select
          showClear={showClose}
          placeholder={'请选择'}
          optionList={optionRenderList}
          value={multi ? values : values[0]}
          multiple={multi}
          maxTagCount={2}
          expandRestTagsOnClick={true}
          showRestTagsPopover={true}
          style={{ width: '100%' }}
          onSelect={(value, option) => {
            if (multi) {
              return;
            }
            setValues(value ? [value as string] : []);
            setLoading(true);
            saveValue(value ? [value as string] : []).then(res => {
              setLoading(false);
            });
          }}
          onDropdownVisibleChange={visible => {
            if (!visible) {
              setSelectState(false);
              setFocus(false);
              if (multi) {
                setLoading(true);
                saveValue(values).then(res => {
                  setLoading(false);
                });
              }
            }
          }}
          onClear={() => {
            setValues([]);
            if (!multi) {
              setLoading(true);
              saveValue([]).then(res => {
                setLoading(false);
              });
            }
          }}
          onChange={value => {
            if (!multi) {
              return;
            }
            let newValue: string[] = [];
            if (Array.isArray(value)) {
              newValue = value as string[];
            } else {
              newValue = [value as string];
            }
            setValues(newValue);
          }}
          defaultOpen={true}
        />
      ) : (
        <div
          style={{
            padding: 6,
            display: 'flex',
            alignItems: 'center',
            width: '100%',
            position: 'relative',
          }}
        >
          <div
            style={{
              width: '100%',
            }}
          >
            <Spin spinning={loading}>
              <TagGroup
                maxTagCount={2}
                style={{ display: 'flex', alignItems: 'center', width: '100%' }}
                popoverProps={{ trigger: 'custom', visible: focus }}
                showPopover={true}
                tagList={values.map((op, index) => {
                  const valueIndex = options.findIndex(it => it.value === op);
                  return {
                    color: options[valueIndex]?.disabled ? 'grey' : tagColorArray[valueIndex % tagColorArray.length],
                    children: options[valueIndex]?.label,
                  };
                })}
              />
            </Spin>
          </div>
          <div
            style={{
              padding: 6,
              zIndex: 1,
              width: '100%',
              position: 'absolute',
            }}
          >
            {focus ? (
              <div style={{ float: 'right' }}>
                <Space>
                  {editable ? (
                    <Button
                      icon={<IconEditStroked />}
                      onClick={() => {
                        setSelectState(true);
                      }}
                      theme="outline"
                      style={{
                        float: 'right',
                        backgroundColor: 'rgba(var(--semi-grey-0), 1)',
                      }}
                    />
                  ) : (
                    <></>
                  )}
                  {loading ? <Spin spinning={loading} /> : <></>}
                </Space>
              </div>
            ) : (
              <></>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

const PrioritySelector: React.FC<{ defaultValue: string }> = ({ defaultValue }) => {
  const [selectState, setSelectState] = useState(false);
  const [focus, setFocus] = useState(false);
  const [selectedValue, setSelectedValue] = useState<string>('P2');
  useEffect(() => {
    setSelectedValue(defaultValue);
  }, [defaultValue]);
  const optionRenders = [
    {
      value: 'P00',
      label: (
        <Tag style={{ backgroundColor: 'rgba(var(--semi-red-5), 1)', color: 'rgba(var(--semi-grey-1), 1)' }}>P00</Tag>
      ),
    },
    {
      value: 'P0',
      label: <Tag color={'red'}>P0</Tag>,
    },
    {
      value: 'P1',
      label: <Tag color={'blue'}>P1</Tag>,
    },
    {
      value: 'P2',
      label: <Tag color={'green'}>P2</Tag>,
    },
  ];
  return optionRenders.find(it => it.value === defaultValue)?.label ?? <></>;
};

export const YesOrNoSelector: React.FC<{
  optionNames: string[];
  defaultValue?: string;
  saveValue: (newValue?: boolean) => Promise<void>;
  editable?: boolean;
  defaultIcon?: ReactNode;
  afterEditIcon?: ReactNode;
  yesColor?: TagColor;
  noColor?: TagColor;
}> = ({ optionNames, defaultValue, saveValue, editable = true, defaultIcon, afterEditIcon, yesColor, noColor }) => {
  const [selectState, setSelectState] = useState(false);
  const [focus, setFocus] = useState(false);
  const [selectedValue, setSelectedValue] = useState<string>();
  const [loading, setLoading] = useState(false);
  const [tagIcon, setTagIcon] = useState<ReactNode>();
  useEffect(() => {
    setSelectedValue(defaultValue);
  }, [defaultValue]);
  useEffect(() => {
    setTagIcon(defaultIcon);
  }, [defaultIcon]);
  const displayRenders = [
    {
      value: 'true',
      label: (
        <Tag color={yesColor ?? 'green'} prefixIcon={tagIcon}>
          {optionNames[0] ?? '有'}
        </Tag>
      ),
    },
    {
      value: 'false',
      label: (
        <Tag color={noColor ?? 'pink'} prefixIcon={tagIcon}>
          {optionNames[1] ?? '无'}
        </Tag>
      ),
    },
    {
      value: 'unknown',
      label: <Text style={{ padding: 10 }}> - </Text>,
    },
  ];
  const optionRenders = [
    {
      value: 'true',
      label: <Tag color={yesColor ?? 'green'}>{optionNames[0] ?? '有'}</Tag>,
    },
    {
      value: 'false',
      label: <Tag color={noColor ?? 'pink'}>{optionNames[1] ?? '无'}</Tag>,
    },
  ];
  return (
    <div
      style={{
        width: '100%',
        height: '100%',
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        padding: 8,
        border: selectState ? '2px solid rgba(var(--semi-blue-7), 1)' : undefined,
      }}
      onMouseEnter={() => {
        setFocus(editable);
      }}
      onMouseLeave={() => {
        setFocus(false);
      }}
    >
      {selectState && editable ? (
        <Select
          showClear={true}
          loading={loading}
          placeholder={'请选择'}
          optionList={optionRenders}
          onClear={() => {
            setSelectedValue(undefined);
          }}
          value={selectedValue}
          expandRestTagsOnClick={true}
          showRestTagsPopover={true}
          style={{ width: '100%' }}
          onDropdownVisibleChange={visible => {
            if (!visible) {
              setSelectState(false);
              setFocus(false);
              if (afterEditIcon) {
                setTagIcon(afterEditIcon);
              }
            }
          }}
          onChange={value => {
            if (value === undefined) {
              setSelectedValue(undefined);
              setLoading(true);
              saveValue(undefined).then(res => {
                setLoading(false);
              });
              setSelectState(false);
              setFocus(false);
            }
          }}
          onSelect={(value, option) => {
            setSelectedValue(value as string);
            setLoading(true);
            saveValue(value === 'true').then(res => {
              setLoading(false);
              setSelectState(false);
              setFocus(false);
            });
          }}
          defaultOpen={true}
        />
      ) : (
        <div
          style={{
            padding: 6,
            display: 'flex',
            alignItems: 'center',
            width: '100%',
            position: 'relative',
          }}
        >
          <div
            style={{
              maxWidth: '100%',
            }}
          >
            <Spin spinning={loading}>{displayRenders.find(it => it.value === selectedValue)?.label}</Spin>
          </div>
          <div
            style={{
              padding: 6,
              zIndex: 1,
              width: '100%',
              position: 'absolute',
            }}
          >
            {focus && editable ? (
              <Button
                icon={<IconEditStroked />}
                onClick={() => {
                  setSelectState(true);
                }}
                theme="outline"
                style={{
                  float: 'right',
                  backgroundColor: 'rgba(var(--semi-grey-0), 1)',
                }}
              />
            ) : (
              <></>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

const handleOpenLibraConclusionRule = (columnId: string) => {
  window.open(
    'https://bytedance.larkoffice.com/docx/TZSwdNbBNoQFcLxHtB1cklsCnyb#GLShdvGX4o8ALzxOIOxc0G7onBg',
    '_blank',
  );
};

// 是否应该显示帮助文档的图标
const shouldShowHelpDocIcon = (columnId: string) => {
  switch (columnId) {
    case 'has_platform_revenue':
      return true;
    case 'has_module_revenue':
      return true;
    case 'has_key_process_revenue':
      return true;
    default:
      return false;
  }
};

// 显示的 Tooltip 的标题
const helpDocIconTooltipTitle = (columnId: string) => {
  switch (columnId) {
    case 'has_platform_revenue':
      return '端收益指标计算规则';
    case 'has_module_revenue':
      return '模块收益指标计算规则';
    case 'has_key_process_revenue':
      return '关键过程收益指标计算规则';
    default:
      return '';
  }
};

export const RedMarkColumnTitle: React.FC<{ title: string; columnId: string; toolTip?: string }> = ({
  title,
  columnId,
  toolTip,
}) => (
  <div style={{ alignItems: 'start', alignContent: 'start', width: '100%' }}>
    <Space spacing={3}>
      <Text strong type={'tertiary'}>
        {title}
      </Text>
      <Text style={{ color: 'rgba(var(--semi-red-6), 1)' }}>*</Text>
      {shouldShowHelpDocIcon(columnId) && (
        <>
          <Tooltip content={helpDocIconTooltipTitle(columnId)}>
            <Button
              size="small"
              theme="borderless"
              icon={<IconInfoCircle style={{ color: 'rgba(var(--semi-grey-3), 1)' }} />}
              onClick={() => {
                handleOpenLibraConclusionRule(columnId);
              }}
            />
          </Tooltip>
        </>
      )}
      {toolTip && (
        <>
          <Tooltip content={toolTip}>
            <Button
              size="small"
              theme="borderless"
              icon={<IconInfoCircle style={{ color: 'rgba(var(--semi-grey-3), 1)' }} />}
            />
          </Tooltip>
        </>
      )}
    </Space>
  </div>
);

// 显示的 Popover 内容
const PopoverDisplayCustomContent = (columnId: string) => {
  switch (columnId) {
    case 'review_duration':
      return (
        <div style={{ padding: 12 }}>
          <Text style={{ lineHeight: '2' }}>
            <span style={{ color: 'rgba(var(--semi-red-6), 1)' }}>*</span>说明：
            <br />
            1. 若周期已锁定，Review时长 = 「锁定日期」-「详评完成日期」的自然天数
            <br />
            2. 若周期未锁定，Review时长 = 「今天日期」-「详评完成日期」的自然天数
          </Text>
        </div>
      );
    case 'algorithm_related':
      return (
        <div style={{ padding: 12 }}>
          <Text style={{ lineHeight: '2' }}>需求的落地是否和推荐搜索等合作</Text>
        </div>
      );
    default:
      return <></>;
  }
};

export const PopoverColumnTitle: React.FC<{ title: string; columnId: string }> = ({ title, columnId }) => (
  <div style={{ alignItems: 'start', alignContent: 'start', width: '100%' }}>
    <Space spacing={3}>
      <Text strong type={'tertiary'}>
        {title}
      </Text>
      {
        <>
          <Popover content={PopoverDisplayCustomContent(columnId)} position="top">
            <Button
              size="small"
              theme="borderless"
              icon={<IconInfoCircle style={{ color: 'rgba(var(--semi-grey-3), 1)' }} />}
            />
          </Popover>
        </>
      }
    </Space>
  </div>
);
const checkRevenueConclusionValid = (
  manuallyConclusion?: string,
  autoConclusion?: StoryRevenueTaskRevenueConclusionInfo[],
) => {
  const hasManuallyConclusion = (manuallyConclusion?.length ?? 0) > 0;
  const significanceInfos: StoryRevenueTaskRevenueSignificanceInfo[] = [];
  autoConclusion?.forEach(it => {
    it.significanceValues.forEach(it1 => {
      significanceInfos.push(it1);
    });
  });
  const hasAutoConclusion = significanceInfos.length > 0;
  return hasManuallyConclusion || hasAutoConclusion;
};
const getFillIncompleteReasons = (taskInfo: StoryRevenueTaskInfo): string[] => {
  const missingFields = [];

  // 需求名称校验
  if (!taskInfo.meegoInfo.name || taskInfo.meegoInfo.name.length === 0) {
    missingFields.push('需求名称');
  }

  // 需求Owner校验
  if (!taskInfo.meegoInfo.owners || taskInfo.meegoInfo.owners.length === 0) {
    missingFields.push('需求Owner');
  }

  // 业务线校验
  if (
    (!taskInfo.meegoInfo.primaryBusiness || taskInfo.meegoInfo.primaryBusiness.length === 0) &&
    (!taskInfo.meegoInfo.secondaryBusiness || taskInfo.meegoInfo.secondaryBusiness.length === 0)
  ) {
    missingFields.push('需求来源业务线/业务线');
  }

  // 优先级校验
  if (!taskInfo.meegoInfo.priority || taskInfo.meegoInfo.priority.length === 0) {
    missingFields.push('优先级');
  }

  // 收益评估类型校验
  if (taskInfo.revenueInfo?.reviewType === undefined || taskInfo.revenueInfo?.reviewType === null) {
    missingFields.push('是否参与季度需求收益评估');
  }

  // 无结论原因校验
  if (
    taskInfo.revenueInfo?.reviewType === StoryRevenueTaskRevenueReviewType.NotParticipateForNoConclusionYet &&
    !taskInfo.revenueInfo?.noConclusionReason
  ) {
    missingFields.push('无法回收结论的原因');
  }

  // 实验信息校验
  if (taskInfo.revenueInfo?.reviewType === StoryRevenueTaskRevenueReviewType.NotParticipateForNoConclusionYet) {
    if (!taskInfo.experimentInfo) {
      missingFields.push('关联实验链接');
    }
    if (taskInfo.experimentInfo?.subExperimentCount === 0) {
      missingFields.push('实验数量<1');
    }
  }

  // 不参与评估则无需继续校验
  if (taskInfo.revenueInfo?.reviewType !== StoryRevenueTaskRevenueReviewType.ParticipateInTheEvaluation) {
    taskInfo.fillInCompleted = missingFields.length === 0;
    return missingFields;
  }

  // 算法相关性校验
  let needAlgorithmRelated = false;
  if (taskInfo.meegoInfo.primaryBusiness === 'UG') {
    needAlgorithmRelated = true;
  }
  if (taskInfo.meegoInfo.primaryBusiness === '内容生态' && taskInfo.meegoInfo.secondaryBusiness === '模板框架与分发') {
    needAlgorithmRelated = true;
  }

  if (
    needAlgorithmRelated &&
    (taskInfo.meegoInfo.algorithmRelated === undefined || taskInfo.meegoInfo.algorithmRelated === null)
  ) {
    missingFields.push('是否和算法相关');
  }

  // 收益期望类型校验
  if (taskInfo.revenueInfo.expectationType === undefined || taskInfo.revenueInfo.expectationType === null) {
    missingFields.push('收益自评');
  }

  // 端收益校验
  if (
    (taskInfo.revenueInfo.hasPlatformRevenue === undefined || taskInfo.revenueInfo.hasPlatformRevenue === null) &&
    (taskInfo.revenueInfo.manullyHasPlatformRevenue === undefined ||
      taskInfo.revenueInfo.manullyHasPlatformRevenue === null)
  ) {
    missingFields.push('有端收益(PM)');
  }

  // 模块收益校验
  if (
    (taskInfo.revenueInfo.hasModuleRevenue === undefined || taskInfo.revenueInfo.hasModuleRevenue === null) &&
    (taskInfo.revenueInfo.manullyHasModuleRevenue === undefined ||
      taskInfo.revenueInfo.manullyHasModuleRevenue === null)
  ) {
    missingFields.push('有模块收益(PM)');
  }

  // 关键过程收益校验
  if (
    (taskInfo.revenueInfo.hasKeyProcessRevenue === undefined || taskInfo.revenueInfo.hasKeyProcessRevenue === null) &&
    (taskInfo.revenueInfo.manullyHasKeyProcessRevenue === undefined ||
      taskInfo.revenueInfo.manullyHasKeyProcessRevenue === null)
  ) {
    missingFields.push('有关键过程收益(PM)');
  }

  // 指标损失校验
  if (
    (taskInfo.revenueInfo.hasMetricLoss === undefined || taskInfo.revenueInfo.hasMetricLoss === null) &&
    (taskInfo.revenueInfo.manuallyHasMetricLoss === undefined || taskInfo.revenueInfo.manuallyHasMetricLoss === null)
  ) {
    missingFields.push('有指标损失');
  }

  // 推全状态校验
  if (taskInfo.revenueInfo.isFullyRelease === undefined || taskInfo.revenueInfo.isFullyRelease === null) {
    missingFields.push('本次是否计划上线/推全');
  }

  // 收益自评校验
  if (taskInfo.revenueInfo.revenueSelfAssessment === undefined || taskInfo.revenueInfo.revenueSelfAssessment === null) {
    missingFields.push('收益自评');
  }

  // 自评原因校验 (根据需求添加)
  if (taskInfo.revenueInfo.revenueSelfAssessment === undefined || taskInfo.revenueInfo.revenueSelfAssessment === null) {
    missingFields.push('自评原因');
  }

  // 端收益指标详情校验 (根据需求添加)
  if (
    (taskInfo.revenueInfo.hasPlatformRevenue || taskInfo.revenueInfo.manullyHasPlatformRevenue) &&
    !checkRevenueConclusionValid(
      taskInfo.revenueInfo?.platformRevenueMetricConclusion,
      taskInfo.revenueInfo?.platformRevenueMetricAutoComputedConclusion,
    )
  ) {
    missingFields.push('端收益指标详情');
  }

  // 模块收益指标详情校验 (根据需求添加)
  if (
    (taskInfo.revenueInfo.hasModuleRevenue || taskInfo.revenueInfo.manullyHasModuleRevenue) &&
    !checkRevenueConclusionValid(
      taskInfo.revenueInfo?.moduleRevenueMetricConclusion,
      taskInfo.revenueInfo?.moduleRevenueMetricAutoComputedConclusion,
    )
  ) {
    missingFields.push('模块收益指标详情');
  }

  // 过程指标收益详情校验 (根据需求添加)
  if (
    (taskInfo.revenueInfo.hasKeyProcessRevenue || taskInfo.revenueInfo.manullyHasKeyProcessRevenue) &&
    !checkRevenueConclusionValid(
      taskInfo.revenueInfo?.keyProcessRevenueMetricConclusion,
      taskInfo.revenueInfo?.keyProcessRevenueMetricAutoComputedConclusion,
    )
  ) {
    missingFields.push('过程指标收益详情');
  }

  return missingFields;
};
export const getReviewTableColumnObject = (
  columnId: string,
  config: StoryRevenueTableColumnConfig,
  userRoleType: StoryRevenueRoleType,
  onEditTaskInfo: (taskInfo: StoryRevenueTaskInfo, keyPath: string, newValue: any) => Promise<boolean>,
  updateTaskInfo: (newInfo: StoryRevenueTaskInfo) => void,
  openTaskInfoForm: (taskInfo: StoryRevenueTaskInfo) => void,
  allPeriodInfo?: StoryRevenueReviewPeriodInfo[],
): ColumnProps<StoryRevenueTaskInfo> | undefined => {
  const isTaskInfoEditable = (taskInfo: StoryRevenueTaskInfo) => {
    if (!allPeriodInfo) {
      return false;
    }
    for (const period of allPeriodInfo) {
      if (taskInfo.reviewPeriodId === period.reviewPeriodId) {
        return taskInfoEditable(taskInfo, userRoleType, period);
      }
    }
    return false;
  };
  const isRoleEditable = (taskInfo: StoryRevenueTaskInfo) => {
    if (taskInfo.updating) {
      return false;
    }
    const selectedPeriod = allPeriodInfo?.find(it => it.reviewPeriodId === taskInfo.reviewPeriodId);
    if (!selectedPeriod) {
      return false;
    }
    if (selectedPeriod.updating) {
      // 整个 Review 周期正在更新中，不允许编辑
      return false;
    }

    if (selectedPeriod.isSyncingDataToMeego) {
      // 如果数据正在回写到 Meego 中，则不允许编辑
      return false;
    }

    if (selectedPeriod.dsLocked) {
      // 如果数据已存档，则不允许编辑（DS 和管理员也不行）
      return false;
    }
    if (userRoleType === StoryRevenueRoleType.DataAnalyst || userRoleType === StoryRevenueRoleType.Admin) {
      return true;
    }
    return false;
  };
  const columnOnjectMap: { [key: string]: ColumnProps<StoryRevenueTaskInfo> } = {
    feature_name: {
      ellipsis: true,
      title: '需求名称',
      width: 300,
      fixed: true,
      colSpan: 2,
      dataIndex: 'meegoInfo.name',
      onCell: (record, rowIndex) => {
        if (record?.updating) {
          return {
            style: {
              background: 'rgba(var(--semi-light-green-0), 0.75)',
            },
          };
        }
        return {};
      },
      render: (text, record, index, options) => {
        if (!record.meegoInfo) {
          return <></>;
        }
        return (
          <div
            style={{
              maxWidth: '100%',
            }}
          >
            {record.updating ? (
              <div
                style={{
                  position: 'absolute',
                  top: 0,
                  left: 0,
                }}
              >
                <Tag size="small" color="light-green">
                  更新中
                </Tag>
              </div>
            ) : (
              <></>
            )}
            <Text link onClick={() => openTaskInfoForm(record)} ellipsis={true}>
              {record.meegoInfo.name}
            </Text>
            {record.terminated ? (
              <Tag color="grey" style={{ width: 56, marginLeft: '8px' }}>
                已终止
              </Tag>
            ) : (
              <></>
            )}
          </div>
        );
      },
    },
    operation: {
      ellipsis: true,
      title: '操作',
      width: 120,
      resize: false,
      fixed: true,
      colSpan: 0,
      dataIndex: 'operation',
      onCell: (record, rowIndex) => {
        if (record?.updating) {
          return {
            style: {
              background: 'rgba(var(--semi-light-green-0), 0.75)',
            },
          };
        }
        return {};
      },
      render: (text, record, index) => {
        const selectedPeriod = allPeriodInfo?.find(it => it.reviewPeriodId === record.reviewPeriodId);
        return <FeatureOperationCell taskInfo={record} periodInfo={selectedPeriod} onTaskInfoUpdate={updateTaskInfo} />;
      },
    },
    no_conclusion_reason: {
      width: 200,
      dataIndex: 'revenueInfo.noConclusionReason',
      onCell: (record, rowIndex) => ({
        style: { width: '100%', height: 50, padding: 0 },
      }),
      render: (text, record, index) => {
        if (record.revenueInfo?.reviewType !== StoryRevenueTaskRevenueReviewType.NotParticipateForNoConclusionYet) {
          return <StoryRevenueSelfAssementEmptyCell hoverText={'无需填写'} />;
        }
        return (
          <TableTextEditCell
            onTextUpdate={async newText => {
              await onEditTaskInfo(record, 'revenueInfo.noConclusionReason', newText);
            }}
            cellTitle={'结论无法回收的原因'}
            initText={record.revenueInfo?.noConclusionReason}
            editable={isTaskInfoEditable(record)}
            placeholder={'原因如：实验开启时间短未到回收周期、人力待排期、反转实验、需求pending、待交接'}
          />
        );
      },
    },
    single_platform_reason: {
      width: 200,
      dataIndex: 'meegoInfo.singleAppReason',
      onCell: (record, rowIndex) => ({
        style: { width: '100%', height: 50, padding: 0 },
      }),
      render: (text, record, index) => {
        if (record.meegoInfo.publishedApps.length > 1) {
          return <StoryRevenueSelfAssementEmptyCell hoverText={'无需填写'} />;
        }
        return (
          <TableTextEditCell
            onTextUpdate={async newText => {
              await onEditTaskInfo(record, 'meegoInfo.singleAppReason', newText);
            }}
            cellTitle={'仅上单端的原因'}
            initText={record.meegoInfo.singleAppReason}
            editable={isTaskInfoEditable(record)}
          />
        );
      },
    },
    platform_revenue_has_diff: {
      width: 200,
      dataIndex: 'revenueInfo.platformRevenueHasDiff',
      onCell: (record, rowIndex) => ({
        style: { width: '100%', height: 50, padding: 0 },
      }),
      render: (text, record) => (
        <YesOrNoSelector
          editable={false}
          saveValue={async newValue => {
            if (!record.revenueInfo) {
              return;
            }
            const res = await onEditTaskInfo(record, 'revenueInfo.dsHasPlatformRevenue', newValue);
            if (res) {
              record.revenueInfo.dsHasPlatformRevenue = newValue;
            }
          }}
          defaultValue={
            record.revenueInfo?.platformRevenueHasDiff === undefined
              ? undefined
              : record.revenueInfo?.platformRevenueHasDiff
                ? 'true'
                : 'false'
          }
          optionNames={['是', '否']}
        />
      ),
    },
    algorithm_related: {
      width: 200,
      dataIndex: 'meegoInfo.algorithmRelated',
      onCell: (record, rowIndex) => ({
        style: { width: '100%', height: 50, padding: 0 },
      }),
      render: (text, record, index) => {
        const editValue =
          record.meegoInfo.algorithmRelated === undefined
            ? undefined
            : record.meegoInfo.algorithmRelated
              ? 'true'
              : 'false';
        const editable = isTaskInfoEditable(record);

        if (record.revenueInfo?.reviewType !== StoryRevenueTaskRevenueReviewType.ParticipateInTheEvaluation) {
          return <StoryRevenueSelfAssementEmptyCell hoverText={'不参与Review，无法编辑'} />;
        }
        let need = false;
        if (record.meegoInfo.primaryBusiness === 'UG') {
          need = true;
        }
        if (
          record.meegoInfo.primaryBusiness === '内容生态' &&
          record.meegoInfo.secondaryBusiness === '模板框架与分发'
        ) {
          need = true;
        }
        if (!need) {
          return <StoryRevenueSelfAssementEmptyCell hoverText={'无需填写'} />;
        }
        return (
          <YesOrNoSelector
            editable={editable}
            saveValue={async newValue => {
              if (!record.revenueInfo) {
                return;
              }
              const res = await onEditTaskInfo(record, 'meegoInfo.algorithmRelated', newValue);
            }}
            defaultValue={editValue}
            optionNames={['是', '否']}
          />
        );
      },
    },
    has_metric_loss: {
      width: 150,
      dataIndex: 'revenueInfo.hasMetricLoss',
      onCell: (record, rowIndex) => ({
        style: { width: '100%', height: 50, padding: 0 },
      }),
      render: (text, record, index) => {
        const editValue =
          record.revenueInfo?.manuallyHasMetricLoss !== undefined && record.revenueInfo?.manuallyHasMetricLoss !== null
            ? record.revenueInfo.manuallyHasMetricLoss
              ? 'true'
              : 'false'
            : undefined;
        const cloudValue =
          record.revenueInfo?.hasMetricLoss !== undefined && record.revenueInfo?.hasMetricLoss !== null
            ? record.revenueInfo.hasMetricLoss
              ? 'true'
              : 'false'
            : undefined;
        const editable =
          ((cloudValue === undefined || cloudValue === null) && isTaskInfoEditable(record)) || isRoleEditable(record);
        if (
          (cloudValue === undefined || cloudValue === null) &&
          record.revenueInfo?.reviewType !== StoryRevenueTaskRevenueReviewType.ParticipateInTheEvaluation
        ) {
          return <StoryRevenueSelfAssementEmptyCell hoverText={'不参与Review，无法编辑'} />;
        }
        return (
          <FeatureRevenueSelector
            taskInfo={record}
            editable={editable}
            saveValue={async newValue => {
              if (!record.revenueInfo) {
                return;
              }
              const res = await onEditTaskInfo(record, 'revenueInfo.manuallyHasMetricLoss', newValue);
              if (res) {
                record.revenueInfo.hasMetricLoss = newValue;
              }
            }}
            autoRes={cloudValue}
            manuallyRes={editValue}
            yesColor={'pink'}
            noColor={'green'}
          />
        );
      },
    },
    revenue_review_doc: {
      ellipsis: true,
      width: 200,
      dataIndex: 'revenueInfo.reviewDocUrl',
      onCell: (record, rowIndex) => ({
        style: { width: '100%', height: 50, padding: 0 },
      }),
      render: (text, record, index) => (
        <RevenueReviewDocCell
          taskInfo={record}
          editable={true}
          onTextUpdate={async newText => {
            await onEditTaskInfo(record, 'revenueInfo.reviewDocUrl', newText);
          }}
        />
      ),
    },
    feature_owner: {
      ellipsis: true,
      width: 130,
      dataIndex: 'meegoInfo.owner.name',
      render: (text, record, index, options) => {
        if (!record.meegoInfo || record.meegoInfo.owners.length === 0) {
          return <></>;
        }
        if (record.meegoInfo.owners.length === 1) {
          const owner = record.meegoInfo.owners[0];
          return (
            <UserCard
              email={owner?.email}
              simpleUserData={{
                avatarUrl: typeof owner?.avatar === 'string' ? owner?.avatar : owner?.avatar?.avatar_240,
                name: owner?.name,
              }}
              triggerType="hover"
            />
          );
        } else {
          return (
            <div style={{ display: 'flex', justifyContent: 'space-between' }}>
              <SemiReactUserGroup
                users={StoryRevenueConvertCopyUserToUser(record.meegoInfo.owners)}
                triggerType={'hover'}
              />
              {/* {owners.map((user, index) => UserAvatar({ checkUser: user }))} */}
            </div>
          );
        }
      },
    },
    is_match_expected: {
      width: 150,
      dataIndex: 'revenueInfo.expectationType',
      onCell: (record, rowIndex) => ({
        style: { width: '100%', height: 50, padding: 0 },
      }),
      render: (text, record, index) => {
        if (!record.revenueInfo) {
          return <></>;
        }
        if (record.revenueInfo.reviewType !== StoryRevenueTaskRevenueReviewType.ParticipateInTheEvaluation) {
          return <StoryRevenueSelfAssementEmptyCell hoverText={'不参与Review，无法编辑'} />;
        }
        return (
          <RevenueExpectionSelector
            taskInfo={record}
            saveValue={async newValue => {
              if (!record.revenueInfo) {
                return;
              }
              if (record.revenueInfo.expectationType === newValue) {
                return;
              }
              const res = await onEditTaskInfo(record, 'revenueInfo.expectationType', newValue);
              if (res && newValue) {
                record.revenueInfo.expectationType = newValue;
                record.revenueInfo.revenueSelfAssessment = undefined;
              }
            }}
            editable={isTaskInfoEditable(record)}
          />
        );
      },
    },
    task_id: {
      width: 100,
      dataIndex: '_id',
      render: (text, record, index) => <Text>{record._id ?? ''}</Text>,
    },
    task_terminated: {
      width: 100,
      dataIndex: 'terminated',
      render: (text, record, index) => {
        if (record.terminated) {
          return <Tag color={'grey'}>是</Tag>;
        } else {
          return <Tag color={'indigo'}>否</Tag>;
        }
      },
    },
    task_version: {
      width: 150,
      dataIndex: 'reviewPeriodId',
      onCell: (record, rowIndex) => ({
        style: { width: '100%', height: 50, padding: 0 },
      }),
      render: (text, record, index) => {
        const selectedPeriod = allPeriodInfo?.find(it => it.reviewPeriodId === record.reviewPeriodId);
        const options = allPeriodInfo?.map(it => ({ value: it.reviewPeriodId, label: it.reviewPeriodName }));
        return (
          <div style={{ width: '100%', height: '100%' }}>
            <EventResponseTagSelector
              showClose={false}
              taskInfo={record}
              editable={isTaskInfoEditable(record)}
              options={options ?? []}
              defaultValue={selectedPeriod?.reviewPeriodName}
              saveValue={async newValue => {
                if (record.reviewPeriodId === newValue[0]) {
                  return;
                }
                const res = await onEditTaskInfo(record, 'reviewPeriodId', newValue);
                if (res && newValue[0]) {
                  record.reviewPeriodId = newValue[0];
                }
              }}
            />
          </div>
        );
      },
    },
    review_duration: {
      width: 150,
      dataIndex: 'reviewDuration',
      render: (text, record, index) => <Text strong>{`${record.reviewDuration}天`}</Text>,
    },
    task_integrity: {
      width: 150,
      dataIndex: 'fillInCompleted',
      render: (text, record, index) => {
        if (record.fillInCompleted) {
          return (
            <div
              style={{
                width: '100%',
                height: '100%',
                paddingLeft: 24,
                paddingRight: 12,
                alignItems: 'center',
                display: 'flex',
              }}
            >
              <Tag color={'green'}>完整</Tag>
            </div>
          );
        } else {
          return (
            <>
              <FillInCompletedCell hoverText={getFillIncompleteReasons(record)} />
            </>
          );
        }
      },
    },
    ds_task_integrity: {
      width: 150,
      dataIndex: 'revenueInfoCompleted',
      render: (text, record, index) => {
        if (record.revenueInfoCompleted === undefined) {
          return <></>;
        }
        if (record.revenueInfoCompleted) {
          return <Tag color={'green'}>完整</Tag>;
        } else if (!record.revenueInfoCompleted) {
          return <Tag color={'red'}>不完整</Tag>;
        }
      },
    },
    feature_creator: {
      width: 130,
      dataIndex: 'meegoInfo.creator',
      render: (text, record, index) => {
        const creator = record.meegoInfo?.creator;
        if (!creator) {
          return <></>;
        }
        return (
          <UserCard
            email={creator.email}
            simpleUserData={{
              avatarUrl: typeof creator.avatar === 'string' ? creator.avatar : creator.avatar?.avatar_240,
              name: creator.name,
            }}
            triggerType="hover"
          />
        );
      },
    },
    source_business: {
      width: 150,
      dataIndex: 'meegoInfo.sourceBusiness',
      useFullRender: true,
      render: (text, record, index) => {
        let sourceBusiness = '';
        if (record.meegoInfo?.sourceBusiness && record.meegoInfo?.sourceBusiness.length > 0) {
          const primary = record.meegoInfo?.sourceBusiness[0].primary;
          const secondary = record.meegoInfo?.sourceBusiness[0].secondary;
          if (primary.length > 0) {
            sourceBusiness += primary;
            if (secondary.length > 0) {
              sourceBusiness += `/${secondary}`;
            }
          } else {
            sourceBusiness += secondary.length > 0 ? secondary : '';
          }
        }
        if (sourceBusiness.length === 0) {
          return <></>;
        }
        return <TagArray tagNames={[sourceBusiness]} />;
      },
    },
    primary_business: {
      width: 150,
      dataIndex: 'meegoInfo.primaryBusiness',
      useFullRender: true,
      render: (text, record, index) => {
        let businessName = '';
        if (record.meegoInfo?.primaryBusiness) {
          businessName += record.meegoInfo?.primaryBusiness;
          if (record.meegoInfo?.secondaryBusiness) {
            businessName += `/${record.meegoInfo?.secondaryBusiness}`;
          }
        } else {
          businessName += record.meegoInfo?.secondaryBusiness ?? '';
        }
        return <TagArray tagNames={[businessName]} />;
      },
    },
    biz_tag: {
      width: 150,
      dataIndex: 'meegoInfo.bizTag',
      render: (text, record, index) => {
        if (record.meegoInfo?.bizTag) {
          const tagNames = [];
          for (const tag of record.meegoInfo.bizTag) {
            let tagName = tag.primary;
            if (tagName.length === 0) {
              continue;
            }
            if (tag.secondary.length > 0) {
              tagName += `-${tag.secondary}`;
            }
            tagNames.push(tagName);
          }
          if (tagNames.length === 0) {
            return <></>;
          }
          return <TagArray tagNames={tagNames} />;
        }
        return <></>;
      },
    },
    sub_modules: {
      width: 180,
      dataIndex: 'meegoInfo.submodules',
      onCell: (record, rowIndex) => ({
        style: { width: '100%', height: 50, padding: 0 },
      }),
      render: (text, record, index) => {
        const allModules = getMeegoFieldValueAndLabel('field_2a112b');
        const options = allModules.map(it => ({
          value: it.label,
          label: it.label,
        }));
        return (
          <div style={{ width: '100%', height: '100%' }}>
            <EventResponseTagSelector
              taskInfo={record}
              editable={isTaskInfoEditable(record)}
              options={options}
              multi={true}
              defaultValue={record.meegoInfo?.submodules}
              saveValue={async newValue => {
                const res = await onEditTaskInfo(record, 'meegoInfo.submodules', newValue);
                if (res) {
                  record.meegoInfo.submodules = newValue;
                }
              }}
            />
          </div>
        );
      },
    },
    feature_priority: {
      ellipsis: true,
      width: 120,
      dataIndex: 'meegoInfo.priority',
      render: (text, record, index) => <PrioritySelector defaultValue={record.meegoInfo?.priority ?? 'P00'} />,
    },
    published_regions: {
      width: 200,
      dataIndex: 'meegoInfo.publishedRegions',
      render: (text, record, index) => <TagArray tagNames={record.meegoInfo?.publishedRegions ?? []} />,
    },
    published_apps: {
      width: 200,
      dataIndex: 'meegoInfo.publishedApps',
      render: (text, record, index) => <TagArray tagNames={record.meegoInfo?.publishedApps ?? []} />,
    },
    feature_doc: {
      width: 300,
      dataIndex: 'meegoInfo.prdUrl',
      render: (text, record, index) => {
        if (!record.meegoInfo) {
          return <></>;
        }
        return (
          <Text link={{ href: record.meegoInfo?.prdUrl, target: '_blank' }}>{`需求文档-${record.meegoInfo.name}`}</Text>
        );
      },
    },
    rd_work_load: {
      width: 100,
      dataIndex: 'meegoInfo.rdWorkload',
      render: (text, record, index) => <Text>{`${record.meegoInfo?.rdWorkload ?? 0}人天`}</Text>,
    },
    related_rd_teams: {
      width: 130,
      dataIndex: 'meegoInfo.relatedRDTeams',
      render: (text, record, index) => <TagArray tagNames={record.meegoInfo?.relatedRDTeams ?? []} />,
    },
    tech_owner: {
      width: 130,
      dataIndex: 'meegoInfo.techOwners',
      render: (text, record, index, options) => {
        if (!record.meegoInfo || record.meegoInfo.techOwners.length === 0) {
          return <></>;
        }
        if (record.meegoInfo.techOwners.length === 1) {
          const owner = record.meegoInfo.techOwners[0];
          return (
            <UserCard
              email={owner?.email}
              simpleUserData={{
                avatarUrl: typeof owner?.avatar === 'string' ? owner?.avatar : owner?.avatar?.avatar_240,
                name: owner?.name,
              }}
              triggerType="hover"
            />
          );
        } else {
          return (
            <div style={{ display: 'flex', justifyContent: 'space-between' }}>
              <SemiReactUserGroup
                users={StoryRevenueConvertCopyUserToUser(record.meegoInfo.techOwners)}
                triggerType={'hover'}
              />
            </div>
          );
        }
      },
    },
    feature_status: {
      width: 150,
      dataIndex: 'meegoInfo.status',
      render: (text, record, index) => <TagArray tagNames={[record.meegoInfo?.status ?? '']} />,
    },
    test_finished_time: {
      width: 100,
      dataIndex: 'meegoInfo.testFinishedTime',
      render: (text, record, index) => {
        if (!record.meegoInfo) {
          return <></>;
        }
        const date = new Date(record.meegoInfo.testFinishedTime);
        const dateString = date.toLocaleDateString('zh-CN', {
          year: 'numeric',
          month: '2-digit',
          day: '2-digit',
          timeZone: 'Asia/Shanghai',
        });
        return <Text>{dateString}</Text>;
      },
    },
    review_finished_time: {
      width: 100,
      dataIndex: 'meegoInfo.internalReviewFinishedTime',
      render: (text, record, index) => {
        if (!record.meegoInfo) {
          return <></>;
        }
        const date = new Date(record.meegoInfo.internalReviewFinishedTime);
        const dateString = date.toLocaleDateString('zh-CN', {
          year: 'numeric',
          month: '2-digit',
          day: '2-digit',
          timeZone: 'Asia/Shanghai',
        });
        return <Text>{dateString}</Text>;
      },
    },
    meego_id: {
      width: 50,
      dataIndex: 'meegoInfo.id',
      render: (text, record, index) => <Text>{record.meegoInfo?.id ?? ''}</Text>,
    },
    is_review_case: {
      width: 250,
      onCell: (record, rowIndex) => ({
        style: { width: '100%', height: 50, padding: 0 },
      }),
      dataIndex: 'revenueInfo.reviewType',
      render: (text, record, index) => {
        const reviewTypeMap: Record<string, string> = {};
        reviewTypeMap[StoryRevenueTaskRevenueReviewType.ParticipateInTheEvaluation.toString()] =
          '参与评估（能AA/AB严谨量化表现）';
        reviewTypeMap[StoryRevenueTaskRevenueReviewType.NotParticipateForExperimentNotOpen.toString()] =
          '不参与-优化迭代但未开实验';
        reviewTypeMap[StoryRevenueTaskRevenueReviewType.NotParticipateForNoConclusionYet.toString()] =
          '不参与-已开实验但尚无结论';
        reviewTypeMap[StoryRevenueTaskRevenueReviewType.NotParticipateForDataInfrastructure.toString()] =
          '不参与-数据基建/产品基建';
        reviewTypeMap[StoryRevenueTaskRevenueReviewType.NotParticipateForStoryUnderDevelopment.toString()] =
          '不参与-需求开发中';
        reviewTypeMap[StoryRevenueTaskRevenueReviewType.NotParticipateForStoryIsPending.toString()] =
          '不参与-需求pending';
        reviewTypeMap[StoryRevenueTaskRevenueReviewType.NotParticipateForLongTermCapacityBuilding.toString()] =
          '不参与-长期建设能力（后续需要参与评估）';
        reviewTypeMap[
          StoryRevenueTaskRevenueReviewType.NotParticipateForNegativeIssueGovernanceAndExperienceOpt.toString()
        ] = '不参与-负向问题治理&体验优化';
        reviewTypeMap[StoryRevenueTaskRevenueReviewType.NotParticipateForBugFix.toString()] = '不参与-bug修复[废弃]';
        reviewTypeMap[StoryRevenueTaskRevenueReviewType.NotParticipateForSecurityCompliance.toString()] =
          '不参与-安全合规降负反馈类[废弃]';
        const optionList = Object.keys(reviewTypeMap).map(it => ({
          value: it,
          label: reviewTypeMap[Number(it)],
          disabled:
            Number(it) === StoryRevenueTaskRevenueReviewType.NotParticipateForBugFix ||
            Number(it) === StoryRevenueTaskRevenueReviewType.NotParticipateForSecurityCompliance,
        }));
        // 使用 sort() 方法排序
        optionList.sort((a, b) => {
          if (a.disabled && !b.disabled) {
            return 1; // 将 disabled 为 true 的排在后面
          } else if (!a.disabled && b.disabled) {
            return -1; // 将 disabled 为 false 的排在前面
          } else {
            return 0; // 保持原有顺序
          }
        });
        return (
          <div style={{ width: '100%', height: '100%' }}>
            <EventResponseTagSelector
              taskInfo={record}
              editable={isTaskInfoEditable(record)}
              options={optionList}
              defaultValue={
                record.revenueInfo?.reviewType !== undefined && record.revenueInfo?.reviewType !== null
                  ? record.revenueInfo?.reviewType.toString()
                  : undefined
              }
              saveValue={async newValue => {
                if (!record.revenueInfo) {
                  return;
                }
                const res = await onEditTaskInfo(record, 'revenueInfo.reviewType', Number(newValue[0]));
                if (res && newValue[0]) {
                  record.revenueInfo.reviewType = Number(newValue[0]);
                }
                if (
                  newValue[0] === StoryRevenueTaskRevenueReviewType.ParticipateInTheEvaluation.toString() ||
                  newValue[0] === StoryRevenueTaskRevenueReviewType.NotParticipateForNoConclusionYet.toString()
                ) {
                  openTaskInfoForm(record);
                  Toast.info('请填写完整信息');
                }
              }}
            />
          </div>
        );
      },
    },
    has_platform_revenue: {
      width: 150,
      dataIndex: 'revenueInfo.hasPlatformRevenue',
      onCell: (record, rowIndex) => ({
        style: { width: '100%', height: 50, padding: 0 },
      }),
      render: (text, record, index) => {
        const editValue =
          record.revenueInfo?.manullyHasPlatformRevenue !== undefined &&
          record.revenueInfo?.manullyHasPlatformRevenue !== null
            ? record.revenueInfo.manullyHasPlatformRevenue
              ? 'true'
              : 'false'
            : undefined;
        const cloudValue =
          record.revenueInfo?.hasPlatformRevenue !== undefined && record.revenueInfo?.hasPlatformRevenue !== null
            ? record.revenueInfo.hasPlatformRevenue
              ? 'true'
              : 'false'
            : undefined;
        const editable =
          (((cloudValue === undefined || cloudValue === null) && isTaskInfoEditable(record)) ||
            isRoleEditable(record)) &&
          !record.updating;
        if (
          (cloudValue === undefined || cloudValue === null) &&
          record.revenueInfo?.reviewType !== StoryRevenueTaskRevenueReviewType.ParticipateInTheEvaluation
        ) {
          return <StoryRevenueSelfAssementEmptyCell hoverText={'不参与Review，无法编辑'} />;
        }
        return (
          <FeatureRevenueSelector
            taskInfo={record}
            editable={editable}
            saveValue={async newValue => {
              if (!record.revenueInfo) {
                return;
              }
              const res = await onEditTaskInfo(record, 'revenueInfo.manullyHasPlatformRevenue', newValue);
              if (res) {
                record.revenueInfo.hasPlatformRevenue = newValue;
              }
            }}
            autoRes={cloudValue}
            manuallyRes={editValue}
          />
        );
      },
    },
    ds_has_platform_revenue: {
      width: 150,
      dataIndex: 'revenueInfo.dsHasPlatformRevenue',
      onCell: (record, rowIndex) => ({
        style: { width: '100%', height: 50, padding: 0, background: 'rgba(var(--semi-light-blue-0), 0.75)' },
      }),
      onHeaderCell: (record, rowIndex) => ({
        style: { background: 'rgba(var(--semi-light-blue-0), 0.75)' },
      }),
      render: (text, record, index) => {
        const editValue =
          record.revenueInfo?.dsHasPlatformRevenue !== undefined && record.revenueInfo?.dsHasPlatformRevenue !== null
            ? record.revenueInfo.dsHasPlatformRevenue
              ? 'true'
              : 'false'
            : undefined;
        const editable = isRoleEditable(record);

        if (record.revenueInfo?.reviewType !== StoryRevenueTaskRevenueReviewType.ParticipateInTheEvaluation) {
          return <StoryRevenueSelfAssementEmptyCell hoverText={'不参与Review，无法编辑'} />;
        }
        return (
          <YesOrNoSelector
            editable={false}
            saveValue={async newValue => {
              if (!record.revenueInfo) {
                return;
              }
              const res = await onEditTaskInfo(record, 'revenueInfo.dsHasPlatformRevenue', newValue);
              if (res) {
                record.revenueInfo.dsHasPlatformRevenue = newValue;
              }
            }}
            defaultValue={editValue}
            optionNames={['有', '无']}
          />
        );
      },
    },
    has_module_revenue: {
      width: 160,
      dataIndex: 'revenueInfo.hasModuleRevenue',
      onCell: (record, rowIndex) => ({
        style: { width: '100%', height: 50, padding: 0 },
      }),
      render: (text, record, index) => {
        const editValue =
          record.revenueInfo?.manullyHasModuleRevenue !== undefined &&
          record.revenueInfo?.manullyHasModuleRevenue !== null
            ? record.revenueInfo.manullyHasModuleRevenue
              ? 'true'
              : 'false'
            : undefined;
        const cloudValue =
          record.revenueInfo?.hasModuleRevenue !== undefined && record.revenueInfo?.hasModuleRevenue !== null
            ? record.revenueInfo.hasModuleRevenue
              ? 'true'
              : 'false'
            : undefined;
        const editable =
          (((cloudValue === undefined || cloudValue === null) && isTaskInfoEditable(record)) ||
            isRoleEditable(record)) &&
          !record.updating;
        if (
          (cloudValue === undefined || cloudValue === null) &&
          record.revenueInfo?.reviewType !== StoryRevenueTaskRevenueReviewType.ParticipateInTheEvaluation
        ) {
          return <StoryRevenueSelfAssementEmptyCell hoverText={'不参与Review，无法编辑'} />;
        }
        return (
          <FeatureRevenueSelector
            taskInfo={record}
            editable={editable}
            saveValue={async newValue => {
              if (!record.revenueInfo) {
                return;
              }
              const res = await onEditTaskInfo(record, 'revenueInfo.manullyHasModuleRevenue', newValue);
              if (res) {
                record.revenueInfo.hasPlatformRevenue = newValue;
              }
            }}
            autoRes={cloudValue}
            manuallyRes={editValue}
          />
        );
      },
    },
    ds_has_module_revenue: {
      width: 150,
      dataIndex: 'revenueInfo.dsHasModuleRevenue',
      onCell: (record, rowIndex) => ({
        style: { width: '100%', height: 50, padding: 0, background: 'rgba(var(--semi-light-blue-0), 0.75)' },
      }),
      onHeaderCell: (record, rowIndex) => ({
        style: { background: 'rgba(var(--semi-light-blue-0), 0.75)' },
      }),
      render: (text, record, index) => {
        const editValue =
          record.revenueInfo?.dsHasModuleRevenue !== undefined && record.revenueInfo?.dsHasModuleRevenue !== null
            ? record.revenueInfo.dsHasModuleRevenue
              ? 'true'
              : 'false'
            : undefined;
        const editable = isRoleEditable(record);
        if (record.revenueInfo?.reviewType !== StoryRevenueTaskRevenueReviewType.ParticipateInTheEvaluation) {
          return <StoryRevenueSelfAssementEmptyCell hoverText={'不参与Review，无法编辑'} />;
        }
        return (
          <YesOrNoSelector
            editable={false}
            saveValue={async newValue => {
              if (!record.revenueInfo) {
                return;
              }
              const res = await onEditTaskInfo(record, 'revenueInfo.dsHasModuleRevenue', newValue);
              if (res) {
                record.revenueInfo.dsHasModuleRevenue = newValue;
              }
            }}
            defaultValue={editValue}
            optionNames={['有', '无']}
          />
        );
      },
    },
    has_key_process_revenue: {
      width: 200,
      onCell: (record, rowIndex) => ({
        style: { width: '100%', height: 50, padding: 0 },
      }),
      dataIndex: 'revenueInfo.hasKeyProcessRevenue',
      render: (text, record, index) => {
        const editValue =
          record.revenueInfo?.manullyHasKeyProcessRevenue !== undefined &&
          record.revenueInfo?.manullyHasKeyProcessRevenue !== null
            ? record.revenueInfo.manullyHasKeyProcessRevenue
              ? 'true'
              : 'false'
            : undefined;
        const cloudValue =
          record.revenueInfo?.hasKeyProcessRevenue !== undefined && record.revenueInfo?.hasKeyProcessRevenue !== null
            ? record.revenueInfo.hasKeyProcessRevenue
              ? 'true'
              : 'false'
            : undefined;
        const editable =
          (((cloudValue === undefined || cloudValue === null) && isTaskInfoEditable(record)) ||
            isRoleEditable(record)) &&
          !record.updating;
        if (
          (cloudValue === undefined || cloudValue === null) &&
          record.revenueInfo?.reviewType !== StoryRevenueTaskRevenueReviewType.ParticipateInTheEvaluation
        ) {
          return <StoryRevenueSelfAssementEmptyCell hoverText={'不参与Review，无法编辑'} />;
        }
        return (
          <FeatureRevenueSelector
            taskInfo={record}
            editable={editable}
            saveValue={async newValue => {
              if (!record.revenueInfo) {
                return;
              }
              const res = await onEditTaskInfo(record, 'revenueInfo.manullyHasKeyProcessRevenue', newValue);
              if (res) {
                record.revenueInfo.hasPlatformRevenue = newValue;
              }
            }}
            autoRes={cloudValue}
            manuallyRes={editValue}
          />
        );
      },
    },
    ds_has_key_process_revenue: {
      width: 180,
      dataIndex: 'revenueInfo.dsHasKeyProcessRevenue',
      onCell: (record, rowIndex) => ({
        style: { width: '100%', height: 50, padding: 0, background: 'rgba(var(--semi-light-blue-0), 0.75)' },
      }),
      onHeaderCell: (record, rowIndex) => ({
        style: { background: 'rgba(var(--semi-light-blue-0), 0.75)' },
      }),
      render: (text, record, index) => {
        const editValue =
          record.revenueInfo?.dsHasKeyProcessRevenue !== undefined &&
          record.revenueInfo?.dsHasKeyProcessRevenue !== null
            ? record.revenueInfo.dsHasKeyProcessRevenue
              ? 'true'
              : 'false'
            : undefined;
        const editable = isRoleEditable(record);
        if (record.revenueInfo?.reviewType !== StoryRevenueTaskRevenueReviewType.ParticipateInTheEvaluation) {
          return <StoryRevenueSelfAssementEmptyCell hoverText={'不参与Review，无法编辑'} />;
        }
        return (
          <YesOrNoSelector
            editable={false}
            saveValue={async newValue => {
              if (!record.revenueInfo) {
                return;
              }
              const res = await onEditTaskInfo(record, 'revenueInfo.dsHasKeyProcessRevenue', newValue);
              if (res) {
                record.revenueInfo.dsHasKeyProcessRevenue = newValue;
              }
            }}
            defaultValue={editValue}
            optionNames={['有', '无']}
          />
        );
      },
    },
    ds_revenue_conclusion_revenue: {
      width: 200,
      dataIndex: 'revenueInfo.dsRevenueConclusionRemark',
      onCell: (record, rowIndex) => ({
        style: { width: '100%', padding: 0, height: 50, background: 'rgba(var(--semi-light-blue-0), 0.75)' },
      }),
      onHeaderCell: (record, rowIndex) => ({
        style: { background: 'rgba(var(--semi-light-blue-0), 0.75)' },
      }),
      render: (text, record) => {
        if (record.revenueInfo?.reviewType !== StoryRevenueTaskRevenueReviewType.ParticipateInTheEvaluation) {
          return <StoryRevenueSelfAssementEmptyCell hoverText={'不参与Review，无法编辑'} />;
        }
        return (
          <TableTextEditCell
            onTextUpdate={async newText => {
              await onEditTaskInfo(record, 'revenueInfo.dsRevenueConclusionRemark', newText);
            }}
            cellTitle={'DS修正收益判断差异说明'}
            initText={record.revenueInfo?.dsRevenueConclusionRemark}
            editable={isRoleEditable(record)}
          />
        );
      },
    },
    client_revenue_conclusion: {
      width: 200,
      dataIndex: 'revenueInfo.platformRevenueMetricConclusion',
      onCell: (record, rowIndex) => ({
        style: { width: '100%', padding: 0, height: 50 },
      }),
      render: (text, record) => {
        const conclusions = record.revenueInfo?.platformRevenueMetricAutoComputedConclusion;
        const significantValue = conclusions
          ? conclusions[0]?.significanceValues
            ? conclusions[0]?.significanceValues[0]
            : undefined
          : undefined;
        const autoComputedConclusion: StoryRevenueTaskRevenueSignificanceInfo[] = [];
        conclusions?.forEach(it => {
          it.significanceValues.forEach(it1 => {
            autoComputedConclusion.push(it1);
          });
        });
        const moreConclusion = autoComputedConclusion.length > 1 ? '……' : undefined;
        if (
          !significantValue &&
          record.revenueInfo?.reviewType !== StoryRevenueTaskRevenueReviewType.ParticipateInTheEvaluation
        ) {
          return <StoryRevenueSelfAssementEmptyCell hoverText={'不参与Review，无法编辑'} />;
        }
        return significantValue ? (
          <RevenueConclusionCell
            taskInfo={record}
            revenueInfo={record.revenueInfo}
            conclusion={conclusions ? conclusions[0] : undefined}
          />
        ) : (
          <TableTextEditCell
            onTextUpdate={async newText => {
              await onEditTaskInfo(record, 'revenueInfo.platformRevenueMetricConclusion', newText);
            }}
            cellTitle={'端收益指标详情'}
            initText={record.revenueInfo?.platformRevenueMetricConclusion ?? moreConclusion}
            editable={isTaskInfoEditable(record)}
          />
        );
      },
    },
    module_revenue_conclusion: {
      width: 200,
      dataIndex: 'revenueInfo.moduleRevenueMetricAutoComputedConclusion.type',
      onCell: (record, rowIndex) => ({
        style: { width: '100%', padding: 0, height: 50 },
      }),
      render: (text, record) => {
        const conclusions = record.revenueInfo?.moduleRevenueMetricAutoComputedConclusion;
        const significantValue = conclusions
          ? conclusions[0]?.significanceValues
            ? conclusions[0]?.significanceValues[0]
            : undefined
          : undefined;
        if (
          !significantValue &&
          record.revenueInfo?.reviewType !== StoryRevenueTaskRevenueReviewType.ParticipateInTheEvaluation
        ) {
          return <StoryRevenueSelfAssementEmptyCell hoverText={'不参与Review，无法编辑'} />;
        }
        const autoComputedConclusion: StoryRevenueTaskRevenueSignificanceInfo[] = [];
        conclusions?.forEach(it => {
          it.significanceValues.forEach(it1 => {
            autoComputedConclusion.push(it1);
          });
        });
        const moreConclusion = autoComputedConclusion.length > 1 ? '……' : undefined;
        return significantValue ? (
          <RevenueConclusionCell
            taskInfo={record}
            revenueInfo={record.revenueInfo}
            conclusion={conclusions ? conclusions[0] : undefined}
          />
        ) : (
          <TableTextEditCell
            onTextUpdate={async newText => {
              await onEditTaskInfo(record, 'revenueInfo.moduleRevenueMetricConclusion', newText);
            }}
            cellTitle={'模块收益指标详情'}
            initText={record.revenueInfo?.moduleRevenueMetricConclusion ?? moreConclusion}
            editable={isTaskInfoEditable(record)}
          />
        );
      },
    },
    process_revenue_conclusion: {
      width: 200,
      dataIndex: 'revenueInfo.keyProcessRevenueMetricAutoComputedConclusion.type',
      onCell: (record, rowIndex) => ({
        style: { width: '100%', padding: 0, height: 50 },
      }),
      render: (text, record) => {
        const conclusions = record.revenueInfo?.keyProcessRevenueMetricAutoComputedConclusion;
        const significantValue = conclusions
          ? conclusions[0]?.significanceValues
            ? conclusions[0]?.significanceValues[0]
            : undefined
          : undefined;
        if (
          !significantValue &&
          record.revenueInfo?.reviewType !== StoryRevenueTaskRevenueReviewType.ParticipateInTheEvaluation
        ) {
          return <StoryRevenueSelfAssementEmptyCell hoverText={'不参与Review，无法编辑'} />;
        }
        const autoComputedConclusion: StoryRevenueTaskRevenueSignificanceInfo[] = [];
        conclusions?.forEach(it => {
          it.significanceValues.forEach(it1 => {
            autoComputedConclusion.push(it1);
          });
        });
        const moreConclusion = autoComputedConclusion.length > 1 ? '……' : undefined;
        return significantValue ? (
          <RevenueConclusionCell
            taskInfo={record}
            revenueInfo={record.revenueInfo}
            conclusion={conclusions ? conclusions[0] : undefined}
          />
        ) : (
          <TableTextEditCell
            onTextUpdate={async newText => {
              await onEditTaskInfo(record, 'revenueInfo.keyProcessRevenueMetricConclusion', newText);
            }}
            cellTitle={'过程指标收益详情'}
            initText={record.revenueInfo?.keyProcessRevenueMetricConclusion ?? moreConclusion}
            editable={isTaskInfoEditable(record)}
          />
        );
      },
    },
    is_full_release: {
      width: 200,
      dataIndex: 'revenueInfo.isFullyRelease',
      onCell: (record, rowIndex) => ({
        style: { width: '100%', height: 50, padding: 0 },
      }),
      render: (text, record, index) => {
        if (record.revenueInfo?.reviewType !== StoryRevenueTaskRevenueReviewType.ParticipateInTheEvaluation) {
          return <StoryRevenueSelfAssementEmptyCell hoverText={'不参与Review，无法编辑'} />;
        }
        return (
          <YesOrNoSelector
            optionNames={['是', '暂无计划']}
            defaultValue={
              record.revenueInfo?.isFullyRelease !== undefined && record.revenueInfo?.isFullyRelease !== null
                ? record.revenueInfo.isFullyRelease
                  ? 'true'
                  : 'false'
                : '待补充'
            }
            saveValue={async newValue => {
              if (!record.revenueInfo) {
                return;
              }
              const res = await onEditTaskInfo(record, 'revenueInfo.isFullyRelease', newValue);
              if (res) {
                record.revenueInfo.isFullyRelease = newValue;
              }
            }}
            editable={isTaskInfoEditable(record)}
          />
        );
      },
    },
    has_analysis_basis: {
      width: 150,
      dataIndex: 'hasAnalysisBasis',
      render: (text, record, index) => {
        const experimentList = record.experimentInfo?.subExperimentList;
        const appList = record.publishedAppRevenueInfos;
        let basisCount = 0;
        if (experimentList && experimentList?.length > 0) {
          basisCount = experimentList.filter(it => it.markAsAnalysisBasis).length;
        } else if (appList) {
          basisCount = appList.filter(it => it.markAsAnalysisBasis).length;
        }
        if (basisCount > 0) {
          return <Tag color={'green'}>有</Tag>;
        } else {
          return <Tag color={'pink'}>无</Tag>;
        }
      },
    },
    revenue_self_assessment: {
      width: 150,
      onCell: (record, rowIndex) => ({
        style: { width: '100%', height: 50, padding: 0 },
      }),
      dataIndex: 'revenueInfo.revenueSelfAssessment',
      render: (text, record, index) => {
        const reviewTypeMap: Record<number, string> = {};
        if (record.revenueInfo?.reviewType !== StoryRevenueTaskRevenueReviewType.ParticipateInTheEvaluation) {
          return <StoryRevenueSelfAssementEmptyCell hoverText={'不参与Review，无法编辑'} />;
        }
        if (record.revenueInfo?.expectationType === undefined || record.revenueInfo?.expectationType === null) {
          return <StoryRevenueSelfAssementEmptyCell hoverText={'请先填写收益自评'} />;
        }
        if (record.revenueInfo.expectationType === StoryRevenueTaskRevenueExpectationType.BelowExpectations) {
          reviewTypeMap[StoryRevenueTaskRevenueSelfAssessmentType.BelowExpectationsAndContinuousOptimization] =
            '后续继续优化（产品决策正常）';
          reviewTypeMap[StoryRevenueTaskRevenueSelfAssessmentType.BelowExpectationsAndStopTrying] =
            '后续停止尝试（产品决策正常）';
          reviewTypeMap[StoryRevenueTaskRevenueSelfAssessmentType.BelowExpectationsForPMWrongDecision] = '产品决策失误';
        }
        if (record.revenueInfo.expectationType === StoryRevenueTaskRevenueExpectationType.MeetExpectations) {
          reviewTypeMap[StoryRevenueTaskRevenueSelfAssessmentType.MeetExpectationsForNegativeGovernance] =
            '负向治理（体验优化）';
          reviewTypeMap[StoryRevenueTaskRevenueSelfAssessmentType.MeetExpectationsForLongTermValue] =
            '长期价值（长期收益更大/基础能力建设）';
          reviewTypeMap[StoryRevenueTaskRevenueSelfAssessmentType.MeetExpectationsForLongTermExploration] =
            '长期探索（AI类需要保持耐心探索）';
          reviewTypeMap[StoryRevenueTaskRevenueSelfAssessmentType.MeetExpectationsForVerticalScenario] =
            '垂直场景（高级功能/渗透低的场景，不预期撬动端or模块）';
        }
        if (record.revenueInfo.expectationType === StoryRevenueTaskRevenueExpectationType.ExceedExpectations) {
          reviewTypeMap[StoryRevenueTaskRevenueSelfAssessmentType.ExceedExpectations] = '超出预期';
        }
        const optionList = Object.keys(reviewTypeMap).map(it => ({
          value: it.toString(),
          label: reviewTypeMap[Number(it)],
        }));
        return (
          <div style={{ width: '100%', height: '100%' }}>
            <EventResponseTagSelector
              taskInfo={record}
              editable={isTaskInfoEditable(record)}
              options={optionList}
              defaultValue={
                record.revenueInfo?.revenueSelfAssessment !== undefined &&
                record.revenueInfo?.revenueSelfAssessment !== null
                  ? [record.revenueInfo?.revenueSelfAssessment.toString()]
                  : undefined
              }
              saveValue={async newValue => {
                if (!record.revenueInfo) {
                  return;
                }
                const res = await onEditTaskInfo(record, 'revenueInfo.revenueSelfAssessment', Number(newValue[0]));
                if (res && newValue[0]) {
                  record.revenueInfo.revenueSelfAssessment = Number(newValue[0]);
                }
              }}
            />
          </div>
        );
      },
    },
    review_period_id: {
      width: 150,
      dataIndex: 'revenueInfo.reviewPeriodId',
      onCell: (record, rowIndex) => ({
        style: { width: '100%', height: 50, padding: 0 },
      }),
      render: (text, record, index) => {
        const selectedPeriod = allPeriodInfo?.find(it => it.reviewPeriodId === record.reviewPeriodId);
        const options = allPeriodInfo?.map(it => ({ value: it.reviewPeriodId, label: it.reviewPeriodName }));
        return (
          <div style={{ width: '100%', height: '100%' }}>
            <EventResponseTagSelector
              showClose={false}
              taskInfo={record}
              editable={isTaskInfoEditable(record)}
              options={options ?? []}
              defaultValue={selectedPeriod?.reviewPeriodName}
              saveValue={async newValue => {
                if (!record.revenueInfo) {
                  return;
                }
                if (record.reviewPeriodId === newValue[0]) {
                  return;
                }
                const res = await onEditTaskInfo(record, 'revenueInfo.reviewPeriodId', newValue);
                if (res && newValue[0]) {
                  record.revenueInfo.reviewPeriodId = newValue[0];
                }
              }}
            />
          </div>
        );
      },
    },
    related_flight_link: {
      width: 200,
      dataIndex: 'experimentInfo.subExperimentList.libraTitle',
      render: (text, record, index) => <RelatedFlightLinkCell taskInfo={record} />,
      onCell: (record, rowIndex) => ({
        style: { width: '100%', height: 50, padding: 0 },
      }),
    },
    related_flight_state: {
      width: 150,
      dataIndex: 'experimentInfo.subExperimentList.status',
      render: (text, record, index) => {
        if (!record.experimentInfo) {
          return <></>;
        }
        if (record.experimentInfo.subExperimentCount === 0) {
          return <></>;
        }
        if (record.experimentInfo.subExperimentCount > 1) {
          return <></>;
        }
        const reviewTypeMap: Record<number, string> = {};
        reviewTypeMap[StoryRevenueTaskSubExperimentStatus.Ended] = '已结束';
        reviewTypeMap[StoryRevenueTaskSubExperimentStatus.InProgress] = '进行中';
        reviewTypeMap[StoryRevenueTaskSubExperimentStatus.ToBeScheduled] = '待调度';
        reviewTypeMap[StoryRevenueTaskSubExperimentStatus.InDebug] = '调试中';
        reviewTypeMap[StoryRevenueTaskSubExperimentStatus.Paused] = '已暂停';
        reviewTypeMap[StoryRevenueTaskSubExperimentStatus.ToBeScheduledEnded] = '待调度结束';
        reviewTypeMap[StoryRevenueTaskSubExperimentStatus.Released] = '已上线';
        const optionList = Object.values(reviewTypeMap);
        return <TagArray tagNames={[reviewTypeMap[record.experimentInfo?.subExperimentList[0].status]]} />;
      },
    },
    flight_start_time: {
      width: 200,
      dataIndex: 'experimentInfo.subExperimentList.startTime',
      render: (text, record, index) => {
        if (!record.experimentInfo) {
          return <></>;
        }
        if (record.experimentInfo.subExperimentCount === 0) {
          return <></>;
        }
        if (record.experimentInfo.subExperimentCount > 1) {
          return <></>;
        }
        if (record.experimentInfo.subExperimentList[0].startTime < 10) {
          return <></>;
        }
        const date = new Date(record.experimentInfo.subExperimentList[0].startTime * 1000);
        const dateString = date.toLocaleDateString('zh-CN', {
          year: 'numeric',
          month: '2-digit',
          day: '2-digit',
          timeZone: 'Asia/Shanghai',
        });
        return <Text>{dateString}</Text>;
      },
    },
    flight_close_time: {
      width: 200,
      dataIndex: 'experimentInfo.subExperimentList.endTime',
      render: (text, record, index) => {
        if (!record.experimentInfo) {
          return <></>;
        }
        if (record.experimentInfo.subExperimentCount === 0) {
          return <></>;
        }
        if (record.experimentInfo.subExperimentCount > 1) {
          return <></>;
        }
        if (record.experimentInfo.subExperimentList[0].endTime < 10) {
          return <></>;
        }
        const date = new Date(record.experimentInfo.subExperimentList[0].endTime * 1000);
        const dateString = date.toLocaleDateString('zh-CN', {
          year: 'numeric',
          month: '2-digit',
          day: '2-digit',
          timeZone: 'Asia/Shanghai',
        });
        return <Text>{dateString}</Text>;
      },
    },
    ds_reviewer: {
      width: 200,
      dataIndex: 'dsReviewer.name',
      onCell: (record, rowIndex) => ({
        style: { width: '100%', padding: 0, height: 50 },
      }),
      render: (text, record, index) => (
        <EditableUserGroupCell
          taskInfo={record}
          users={
            record.revenueInfo?.dsReviewer
              ? StoryRevenueConvertCopyUserToUser(record.revenueInfo?.dsReviewer)
              : undefined
          }
          editable={isRoleEditable(record)}
          updateTaskInfo={updateTaskInfo}
          keyPath={'revenueInfo.dsReviewer'}
          columnConfig={TableColumnConfigMap()['ds_reviewer']}
        />
      ),
    },
    remark: {
      width: 100,
      dataIndex: 'revenueInfo.remark',
      onCell: (record, rowIndex) => ({
        style: { width: '100%', padding: 0, height: 50 },
      }),
      render: (text, record) => (
        <TableTextEditCell
          onTextUpdate={async newText => {
            await onEditTaskInfo(record, 'revenueInfo.remark', newText);
          }}
          cellTitle={'备注信息'}
          initText={record.revenueInfo?.remark}
          editable={isTaskInfoEditable(record)}
        />
      ),
    },
  };
  return columnOnjectMap[columnId];
};

export const getSubFlightTableColumnObject = (
  columnId: string,
  onEditTaskInfo: (
    experimentInfo: StoryRevenueTaskSubExperimentInfo,
    keyPath: string,
    newValue: any,
  ) => Promise<boolean>,
): ColumnProps<StoryRevenueTaskSubExperimentInfo> | undefined => {
  const columnOnjectMap: { [key: string]: ColumnProps<StoryRevenueTaskSubExperimentInfo> } = {
    has_platform_revenue: {
      width: 150,
      dataIndex: 'revenueInfo.hasPlatformRevenue',
      onCell: (record, rowIndex) => ({
        style: { width: '100%', height: 50, padding: 0 },
      }),
      render: (text, record, index) => (
        <YesOrNoSelector
          optionNames={['有', '无']}
          defaultValue={
            record.revenueInfo?.hasPlatformRevenue !== undefined
              ? record.revenueInfo.hasPlatformRevenue
                ? 'true'
                : 'false'
              : '待评估'
          }
          saveValue={async newValue => {
            if (!record.revenueInfo) {
              return;
            }
            const res = await onEditTaskInfo(record, 'revenueInfo.hasPlatformRevenue', newValue);
            if (res) {
              record.revenueInfo.hasPlatformRevenue = newValue;
            }
          }}
          editable={false}
        />
      ),
    },
    has_module_revenue: {
      width: 150,
      dataIndex: 'revenueInfo.hasModuleRevenue',
      onCell: (record, rowIndex) => ({
        style: { width: '100%', height: 50, padding: 0 },
      }),
      render: (text, record, index) => (
        <YesOrNoSelector
          optionNames={['有', '无']}
          defaultValue={
            record.revenueInfo?.hasModuleRevenue !== undefined
              ? record.revenueInfo.hasModuleRevenue
                ? 'true'
                : 'false'
              : '待评估'
          }
          saveValue={async newValue => {
            if (!record.revenueInfo) {
              return;
            }
            const res = await onEditTaskInfo(record, 'revenueInfo.hasModuleRevenue', newValue);
            if (res) {
              record.revenueInfo.hasModuleRevenue = newValue;
            }
          }}
          editable={false}
        />
      ),
    },
    client_revenue_conclusion: {
      width: 200,
      dataIndex: 'revenueInfo.platformRevenueMetricConclusion',
      onCell: (record, rowIndex) => ({
        style: { width: '100%', padding: 0, height: 50 },
      }),
      render: (text, record) => {
        const conclusions = record.revenueInfo?.platformRevenueMetricAutoComputedConclusion;
        const significantValue = conclusions
          ? conclusions[0]?.significanceValues
            ? conclusions[0]?.significanceValues[0]
            : undefined
          : undefined;
        return significantValue ? (
          <RevenueConclusionCell
            taskInfo={undefined}
            revenueInfo={record.revenueInfo}
            conclusion={conclusions ? conclusions[0] : undefined}
          />
        ) : (
          // <TableTextEditCell
          // 	onTextUpdate={async (newText) => {
          // 		await onEditTaskInfo(record, "revenueInfo.platformRevenueMetricConclusion", newText);
          // 	}}
          // 	cellTitle={"端收益结论"}
          // 	initText={record.revenueInfo?.platformRevenueMetricConclusion}
          // />
          <></>
        );
      },
    },
    module_revenue_conclusion: {
      width: 200,
      dataIndex: 'revenueInfo.moduleRevenueMetricConclusion',
      onCell: (record, rowIndex) => ({
        style: { width: '100%', padding: 0, height: 50 },
      }),
      render: (text, record) => {
        const conclusions = record.revenueInfo?.moduleRevenueMetricAutoComputedConclusion;
        const significantValue = conclusions
          ? conclusions[0]?.significanceValues
            ? conclusions[0]?.significanceValues[0]
            : undefined
          : undefined;
        return significantValue ? (
          <RevenueConclusionCell
            taskInfo={undefined}
            revenueInfo={record.revenueInfo}
            conclusion={conclusions ? conclusions[0] : undefined}
          />
        ) : (
          // <TableTextEditCell
          // 	onTextUpdate={async (newText) => {
          // 		await onEditTaskInfo(record, "revenueInfo.moduleRevenueMetricConclusion", newText);
          // 	}}
          // 	cellTitle={"模块收益结论"}
          // 	initText={record.revenueInfo?.moduleRevenueMetricConclusion}
          // />
          <></>
        );
      },
    },
    process_revenue_conclusion: {
      width: 200,
      dataIndex: 'revenueInfo.keyProcessRevenueMetricConclusion',
      onCell: (record, rowIndex) => ({
        style: { width: '100%', padding: 0, height: 50 },
      }),
      render: (text, record) => {
        const conclusions = record.revenueInfo?.keyProcessRevenueMetricAutoComputedConclusion;
        const significantValue = conclusions
          ? conclusions[0]?.significanceValues
            ? conclusions[0]?.significanceValues[0]
            : undefined
          : undefined;
        return significantValue ? (
          <RevenueConclusionCell
            taskInfo={undefined}
            revenueInfo={record.revenueInfo}
            conclusion={conclusions ? conclusions[0] : undefined}
          />
        ) : (
          // <TableTextEditCell
          // 	onTextUpdate={async (newText) => {
          // 		await onEditTaskInfo(record, "revenueInfo.keyProcessRevenueMetricConclusion", newText);
          // 	}}
          // 	cellTitle={"过程收益结论"}
          // 	initText={record.revenueInfo?.keyProcessRevenueMetricConclusion}
          // />
          <></>
        );
      },
    },
    has_key_process_revenue: {
      width: 150,
      onCell: (record, rowIndex) => ({
        style: { width: '100%', height: 50, padding: 0 },
      }),
      dataIndex: 'revenueInfo.hasKeyProcessRevenue',
      render: (text, record, index) => (
        <YesOrNoSelector
          optionNames={['有', '无']}
          defaultValue={
            record.revenueInfo?.hasKeyProcessRevenue !== undefined
              ? record.revenueInfo.hasKeyProcessRevenue
                ? 'true'
                : 'false'
              : '待评估'
          }
          saveValue={async newValue => {
            if (!record.revenueInfo) {
              return;
            }
            const res = await onEditTaskInfo(record, 'revenueInfo.hasKeyProcessRevenue', newValue);
            if (res) {
              record.revenueInfo.hasKeyProcessRevenue = newValue;
            }
          }}
          editable={false}
        />
      ),
    },
  };
  return columnOnjectMap[columnId];
};
