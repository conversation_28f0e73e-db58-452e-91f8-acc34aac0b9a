import React, { useState } from 'react';
import { StoryRevenueTaskInfo } from '@shared/storyRevenueReviewPlatform/StoryRevenueTaskInfo';
import { Button, Modal, Space, Toast, Tooltip, Typography } from '@douyinfe/semi-ui';
import { IconAlertTriangle, IconSync } from '@douyinfe/semi-icons';
import {
  batchSyncDataToMeego,
  updateStoryRevenueTask,
  updateStoryRevenueTaskMeegoInfo,
} from '@api/storyRevenueReviewPlatform';
import { useModel } from '@edenx/runtime/model';
import UserSettingModule from '@/model/userSettingModel';
import { StoryRevenueReviewPeriodInfo } from '@shared/storyRevenueReviewPlatform/StoryRevenueReviewPeriodInfo';

const { Text } = Typography;

const StoryRevenueTaskSyncDataToMeegoButton: React.FC<{
  taskInfo: StoryRevenueTaskInfo;
  periodInfo: StoryRevenueReviewPeriodInfo;
  onTaskInfoUpdate: (newTaskInfo: StoryRevenueTaskInfo) => void;
}> = ({ taskInfo, periodInfo, onTaskInfoUpdate }) => {
  const [updateConfirmVisible, setUpdateConfirmVisible] = useState(false);
  const [userSettingState] = useModel(UserSettingModule);

  const changeUpdateStatus = async (updating: boolean) => {
    const newTaskInfo = {
      ...taskInfo,
      updating,
    };
    onTaskInfoUpdate(newTaskInfo);
    await updateStoryRevenueTask({
      data: newTaskInfo,
    });
  };

  // 确认同步收益信息至 Meego
  const handleConfirmOK = async () => {
    setUpdateConfirmVisible(false);
    // 更新 Task UI，表示更新中
    await changeUpdateStatus(true);
    // 单个 Task Info 同步收益信息至 Meego
    const taskIdList = [taskInfo._id ?? ''];
    batchSyncDataToMeego({
      data: {
        periodInfo,
        taskIdList,
        updatePeriodSyncingDataStatus: false,
      },
    })
      .then(async res => {
        await changeUpdateStatus(false);
        if (res.code !== 0) {
          Toast.error(`同步收益信息至 Meego 失败，error: ${res.msg}`);
          return;
        }
        Toast.success(`同步收益信息至 Meego 成功！`);
      })
      .catch(async err => {
        await changeUpdateStatus(false);
        Toast.error(`同步收益信息至 Meego 失败，error: ${err}`);
      });
  };

  // 取消同步收益信息至 Meego
  const handleConfirmCancel = () => {
    setUpdateConfirmVisible(false);
  };

  // 点击更新按钮
  const handleClickRefreshButton = () => {
    setUpdateConfirmVisible(true);
  };

  return (
    <>
      <Button
        theme="borderless"
        // disabled={taskInfo.updating}
        style={{ color: 'rgba(var(--semi-grey-5), 1)' }}
        onClick={handleClickRefreshButton}
      >
        同步收益信息至 Meego
      </Button>
      {/* <Tooltip content={taskInfo.updating ? '数据更新中无法操作' : '更新 Meego 信息'}>*/}
      {/*  <Button*/}
      {/*    icon={<IconSync />}*/}
      {/*    theme="borderless"*/}
      {/*    // disabled={taskInfo.updating}*/}
      {/*    style={{ color: 'rgba(var(--semi-grey-5), 1)' }}*/}
      {/*    onClick={handleClickRefreshButton}*/}
      {/*  />*/}
      {/* </Tooltip>*/}
      <Modal
        icon={<IconAlertTriangle style={{ color: 'orange', fontSize: 24 }} />}
        title={'确认回传收益信息至 Meego？'}
        visible={updateConfirmVisible}
        onOk={handleConfirmOK}
        onCancel={handleConfirmCancel}
        closeOnEsc={true}
      >
        <>
          <Space vertical={false} align="start">
            <Text strong style={{ whiteSpace: 'nowrap' }}>
              需求:
            </Text>
            <Text strong link={{ href: taskInfo.meegoInfo.url, target: '_blank' }}>
              {taskInfo.meegoInfo.name}
            </Text>
          </Space>
          <br />
          <Space vertical={false} align="start">
            <Text>
              将需求的「参与收益 Review 周期」、「收益 Review 状态」等信息同步至 Meego。同步过程中需求将处于
              <strong>不可编辑状态</strong>。
            </Text>
          </Space>
        </>
      </Modal>
    </>
  );
};

export default StoryRevenueTaskSyncDataToMeegoButton;
