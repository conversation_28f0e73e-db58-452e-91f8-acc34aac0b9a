import {
  StoryRevenueTaskInfo,
  StoryRevenueTaskSubExperimentInfo,
} from '@shared/storyRevenueReviewPlatform/StoryRevenueTaskInfo';
import React, { useEffect, useState } from 'react';
import { Button, Card, Popover, Space, Typography } from '@douyinfe/semi-ui';
import { IconExpand, IconShrink } from '@douyinfe/semi-icons';

const { Text } = Typography;

export const RelatedFlightLinkCell: React.FC<{
  taskInfo?: StoryRevenueTaskInfo;
}> = ({ taskInfo }) => {
  const [flightList, setFlightList] = useState<StoryRevenueTaskSubExperimentInfo[]>([]);
  const [expanded, setExpanded] = useState(false);
  const [popoverVisible, setPopoverVisible] = useState(false);

  useEffect(() => {
    setFlightList(taskInfo?.experimentInfo?.subExperimentList ?? []);
  }, [taskInfo]);

  return flightList ? (
    <div
      style={{
        maxWidth: '100%',
        maxHeight: '100%',
        paddingLeft: 12,
        paddingRight: 12,
      }}
    >
      <Space vertical={true} align={'start'} style={{ width: '100%', padding: 1 }} spacing={2}>
        {expanded ? (
          flightList.map(it => (
            <Text link={{ href: it.libraUrl, target: '_blank' }} ellipsis={true} key={it.libraFlightId}>
              {it.libraTitle}
            </Text>
          ))
        ) : (
          <Text link={{ href: flightList[0]?.libraUrl, target: '_blank' }} ellipsis={true}>
            {flightList[0]?.libraTitle}
          </Text>
        )}
        {flightList.length > 1 ? (
          <div
            style={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
              width: '100%',
            }}
          >
            <Text type="quaternary">{expanded ? '' : '......'}</Text>
            <Popover
              content={
                <Card style={{ minWidth: 250 }}>
                  <Space vertical={true} align={'start'} style={{ width: '100%', padding: 1 }} spacing={2}>
                    {flightList.map(it => (
                      <Text link={{ href: it.libraUrl, target: '_blank' }} ellipsis={true} key={it.libraFlightId}>
                        {it.libraTitle}
                      </Text>
                    ))}
                  </Space>
                </Card>
              }
              trigger={'hover'}
              // visible={popoverVisible}
            >
              <div
                onMouseEnter={() => {
                  setPopoverVisible(!expanded);
                }}
                onMouseLeave={() => setPopoverVisible(false)}
              >
                <Button
                  icon={expanded ? <IconShrink /> : <IconExpand />}
                  style={{ color: 'rgba(var(--semi-grey-5), 1)' }}
                  onClick={() => {
                    setExpanded(!expanded);
                  }}
                />
              </div>
            </Popover>
          </div>
        ) : (
          <></>
        )}
      </Space>
    </div>
  ) : (
    <></>
  );
};

export default RelatedFlightLinkCell;
