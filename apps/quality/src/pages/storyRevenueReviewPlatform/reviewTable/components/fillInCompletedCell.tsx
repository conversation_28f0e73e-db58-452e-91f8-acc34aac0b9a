import React, { useState } from 'react';
import { Popover, Tag, Typography } from '@douyinfe/semi-ui';

const { Text } = Typography;
const FillInCompletedCell: React.FC<{ hoverText: string[] }> = ({ hoverText }) => {
  const [popoverVisible, setPopoverVisible] = useState(false);

  return (
    <div
      style={{
        width: '100%',
        height: '100%',
        paddingLeft: 24,
        paddingRight: 12,
        alignItems: 'center',
        display: 'flex',
      }}
      onMouseEnter={() => {
        setPopoverVisible(true);
      }}
      onMouseLeave={() => {
        setPopoverVisible(false);
      }}
    >
      <Popover
        content={
          <div>
            <div>以下信息填写不完整：</div>
            <ul style={{ marginTop: 4, paddingLeft: 16 }}>
              {hoverText.map((reason, index) => (
                <li key={index}>{reason}</li>
              ))}
            </ul>
          </div>
        }
        trigger={'custom'}
        visible={popoverVisible}
        showArrow={true}
      >
        <Tag color={'red'}>不完整</Tag>
      </Popover>
    </div>
  );
};

export default FillInCompletedCell;
