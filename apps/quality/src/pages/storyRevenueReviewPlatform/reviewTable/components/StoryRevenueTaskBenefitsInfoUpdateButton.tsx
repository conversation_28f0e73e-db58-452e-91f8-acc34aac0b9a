import React, { useState } from 'react';
import {
  StoryRevenueTaskBenefitsRefreshType,
  StoryRevenueTaskInfo,
  StoryRevenueTaskInfoFix,
} from '@shared/storyRevenueReviewPlatform/StoryRevenueTaskInfo';
import { Button, Modal, Space, Toast, Tooltip, Typography } from '@douyinfe/semi-ui';
import { IconAlertTriangle, IconSync } from '@douyinfe/semi-icons';
import {
  batchSyncDataToMeego,
  refreshStoryRevenueTaskInfoBenefits,
  updateStoryRevenueTask,
  updateStoryRevenueTaskMeegoInfo,
} from '@api/storyRevenueReviewPlatform';
import { useModel } from '@edenx/runtime/model';
import UserSettingModule from '@/model/userSettingModel';
import { StoryRevenueReviewPeriodInfo } from '@shared/storyRevenueReviewPlatform/StoryRevenueReviewPeriodInfo';

const { Text } = Typography;

const StoryRevenueTaskBenefitsInfoUpdateByLibraApiButton: React.FC<{
  taskInfo: StoryRevenueTaskInfo;
  periodInfo: StoryRevenueReviewPeriodInfo;
  onTaskInfoUpdate: (newTaskInfo: StoryRevenueTaskInfo) => void;
}> = ({ taskInfo, periodInfo, onTaskInfoUpdate }) => {
  const [updateConfirmVisible, setUpdateConfirmVisible] = useState(false);
  const [userSettingState] = useModel(UserSettingModule);

  const changeUpdateStatus = async (updating: boolean, originTaskInfo: StoryRevenueTaskInfo) => {
    const newTaskInfo = {
      ...originTaskInfo,
      updating,
    };
    onTaskInfoUpdate(newTaskInfo);
  };

  // 刷新实验收益信息
  const handleConfirmOK = async () => {
    setUpdateConfirmVisible(false);
    // 更新 Task UI，表示更新中
    await changeUpdateStatus(true, taskInfo);
    refreshStoryRevenueTaskInfoBenefits({
      data: {
        taskInfo: StoryRevenueTaskInfoFix(taskInfo),
        refreshType: StoryRevenueTaskBenefitsRefreshType.ByLibraApi,
      },
    })
      .then(async res => {
        if (res.code !== 0) {
          new Error(`${res.msg}`);
        }
        await changeUpdateStatus(false, res.data);
        if (res.code !== 0) {
          Toast.error(`刷新实验收益信息失败，error: ${res.msg}`);
          return;
        }
        Toast.success(`刷新实验收益信息成功！`);
      })
      .catch(async err => {
        await changeUpdateStatus(false, taskInfo);
        Toast.error(`刷新实验收益信息失败，error: ${err}`);
      });
  };

  // 取消同步收益信息至 Meego
  const handleConfirmCancel = () => {
    setUpdateConfirmVisible(false);
  };

  // 点击更新按钮
  const handleClickRefreshButton = () => {
    setUpdateConfirmVisible(true);
  };

  return (
    <>
      <Button
        theme="borderless"
        // disabled={taskInfo.updating}
        style={{ color: 'rgba(var(--semi-grey-5), 1)' }}
        onClick={handleClickRefreshButton}
      >
        刷新实验收益信息
      </Button>
      {/* <Tooltip content={taskInfo.updating ? '数据更新中无法操作' : '更新 Meego 信息'}>*/}
      {/*  <Button*/}
      {/*    icon={<IconSync />}*/}
      {/*    theme="borderless"*/}
      {/*    // disabled={taskInfo.updating}*/}
      {/*    style={{ color: 'rgba(var(--semi-grey-5), 1)' }}*/}
      {/*    onClick={handleClickRefreshButton}*/}
      {/*  />*/}
      {/* </Tooltip>*/}
      <Modal
        icon={<IconAlertTriangle style={{ color: 'orange', fontSize: 24 }} />}
        title={'确认刷新实验收益信息？'}
        visible={updateConfirmVisible}
        onOk={handleConfirmOK}
        onCancel={handleConfirmCancel}
        closeOnEsc={true}
      >
        <>
          <Space vertical={false} align="start">
            <Text strong style={{ whiteSpace: 'nowrap' }}>
              需求:
            </Text>
            <Text strong link={{ href: taskInfo.meegoInfo.url, target: '_blank' }}>
              {taskInfo.meegoInfo.name}
            </Text>
          </Space>
          <br />
          <Space vertical={false} align="start">
            <Text>
              刷新需求绑定的所有实验收益信息，整个过程耗时较长，约 0.5~5min。刷新过程中该需求将处于
              <strong>不可编辑状态</strong>。
            </Text>
          </Space>
        </>
      </Modal>
    </>
  );
};

export default StoryRevenueTaskBenefitsInfoUpdateByLibraApiButton;
