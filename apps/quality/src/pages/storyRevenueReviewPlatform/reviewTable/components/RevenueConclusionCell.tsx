import React, { useEffect, useState } from 'react';
import {
  StoryRevenueSignificanceType,
  StoryRevenueTaskInfo,
  StoryRevenueTaskRevenueConclusionInfo,
  StoryRevenueTaskRevenueInfo,
  StoryRevenueTaskRevenueSignificanceInfo,
  StoryRevenueTaskSubExperimentInfo,
} from '@shared/storyRevenueReviewPlatform/StoryRevenueTaskInfo';
import { Button, Card, Popover, Space, Typography } from '@douyinfe/semi-ui';
import { IconExpand, IconShrink } from '@douyinfe/semi-icons';

const { Text } = Typography;

const testRevenueInfo = (): StoryRevenueTaskRevenueConclusionInfo => ({
  vid: '8292356',
  hasRevenue: true,
  type: 0,
  significanceValues: [
    {
      metricGroupId: '75223',
      metricGroupName: 'videocut active days CUPED',
      metricId: '902503',
      metricIdName: 'ActiveDays/U',
      significance: 1,
      relativeDiff: '8.725%',
      absoluteDiff: '0.087355',
      pValue: '0.013931',
      experimentalVid: '8292356',
      benchmarkVid: '8292355',
    },
    {
      metricGroupId: '75223',
      metricGroupName: 'videocut active days CUPED',
      metricId: '902506',
      metricIdName: '大盘活跃LT',
      significance: 1,
      relativeDiff: '8.725%',
      absoluteDiff: '0.087355',
      pValue: '0.013931',
      experimentalVid: '8292356',
      benchmarkVid: '8292355',
    },
    {
      metricGroupId: '75223',
      metricGroupName: 'videocut active days CUPED',
      metricId: '902504',
      metricIdName: '大盘导出LT',
      significance: 1,
      relativeDiff: '0.725%',
      absoluteDiff: '0.087355',
      pValue: '0.013931',
      experimentalVid: '8292356',
      benchmarkVid: '8292355',
    },
    {
      metricGroupId: '75223',
      metricGroupName: 'videocut active days CUPED',
      metricId: '902505',
      metricIdName: '人均ARPU',
      significance: 1,
      relativeDiff: '1.725%',
      absoluteDiff: '0.087355',
      pValue: '0.013931',
      experimentalVid: '8292356',
      benchmarkVid: '8292355',
    },
    {
      metricGroupId: '75223',
      metricGroupName: 'videocut active days CUPED',
      metricId: '902507',
      metricIdName: '导出渗透',
      significance: 2,
      relativeDiff: '-1.725%',
      absoluteDiff: '0.087355',
      pValue: '0.013931',
      experimentalVid: '8292356',
      benchmarkVid: '8292355',
    },
  ],
});

const SignificanceLabel: React.FC<{ significanceInfo?: StoryRevenueTaskRevenueSignificanceInfo }> = ({
  significanceInfo,
}) =>
  significanceInfo ? (
    <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', width: '100%' }}>
      <Text ellipsis={true} type="tertiary">
        {significanceInfo.metricIdName}
      </Text>
      <Text
        type={
          significanceInfo.significance === StoryRevenueSignificanceType.Positive
            ? 'success'
            : significanceInfo.significance === StoryRevenueSignificanceType.Negative
              ? 'danger'
              : 'quaternary'
        }
        strong={true}
      >
        {`${
          significanceInfo.relativeDiff
        }${significanceInfo.significance === StoryRevenueSignificanceType.Positive ? '↑' : significanceInfo.significance === StoryRevenueSignificanceType.Negative ? '↓' : ''}`}
      </Text>
    </div>
  ) : (
    <></>
  );

const SignificanceLableGroup: React.FC<{ conclusion?: StoryRevenueTaskRevenueConclusionInfo }> = ({ conclusion }) =>
  conclusion ? (
    <Space vertical={true} align={'start'} spacing={5} style={{ margin: '0 auto', display: 'flex', width: '100%' }}>
      {conclusion.significanceValues.map(value => (
        <SignificanceLabel significanceInfo={value} key={value.metricId} />
      ))}
    </Space>
  ) : (
    <></>
  );

export const RevenueConclusionCell: React.FC<{
  taskInfo?: StoryRevenueTaskInfo;
  revenueInfo?: StoryRevenueTaskRevenueInfo;
  conclusion?: StoryRevenueTaskRevenueConclusionInfo;
}> = ({ taskInfo, revenueInfo, conclusion }) => {
  const [curConclusion, setCurConclusion] = useState<StoryRevenueTaskRevenueConclusionInfo | undefined>();
  const [expanded, setExpanded] = useState(false);
  const [popoverVisible, setPopoverVisible] = useState(false);

  useEffect(() => {
    setCurConclusion(conclusion ? conclusion : undefined);
  }, [conclusion]);

  useEffect(() => {
    if (expanded) {
      setPopoverVisible(false);
    }
  }, [expanded]);

  return curConclusion ? (
    <div
      style={{
        maxWidth: '100%',
        maxHeight: '100%',
        padding: 6,
      }}
    >
      <Space vertical={true} align={'start'} style={{ width: '100%', padding: 1 }} spacing={2}>
        {expanded ? (
          <SignificanceLableGroup conclusion={curConclusion} />
        ) : (
          <SignificanceLabel
            significanceInfo={curConclusion?.significanceValues ? curConclusion?.significanceValues[0] : undefined}
          />
        )}
        {curConclusion.significanceValues.length > 1 ? (
          <div
            style={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
              width: '100%',
            }}
          >
            <Text type="quaternary">{expanded ? '' : '......'}</Text>
            <Popover
              content={
                <Card style={{ minWidth: 250 }}>
                  <SignificanceLableGroup conclusion={curConclusion} />
                </Card>
              }
              trigger={'custom'}
              visible={popoverVisible}
            >
              <div
                onMouseEnter={() => {
                  setPopoverVisible(!expanded);
                }}
                onMouseLeave={() => setPopoverVisible(false)}
              >
                <Button
                  icon={expanded ? <IconShrink /> : <IconExpand />}
                  style={{ color: 'rgba(var(--semi-grey-5), 1)' }}
                  onClick={() => {
                    setExpanded(!expanded);
                  }}
                />
              </div>
            </Popover>
          </div>
        ) : (
          <></>
        )}
      </Space>
    </div>
  ) : (
    <></>
  );
};
