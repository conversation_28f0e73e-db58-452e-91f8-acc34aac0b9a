import {
  StoryRevenueConvertCopyUserToUser,
  StoryRevenuePublishedAppRevenueInfo,
  StoryRevenueTaskInfo,
  StoryRevenueTaskInfoEditResultType,
  StoryRevenueTaskRevenueConclusionInfo,
  StoryRevenueTaskRevenueInfo,
  StoryRevenueTaskRevenueReviewType,
  StoryRevenueTaskSubExperimentInfo,
  StoryRevenueTaskSubExperimentStatus,
} from '@shared/storyRevenueReviewPlatform/StoryRevenueTaskInfo';
import React, { useEffect, useState } from 'react';
import { IconDelete, IconEyeOpened, IconPlus } from '@douyinfe/semi-icons';
import {
  Button,
  Checkbox,
  Descriptions,
  Modal,
  Popconfirm,
  Select,
  SideSheet,
  Space,
  Table,
  Tag,
  Toast,
  Typography,
} from '@douyinfe/semi-ui';
import { ColumnProps, RenderReturnObject } from '@douyinfe/semi-ui/lib/es/table';
import {
  tagColorArray,
  YesOrNoSelector,
} from '@/pages/storyRevenueReviewPlatform/reviewTable/ReviewTableColumnObjectFactory';
import { RevenueConclusionCell } from '@/pages/storyRevenueReviewPlatform/reviewTable/components/RevenueConclusionCell';
import { head, noop } from 'lodash';
import TableTextEditCell from '@/pages/storyRevenueReviewPlatform/reviewTable/components/TableTextEditCell';
import {
  addTaskAppRevenue,
  autoSyncAnalyzeBasisConclusion,
  deleteTaskAppRevenue,
  editStoryRevenueTaskInfo,
  editStoryRevenueTaskSubRevenueInfo,
} from '@api/storyRevenueReviewPlatform';
import { useModel } from '@edenx/runtime/model';
import UserSettingModule from '@/model/userSettingModel';
import { StoryRevenueTableColumnConfig } from '@shared/storyRevenueReviewPlatform/StoryRevenueUserSettings/StoryRevenueTableColumnUserSetting';
import { TableColumnConfigMap } from '@shared/storyRevenueReviewPlatform/StoryRevenuePlatformUtils';
import StoryRevenueSelfAssementEmptyCell from '@/pages/storyRevenueReviewPlatform/reviewTable/components/StoryRevenueSelfAssementEmptyCell';
import FeatureRevenueSelector from '@/pages/storyRevenueReviewPlatform/reviewTable/components/FeatureRevenueSelector';
import {
  StoryRevenuePermission,
  StoryRevenueRoleType,
} from '@shared/storyRevenueReviewPlatform/StoryRevenueUserMemberInfo';
import EditableUserGroupCell from '@/pages/storyRevenueReviewPlatform/reviewTable/components/EditableUserGroupCell';
import { StoryRevenueFaceUMeegoFeildInfo } from '@shared/storyRevenueReviewPlatform/StoryRevenueFaceUMeegoFeildInfo';
import { formatTimeDifference, libraConvertTimestampToDateStr } from '@shared/libra/libraManageUtils';

const { Text, Title } = Typography;

interface RevenueConclusionInfo {
  hasPlatformRevenue?: boolean; // 是否有端收益
  platformRevenueMetricAutoComputedConclusion?: StoryRevenueTaskRevenueConclusionInfo;
  hasModuleRevenue?: boolean; // 是否有模块收益
  moduleRevenueMetricAutoComputedConclusion?: StoryRevenueTaskRevenueConclusionInfo;
  hasKeyProcessRevenue?: boolean; // 是否有关键过程收益
  keyProcessRevenueMetricAutoComputedConclusion?: StoryRevenueTaskRevenueConclusionInfo;
  flightId: string;
  vid: string;
  flightInfo?: StoryRevenueTaskSubExperimentInfo;
  appRevenueInfo?: StoryRevenuePublishedAppRevenueInfo;
  hasMetricLoss?: boolean; // 是否有指标损失
}

const AppAppendModal: React.FC<{
  taskInfo: StoryRevenueTaskInfo;
  updateTaskInfo: (newInfo: StoryRevenueTaskInfo) => void;
  userRoleType: StoryRevenueRoleType;
}> = ({ taskInfo, updateTaskInfo, userRoleType }) => {
  const [appOptions, setAppOptions] = useState<{ label: string; value: string }[]>([]);
  const [selectedApp, setSelectedApp] = useState<string | undefined>();
  const [modalVisible, setModalVisible] = useState(false);

  useEffect(() => {
    const meegoFieldAppOptions = StoryRevenueFaceUMeegoFeildInfo.find(it => it.field_key === 'supported_apps');
    if (meegoFieldAppOptions?.options) {
      let allAppOptions = meegoFieldAppOptions.options.map(it => ({
        label: it.label.toString(),
        value: it.label.toString(),
      }));
      allAppOptions = allAppOptions.filter(it =>
        taskInfo.publishedAppRevenueInfos
          ? taskInfo.publishedAppRevenueInfos.find(info => info?.publishedApp === it.label) === undefined
          : true,
      );
      setAppOptions(allAppOptions);
    } else {
      setAppOptions([]);
    }
  }, [taskInfo]);

  const onAppendApp = async () => {
    const newInfo = await addTaskAppRevenue({
      data: {
        meegoId: taskInfo.meegoInfo.id,
        periodId: taskInfo.reviewPeriodId,
        appName: selectedApp ?? '',
      },
    });
    if (newInfo) {
      updateTaskInfo(newInfo);
    }
    setSelectedApp(undefined);
    setModalVisible(false);
  };

  return (
    <>
      <Button icon={<IconPlus />} onClick={() => setModalVisible(true)}>
        添加App
      </Button>
      <Modal
        visible={modalVisible}
        onCancel={() => setModalVisible(false)}
        okButtonProps={{ disabled: !selectedApp?.length }}
        title={'请选择需要添加的APP'}
        onOk={onAppendApp}
      >
        <Select
          style={{ width: '100%' }}
          filter={true}
          optionList={appOptions}
          value={selectedApp}
          onChange={newValue => setSelectedApp(newValue?.toString() ?? '')}
        />
      </Modal>
    </>
  );
};

const FeatureRevenueInfoSideSheet: React.FC<{
  taskInfo: StoryRevenueTaskInfo;
  updateTaskInfo: (newInfo: StoryRevenueTaskInfo) => void;
  periodLocked: boolean;
  userRoleType: StoryRevenueRoleType;
}> = ({ taskInfo, updateTaskInfo, periodLocked, userRoleType }) => {
  const [revenueConclusionInfo, setRevenueConclusionInfo] = useState<RevenueConclusionInfo[]>([]);
  const [sheetVisible, setSheetVisible] = useState(false);
  const [loading, setLoading] = useState(false);
  const [userSettingState] = useModel(UserSettingModule);
  const [showAppEditButton, setShowAppEditButton] = useState(false);

  useEffect(() => {
    if (!taskInfo.experimentInfo?.subExperimentCount) {
      setShowAppEditButton(Boolean(userRoleType & StoryRevenuePermission.Export));
    } else {
      setShowAppEditButton(false);
    }
  }, [taskInfo, userRoleType]);

  const onEditTaskInfo = async (
    info: StoryRevenueTaskInfo,
    keyPath: string,
    newValue: any,
    columnConfig: StoryRevenueTableColumnConfig,
  ): Promise<boolean> => {
    const keys = keyPath.split('.');
    let tmpValue: any = info;
    keys.forEach(key => {
      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
      // @ts-ignore
      tmpValue = tmpValue[key];
    });
    setLoading(true);
    const res = await editStoryRevenueTaskInfo({
      data: {
        meegoId: info.meegoInfo.id,
        periodId: info.reviewPeriodId,
        keyPath,
        newValue,
        tmpValue,
        editUserEmail: userSettingState.info.email,
        columnConfig,
      },
    });
    if (res.result === StoryRevenueTaskInfoEditResultType.Success) {
      Toast.success('更新成功');
    } else if (res.result === StoryRevenueTaskInfoEditResultType.Conflict) {
      Toast.error('更新失败，源数据已更改，请刷新页面重试');
    } else {
      Toast.error('更新失败，未知错误');
    }
    setLoading(false);
    if (res.newTakInfo) {
      updateTaskInfo(res.newTakInfo);
    }
    return res.result === StoryRevenueTaskInfoEditResultType.Success;
  };

  const onEditSubRevenueInfo = async (
    info: StoryRevenueTaskInfo,
    keyPath: string,
    tmpValue: any,
    newValue: any,
    columnConfig: StoryRevenueTableColumnConfig,
    flightId?: string,
    publishedApp?: string,
  ): Promise<boolean> => {
    setLoading(true);
    const res = await editStoryRevenueTaskSubRevenueInfo({
      data: {
        meegoId: info.meegoInfo.id,
        periodId: info.reviewPeriodId,
        keyPath,
        newValue,
        tmpValue,
        editUserEmail: userSettingState.info.email,
        flightId,
        publishedApp,
        columnConfig,
      },
    });
    if (res.result === StoryRevenueTaskInfoEditResultType.Success) {
      Toast.success('更新成功');
    } else if (res.result === StoryRevenueTaskInfoEditResultType.Conflict) {
      Toast.error('更新失败，源数据已更改，请刷新页面重试');
    } else {
      Toast.error('更新失败，未知错误');
    }
    setLoading(false);
    if (res.newTakInfo) {
      updateTaskInfo(res.newTakInfo);
    }
    return res.result === StoryRevenueTaskInfoEditResultType.Success;
  };

  const transRevenueInfo = (
    flightId: string,
    flightInfo?: StoryRevenueTaskSubExperimentInfo,
    revenueInfo?: StoryRevenueTaskRevenueInfo,
    customVid?: string,
  ) => {
    const keyProcessConclisionLength = revenueInfo?.keyProcessRevenueMetricAutoComputedConclusion?.length ?? 0;
    const moduleConclusionLength = revenueInfo?.moduleRevenueMetricAutoComputedConclusion?.length ?? 0;
    const clientConclusionLength = revenueInfo?.platformRevenueMetricAutoComputedConclusion?.length ?? 0;
    if (keyProcessConclisionLength === 0 && moduleConclusionLength === 0 && clientConclusionLength === 0) {
      return [{ flightId, vid: customVid ?? '', flightInfo } as RevenueConclusionInfo];
    }
    if (
      !revenueInfo?.keyProcessRevenueMetricAutoComputedConclusion &&
      !revenueInfo?.moduleRevenueMetricAutoComputedConclusion &&
      !revenueInfo?.platformRevenueMetricAutoComputedConclusion
    ) {
      return [{ flightId, vid: customVid ?? '' } as RevenueConclusionInfo];
    }
    const groupedRevenueInfos: { [key: string]: RevenueConclusionInfo } = {};
    revenueInfo.platformRevenueMetricAutoComputedConclusion?.map(it => {
      let info = groupedRevenueInfos[customVid ?? it.vid];
      if (!info) {
        groupedRevenueInfos[customVid ?? it.vid] = {
          flightId,
          vid: customVid ?? it.vid,
          flightInfo,
        } as RevenueConclusionInfo;
      }
      info = groupedRevenueInfos[customVid ?? it.vid];
      info.hasPlatformRevenue = it.hasRevenue;
      info.platformRevenueMetricAutoComputedConclusion = it;
    });
    revenueInfo.moduleRevenueMetricAutoComputedConclusion?.map(it => {
      let info = groupedRevenueInfos[customVid ?? it.vid];
      if (!info) {
        groupedRevenueInfos[customVid ?? it.vid] = {
          flightId,
          vid: customVid ?? it.vid,
          flightInfo,
        } as RevenueConclusionInfo;
      }
      info = groupedRevenueInfos[customVid ?? it.vid];
      info.hasModuleRevenue = it.hasRevenue;
      info.moduleRevenueMetricAutoComputedConclusion = it;
    });
    revenueInfo.keyProcessRevenueMetricAutoComputedConclusion?.map(it => {
      let info = groupedRevenueInfos[customVid ?? it.vid];
      if (!info) {
        groupedRevenueInfos[customVid ?? it.vid] = {
          flightId,
          vid: customVid ?? it.vid,
          flightInfo,
        } as RevenueConclusionInfo;
      }
      info = groupedRevenueInfos[customVid ?? it.vid];
      info.hasKeyProcessRevenue = it.hasRevenue;
      info.keyProcessRevenueMetricAutoComputedConclusion = it;
    });
    return Object.values(groupedRevenueInfos);
  };

  useEffect(() => {
    const headerConclusion =
      head(transRevenueInfo('head', undefined, taskInfo.revenueInfo, 'head')) ??
      ({
        flightId: 'head',
        vid: 'head',
      } as RevenueConclusionInfo);
    const infoDataSource = [headerConclusion];
    if ((taskInfo.experimentInfo?.subExperimentCount ?? 0) > 0) {
      taskInfo.experimentInfo?.subExperimentList?.map(it => {
        infoDataSource.push(...transRevenueInfo(it.libraFlightId, it, it.revenueInfo));
      });
    } else {
      taskInfo.publishedAppRevenueInfos?.map(it => {
        infoDataSource.push({ flightId: 'empty', vid: 'empty', appRevenueInfo: it } as RevenueConclusionInfo);
      });
    }
    setRevenueConclusionInfo(infoDataSource);
  }, [taskInfo]);

  const revenueEditable = (roleType: StoryRevenueRoleType) => {
    if (taskInfo.updating) {
      return false;
    }
    if (userRoleType === StoryRevenueRoleType.DataAnalyst || userRoleType === StoryRevenueRoleType.Admin) {
      return true;
    }
    if (periodLocked) {
      return false;
    }
    if (userRoleType === StoryRevenueRoleType.ProductManager) {
      return true;
    }
    return false;
  };

  const roleEditable = (roleType: StoryRevenueRoleType) => {
    if (taskInfo.updating) {
      return false;
    }

    if (userRoleType === StoryRevenueRoleType.DataAnalyst || userRoleType === StoryRevenueRoleType.Admin) {
      return true;
    }

    return false;
  };

  const featureRevenueSelector = (
    editable: boolean,
    columnId: string,
    keyPath: string,
    manuallyRevenue?: boolean,
    autoRevenue?: boolean,
  ) => {
    const editValue =
      manuallyRevenue !== undefined && manuallyRevenue !== null ? (manuallyRevenue ? 'true' : 'false') : undefined;
    const cloudValue = autoRevenue !== undefined && autoRevenue !== null ? (autoRevenue ? 'true' : 'false') : undefined;
    const cusEditable =
      ((cloudValue === undefined || cloudValue === null) && revenueEditable(userRoleType) && editable) ||
      roleEditable(userRoleType);
    return (
      <FeatureRevenueSelector
        yesColor={columnId === 'has_metric_loss' ? 'pink' : undefined}
        noColor={columnId === 'has_metric_loss' ? 'green' : undefined}
        taskInfo={taskInfo}
        editable={cusEditable}
        saveValue={async newValue => {
          const columnConfig = TableColumnConfigMap()[columnId];
          const res = await onEditTaskInfo(taskInfo, keyPath, newValue, columnConfig);
        }}
        manuallyRes={editValue}
        autoRes={cloudValue}
        hideLoading={true}
      />
    );
  };

  const tableColumns: ColumnProps<RevenueConclusionInfo>[] = [
    {
      title: '收益基本信息',
      dataIndex: 'flightId',
      width: 230,
      fixed: true,
      colSpan: 2,
      ellipsis: true,
      render: (text, record, index) => {
        if (index === 0) {
          const props: { [key: string]: any } = {};
          props.colSpan = 2;
          const children = <Text>{`总收益信息（关联实验${taskInfo.experimentInfo?.subExperimentCount ?? 0}个）`}</Text>;
          const renderObject: RenderReturnObject = { children, props };
          return renderObject;
        } else {
          const prevRecord = revenueConclusionInfo[index - 1];
          const props: { [key: string]: any } = {};
          // const flightInfo = taskInfo.experimentInfo?.subExperimentList?.find(
          // 	(it) => it.libraFlightId === record.flightId
          // );
          const { flightInfo } = record;
          if (flightInfo) {
            if (record.flightId === prevRecord.flightId) {
              props.rowSpan = 0;
            } else {
              props.rowSpan = revenueConclusionInfo.filter(it => it.flightId === record.flightId).length;
            }
            if (record.vid.length === 0) {
              props.colSpan = 2;
            }
          } else {
            props.colSpan = 2;
          }
          const reviewTypeMap: Record<string, string> = {};
          reviewTypeMap[StoryRevenueTaskSubExperimentStatus.Ended.toString()] = '已结束';
          reviewTypeMap[StoryRevenueTaskSubExperimentStatus.InProgress.toString()] = '进行中';
          reviewTypeMap[StoryRevenueTaskSubExperimentStatus.ToBeScheduled.toString()] = '待调度';
          reviewTypeMap[StoryRevenueTaskSubExperimentStatus.InDebug.toString()] = '调试中';
          reviewTypeMap[StoryRevenueTaskSubExperimentStatus.Paused.toString()] = '已暂停';
          reviewTypeMap[StoryRevenueTaskSubExperimentStatus.ToBeScheduledEnded.toString()] = '待调度结束';
          reviewTypeMap[StoryRevenueTaskSubExperimentStatus.Released.toString()] = '已上线';
          const children = (
            <Space vertical={true} align={'start'}>
              {flightInfo ? (
                <Text link={{ href: flightInfo?.libraUrl, target: '_blank' }}>{flightInfo?.libraTitle}</Text>
              ) : (
                <></>
              )}
              <Space>
                {flightInfo?.status !== undefined && flightInfo?.status !== null ? (
                  <Tag
                    color={tagColorArray[flightInfo.status % tagColorArray.length]}
                    style={{ maxWidth: '100%' }}
                    type="ghost"
                  >
                    {reviewTypeMap[flightInfo.status.toString()]}
                  </Tag>
                ) : (
                  <></>
                )}
                {flightInfo?.publishedApp ? (
                  <Tag
                    color={tagColorArray[flightInfo.publishedApp?.length ?? 0 % tagColorArray.length]}
                    style={{ maxWidth: '100%' }}
                    type="ghost"
                  >
                    {flightInfo.publishedApp}
                  </Tag>
                ) : record.appRevenueInfo?.publishedApp ? (
                  <Tag
                    color={tagColorArray[record.appRevenueInfo?.publishedApp?.length ?? 0 % tagColorArray.length]}
                    style={{ maxWidth: '100%' }}
                    type="ghost"
                  >
                    {record.appRevenueInfo?.publishedApp}
                  </Tag>
                ) : (
                  <></>
                )}
              </Space>
              {flightInfo && (
                <Space vertical={true} align={'start'}>
                  <Text size={'small'} style={{ color: 'rgba(var(--semi-grey-5), 1)' }}>
                    {Math.floor(new Date().getTime()) > flightInfo.endTime &&
                    (flightInfo.status === StoryRevenueTaskSubExperimentStatus.Ended ||
                      flightInfo.status === StoryRevenueTaskSubExperimentStatus.Released)
                      ? formatTimeDifference(flightInfo.startTime * 1000, flightInfo.endTime * 1000)
                      : formatTimeDifference(flightInfo.startTime * 1000, Math.floor(new Date().getTime()))}
                    ({libraConvertTimestampToDateStr(flightInfo.startTime, '/')} ~{' '}
                    {libraConvertTimestampToDateStr(flightInfo.endTime, '/')})
                  </Text>
                </Space>
              )}
            </Space>
          );
          const renderObject: RenderReturnObject = { children, props };
          return renderObject;
        }
      },
    },
    {
      title: '实验组',
      width: 200,
      fixed: true,
      dataIndex: 'vid',
      ellipsis: true,
      colSpan: 0,
      render: (text, record, index) => {
        const children = <Text>{`实验组VID：${record.vid}`}</Text>;
        const props: { [key: string]: any } = {};
        if (index === 0 || !record.flightInfo || record.vid.length === 0) {
          props.colSpan = 0;
        }
        const renderObject: RenderReturnObject = { children, props };
        return renderObject;
      },
    },
    {
      title: '是否为分析依据',
      width: 200,
      dataIndex: 'flightInfo.markAsAnalysisBasis',
      ellipsis: true,
      onCell: (record, rowIndex) => ({
        style: { width: '100%', height: 50, padding: 0 },
      }),
      render: (text, record, index) => {
        if (taskInfo.revenueInfo?.reviewType !== StoryRevenueTaskRevenueReviewType.ParticipateInTheEvaluation) {
          return <StoryRevenueSelfAssementEmptyCell hoverText={'不参与Review，无法编辑'} />;
        }
        let isAnalysisBasis: boolean | undefined;
        const prevRecord = revenueConclusionInfo[index - 1];
        const props: { [key: string]: any } = {};
        const { flightInfo } = record;
        if (flightInfo) {
          if (record.flightId === prevRecord.flightId) {
            props.rowSpan = 0;
          } else {
            props.rowSpan = revenueConclusionInfo.filter(it => it.flightId === record.flightId).length;
          }
        }
        if (index === 0) {
          const analyzeBasis = revenueConclusionInfo.find(
            it => it.flightInfo?.markAsAnalysisBasis || it.appRevenueInfo?.markAsAnalysisBasis,
          );
          if (analyzeBasis) {
            isAnalysisBasis = true;
          }
        } else {
          isAnalysisBasis = flightInfo ? flightInfo.markAsAnalysisBasis : record.appRevenueInfo?.markAsAnalysisBasis;
        }
        const children = (
          <Checkbox
            style={{ padding: 10 }}
            disabled={!roleEditable(userRoleType) || index === 0}
            checked={isAnalysisBasis}
            onChange={async e => {
              const { checked } = e.target;
              const columnConfig = TableColumnConfigMap()['is_analysis_basis'];
              await onEditSubRevenueInfo(
                taskInfo,
                'markAsAnalysisBasis',
                isAnalysisBasis,
                checked,
                columnConfig,
                flightInfo?.libraFlightId,
                record.appRevenueInfo?.publishedApp,
              );
            }}
          />
        );
        const renderObject: RenderReturnObject = { children, props };
        return renderObject;
      },
    },
    {
      width: 150,
      dataIndex: 'hasPlatformRevenue',
      ellipsis: true,
      title: '有端收益(PM)',
      onCell: (record, rowIndex) => ({
        style: { width: '100%', height: 50, padding: 0 },
      }),
      render: (text, record, index) => {
        let hasRevenue = index === 0 ? taskInfo.revenueInfo?.hasPlatformRevenue : record.hasPlatformRevenue;
        if (
          index === 0 &&
          taskInfo.revenueInfo?.reviewType !== StoryRevenueTaskRevenueReviewType.ParticipateInTheEvaluation &&
          hasRevenue === undefined
        ) {
          return <StoryRevenueSelfAssementEmptyCell hoverText={'不参与Review，无法编辑'} />;
        }
        if (index === 0) {
          return featureRevenueSelector(
            record.platformRevenueMetricAutoComputedConclusion === undefined && !periodLocked,
            'has_platform_revenue',
            'revenueInfo.manullyHasPlatformRevenue',
            taskInfo.revenueInfo?.manullyHasPlatformRevenue,
            taskInfo.revenueInfo?.hasPlatformRevenue,
          );
        }
        if (index > 0) {
          if (!record.platformRevenueMetricAutoComputedConclusion) {
            hasRevenue = undefined;
          } else if (record.platformRevenueMetricAutoComputedConclusion.significanceValues.length === 0) {
            hasRevenue = undefined;
          }
        }
        return (
          <YesOrNoSelector
            optionNames={['有', '无']}
            defaultValue={
              hasRevenue !== undefined && hasRevenue !== null
                ? hasRevenue
                  ? 'true'
                  : 'false'
                : index === 0
                  ? '待评估'
                  : 'unknown'
            }
            saveValue={async newValue => {
              const columnConfig = TableColumnConfigMap()['has_platform_revenue'];
              const res = await onEditTaskInfo(taskInfo, 'revenueInfo.hasPlatformRevenue', newValue, columnConfig);
              if (res) {
                record.hasPlatformRevenue = newValue;
              }
            }}
            editable={false}
          />
        );
      },
    },
    {
      width: 150,
      dataIndex: 'dsHasPlatformRevenue',
      ellipsis: true,
      title: '有端收益(DS)',
      onCell: (record, rowIndex) => ({
        style: { width: '100%', height: 50, padding: 0, background: 'rgba(var(--semi-light-blue-0), 0.75)' },
      }),
      onHeaderCell: (record, rowIndex) => ({
        style: { background: 'rgba(var(--semi-light-blue-0), 0.75)' },
      }),
      render: (text, record, index) => {
        const hasRevenue = taskInfo.revenueInfo?.dsHasPlatformRevenue;
        if (taskInfo.revenueInfo?.reviewType !== StoryRevenueTaskRevenueReviewType.ParticipateInTheEvaluation) {
          return <StoryRevenueSelfAssementEmptyCell hoverText={'不参与Review，无法编辑'} />;
        }
        if (index === 0) {
          return (
            <YesOrNoSelector
              editable={false}
              saveValue={async newValue => {
                const columnConfig = TableColumnConfigMap()['ds_has_platform_revenue'];
                await onEditTaskInfo(taskInfo, 'revenueInfo.dsHasPlatformRevenue', newValue, columnConfig);
              }}
              defaultValue={
                hasRevenue !== undefined && hasRevenue !== null
                  ? hasRevenue
                    ? 'true'
                    : 'false'
                  : index === 0
                    ? '待评估'
                    : 'unknown'
              }
              optionNames={['有', '无']}
            />
          );
        }
        const { flightInfo } = record;
        const prevRecord = revenueConclusionInfo[index - 1];
        const props: { [key: string]: any } = {};
        if (flightInfo) {
          if (record.flightId === prevRecord.flightId) {
            props.rowSpan = 0;
          } else {
            props.rowSpan = revenueConclusionInfo.filter(it => it.flightId === record.flightId).length;
          }
        }
        const subHasRevenue = record.flightInfo
          ? record.flightInfo.revenueInfo?.dsHasPlatformRevenue
          : record.appRevenueInfo?.dsHasPlatformRevenue;
        const children = (
          <YesOrNoSelector
            editable={roleEditable(userRoleType)}
            saveValue={async newValue => {
              const columnConfig = TableColumnConfigMap()['ds_has_platform_revenue'];
              await onEditSubRevenueInfo(
                taskInfo,
                record.flightInfo ? 'revenueInfo.dsHasPlatformRevenue' : 'dsHasPlatformRevenue',
                subHasRevenue,
                newValue,
                columnConfig,
                flightInfo?.libraFlightId,
                record.appRevenueInfo?.publishedApp,
              );
            }}
            defaultValue={
              subHasRevenue !== undefined && subHasRevenue !== null ? (subHasRevenue ? 'true' : 'false') : '待评估'
            }
            optionNames={['有', '无']}
          />
        );
        const renderObject: RenderReturnObject = { children, props };
        return renderObject;
      },
    },
    {
      width: 200,
      title: '端收益指标详情',
      ellipsis: true,
      dataIndex: 'platformRevenueMetricConclusion.hasRevenue',
      onCell: (record, rowIndex) => ({
        style: { width: '100%', padding: 0, height: 50 },
      }),
      render: (text, record, index) => {
        const conclusions = record.platformRevenueMetricAutoComputedConclusion;
        if (
          index === 0 &&
          taskInfo.revenueInfo?.reviewType !== StoryRevenueTaskRevenueReviewType.ParticipateInTheEvaluation &&
          !conclusions?.significanceValues?.length
        ) {
          return <StoryRevenueSelfAssementEmptyCell hoverText={'不参与Review，无法编辑'} />;
        }
        const cusEditable = revenueEditable(userRoleType) || roleEditable(userRoleType);
        return conclusions?.significanceValues?.length ? (
          <RevenueConclusionCell taskInfo={undefined} revenueInfo={undefined} conclusion={conclusions} />
        ) : index === 0 && record.platformRevenueMetricAutoComputedConclusion === undefined ? (
          <TableTextEditCell
            onTextUpdate={async newText => {
              const columnConfig = TableColumnConfigMap()['client_revenue_conclusion'];
              await onEditTaskInfo(taskInfo, 'revenueInfo.platformRevenueMetricConclusion', newText, columnConfig);
            }}
            cellTitle={'端收益指标详情'}
            initText={taskInfo.revenueInfo?.platformRevenueMetricConclusion}
            editable={cusEditable}
          />
        ) : (
          <div style={{ padding: 24 }}>-</div>
        );
      },
    },
    {
      width: 150,
      dataIndex: 'hasModuleRevenue',
      ellipsis: true,
      title: '有模块收益(PM)',
      onCell: (record, rowIndex) => ({
        style: { width: '100%', height: 50, padding: 0 },
      }),
      render: (text, record, index) => {
        let hasRevenue = index === 0 ? taskInfo.revenueInfo?.hasModuleRevenue : record.hasModuleRevenue;
        if (
          index === 0 &&
          taskInfo.revenueInfo?.reviewType !== StoryRevenueTaskRevenueReviewType.ParticipateInTheEvaluation &&
          hasRevenue === undefined
        ) {
          return <StoryRevenueSelfAssementEmptyCell hoverText={'不参与Review，无法编辑'} />;
        }
        if (index === 0) {
          return featureRevenueSelector(
            record.moduleRevenueMetricAutoComputedConclusion === undefined && !periodLocked,
            'has_module_revenue',
            'revenueInfo.manullyHasModuleRevenue',
            taskInfo.revenueInfo?.manullyHasModuleRevenue,
            taskInfo.revenueInfo?.hasModuleRevenue,
          );
        }
        if (index > 0) {
          if (!record.moduleRevenueMetricAutoComputedConclusion) {
            hasRevenue = undefined;
          } else if (record.moduleRevenueMetricAutoComputedConclusion.significanceValues.length === 0) {
            hasRevenue = undefined;
          }
        }
        return (
          <YesOrNoSelector
            optionNames={['有', '无']}
            defaultValue={
              hasRevenue !== undefined && hasRevenue !== null
                ? hasRevenue
                  ? 'true'
                  : 'false'
                : index === 0
                  ? '待评估'
                  : 'unknown'
            }
            saveValue={async newValue => {
              const columnConfig = TableColumnConfigMap()['has_module_revenue'];
              const res = await onEditTaskInfo(taskInfo, 'revenueInfo.hasModuleRevenue', newValue, columnConfig);
              if (res) {
                record.hasModuleRevenue = newValue;
              }
            }}
            editable={false}
          />
        );
      },
    },
    {
      width: 150,
      dataIndex: 'dsHasModuleRevenue',
      ellipsis: true,
      title: '有模块收益(DS)',
      onCell: (record, rowIndex) => ({
        style: { width: '100%', height: 50, padding: 0, background: 'rgba(var(--semi-light-blue-0), 0.75)' },
      }),
      onHeaderCell: (record, rowIndex) => ({
        style: { background: 'rgba(var(--semi-light-blue-0), 0.75)' },
      }),
      render: (text, record, index) => {
        const hasRevenue = taskInfo.revenueInfo?.dsHasModuleRevenue;
        if (taskInfo.revenueInfo?.reviewType !== StoryRevenueTaskRevenueReviewType.ParticipateInTheEvaluation) {
          return <StoryRevenueSelfAssementEmptyCell hoverText={'不参与Review，无法编辑'} />;
        }
        if (index === 0) {
          return (
            <YesOrNoSelector
              editable={false}
              saveValue={async newValue => {
                const columnConfig = TableColumnConfigMap()['ds_has_module_revenue'];
                await onEditTaskInfo(taskInfo, 'revenueInfo.dsHasModuleRevenue', newValue, columnConfig);
              }}
              defaultValue={
                hasRevenue !== undefined && hasRevenue !== null
                  ? hasRevenue
                    ? 'true'
                    : 'false'
                  : index === 0
                    ? '待评估'
                    : 'unknown'
              }
              optionNames={['有', '无']}
            />
          );
        }
        const { flightInfo } = record;
        const prevRecord = revenueConclusionInfo[index - 1];
        const props: { [key: string]: any } = {};
        if (flightInfo) {
          if (record.flightId === prevRecord.flightId) {
            props.rowSpan = 0;
          } else {
            props.rowSpan = revenueConclusionInfo.filter(it => it.flightId === record.flightId).length;
          }
        }
        const subHasRevenue = record.flightInfo
          ? record.flightInfo.revenueInfo?.dsHasModuleRevenue
          : record.appRevenueInfo?.dsHasModuleRevenue;
        const children = (
          <YesOrNoSelector
            editable={roleEditable(userRoleType)}
            saveValue={async newValue => {
              const columnConfig = TableColumnConfigMap()['ds_has_module_revenue'];
              await onEditSubRevenueInfo(
                taskInfo,
                record.flightInfo ? 'revenueInfo.dsHasModuleRevenue' : 'dsHasModuleRevenue',
                subHasRevenue,
                newValue,
                columnConfig,
                flightInfo?.libraFlightId,
                record.appRevenueInfo?.publishedApp,
              );
            }}
            defaultValue={
              subHasRevenue !== undefined && subHasRevenue !== null ? (subHasRevenue ? 'true' : 'false') : '待评估'
            }
            optionNames={['有', '无']}
          />
        );
        const renderObject: RenderReturnObject = { children, props };
        return renderObject;
      },
    },
    {
      width: 200,
      dataIndex: 'moduleRevenueMetricAutoComputedConclusion.hasRevenue',
      title: '模块收益指标详情',
      ellipsis: true,
      onCell: (record, rowIndex) => ({
        style: { width: '100%', padding: 0, height: 50 },
      }),
      render: (text, record, index) => {
        const conclusions = record.moduleRevenueMetricAutoComputedConclusion;
        if (
          index === 0 &&
          taskInfo.revenueInfo?.reviewType !== StoryRevenueTaskRevenueReviewType.ParticipateInTheEvaluation &&
          !conclusions?.significanceValues?.length
        ) {
          return <StoryRevenueSelfAssementEmptyCell hoverText={'不参与Review，无法编辑'} />;
        }
        const cusEditable = revenueEditable(userRoleType) || roleEditable(userRoleType);
        return conclusions?.significanceValues?.length ? (
          <RevenueConclusionCell taskInfo={undefined} revenueInfo={undefined} conclusion={conclusions} />
        ) : index === 0 && record.moduleRevenueMetricAutoComputedConclusion === undefined ? (
          <TableTextEditCell
            onTextUpdate={async newText => {
              const columnConfig = TableColumnConfigMap()['module_revenue_conclusion'];
              await onEditTaskInfo(taskInfo, 'revenueInfo.moduleRevenueMetricConclusion', newText, columnConfig);
            }}
            cellTitle={'模块收益指标详情'}
            initText={taskInfo.revenueInfo?.moduleRevenueMetricConclusion}
            editable={cusEditable}
          />
        ) : (
          <div style={{ padding: 24 }}>-</div>
        );
      },
    },
    {
      width: 150,
      dataIndex: 'hasKeyProcessRevenue',
      title: '有关键过程收益(PM)',
      ellipsis: true,
      onCell: (record, rowIndex) => ({
        style: { width: '100%', height: 50, padding: 0 },
      }),
      render: (text, record, index) => {
        let hasRevenue = index === 0 ? taskInfo.revenueInfo?.hasKeyProcessRevenue : record.hasKeyProcessRevenue;
        if (index > 0) {
          if (!record.keyProcessRevenueMetricAutoComputedConclusion) {
            hasRevenue = undefined;
          } else if (record.keyProcessRevenueMetricAutoComputedConclusion.significanceValues.length === 0) {
            hasRevenue = undefined;
          }
        }
        if (
          index === 0 &&
          taskInfo.revenueInfo?.reviewType !== StoryRevenueTaskRevenueReviewType.ParticipateInTheEvaluation &&
          hasRevenue === undefined
        ) {
          return <StoryRevenueSelfAssementEmptyCell hoverText={'不参与Review，无法编辑'} />;
        }
        if (index === 0) {
          return featureRevenueSelector(
            record.keyProcessRevenueMetricAutoComputedConclusion === undefined && !periodLocked,
            'has_key_process_revenue',
            'revenueInfo.manullyHasKeyProcessRevenue',
            taskInfo.revenueInfo?.manullyHasKeyProcessRevenue,
            taskInfo.revenueInfo?.hasKeyProcessRevenue,
          );
        }
        return (
          <YesOrNoSelector
            optionNames={['有', '无']}
            defaultValue={
              hasRevenue !== undefined && hasRevenue !== null
                ? hasRevenue
                  ? 'true'
                  : 'false'
                : index === 0
                  ? '待评估'
                  : 'unknown'
            }
            saveValue={async newValue => {
              const columnConfig = TableColumnConfigMap()['has_key_process_revenue'];
              const res = await onEditTaskInfo(taskInfo, 'revenueInfo.hasKeyProcessRevenue', newValue, columnConfig);
              if (res) {
                record.hasKeyProcessRevenue = newValue;
              }
            }}
            editable={false}
          />
        );
      },
    },
    {
      width: 150,
      dataIndex: 'dsHasKeyProcessRevenue',
      ellipsis: true,
      title: '有关键过程收益(DS)',
      onCell: (record, rowIndex) => ({
        style: { width: '100%', height: 50, padding: 0, background: 'rgba(var(--semi-light-blue-0), 0.75)' },
      }),
      onHeaderCell: (record, rowIndex) => ({
        style: { background: 'rgba(var(--semi-light-blue-0), 0.75)' },
      }),
      render: (text, record, index) => {
        const hasRevenue = taskInfo.revenueInfo?.dsHasKeyProcessRevenue;
        if (taskInfo.revenueInfo?.reviewType !== StoryRevenueTaskRevenueReviewType.ParticipateInTheEvaluation) {
          return <StoryRevenueSelfAssementEmptyCell hoverText={'不参与Review，无法编辑'} />;
        }
        if (index === 0) {
          return (
            <YesOrNoSelector
              editable={false}
              saveValue={async newValue => {
                const columnConfig = TableColumnConfigMap()['ds_has_key_process_revenue'];
                await onEditTaskInfo(taskInfo, 'revenueInfo.dsHasKeyProcessRevenue', newValue, columnConfig);
              }}
              defaultValue={
                hasRevenue !== undefined && hasRevenue !== null
                  ? hasRevenue
                    ? 'true'
                    : 'false'
                  : index === 0
                    ? '待评估'
                    : 'unknown'
              }
              optionNames={['有', '无']}
            />
          );
        }
        const { flightInfo } = record;
        const prevRecord = revenueConclusionInfo[index - 1];
        const props: { [key: string]: any } = {};
        if (flightInfo) {
          if (record.flightId === prevRecord.flightId) {
            props.rowSpan = 0;
          } else {
            props.rowSpan = revenueConclusionInfo.filter(it => it.flightId === record.flightId).length;
          }
        }
        const subHasRevenue = record.flightInfo
          ? record.flightInfo.revenueInfo?.dsHasKeyProcessRevenue
          : record.appRevenueInfo?.dsHasKeyProcessRevenue;
        const children = (
          <YesOrNoSelector
            editable={roleEditable(userRoleType)}
            saveValue={async newValue => {
              const columnConfig = TableColumnConfigMap()['ds_has_key_process_revenue'];
              await onEditSubRevenueInfo(
                taskInfo,
                record.flightInfo ? 'revenueInfo.dsHasKeyProcessRevenue' : 'dsHasKeyProcessRevenue',
                subHasRevenue,
                newValue,
                columnConfig,
                flightInfo?.libraFlightId,
                record.appRevenueInfo?.publishedApp,
              );
            }}
            defaultValue={
              subHasRevenue !== undefined && subHasRevenue !== null ? (subHasRevenue ? 'true' : 'false') : '待评估'
            }
            optionNames={['有', '无']}
          />
        );
        const renderObject: RenderReturnObject = { children, props };
        return renderObject;
      },
    },
    {
      width: 200,
      ellipsis: true,
      title: '过程指标收益详情',
      dataIndex: 'keyProcessRevenueMetricAutoComputedConclusion.hasRevenue',
      onCell: (record, rowIndex) => ({
        style: { width: '100%', padding: 0, height: 50 },
      }),
      render: (text, record, index) => {
        const conclusions = record.keyProcessRevenueMetricAutoComputedConclusion;
        if (
          index === 0 &&
          taskInfo.revenueInfo?.reviewType !== StoryRevenueTaskRevenueReviewType.ParticipateInTheEvaluation &&
          !conclusions?.significanceValues?.length
        ) {
          return <StoryRevenueSelfAssementEmptyCell hoverText={'不参与Review，无法编辑'} />;
        }
        const cusEditable = revenueEditable(userRoleType) || roleEditable(userRoleType);
        return conclusions?.significanceValues?.length ? (
          <RevenueConclusionCell taskInfo={undefined} revenueInfo={undefined} conclusion={conclusions} />
        ) : index === 0 && record.keyProcessRevenueMetricAutoComputedConclusion === undefined ? (
          <TableTextEditCell
            onTextUpdate={async newText => {
              const columnConfig = TableColumnConfigMap()['process_revenue_conclusion'];
              await onEditTaskInfo(taskInfo, 'revenueInfo.keyProcessRevenueMetricConclusion', newText, columnConfig);
            }}
            cellTitle={'过程指标收益详情'}
            initText={taskInfo.revenueInfo?.keyProcessRevenueMetricConclusion}
            editable={cusEditable}
          />
        ) : (
          <div style={{ padding: 24 }}>-</div>
        );
      },
    },
    {
      width: 150,
      dataIndex: 'hasMetricLoss',
      title: '有指标损失',
      ellipsis: true,
      onCell: (record, rowIndex) => ({
        style: { width: '100%', height: 50, padding: 0 },
      }),
      render: (text, record, index) => {
        let hasMetricLoss: boolean | undefined;
        if (index === 0) {
          hasMetricLoss = taskInfo.revenueInfo?.hasMetricLoss;
        } else {
          let { hasPlatformRevenue } = record;
          if (!record.platformRevenueMetricAutoComputedConclusion) {
            hasPlatformRevenue = undefined;
          } else if (record.platformRevenueMetricAutoComputedConclusion.significanceValues.length === 0) {
            hasPlatformRevenue = undefined;
          }
          let { hasModuleRevenue } = record;
          if (!record.moduleRevenueMetricAutoComputedConclusion) {
            hasModuleRevenue = undefined;
          } else if (record.moduleRevenueMetricAutoComputedConclusion.significanceValues.length === 0) {
            hasModuleRevenue = undefined;
          }
          if (hasPlatformRevenue || hasModuleRevenue) {
            hasMetricLoss = false;
          } else if (hasPlatformRevenue === false || hasModuleRevenue === false) {
            hasMetricLoss = true;
          }
        }
        if (
          index === 0 &&
          taskInfo.revenueInfo?.reviewType !== StoryRevenueTaskRevenueReviewType.ParticipateInTheEvaluation &&
          hasMetricLoss === undefined
        ) {
          return <StoryRevenueSelfAssementEmptyCell hoverText={'不参与Review，无法编辑'} />;
        }
        if (index === 0) {
          return featureRevenueSelector(
            !periodLocked,
            'has_metric_loss',
            'revenueInfo.manuallyHasMetricLoss',
            taskInfo.revenueInfo?.manuallyHasMetricLoss,
            taskInfo.revenueInfo?.hasMetricLoss,
          );
        }
        return (
          <YesOrNoSelector
            yesColor={'pink'}
            noColor={'green'}
            optionNames={['有', '无']}
            defaultValue={
              hasMetricLoss !== undefined && hasMetricLoss !== null
                ? hasMetricLoss
                  ? 'true'
                  : 'false'
                : index === 0
                  ? '待评估'
                  : 'unknown'
            }
            saveValue={async newValue => {
              const columnConfig = TableColumnConfigMap()['has_metric_loss'];
              const res = await onEditTaskInfo(taskInfo, 'revenueInfo.hasMetricLoss', newValue, columnConfig);
              if (res) {
                record.hasMetricLoss = newValue;
              }
            }}
            editable={false}
          />
        );
      },
    },
    {
      width: 200,
      dataIndex: 'revenueInfo.dsRevenueConclusionRemark',
      title: 'DS修正收益判断差异说明',
      onCell: (record, rowIndex) => ({
        style: { width: '100%', padding: 0, height: 50, background: 'rgba(var(--semi-light-blue-0), 0.75)' },
      }),
      onHeaderCell: (record, rowIndex) => ({
        style: { background: 'rgba(var(--semi-light-blue-0), 0.75)' },
      }),
      render: (text, record, index) => {
        if (taskInfo.revenueInfo?.reviewType !== StoryRevenueTaskRevenueReviewType.ParticipateInTheEvaluation) {
          return <StoryRevenueSelfAssementEmptyCell hoverText={'不参与Review，无法编辑'} />;
        }
        const { flightInfo } = record;
        const prevRecord = revenueConclusionInfo[index - 1];
        const props: { [key: string]: any } = {};
        if (flightInfo) {
          if (record.flightId === prevRecord.flightId) {
            props.rowSpan = 0;
          } else {
            props.rowSpan = revenueConclusionInfo.filter(it => it.flightId === record.flightId).length;
          }
        }
        let remarkContent = taskInfo.revenueInfo?.dsRevenueConclusionRemark;
        if (index > 0) {
          if (record.flightInfo) {
            remarkContent = record.flightInfo.revenueInfo?.dsRevenueConclusionRemark;
          } else {
            remarkContent = record.appRevenueInfo?.dsRevenueConclusionRemark;
          }
        }
        const children = (
          <TableTextEditCell
            onTextUpdate={async newText => {
              const columnConfig = TableColumnConfigMap()['ds_revenue_conclusion_revenue'];
              if (index === 0) {
                await onEditTaskInfo(taskInfo, 'revenueInfo.dsRevenueConclusionRemark', newText, columnConfig);
              } else {
                await onEditSubRevenueInfo(
                  taskInfo,
                  record.flightInfo ? 'revenueInfo.dsRevenueConclusionRemark' : 'dsRevenueConclusionRemark',
                  remarkContent,
                  newText,
                  columnConfig,
                  flightInfo?.libraFlightId,
                  record.appRevenueInfo?.publishedApp,
                );
              }
            }}
            cellTitle={'DS修正收益判断差异说明'}
            initText={remarkContent}
            editable={roleEditable(userRoleType)}
          />
        );
        const renderObject: RenderReturnObject = { children, props };
        return renderObject;
      },
    },
    {
      width: 200,
      title: '操作',
      dataIndex: 'operation',
      colSpan: showAppEditButton ? 1 : 0,
      render: (text, record, index) => {
        const props: { [key: string]: any } = {};
        props.colSpan = showAppEditButton ? 1 : 0;
        const children =
          index === 0 ? (
            <>-</>
          ) : (
            <Popconfirm
              title={'删除此App收益信息？'}
              onConfirm={async () => {
                const newInfo = await deleteTaskAppRevenue({
                  data: {
                    meegoId: taskInfo.meegoInfo.id,
                    periodId: taskInfo.reviewPeriodId,
                    appName: record.appRevenueInfo?.publishedApp ?? '',
                  },
                });
                if (newInfo) {
                  updateTaskInfo(newInfo);
                }
              }}
            >
              <Button icon={<IconDelete />} type={'danger'} />
            </Popconfirm>
          );
        const renderObject: RenderReturnObject = { children, props };
        return renderObject;
      },
    },
    {
      width: 200,
      title: 'DS Review人',
      dataIndex: 'dsReviewer.name',
      onCell: (record, rowIndex) => ({
        style: { width: '100%', padding: 0, height: 50 },
      }),
      render: (text, record, index) => {
        const props: { [key: string]: any } = {};
        props.rowSpan = index === 0 ? revenueConclusionInfo.length : 0;
        const children = (
          <EditableUserGroupCell
            taskInfo={taskInfo}
            users={
              taskInfo.revenueInfo?.dsReviewer
                ? StoryRevenueConvertCopyUserToUser(taskInfo.revenueInfo?.dsReviewer)
                : undefined
            }
            editable={roleEditable(userRoleType)}
            updateTaskInfo={updateTaskInfo}
            keyPath={'revenueInfo.dsReviewer'}
            columnConfig={TableColumnConfigMap()['ds_reviewer']}
          />
        );
        const renderObject: RenderReturnObject = { children, props };
        return renderObject;
      },
    },
  ];

  const descStyle = {
    boxShadow: 'var(--semi-shadow-elevated)',
    backgroundColor: 'var(--semi-color-bg-2)',
    borderRadius: '4px',
    padding: '10px',
    width: '100%',
  };

  const descData = [
    {
      key: '需求名称',
      value: (
        <Space vertical={false}>
          <Title heading={6} link={{ href: taskInfo.meegoInfo.url, target: '_blank' }} ellipsis={true}>
            {taskInfo.meegoInfo.name}
          </Title>
          {taskInfo.revenueInfo?.isFullyRelease && (
            <Tag size="small" color="green">
              本次计划上线/推全
            </Tag>
          )}
        </Space>
      ),
    },
  ];

  return (
    <>
      <Button
        theme={'borderless'}
        type={'tertiary'}
        icon={<IconEyeOpened />}
        onClick={() => {
          setSheetVisible(true);
        }}
        style={{ height: 23 }}
        size={'small'}
      >
        详情
      </Button>
      <SideSheet
        visible={sheetVisible}
        onCancel={() => {
          setSheetVisible(false);
          if (taskInfo.revenueInfo?.reviewType !== StoryRevenueTaskRevenueReviewType.ParticipateInTheEvaluation) {
            return;
          }
          autoSyncAnalyzeBasisConclusion({
            data: {
              meegoId: taskInfo.meegoInfo.id,
              periodId: taskInfo.reviewPeriodId,
            },
          }).then(res => {
            if (res) {
              updateTaskInfo(res);
            }
          });
        }}
        width={'80%'}
        title={'需求收益信息'}
        // bodyStyle={{ width: "98%" }}
      >
        <div style={{ height: 3 }} />
        <Descriptions data={descData} row style={descStyle} />
        <div style={{ height: 10 }} />
        <Table
          loading={loading}
          columns={tableColumns}
          dataSource={revenueConclusionInfo}
          onRow={(record: RevenueConclusionInfo, index: number) => ({
            onMouseEnter: noop,
          })}
          scroll={{
            y: '180%',
          }}
          bordered={true}
          rowKey={'vid'}
          resizable={true}
          pagination={false}
          sticky={true}
        />
        <div style={{ height: 10 }} />
        {showAppEditButton && (
          <AppAppendModal taskInfo={taskInfo} updateTaskInfo={updateTaskInfo} userRoleType={userRoleType} />
        )}
      </SideSheet>
    </>
  );
};

export default FeatureRevenueInfoSideSheet;
