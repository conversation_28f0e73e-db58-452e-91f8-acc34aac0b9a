import React, { useEffect, useState } from 'react';
import UserAvatarGroup from '@/component/UserAvatarGroup';
import { Button, Spin, Toast } from '@douyinfe/semi-ui';
import { IconEditStroked } from '@douyinfe/semi-icons';
import SemiUserSelector from '@/component/SemiUserSelector';
import { User } from '@pa/shared/dist/src/core';
import {
  StoryRevenueTaskInfo,
  StoryRevenueTaskInfoEditResultType,
} from '@shared/storyRevenueReviewPlatform/StoryRevenueTaskInfo';
import { StoryRevenueTableColumnConfig } from '@shared/storyRevenueReviewPlatform/StoryRevenueUserSettings/StoryRevenueTableColumnUserSetting';
import { editStoryRevenueTaskInfo } from '@api/storyRevenueReviewPlatform';
import { useModel } from '@edenx/runtime/model';
import UserSettingModule from '@/model/userSettingModel';

const EditableUserGroupCell: React.FC<{
  taskInfo: StoryRevenueTaskInfo;
  users?: User[];
  editable: boolean;
  updateTaskInfo: (newTaskInfo: StoryRevenueTaskInfo) => void;
  keyPath: string;
  columnConfig: StoryRevenueTableColumnConfig;
}> = ({ users, editable, updateTaskInfo, keyPath, taskInfo, columnConfig }) => {
  const [focus, setFocus] = useState(false);
  const [editState, setEditState] = useState(false);
  const [loading, setLoading] = useState(false);
  const [userSettingState] = useModel(UserSettingModule);

  const [selectedUsers, setSeletcedUsers] = useState<User[]>([]);

  useEffect(() => {
    setSeletcedUsers(users ?? []);
  }, [users]);

  const handleMouseEnter = () => {
    setFocus(true);
  };

  const handleMouseLeave = () => {
    setFocus(false);
  };

  const saveUsers = async (newUsers: User[]) => {
    const oldEmails = users?.map(it => it.email);
    const newEmails = newUsers.map(it => it.email);
    const compareArray = (lh: any[], rh: any[]): boolean => {
      if (lh.length !== rh.length) {
        return false;
      }
      for (let i = 0; i < lh.length; i++) {
        if (lh[i] !== rh[i]) {
          return false;
        }
      }
      return true;
    };
    if (compareArray(oldEmails ?? [], newEmails ?? [])) {
      return;
    }
    setLoading(true);
    const res = await editStoryRevenueTaskInfo({
      data: {
        meegoId: taskInfo.meegoInfo.id,
        periodId: taskInfo.reviewPeriodId,
        keyPath,
        newValue: newUsers,
        tmpValue: users,
        editUserEmail: userSettingState.info.email,
        columnConfig,
      },
    });
    if (res.result === StoryRevenueTaskInfoEditResultType.Success) {
      Toast.success('更新成功');
    } else if (res.result === StoryRevenueTaskInfoEditResultType.Conflict) {
      Toast.error('更新失败，源数据已更改，请刷新页面重试');
    } else {
      Toast.error('更新失败，未知错误');
    }
    setLoading(false);
    if (res.newTakInfo) {
      updateTaskInfo(res.newTakInfo);
    }
    return res.result === StoryRevenueTaskInfoEditResultType.Success;
  };

  return (
    <div
      onMouseEnter={() => handleMouseEnter()}
      onMouseLeave={() => handleMouseLeave()}
      style={{
        width: '100%',
        height: '100%',
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        padding: 8,
      }}
    >
      {editState ? (
        <SemiUserSelector
          initUsers={selectedUsers}
          multi={true}
          updateSelectedUsers={user => {
            setSeletcedUsers(user);
          }}
          style={{ width: '100%' }}
          visibleChanged={visible => {
            if (!visible) {
              setEditState(false);
              setLoading(true);
              saveUsers(selectedUsers).then(res => {
                setLoading(false);
              });
            }
          }}
          defaultOpenUserList={true}
        />
      ) : (
        <div
          style={{
            padding: 6,
            display: 'flex',
            alignItems: 'center',
            width: '100%',
            position: 'relative',
          }}
        >
          <div
            style={{
              width: '100%',
              height: '100%',
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
            }}
          >
            <Spin spinning={loading}>
              <UserAvatarGroup users={selectedUsers ?? []} triggerType={'hover'} />
            </Spin>
            {focus ? (
              <Button
                icon={<IconEditStroked />}
                onClick={() => {
                  setEditState(true);
                }}
                theme="outline"
                style={{
                  backgroundColor: 'rgba(var(--semi-grey-0), 1)',
                }}
              />
            ) : (
              <></>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default EditableUserGroupCell;
