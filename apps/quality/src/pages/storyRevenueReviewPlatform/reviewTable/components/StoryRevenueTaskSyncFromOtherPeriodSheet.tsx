import React, { useEffect, useState } from 'react';
import {
  StoryRevenueTaskInfo,
  StoryRevenueTaskRevenueExpectationType,
  StoryRevenueTaskRevenueReviewType,
  StoryRevenueTaskRevenueSelfAssessmentType,
} from '@shared/storyRevenueReviewPlatform/StoryRevenueTaskInfo';
import {
  Banner,
  Button,
  Modal,
  Radio,
  SideSheet,
  Space,
  Spin,
  Table,
  Toast,
  Tooltip,
  Typography,
} from '@douyinfe/semi-ui';
import { IconAlertTriangle } from '@douyinfe/semi-icons';
import { StoryRevenueReviewPeriodInfo } from '@shared/storyRevenueReviewPlatform/StoryRevenueReviewPeriodInfo';
import {
  getAllActiveTaskInfoListByMeegoId,
  getAllActiveTaskInfoListWithPeriodInfoByMeegoId,
  mergeRevenueInfoWithTwoTaskInfos,
  updateStoryRevenueTask,
} from '@api/storyRevenueReviewPlatform';
import { StoryRevenueUserMemberInfo } from '@shared/storyRevenueReviewPlatform/StoryRevenueUserMemberInfo';
import StoryRevenueSelfAssementEmptyCell from '@/pages/storyRevenueReviewPlatform/reviewTable/components/StoryRevenueSelfAssementEmptyCell';

const { Text, Title } = Typography;

const StoryRevenueTaskSyncFromOtherPeriodSheet: React.FC<{
  visible: boolean;
  taskInfo: StoryRevenueTaskInfo;
  periodInfo: StoryRevenueReviewPeriodInfo;
  onTaskInfoUpdate: (newTaskInfo: StoryRevenueTaskInfo) => void;
  onVisibleChange: (visible: boolean) => void;
}> = ({ visible, taskInfo, periodInfo, onTaskInfoUpdate, onVisibleChange }) => {
  const [sheetVisible, setSheetVisible] = useState(false);
  const [loading, setLoading] = useState(false);
  const [saveLoading, setSaveLoading] = useState(false);
  const [dataSource, setDataSource] = useState<
    {
      taskInfo: StoryRevenueTaskInfo;
      periodInfo: StoryRevenueReviewPeriodInfo;
      latestUpdateTime: number;
    }[]
  >([]);
  const [selectedRowKey, setSelectedRowKey] = useState('');

  const handleSideSheetCancel = () => {
    onVisibleChange(false);
  };

  const changeUpdateStatus = async (updating: boolean, originTaskInfo: StoryRevenueTaskInfo) => {
    const newTaskInfo = {
      ...originTaskInfo,
      updating,
    };
    onTaskInfoUpdate(newTaskInfo);
  };

  const handleSideSheetConfirm = async () => {
    setSaveLoading(true);

    // 更新 Task UI，表示更新中
    await changeUpdateStatus(true, taskInfo);

    try {
      const selectedRow = dataSource.find(
        it => `${it.taskInfo.reviewPeriodId}-${it.taskInfo.meegoInfo.id}` === selectedRowKey,
      );
      if (!selectedRow || !selectedRow.taskInfo) {
        new Error('请选择要同步的需求！');
      } else {
        // 进行两个 task 的收益信息 Merge
        const result = await mergeRevenueInfoWithTwoTaskInfos({
          data: {
            taskInfo1: taskInfo,
            taskInfo2: selectedRow.taskInfo,
          },
        });
        if (!result.data) {
          new Error('同步需求收益信息失败！');
        }

        // 完成 Meego 更新，再次刷新 UI，表示刷新完毕
        await changeUpdateStatus(false, result.data as StoryRevenueTaskInfo);
        Toast.success(`跨周期同步收益信息成功！`);

        setSaveLoading(false);
        onVisibleChange(false);
      }
    } catch (error) {
      Toast.error(`跨周期同步收益信息，error: ${error}`);
      // 发生错误，兼容处理，恢复成原始状态
      await changeUpdateStatus(false, taskInfo);
      setSaveLoading(false);
    }
  };

  const CustomTableFooter: React.FC<{
    onCancel: () => void;
    onConfirm: () => void;
    onSaveLoading: boolean;
  }> = ({ onCancel, onConfirm, onSaveLoading }) => (
    <div style={{ float: 'right' }}>
      <Space vertical={false} spacing="medium">
        <Button disabled={onSaveLoading} onClick={onCancel}>
          取消
        </Button>
        <Button disabled={onSaveLoading} onClick={onConfirm} theme="solid" type="primary">
          确认
        </Button>
      </Space>
    </div>
  );

  const columns = [
    {
      title: '',
      dataIndex: 'selection',
      width: 20,
      render: (
        _: any,
        record: {
          taskInfo: StoryRevenueTaskInfo;
          periodInfo: StoryRevenueReviewPeriodInfo;
          latestUpdateTime: number;
        },
      ) => (
        <Radio
          checked={selectedRowKey === `${record.taskInfo.reviewPeriodId}-${record.taskInfo.meegoInfo.id}`}
          onChange={() => setSelectedRowKey(`${record.taskInfo.reviewPeriodId}-${record.taskInfo.meegoInfo.id}`)}
        />
      ),
    },
    {
      title: '收益review周期',
      dataIndex: 'reviewPeriod',
      width: 150,
      render: (
        _: any,
        record: {
          taskInfo: StoryRevenueTaskInfo;
          periodInfo: StoryRevenueReviewPeriodInfo;
          latestUpdateTime: number;
        },
      ) => (
        <div>
          <Text>{record.periodInfo.reviewPeriodName}</Text>
        </div>
      ),
    },
    {
      title: '是否参与季度需求收益评估',
      dataIndex: 'revenueInfo.reviewType',
      width: 250,
      render: (
        _: any,
        record: {
          taskInfo: StoryRevenueTaskInfo;
          periodInfo: StoryRevenueReviewPeriodInfo;
          latestUpdateTime: number;
        },
      ) => {
        if (!record.taskInfo.revenueInfo || record.taskInfo.revenueInfo.reviewType === undefined) {
          return <div />;
        }
        const reviewTypeMap: Record<string, string> = {};
        reviewTypeMap[StoryRevenueTaskRevenueReviewType.ParticipateInTheEvaluation.toString()] =
          '参与评估（能AA/AB严谨量化表现）';
        reviewTypeMap[StoryRevenueTaskRevenueReviewType.NotParticipateForExperimentNotOpen.toString()] =
          '不参与-优化迭代但未开实验';
        reviewTypeMap[StoryRevenueTaskRevenueReviewType.NotParticipateForNoConclusionYet.toString()] =
          '不参与-已开实验但尚无结论';
        reviewTypeMap[StoryRevenueTaskRevenueReviewType.NotParticipateForDataInfrastructure.toString()] =
          '不参与-数据基建/产品基建';
        reviewTypeMap[StoryRevenueTaskRevenueReviewType.NotParticipateForStoryUnderDevelopment.toString()] =
          '不参与-需求开发中';
        reviewTypeMap[StoryRevenueTaskRevenueReviewType.NotParticipateForStoryIsPending.toString()] =
          '不参与-需求pending';
        reviewTypeMap[StoryRevenueTaskRevenueReviewType.NotParticipateForLongTermCapacityBuilding.toString()] =
          '不参与-长期建设能力（后续需要参与评估）';
        reviewTypeMap[
          StoryRevenueTaskRevenueReviewType.NotParticipateForNegativeIssueGovernanceAndExperienceOpt.toString()
        ] = '不参与-负向问题治理&体验优化';
        reviewTypeMap[StoryRevenueTaskRevenueReviewType.NotParticipateForBugFix.toString()] = '不参与-bug修复[废弃]';
        reviewTypeMap[StoryRevenueTaskRevenueReviewType.NotParticipateForSecurityCompliance.toString()] =
          '不参与-安全合规降负反馈类[废弃]';

        return (
          <div>
            <Text>{reviewTypeMap[record.taskInfo.revenueInfo.reviewType.toString()]}</Text>
          </div>
        );
      },
    },
    {
      title: '收益自评',
      dataIndex: 'revenueInfo.expectationType',
      width: 150,
      render: (
        _: any,
        record: {
          taskInfo: StoryRevenueTaskInfo;
          periodInfo: StoryRevenueReviewPeriodInfo;
          latestUpdateTime: number;
        },
      ) => {
        if (!record.taskInfo.revenueInfo || record.taskInfo.revenueInfo.expectationType === undefined) {
          return <div />;
        }
        const expectationTypeMap: Record<string, string> = {};
        expectationTypeMap[StoryRevenueTaskRevenueExpectationType.MeetExpectations.toString()] = '符合预期';
        expectationTypeMap[StoryRevenueTaskRevenueExpectationType.ExceedExpectations.toString()] = '超出预期';
        expectationTypeMap[StoryRevenueTaskRevenueExpectationType.BelowExpectations.toString()] = '不及预期';

        return (
          <div>
            <Text>{expectationTypeMap[record.taskInfo.revenueInfo.expectationType.toString()]}</Text>
          </div>
        );
      },
    },
    {
      title: '自评原因',
      dataIndex: 'revenueInfo.revenueSelfAssessment',
      width: 150,
      render: (
        _: any,
        record: {
          taskInfo: StoryRevenueTaskInfo;
          periodInfo: StoryRevenueReviewPeriodInfo;
          latestUpdateTime: number;
        },
      ) => {
        if (
          !record.taskInfo.revenueInfo ||
          record.taskInfo.revenueInfo.revenueSelfAssessment === undefined ||
          record.taskInfo.revenueInfo.expectationType === undefined
        ) {
          return <div />;
        }

        const typeMap: Record<number, string> = {};
        if (record.taskInfo.revenueInfo.expectationType === StoryRevenueTaskRevenueExpectationType.BelowExpectations) {
          typeMap[StoryRevenueTaskRevenueSelfAssessmentType.BelowExpectationsAndContinuousOptimization] =
            '后续继续优化（产品决策正常）';
          typeMap[StoryRevenueTaskRevenueSelfAssessmentType.BelowExpectationsAndStopTrying] =
            '后续停止尝试（产品决策正常）';
          typeMap[StoryRevenueTaskRevenueSelfAssessmentType.BelowExpectationsForPMWrongDecision] = '产品决策失误';
        }
        if (record.taskInfo.revenueInfo.expectationType === StoryRevenueTaskRevenueExpectationType.MeetExpectations) {
          typeMap[StoryRevenueTaskRevenueSelfAssessmentType.MeetExpectationsForNegativeGovernance] =
            '负向治理（体验优化）';
          typeMap[StoryRevenueTaskRevenueSelfAssessmentType.MeetExpectationsForLongTermValue] =
            '长期价值（长期收益更大/基础能力建设）';
          typeMap[StoryRevenueTaskRevenueSelfAssessmentType.MeetExpectationsForLongTermExploration] =
            '长期探索（AI类需要保持耐心探索）';
          typeMap[StoryRevenueTaskRevenueSelfAssessmentType.MeetExpectationsForVerticalScenario] =
            '垂直场景（高级功能/渗透低的场景，不预期撬动端or模块）';
        }
        if (record.taskInfo.revenueInfo.expectationType === StoryRevenueTaskRevenueExpectationType.ExceedExpectations) {
          typeMap[StoryRevenueTaskRevenueSelfAssessmentType.ExceedExpectations] = '超出预期';
        }

        return (
          <div>
            <Text>{typeMap[record.taskInfo.revenueInfo.revenueSelfAssessment]}</Text>
          </div>
        );
      },
    },
    {
      title: '本次是否计划上线/推全',
      width: 200,
      dataIndex: 'revenueInfo.isFullyRelease',
      render: (
        _: any,
        record: {
          taskInfo: StoryRevenueTaskInfo;
          periodInfo: StoryRevenueReviewPeriodInfo;
          latestUpdateTime: number;
        },
      ) => {
        if (!record.taskInfo.revenueInfo || record.taskInfo.revenueInfo.isFullyRelease === undefined) {
          return <div />;
        }
        return (
          <div>
            <Text>{record.taskInfo.revenueInfo.isFullyRelease ? '是' : '暂无计划'}</Text>
          </div>
        );
      },
    },
    {
      title: '有端收益(PM)',
      width: 150,
      dataIndex: 'revenueInfo.hasPlatformRevenue',
      render: (
        _: any,
        record: {
          taskInfo: StoryRevenueTaskInfo;
          periodInfo: StoryRevenueReviewPeriodInfo;
          latestUpdateTime: number;
        },
      ) => {
        if (!record.taskInfo.revenueInfo) {
          return <div />;
        }
        if (record.taskInfo.revenueInfo.manullyHasPlatformRevenue !== undefined) {
          return (
            <div>
              <Text>{record.taskInfo.revenueInfo.manullyHasPlatformRevenue ? '有(已编辑)' : '无'}</Text>
            </div>
          );
        }
        if (record.taskInfo.revenueInfo.hasPlatformRevenue !== undefined) {
          return (
            <div>
              <Text>{record.taskInfo.revenueInfo.hasPlatformRevenue ? '有' : '无'}</Text>
            </div>
          );
        }

        return <div />;
      },
    },
    {
      title: '有模块收益(PM)',
      width: 150,
      dataIndex: 'revenueInfo.hasModuleRevenue',
      render: (
        _: any,
        record: {
          taskInfo: StoryRevenueTaskInfo;
          periodInfo: StoryRevenueReviewPeriodInfo;
          latestUpdateTime: number;
        },
      ) => {
        if (!record.taskInfo.revenueInfo) {
          return <div />;
        }
        if (record.taskInfo.revenueInfo.manullyHasModuleRevenue !== undefined) {
          return (
            <div>
              <Text>{record.taskInfo.revenueInfo.manullyHasModuleRevenue ? '有(已编辑)' : '无'}</Text>
            </div>
          );
        }
        if (record.taskInfo.revenueInfo.hasModuleRevenue !== undefined) {
          return (
            <div>
              <Text>{record.taskInfo.revenueInfo.hasModuleRevenue ? '有' : '无'}</Text>
            </div>
          );
        }

        return <div />;
      },
    },
    {
      title: '有关键过程收益(PM)',
      width: 150,
      dataIndex: 'revenueInfo.hasKeyProcessRevenue',
      render: (
        _: any,
        record: {
          taskInfo: StoryRevenueTaskInfo;
          periodInfo: StoryRevenueReviewPeriodInfo;
          latestUpdateTime: number;
        },
      ) => {
        if (!record.taskInfo.revenueInfo) {
          return <div />;
        }
        if (record.taskInfo.revenueInfo.manullyHasKeyProcessRevenue !== undefined) {
          return (
            <div>
              <Text>{record.taskInfo.revenueInfo.manullyHasKeyProcessRevenue ? '有(已编辑)' : '无'}</Text>
            </div>
          );
        }
        if (record.taskInfo.revenueInfo.hasKeyProcessRevenue !== undefined) {
          return (
            <div>
              <Text>{record.taskInfo.revenueInfo.hasKeyProcessRevenue ? '有' : '无'}</Text>
            </div>
          );
        }

        return <div />;
      },
    },
    {
      title: '最近修改时间',
      width: 150,
      dataIndex: 'latestUpdateTime',
      render: (
        _: any,
        record: {
          taskInfo: StoryRevenueTaskInfo;
          periodInfo: StoryRevenueReviewPeriodInfo;
          latestUpdateTime: number;
        },
      ) => {
        console.log(record);
        const date = new Date(record.latestUpdateTime * 1000);
        const ds = date.toLocaleString('zh-CN', {
          timeZone: 'Asia/Shanghai',
        });
        return <Text>{ds}</Text>;
      },
    },
  ];

  useEffect(() => {
    // 查询所有需求 id 相同的 task list（并且要求 review 周期是未数据存档状态）
    setLoading(true);
    getAllActiveTaskInfoListWithPeriodInfoByMeegoId({
      data: {
        meegoId: taskInfo.meegoInfo.id,
        currentReviewPeriodId: periodInfo.reviewPeriodId,
        filterCurrentReviewPeriod: true,
      },
    })
      .then(res => {
        if (!res) {
          Toast.error(`查询 meego id: ${taskInfo.meegoInfo.id} 关联的所有未存档 task info 失败！`);
          setLoading(false);
          return;
        }

        setLoading(false);
        setDataSource(res);
        if (res.length > 0) {
          setSelectedRowKey(`${res[0].taskInfo.reviewPeriodId}-${res[0].taskInfo.meegoInfo.id}`);
        }
      })
      .catch(err => {
        Toast.error(`查询 meego id: ${taskInfo.meegoInfo.id} 关联的所有未存档 task info 失败！error: ${err.message}`);
        setLoading(false);
      });
  }, []);

  return (
    <>
      <SideSheet
        title={
          <Space vertical={true} align={'start'} style={{ width: '100%' }}>
            <Title heading={4}>跨周期同步需求收益</Title>
            <Text link={{ href: `${taskInfo.meegoInfo.url}`, target: '_blank' }}>{taskInfo.meegoInfo.name}</Text>
          </Space>
        }
        closeOnEsc={true}
        visible={visible}
        width={1100}
        onCancel={handleSideSheetCancel}
        footer={
          <CustomTableFooter
            onCancel={handleSideSheetCancel}
            onConfirm={handleSideSheetConfirm}
            onSaveLoading={saveLoading || loading}
          />
        }
      >
        {loading ? (
          <Spin spinning={loading} />
        ) : (
          <div>
            <Space vertical={true} spacing={'tight'} style={{ width: '100%' }} align="start">
              <Banner
                type={'info'}
                closeIcon={null}
                style={{ width: '100%' }}
                fullMode={false}
                bordered={false}
                description={
                  <Text size={'small'} style={{ color: 'rgba(var(--semi-light-blue-7), 1)' }}>
                    【提示】请谨慎操作，同步后将覆盖当前需求的收益信息。
                  </Text>
                }
              />
              <Table dataSource={dataSource} columns={columns} />
            </Space>
          </div>
        )}
      </SideSheet>
    </>
  );
};

export default StoryRevenueTaskSyncFromOtherPeriodSheet;
