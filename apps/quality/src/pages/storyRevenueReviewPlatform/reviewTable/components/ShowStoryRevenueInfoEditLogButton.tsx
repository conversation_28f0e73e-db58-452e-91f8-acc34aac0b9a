import { StoryRevenueTaskInfo } from '@shared/storyRevenueReviewPlatform/StoryRevenueTaskInfo';
import React, { useEffect, useRef, useState } from 'react';
import { StoryRevenueTaskInfoEditAction } from '@shared/storyRevenueReviewPlatform/StoryRevenueUserSettings/StoryRevenueTaskInfoEditLog';
import { Button, Input, SideSheet, Space, Spin, Table, Tooltip, Typography } from '@douyinfe/semi-ui';
import SemiUserCard from '@/component/SemiUserCard';
import {
  fetchStoryRevenueMeegoEditLog,
  fetchStoryRevenueMeegoEditLogWithPeriodId,
} from '@api/storyRevenueReviewPlatform';
import { IconHistory, IconSearch } from '@douyinfe/semi-icons';
import {
  TableColumnConfigMap,
  transRawValue2LogDisplayValue,
} from '@shared/storyRevenueReviewPlatform/StoryRevenuePlatformUtils';
import { ColumnProps } from '@douyinfe/semi-ui/lib/es/table';

const { Text, Paragraph } = Typography;

interface StoryRevenueTaskInfoEditRowData {
  action: StoryRevenueTaskInfoEditAction;
  column_name: string;
  column_id: string;
  key_path: string;
}

const StoryRevenueInfoEditLogList: React.FC<{
  taskInfo: StoryRevenueTaskInfo;
}> = ({ taskInfo }) => {
  const [loading, setLoading] = useState(false);
  const [editLogData, setEditLogData] = useState<StoryRevenueTaskInfoEditRowData[]>([]);
  const searchInput = useRef<HTMLInputElement>(null);
  const [columnTypes, setColumnTypes] = useState<{ value: string; text: string }[]>([]);

  const editLogColumns: ColumnProps<StoryRevenueTaskInfoEditRowData>[] = [
    {
      title: '操作人',
      dataIndex: 'action.editor.email',
      width: '10%',
      render: (text, record, index) => (
        <SemiUserCard
          email={record.action.editor.email}
          simpleUserData={{
            avatarUrl:
              typeof record.action.editor.avatar === 'string'
                ? record.action.editor.avatar
                : record.action.editor.avatar?.avatar_240,
            name: record.action.editor.name,
          }}
        />
      ),
      onFilter: (value, info) =>
        info ? `${info.action.editor.email}`.toLowerCase().includes((value as string).toLowerCase()) : false,
      renderFilterDropdown: props => {
        if (!props) {
          return <></>;
        }
        const { tempFilteredValue, setTempFilteredValue, confirm, clear, close } = props;
        const handleChange = (value: string | undefined) => {
          const filteredValue = value ? [value] : [];
          setTempFilteredValue(filteredValue);
        };
        return (
          <Space vertical align="end" style={{ padding: 8 }}>
            <Input
              ref={searchInput}
              placeholder={`输入邮箱搜索`}
              value={tempFilteredValue[0]?.toString()}
              onChange={handleChange}
              onEnterPress={() => confirm({ filteredValue: tempFilteredValue })}
            />
            <Space>
              <Button onClick={() => confirm({ closeDropdown: true })} icon={<IconSearch />}>
                搜索
              </Button>
              <Button type={'tertiary'} onClick={() => clear({ closeDropdown: true })}>
                重置
              </Button>
            </Space>
          </Space>
        );
      },
    },
    {
      title: '操作字段',
      dataIndex: 'column_name',
      width: '15%',
      render: (text, record) => <Text>{record.column_name}</Text>,
      filters: columnTypes,
      onFilter: (value: string, record: StoryRevenueTaskInfoEditRowData | undefined) => record?.column_id === value,
    },
    {
      title: '操作详情',
      dataIndex: 'action.old_value',
      width: '65%',
      render: (text, record) => {
        let editDesc = `${transRawValue2LogDisplayValue(record.column_id, record.action.old_value)}  =>  ${transRawValue2LogDisplayValue(record.column_id, record.action.new_value)}`;
        if (record.key_path === 'updating') {
          editDesc = record.action.new_value ? '开始更新Meego信息' : 'Meego信息更新完成';
        }
        return <Paragraph>{editDesc}</Paragraph>;
      },
    },
    {
      title: '操作时间',
      dataIndex: 'action.edit_time',
      width: '10%',
      render: (text, record) => {
        const date = new Date(record.action.edit_time * 1000);
        const ds = date.toLocaleString('zh-CN', {
          timeZone: 'Asia/Shanghai',
        });
        return <Text>{ds}</Text>;
      },
    },
  ];

  useEffect(() => {
    setLoading(true);
    fetchStoryRevenueMeegoEditLogWithPeriodId({
      data: {
        meegoId: taskInfo.meegoInfo.id,
        periodId: taskInfo.reviewPeriodId,
      },
    }).then(res => {
      if (res) {
        const editDatas: StoryRevenueTaskInfoEditRowData[] = [];
        const columnIds: { value: string; text: string }[] = [];
        res.forEach(it1 => {
          let columnId = it1.column_config.column_id;
          const defaultColumnConfig = TableColumnConfigMap()[columnId];
          let columnName = defaultColumnConfig?.column_name ?? it1.column_config.column_name;
          if (it1.info_key_path === 'updating') {
            columnId = 'updating';
            columnName = '更新Meego信息';
          }
          it1.actions.forEach(it2 => {
            editDatas.push({
              action: it2,
              column_id: columnId,
              column_name: columnName,
              key_path: it1.info_key_path,
            } as StoryRevenueTaskInfoEditRowData);
          });
          if (!columnIds.find(it => it.value === columnId)) {
            columnIds.push({ value: columnId, text: columnName });
          }
        });
        setColumnTypes(columnIds);
        const sortedData = editDatas.sort((a, b) => b.action.edit_time - a.action.edit_time);
        setEditLogData(sortedData);
      }
      setLoading(false);
    });
  }, [taskInfo]);

  return (
    <Spin spinning={loading}>
      <Table
        columns={editLogColumns}
        keepDOM={false}
        pagination={{ pageSize: 10 }}
        dataSource={editLogData}
        rowKey={(record: StoryRevenueTaskInfoEditRowData) =>
          (record?.column_id ?? '') + (record?.action?.edit_time?.toString() ?? '')
        }
      />
    </Spin>
  );
};

const ShowStoryRevenueInfoEditLogButton: React.FC<{
  taskInfo: StoryRevenueTaskInfo;
  onVisibleChange: (visible: boolean) => void;
}> = ({ taskInfo, onVisibleChange }) => {
  const [modalVisible, setModalVisible] = useState(false);
  const [toolTipVisible, setToolTipVisible] = useState(false);

  useEffect(() => {
    if (modalVisible) {
      setToolTipVisible(false);
    }
  }, [modalVisible]);

  return (
    <>
      <Tooltip content={'操作记录'} visible={toolTipVisible}>
        <Button
          icon={<IconHistory />}
          theme="borderless"
          onClick={() => {
            setModalVisible(true);
            onVisibleChange(true);
          }}
          onMouseEnter={() => setToolTipVisible(true)}
          onMouseLeave={() => setToolTipVisible(false)}
          style={{ color: 'rgba(var(--semi-grey-5), 1)' }}
        />
        <SideSheet
          title={`${taskInfo.meegoInfo.name} - 操作记录`}
          visible={modalVisible}
          keepDOM={false}
          // afterClose={() => setModalVisible(false)}
          afterVisibleChange={visible => {
            if (!visible) {
              onVisibleChange(false);
            }
          }}
          onCancel={() => {
            setModalVisible(false);
          }}
          width={1200}
        >
          <StoryRevenueInfoEditLogList taskInfo={taskInfo} />
        </SideSheet>
      </Tooltip>
    </>
  );
};

export default ShowStoryRevenueInfoEditLogButton;
