import React, { useEffect, useState } from 'react';
import { Form, Toast, Spin } from '@douyinfe/semi-ui';
import {
  getUserInfoByEmail,
  queryStoryRevenueLibraMetaInfoByLibraApi,
  queryStoryRevenueLibraMetaInfoList,
} from '@api/storyRevenueReviewPlatform';
import { StoryRevenueLibraMetaInfo } from '@shared/storyRevenueReviewPlatform/StoryRevenueLibraMetaInfo';
import {
  StoryRevenueTaskInfo,
  StoryRevenueTaskSubExperimentInfo,
  StoryRevenueTaskSubExperimentStatus,
  StoryRevenueTaskSubExperimentBindType,
} from '@shared/storyRevenueReviewPlatform/StoryRevenueTaskInfo';
import { User } from '@pa/shared/dist/src/core';
import { FormApi } from '@douyinfe/semi-ui/lib/es/form';
import { Region } from '../../../../../api/utils/region';
import { LibraRegion } from '@shared/libra/commonLibra';
import { getMeegoAppNameByAppId, libraReportUrl2 } from '@shared/libra/libraManageUtils';

interface MeegoBindLibraInputProps {
  index: number;
  taskInfo: StoryRevenueTaskInfo;
  onLibraFlightIdParsed: (
    newTaskInfo: StoryRevenueTaskInfo,
    index: number,
    value: string,
    newParsedFlightId: string,
    fieldName: string,
  ) => void;
  onLibraFlightIdInParsing: (inParsing: boolean) => void;
  onEditing: (isEditing: boolean) => void;
  formApi: FormApi<any>;
  checkFlightIdExistedInDataSource: (flightId: string) => boolean;
}
const MeegoBindLibraInput: React.FC<MeegoBindLibraInputProps & React.ComponentProps<typeof Form.Input>> = ({
  index,
  taskInfo,
  onLibraFlightIdParsed,
  onLibraFlightIdInParsing,
  onEditing,
  formApi,
  checkFlightIdExistedInDataSource,
  ...props
}) => {
  const [value, setValue] = useState('');
  const [loading, setLoading] = useState(false);
  const [valid, setValid] = useState(false);
  const [lastParsedValue, setLastParsedValue] = useState<StoryRevenueLibraMetaInfo>();
  const [lastParsedNewTaskInfo, setLastParsedNewTaskInfo] = useState<StoryRevenueTaskInfo>(taskInfo);
  const [parseFailed, setParseFailed] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  let immediatelyParseFailed = parseFailed; // 避免 setState 机制偏慢
  // 正则表达式，匹配 Libra 链接
  const urlRegex =
    /^https:\/\/(data.bytedance|libra-sg.tiktok-row|libra-va.tiktok-row)\.net\/libra\/flight\/(\d{7,8})(\/.*)?$/;

  useEffect(() => {
    // 感知 loading 态变化，通知外部
    onLibraFlightIdInParsing(loading);
  }, [loading]);

  useEffect(() => {
    // 感知编辑状态变化，通知外部
    onEditing(isEditing);
  }, [isEditing]);

  const handleChange = (val: string) => {
    // 输入框内容变化处理
    setValue(val);
    // 内容变化，重置 parse 状态
    immediatelyParseFailed = false;
    setParseFailed(false);
  };

  const getFlightIdFromInputValue = (val: string): string => {
    const match = val.match(urlRegex);
    if (match) {
      return match[2] as string;
    }
    return '';
  };

  // 解析 Owner 字符串，获取 User Info List
  const getUserInfoList = async (ownersStr: string): Promise<User[]> => {
    const ownersArray = ownersStr.split(',');
    const userInfoList: User[] = [];
    for (const owner of ownersArray) {
      const email = `${owner}@bytedance.com`;
      const userInfo = await getUserInfoByEmail({
        data: {
          email,
        },
      });
      userInfoList.push(userInfo);
    }
    return userInfoList;
  };

  const isTheSameFlightId = (newUrl: string, oldUrl: string): boolean => {
    const newFlightId = getFlightIdFromInputValue(newUrl);
    const oldFlightId = getFlightIdFromInputValue(oldUrl);
    return newFlightId === oldFlightId && newFlightId.length > 0;
  };

  const handleParseFailed = () => {
    Toast.error(`解析 Libra 实验链接失败：${value}`);
    immediatelyParseFailed = true;
    setParseFailed(true);
    // Input 提示错误
    const { field } = props;
    setTimeout(() => {
      formApi.validate([field]);
    }, 500);
  };

  const handleFocus = () => {
    setIsEditing(true);
  };

  const handleBlur = () => {
    setIsEditing(false);

    // 失去焦点处理，解析 Libra 链接
    if (!valid) {
      // 当前链接非法，停止解析
      return;
    }

    setLoading(true); // 失去焦点后，显示 loading

    const flightId = getFlightIdFromInputValue(value);
    const flightIdNum = parseInt(flightId, 10);

    if (lastParsedValue && lastParsedValue.flightId === flightIdNum) {
      // 和上次解析的结果相同，不需要重新解析
      // 判断 dataSource 是否存在，如果不存在，则需要再次生成
      const existed = checkFlightIdExistedInDataSource(flightId);
      if (existed) {
        setLoading(false);
        return;
      }
    }

    let region = LibraRegion.UNKNOWN;
    if (value.includes('data.bytedance.net')) {
      region = LibraRegion.CN;
    } else if (value.includes('libra-sg.tiktok-row.net')) {
      region = LibraRegion.SG;
    } else if (value.includes('libra-va.tiktok-row.net')) {
      region = LibraRegion.VA;
    }

    queryStoryRevenueLibraMetaInfoByLibraApi({
      data: {
        flightId,
        region,
      },
    })
      .then(async result => {
        setLoading(false); // 操作完成后，隐藏 loading
        if (!result) {
          handleParseFailed();
          return;
        }

        // 取第一个解析结果
        const libraInfo = result;

        setLastParsedValue(libraInfo);
        immediatelyParseFailed = false;
        setParseFailed(false);
        Toast.success(
          // eslint-disable-next-line max-len
          `解析 Libra 实验链接成功！flightId: ${libraInfo.flightId}, appId: ${libraInfo.appId}`,
        );

        // 处理解析结果
        const libraInfoOwnerList = await getUserInfoList(libraInfo.owner);
        let newTaskInfo = { ...taskInfo };
        const publishedApp = getMeegoAppNameByAppId(libraInfo.appId);
        const subExperimentInfo: StoryRevenueTaskSubExperimentInfo = {
          libraFlightId: libraInfo.flightId.toString(),
          libraUrl: libraReportUrl2(libraInfo),
          libraTitle: libraInfo.display_name,
          libraOwners: libraInfoOwnerList,
          status: libraInfo.status as StoryRevenueTaskSubExperimentStatus,
          startTime: libraInfo.startTime,
          endTime: libraInfo.endTime,
          duration: libraInfo.duration,
          bindType: StoryRevenueTaskSubExperimentBindType.FromPaperAirplane,
          revenueInfo: {
            reviewPeriodId: taskInfo.reviewPeriodId,
          },
          publishedApp,
        };
        if (taskInfo.experimentInfo) {
          // 已经有实验，则追加
          const newSubExperimentList: StoryRevenueTaskSubExperimentInfo[] = [];
          newSubExperimentList.push(subExperimentInfo); // 将新添加的放最前面
          for (const experiment of taskInfo.experimentInfo.subExperimentList) {
            // 避免重复添加
            if (experiment.libraFlightId === subExperimentInfo.libraFlightId) {
              continue;
            }
            newSubExperimentList.push(experiment);
          }
          newTaskInfo = {
            ...taskInfo,
            experimentInfo: {
              subExperimentCount: newSubExperimentList.length,
              subExperimentList: newSubExperimentList,
            },
          };
        } else {
          // 否则绑定新的实验
          const experimentInfo = {
            subExperimentCount: 1,
            subExperimentList: [subExperimentInfo],
          };
          newTaskInfo = { ...taskInfo, experimentInfo };
        }

        // 结果回调
        setLastParsedNewTaskInfo(newTaskInfo);
        const { field } = props;
        onLibraFlightIdParsed(newTaskInfo, index, value, libraInfo.flightId.toString(), field);
      })
      .catch(error => {
        setLoading(false); // 操作完成后，隐藏 loading
        handleParseFailed();
      });
  };

  const handleValidate = (val: string) => {
    if (immediatelyParseFailed) {
      // 上次解析失败，不允许继续解析
      setValid(false);
      return 'Libra 实验链接解析失败，请检查实验 ID 是否正确';
    }

    const valTrim = val.trim(); // 去除空格
    // 校验 Libra 链接
    if (urlRegex.test(valTrim)) {
      // 检查是否已经被绑定
      const flightId = getFlightIdFromInputValue(valTrim);
      for (const experiment of taskInfo.experimentInfo?.subExperimentList || []) {
        if (experiment.libraFlightId === flightId) {
          const { initValue } = props;
          if (urlRegex.test(initValue)) {
            const initFlightId = getFlightIdFromInputValue(initValue);
            if (initFlightId === flightId) {
              // 校验成功（属于同样的 Flight Id）
              setValid(true);
              return '';
            }
          }

          // 校验失败
          setValid(false);
          return `Libra 实验已被绑定，id: ${flightId}`;
        }
      }

      // 校验成功
      setValid(true);
      return '';
    }

    // 校验失败
    setValid(false);
    return '请填写正确的 Libra 实验链接';
  };

  const handleReset = () => {
    setValue('');
    setValid(false);
    setLastParsedValue(undefined);
    setParseFailed(false);
  };

  return (
    <Form.Input
      {...props}
      onChange={handleChange}
      onBlur={handleBlur}
      onFocus={handleFocus}
      disabled={loading}
      validate={handleValidate}
      onReset={handleReset}
      suffix={loading && <Spin size={'small'} style={{ marginTop: 14 }} />}
    />
  );
};

export default MeegoBindLibraInput;
