import {
  StoryRevenuePublishedAppRevenueInfo,
  StoryRevenueTaskExperimentInfo,
  StoryRevenueTaskInfo,
  StoryRevenueTaskMeegoInfo,
  StoryRevenueTaskRevenueExpectationType,
  StoryRevenueTaskRevenueInfo,
  StoryRevenueTaskRevenueReviewType,
  StoryRevenueTaskRevenueSelfAssessmentType,
  StoryRevenueTaskStatus,
} from '@shared/storyRevenueReviewPlatform/StoryRevenueTaskInfo';
import { Button, Form, Tag, Toast } from '@douyinfe/semi-ui';
import React, { useEffect, useState } from 'react';
import { FormApi } from '@douyinfe/semi-ui/lib/es/form';
import { StoryRevenueRoleType } from '@shared/storyRevenueReviewPlatform/StoryRevenueUserMemberInfo';
import { StoryRevenueReviewPeriodInfo } from '@shared/storyRevenueReviewPlatform/StoryRevenueReviewPeriodInfo';
import { taskInfoEditable } from '@shared/storyRevenueReviewPlatform/StoryRevenuePlatformUtils';
import { submitStoryTaskInfo } from '@api/storyRevenueReviewPlatform';
import { useModel } from '@edenx/runtime/model';
import UserSettingModule from '@/model/userSettingModel';

interface MockStoryRevenueTaskInfo {
  terminated: boolean; // Task 是否终止
  status: StoryRevenueTaskStatus; // Task 状态（待评估、评估中、已归档）
  reviewPeriodId: string; // 所属的 Review 周期
  reviewDuration: number; // Review 时长
  fillInCompleted: boolean; // Task 是否填写完整
  meegoInfo: StoryRevenueTaskMeegoInfo; // Task Meego 需求信息
  experimentInfo: StoryRevenueTaskExperimentInfo; // Task 实验信息
  revenueInfo: StoryRevenueTaskRevenueInfo; // Task 收益信息
  updating?: boolean; // 是否正在更新数据
  lastManuallyBindLibraTime?: number; // 上次手动绑定 Libra 实验的时间
  publishedAppRevenueInfos?: StoryRevenuePublishedAppRevenueInfo[]; // 上线应用收益信息（在没有任何子实验的情况下由 DS 填写）
  revenueInfoCompleted?: boolean; // DS字段是否填写完整
}

const ExpectationTypeOptions = [
  {
    value: StoryRevenueTaskRevenueExpectationType.MeetExpectations,
    label: (
      <Tag style={{ display: 'flex', maxWidth: '100%' }} color={'blue'}>
        符合预期
      </Tag>
    ),
  },
  {
    value: StoryRevenueTaskRevenueExpectationType.ExceedExpectations,
    label: (
      <Tag style={{ display: 'flex', maxWidth: '100%' }} color={'green'}>
        超出预期
      </Tag>
    ),
  },
  {
    value: StoryRevenueTaskRevenueExpectationType.BelowExpectations,
    label: (
      <Tag style={{ display: 'flex', maxWidth: '100%' }} color={'pink'}>
        不及预期
      </Tag>
    ),
  },
];

const getSelfAssementOptions = (expectationType: StoryRevenueTaskRevenueExpectationType) => {
  const reviewTypeMap: Record<number, string> = {};
  if (expectationType === StoryRevenueTaskRevenueExpectationType.BelowExpectations) {
    reviewTypeMap[StoryRevenueTaskRevenueSelfAssessmentType.BelowExpectationsAndContinuousOptimization] =
      '后续继续优化（产品决策正常）';
    reviewTypeMap[StoryRevenueTaskRevenueSelfAssessmentType.BelowExpectationsAndStopTrying] =
      '后续停止尝试（产品决策正常）';
    reviewTypeMap[StoryRevenueTaskRevenueSelfAssessmentType.BelowExpectationsForPMWrongDecision] = '产品决策失误';
  }
  if (expectationType === StoryRevenueTaskRevenueExpectationType.MeetExpectations) {
    reviewTypeMap[StoryRevenueTaskRevenueSelfAssessmentType.MeetExpectationsForNegativeGovernance] =
      '负向治理（体验优化）';
    reviewTypeMap[StoryRevenueTaskRevenueSelfAssessmentType.MeetExpectationsForLongTermValue] =
      '长期价值（长期收益更大/基础能力建设）';
    reviewTypeMap[StoryRevenueTaskRevenueSelfAssessmentType.MeetExpectationsForLongTermExploration] =
      '长期探索（AI类需要保持耐心探索）';
    reviewTypeMap[StoryRevenueTaskRevenueSelfAssessmentType.MeetExpectationsForVerticalScenario] =
      '垂直场景（高级功能/渗透低的场景，不预期撬动端or模块）';
  }
  if (expectationType === StoryRevenueTaskRevenueExpectationType.ExceedExpectations) {
    reviewTypeMap[StoryRevenueTaskRevenueSelfAssessmentType.ExceedExpectations] = '超出预期';
  }
  const optionList = Object.keys(reviewTypeMap).map(it => ({
    value: Number(it),
    label: reviewTypeMap[Number(it)],
  }));
  return optionList;
};

export const TaskInfoSubmitForm: React.FC<{
  taskInfo?: StoryRevenueTaskInfo;
  userRoleType: StoryRevenueRoleType;
  periodInfo?: StoryRevenueReviewPeriodInfo;
  updateTaskInfo: (newInfo: StoryRevenueTaskInfo) => void;
}> = ({ taskInfo, userRoleType, periodInfo, updateTaskInfo }) => {
  const api = React.useRef<FormApi<MockStoryRevenueTaskInfo>>();
  const [showAlgorithmRelated, setShowAlgorithmRelated] = React.useState(false);
  const [editable, setEditable] = useState(false);
  const [submitLoading, setSubmitLoading] = useState(false);

  const [userSettingState] = useModel(UserSettingModule);

  useEffect(() => {
    if (!taskInfo || !periodInfo) {
      return;
    }
    setEditable(taskInfoEditable(taskInfo, userRoleType, periodInfo));
  }, [taskInfo, periodInfo, userRoleType]);

  useEffect(() => {
    if (taskInfo?.meegoInfo.primaryBusiness === 'UG') {
      setShowAlgorithmRelated(true);
      return;
    }
    if (
      taskInfo?.meegoInfo.primaryBusiness === '内容生态' &&
      taskInfo?.meegoInfo.secondaryBusiness === '模板框架与分发'
    ) {
      setShowAlgorithmRelated(true);
      return;
    }
    setShowAlgorithmRelated(false);
  }, [taskInfo]);

  const handleSubmit = async (values: MockStoryRevenueTaskInfo) => {
    if (!taskInfo || !periodInfo || !editable) {
      return;
    }
    setSubmitLoading(true);
    await submitStoryTaskInfo({
      data: {
        query: values,
        meegoId: taskInfo.meegoInfo.id,
        periodId: periodInfo.reviewPeriodId,
        userEmail: userSettingState.info.email ?? '',
      },
    }).then(res => {
      const newInfo = res as StoryRevenueTaskInfo;
      if (newInfo) {
        Toast.success('提交成功');
        updateTaskInfo(newInfo);
      } else {
        Toast.error('提交失败，请联系管理员');
      }
    });
    setSubmitLoading(false);
  };

  return (
    <Form layout="vertical" onSubmit={values => handleSubmit(values)} style={{ width: '90%' }}>
      {({ formState, values, formApi }) => (
        <>
          <Form.Select
            field={'revenueInfo.reviewType'}
            label={'是否参与季度需求收益评估'}
            rules={[{ required: true, message: '请选择是否参与季度需求收益评估' }]}
            optionList={[
              {
                label: '参与评估（能AA/AB严谨量化表现）',
                value: StoryRevenueTaskRevenueReviewType.ParticipateInTheEvaluation,
                disabled: false,
              },
              {
                label: '不参与-优化迭代但未开实验',
                value: StoryRevenueTaskRevenueReviewType.NotParticipateForExperimentNotOpen,
                disabled: false,
              },
              {
                label: '不参与-已开实验但尚无结论',
                value: StoryRevenueTaskRevenueReviewType.NotParticipateForNoConclusionYet,
                disabled: false,
              },
              {
                label: '不参与-数据基建/产品基建',
                value: StoryRevenueTaskRevenueReviewType.NotParticipateForDataInfrastructure,
                disabled: false,
              },
              {
                label: '不参与-需求开发中',
                value: StoryRevenueTaskRevenueReviewType.NotParticipateForStoryUnderDevelopment,
                disabled: false,
              },
              {
                label: '不参与-需求pending',
                value: StoryRevenueTaskRevenueReviewType.NotParticipateForStoryIsPending,
                disabled: false,
              },
              {
                label: '不参与-长期建设能力（后续需要参与评估）',
                value: StoryRevenueTaskRevenueReviewType.NotParticipateForLongTermCapacityBuilding,
                disabled: false,
              },
              {
                label: '不参与-负向问题治理&体验优化',
                value: StoryRevenueTaskRevenueReviewType.NotParticipateForNegativeIssueGovernanceAndExperienceOpt,
                disabled: false,
              },
              {
                label: '不参与-bug修复[废弃]',
                value: StoryRevenueTaskRevenueReviewType.NotParticipateForBugFix,
                disabled: true,
              },
              {
                label: '不参与-安全合规降负反馈类[废弃]',
                value: StoryRevenueTaskRevenueReviewType.NotParticipateForSecurityCompliance,
                disabled: true,
              },
            ]}
            onChange={value => {
              formApi.setValue('revenueInfo.reviewType', value);
            }}
            initValue={taskInfo?.revenueInfo?.reviewType}
            disabled={!editable}
            showClear={false}
            style={{ width: '100%' }}
          />
          {values.revenueInfo?.reviewType === StoryRevenueTaskRevenueReviewType.NotParticipateForNoConclusionYet && (
            <Form.Input
              field={'revenueInfo.noConclusionReason'}
              label={'无法回收结论的原因'}
              rules={[{ required: true, message: '请输入无法回收结论的原因' }]}
              placeholder={'原因如：实验开启时间短未到回收周期、人力待排期、反转实验、需求pending、待交接'}
              disabled={!editable}
              style={{ width: '100%' }}
              initValue={taskInfo?.revenueInfo?.noConclusionReason}
              showClear={false}
            />
          )}
          {showAlgorithmRelated &&
            values.revenueInfo?.reviewType === StoryRevenueTaskRevenueReviewType.ParticipateInTheEvaluation && (
              <Form.Select
                style={{ width: '100%' }}
                field={'meegoInfo.algorithmRelated'}
                label={'是否与算法相关（需求的落地是否和推荐搜索等合作）'}
                rules={[{ required: true, message: '请选择是否与算法相关' }]}
                optionList={[
                  // @ts-ignore
                  { label: '是', value: true },
                  // @ts-ignore
                  { label: '否', value: false },
                ]}
                // onChange={value => {
                //   formApi.setValue('meegoInfo.algorithmRelated', Boolean(value));
                // }}
                showClear={false}
                initValue={taskInfo?.meegoInfo?.algorithmRelated}
                disabled={!editable}
              />
            )}
          {/* {taskInfo?.meegoInfo?.publishedApps?.length === 1 &&*/}
          {/*  values.revenueInfo?.reviewType === StoryRevenueTaskRevenueReviewType.ParticipateInTheEvaluation && (*/}
          {/*    <Form.Input*/}
          {/*      field={'meegoInfo.singleAppReason'}*/}
          {/*      rules={[{ required: true, message: '请输入仅上单端的原因' }]}*/}
          {/*      label={{ text: '仅上单端的原因', required: true }}*/}
          {/*      disabled={!editable}*/}
          {/*      style={{ width: '100%' }}*/}
          {/*      initValue={taskInfo?.meegoInfo?.singleAppReason}*/}
          {/*      showClear={false}*/}
          {/*    />*/}
          {/*  )}*/}
          {values.revenueInfo?.reviewType === StoryRevenueTaskRevenueReviewType.ParticipateInTheEvaluation && (
            <Form.Select
              field={'revenueInfo.expectationType'}
              label={{ text: '收益自评', required: true }}
              rules={[{ required: true, message: '请选择收益自评' }]}
              optionList={ExpectationTypeOptions}
              onChange={value => {
                formApi.setValue('revenueInfo.expectationType', value);
                formApi.setValue('revenueInfo.revenueSelfAssessment', undefined);
              }}
              initValue={taskInfo?.revenueInfo?.expectationType}
              disabled={!editable}
              showClear={false}
              style={{ width: '100%' }}
            />
          )}
          {values.revenueInfo?.reviewType === StoryRevenueTaskRevenueReviewType.ParticipateInTheEvaluation &&
            values?.revenueInfo?.expectationType !== undefined &&
            values?.revenueInfo?.expectationType !== null && (
              <Form.Select
                field={'revenueInfo.revenueSelfAssessment'}
                rules={[{ required: true, message: '请选择收益自评' }]}
                label={{ text: '自评原因', required: true }}
                showClear={false}
                optionList={getSelfAssementOptions(values.revenueInfo.expectationType)}
                onChange={value => {
                  formApi.setValue('revenueInfo.revenueSelfAssessment', Number(value));
                }}
                initValue={taskInfo?.revenueInfo?.revenueSelfAssessment}
                disabled={!editable}
                style={{ width: '100%' }}
              />
            )}
          {values.revenueInfo?.reviewType === StoryRevenueTaskRevenueReviewType.ParticipateInTheEvaluation && (
            <Form.Select
              showClear={false}
              style={{ width: '100%' }}
              field={'revenueInfo.isFullyRelease'}
              rules={[{ required: true, message: '请确认本次是否计划上线/推全' }]}
              label={{ text: '本次是否计划上线/推全', required: true }}
              optionList={[
                // @ts-ignore
                { label: '是', value: true },
                // @ts-ignore
                { label: '暂无计划', value: false },
              ]}
              // onSelect={value => {
              //   const newValue = Boolean(value);
              //   formApi.setValue('revenueInfo.isFullyRelease', newValue);
              // }}
              // onChange={value => {
              //   const newValue = Boolean(value);
              //   formApi.setValue('revenueInfo.isFullyRelease', newValue);
              // }}
              initValue={taskInfo?.revenueInfo?.isFullyRelease}
              disabled={!editable}
            />
          )}
          {/* {values.revenueInfo?.reviewType === StoryRevenueTaskRevenueReviewType.ParticipateInTheEvaluation && (*/}
          {/*  <Form.Select*/}
          {/*    style={{ width: '100%' }}*/}
          {/*    field={'revenueInfo.hasPlatformRevenue'}*/}
          {/*    label={'有端收益'}*/}
          {/*    showClear={false}*/}
          {/*    optionList={[*/}
          {/*      { label: '有', value: Number(true) },*/}
          {/*      { label: '无', value: Number(false) },*/}
          {/*    ]}*/}
          {/*    onChange={value => {*/}
          {/*      if (value) {*/}
          {/*        formApi.setValue('revenueInfo.hasPlatformRevenue', Boolean(value));*/}
          {/*      }*/}
          {/*    }}*/}
          {/*    initValue={Number(taskInfo?.revenueInfo?.hasPlatformRevenue)}*/}
          {/*    disabled={!editable}*/}
          {/*  />*/}
          {/* )}*/}
          {/* {values.revenueInfo?.reviewType === StoryRevenueTaskRevenueReviewType.ParticipateInTheEvaluation && (*/}
          {/*  <Form.Select*/}
          {/*    showClear={false}*/}
          {/*    style={{ width: '100%' }}*/}
          {/*    field={'revenueInfo.hasModuleRevenue'}*/}
          {/*    label={'有模块收益'}*/}
          {/*    optionList={[*/}
          {/*      { label: '有', value: Number(true) },*/}
          {/*      { label: '无', value: Number(false) },*/}
          {/*    ]}*/}
          {/*    onChange={value => {*/}
          {/*      if (value) {*/}
          {/*        formApi.setValue('revenueInfo.hasModuleRevenue', Boolean(value));*/}
          {/*      }*/}
          {/*    }}*/}
          {/*    initValue={Number(taskInfo?.revenueInfo?.hasModuleRevenue)}*/}
          {/*    disabled={!editable}*/}
          {/*  />*/}
          {/* )}*/}
          {/* {values.revenueInfo?.reviewType === StoryRevenueTaskRevenueReviewType.ParticipateInTheEvaluation && (*/}
          {/*  <Form.Select*/}
          {/*    showClear={false}*/}
          {/*    style={{ width: '100%' }}*/}
          {/*    field={'revenueInfo.hasKeyProcessRevenue'}*/}
          {/*    label={'有关键过程收益'}*/}
          {/*    optionList={[*/}
          {/*      { label: '有', value: Number(true) },*/}
          {/*      { label: '无', value: Number(false) },*/}
          {/*    ]}*/}
          {/*    onChange={value => {*/}
          {/*      if (value) {*/}
          {/*        formApi.setValue('revenueInfo.hasKeyProcessRevenue', Boolean(value));*/}
          {/*      }*/}
          {/*    }}*/}
          {/*    initValue={Number(taskInfo?.revenueInfo?.hasKeyProcessRevenue)}*/}
          {/*    disabled={!editable}*/}
          {/*  />*/}
          {/* )}*/}
          {/* {values.revenueInfo?.reviewType === StoryRevenueTaskRevenueReviewType.ParticipateInTheEvaluation && (*/}
          {/*  <Form.Select*/}
          {/*    showClear={false}*/}
          {/*    style={{ width: '100%' }}*/}
          {/*    field={'revenueInfo.hasMetricLoss'}*/}
          {/*    label={'有指标损失'}*/}
          {/*    optionList={[*/}
          {/*      { label: '有', value: Number(true) },*/}
          {/*      { label: '无', value: Number(false) },*/}
          {/*    ]}*/}
          {/*    onChange={value => {*/}
          {/*      if (value) {*/}
          {/*        formApi.setValue('revenueInfo.hasMetricLoss', Boolean(value));*/}
          {/*      }*/}
          {/*    }}*/}
          {/*    initValue={Number(taskInfo?.revenueInfo?.hasMetricLoss)}*/}
          {/*    disabled={!editable}*/}
          {/*  />*/}
          {/* )}*/}
          <Button htmlType="submit" loading={submitLoading}>
            提交
          </Button>
        </>
      )}
    </Form>
  );
};
