import React, { useState } from 'react';
import { StoryRevenueTaskInfo } from '@shared/storyRevenueReviewPlatform/StoryRevenueTaskInfo';
import { Button, Modal, Space, Toast, Tooltip, Typography } from '@douyinfe/semi-ui';
import { IconAlertTriangle, IconSync } from '@douyinfe/semi-icons';
import { updateStoryRevenueTask, updateStoryRevenueTaskMeegoInfo } from '@api/storyRevenueReviewPlatform';
import { useModel } from '@edenx/runtime/model';
import UserSettingModule from '@/model/userSettingModel';

const { Text } = Typography;

const StoryRevenueTaskMeegoInfoUpdateButton: React.FC<{
  taskInfo: StoryRevenueTaskInfo;
  onTaskInfoUpdate: (newTaskInfo: StoryRevenueTaskInfo) => void;
}> = ({ taskInfo, onTaskInfoUpdate }) => {
  const [updateConfirmVisible, setUpdateConfirmVisible] = useState(false);
  const [userSettingState] = useModel(UserSettingModule);

  const changeUpdateStatus = async (updating: boolean, originTaskInfo: StoryRevenueTaskInfo) => {
    const newTaskInfo = {
      ...originTaskInfo,
      updating,
    };
    onTaskInfoUpdate(newTaskInfo);
  };

  // 确认更新 Meego Info
  const handleRefreshConfirmOK = async () => {
    setUpdateConfirmVisible(false);
    // 更新 Task UI，表示更新中
    await changeUpdateStatus(true, taskInfo);
    try {
      // 开始请求网络，拉取最新的 Meego 信息
      const result = await updateStoryRevenueTaskMeegoInfo({
        data: { taskInfo, userEmail: userSettingState.info.email },
      });
      if (result.code !== 0) {
        new Error(`${result.msg}`);
      }

      // 完成 Meego 更新，再次刷新 UI，表示刷新完毕
      await changeUpdateStatus(false, result.data);
      const updatedMeegoInfo = result.data.meegoInfo;
      if (updatedMeegoInfo && updatedMeegoInfo.name) {
        Toast.success(`需求：${result.data.meegoInfo.name}，更新 Meego 信息成功！`);
      } else {
        Toast.success(`需求更新 Meego 信息成功！`);
      }
    } catch (error) {
      // 发生错误，兼容处理，恢复成原始状态
      await changeUpdateStatus(false, taskInfo);
      Toast.error(`更新 Meego 信息失败，error: ${error}`);
    }
  };

  // 取消更新 Meego Info
  const handleRefreshConfirmCancel = () => {
    setUpdateConfirmVisible(false);
  };

  // 点击更新按钮
  const handleClickRefreshButton = () => {
    setUpdateConfirmVisible(true);
  };

  return (
    <>
      <Button
        theme="borderless"
        // disabled={taskInfo.updating}
        style={{ color: 'rgba(var(--semi-grey-5), 1)' }}
        onClick={handleClickRefreshButton}
      >
        从 Meego 同步需求信息
      </Button>
      {/* <Tooltip content={taskInfo.updating ? '数据更新中无法操作' : '更新 Meego 信息'}>*/}
      {/*  <Button*/}
      {/*    icon={<IconSync />}*/}
      {/*    theme="borderless"*/}
      {/*    // disabled={taskInfo.updating}*/}
      {/*    style={{ color: 'rgba(var(--semi-grey-5), 1)' }}*/}
      {/*    onClick={handleClickRefreshButton}*/}
      {/*  />*/}
      {/* </Tooltip>*/}
      <Modal
        icon={<IconAlertTriangle style={{ color: 'orange', fontSize: 24 }} />}
        title={'确认是否更新 Meego 信息？'}
        visible={updateConfirmVisible}
        onOk={handleRefreshConfirmOK}
        onCancel={handleRefreshConfirmCancel}
        closeOnEsc={true}
      >
        <>
          <Space vertical={false} align="start">
            <Text strong style={{ whiteSpace: 'nowrap' }}>
              需求:
            </Text>
            <Text strong link={{ href: taskInfo.meegoInfo.url, target: '_blank' }}>
              {taskInfo.meegoInfo.name}
            </Text>
          </Space>
          <br />
          <Space vertical={false} align="start">
            <Text>
              即将同步需求的 Meego 信息，过程耗时约 5~10s。同步过程中需求将处于
              <strong>不可编辑状态</strong>。
            </Text>
          </Space>
        </>
      </Modal>
    </>
  );
};

export default StoryRevenueTaskMeegoInfoUpdateButton;
