import React, { useEffect, useState } from 'react';
import { Button, Modal, Popover, Space, TextArea, Typography } from '@douyinfe/semi-ui';
import { IconEditStroked } from '@douyinfe/semi-icons';

const { Paragraph, Title, Text } = Typography;

const TableTextContentPopover: React.FC<{ values: string[] }> = ({ values }) => (
  <Space vertical={true} align={'start'} style={{ padding: 12, maxWidth: 800 }}>
    {values.map((value, idx) => (
      <Text key={idx.toString()}>{value}</Text>
    ))}
  </Space>
);

const TableTextEditCell: React.FC<{
  initText?: string;
  onTextUpdate: (newText?: string) => Promise<void>;
  cellTitle?: string;
  placeholder?: string;
  editable: boolean;
}> = ({ initText, cellTitle, onTextUpdate, editable, placeholder }) => {
  const [dialogVisible, setDialogVisible] = useState(false);
  const [focus, setFocus] = useState(false);
  const [value, setValue] = useState<string>();
  const [valueRows, setValueRows] = useState<string[]>([]);
  const [loading, setLoading] = useState(false);

  const [newValue, setNewValue] = useState<string>();

  useEffect(() => {
    setValue(initText);
    setNewValue(initText);
  }, [initText]);

  useEffect(() => {
    setValueRows(value ? value.split('\n') : []);
  }, [value]);

  const showDialog = () => {
    setDialogVisible(true);
  };
  const handleOk = () => {
    setLoading(true);
    onTextUpdate(newValue).then(res => {
      setValue(newValue);
      setDialogVisible(false);
      setLoading(false);
      setFocus(false);
    });
  };
  const handleCancel = () => {
    setDialogVisible(false);
    setFocus(false);
  };

  const handleMouseLeave = () => {
    if (dialogVisible) {
      return;
    }
    setFocus(false);
  };

  const handleMouseEnter = () => {
    if (dialogVisible) {
      return;
    }
    setFocus(true);
  };

  return (
    <div
      onMouseEnter={() => handleMouseEnter()}
      onMouseLeave={() => handleMouseLeave()}
      style={{
        width: '100%',
        height: '100%',
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        padding: 8,
      }}
    >
      <div
        style={{
          padding: 10,
          display: 'flex',
          alignItems: 'center',
          width: '100%',
          position: 'relative',
        }}
      >
        <div
          style={{
            width: '100%',
          }}
        >
          <Popover
            trigger={'custom'}
            visible={focus && !dialogVisible && Boolean(value?.length)}
            content={<TableTextContentPopover values={valueRows} />}
            showArrow={true}
          >
            <Text ellipsis={true} style={{ maxWidth: '100%' }} type={value ? 'primary' : 'tertiary'}>
              {value?.length ? value : placeholder}
            </Text>
          </Popover>
        </div>
        <div
          style={{
            padding: 6,
            zIndex: 1,
            width: '100%',
            position: 'absolute',
          }}
        >
          {focus && editable ? (
            <>
              <Button
                icon={<IconEditStroked />}
                onClick={() => {
                  showDialog();
                }}
                theme="outline"
                style={{
                  float: 'right',
                  backgroundColor: 'rgba(var(--semi-grey-0), 1)',
                }}
              />
              <Modal
                title={cellTitle ?? '编辑内容'}
                visible={dialogVisible}
                onOk={handleOk}
                onCancel={handleCancel}
                closeOnEsc={true}
                okText={'保存'}
                confirmLoading={loading}
              >
                <TextArea onChange={v => setNewValue(v)} placeholder={placeholder ?? '编辑内容'} value={newValue} />
              </Modal>
            </>
          ) : (
            <></>
          )}
        </div>
      </div>
    </div>
  );
};

export default TableTextEditCell;
