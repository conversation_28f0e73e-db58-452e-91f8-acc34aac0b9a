import {
  StoryRevenueFilterCollectTemplate,
  StoryRevenueFilterUserSetting,
} from '@shared/storyRevenueReviewPlatform/StoryRevenueUserSettings/StoryRevenueFilterUserSetting';
import { useState } from 'react';
import { ColumnProps } from '@douyinfe/semi-ui/lib/es/table';
import { Button, Space, Table } from '@douyinfe/semi-ui';

const FilterStatusCollectionTable: React.FC<{
  userSetting?: StoryRevenueFilterUserSetting;
  onTemplateApplyed: (templateId: string) => Promise<void>;
  deleteTemplate: (templateId: string) => Promise<void>;
}> = ({ userSetting, onTemplateApplyed, deleteTemplate }) => {
  const [collectionList, setCollectionList] = useState<StoryRevenueFilterCollectTemplate[]>([]);
  const [applyLoading, setApplyLoading] = useState(false);
  const [deleteLoading, setDeleteLoading] = useState(false);

  const columns: ColumnProps<StoryRevenueFilterCollectTemplate>[] = [
    {
      title: '模版名称',
      width: '60%',
      dataIndex: 'custom_title',
    },
    {
      title: '操作',
      dataIndex: 'key',
      width: '40%',
      render: (text, record) => (
        <Space>
          <Button
            theme="solid"
            type="primary"
            onClick={() => {
              setApplyLoading(true);
              onTemplateApplyed(record.key).then(res => {
                setApplyLoading(false);
              });
            }}
          >
            应用
          </Button>
          <Button
            theme="light"
            type="primary"
            onClick={() => {
              setDeleteLoading(true);
              deleteTemplate(record.key).then(res => {
                setDeleteLoading(false);
              });
            }}
          >
            删除
          </Button>
        </Space>
      ),
    },
  ];

  return (
    <Table
      columns={columns}
      loading={applyLoading || deleteLoading}
      resizable={true}
      bordered={true}
      dataSource={userSetting?.collect_template}
      rowKey={'key'}
    />
  );
};

export default FilterStatusCollectionTable;
