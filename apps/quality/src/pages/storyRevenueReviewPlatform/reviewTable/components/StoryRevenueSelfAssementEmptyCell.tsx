import React, { useState } from 'react';
import { Popover, Typography } from '@douyinfe/semi-ui';

const { Text } = Typography;

const StoryRevenueSelfAssementEmptyCell: React.FC<{ hoverText: string }> = ({ hoverText }) => {
  const [popoverVisible, setPopoverVisible] = useState(false);

  return (
    <div
      style={{
        width: '100%',
        height: '100%',
        paddingLeft: 24,
        paddingRight: 12,
        alignItems: 'center',
        display: 'flex',
      }}
      onMouseEnter={() => {
        setPopoverVisible(true);
      }}
      onMouseLeave={() => {
        setPopoverVisible(false);
      }}
    >
      <Popover
        content={
          <div style={{ padding: 12 }}>
            <Text>{hoverText}</Text>
          </div>
        }
        trigger={'custom'}
        visible={popoverVisible}
        showArrow={true}
      >
        <Text>-</Text>
      </Popover>
    </div>
  );
};

export default StoryRevenueSelfAssementEmptyCell;
