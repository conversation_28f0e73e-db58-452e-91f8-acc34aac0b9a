import { StoryRevenueTaskInfo } from '@shared/storyRevenueReviewPlatform/StoryRevenueTaskInfo';
import { Button, Select, Space, Spin, Tag, Typography } from '@douyinfe/semi-ui';
import React, { useEffect, useState } from 'react';
import { IconEditStroked } from '@douyinfe/semi-icons';
import { TagColor } from '@douyinfe/semi-ui/lib/es/tag';

const { Text, Title } = Typography;

const FeatureRevenueSelector: React.FC<{
  taskInfo: StoryRevenueTaskInfo;
  autoRes?: string;
  manuallyRes?: string;
  editable: boolean;
  saveValue: (newValue?: boolean) => Promise<void>;
  hideLoading?: boolean;
  yesColor?: TagColor;
  noColor?: TagColor;
}> = ({ taskInfo, autoRes, manuallyRes, editable, saveValue, hideLoading, yesColor, noColor }) => {
  const [selectState, setSelectState] = useState(false);
  const [focus, setFocus] = useState(false);
  const [selectedValue, setSelectedValue] = useState<string>();
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (manuallyRes) {
      setSelectedValue(manuallyRes);
    } else {
      setSelectedValue(autoRes);
    }
  }, [autoRes, manuallyRes]);

  const optionRenders = [
    {
      value: 'true',
      label: <Tag color={yesColor ?? 'green'}>{'有'}</Tag>,
    },
    {
      value: 'false',
      label: <Tag color={noColor ?? 'pink'}>{'无'}</Tag>,
    },
  ];
  const autoResRenders = [
    {
      value: 'true',
      label: (
        <Space>
          <Tag color={yesColor ?? 'green'} size={'large'}>
            {'有'}
          </Tag>
          <Tag size={'small'} shape={'circle'}>
            自动计算
          </Tag>
        </Space>
      ),
    },
    {
      value: 'false',
      label: (
        <Space>
          <Tag color={noColor ?? 'pink'} size={'large'}>
            {'无'}
          </Tag>
          <Tag size={'small'} shape={'circle'}>
            自动计算
          </Tag>
        </Space>
      ),
    },
    {
      value: 'unknown',
      label: <Text style={{ padding: 10 }}> - </Text>,
    },
  ];
  const editResRenders = [
    {
      value: 'true',
      label: (
        <Space>
          <Tag color={yesColor ?? 'green'} size={'large'}>
            {'有'}
          </Tag>
          <Tag size={'small'} shape={'circle'}>
            已编辑
          </Tag>
        </Space>
      ),
    },
    {
      value: 'false',
      label: (
        <Space>
          <Tag color={noColor ?? 'pink'} size={'large'}>
            {'无'}
          </Tag>
          <Tag size={'small'} shape={'circle'}>
            已编辑
          </Tag>
        </Space>
      ),
    },
    {
      value: 'unknown',
      label: <Text style={{ padding: 10 }}> - </Text>,
    },
  ];

  return (
    <div
      style={{
        width: '100%',
        height: '100%',
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        padding: 8,
        border: selectState ? '2px solid rgba(var(--semi-blue-7), 1)' : undefined,
      }}
      onMouseEnter={() => {
        setFocus(editable);
      }}
      onMouseLeave={() => {
        setFocus(false);
      }}
    >
      {selectState && editable ? (
        <Select
          showClear={true}
          loading={loading}
          placeholder={'请选择'}
          optionList={optionRenders}
          onClear={() => {
            setSelectedValue(undefined);
          }}
          value={selectedValue}
          expandRestTagsOnClick={true}
          showRestTagsPopover={true}
          style={{ width: '100%' }}
          onDropdownVisibleChange={visible => {
            if (!visible) {
              setSelectState(false);
              setFocus(false);
            }
          }}
          onChange={value => {
            if (value === undefined) {
              setSelectedValue(undefined);
              setLoading(true);
              saveValue(undefined).then(res => {
                setLoading(false);
              });
              setSelectState(false);
              setFocus(false);
            }
          }}
          onSelect={(value, option) => {
            setSelectedValue(value as string);
            setLoading(true);
            saveValue(value === 'true').then(res => {
              setLoading(false);
              setSelectState(false);
              setFocus(false);
            });
          }}
          defaultOpen={true}
        />
      ) : (
        <div
          style={{
            padding: 6,
            display: 'flex',
            alignItems: 'center',
            width: '100%',
            position: 'relative',
          }}
        >
          <div
            style={{
              maxWidth: '100%',
            }}
          >
            <Spin spinning={loading && !hideLoading}>
              {loading
                ? optionRenders.find(it => it.value === selectedValue)?.label
                : manuallyRes
                  ? editResRenders.find(it => it.value === selectedValue)?.label
                  : autoRes
                    ? autoResRenders.find(it => it.value === selectedValue)?.label
                    : optionRenders.find(it => it.value === selectedValue)?.label}
            </Spin>
          </div>
          <div
            style={{
              padding: 6,
              zIndex: 1,
              width: '100%',
              position: 'absolute',
            }}
          >
            {focus && editable ? (
              <Button
                icon={<IconEditStroked />}
                onClick={() => {
                  setSelectState(true);
                }}
                theme="outline"
                style={{
                  float: 'right',
                  backgroundColor: 'rgba(var(--semi-grey-0), 1)',
                }}
              />
            ) : (
              <></>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default FeatureRevenueSelector;
