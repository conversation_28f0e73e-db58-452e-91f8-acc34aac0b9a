import React, { useState } from 'react';
import { Space, Toast, Typography } from '@douyinfe/semi-ui';
import { StoryRevenueTaskInfo } from '@shared/storyRevenueReviewPlatform/StoryRevenueTaskInfo';
import MeegoBindLibraInfoPanelButton from '@/pages/storyRevenueReviewPlatform/meegoBindLibraInfoPanelButton';
import FeatureRevenueInfoSideSheet from '@/pages/storyRevenueReviewPlatform/reviewTable/components/FeatureRevenueInfoSideSheet';
import { StoryRevenueReviewPeriodInfo } from '@shared/storyRevenueReviewPlatform/StoryRevenueReviewPeriodInfo';
import { StoryRevenueRoleType } from '@shared/storyRevenueReviewPlatform/StoryRevenueUserMemberInfo';

const { Text } = Typography;

const SubExperimentNumberCell: React.FC<{
  record: StoryRevenueTaskInfo;
  userRoleType: StoryRevenueRoleType;
  onMeegoBindLibraSuccess: (newTaskInfo: StoryRevenueTaskInfo) => void;
  periodInfo?: StoryRevenueReviewPeriodInfo;
}> = ({ record, userRoleType, onMeegoBindLibraSuccess, periodInfo }) => {
  const [focus, setFocus] = useState(false);
  const [panelVisible, setPanelVisible] = useState(false);

  const handleMouseEnter = () => {
    if (panelVisible) {
      // 如果面板可见，则不处理
      return;
    }
    setFocus(true);
  };

  const bindLibraButtonEnable = () =>
    !periodInfo?.updating &&
    !periodInfo?.isSyncingDataToMeego &&
    !periodInfo?.locked &&
    !periodInfo?.dsLocked &&
    !record.updating &&
    focus &&
    (userRoleType === StoryRevenueRoleType.Admin ||
      userRoleType === StoryRevenueRoleType.DataAnalyst ||
      userRoleType === StoryRevenueRoleType.ProductManager);

  const handleMouseLeave = () => {
    if (panelVisible) {
      // 如果面板可见，则不处理
      return;
    }
    setFocus(false);
  };

  // 完成 Meego 与 Libra 的绑定操作
  const handleLibraBindConfirm = (newTaskInfo: StoryRevenueTaskInfo) => {
    // 刷新数据
    Toast.success(`绑定 Libra 实验成功！需求：${newTaskInfo.meegoInfo.name}`);
    // 发送事件，更新数据库
    onMeegoBindLibraSuccess(newTaskInfo);
  };

  return (
    <div
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
      style={{
        width: '100%',
        height: '100%',
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        padding: 6,
      }}
    >
      <>
        <Space style={{ padding: 12, height: '80%' }}>
          <Text>
            {(record.experimentInfo?.subExperimentCount ?? 0) > 0 ? record.experimentInfo?.subExperimentCount : 0}
          </Text>
          <FeatureRevenueInfoSideSheet
            taskInfo={record}
            updateTaskInfo={onMeegoBindLibraSuccess}
            periodLocked={
              periodInfo ? periodInfo.updating || periodInfo.isSyncingDataToMeego || periodInfo.dsLocked || periodInfo.locked : false
            }
            userRoleType={userRoleType}
          />
        </Space>
        {bindLibraButtonEnable() && (
          <div style={{ float: 'right' }}>
            <MeegoBindLibraInfoPanelButton
              taskInfo={record}
              onVisibleChange={(visible: boolean) => {
                setPanelVisible(visible);
              }}
              onConfirm={handleLibraBindConfirm}
            />
          </div>
        )}
      </>
    </div>
  );
};

export default SubExperimentNumberCell;
