import { StoryRevenueTaskInfo } from '@shared/storyRevenueReviewPlatform/StoryRevenueTaskInfo';
import React, { useEffect, useState } from 'react';
import { Button, Modal, TextArea, Typography } from '@douyinfe/semi-ui';
import { IconEditStroked } from '@douyinfe/semi-icons';

const { Paragraph, Title, Text } = Typography;

const RevenueReviewDocCell: React.FC<{
  taskInfo: StoryRevenueTaskInfo;
  editable: boolean;
  onTextUpdate: (newText?: string) => Promise<void>;
}> = ({ taskInfo, editable, onTextUpdate }) => {
  const [dialogVisible, setDialogVisible] = useState(false);
  const [focus, setFocus] = useState(false);
  const [value, setValue] = useState<string>();
  const [loading, setLoading] = useState(false);

  const [newValue, setNewValue] = useState<string>();

  useEffect(() => {
    setValue(taskInfo.revenueInfo?.reviewDocUrl);
    setNewValue(taskInfo.revenueInfo?.reviewDocUrl);
  }, [taskInfo]);

  const showDialog = () => {
    setDialogVisible(true);
    setFocus(false);
  };

  const handleOk = () => {
    const formatValue = newValue ? newValue.split(' ').join('') : undefined;
    setLoading(true);
    onTextUpdate(formatValue).then(res => {
      setValue(formatValue);
      setDialogVisible(false);
      setLoading(false);
      setFocus(false);
    });
  };
  const handleCancel = () => {
    setDialogVisible(false);
    setFocus(false);
  };

  return (
    <div
      onMouseEnter={() => setFocus(true)}
      onMouseLeave={() => setFocus(false)}
      style={{
        width: '100%',
        height: '100%',
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        padding: 8,
      }}
    >
      <div
        style={{
          padding: 6,
          display: 'flex',
          alignItems: 'center',
          width: '100%',
          position: 'relative',
        }}
      >
        <div
          style={{
            width: '100%',
            height: '100%',
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
          }}
        >
          {taskInfo.revenueInfo?.reviewDocUrl ? (
            <Text
              ellipsis={true}
              style={{ maxWidth: '100%' }}
              link={{ href: taskInfo.revenueInfo?.reviewDocUrl, target: '_blank' }}
            >
              {`复盘文档链接-${taskInfo.meegoInfo.name}`}
            </Text>
          ) : (
            <div style={{ maxWidth: '100%' }} />
          )}
          {focus && editable ? (
            <>
              <Button
                icon={<IconEditStroked />}
                onClick={() => {
                  showDialog();
                }}
                theme="outline"
                style={{
                  float: 'right',
                  backgroundColor: 'rgba(var(--semi-grey-0), 1)',
                }}
              />
              <Modal
                title={'复盘文档链接'}
                visible={dialogVisible}
                onOk={handleOk}
                onCancel={handleCancel}
                closeOnEsc={true}
                okText={'保存'}
                confirmLoading={loading}
              >
                <TextArea onChange={v => setNewValue(v)} placeholder={'请填写复盘文档链接'} value={newValue} />
              </Modal>
            </>
          ) : (
            <></>
          )}
        </div>
      </div>
    </div>
  );
};

export default RevenueReviewDocCell;
