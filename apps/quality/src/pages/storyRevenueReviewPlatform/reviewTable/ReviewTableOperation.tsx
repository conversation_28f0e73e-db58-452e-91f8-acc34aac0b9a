import React, { useEffect, useState } from 'react';
import { Button, Input, Modal, Popover, SideSheet, Space } from '@douyinfe/semi-ui';
import { IconFilter } from '@douyinfe/semi-icons';
import {
  StoryRevenueTableColumnDefaultUserSetting,
  StoryRevenueTableColumnGroup,
} from '@shared/storyRevenueReviewPlatform/StoryRevenueUserSettings/StoryRevenueTableColumnUserSetting';
import {
  EnumFilterConfig,
  FilterConfig,
  FilterGroupType,
  FilterStatus,
  FilterType,
  NumberFilterConfig,
  TextFilterConfig,
  TreeEnumFilterConfig,
  TreeEnumOptions,
  UserFilterConfig,
} from '@shared/utils/conditionFilter';
import {
  fetchFilterTemplate,
  templateFilterApplyed,
  templateFilterDeleted,
  userCollectFilterStatus,
} from '@api/storyRevenueReviewPlatform';
import { getfilterOptions } from '@shared/storyRevenueReviewPlatform/StoryRevenuePlatformUtils';
import ConditionFilter from '@/component/ConditionFilter';
import { DropDownMenuItem } from '@douyinfe/semi-ui/lib/es/dropdown';
import FilterStatusCollectionTable from '@/pages/storyRevenueReviewPlatform/reviewTable/components/FilterStatusCollectionTable';
import {
  StoryRevenueFilterUserSetting,
  StoryRevenueUrlParams,
} from '@shared/storyRevenueReviewPlatform/StoryRevenueUserSettings/StoryRevenueFilterUserSetting';
import { useSearchParams } from '@edenx/runtime/router';
import { useModel } from '@edenx/runtime/model';
import UserSettingModule from '@/model/userSettingModel';

const ReviewTableOperation: React.FC<{
  columnSetting: StoryRevenueTableColumnGroup[];
  onFilterStatusChange: (status: FilterStatus[]) => void;
  updateType: (type: FilterGroupType) => void;
  filterType: FilterGroupType;
  filterStatus: FilterStatus[];
  applyFiltered: boolean;
  onFilterApply: (type: FilterGroupType, status: FilterStatus[], includeTerminated: boolean) => Promise<void>;
  userFilterSetting?: StoryRevenueFilterUserSetting;
  updateUserFilterSetting: (newSetting: StoryRevenueFilterUserSetting) => void;
  includeTerminated: boolean;
  updateInlcudeTerminated: (status: boolean) => void;
}> = ({
  columnSetting,
  filterStatus,
  onFilterStatusChange,
  filterType,
  updateType,
  applyFiltered,
  onFilterApply,
  userFilterSetting,
  updateUserFilterSetting,
  includeTerminated,
  updateInlcudeTerminated,
}) => {
  const [filterVisible, setFilterVisible] = useState(false);
  const [filterConfigs, setFilterConfigs] = useState<FilterConfig[]>([]);

  const [curFilterStatus, setCurFilterStatus] = useState<FilterStatus[]>([]);
  const [curFilterType, setCurFilterType] = useState<FilterGroupType>(FilterGroupType.And);

  const [saveTempDialogVisible, setSaveTempDialogVisible] = useState(false);
  const [saveTempLoading, setSaveTempLoading] = useState(false);
  const [tempTitle, setTempTitle] = useState<string>();

  const [tempListSheetVisible, setTempListSheetVisible] = useState(false);
  const [searchParams, setSearchParams] = useSearchParams();

  const [curUserFilterSetting, setCurUserFIlterSetting] = useState<StoryRevenueFilterUserSetting | undefined>();

  const [userSettingState] = useModel(UserSettingModule);

  const [templateMenuVisible, setTemplateMenuVisible] = useState(false);

  useEffect(() => {
    setCurUserFIlterSetting(userFilterSetting);
  }, [userFilterSetting]);

  useEffect(() => {
    setCurFilterType(filterType);
  }, [filterType]);

  useEffect(() => {
    setCurFilterStatus(filterStatus);
  }, [filterStatus]);

  useEffect(() => {
    const configs: FilterConfig[] = [];
    StoryRevenueTableColumnDefaultUserSetting().groups.forEach(it => {
      it.configs.forEach(it2 => {
        if (it2.filter_config) {
          switch (it2.filter_config.filter_type) {
            case FilterType.FilterTypeEnum:
              configs.push({
                filterType: it2.filter_config.filter_type,
                filterName: it2.column_name,
                filterId: it2.column_id,
                filterOptions: getfilterOptions(it2.column_id, it2.filter_config.value_type),
              } as EnumFilterConfig);
              return;
            case FilterType.FilterTypeTreeEnum:
              configs.push({
                filterType: it2.filter_config.filter_type,
                filterName: it2.column_name,
                filterId: it2.column_id,
                filterOptions: getfilterOptions(it2.column_id, it2.filter_config.value_type) as TreeEnumOptions[],
              } as TreeEnumFilterConfig);
              return;
            case FilterType.FilterTypeText:
              configs.push({
                filterType: it2.filter_config.filter_type,
                filterName: it2.column_name,
                filterId: it2.column_id,
              } as TextFilterConfig);
              return;
            case FilterType.FilterTypeNumber:
              configs.push({
                filterType: it2.filter_config.filter_type,
                filterName: it2.column_name,
                filterId: it2.column_id,
              } as NumberFilterConfig);
              return;
            case FilterType.FilterTypeUser:
              configs.push({
                filterType: it2.filter_config.filter_type,
                filterName: it2.column_name,
                filterId: it2.column_id,
              } as UserFilterConfig);
              return;
            default:
              configs.push({
                filterType: it2.filter_config.filter_type,
                filterName: it2.column_name,
                filterId: it2.column_id,
              } as FilterConfig);
          }
        }
      });
    });
    setFilterConfigs(configs);
  }, [columnSetting]);

  const updateFilterStatus = (status: FilterStatus[]) => {
    onFilterStatusChange(status);
  };

  const spliteButtonMenu = (): DropDownMenuItem[] => [
    {
      node: 'item',
      name: '保存为筛选模版',
      onClick: () => {
        setSaveTempDialogVisible(true);
      },
    },
    {
      node: 'item',
      name: '查看筛选模版列表',
      onClick: () => {
        setTempListSheetVisible(true);
      },
    },
  ];

  useEffect(() => {
    if (saveTempDialogVisible) {
      setFilterVisible(false);
    }
  }, [saveTempDialogVisible]);

  useEffect(() => {
    if (tempListSheetVisible) {
      setFilterVisible(false);
    }
  }, [tempListSheetVisible]);

  return (
    <Space vertical={false} align={'center'}>
      <Popover
        content={
          <ConditionFilter
            onApply={() => {
              setFilterVisible(false);
              onFilterApply(curFilterType, curFilterStatus, includeTerminated);
            }}
            filterType={curFilterType}
            filterConfigs={filterConfigs}
            filterStatus={curFilterStatus}
            updateStatus={statusArray => {
              updateFilterStatus(statusArray);
              setCurFilterStatus(statusArray);
            }}
            onclickSpliteMenuOutSide={e => {
              // setFilterVisible()
            }}
            updateType={type => {
              updateType(type);
              setCurFilterType(type);
            }}
            spliteButtonMenu={spliteButtonMenu()}
            floatCheckStatus={includeTerminated}
            updateFloatCheckStatus={status => updateInlcudeTerminated(status)}
            spliteButtonVisibleChanged={visible => setTemplateMenuVisible(visible)}
          />
        }
        trigger={'custom'}
        position={'bottomRight'}
        visible={filterVisible}
        onClickOutSide={e => {
          setFilterVisible(templateMenuVisible);
        }}
        onVisibleChange={visible => {
          if (!visible) {
            setTemplateMenuVisible(false);
          }
        }}
      >
        <Button
          theme={'borderless'}
          style={{
            color: 'rgba(var(--semi-grey-8), 1)',
            backgroundColor: filterVisible || applyFiltered ? 'rgba(var(--semi-grey-1), 1)' : undefined,
          }}
          icon={<IconFilter />}
          onClick={() => setFilterVisible(!filterVisible)}
        >
          {`筛选${filterStatus.length > 0 ? ` · ${filterStatus.length}` : ''}`}
        </Button>
        <Modal
          title={'输入筛选模版标题'}
          visible={saveTempDialogVisible}
          onOk={() => {
            setSaveTempLoading(true);
            userCollectFilterStatus({
              data: {
                email: userSettingState.info.email,
                groupType: curFilterType,
                filterStatus: curFilterStatus,
                title: tempTitle ?? '',
              },
            }).then(res => {
              setSaveTempDialogVisible(false);
              setSaveTempLoading(false);
              setTempTitle(undefined);
              if (res) {
                updateUserFilterSetting(res);
                setCurUserFIlterSetting(res);
              }
            });
          }}
          onCancel={() => {
            setSaveTempDialogVisible(false);
          }}
          closeOnEsc={true}
          okText={'保存'}
          confirmLoading={saveTempLoading}
          okButtonProps={{ disabled: (tempTitle?.length ?? 0) <= 0 }}
        >
          <Input onChange={v => setTempTitle(v)} value={tempTitle} />
        </Modal>
        <SideSheet
          title={'筛选模版列表'}
          visible={tempListSheetVisible}
          onCancel={() => setTempListSheetVisible(false)}
        >
          <FilterStatusCollectionTable
            userSetting={curUserFilterSetting}
            onTemplateApplyed={async templateId => {
              await fetchFilterTemplate({
                data: {
                  key: templateId,
                },
              })
                .then(res => {
                  if (res) {
                    onFilterApply(res.group_type, res.status, res.include_terminated ?? false);
                    setCurFilterStatus(res.status);
                    setCurFilterType(res.group_type);
                  }
                  return res;
                })
                .then(res => {
                  if (res) {
                    searchParams.set(StoryRevenueUrlParams.FilterTemplateKey, res.key);
                    setSearchParams(searchParams);
                  }
                });
              await templateFilterApplyed({
                data: {
                  email: userSettingState.info.email,
                  templateKey: templateId,
                },
              }).then(newSetting => {
                if (newSetting) {
                  updateUserFilterSetting(newSetting);
                  setCurUserFIlterSetting(newSetting);
                }
              });
            }}
            deleteTemplate={async templateId => {
              await templateFilterDeleted({
                data: {
                  email: userSettingState.info.email,
                  templateKey: templateId,
                },
              }).then(newSetting => {
                if (newSetting) {
                  updateUserFilterSetting(newSetting);
                  setCurUserFIlterSetting(newSetting);
                }
              });
            }}
          />
        </SideSheet>
      </Popover>
      {/* <Button theme={"borderless"} style={{ color: "rgba(var(--semi-grey-8), 1)" }} icon={<IconDescend />}>*/}
      {/*	排序*/}
      {/* </Button>*/}
    </Space>
  );
};

export default ReviewTableOperation;
