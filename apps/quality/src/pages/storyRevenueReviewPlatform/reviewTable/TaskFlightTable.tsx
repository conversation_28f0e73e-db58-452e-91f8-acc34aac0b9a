import {
  StoryRevenueConvertCopyUserToUser,
  StoryRevenueTaskInfo,
  StoryRevenueTaskInfoEditResultType,
  StoryRevenueTaskRevenueReviewType,
  StoryRevenueTaskSubExperimentInfo,
  StoryRevenueTaskSubExperimentStatus,
} from '@shared/storyRevenueReviewPlatform/StoryRevenueTaskInfo';
import {
  StoryRevenueSubFlightTableColumnIdSort,
  StoryRevenueTableColumnConfig,
  StoryRevenueTableColumnGroup,
  StoryRevenueTableColumnIdSort,
} from '@shared/storyRevenueReviewPlatform/StoryRevenueUserSettings/StoryRevenueTableColumnUserSetting';
import React, { useEffect, useState } from 'react';
import { ColumnProps } from '@douyinfe/semi-ui/lib/es/table';
import { Table, Tag, Toast, Typography } from '@douyinfe/semi-ui';
import UserCard from '@/component/UserCard';
import SemiReactUserGroup from '@/component/UserAvatarGroup';
import {
  EventResponseTagSelector,
  getReviewTableColumnObject,
  getSubFlightTableColumnObject,
  TagArray,
  tagColorArray,
} from '@/pages/storyRevenueReviewPlatform/reviewTable/ReviewTableColumnObjectFactory';
import { editStoryRevenueTaskInfo } from '@api/storyRevenueReviewPlatform';
import SubExperimentNumberCell from '@/pages/storyRevenueReviewPlatform/reviewTable/components/SubExperimentNumberCell';
import UserSettingModule from '@/model/userSettingModel';
import { useModel } from '@edenx/runtime/model';

const { Text } = Typography;

const TaskFlightTable: React.FC<{
  taskInfo: StoryRevenueTaskInfo;
  tableColumnSetting: StoryRevenueTableColumnGroup[];
  effectiveTableColumnSetting: {
    groups: StoryRevenueTableColumnGroup[];
    configMap: { [key: string]: StoryRevenueTableColumnConfig };
  };
}> = ({ taskInfo, tableColumnSetting, effectiveTableColumnSetting }) => {
  const [flightTableColumns, setFlightTableColumns] = useState<ColumnProps<StoryRevenueTaskSubExperimentInfo>[]>([]);
  const [tableColumnDisplayArray, setTableColumnDisplayArray] = useState<StoryRevenueTableColumnConfig[]>([]);
  const [userSettingState] = useModel(UserSettingModule);

  const convertChineseStringToNumber = (str: string): number => {
    let sum = 0;
    for (const strElement of str) {
      sum += strElement.charCodeAt(0);
    }
    return sum;
  };
  const onEditTaskInfo = async (
    experimentInfo: StoryRevenueTaskSubExperimentInfo,
    keyPath: string,
    newValue: any,
  ): Promise<boolean> => {
    const keys = keyPath.split('.');
    let tmpValue: any = taskInfo;
    keys.forEach(key => {
      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
      // @ts-ignore
      tmpValue = tmpValue[key];
    });
    const res = await editStoryRevenueTaskInfo({
      data: {
        meegoId: taskInfo.meegoInfo.id,
        periodId: taskInfo.reviewPeriodId,
        keyPath,
        newValue,
        tmpValue,
        editUserEmail: userSettingState.info.email,
      },
    });
    if (res.result === StoryRevenueTaskInfoEditResultType.Success) {
      Toast.success('更新成功');
    } else if (res.result === StoryRevenueTaskInfoEditResultType.Conflict) {
      Toast.error('更新失败，源数据已更改，请刷新页面重试');
    } else {
      Toast.error('更新失败，未知错误');
    }
    return res.result === StoryRevenueTaskInfoEditResultType.Success;
  };

  useEffect(() => {
    if (effectiveTableColumnSetting.groups.length <= 0) {
      return;
    }
    const displayArray: StoryRevenueTableColumnConfig[] = [];
    StoryRevenueSubFlightTableColumnIdSort().forEach(columnId => {
      const config = effectiveTableColumnSetting.configMap[columnId];
      if (config && config.enable) {
        displayArray.push(config);
      }
    });
    setTableColumnDisplayArray(displayArray);
  }, [effectiveTableColumnSetting]);

  const flightBaseColumns: ColumnProps<StoryRevenueTaskSubExperimentInfo>[] = [
    {
      title: '子实验名称',
      width: 200,
      dataIndex: 'libraTitle',
      fixed: true,
      ellipsis: true,
      render: (text, record, index, options) => (
        <Text link={{ href: record.libraUrl, target: '_blank' }}>{record.libraTitle}</Text>
      ),
    },
    {
      title: 'ID',
      width: 80,
      ellipsis: true,
      dataIndex: 'libraFlightId',
      render: (text, record, index, options) => <Text>{record.libraFlightId}</Text>,
    },
    {
      title: '实验Owner',
      ellipsis: true,
      width: 130,
      dataIndex: 'libraOwners.name',
      render: (text, record, index, options) => {
        if (record.libraOwners.length === 1) {
          const owner = record.libraOwners[0];
          return (
            <UserCard
              email={owner?.email}
              simpleUserData={{
                avatarUrl: typeof owner?.avatar === 'string' ? owner?.avatar : owner?.avatar?.avatar_240,
                name: owner?.name,
              }}
              triggerType="hover"
            />
          );
        } else {
          return (
            <div style={{ display: 'flex', justifyContent: 'space-between' }}>
              <SemiReactUserGroup users={StoryRevenueConvertCopyUserToUser(record.libraOwners)} triggerType={'hover'} />
              {/* {owners.map((user, index) => UserAvatar({ checkUser: user }))} */}
            </div>
          );
        }
      },
    },
    {
      title: '状态',
      width: 80,
      ellipsis: true,
      dataIndex: 'status',
      render: (text, record, index) => {
        const reviewTypeMap: Record<string, string> = {};
        reviewTypeMap[StoryRevenueTaskSubExperimentStatus.Ended.toString()] = '已结束';
        reviewTypeMap[StoryRevenueTaskSubExperimentStatus.InProgress.toString()] = '进行中';
        reviewTypeMap[StoryRevenueTaskSubExperimentStatus.ToBeScheduled.toString()] = '待调度';
        reviewTypeMap[StoryRevenueTaskSubExperimentStatus.InDebug.toString()] = '调试中';
        reviewTypeMap[StoryRevenueTaskSubExperimentStatus.Paused.toString()] = '已暂停';
        reviewTypeMap[StoryRevenueTaskSubExperimentStatus.ToBeScheduledEnded.toString()] = '待调度结束';
        reviewTypeMap[StoryRevenueTaskSubExperimentStatus.Released.toString()] = '已上线';
        return record.status !== undefined && record.status !== null ? (
          <Tag color={tagColorArray[record.status % tagColorArray.length]} style={{ maxWidth: '100%' }} type="ghost">
            {reviewTypeMap[record.status.toString()]}
          </Tag>
        ) : (
          <></>
        );
      },
    },
  ];

  useEffect(() => {
    const extraColumnList: ColumnProps<StoryRevenueTaskSubExperimentInfo>[] = [];
    tableColumnDisplayArray.forEach(config => {
      const columnObject = getSubFlightTableColumnObject(config.column_id, onEditTaskInfo);
      if (config.enable && columnObject) {
        columnObject.title = config.column_name;
        columnObject.ellipsis = true;
        extraColumnList.push(columnObject);
      }
    });
    const columns = flightBaseColumns.concat(extraColumnList);

    setFlightTableColumns(columns);
  }, [tableColumnDisplayArray]);

  return (
    <Table
      columns={flightTableColumns}
      dataSource={taskInfo.experimentInfo?.subExperimentList}
      rowKey={'libraFlightId'}
      pagination={{
        pageSize: 10,
      }}
      resizable={true}
      bordered={true}
    />
  );
};

export default TaskFlightTable;
