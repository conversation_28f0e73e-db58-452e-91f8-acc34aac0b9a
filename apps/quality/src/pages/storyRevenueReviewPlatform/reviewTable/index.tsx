import { useModel } from '@edenx/runtime/model';
import UserSettingModule from '@/model/userSettingModel';
import React, { useEffect, useState } from 'react';
import {
  editStoryRevenueTaskInfo,
  fetchFilterTemplate,
  fetchRevenueTableColumnSetting,
  fetchUserFilterSetting,
  filterTaskInfo,
  updateRevenueTableColumnSetting,
  userFilterApplyed,
} from '@api/storyRevenueReviewPlatform';
import {
  ColumnCustomStatus,
  StoryRevenueTableColumnConfig,
  StoryRevenueTableColumnGroup,
  StoryRevenueTableColumnIdSort,
} from '@shared/storyRevenueReviewPlatform/StoryRevenueUserSettings/StoryRevenueTableColumnUserSetting';
import { Card, SideSheet, Space, Spin, Table, TabPane, Tabs, Toast, Typography } from '@douyinfe/semi-ui';
import { ColumnProps } from '@douyinfe/semi-ui/lib/es/table';
import {
  StoryRevenueTaskInfo,
  StoryRevenueTaskInfoEditResultType,
} from '@shared/storyRevenueReviewPlatform/StoryRevenueTaskInfo';
import {
  getReviewTableColumnObject,
  PopoverColumnTitle,
  RedMarkColumnTitle,
} from '@/pages/storyRevenueReviewPlatform/reviewTable/ReviewTableColumnObjectFactory';
import { compact } from 'lodash';
import CustomTableColumnPanelButton from 'src/pages/storyRevenueReviewPlatform/customTableColumnPanelButton';
import ReviewTableOperation from '@/pages/storyRevenueReviewPlatform/reviewTable/ReviewTableOperation';
import { StoryRevenueReviewPeriodInfo } from '@shared/storyRevenueReviewPlatform/StoryRevenueReviewPeriodInfo';
import SubExperimentNumberCell from '@/pages/storyRevenueReviewPlatform/reviewTable/components/SubExperimentNumberCell';
import { FilterGroupType, FilterRuleDesc, FilterStatus } from '@shared/utils/conditionFilter';
import { useSearchParams } from '@edenx/runtime/router';
import {
  StoryRevenueFilterUserSetting,
  StoryRevenueUrlParams,
} from '@shared/storyRevenueReviewPlatform/StoryRevenueUserSettings/StoryRevenueFilterUserSetting';
import { transFilterStatus2Rules } from '@shared/storyRevenueReviewPlatform/StoryRevenuePlatformUtils';
import { StoryRevenueRoleType } from '@shared/storyRevenueReviewPlatform/StoryRevenueUserMemberInfo';
import { TaskInfoSubmitForm } from '@/pages/storyRevenueReviewPlatform/reviewTable/components/TaskInfoSubmitForm';
import { IconLink } from '@douyinfe/semi-icons';

const { Text } = Typography;

const StoryRevenueReviewTable: React.FC<{
  userRoleType: StoryRevenueRoleType;
  period?: StoryRevenueReviewPeriodInfo;
  allPeriodInfo?: StoryRevenueReviewPeriodInfo[];
  onStoryListChange?: (storyList: StoryRevenueTaskInfo[]) => void;
  defaultTab?: string;
}> = ({ userRoleType, period, allPeriodInfo, onStoryListChange, defaultTab }) => {
  const [userSettingState] = useModel(UserSettingModule);

  const [tableColumnSetting, setTableColumnSetting] = useState<StoryRevenueTableColumnGroup[]>([]);
  const [effectiveTableColumnSetting, setEffectiveTableColumnSetting] = useState<{
    groups: StoryRevenueTableColumnGroup[];
    configMap: { [key: string]: StoryRevenueTableColumnConfig };
  }>({ groups: [], configMap: {} });
  const [tableColumnDisplayArray, setTableColumnDisplayArray] = useState<StoryRevenueTableColumnConfig[]>([]);

  const [tableColumnProps, setTableColumnProps] = useState<ColumnProps<StoryRevenueTaskInfo>[]>([]);
  const [storyList, setStoryList] = useState<StoryRevenueTaskInfo[]>();
  const [loading, setLoading] = useState(false);
  const [fetchColumnSettingLoading, setFetchColumnSettingLoading] = useState(false);
  const [expandedRowKeys, setExpandedRowKeys] = useState<string[]>([]);
  const [expandTrigger, setExpandTrigger] = useState<{ taskInfo: StoryRevenueTaskInfo; expand: boolean }>();

  const [tabConfigs, setTabConfigs] = useState<{ key: string; title: string; data: StoryRevenueTaskInfo[] }[]>([]);
  const [filterLoading, setFilterLoading] = useState(false);

  const [myStoryList, setMyStoryList] = useState<StoryRevenueTaskInfo[]>([]);
  const [businessStoryList, setBusinessStoryList] = useState<StoryRevenueTaskInfo[]>([]);
  const [techStoryList, setTechStoryList] = useState<StoryRevenueTaskInfo[]>([]);

  const [windowHeight, setWindowHeight] = useState(0);
  const [filterStatus, setFilterStatus] = useState<FilterStatus[]>([]);
  const [filterType, setFilterType] = useState<FilterGroupType>(FilterGroupType.And);
  const [filterApplyed, setFilterApplyed] = useState(false);
  const [filterIncludeTerminated, setFilterIncludeTerminated] = useState(false);
  const [userFilterSetting, setUserFilterSetting] = useState<StoryRevenueFilterUserSetting | undefined>();
  const [searchParams, setSearchParams] = useSearchParams();

  const [tablePage, setTablePage] = useState<number[]>([1, 1, 1]);
  const [displayStorys, setDisplayStorys] = useState<StoryRevenueTaskInfo[][]>([[], [], []]);

  const [userEmail, setUserEmail] = useState<string>('');
  const [focusTaskInfo, setFocusTaskInfo] = useState<StoryRevenueTaskInfo | undefined>();
  const [taskInfoFormVisible, setTaskInfoFormVisible] = useState(false);

  // 触发单个 Task Info 更新
  const updateOneTaskInfo = (newTaskInfo: StoryRevenueTaskInfo) => {
    const newStoryList = storyList?.map(taskInfo => {
      if (taskInfo._id === newTaskInfo._id) {
        return newTaskInfo;
      }
      return taskInfo;
    });
    setStoryList(newStoryList);
  };

  useEffect(() => {
    setLoading(true);
    new Promise((res, reject) => {
      const newStorys: StoryRevenueTaskInfo[][] = [];
      tabConfigs.forEach((config, index) => {
        const allData = config.data;
        newStorys.push(allData.slice((tablePage[index] - 1) * 10, tablePage[index] * 10));
      });
      setDisplayStorys(newStorys);
      setLoading(false);
    }).then(r => {
      setLoading(false);
    });
  }, [tabConfigs, tablePage]);

  // 筛选器应用
  const applyFilter = async (type: FilterGroupType, status: FilterStatus[], includeTerminated: boolean) => {
    if (!period) {
      return;
    }
    setFilterType(type);
    setFilterStatus(status);
    setFilterApplyed(status.length > 0);
    setFilterIncludeTerminated(includeTerminated);
    const flatColumnConfigs: StoryRevenueTableColumnConfig[] = [];
    effectiveTableColumnSetting.groups.forEach(it => {
      flatColumnConfigs.push(...it.configs);
    });
    const rules: FilterRuleDesc[] = transFilterStatus2Rules(flatColumnConfigs, status);
    setFilterLoading(true);
    filterTaskInfo({
      data: {
        filterRules: rules,
        groupType: type ?? FilterGroupType.And,
        includeTerminated,
        reviewPeriodId: period.reviewPeriodId,
      },
    }).then(res => {
      if (res) {
        setTablePage([1, 1, 1]);
        setStoryList(res);
      }
      setFilterLoading(false);
    });
    userFilterApplyed({
      data: {
        email: userEmail,
        groupType: type ?? FilterGroupType.And,
        filterStatus: status,
        includeTerminated,
      },
    }).then(res => {
      if (res) {
        setUserFilterSetting(res);
        if (rules.length > 0 || includeTerminated) {
          searchParams.set(StoryRevenueUrlParams.FilterTemplateKey, res.use_template);
          setSearchParams(searchParams);
        } else {
          searchParams.delete(StoryRevenueUrlParams.FilterTemplateKey);
          setSearchParams(searchParams);
        }
      }
    });
  };

  const onEditTaskInfo = async (
    info: StoryRevenueTaskInfo,
    keyPath: string,
    newValue: any,
    columnConfig: StoryRevenueTableColumnConfig,
  ): Promise<boolean> => {
    const keys = keyPath.split('.');
    let tmpValue: any = info;
    keys.forEach(key => {
      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
      // @ts-ignore
      tmpValue = tmpValue[key];
    });
    // setLoading(true);
    const res = await editStoryRevenueTaskInfo({
      data: {
        meegoId: info.meegoInfo.id,
        periodId: info.reviewPeriodId,
        keyPath,
        newValue,
        tmpValue,
        editUserEmail: userEmail,
        columnConfig,
      },
    });
    if (res.result === StoryRevenueTaskInfoEditResultType.Success) {
      Toast.success('更新成功');
    } else if (res.result === StoryRevenueTaskInfoEditResultType.Conflict) {
      Toast.error('更新失败，源数据已更改，请刷新页面重试');
    } else {
      Toast.error('更新失败，未知错误');
    }
    setLoading(false);
    if (res.newTakInfo) {
      updateOneTaskInfo(res.newTakInfo);
    }
    return res.result === StoryRevenueTaskInfoEditResultType.Success;
  };

  useEffect(() => {
    if (effectiveTableColumnSetting.groups.length <= 0 || userEmail.length <= 0) {
      return;
    }
    if (!period) {
      return;
    }
    setLoading(true);
    fetchUserFilterSetting({
      data: {
        email: userEmail,
      },
    })
      .then(userSetting => {
        setUserFilterSetting(userSetting ?? undefined);
        return userSetting;
      })
      .then(userSetting => {
        const urlFilterTemplate = searchParams.get(StoryRevenueUrlParams.FilterTemplateKey);
        const templateId = urlFilterTemplate ?? userSetting?.use_template;
        if (templateId) {
          searchParams.set(StoryRevenueUrlParams.FilterTemplateKey, templateId);
          setSearchParams(searchParams);

          fetchFilterTemplate({
            data: {
              key: templateId,
            },
          }).then(template => {
            if (template) {
              setFilterType(template.group_type);
              setFilterStatus(template.status);
              setFilterApplyed(template.status.length > 0);
              setFilterIncludeTerminated(template.include_terminated ?? false);
              const flatColumnConfigs: StoryRevenueTableColumnConfig[] = [];
              effectiveTableColumnSetting.groups.forEach(it => {
                flatColumnConfigs.push(...it.configs);
              });
              const rules: FilterRuleDesc[] = transFilterStatus2Rules(flatColumnConfigs, template.status);
              filterTaskInfo({
                data: {
                  filterRules: rules,
                  groupType: template.group_type ?? FilterGroupType.And,
                  includeTerminated: template.include_terminated ?? false,
                  reviewPeriodId: period?.reviewPeriodId,
                },
              }).then(res => {
                if (res) {
                  setStoryList(res);
                }
                setLoading(false);
              });
            }
          });
        } else {
          filterTaskInfo({
            data: {
              filterRules: [],
              groupType: FilterGroupType.And,
              includeTerminated: filterIncludeTerminated ?? false,
              reviewPeriodId: period?.reviewPeriodId,
            },
          }).then(list => {
            setStoryList(list ?? []);
            setLoading(false);
          });
        }
      });
  }, [userEmail, effectiveTableColumnSetting, period]);

  useEffect(() => {
    if (userSettingState.info.email !== userEmail) {
      setUserEmail(userSettingState.info.email);
    }
  }, [userSettingState.info]);

  useEffect(() => {
    setFetchColumnSettingLoading(true);
    if (userSettingState.info) {
      fetchRevenueTableColumnSetting({
        data: {
          userEmail,
        },
      }).then(res => {
        if (res && res.code === 0) {
          setTableColumnSetting(res.groups);
          setFetchColumnSettingLoading(false);
        } else {
          setFetchColumnSettingLoading(false);
        }
      });
    }
  }, [userEmail]);

  // 单独处理 StoryList 更新回调
  useEffect(() => {
    if (onStoryListChange) {
      onStoryListChange(storyList ?? []);
    }
  }, [storyList]);

  useEffect(() => {
    const effectiveGroups: StoryRevenueTableColumnGroup[] = [];
    const configMap: { [key: string]: StoryRevenueTableColumnConfig } = {};
    tableColumnSetting.forEach(it => {
      const configs: StoryRevenueTableColumnConfig[] = [];
      it.configs.forEach(config => {
        configMap[config.column_id] = config;
        if (StoryRevenueTableColumnIdSort().includes(config.column_id)) {
          configs.push(config);
        }
      });
      effectiveGroups.push({
        group_id: it.group_id,
        group_name: it.group_name,
        configs,
      });
    });
    setEffectiveTableColumnSetting({ groups: effectiveGroups, configMap });
  }, [tableColumnSetting]);

  useEffect(() => {
    if (effectiveTableColumnSetting.groups.length <= 0) {
      return;
    }
    const displayArray: StoryRevenueTableColumnConfig[] = [];
    StoryRevenueTableColumnIdSort().forEach(columnId => {
      const config = effectiveTableColumnSetting.configMap[columnId];
      if (config && config.enable) {
        displayArray.push(config);
      }
    });
    setTableColumnDisplayArray(displayArray);
  }, [effectiveTableColumnSetting]);

  const openTaskInfoForm = (taskInfo: StoryRevenueTaskInfo) => {
    setFocusTaskInfo(taskInfo);
    setTaskInfoFormVisible(true);
  };

  useEffect(() => {
    const columnList: ColumnProps<StoryRevenueTaskInfo>[] = [];
    tableColumnDisplayArray.forEach(config => {
      if (config.column_id === 'sub_flight_number') {
        columnList.push({
          title: config.column_name,
          dataIndex: 'experimentInfo.subExperimentCount',
          onCell: (record, rowIndex) => ({
            style: { width: '100%', height: 50, padding: 0 },
          }),
          width: 160,
          resize: false,
          render: (text, record, index) => (
            <SubExperimentNumberCell
              record={record}
              userRoleType={userRoleType}
              onMeegoBindLibraSuccess={updateOneTaskInfo}
              periodInfo={period}
            />
          ),
        });
        return;
      }
      const columnObject = getReviewTableColumnObject(
        config.column_id,
        config,
        userRoleType,
        (taskInfo: StoryRevenueTaskInfo, keyPath: string, newValue: any) =>
          onEditTaskInfo(taskInfo, keyPath, newValue, config),
        updateOneTaskInfo,
        openTaskInfoForm,
        allPeriodInfo,
      );
      if (config.enable && columnObject) {
        if (config.integration_check_type) {
          let toolTip: string | undefined;
          if (config.column_id === 'algorithm_related') {
            toolTip = '需求的落地是否和推荐搜索等合作';
          }
          columnObject.title = (
            <RedMarkColumnTitle title={config.column_name} columnId={config.column_id} toolTip={toolTip} />
          );
        } else if (config.column_id === 'review_duration') {
          // 需要展示下 Review 时长的计算规则
          columnObject.title = <PopoverColumnTitle title={config.column_name} columnId={config.column_id} />;
        } else {
          columnObject.title = config.column_name;
        }
        columnObject.ellipsis = true;
        columnList.push(columnObject);
      }
    });
    setTableColumnProps(columnList);
  }, [tableColumnDisplayArray, expandedRowKeys, allPeriodInfo, storyList]);

  useEffect(() => {
    if (!storyList) {
      return;
    }
    const my = storyList.filter(it => {
      const { owners } = it.meegoInfo;
      const ownersEmail = compact(owners.map(owner => owner.email));
      return ownersEmail.includes(userEmail);
    });
    setMyStoryList(my);

    const bl = storyList.filter(it => it.meegoInfo?.type !== '技术需求');
    setBusinessStoryList(bl);

    const tl = storyList.filter(it => it.meegoInfo?.type === '技术需求');
    setTechStoryList(tl);
  }, [storyList, userEmail]);

  useEffect(() => {
    if (!expandTrigger || !expandTrigger.taskInfo._id) {
      return;
    }
    if (expandTrigger.expand) {
      setExpandedRowKeys([expandTrigger.taskInfo._id]);
    } else {
      setExpandedRowKeys([]);
    }
  }, [expandTrigger]);

  useEffect(() => {
    const newConfigs = [
      {
        key: 'product',
        title: '产品需求',
        data: businessStoryList,
      },
      {
        key: 'tech',
        title: '技术需求',
        data: techStoryList,
      },
      {
        key: 'mine',
        title: '只看我的需求',
        data: myStoryList,
      },
    ];

    setTabConfigs(newConfigs);
  }, [myStoryList, businessStoryList, techStoryList]);

  const applyColumnSetting = async (status: { [key: string]: ColumnCustomStatus }) => {
    const newSetting = tableColumnSetting.concat([]);
    newSetting.forEach(it => {
      const groupStatus = status[it.group_id];
      if (!groupStatus) {
        return;
      }
      it.configs.forEach(it2 => {
        const configStatus = groupStatus[it2.column_id];
        if (configStatus) {
          it2.enable = groupStatus[it2.column_id].selectedEnable;
        }
      });
    });
    setTableColumnSetting(newSetting);
    if (Object.keys(status).length === 0) {
      return;
    }
    await updateRevenueTableColumnSetting({
      data: {
        userEmail,
        newSetting,
      },
    });
  };

  useEffect(() => {
    function handleResize() {
      setWindowHeight(window.innerHeight);
    }
    window.addEventListener('resize', handleResize);
    handleResize();
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  const handleRow = (record?: StoryRevenueTaskInfo, index?: number) => {
    if (record?.updating) {
      return {
        style: {
          background: 'rgba(var(--semi-light-green-0), 0.75)',
        },
      };
    } else {
      return {};
    }
  };

  return (
    <Spin spinning={filterLoading || loading}>
      <Tabs defaultActiveKey={defaultTab}>
        {tabConfigs.map((it, index) => (
          <TabPane tab={it.title} itemKey={it.key} key={index}>
            <div style={{ height: 50, alignItems: 'center', paddingTop: 6 }}>
              <ReviewTableOperation
                columnSetting={effectiveTableColumnSetting.groups}
                filterStatus={filterStatus}
                onFilterStatusChange={status => setFilterStatus(status)}
                filterType={filterType}
                updateType={type => setFilterType(type)}
                onFilterApply={applyFilter}
                applyFiltered={filterApplyed}
                userFilterSetting={userFilterSetting}
                updateUserFilterSetting={newSetting => setUserFilterSetting(userFilterSetting)}
                includeTerminated={filterIncludeTerminated}
                updateInlcudeTerminated={status => setFilterIncludeTerminated(status)}
              />
              <CustomTableColumnPanelButton
                userSetting={effectiveTableColumnSetting.groups}
                onSettingApply={async status => {
                  setLoading(true);
                  await applyColumnSetting(status);
                  setLoading(false);
                }}
              />
            </div>
            <Card bodyStyle={{ padding: 0 }}>
              <Table
                columns={tableColumnProps}
                dataSource={displayStorys[index]}
                rowKey={'_id'}
                onRow={(record: StoryRevenueTaskInfo, rowIndex: number) => handleRow(record, rowIndex)}
                pagination={{
                  pageSize: 10,
                  currentPage: tablePage[index],
                  total: it.data.length,
                  onPageChange: (page: number) => {
                    const newPages = tablePage.concat([]);
                    newPages[index] = page;
                    setTablePage(newPages);
                  },
                }}
                scroll={{
                  y: `${windowHeight - 250}px`,
                }}
                expandIcon={false}
                bordered={true}
                resizable={true}
                sticky={true}
              />
            </Card>
          </TabPane>
        ))}
      </Tabs>
      <SideSheet
        visible={taskInfoFormVisible}
        onCancel={() => setTaskInfoFormVisible(false)}
        width={600}
        title={
          <Space align={'start'}>
            <Text link={{ href: focusTaskInfo?.meegoInfo?.url, target: '_blank' }} ellipsis={true} icon={<IconLink />}>
              {focusTaskInfo?.meegoInfo?.name ?? ''}
            </Text>
            <Text>需求信息补充</Text>
          </Space>
        }
      >
        <TaskInfoSubmitForm
          taskInfo={focusTaskInfo}
          periodInfo={period}
          userRoleType={userRoleType}
          updateTaskInfo={newInfo => {
            updateOneTaskInfo(newInfo);
            setTaskInfoFormVisible(false);
          }}
        />
      </SideSheet>
    </Spin>
  );
};

export default StoryRevenueReviewTable;
