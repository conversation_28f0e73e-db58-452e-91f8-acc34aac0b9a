import {
  StoryRevenueReviewPeriodInfo,
  StoryRevenueReviewPeriodUpdateType,
} from '@shared/storyRevenueReviewPlatform/StoryRevenueReviewPeriodInfo';
import React, { useEffect, useState } from 'react';
import {
  batchSyncDataToMeego,
  filterTaskInfo,
  getBusinessByPmo,
  queryStoryRevenueReviewPeriodInfoList,
  sendReminders,
  updateStoryRevenueReviewPeriod,
  updateTaskInfoListByReviewPeriodId,
} from '@api/storyRevenueReviewPlatform';
import { OptionProps } from '@douyinfe/semi-ui/lib/es/select';
import { Button, Modal, Select, Space, Spin, Tag, Toast, Tooltip, Typography } from '@douyinfe/semi-ui';
import {
  IconAlertTriangle,
  IconArchive,
  IconExport,
  IconLock,
  IconRefresh,
  IconSync,
  IconUnlock,
} from '@douyinfe/semi-icons';
import { ConditionType, FilterGroupType } from '@shared/utils/conditionFilter';
import { StoryRevenueTaskInfo } from '@shared/storyRevenueReviewPlatform/StoryRevenueTaskInfo';
import { StoryRevenueExcelExporter } from '@shared/storyRevenueReviewPlatform/ExportExcel/StoryRevenueExcelExporter';
import MemberManagePanelButton from '@/pages/storyRevenueReviewPlatform/memberManagePanelButton';
import {
  StoryRevenueRoleType,
  StoryRevenueUserMemberInfo,
  StoryRevenueUserMemberUpdateType,
} from '@shared/storyRevenueReviewPlatform/StoryRevenueUserMemberInfo';
import ReviewPeriodManagePanelButton from '@/pages/storyRevenueReviewPlatform/ReviewPeriodManagePanelButton';
import { strong } from 'fp-ts';
import { IconNotification } from '@douyinfe/semi-icons-lab';

const { Text } = Typography;

const StoryRevenueHomepageHeader: React.FC<{
  userRoleType: StoryRevenueRoleType;
  userEmail: string;
  storyList: StoryRevenueTaskInfo[];
  currentSelectedPeriod: StoryRevenueReviewPeriodInfo | undefined;
  onPeriodSelectChange: (info: StoryRevenueReviewPeriodInfo) => void;
  onAllPeriodsUpdate: (infos: StoryRevenueReviewPeriodInfo[]) => void;
  onUserMemberUpdate: (user: StoryRevenueUserMemberInfo, updateType: StoryRevenueUserMemberUpdateType) => void;
  onPeriodUpdate: (period: StoryRevenueReviewPeriodInfo, updateType: StoryRevenueReviewPeriodUpdateType) => void;
  // eslint-disable-next-line max-lines-per-function
}> = ({
  userRoleType,
  userEmail,
  storyList,
  currentSelectedPeriod,
  onPeriodSelectChange,
  onAllPeriodsUpdate,
  onUserMemberUpdate,
  onPeriodUpdate,
}) => {
  const [periodInfoLoading, setPeriodInfoLoading] = useState(false);
  const [periodInfos, setPeriodInfos] = useState<StoryRevenueReviewPeriodInfo[]>([]);
  const [selectedPeriodInfo, setSelectedPeriodInfo] = useState<StoryRevenueReviewPeriodInfo | undefined>(
    currentSelectedPeriod,
  );
  const [periodOptionList, setPeriodOptionList] = useState<OptionProps[]>([]);
  const [exportToLarkSheetConfirmVisible, setExportToLarkSheetConfirmVisible] = useState(false);
  const [lockReviewPeriodConfirmVisible, setLockReviewPeriodConfirmVisible] = useState(false);
  const [dsLockReviewPeriodConfirmVisible, setDsLockReviewPeriodConfirmVisible] = useState(false);
  const [syncDataToMeegoConfirmVisible, setSyncDataToMeegoConfirmVisible] = useState(false);
  const [updatePeriodConfrimVisible, setUpdatePeriodConfrimVisible] = useState(false);
  const [isExporting, setIsExporting] = useState(false);
  const [isLocking, setIsLocking] = useState(false);
  const [isDsLocking, setIsDsLocking] = useState(false);
  const [isSyncingDataToMeego, setIsSyncingDataToMeego] = useState(false);
  const [locked, setLocked] = useState(false);
  const [dsLocked, setDsLocked] = useState(false);
  const [isPeriodUpdating, setIsPeriodUpdating] = useState(selectedPeriodInfo?.updating ?? false);
  const [remindConfirmVisible, setRemindConfirmVisible] = useState(false);
  const [isReminding, setIsReminding] = useState(false);
  const [incompleteTasksCount, setIncompleteTasksCount] = useState(0);
  const [isPMO, setIsPMO] = useState(userRoleType === StoryRevenueRoleType.Admin);
  const [incompleteTasks, setIncompleteTasks] = useState<StoryRevenueTaskInfo[]>([]);
  useEffect(() => {
    setPeriodInfoLoading(true);
    queryStoryRevenueReviewPeriodInfoList({
      data: {},
    }).then(res => {
      if (res) {
        // 排序一下，将 Default 周期放到前面
        res.sort((a, b) => (b.isDefault ? 1 : 0) - (a.isDefault ? 1 : 0));
        setPeriodInfos(res);
        const urlReviewPeriodId = new URLSearchParams(window.location.search).get('reviewPeriodId');
        const periodFromUrl = urlReviewPeriodId ? res.find(item => item.reviewPeriodId === urlReviewPeriodId) : null;

        if (selectedPeriodInfo === undefined) {
          const firstDefault = periodFromUrl || res.find(item => item.isDefault) || res[0];
          setSelectedPeriodInfo(firstDefault);
        }
      }
      setPeriodInfoLoading(false);
    });
  }, []);

  useEffect(() => {
    const optionList = periodInfos.map(it => ({
      value: it.reviewPeriodId,
      label: it.isDefault ? (
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <Text>{it.reviewPeriodName}</Text>
          <Tag color="green" style={{ marginLeft: '8px' }}>
            默认
          </Tag>
        </div>
      ) : (
        <Text>{it.reviewPeriodName}</Text>
      ),
    }));
    setPeriodOptionList(optionList);
    onAllPeriodsUpdate(periodInfos);
  }, [periodInfos]);

  useEffect(() => {
    if (!selectedPeriodInfo) {
      return;
    }
    setLocked(selectedPeriodInfo.locked);
    setDsLocked(selectedPeriodInfo.dsLocked ?? false);
    onPeriodSelectChange(selectedPeriodInfo);
  }, [selectedPeriodInfo]);

  // 确认导出到飞书表格
  const handleExportToLarkSheetConfirmOK = async () => {
    // 展示 loading 态，导出按钮不可点击
    setIsExporting(true);
    // 关闭弹窗
    setExportToLarkSheetConfirmVisible(false);

    if (selectedPeriodInfo === undefined) {
      Toast.error(`当前需求收益 Review 周期错误！`);
      setIsExporting(false);
      return;
    }

    const excelReporter = new StoryRevenueExcelExporter(storyList, selectedPeriodInfo.reviewPeriodName);
    await excelReporter.didExport();
    setIsExporting(false);
  };

  // 取消导出到飞书表格
  const handleExportToLarkSheetConfirmCancel = () => {
    setExportToLarkSheetConfirmVisible(false);
  };

  // 导出飞书表格弹窗
  const showExportToLarkSheetConfirm = () => {
    setExportToLarkSheetConfirmVisible(true);
  };

  // 确认锁定 Review 周期
  const handleLockReviewPeriodConfirmOK = () => {
    setIsLocking(true);
    // 关闭弹窗
    if (selectedPeriodInfo === undefined) {
      Toast.error(`当前需求收益 Review 周期错误！`);
      setIsLocking(false);
      return;
    }

    const unixTimestamp: number = Math.floor(new Date().getTime() / 1000);
    const isLocked = !selectedPeriodInfo.locked;
    const newPeriodInfo = { ...selectedPeriodInfo, locked: isLocked };
    if (isLocked) {
      // 锁定
      newPeriodInfo.lastLockTime = unixTimestamp;
    } else {
      // 取消锁定
      newPeriodInfo.lastUnlockTime = unixTimestamp;
    }
    updateStoryRevenueReviewPeriod({ data: newPeriodInfo })
      .then(res => {
        if (res.code !== 0) {
          new Error(`${res.message}`);
        }

        // 锁定或取消锁定成功
        setIsLocking(false);
        selectedPeriodInfo.locked = !selectedPeriodInfo.locked;
        const newPeriodInfos = periodInfos.concat([]);
        const selectedIndex = newPeriodInfos.findIndex(it => it.reviewPeriodId === selectedPeriodInfo.reviewPeriodId);
        newPeriodInfos[selectedIndex].locked = selectedPeriodInfo.locked;
        setPeriodInfos(newPeriodInfos);
        setSelectedPeriodInfo({ ...newPeriodInfos[selectedIndex] });
        setLockReviewPeriodConfirmVisible(false);
      })
      .catch(err => {
        Toast.error(`锁定失败！${err}`);
        setIsLocking(false);
      });
  };

  // 确认数据存档
  const handleDsLockReviewPeriodConfirmOK = () => {
    setIsDsLocking(true);
    // 关闭弹窗
    if (selectedPeriodInfo === undefined) {
      Toast.error(`当前需求收益 Review 周期错误！`);
      setIsDsLocking(false);
      return;
    }

    const unixTimestamp: number = Math.floor(new Date().getTime() / 1000);
    const isDsLocked = !selectedPeriodInfo.dsLocked;
    const newPeriodInfo = { ...selectedPeriodInfo, dsLocked: isDsLocked };
    if (isDsLocked) {
      // DS 锁定
      newPeriodInfo.dsLockTime = unixTimestamp;
    } else {
      // 取消 DS 锁定
      delete newPeriodInfo.dsLockTime;
    }
    updateStoryRevenueReviewPeriod({ data: newPeriodInfo })
      .then(res => {
        if (res.code !== 0) {
          new Error(`${res.message}`);
        }

        // 数据存档或取消数据存档成功
        setIsDsLocking(false);
        selectedPeriodInfo.dsLocked = !selectedPeriodInfo.dsLocked;
        const newPeriodInfos = periodInfos.concat([]);
        const selectedIndex = newPeriodInfos.findIndex(it => it.reviewPeriodId === selectedPeriodInfo.reviewPeriodId);
        newPeriodInfos[selectedIndex].dsLocked = selectedPeriodInfo.dsLocked;
        setPeriodInfos(newPeriodInfos);
        setSelectedPeriodInfo({ ...newPeriodInfos[selectedIndex] });
        setDsLockReviewPeriodConfirmVisible(false);
      })
      .catch(err => {
        Toast.error(`数据存档失败！${err}`);
        setIsDsLocking(false);
      });
  };

  // 取消锁定 Review 周期
  const handleLockReviewPeriodConfirmCancel = () => {
    // 关闭弹窗
    setLockReviewPeriodConfirmVisible(false);
  };

  // 取消数据存档
  const handleDsLockReviewPeriodConfirmCancel = () => {
    // 关闭弹窗
    setDsLockReviewPeriodConfirmVisible(false);
  };

  // 锁定 Review 周期弹窗
  const showLockReviewPeriodConfirm = () => {
    setLockReviewPeriodConfirmVisible(true);
  };

  // 数据存档弹窗
  const showDsLockReviewPeriodConfirm = () => {
    setDsLockReviewPeriodConfirmVisible(true);
  };

  // 同步收益信息至 Meego 弹窗
  const showSyncDataToMeegoConfirm = () => {
    setSyncDataToMeegoConfirmVisible(true);
  };

  // 同步收益信息至 Meego 确认
  const handleSyncDataToMeegoConfirmOK = async () => {
    // 展示 loading 态
    setIsSyncingDataToMeego(true);
    // 关闭弹窗
    setSyncDataToMeegoConfirmVisible(false);

    if (selectedPeriodInfo === undefined) {
      Toast.error(`当前需求收益 Review 周期错误！`);
      setIsSyncingDataToMeego(false);
      return;
    }

    // 开始批量同步收益信息至 Meego
    const taskIdList = storyList.map(it => it._id ?? '');
    const result = await batchSyncDataToMeego({
      data: {
        periodInfo: selectedPeriodInfo,
        taskIdList,
        updatePeriodSyncingDataStatus: true,
      },
    });

    if (result.code !== 0) {
      Toast.error(`批量同步收益信息至 Meego 失败，error: ${result.msg}`);
    } else {
      Toast.success(`批量同步收益信息至 Meego 成功！`);
    }
    setIsSyncingDataToMeego(false);
  };

  // 同步收益信息至 Meego 取消
  const handleSyncDataToMeegoConfirmCancel = () => {
    setSyncDataToMeegoConfirmVisible(false);
  };

  // 刷新整个 Review 周期弹窗
  const showUpdatePeriodConfirm = () => {
    setUpdatePeriodConfrimVisible(true);
  };

  // 刷新整个 Review 周期确认
  const handleUpdatePeriodConfirmOK = async () => {
    // 展示 loading 态
    setIsPeriodUpdating(true);
    // 关闭弹窗
    setUpdatePeriodConfrimVisible(false);

    if (selectedPeriodInfo === undefined) {
      Toast.error(`当前需求收益 Review 周期错误！`);
      setIsPeriodUpdating(false);
      return;
    }

    updateTaskInfoListByReviewPeriodId({
      data: {
        reviewPeriodId: selectedPeriodInfo.reviewPeriodId,
      },
    })
      .then(result => {
        if (result.code !== 0) {
          Toast.error(`刷新 Review 周期失败，error: ${result.msg}`);
        } else {
          Toast.success(`刷新 Review 周期成功！`);
          if (onPeriodUpdate && result.data) {
            onPeriodUpdate(result.data as StoryRevenueReviewPeriodInfo, StoryRevenueReviewPeriodUpdateType.Update);
          }
        }
        setIsPeriodUpdating(false);
      })
      .catch(err => {
        Toast.error(`刷新 Review 周期失败，error: ${err}`);
        setIsPeriodUpdating(false);
      });
  };

  // 刷新整个 Review 周期取消
  const handleUpdatePeriodConfirmCancel = () => {
    setUpdatePeriodConfrimVisible(false);
  };

  // 显示催填确认对话框
  const showRemindConfirm = async () => {
    setIsReminding(true);
    try {
      // 获取当前用户信息
      if (userRoleType !== StoryRevenueRoleType.PMO && userRoleType !== StoryRevenueRoleType.Admin) {
        Toast.error('您当前不是PMO和管理员，无法催填');
        setIsReminding(false);
        return;
      }
      let result: StoryRevenueTaskInfo[] | null = [];
      if (userEmail === '<EMAIL>' || userEmail === '<EMAIL>') {
        // result = await filterTaskInfo({
        //   data: {
        //     filterRules: [
        //       {
        //         keyPath: ['fillInCompleted', 'revenueInfoCompleted'],
        //         conditionType: ConditionType.ConditionTypeIn,
        //         targetValue: [false],
        //       },
        //     ],
        //     groupType: FilterGroupType.And,
        //     includeTerminated: false,
        //     reviewPeriodId: selectedPeriodInfo?.reviewPeriodId ?? '',
        //   },
        // });
        result = storyList.filter(it => !it.fillInCompleted || !it.revenueInfoCompleted);
      } else if (userRoleType === StoryRevenueRoleType.PMO || userRoleType === StoryRevenueRoleType.Admin) {
        const business = await getBusinessByPmo({
          data: {
            userEmail,
          },
        });
        if (!business || business.length === 0) {
          Toast.info('您当前没有负责的业务线');
          setIsReminding(false);
          return;
        }

        // result = await filterTaskInfo({
        //   data: {
        //     filterRules: [
        //       {
        //         keyPath: ['meegoInfo.sourceBusiness.primary', 'meegoInfo.sourceBusiness.secondary'],
        //         conditionType: ConditionType.ConditionTypeIn,
        //         targetValue: business,
        //       },
        //       {
        //         keyPath: ['fillInCompleted', 'revenueInfoCompleted'],
        //         conditionType: ConditionType.ConditionTypeIn,
        //         targetValue: [false],
        //       },
        //     ],
        //     groupType: FilterGroupType.And,
        //     includeTerminated: false,
        //     reviewPeriodId: selectedPeriodInfo?.reviewPeriodId ?? '',
        //   },
        // });
        result = storyList.filter(it => {
          const sourceBusiness = it.meegoInfo?.sourceBusiness;
          if (!sourceBusiness) {
            return false;
          }
          return (
            sourceBusiness.some(businessItem =>
              business.some(b => b === businessItem.primary || b === businessItem.secondary),
            ) &&
            (!it.fillInCompleted || !it.revenueInfoCompleted)
          );
        });
      }
      if (result) {
        setIncompleteTasks(result);
      }
      // 设置未完成需求数量
      setIncompleteTasksCount(result?.length ?? 0);
      // 显示确认对话框
      setRemindConfirmVisible(true);
    } catch (error) {
      Toast.error(`获取未完成需求失败: ${error}`);
    } finally {
      setIsReminding(false);
    }
  };

  // 确认催填
  const handleRemindConfirmOK = async () => {
    setIsReminding(true);
    setRemindConfirmVisible(false);

    try {
      // 发送催填通知
      const reviewPeriodName = selectedPeriodInfo?.reviewPeriodName || '';
      const reviewPeriodId = selectedPeriodInfo?.reviewPeriodId || '';

      // 以10个任务为单位分批发送
      const batchSize = 10;

      const sendPromises = [];
      for (let i = 0; i < incompleteTasks.length; i += batchSize) {
        const batchTasks = incompleteTasks.slice(i, i + batchSize);
        sendPromises.push(
          sendReminders({
            data: {
              storyList: batchTasks,
              reviewPeriodName,
              reviewPeriodId,
            },
          }).catch(err => {
            console.error(`部分催填请求失败: ${err}`);
            return { success: false, error: err };
          }),
        );
      }
      Toast.success(`催填通知已开始发送！`);
      // 并行执行所有发送任务
      await Promise.all(sendPromises);
    } catch (error) {
      Toast.error(`催填失败: ${error}`);
    } finally {
      setIsReminding(false);
    }
  };

  // 取消催填
  const handleRemindConfirmCancel = () => {
    setRemindConfirmVisible(false);
  };

  // 处理成员权限变化（添加、更新、删除用户）
  const handleUserMemberUpdate = (user: StoryRevenueUserMemberInfo, updateType: StoryRevenueUserMemberUpdateType) => {
    // 如果是更新到自己的权限，需要页面全局刷新一下
    onUserMemberUpdate(user, updateType);
  };

  // 处理 Review 周期变化（添加、更新、删除周期）
  const handleReviewPeriodUpdate = (
    reviewPeriod: StoryRevenueReviewPeriodInfo,
    updateType: StoryRevenueReviewPeriodUpdateType,
  ) => {
    onPeriodUpdate(reviewPeriod, updateType);
  };

  const periodCurrentStatusDisplayText = () => {
    if (selectedPeriodInfo?.dsLocked) {
      return <Text type={'warning'}>数据已存档</Text>;
    }
    if (selectedPeriodInfo?.locked) {
      return <Text type={'warning'}>编辑已锁定</Text>;
    }

    return <Text type={'success'}>可编辑</Text>;
  };

  return (
    <div style={{ display: 'flex', justifyContent: 'space-between', height: 50, alignItems: 'center' }}>
      <Spin spinning={periodInfoLoading}>
        <div>
          <Space vertical={false} spacing={'medium'}>
            <Space vertical={false} spacing={'tight'}>
              <Text>收益Review周期</Text>
              <Select
                placeholder={'请选择'}
                optionList={periodOptionList}
                value={selectedPeriodInfo?.reviewPeriodId}
                onSelect={value => {
                  setSelectedPeriodInfo(periodInfos.find(it => it.reviewPeriodId === value));
                }}
              />
            </Space>
            <Space vertical={false} spacing={'tight'}>
              <Text>状态：</Text>
              {periodCurrentStatusDisplayText()}
            </Space>
          </Space>
        </div>
      </Spin>
      <div style={{ float: 'right' }}>
        <Space vertical={false} spacing={'medium'}>
          <Space vertical={false} spacing={3}>
            {/** 如果数据已存档，则不展示编辑锁定按钮 */}
            {userRoleType === StoryRevenueRoleType.Admin && selectedPeriodInfo && !selectedPeriodInfo.dsLocked && (
              <>
                <Tooltip content={selectedPeriodInfo && selectedPeriodInfo.locked ? '编辑解锁' : '编辑锁定'}>
                  <Button
                    icon={selectedPeriodInfo && selectedPeriodInfo.locked ? <IconUnlock /> : <IconLock />}
                    disabled={isLocking}
                    theme={'borderless'}
                    style={{ color: 'rgba(var(--semi-grey-5), 1)' }}
                    onClick={showLockReviewPeriodConfirm}
                  />
                </Tooltip>
                <Modal
                  icon={<IconAlertTriangle style={{ color: 'orange', fontSize: 24 }} />}
                  title={selectedPeriodInfo && selectedPeriodInfo.locked ? '确认是否编辑解锁' : '确认是否编辑锁定'}
                  visible={lockReviewPeriodConfirmVisible}
                  onOk={handleLockReviewPeriodConfirmOK}
                  onCancel={handleLockReviewPeriodConfirmCancel}
                  closeOnEsc={true}
                  confirmLoading={isLocking}
                >
                  <Text strong style={{ color: 'var(--semi-color-primary)' }}>
                    Review 周期：{selectedPeriodInfo?.reviewPeriodName}
                  </Text>
                  <br />
                  {selectedPeriodInfo && selectedPeriodInfo.locked
                    ? '当前版本已编辑锁定，若重新解锁则可恢复需求收益数据的编辑，请谨慎操作。'
                    : '锁定后需求收益数据将无法编辑，请谨慎操作。'}
                </Modal>
              </>
            )}
            {/** 如果编辑都未锁定，则不展示数据存档按钮 */}
            {userRoleType === StoryRevenueRoleType.Admin && selectedPeriodInfo && selectedPeriodInfo.locked && (
              <>
                <Tooltip content={selectedPeriodInfo && selectedPeriodInfo.dsLocked ? '取消数据存档' : '数据存档'}>
                  <Button
                    icon={selectedPeriodInfo && selectedPeriodInfo.dsLocked ? <IconArchive /> : <IconArchive />}
                    disabled={isDsLocking}
                    theme={'borderless'}
                    style={{ color: 'rgba(var(--semi-grey-5), 1)' }}
                    onClick={showDsLockReviewPeriodConfirm}
                  />
                </Tooltip>
                <Modal
                  icon={<IconAlertTriangle style={{ color: 'orange', fontSize: 24 }} />}
                  title={
                    selectedPeriodInfo && selectedPeriodInfo.dsLocked ? '确认是否取消数据存档' : '确认是否数据存档'
                  }
                  visible={dsLockReviewPeriodConfirmVisible}
                  onOk={handleDsLockReviewPeriodConfirmOK}
                  onCancel={handleDsLockReviewPeriodConfirmCancel}
                  closeOnEsc={true}
                  confirmLoading={isDsLocking}
                >
                  <Text strong style={{ color: 'var(--semi-color-primary)' }}>
                    Review 周期：{selectedPeriodInfo?.reviewPeriodName}
                  </Text>
                  <br />
                  {selectedPeriodInfo && selectedPeriodInfo.dsLocked
                    ? '当前版本已数据存档，若取消存档则可恢复需求收益数据的编辑、自动同步，请谨慎操作。'
                    : '数据存档后需求收益数据将无法编辑、无法自动同步，请谨慎操作。'}
                </Modal>
              </>
            )}
            {(userRoleType === StoryRevenueRoleType.Admin || userRoleType === StoryRevenueRoleType.DataAnalyst) && (
              <>
                <Tooltip content="导出到本地 Excel">
                  {isExporting ? (
                    <Spin size="middle" />
                  ) : (
                    <Button
                      icon={<IconExport />}
                      theme={'borderless'}
                      style={{ color: 'rgba(var(--semi-grey-5), 1)' }}
                      disabled={isExporting}
                      onClick={showExportToLarkSheetConfirm}
                    />
                  )}
                </Tooltip>
                <Modal
                  title={'确认导出到本地 Excel 表格？'}
                  visible={exportToLarkSheetConfirmVisible}
                  onOk={handleExportToLarkSheetConfirmOK}
                  onCancel={handleExportToLarkSheetConfirmCancel}
                  closeOnEsc={true}
                >
                  <Text strong style={{ color: 'var(--semi-color-primary)' }}>
                    文件名：{selectedPeriodInfo?.reviewPeriodName}.xlsx
                  </Text>
                  <br />
                  将该 Review 周期所有需求的收益数据导出，请注意保密！
                </Modal>
              </>
            )}
            {userRoleType === StoryRevenueRoleType.Admin && (
              <MemberManagePanelButton
                periodInfo={selectedPeriodInfo}
                onVisibleChange={() => {
                  console.log('member manage clicked');
                }}
                onUserMemberUpdate={handleUserMemberUpdate}
              />
            )}
            {userRoleType === StoryRevenueRoleType.Admin && (
              <ReviewPeriodManagePanelButton
                onVisibleChange={() => {
                  console.log('period manage clicked');
                }}
                onPeriodUpdate={handleReviewPeriodUpdate}
              />
            )}
            {/* /!* 一键催填按钮，仅对PMO和Admin角色可见，且数据未存档 *!/*/}
            {(userRoleType === StoryRevenueRoleType.PMO || userRoleType === StoryRevenueRoleType.Admin) && (
              <>
                <Tooltip content="一键催填">
                  {isReminding ? (
                    <Spin size="middle" />
                  ) : (
                    <Button
                      icon={<IconNotification />}
                      theme={'borderless'}
                      style={{ color: 'rgba(var(--semi-grey-5), 1)' }}
                      disabled={isReminding}
                      onClick={showRemindConfirm}
                    />
                  )}
                </Tooltip>
                <Modal
                  title={'确认催填'}
                  visible={remindConfirmVisible}
                  onOk={handleRemindConfirmOK}
                  onCancel={handleRemindConfirmCancel}
                  closeOnEsc={true}
                  confirmLoading={isReminding}
                >
                  <Text strong style={{ color: 'var(--semi-color-primary)' }}>
                    Review 周期：{selectedPeriodInfo?.reviewPeriodName}
                  </Text>
                  <br />
                  <Text>
                    你要催填的需求 <strong>{incompleteTasksCount}</strong> 个，请确认。
                  </Text>
                  <br />
                  <Text style={{ color: 'rgba(var(--semi-grey-5), 1)' }} size={'small'}>
                    说明：催填后将发送飞书卡片通知给需求对应的PM和DA。
                  </Text>
                </Modal>
              </>
            )}

            {/* 数据未存档，则可以进行数据同步 */}
            {userRoleType === StoryRevenueRoleType.Admin && selectedPeriodInfo && !selectedPeriodInfo.dsLocked && (
              <>
                <Tooltip content="刷新 Review 周期">
                  {isPeriodUpdating ? (
                    <Spin size="middle" />
                  ) : (
                    <Button
                      icon={<IconRefresh />}
                      theme={'borderless'}
                      style={{ color: 'rgba(var(--semi-grey-5), 1)' }}
                      disabled={isPeriodUpdating || selectedPeriodInfo.updating}
                      onClick={showUpdatePeriodConfirm}
                    />
                  )}
                </Tooltip>
                <Modal
                  title={'确定刷新 Review 周期？'}
                  visible={updatePeriodConfrimVisible}
                  onOk={handleUpdatePeriodConfirmOK}
                  onCancel={handleUpdatePeriodConfirmCancel}
                  closeOnEsc={true}
                >
                  <Text strong style={{ color: 'var(--semi-color-primary)' }}>
                    Review 周期：{selectedPeriodInfo?.reviewPeriodName}
                  </Text>
                  <br />
                  将刷新 Review 周期下的所有需求信息（但不包括实验收益信息）。
                  <strong>整个过程耗时约 1~5 min</strong>。需求列表越多，耗时越长，请耐心等待。
                  <br />
                  <Text style={{ color: 'rgba(var(--semi-grey-5), 1)' }} size={'small'}>
                    说明：因为刷新实验收益信息较为耗时，暂不提供批量方式。可手动刷新单个需求的收益信息。
                  </Text>
                </Modal>
              </>
            )}
            {userRoleType === StoryRevenueRoleType.Admin && selectedPeriodInfo && selectedPeriodInfo.dsLocked && (
              <>
                <Tooltip content="同步收益信息至 Meego">
                  {isSyncingDataToMeego ? (
                    <Spin size="middle" />
                  ) : (
                    <Button
                      icon={<IconSync />}
                      theme={'borderless'}
                      style={{ color: 'rgba(var(--semi-grey-5), 1)' }}
                      disabled={isSyncingDataToMeego || selectedPeriodInfo.isSyncingDataToMeego}
                      onClick={showSyncDataToMeegoConfirm}
                    />
                  )}
                </Tooltip>
                <Modal
                  title={'确认回传收益信息至 Meego？'}
                  visible={syncDataToMeegoConfirmVisible}
                  onOk={handleSyncDataToMeegoConfirmOK}
                  onCancel={handleSyncDataToMeegoConfirmCancel}
                  closeOnEsc={true}
                >
                  <Text strong style={{ color: 'var(--semi-color-primary)' }}>
                    Review 周期：{selectedPeriodInfo?.reviewPeriodName}
                  </Text>
                  <br />
                  批量将每个需求的「参与收益 Review 周期」、「收益 Review 状态」等信息同步至 Meego。
                  <strong>整个过程耗时约 1~2 min</strong>。
                  <br />
                  <Text style={{ color: 'rgba(var(--semi-grey-5), 1)' }} size={'small'}>
                    说明：仅筛选条件下的需求列表会同步，若要同步所有需求请清空筛选。
                  </Text>
                </Modal>
              </>
            )}
          </Space>
        </Space>
      </div>
    </div>
  );
};

export default StoryRevenueHomepageHeader;
