import React, { useEffect, useRef, useState } from 'react';
import VersionSelector from '@/component/VersionSelector';
import { buildTreeFromVersionList } from '@/component/VersionSelector/utils';
import { Col, DatePicker, Divider, Row, Space, Switch, Tag } from 'antd';
import { AlarmVersion } from '@shared/typings/tea/metric';
import dayjs from 'dayjs';
import { querySlardarInfo } from '@api/slardar';
import { SlardarInfoForGraph } from '@/pages/versionProcess/graySlardar/shared';
import StabilityChart from '@/pages/versionProcess/graySlardar/stabilityChart';
import StabilityIssueList from '@/pages/versionProcess/graySlardar/stabilityIssueList';
import { useModel } from '@edenx/runtime/model';
import AppSettingModule from '@/model/appSettingModel';
import { SlardarPlatformType } from '@pa/shared/dist/src/appSettings/appSettings';
import minMax from 'dayjs/plugin/minMax';
import UserSettingModule from '@/model/userSettingModel';
import { useNavigate, useSearchParams } from '@edenx/runtime/router';
import { getVersions } from '@/utils/version';
import { versionCodeToVersion } from '@shared/utils/version_utils';
import { queryPreviousVersion } from '@api/version';
import BitsVersionInfoCard from '@/component/BitsVersionInfoCard';
import { useCustomSearchParams } from '@/utils/customSearchParams';
import VersionSettingModule from '@/model/versionSettingModel';
import { VersionStageChecklistStatus } from '@shared/releasePlatform/versionStage';
import { DeviceLevel } from '@shared/common';
import { PlatformType } from '@pa/shared/dist/src/core';

function startTime(publishTime: number) {
  return dayjs.unix(publishTime).add(1, 'h').startOf('h');
}

function endTime(publishTime: number) {
  dayjs.extend(minMax);
  return dayjs.min(startTime(publishTime).add(3, 'd'), dayjs()) || dayjs();
}

export interface StabilityProps {
  deviceLevel: DeviceLevel;
  versionChecklistStatus?: VersionStageChecklistStatus[];
}

const Stability: React.FC<StabilityProps> = ({ deviceLevel, versionChecklistStatus }) => {
  // 版本列表
  const [versions, setVersions] = useState<AlarmVersion[]>([]);

  // 已经选择的“版本”
  const [selected, setSelected] = useState<string | undefined>(undefined);

  // 已经选择的“最佳版本”
  const [selectedBest, setSelectedBest] = useState<string | undefined>(undefined);

  // 所有的这么多 Slardar 指标
  const [slardarMetricInfo, setSlardarMetricInfo] = useState<SlardarInfoForGraph[]>([]);

  const [appSettingState] = useModel(AppSettingModule);
  const [versionSettingState] = useModel(VersionSettingModule);

  const [userSettingState] = useModel(UserSettingModule);

  const [baseVersion, setBaseVersion] = useState<string>('');

  const [isAverage, setIsAverage] = useState(appSettingState.info.platform === PlatformType.Android);

  const navigate = useNavigate();

  // 自定义放量时间
  const [customPublishTime, setCustomPublishTime] = useState<number | undefined>();

  const [searchParams, setCustomSearchParam] = useCustomSearchParams(
    useSearchParams(),
    appSettingState,
    versionSettingState,
  );
  const hasInit = useRef(false);

  const updateSelected = async (sel_str: string, sel_version: string) => {
    setSelected(sel_str);
    setBaseVersion('');
    const res = await queryPreviousVersion({
      data: {
        aid: appSettingState.info.businessInfo.aid,
        version: sel_version,
        versionCode: sel_str,
      },
    });
    if (res?.version_code) {
      setBaseVersion(res.version_code);
    }
  };

  useEffect(() => {
    if (!hasInit.current) {
      return;
    }
    setCustomSearchParam('selected', selected);
    setCustomSearchParam('best', selectedBest);
  }, [selected, selectedBest]);

  const updateSlardarInfo = async () => {
    const resp = await querySlardarInfo({
      data: { aid: appSettingState.info.businessInfo.aid, platform: appSettingState.info.platform.toString() },
    });
    return resp.InfoList.map(it => ({
      metricName: it.Id,
      displayName: it.DisplayName,
      displayPriority: it.DisplayPriority,
    }));
  };

  useEffect(() => {
    const versionPromise = getVersions(
      appSettingState.info.businessInfo.aid,
      appSettingState.info.platform === PlatformType.iOS ? SlardarPlatformType.iOS : SlardarPlatformType.Android,
    );
    const slardarInfoPromise = updateSlardarInfo();
    Promise.all([versionPromise, slardarInfoPromise]).then(async ([version, slardarInfo]) => {
      setVersions(version);
      const paramSelected = searchParams.get('selected');
      const paramBest = searchParams.get('best');
      const findVersion = paramSelected ? version.find(v => v.versionCode.toString() === paramSelected) : undefined;
      hasInit.current = true;
      if (findVersion) {
        await updateSelected(findVersion.versionCode, findVersion.version);
      } else {
        console.log('setting selected!');
        await updateSelected(version[0].versionCode, version[0].version);
      }
      if (paramBest) {
        setSelectedBest(paramBest);
      }
      setSlardarMetricInfo(slardarInfo);
    });
  }, [appSettingState.info, userSettingState.info]);

  const onVersionChange = async (v: string | string[]) => {
    const ver_str = Array.isArray(v) ? v[0] : v;
    await updateSelected(ver_str, versionCodeToVersion(ver_str));
  };

  const onBestVersionChange = async (v: string | string[]) => {
    const ver_str = Array.isArray(v) ? v[0] : v;
    setSelectedBest(ver_str);
  };

  const getVersionTimestamp = (version_code?: string) =>
    versions.find(it => it.versionCode === version_code)?.timestamp ?? 0;

  const buildCrashIssueList = () => {
    if (!selected) {
      return <>请选择一个版本！</>;
    }
    return (
      <StabilityIssueList
        versionCode={selected}
        baseVersionCode={selectedBest ?? '0'}
        start_time={startTime(getVersionTimestamp(selected)).unix()}
        end_time={endTime(getVersionTimestamp(selected)).unix()}
        deviceLevel={deviceLevel}
      />
    );
  };
  return (
    <>
      <Row gutter={[16, 16]}>
        <Col span={24}>
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Space size={'middle'}>
              选择版本：
              <VersionSelector
                data={buildTreeFromVersionList(versions)}
                onChange={onVersionChange}
                value={selected!}
                isSingle={true}
              />
              对比最佳版本：
              <VersionSelector
                data={buildTreeFromVersionList(versions)}
                onChange={onBestVersionChange}
                value={selectedBest!}
                isSingle={true}
              />
              <Switch
                checkedChildren="求和平均"
                unCheckedChildren="原始数据"
                defaultChecked={isAverage}
                onChange={data => setIsAverage(data)}
              />
            </Space>
          </div>
        </Col>
        <Col span={24}>
          <BitsVersionInfoCard
            key={selected?.toString() ?? 'undefined'}
            version_code={selected}
            start_time={it => startTime(it).unix()}
            end_time={it => endTime(it).unix()}
          />
        </Col>
      </Row>
      <Divider />
      <Space direction="horizontal">
        自定义版本放量时间：
        <DatePicker showTime onChange={v => setCustomPublishTime(v ? v.unix() : undefined)} />
        <Tag color={customPublishTime ? 'green' : 'red'}>
          {customPublishTime
            ? `当前自定义放量时间：${dayjs.unix(customPublishTime).format('YYYY-MM-DD HH:mm:ss')}`
            : '未采用自定义放量时间'}
        </Tag>
      </Space>
      <Divider />
      <StabilityChart
        versions={[selected, baseVersion, selectedBest]}
        metrics={slardarMetricInfo}
        range={[startTime(getVersionTimestamp(selected)).unix(), endTime(getVersionTimestamp(selected)).unix()]}
        isAverage={isAverage}
        baseTimestamp={customPublishTime}
        deviceLevel={deviceLevel}
      />
      <Divider />
      {buildCrashIssueList()}
    </>
  );
};

export default Stability;
