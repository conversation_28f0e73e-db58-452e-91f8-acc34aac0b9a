import { SlardarInfoForGraph } from '@/pages/versionProcess/graySlardar/shared';
import React from 'react';
import SlardarChart from '@/component/Charts/SlardarChart';
import { Col, Row } from 'antd';
import { useModel } from '@edenx/runtime/model';
import AppSettingModule from '@/model/appSettingModel';
import { put_back_by } from '@shared/utils/tools';
import { DeviceLevel } from '@shared/common';

export interface StabilityChartProps {
  versions: (string | undefined)[];
  metrics: SlardarInfoForGraph[];
  range: [number, number];
  isAverage: boolean;
  baseTimestamp?: number;
  deviceLevel: DeviceLevel;
}

const StabilityChart: React.FC<StabilityChartProps> = ({
  versions,
  metrics,
  range,
  isAverage,
  baseTimestamp,
  deviceLevel,
}) => {
  const pv = versions.map(it => it?.toString() || '');
  const [appSettingStatus] = useModel(AppSettingModule);
  const graphs = metrics
    .filter(it => !['JavaSmallInstance', 'JavaMemLeak'].includes(it.metricName))
    .map(it => (
      <Col
        span={8}
        key={`${appSettingStatus.info.platform}-${it.metricName}-${isAverage}-${pv.join('')}-${baseTimestamp || 0}`}
      >
        <SlardarChart
          title={it.displayName}
          slardarInfoId={it.metricName}
          versions={versions}
          timeRange={range}
          isAverage={isAverage}
          baseTimestamp={baseTimestamp}
          deviceLevel={deviceLevel}
        />
      </Col>
    ));
  return (
    <>
      <Row gutter={[16, 24]} key={`SlardarGraph_${pv.join('')}`}>
        {graphs}
      </Row>
    </>
  );
};

export default StabilityChart;
