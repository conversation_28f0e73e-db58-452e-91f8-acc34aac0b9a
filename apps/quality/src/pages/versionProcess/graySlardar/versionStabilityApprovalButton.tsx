import { VersionStageChecklistStatus } from '@shared/releasePlatform/versionStage';
import { Button, Col, Empty, Form, Row, Select, SideSheet, Space, Spin, Toast, Typography } from '@douyinfe/semi-ui';
import { IllustrationConstruction, IllustrationConstructionDark } from '@douyinfe/semi-illustrations';
import React, { useEffect, useState } from 'react';
import { useModel } from '@edenx/runtime/model';
import AppSettingModule from '@/model/appSettingModel';
import { findOnprogressChecklistStatus, updateCheckItem } from '@api/releasePlatform';
import {
  CheckItemStatus,
  ItemType,
  StabilityMetricItemInfo,
  StabilityMetricsItemStatus,
} from '@shared/releasePlatform/versionStageInfoCheckList';
import { MAIN_HOST_HTTPS } from '@pa/shared/dist/src/appSettings/appSettings';

const { Text, Title } = Typography;

const VersionStabilityApprovalForm: React.FC = () => {
  const [selectedVersion, setSelectedVersion] = useState<string>();
  const [selectedStage, setSelectedStage] = useState<string>();
  const [selectedCheckItemId, setSelectedCheckItemId] = useState<string>();
  const [selectedApproveResult, setSelectedApproveResult] = useState<StabilityMetricsItemStatus>();
  const [resultDescription, setReasultDescription] = useState<string>();
  const [releasePlatformUrl, setReleasePlatformUrl] = useState<string>();

  const [appSettingState] = useModel(AppSettingModule);

  const [needApproveStatusList, setNeedApproveStatusList] = useState<VersionStageChecklistStatus[]>([]);
  const [approveVersionOptionList, setApproveVersionOptionList] = useState<{ value: string; label: string }[]>([]);
  const [approveStageOptionList, setApproveStageOptionList] = useState<{ value: string; label: string }[]>([]);
  const [approveSlardarTypeOptionList, setApproveSlardarTypeOptionList] = useState<{ value: string; label: string }[]>(
    [],
  );
  const [approveResultOptionList, setApproveResultOptionList] = useState<
    { value: StabilityMetricsItemStatus; label: string }[]
  >([]);

  const [versionChecklistStatus, setVersionChecklistStatus] = useState<VersionStageChecklistStatus[]>([]);
  const [loading, setloading] = useState(false);
  const [submitting, setSubmitting] = useState(false);

  useEffect(() => {
    let newUrl = `${MAIN_HOST_HTTPS}/release/list?appid=${appSettingState.info.id}`;
    if (selectedVersion) {
      newUrl += `&show_detail=true&version=${selectedVersion}`;
    }
    if (selectedStage) {
      const selectedStatus = needApproveStatusList.find(it => it.versionInfo.version === selectedVersion);
      const stageInfo = selectedStatus?.stageCheckStatus.find(
        it => it.stageInfo.stage_name === selectedStage,
      )?.stageInfo;
      if (stageInfo?.parent_stage_name) {
        newUrl += `&main_stage=${stageInfo?.parent_stage_name}&sub_stage=${selectedStage}`;
      } else {
        newUrl += `&main_stage=${selectedStage}`;
      }
    }
    setReleasePlatformUrl(newUrl);
  }, [appSettingState.info, selectedVersion, selectedStage]);

  useEffect(() => {
    setloading(true);
    findOnprogressChecklistStatus({
      data: {
        appId: appSettingState.info.id,
      },
    }).then(res => {
      setVersionChecklistStatus(res ?? []);
      setloading(false);
    });
  }, [appSettingState.info]);

  useEffect(() => {
    const approvalList: VersionStageChecklistStatus[] = [];
    versionChecklistStatus.forEach(status => {
      let hasStabilityItem = false;
      status.stageCheckStatus.forEach(it => {
        const stabilityItems = it.checklist.check_items.filter(item => item.item_type === ItemType.Slardar);
        if (stabilityItems.length > 0) {
          hasStabilityItem = true;
        }
      });
      if (hasStabilityItem) {
        approvalList.push(status);
      }
    });
    setNeedApproveStatusList(approvalList);
  }, [versionChecklistStatus]);

  useEffect(() => {
    const versionOptionList = needApproveStatusList.map(it => ({
      value: it.versionInfo.version,
      label: it.versionInfo.version,
    }));
    setApproveVersionOptionList(versionOptionList);
  }, [needApproveStatusList]);

  useEffect(() => {
    setSelectedStage(undefined);
    const selectedStatus = needApproveStatusList.find(it => it.versionInfo.version === selectedVersion);
    if (selectedStatus) {
      const needApproveStages = selectedStatus.stageCheckStatus.filter(it => {
        const stabilityItems = it.checklist.check_items.filter(item => item.item_type === ItemType.Slardar);
        if (stabilityItems.length > 0) {
          return true;
        }
        return false;
      });
      const newStageOptionList = needApproveStages.map(it => ({
        value: it.stageInfo.stage_name,
        label: it.stageInfo.display_name,
      }));
      setApproveStageOptionList(newStageOptionList);
    } else {
      setApproveVersionOptionList([]);
    }
  }, [selectedVersion]);

  useEffect(() => {
    setSelectedCheckItemId(undefined);
    const selectedStatus = needApproveStatusList.find(it => it.versionInfo.version === selectedVersion);
    if (selectedStatus) {
      const checklist = selectedStatus.stageCheckStatus.find(
        it => it.stageInfo.stage_name === selectedStage,
      )?.checklist;
      const stabilityCheckItems = checklist
        ? checklist.check_items.filter(it => it.item_type === ItemType.Slardar)
        : [];
      const newSlardarTypeOptions = stabilityCheckItems.map(it => {
        const itemInfo = it.item_info as StabilityMetricItemInfo;
        return {
          value: it.check_item_id,
          label: itemInfo.crash_type,
        };
      });
      setApproveSlardarTypeOptionList(newSlardarTypeOptions);
    } else {
      setApproveSlardarTypeOptionList([]);
    }
  }, [selectedStage]);

  const resultOptions = [
    {
      value: StabilityMetricsItemStatus.Pass,
      label: '通过',
    },
    {
      value: StabilityMetricsItemStatus.Blocked,
      label: '阻塞',
    },
    {
      value: StabilityMetricsItemStatus.Exempted,
      label: '豁免',
    },
  ];

  useEffect(() => {
    if (selectedCheckItemId) {
      const selectedStatus = needApproveStatusList.find(it => it.versionInfo.version === selectedVersion);
      const checklist = selectedStatus?.stageCheckStatus?.find(
        it => it.stageInfo.stage_name === selectedStage,
      )?.checklist;
      const checkItem = checklist?.check_items?.find(it => it.check_item_id === selectedCheckItemId);
      if (checkItem) {
        const itemInfo = checkItem.item_info as StabilityMetricItemInfo;
        if (itemInfo?.status === StabilityMetricsItemStatus.TBD) {
          setSelectedApproveResult(undefined);
        } else {
          setSelectedApproveResult(itemInfo?.status);
        }
        setReasultDescription(itemInfo?.result_detail);
      } else {
        setSelectedApproveResult(undefined);
      }
    } else {
      setSelectedApproveResult(undefined);
    }
  }, [selectedCheckItemId]);

  const submitApproveResult = () => {
    const selectedStatus = needApproveStatusList.find(it => it.versionInfo.version === selectedVersion);
    const checklist = selectedStatus?.stageCheckStatus?.find(
      it => it.stageInfo.stage_name === selectedStage,
    )?.checklist;
    const checkItem = checklist?.check_items?.find(it => it.check_item_id === selectedCheckItemId);
    const itemInfo = checkItem?.item_info as StabilityMetricItemInfo;
    if (checkItem && itemInfo && selectedApproveResult !== undefined) {
      setSubmitting(true);
      itemInfo.status = selectedApproveResult;
      itemInfo.result_detail = resultDescription ?? '';
      if (
        selectedApproveResult === StabilityMetricsItemStatus.Exempted ||
        selectedApproveResult === StabilityMetricsItemStatus.Pass
      ) {
        checkItem.status = CheckItemStatus.Exempt;
      }
      updateCheckItem({
        data: {
          appId: appSettingState.info.id,
          version: selectedVersion ?? '',
          stage: selectedStage ?? '',
          checkItemId: selectedCheckItemId ?? '',
          updateData: checkItem,
        },
      }).then(res => {
        if (res.code === 0) {
          Toast.success('更新成功');
        } else {
          Toast.success('更新失败');
        }
        setSubmitting(false);
      });
    } else {
      Toast.error('更新失败，未知错误，请联系管理员');
    }
  };

  return needApproveStatusList.length > 0 || loading ? (
    <Spin spinning={loading}>
      <Form layout={'vertical'}>
        <Row gutter={16}>
          <Col span={12}>
            <Form.Input field={'appName'} label={'App'} initValue={appSettingState.info.name} disabled={true} />
          </Col>
          <Col span={12}>
            <Form.Input field={'platform'} label={'平台'} initValue={appSettingState.info.platform} disabled={true} />
          </Col>
        </Row>
        <Row gutter={16}>
          <Col span={12}>
            <Space vertical={true} style={{ width: '100%' }} align={'start'}>
              <Space>
                <Text strong={true}>评估版本</Text>
                <Text style={{ color: 'rgba(var(--semi-red-6), 1)' }}>*</Text>
              </Space>
              <Select
                style={{ width: '100%' }}
                onSelect={value => setSelectedVersion(value as string)}
                optionList={approveVersionOptionList}
                value={selectedVersion}
                placeholder={'请选择要评估准出的版本'}
              />
            </Space>
          </Col>
          <Col span={12}>
            <Space vertical={true} style={{ width: '100%' }} align={'start'}>
              <Space>
                <Text strong={true}>评估阶段</Text>
                <Text style={{ color: 'rgba(var(--semi-red-6), 1)' }}>*</Text>
              </Space>
              <Select
                disabled={!selectedVersion}
                style={{ width: '100%' }}
                onSelect={value => setSelectedStage(value as string)}
                optionList={approveStageOptionList}
                value={selectedStage}
                placeholder={'请选择要评估准出的阶段'}
              />
            </Space>
          </Col>
        </Row>
        <div style={{ height: 10 }} />
        <Row gutter={16}>
          <Col span={12}>
            <Space vertical={true} style={{ width: '100%' }} align={'start'}>
              <Space>
                <Text strong={true}>指标类型</Text>
                <Text style={{ color: 'rgba(var(--semi-red-6), 1)' }}>*</Text>
              </Space>
              <Select
                disabled={!selectedStage}
                style={{ width: '100%' }}
                onSelect={value => setSelectedCheckItemId(value as string)}
                optionList={approveSlardarTypeOptionList}
                value={selectedCheckItemId}
                placeholder={'请选择要评估准出的指标'}
              />
            </Space>
          </Col>
          <Col span={12}>
            <Space vertical={true} style={{ width: '100%' }} align={'start'}>
              <Space>
                <Text strong={true}>准出结论</Text>
                <Text style={{ color: 'rgba(var(--semi-red-6), 1)' }}>*</Text>
              </Space>
              <Select
                disabled={!selectedCheckItemId}
                style={{ width: '100%' }}
                onSelect={value => setSelectedApproveResult(value as StabilityMetricsItemStatus)}
                optionList={resultOptions}
                value={selectedApproveResult}
                placeholder={'请选择准出结论'}
              />
            </Space>
          </Col>
        </Row>
        <Row>
          <Col span={24}>
            <Form.TextArea
              field={'description'}
              label={'详细结论'}
              rows={10}
              initValue={resultDescription}
              onChange={e => setReasultDescription(e)}
            />
          </Col>
        </Row>
        <Space>
          <Button
            type={'primary'}
            theme={'solid'}
            disabled={selectedApproveResult === undefined}
            onClick={submitApproveResult}
            loading={submitting}
          >
            提交结论
          </Button>
          <Button type={'primary'} theme={'solid'} onClick={() => window.open(releasePlatformUrl)}>
            跳转发版平台
          </Button>
        </Space>
      </Form>
    </Spin>
  ) : (
    <Spin spinning={loading}>
      <Empty
        image={<IllustrationConstruction style={{ width: 150, height: 150 }} />}
        darkModeImage={<IllustrationConstructionDark style={{ width: 150, height: 150 }} />}
        title={'暂无需要评估的版本'}
        description={
          <Button type={'primary'} theme={'solid'} onClick={() => window.open(releasePlatformUrl)}>
            跳转发版平台
          </Button>
        }
      />
    </Spin>
  );
};

const VersionStabilityApprovalButton: React.FC = () => {
  const [sheetVisible, setSheetVisible] = useState(false);
  const [{ info }] = useModel(AppSettingModule);

  return (
    <>
      <Button type={'primary'} theme={'solid'} onClick={() => setSheetVisible(true)}>
        版本准出评估
      </Button>
      <SideSheet
        keepDOM={true}
        onCancel={() => setSheetVisible(false)}
        visible={sheetVisible}
        width={800}
        title={'稳定性指标准出评估'}
      >
        <VersionStabilityApprovalForm />
      </SideSheet>
    </>
  );
};

export default VersionStabilityApprovalButton;
