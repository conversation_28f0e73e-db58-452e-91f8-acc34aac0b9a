import React, { useEffect, useState } from 'react';
import { CrashType } from '@shared/typings/slardar/crash/issueListSearch';
import { useModel } from '@edenx/runtime/model';
import AppSettingModule from '@/model/appSettingModel';
import { Tabs } from 'antd';
import SlardarCrashList from '@/component/SlardarCrashList';
import { querySlardarInfo } from '@api/slardar';
import { DeviceLevel } from '@shared/common';

interface StabilityIssueListParams {
  versionCode: string;
  baseVersionCode: string;
  start_time: number;
  end_time: number;
  deviceLevel: DeviceLevel;
}

const StabilityIssueList: React.FC<StabilityIssueListParams> = ({
  versionCode,
  baseVersionCode,
  start_time,
  end_time,
  deviceLevel,
}) => {
  const [{ info }] = useModel(AppSettingModule);
  const [CrashItem, setCrashItem] = useState<{ displayName: string; crashType: CrashType }[]>([]);
  useEffect(() => {
    querySlardarInfo({
      data: { aid: info.businessInfo.aid, platform: info.platform.toString() },
    }).then(resp => {
      const Item = resp.InfoList.map(item => ({
        displayName: item.DisplayName,
        crashType: item.Id as CrashType,
      }));
      setCrashItem(Item);
    });
  }, [info]);
  return (
    <Tabs
      onChange={(key: string) => {
        console.log(`issueListChange:${key}`);
      }}
      key={versionCode}
      type="card"
    >
      {CrashItem.map(item => (
        <Tabs.TabPane key={item.crashType} tab={item.displayName}>
          <SlardarCrashList
            start_time={start_time}
            end_time={end_time}
            crashType={item.crashType}
            versionCode={versionCode}
            baseVersionCode={baseVersionCode}
            deviceLevel={deviceLevel}
          />
        </Tabs.TabPane>
      ))}
    </Tabs>
  );
};

export default StabilityIssueList;
