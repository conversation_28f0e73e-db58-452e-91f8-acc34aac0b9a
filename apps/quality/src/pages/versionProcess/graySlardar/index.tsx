import React from 'react';
import { Tabs } from 'antd';
import Stability from '@/pages/versionProcess/graySlardar/stability';
import StabilityIssueListForVersion from '@/pages/versionProcess/graySlardar/stabilityIssueListForVersion';
import { useModel } from '@edenx/runtime/model';
import AppSettingModule from '@/model/appSettingModel';
import { noop } from 'lodash';
import VersionStabilityApprovalButton from '@/pages/versionProcess/graySlardar/versionStabilityApprovalButton';
import { BusinessType } from '@pa/shared/dist/src/appSettings/appSettings';
import { DeviceLevel } from '@shared/common';
import { PlatformType } from '@pa/shared/dist/src/core';

const GraySlardar: React.FC = () => {
  const [{ info }] = useModel(AppSettingModule);

  const items = [
    {
      label: '稳定性',
      key: 'Stability',
      children: <Stability deviceLevel={DeviceLevel.ALL} />,
    },
    {
      label: '高端机稳定性',
      key: 'HighStability',
      children: <Stability deviceLevel={DeviceLevel.HIGH} />,
    },
    {
      label: '低端机稳定性',
      key: 'LowStability',
      children: <Stability deviceLevel={DeviceLevel.LOW} />,
    },
  ];
  if (info.platform === PlatformType.Android && info.businessID === BusinessType.LV) {
    items.push(
      {
        label: '聚合稳定性',
        key: 'Aggregation',
        children: <StabilityIssueListForVersion deviceLevel={DeviceLevel.ALL} />,
      },
      {
        label: '高端机聚合稳定性',
        key: 'HighAggregation',
        children: <StabilityIssueListForVersion deviceLevel={DeviceLevel.HIGH} />,
      },
      {
        label: '低端机聚合稳定性',
        key: 'LowAggregation',
        children: <StabilityIssueListForVersion deviceLevel={DeviceLevel.LOW} />,
      },
    );
  }
  return <Tabs onChange={noop} type="card" items={items} tabBarExtraContent={<VersionStabilityApprovalButton />} />;
};

export default GraySlardar;
