import React, { useEffect, useRef, useState } from 'react';
import { Col, Divider, Row, Space } from 'antd';
import dayjs from 'dayjs';
import { querySlardarInfo } from '@api/slardar';
import { SlardarInfoForGraph } from '@/pages/versionProcess/offlineSlardar/shared';
import StabilityIssueList from '@/pages/versionProcess/offlineSlardar/stabilityIssueList';
import { useModel } from '@edenx/runtime/model';
import AppSettingModule from '@/model/appSettingModel';
import { SlardarPlatformType } from '@pa/shared/dist/src/appSettings/appSettings';
import minMax from 'dayjs/plugin/minMax';
import UserSettingModule from '@/model/userSettingModel';
import { useNavigate, useSearchParams } from '@edenx/runtime/router';
import { getAppVersion, getVersionIntegrationTime } from '@api/version';
import { useCustomSearchParams } from '@/utils/customSearchParams';
import VersionSettingModule from '@/model/versionSettingModel';
import { VersionStageChecklistStatus } from '@shared/releasePlatform/versionStage';
import { DeviceLevel } from '@shared/common';
import { PlatformType } from '@pa/shared/dist/src/core';
import OfflineVersionSelector from '@/component/VersionSelector/offline_index';
import { GroupName } from '@shared/utils/offlineslardar';

function startTime(publishTime: number) {
  return dayjs.unix(publishTime);
}

function endTime(publishTime: number) {
  dayjs.extend(minMax);
  return dayjs.min(startTime(publishTime).add(28, 'd'), dayjs()) || dayjs();
}

export interface StabilityProps {
  deviceLevel: DeviceLevel;
  versionChecklistStatus?: VersionStageChecklistStatus[];
  groupName: GroupName | null;
}

const Stability: React.FC<StabilityProps> = ({ deviceLevel, groupName, versionChecklistStatus }) => {
  // 版本列表
  const [versions, setVersions] = useState<string[]>([]);
  // 已经选择的“版本”
  const [selected, setSelected] = useState<string | undefined>(undefined);
  // 已经选择的“最佳版本”
  const [selectedBest, setSelectedBest] = useState<string | undefined>(undefined);
  // 所有的这么多 Slardar 指标
  const [slardarMetricInfo, setSlardarMetricInfo] = useState<SlardarInfoForGraph[]>([]);
  const [appSettingState] = useModel(AppSettingModule);
  const [versionSettingState] = useModel(VersionSettingModule);
  const [userSettingState] = useModel(UserSettingModule);
  const navigate = useNavigate();
  const [apiStartTime, setApiStartTime] = useState<number | undefined>();
  const [searchParams, setCustomSearchParam] = useCustomSearchParams(
    useSearchParams(),
    appSettingState,
    versionSettingState,
  );
  const hasInit = useRef(false);
  useEffect(() => {
    if (!hasInit.current) {
      return;
    }
    setCustomSearchParam('selected', selected);
    setCustomSearchParam('best', selectedBest);
  }, [selected, selectedBest]);
  const updateSlardarInfo = async () => {
    const resp = await querySlardarInfo({
      data: { aid: appSettingState.info.businessInfo.aid, platform: appSettingState.info.platform.toString() },
    });
    return resp.InfoList.map(it => ({
      metricName: it.Id,
      displayName: it.DisplayName,
      displayPriority: it.DisplayPriority,
    }));
  };
  useEffect(() => {
    const fetchInitialData = async () => {
      const platform =
        appSettingState.info.platform === PlatformType.iOS ? SlardarPlatformType.iOS : SlardarPlatformType.Android;

      const [versionResponse, slardarInfo] = await Promise.all([
        getAppVersion({ data: { aid: appSettingState.info.businessInfo.aid, platform } }),
        updateSlardarInfo(),
      ]);

      setSlardarMetricInfo(slardarInfo);

      if (versionResponse.code === 200 && versionResponse.data && versionResponse.data.length > 0) {
        const versionList = versionResponse.data;
        setVersions(versionList);

        const paramSelected = searchParams.get('selected');
        const paramBest = searchParams.get('best');

        // 根据 URL 参数或默认选择第一个版本
        const initialSelected = paramSelected && versionList.includes(paramSelected) ? paramSelected : versionList[0];
        setSelected(initialSelected);

        if (paramBest && versionList.includes(paramBest)) {
          setSelectedBest(paramBest);
        }
      } else {
        setVersions([]);
      }
      hasInit.current = true;
    };

    fetchInitialData();
  }, [appSettingState.info, userSettingState.info]);

  useEffect(() => {
    const fetchStartTime = async () => {
      setApiStartTime(undefined); // 请求开始，先清空状态
      if (selected) {
        try {
          const res: any = await getVersionIntegrationTime({
            data: {
              aid: appSettingState.info.businessInfo.aid,
              platform:
                appSettingState.info.platform === PlatformType.Android
                  ? SlardarPlatformType.Android
                  : SlardarPlatformType.iOS,
              version: selected,
            },
          });
          if (res.data !== null) {
            // 成功，设置时间戳，data 可能是 number 或 null，null 转成 undefined
            setApiStartTime(res.data ?? undefined);
          } else {
            // 失败，打印错误信息，状态设为 undefined
            console.error('获取开始时间失败:', res ?? '未知错误');
            setApiStartTime(undefined);
          }
        } catch (error) {
          // 网络或其他异常，打印错误，状态设为 undefined
          console.error('请求开始时间异常:', error);
          setApiStartTime(undefined);
        }
      }
    };
    fetchStartTime();
  }, [selected, appSettingState.info.businessInfo.aid, appSettingState.info.platform]);

  const onVersionChange = (value: undefined | string, label: React.ReactNode[], extra: { title: string }) => {
    setSelected(value);
    // 这里可以根据需要处理 title，比如传递给其他组件
    console.log('Selected title:', extra.title);
  };
  const buildCrashIssueList = () => {
    if (!selected) {
      return <>请选择一个版本！</>;
    }
    // 等待起始时间接口返回
    if (apiStartTime === undefined) {
      return <>正在获取版本起始时间...</>;
    }
    // TODO 时间必须是准确的 就是
    const start_time = startTime(apiStartTime).unix();
    console.log('start_time:', start_time);
    return (
      <StabilityIssueList
        version={selected}
        baseVersionCode={selectedBest ?? '0'}
        start_time={start_time}
        end_time={endTime(start_time).unix()}
        deviceLevel={deviceLevel}
      />
    );
  };
  return (
    <>
      <Row gutter={[16, 16]}>
        <Col span={24}>
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Space size={'middle'}>
              选择版本：
              <OfflineVersionSelector data={versions} onChange={onVersionChange} value={selected!} isSingle={true} />
            </Space>
          </div>
        </Col>
      </Row>
      <Divider />
      {buildCrashIssueList()}
    </>
  );
};
export default Stability;
