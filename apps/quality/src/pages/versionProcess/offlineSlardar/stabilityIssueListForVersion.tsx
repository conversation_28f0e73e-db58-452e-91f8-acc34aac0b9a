import React, { useEffect, useState } from 'react';
import { CrashName } from '@shared/typings/slardar/crash/issueListSearch';
import { useModel } from '@edenx/runtime/model';
import AppSettingModule from '@/model/appSettingModel';
import { Tabs } from 'antd';
import { AndroidCrash } from '@shared/constants/slardar';
import OfflineSlardarCrashListForVersion from '@/component/SlardarCrashList/SlardarCrashListForVersion/offline_index';
import VersionSettingModel from '@/model/versionSettingModel';
import { DeviceLevel } from '@shared/common';
import { GroupName } from '@shared/utils/offlineslardar';

export interface StabilityForVersionProps {
  deviceLevel: DeviceLevel;
  groupName: GroupName | null;
}

const StabilityIssueListForVersion: React.FC<StabilityForVersionProps> = ({ deviceLevel, groupName }) => {
  const [{ info }] = useModel(AppSettingModule);
  const [CrashItem, setCrashItem] = useState<{ label: string; key: string; children: React.ReactNode }[]>([]);
  const [versionSettings] = useModel(VersionSettingModel);
  useEffect(() => {
    const Item = AndroidCrash.map(crashType => ({
      label: CrashName[crashType],
      key: crashType,
      children: (
        <OfflineSlardarCrashListForVersion
          crashType={crashType}
          version={info.name === '剪映' ? versionSettings.info.lvVersion : versionSettings.info.ccVersion}
          deviceLevel={deviceLevel}
        />
      ),
    }));
    setCrashItem(Item);
  }, [info.platform, versionSettings.info]);
  return (
    <Tabs
      onChange={(key: string) => {
        console.log(key);
      }}
      type="card"
      key={versionSettings.info.lvVersion}
      items={CrashItem}
    />
  );
};

export default StabilityIssueListForVersion;
