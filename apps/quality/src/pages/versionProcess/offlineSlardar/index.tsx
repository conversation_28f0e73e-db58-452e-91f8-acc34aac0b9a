import React from 'react';
import Stability from '@/pages/versionProcess/offlineSlardar/stability';
import StabilityIssueListForVersion from '@/pages/versionProcess/offlineSlardar/stabilityIssueListForVersion';
import { useModel } from '@edenx/runtime/model';
import AppSettingModule from '@/model/appSettingModel';
import { BusinessType } from '@pa/shared/dist/src/appSettings/appSettings';
import { PlatformType } from '@pa/shared/dist/src/core';
import { DeviceLevel } from '@shared/common';
import { Tabs } from 'antd';
import { noop } from 'lodash';
import VersionStabilityApprovalButton from '@/pages/versionProcess/graySlardar/versionStabilityApprovalButton';
import { convertAppIdToGroupName, GroupName } from '@shared/utils/offlineslardar';
const OfflineSlardar: React.FC = () => {
  const [{ info }] = useModel(AppSettingModule);
  const groupName: GroupName | null = convertAppIdToGroupName(info.id);
  console.log('Current groupName:', groupName);

  const items = [
    {
      label: '稳定性',
      key: 'Stability',
      children: <Stability deviceLevel={DeviceLevel.ALL} groupName={groupName} />,
    },
    // {
    //   label: '高端机稳定性',
    //   key: 'HighStability',
    //   children: <Stability deviceLevel={DeviceLevel.HIGH} groupName={groupName} />,
    // },
    // {
    //   label: '低端机稳定性',
    //   key: 'LowStability',
    //   children: <Stability deviceLevel={DeviceLevel.LOW} groupName={groupName} />,
    // },
  ];
  // if (info.platform === PlatformType.Android && info.businessID === BusinessType.LV) {
  //   items.push(
  //     {
  //       label: '聚合稳定性',
  //       key: 'Aggregation',
  //       children: <StabilityIssueListForVersion deviceLevel={DeviceLevel.ALL} groupName={groupName} />,
  //     },
  //     {
  //       label: '高端机聚合稳定性',
  //       key: 'HighAggregation',
  //       children: <StabilityIssueListForVersion deviceLevel={DeviceLevel.HIGH} groupName={groupName} />,
  //     },
  //     {
  //       label: '低端机聚合稳定性',
  //       key: 'LowAggregation',
  //       children: <StabilityIssueListForVersion deviceLevel={DeviceLevel.LOW} groupName={groupName} />,
  //     },
  //   );
  // }
  return <Tabs onChange={noop} type="card" items={items} />;
};

export default OfflineSlardar;
