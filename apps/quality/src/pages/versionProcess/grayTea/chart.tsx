import React, { useState } from 'react';
import { AlarmVersion, TeaGranularity, TeaGranularityName } from '@shared/typings/tea/metric';
import { Flex, Select, Tag } from 'antd';
import TeaGrayChart from 'src/component/TeaGrayChart';
import { useModel } from '@edenx/runtime/model';
import AppSettingModule from '@/model/appSettingModel';

const ChartPart: React.FC<{
  v: AlarmVersion[];
  g: TeaGranularity;
}> = ({ v, g }) => (
  <Flex justify={'center'} align={'center'} style={{ width: '100%' }}>
    {v.length === 2 ? <TeaGrayChart versions={[v[0], v[1]]} granularity={g} /> : <>请选择 2 个版本以查看数据！</>}
  </Flex>
);

const GrayTeaChartPage: React.FC<{ versions: AlarmVersion[] }> = ({ versions }) => {
  const [granularity, setGranularity] = useState<TeaGranularity>(TeaGranularity.HOUR_1);
  const [{ info }] = useModel(AppSettingModule);

  return (
    <>
      <Flex
        style={{
          width: '100%',
          height: 50,
          borderRadius: 6,
          border: '1px solid #CCCCCC',
        }}
        justify={'start'}
        gap={'large'}
        align={'center'}
      >
        <Flex
          style={{
            width: '35%',
            marginLeft: '15px',
          }}
          align={'center'}
        >
          <div
            style={{
              whiteSpace: 'nowrap',
              marginRight: 'auto',
            }}
          >
            选择报表时间粒度：
          </div>
          <Select<TeaGranularity>
            defaultValue={TeaGranularity.HOUR_1}
            options={[TeaGranularity.HOUR_1, TeaGranularity.HOUR_24].map(it => ({
              value: it,
              label: <Tag>{TeaGranularityName[it]}</Tag>,
            }))}
            onSelect={setGranularity}
            style={{
              width: '100%',
              flexShrink: 1,
              minInlineSize: 'min-content',
            }}
          />
        </Flex>
      </Flex>
      <hr />
      <ChartPart
        key={`${versions.map(it => it.versionCode).join('_')}_${granularity}_${info.platform}`}
        v={versions}
        g={granularity}
      />
    </>
  );
};

export default GrayTeaChartPage;
