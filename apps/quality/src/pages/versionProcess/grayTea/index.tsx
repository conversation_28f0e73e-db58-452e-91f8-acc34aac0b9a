import React, { useEffect, useState } from 'react';
import { Button, Flex, message, Space, Tabs } from 'antd';
import { compact, groupBy, maxBy, noop, toPairs } from 'lodash';
import GrayTeaTablePage from '@/pages/versionProcess/grayTea/table';
import GrayTeaChartPage from '@/pages/versionProcess/grayTea/chart';
import { AlarmVersion, BusinessName, Platform } from '@shared/typings/tea/metric';
import { getVersions } from '@/utils/version';
import { generate_sorter, save_as_csv, to_platform, to_slardar_platform } from '@shared/utils/tools';
import { useModel } from '@edenx/runtime/model';
import AppSettingModule from '@/model/appSettingModel';
import VersionSelector from '@/component/VersionSelector';
import { buildTreeFromVersionList } from '@/component/VersionSelector/utils';
import { VersionType } from '@shared/utils/version_utils';
import { queryPreviousVersion } from '@api/version';
import { DownloadOutlined } from '@ant-design/icons';
import { getTeaGrayMetricValue, getTeaVersionedMetric } from '@api/tea';
import { buildTeaUrl } from '@shared/utils/tea';
import BitsVersionInfoCard from '@/component/BitsVersionInfoCard';
import dayjs from 'dayjs';

const handle_export = async (v: AlarmVersion[], platform: Platform) => {
  if (v.length <= 0) {
    message.error('请选择版本以导出数据！');
    return;
  }

  const metrics = await getTeaVersionedMetric({
    data: {
      platform,
    },
  });
  const values = await getTeaGrayMetricValue({
    data: {
      versions: v.map(it => it.versionCode),
    },
  });

  const header = `业务线,指标名,TEA 链接,POC,${v.map(it => it.versionCode).join(',')}`;
  const body = metrics.map(
    it =>
      `${BusinessName[it.Business]},${it.DisplayName},${buildTeaUrl(
        it.IsNewTea,
        it.TeaAppId,
        it.TeaId,
        it.TeaIdType,
      )},${it.POC},${v.map(ver => values.find(val => val.metricId === it.Name && val.versionCode === ver.versionCode)?.value ?? '-').join(',')}`,
  );
  save_as_csv(`${header}\n${body.join('\n')}`, `data_export_${v.map(it => it.versionCode).join('_')}_${platform}.csv`);
};

const GrayTea: React.FC = () => {
  // 版本列表
  const [versions, setVersions] = useState<AlarmVersion[]>([]);
  const [selected, setSelected] = useState<AlarmVersion[]>([]);

  const [exportLoading, setExportLoading] = useState(false);

  const [{ info }] = useModel(AppSettingModule);
  useEffect(() => {
    getVersions(info.businessInfo.aid, to_slardar_platform(info.platform)).then(ver => {
      setVersions(
        ver
          .concat(
            toPairs(groupBy(ver, it => it.version)).map(
              ([k, v]): AlarmVersion => ({
                version: k,
                versionCode: k,
                versionType: VersionType.MAJOR_VERSION,
                timestamp: Math.min(...v.map(it => it.timestamp)),
              }),
            ),
          )
          .sort(generate_sorter('#~version', '~versionCode')),
      );
      const current_ver = maxBy(ver, v => v.versionCode);
      if (current_ver) {
        queryPreviousVersion({
          data: {
            aid: info.businessInfo.aid,
            version: current_ver.version,
            versionCode: current_ver.versionCode,
          },
        }).then(res => {
          setSelected(compact([current_ver, ver.find(it => it.versionCode === res?.version_code)]));
        });
      } else {
        setSelected([]);
      }
    });
  }, [info.platform]);

  const start_time_calc = (t: number) => dayjs.unix(t).startOf('h').add(1, 'h').unix();
  const end_time_calc = (t: number) => 3 * 86400 + start_time_calc(t);

  return (
    <Space direction={'vertical'} style={{ width: '100%' }} size={'large'}>
      <Flex
        style={{
          width: '100%',
          height: 50,
          borderRadius: 6,
          border: '1px solid #CCCCCC',
        }}
        justify={'start'}
        gap={'large'}
        align={'center'}
      >
        <Flex
          style={{
            width: '35%',
            minWidth: '455px',
            marginLeft: '15px',
          }}
          align={'center'}
        >
          <div
            style={{
              whiteSpace: 'nowrap',
              marginRight: 'auto',
            }}
          >
            选择报表版本：
          </div>
          <VersionSelector
            data={buildTreeFromVersionList(versions)}
            onChange={v => setSelected(compact([v].flat().map(it => versions.find(r => r.versionCode === it))))}
            value={selected.map(it => it.versionCode)}
            style={{
              width: '100%',
              flexShrink: 1,
              minInlineSize: 'min-content',
            }}
          />
        </Flex>
        <Button
          type={'primary'}
          icon={<DownloadOutlined />}
          loading={exportLoading}
          onClick={async () => {
            setExportLoading(true);
            await handle_export(selected, to_platform(info.platform));
            setExportLoading(false);
          }}
        >
          导出数据
        </Button>
      </Flex>
      <div style={{ padding: '0 15px 0 15px' }}>
        <BitsVersionInfoCard
          start_time={start_time_calc}
          end_time={end_time_calc}
          version_code={selected.at(0)?.versionCode}
        />
      </div>
      <Tabs
        onChange={noop}
        items={[
          {
            label: '折线图',
            key: 'chart',
            children: <GrayTeaChartPage versions={selected} />,
          },
          {
            label: '表格',
            key: 'table',
            children: <GrayTeaTablePage versions={selected} />,
          },
        ]}
        type="card"
      />
    </Space>
  );
};

export default GrayTea;
