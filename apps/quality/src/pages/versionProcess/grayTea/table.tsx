import React from 'react';
import { AlarmVersion } from '@shared/typings/tea/metric';
import { Flex } from 'antd';
import TeaGrayTable from 'src/component/TeaGrayTable';
import { generate_sorter } from '@shared/utils/tools';

const GrayTeaTablePage: React.FC<{ versions: AlarmVersion[] }> = ({ versions }) => {
  const render_table = (v: AlarmVersion[]) => {
    if (v.length < 1) {
      return (
        <Flex justify={'center'} style={{ width: '100%' }}>
          <>请选择版本以查看数据！</>
        </Flex>
      );
    }
    return (
      <Flex justify={'center'} align={'center'} style={{ width: '100%' }}>
        <TeaGrayTable versions={v.sort(generate_sorter('~version', '~versionCode')).map(it => it.versionCode)} />
      </Flex>
    );
  };

  return <>{render_table(versions)}</>;
};

export default GrayTeaTablePage;
