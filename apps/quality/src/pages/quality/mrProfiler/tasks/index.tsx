/* eslint-disable max-lines-per-function */
import { useEffect, useState } from 'react';
import { Avatar, Button, List, Select, SideSheet, Spin, Table, TabPane, Tabs, Typography } from '@douyinfe/semi-ui';
import { MRProfilerTaskTable } from 'api/model/MrProfilerModel';
import {
  getMrProfilerTasks,
  getMrProfilerTasksWithType,
  getVersionList,
  retryMRProfilerBytestTasks,
  updateMRProfilerTaskConfirmStatus,
} from '@api/mrProfiler';
import { IconMore } from '@douyinfe/semi-icons';
import { ColumnProps } from '@douyinfe/semi-ui/lib/es/table';
import ButtonGroup from 'antd/lib/button/button-group';
import { AvatarColor } from '@douyinfe/semi-ui/lib/es/avatar';
import { QualityToolsButton, ToolsName, CreatMeegoButton } from '@shared/utils/QualityTools';

const MRProfilerTasks: React.FC = () => {
  const [searchLoading, setSearchLoading] = useState(false);
  const [taskData, setTaskData] = useState<MRProfilerTaskTable[]>([]);
  const [sideSheetVisable, setSideSheetVisable] = useState(false);
  const [selectTask, setSelectTask] = useState<MRProfilerTaskTable>();
  const [targetName, setTargetName] = useState<string>('VideoFusion');
  const [tableLoading, setTableLoading] = useState(false);
  const [version, setVersion] = useState<string>('');
  const [versionList, setVersionList] = useState<string[]>([]);
  const [spinLoading, setSpinLoading] = useState(false);
  const [mrProfilerType, setMrProfilerType] = useState<string>('mr');
  const taskSheetDetials = [];
  const deviceTypes = ['高端机', '中端机', '低端机'];
  const { Text } = Typography;
  const perfStatusStringMap = {
    undefined: {
      text: '未知',
      color: 'blank',
    },
    0: {
      text: '未知',
      color: 'blank',
    },
    1: {
      text: '负向',
      color: 'red',
    },
    2: {
      text: '正向',
      color: 'green',
    },
  };
  const taskStatusStringMap = {
    undefined: {
      text: '未知',
      color: 'blank',
    },
    0: {
      text: '创建',
      color: 'blank',
    },
    1: {
      text: '打包中',
      color: 'green',
    },
    2: {
      text: '打包失败',
      color: 'red',
    },
    3: {
      text: 'bytest任务执行中',
      color: 'green',
    },
    4: {
      text: 'bytest任务执行失败',
      color: 'red',
    },
    5: {
      text: '解析中',
      color: 'green',
    },
    6: {
      text: '解析失败',
      color: 'red',
    },
    7: {
      text: '生成报告中',
      color: 'green',
    },
    8: {
      text: '生报告失败',
      color: 'red',
    },
    9: {
      text: '完成',
      color: 'green',
    },
    10: {
      text: '超时',
      color: 'red',
    },
  };

  useEffect(() => {
    setSpinLoading(true);
    const fetchVersionList = async () => {
      const versionListTemp = await getVersionList();
      setVersionList(versionListTemp);
    };
    fetchVersionList();
    setSpinLoading(false);
  }, []);

  async function searchTasks() {
    setSearchLoading(true);
    console.log(`searchTasks: ${targetName}, ${version}`);
    if (mrProfilerType === 'mr') {
      const result = await getMrProfilerTasks({ data: { target_name: targetName, version } });
      if (result !== null) {
        setTaskData(result);
      }
    } else if (mrProfilerType === 'version') {
      const result = await getMrProfilerTasksWithType({ data: { target_name: targetName, version, type: 1 } });
      if (result !== null) {
        setTaskData(result);
      }
    }
    setSearchLoading(false);
  }

  async function selectRow(selectedRow: MRProfilerTaskTable) {
    console.log(`mr_profiler task: ${JSON.stringify(selectedRow)}`);
    setSideSheetVisable(true);
    setSelectTask(selectedRow);
  }

  async function changeVersion(value: string) {
    setVersion(value);
  }

  async function changeTarget(value: any) {
    setTargetName(value);
  }

  async function changeConfirm(selectedRow: MRProfilerTaskTable, value: any) {
    setTableLoading(true);
    console.log(`mr_profiler update id: ${selectedRow._id}, confirm status: ${value}`);
    await updateMRProfilerTaskConfirmStatus({ data: { id: selectedRow._id, confirm_status: value } });
    selectedRow.confirm_status = value;
    setTableLoading(false);
  }

  async function retryTask(device_type: string) {
    if (!selectTask) {
      return;
    }
    console.log(`retry: id: ${selectTask._id}, device type: ${device_type}`);
    await retryMRProfilerBytestTasks({ data: { id: selectTask?._id, device_type } });
  }

  if (selectTask?.analyse_results !== undefined) {
    for (const deviceType of deviceTypes) {
      const bytestJobId = selectTask?.analyse_results[deviceType].bytest_job_id;
      let color: AvatarColor = deviceType === '高端机' ? 'red' : 'green';
      if (deviceType === '中端机') {
        color = 'blue';
      }
      taskSheetDetials.push({
        color,
        device_type: deviceType,
        status: selectTask?.analyse_results[deviceType].summary.status,
        desc: selectTask?.analyse_results[deviceType].summary.desc,
        report_url: `https://bytrace.bytedance.net/comparison/idea-mr?tab=Report&type=idea_mr&url=${selectTask?.analyse_results[deviceType].report_url}`,
        bytest_url: `https://bits.bytedance.net/quality/projects/1499910658/reports/${bytestJobId}/overview?serviceId=2`,
      });
    }
  }

  const columns: ColumnProps<MRProfilerTaskTable>[] = [
    {
      title: 'mr标题',
      dataIndex: 'mr_title',
      render: (text: string, record: MRProfilerTaskTable) => (
        <Text
          link={{
            href: `https://bits.bytedance.net/devops/1486844930/code/detail/${record.mr_id}`,
            target: '_blank',
          }}
        >
          {record.mr_title}
        </Text>
      ),
    },
    {
      title: 'mr作者',
      dataIndex: 'mr_author',
    },
    {
      title: '场景',
      dataIndex: 'scene',
    },
    {
      title: '任务状态',
      dataIndex: 'status',
      filters: [
        {
          text: '创建',
          value: 0,
        },
        {
          text: '打包中',
          value: 1,
        },
        {
          text: '打包失败',
          value: 2,
        },
        {
          text: 'bytest任务执行中',
          value: 3,
        },
        {
          text: 'bytest任务执行失败',
          value: 4,
        },
        {
          text: '解析中',
          value: 5,
        },
        {
          text: '解析失败',
          value: 6,
        },
        {
          text: '生成报告中',
          value: 7,
        },
        {
          text: '生报告失败',
          value: 8,
        },
        {
          text: '完成',
          value: 9,
        },
        {
          text: '超时',
          value: 10,
        },
      ],
      onFilter: (value, record) => {
        if (!record) {
          return false;
        }
        return record.status === value;
      },
      render: (text: string, record: MRProfilerTaskTable) => (
        <Text strong style={{ color: taskStatusStringMap[record.status].color }}>
          {taskStatusStringMap[record.status].text}
        </Text>
      ),
    },
    {
      title: '性能状态',
      dataIndex: 'perf_status',
      filters: [
        {
          text: '未知',
          value: 0,
        },
        {
          text: '负向',
          value: 1,
        },
        {
          text: '正向',
          value: 2,
        },
      ],
      onFilter: (value, record) => {
        if (!record) {
          return false;
        }
        return record.perf_status === value;
      },
      render: (text: string, record: MRProfilerTaskTable) => (
        <Text strong style={{ color: perfStatusStringMap[record.perf_status].color }}>
          {perfStatusStringMap[record.perf_status].text}
        </Text>
      ),
    },
    {
      title: '确认状态',
      dataIndex: 'confirm_status',
      filters: [
        {
          text: '未确定',
          value: 0,
        },
        {
          text: '有效',
          value: 1,
        },
        {
          text: '无效',
          value: 2,
        },
      ],
      onFilter: (value, record) => {
        if (!record) {
          return false;
        }
        return record.confirm_status === value;
      },
      render: (text: string, record: MRProfilerTaskTable) => (
        <Select style={{ width: 100 }} value={record.confirm_status} onChange={value => changeConfirm(record, value)}>
          <Select.Option value={0}>未确认</Select.Option>
          <Select.Option value={1}>有效</Select.Option>
          <Select.Option value={2}>无效</Select.Option>
        </Select>
      ),
    },
    {
      title: '详情',
      dataIndex: 'detail',
      fixed: 'right',
      width: 100,
      render: (text: string, record: MRProfilerTaskTable) => <IconMore onClick={() => selectRow(record)} />,
    },
  ];

  return (
    <Spin spinning={spinLoading}>
      <Select style={{ width: 100 }} value={targetName} onChange={value => changeTarget(value)}>
        <Select.Option value="VideoFusion">剪映</Select.Option>
        <Select.Option value="CapCut">CapCut</Select.Option>
      </Select>
      <Select
        style={{ width: 200, marginTop: 12, marginBottom: 12, marginRight: 12, marginLeft: 12 }}
        placeholder="版本"
        value={version}
        onChange={(value: any) => changeVersion(value)}
      >
        {versionList.map(versionT => (
          <Select.Option key={versionT} value={versionT}>
            {versionT}
          </Select.Option>
        ))}
      </Select>
      <Select
        style={{ width: 200, marginRight: 12, marginLeft: 12 }}
        value={mrProfilerType}
        onChange={(value: any) => setMrProfilerType(value)}
      >
        <Select.Option value="mr">MR捕获</Select.Option>
        <Select.Option value="version">版本定时</Select.Option>
      </Select>
      <QualityToolsButton
        loading={searchLoading}
        toolName={ToolsName.MRProfilerTask}
        title={'查询'}
        onClick={() => searchTasks()}
      />
      <br />
      <Table
        rowKey="_id"
        loading={tableLoading}
        columns={columns}
        dataSource={taskData}
        pagination={false}
        bordered={true}
      />
      <SideSheet size="medium" visible={sideSheetVisable} onCancel={() => setSideSheetVisable(false)}>
        <Tabs type="line">
          <TabPane tab="执行详情" itemKey="1">
            <List
              dataSource={taskSheetDetials}
              renderItem={item => (
                <List.Item
                  header={<Avatar color={item.color}>{item.device_type}</Avatar>}
                  main={
                    <div>
                      <Text
                        link={{
                          href: item.report_url,
                          target: '_blank',
                        }}
                      >
                        <Text strong>{item.status}</Text>
                        <br />
                        {item.desc}
                      </Text>
                    </div>
                  }
                  extra={
                    <ButtonGroup>
                      <Text
                        link={{
                          href: item.bytest_url,
                          target: '_blank',
                        }}
                      >
                        Bytest任务链接
                      </Text>
                      <br />
                      <br />
                      <Button theme="solid" type="warning" onClick={() => retryTask(item.device_type)}>
                        重试
                      </Button>
                    </ButtonGroup>
                  }
                />
              )}
            />
          </TabPane>
          <TabPane tab="编包信息" itemKey="2">
            <Text
              link={{
                href: `https://bits.bytedance.net/space/legacy/build/logs?jobId=${selectTask?.base_bits_job_id}`,
                target: '_blank',
              }}
            >
              基准包链接
            </Text>
            <br />
            <br />
            <Text
              link={{
                href: `https://bits.bytedance.net/space/legacy/build/logs?jobId=${selectTask?.test_bits_job_id}`,
                target: '_blank',
              }}
            >
              被测包链接
            </Text>
          </TabPane>
        </Tabs>
      </SideSheet>
    </Spin>
  );
};

export default MRProfilerTasks;
