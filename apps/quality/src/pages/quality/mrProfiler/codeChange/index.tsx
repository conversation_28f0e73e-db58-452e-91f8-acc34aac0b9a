/* eslint-disable max-lines-per-function */
import { useEffect, useState } from 'react';
import {
  Button,
  Col,
  Input,
  Row,
  Select,
  SideSheet,
  Spin,
  Table,
  TabPane,
  Tabs,
  Tag,
  TextArea,
  Typography,
} from '@douyinfe/semi-ui';
import { getCodeChange, getVersionList, MRCodeChangeDisplayInfo } from '@api/mrProfiler';
import { ColumnProps } from '@douyinfe/semi-ui/lib/es/table';
import { MRCodeChangeCodeSimpleInfo } from 'api/model/MRCodeChangeModel';
import { IconClear, IconExternalOpen, IconTickCircle, IconUser } from '@douyinfe/semi-icons';
import { TagColor } from '@douyinfe/semi-ui/lib/es/tag';
import { useSearchParams } from '@edenx/runtime/router';
import { MRAttr } from './issueMRAtt';
import { QualityToolsButton, ToolsName } from '@shared/utils/QualityTools';

const { Text } = Typography;

export enum MRProfilerSearchParams {
  appid = 'appid',
  version = 'version',
  filePath = 'filePath',
  methodName = 'methodName',
  affectScene = 'affectScene',
  autoSearch = 'autoSearch',
}

const MRProfilerCodeChange: React.FC = () => {
  const [tabActiveKey, setTabActiveKey] = useState('common');
  const [searchParams, setSearchParams] = useSearchParams();
  const [searchLoading, setSearchLoading] = useState(false);
  const [codeChangeData, setCodeChangeData] = useState<MRCodeChangeDisplayInfo[]>([]);
  const [selectCodeChange, setSelectCodeChange] = useState<MRCodeChangeDisplayInfo>();
  const [targetName, setTargetName] = useState<string>('VideoFusion');
  const [filePath, setFilePath] = useState<string>('');
  const [methodName, setMethodName] = useState<string>('');
  const [affectScene, setAffectScene] = useState<string>('');
  const [tableLoading, setTableLoading] = useState(false);
  const [version, setVersion] = useState<string>('');
  const [versionList, setVersionList] = useState<string[]>([]);
  const [spinLoading, setSpinLoading] = useState(false);
  const [sideSheetVisable, setSideSheetVisable] = useState(false);

  const thresholdVersion = '16.4.0';
  function compareVersions(v1: string, v2: string): number {
    const parts1 = v1.split('.').map(Number);
    const parts2 = v2.split('.').map(Number);
    const len = Math.max(parts1.length, parts2.length);

    for (let i = 0; i < len; i++) {
      const n1 = parts1[i] || 0;
      const n2 = parts2[i] || 0;
      if (n1 > n2) {
        return 1;
      }
      if (n1 < n2) {
        return -1;
      }
    }
    return 0;
  }

  function transformVersion(inputVersion: string): string {
    let finalVersion = inputVersion;
    if (targetName === 'CapCut') {
      if (compareVersions(inputVersion, thresholdVersion) <= 0) {
        return finalVersion;
      }
      const versionParts = inputVersion.split('.');
      if (versionParts.length > 0) {
        const majorVersion = parseInt(versionParts[0], 10);
        if (!isNaN(majorVersion)) {
          versionParts[0] = (majorVersion - 2).toString();
          finalVersion = versionParts.join('.');
        } else {
          console.warn(`Invalid major version format: ${versionParts[0]}`);
          // 根据需要处理无效版本号的情况，例如不修改或报错
        }
      } else {
        console.warn(`Invalid version format: ${inputVersion}`);
        // 根据需要处理无效版本号的情况
      }
    }
    return finalVersion;
  }

  async function searchCodeChange() {
    setSearchLoading(true);
    setTableLoading(true);

    const finalVersion = transformVersion(version);
    console.log(
      `searchCodeChange: ${targetName}, ${finalVersion}, ${filePath.split('\n')}, ${methodName.split('\n')}, ${affectScene}`,
    );
    const result = await getCodeChange({
      data: {
        target_name: targetName,
        version: finalVersion,
        file_paths: filePath.split('\n').filter(line => line.trim() !== ''),
        method_names: methodName.split('\n').filter(line => line.trim() !== ''),
        affect_scene: affectScene,
      },
    });
    if (result !== null) {
      setCodeChangeData(result);
    }
    setSearchLoading(false);
    setTableLoading(false);
  }

  useEffect(() => {
    setSpinLoading(true);
    const fetchVersionList = async () => {
      const versionListTemp = await getVersionList();
      setVersionList(versionListTemp);
    };
    fetchVersionList();
    // 如果带参数传入，且参数中包含issue_url，则使能issue url 的tab
    const inputIssueUrl = searchParams.get('issue_url');
    if (inputIssueUrl && inputIssueUrl !== null) {
      setTabActiveKey('issue');
    }
    setSpinLoading(false);

    const paramAppid = searchParams.get(MRProfilerSearchParams.appid);
    if (paramAppid) {
      if (paramAppid === '177501') {
        setTargetName('VideoFusion');
      } else if (paramAppid === '300601') {
        setTargetName('CapCut');
      }
    }

    const paramVersion = searchParams.get(MRProfilerSearchParams.version);
    if (paramVersion) {
      setVersion(paramVersion);
    }
    const paramFilePath = searchParams.get(MRProfilerSearchParams.filePath);
    if (paramFilePath) {
      const filePaths = paramFilePath.split(',');
      setFilePath(filePaths.join('\n'));
    }

    const paramMethodName = searchParams.get(MRProfilerSearchParams.methodName);
    if (paramMethodName) {
      const methodNames = paramMethodName.split(',');
      setMethodName(methodNames.join('\n'));
    }

    const paramAffectScene = searchParams.get(MRProfilerSearchParams.affectScene);
    if (paramAffectScene) {
      setAffectScene(paramAffectScene);
    }

    const autoSearch = searchParams.get(MRProfilerSearchParams.autoSearch);
    if (autoSearch === 'true') {
      searchCodeChange()
        .then(r => {})
        .catch(err => {
          console.log('autoSearch error', err);
        });
    }
  }, [searchParams]);

  async function changeVersion(value: string) {
    setVersion(value);
  }

  async function changeTarget(value: any) {
    setTargetName(value);
  }

  async function selectRow(selectedRow: MRCodeChangeDisplayInfo) {
    setSideSheetVisable(true);
    setSelectCodeChange(selectedRow);
  }

  const columns: ColumnProps<MRCodeChangeDisplayInfo>[] = [
    {
      title: 'mr标题',
      dataIndex: 'mr_title',
      render: (text: string, record: MRCodeChangeDisplayInfo) => (
        <div>
          <Text
            icon={<IconExternalOpen />}
            link={{
              href: `https://bits.bytedance.net/devops/1486844930/code/detail/${record.mr_id}`,
              target: '_blank',
            }}
          >
            {record.mr_title}
          </Text>
          <br />
          <Text icon={<IconUser />}>{record.mr_author}</Text>
          <br />
          <Button theme="borderless" type="warning" onClick={() => selectRow(record)}>
            变更信息
          </Button>
        </div>
      ),
    },
    {
      title: '改动点',
      dataIndex: 'change_points',
      render: (changePoints: MRCodeChangeCodeSimpleInfo[]) => {
        const result = [];
        for (const changePoint of changePoints) {
          result.push(
            <p style={{ width: 500, color: 'green' }}>
              <strong>{changePoint.path}</strong>
            </p>,
          );
          result.push(
            <p style={{ width: 500, color: 'black' }}>
              <strong>{changePoint.method_name}</strong>
            </p>,
          );
          result.push(<br />);
        }
        return result;
      },
    },
    {
      title: '是否合入',
      dataIndex: 'is_merged',
      filters: [
        {
          text: '未合入',
          value: false,
        },
        {
          text: '已合入',
          value: true,
        },
      ],
      onFilter: (value, record) => {
        if (!record) {
          return false;
        }
        return record.is_merged === value;
      },
      render: text => {
        const isMerged = text === true;
        const tagConfig = {
          true: { color: 'green' as TagColor, prefixIcon: <IconTickCircle />, text: '已合入' },
          false: { color: 'pink' as TagColor, prefixIcon: <IconClear />, text: '未合入' },
        };
        const tagProps = isMerged ? tagConfig.true : tagConfig.false;
        return (
          <Tag shape="circle" prefixIcon={tagProps.prefixIcon} color={tagProps.color} style={{ userSelect: 'text' }}>
            {tagProps.text}
          </Tag>
        );
      },
    },
  ];

  return (
    <Spin spinning={spinLoading}>
      <div>
        <Tabs type="line" activeKey={tabActiveKey} onChange={value => setTabActiveKey(value)}>
          <TabPane tab="单点信息搜索" itemKey="common">
            <Row type="flex" align="top">
              <Col span={2}>
                <Select style={{ width: 100 }} value={targetName} onChange={value => changeTarget(value)}>
                  <Select.Option value="VideoFusion">剪映</Select.Option>
                  <Select.Option value="CapCut">CapCut</Select.Option>
                </Select>
              </Col>
              <Col span={4}>
                <Select
                  // multiple
                  style={{ width: 200, marginLeft: 12 }}
                  placeholder="版本"
                  value={version}
                  onChange={(value: any) => changeVersion(value)}
                >
                  {versionList.map(versionT => (
                    <Select.Option key={versionT} value={versionT}>
                      {versionT}
                    </Select.Option>
                  ))}
                </Select>
              </Col>
              <Col span={6}>
                <TextArea
                  autosize
                  rows={1}
                  value={filePath}
                  onChange={(value: string) => setFilePath(value)}
                  placeholder="文件名"
                  style={{ width: 300, marginLeft: 12 }}
                />
              </Col>
              <Col span={6}>
                <TextArea
                  autosize
                  rows={1}
                  value={methodName}
                  onChange={(value: string) => setMethodName(value)}
                  placeholder="类名/方法名/变量名"
                  style={{
                    width: 300,
                    marginRight: 12,
                    marginLeft: 12,
                  }}
                  // onEnterPress={() => searchCodeChange()}
                />
              </Col>
              <Col span={4}>
                <Input
                  value={affectScene}
                  onChange={(value: string) => setAffectScene(value)}
                  placeholder="影响场景"
                  style={{
                    width: 100,
                    marginRight: 12,
                    marginLeft: 12,
                  }}
                  // onEnterPress={() => searchCodeChange()}
                />
                <QualityToolsButton
                  loading={searchLoading}
                  toolName={ToolsName.MRProfilerCodeChangeSearch}
                  title={'查询'}
                  onClick={() => searchCodeChange()}
                />
              </Col>
            </Row>
            <br />
            <Table
              rowKey="mr_id"
              loading={tableLoading}
              columns={columns}
              dataSource={codeChangeData}
              pagination={false}
              bordered={true}
            />
          </TabPane>
          <TabPane tab="issue链接搜索" itemKey="issue">
            <MRAttr versionList={versionList} />
          </TabPane>
        </Tabs>
        <SideSheet
          size="medium"
          title={selectCodeChange?.mr_title}
          visible={sideSheetVisable}
          onCancel={() => setSideSheetVisable(false)}
        >
          <Row justify="end">
            <Col span={4}>base cid:</Col>
            <Col span={20}>{selectCodeChange?.base_cid}</Col>
          </Row>
          <br />
          <Row justify="end">
            <Col span={4}>test cid:</Col>
            <Col span={20}>{selectCodeChange?.test_cid}</Col>
          </Row>
          <br />
          <Row justify="end">
            <Col span={4}>pod 变更:</Col>
            <Col span={20}>{selectCodeChange?.change_pods.map(pod => <p>{pod}</p>)}</Col>
          </Row>
          <br />
          <Row justify="end">
            <Text
              link={{
                href: `${selectCodeChange?.git_diff_infos.git_diff_txt_url.replace('json', 'txt')}`,
                target: '_blank',
              }}
            >
              git diff file
            </Text>
          </Row>
          <br />
          <Row justify="end">
            <Text
              link={{
                href: `${selectCodeChange?.git_diff_infos.base_staking_file_url}`,
                target: '_blank',
              }}
            >
              base 插桩文件
            </Text>
          </Row>
          <br />
          <Row justify="end">
            <Text
              link={{
                href: `${selectCodeChange?.git_diff_infos.test_staking_file_url}`,
                target: '_blank',
              }}
            >
              test 插桩文件
            </Text>
          </Row>
          <br />
          <Row justify="end">
            <Text
              link={{
                href: `https://bits.bytedance.net/space/legacy/build/logs?jobId=${selectCodeChange?.analyse_bits_job_id}`,
                target: '_blank',
              }}
            >
              分析job
            </Text>
          </Row>
        </SideSheet>
      </div>
    </Spin>
  );
};

export default MRProfilerCodeChange;
