/* eslint-disable max-lines-per-function */
import { useEffect, useState } from 'react';
import { Input, Select, Table, Toast, Typography } from '@douyinfe/semi-ui';
import { getMRAttrInfos } from '@api/mrProfiler';
import { ColumnProps } from '@douyinfe/semi-ui/lib/es/table';
import { DisplayMRAttrInfo, MRAttrChangePoint, MRAttrInputSource } from '@shared/mrProfiler/mrProfiler';
import { useSearchParams } from '@edenx/runtime/router';
import slardarApiUserReqMgr from '@/utils/slardarApiUserReqMgr';
import { getUserActionClassFromAlog } from '@shared/utils/slardar';
import Tag, { TagColor } from '@douyinfe/semi-ui/lib/es/tag';
import { IconClear, IconExternalOpen, IconGit, IconPulse, IconTickCircle, IconUser } from '@douyinfe/semi-icons';
import { meegoCreateSlardarBug } from '@api/meego';
import { DiscoverStag2Option } from '@shared/walle/consumeResult';
import { NetworkCode } from '@pa/shared/dist/src/core';
import { message } from 'antd';
import { useModel } from '@edenx/runtime/model';
import UserSettingModule from '@/model/userSettingModel';
import { QualityToolsButton, ToolsName } from '@shared/utils/QualityTools';

export interface MRAttrProps {
  versionList: string[];
}

export const MRAttr: React.FC<MRAttrProps> = (props: MRAttrProps) => {
  const [userSettings] = useModel(UserSettingModule);
  const { versionList } = props;
  const [searchParams, setSearchParams] = useSearchParams();
  const [searchLoading, setSearchLoading] = useState(false);
  const [attrInfo, setAttrInfo] = useState<DisplayMRAttrInfo[]>([]);
  const [tableLoading, setTableLoading] = useState(false);
  const [selectVersionList, setSelectVersionList] = useState<string[]>([]);
  const [issueUrl, setIssueUrl] = useState<string>('');
  const { Text } = Typography;

  const onSelectVersionListChange = (value: any) => {
    setSelectVersionList(value);
  };

  const thresholdVersion = '16.4.0';
  function compareVersions(v1: string, v2: string): number {
    const parts1 = v1.split('.').map(Number);
    const parts2 = v2.split('.').map(Number);
    const len = Math.max(parts1.length, parts2.length);

    for (let i = 0; i < len; i++) {
      const n1 = parts1[i] || 0;
      const n2 = parts2[i] || 0;
      if (n1 > n2) {
        return 1;
      }
      if (n1 < n2) {
        return -1;
      }
    }
    return 0;
  }

  function transformVersion(inputVersion: string): string {
    let finalVersion = inputVersion;
    if (compareVersions(inputVersion, thresholdVersion) <= 0) {
      return finalVersion;
    }
    const versionParts = inputVersion.split('.');
    if (versionParts.length > 0) {
      const majorVersion = parseInt(versionParts[0], 10);
      if (!isNaN(majorVersion)) {
        versionParts[0] = (majorVersion - 2).toString();
        finalVersion = versionParts.join('.');
      } else {
        console.warn(`Invalid major version format: ${versionParts[0]}`);
        // 根据需要处理无效版本号的情况，例如不修改或报错
      }
    } else {
      console.warn(`Invalid version format: ${inputVersion}`);
      // 根据需要处理无效版本号的情况
    }
    return finalVersion;
  }

  async function searchCodeChangeWithIssueUrl(version_list: string[], issue_url: string) {
    setSearchLoading(true);
    setTableLoading(true);
    let new_version_list: string[] = version_list;
    if (issue_url.includes('slardar-us')) {
      new_version_list = [];
      for (const version of version_list) {
        const new_version = transformVersion(version);
        new_version_list.push(new_version);
      }
    }
    console.log(`new_version_list: ${new_version_list}`);
    const mgr = new slardarApiUserReqMgr();
    const { success, error, value } = await mgr.getBackTraceFromIssueUrl(issue_url);
    if (!success) {
      console.log(error);
      Toast.info(error?.message ? error?.message : 'failed');
    } else {
      console.log(`searchCodeChangeWithIssueUrl: ${JSON.stringify(value, null, 2)}`);
      let filterClasses: string[] = [];
      if (value) {
        const alogInfo = await mgr.getAlogInfo(issue_url, value?.device_id, value?.crash_time);
        if (alogInfo.value) {
          filterClasses = getUserActionClassFromAlog(alogInfo.value);
          console.log(`filterClasses: ${filterClasses}`);
        }
      }
      const result = await getMRAttrInfos({
        data: {
          version_list: new_version_list,
          backtraces: value ? value.back_traces : [],
          classes: filterClasses,
        },
      });
      setAttrInfo(result);
    }
    setSearchLoading(false);
    setTableLoading(false);
  }

  async function meego(selectedRow: DisplayMRAttrInfo) {
    setTableLoading(true);
    console.log(JSON.stringify(selectedRow));
    try {
      const result = await meegoCreateSlardarBug({
        data: {
          link: issueUrl,
          discoverVersion: selectedRow.version,
          priority: '2',
          assignee: `<EMAIL>`,
          discoverStage: DiscoverStag2Option.test,
          creator: userSettings.info.email,
          isConsume: false,
          isAutoCreated: false,
        },
      });
      if (result?.ret === NetworkCode.Success && result.data) {
        console.log(result.data);
        window.open(result.data, '_blank');
        if (!result.IsConsumed) {
          message.error(`walle分配失败，使用兜底分配人`, 15);
        }
      } else {
        message.error(`建单失败,code=${result?.ret},msg=${result?.error_msg}`, 15);
      }
    } catch (e) {
      message.error(`建单失败,err=${e}`, 15);
    }
    setTableLoading(false);
  }

  useEffect(() => {
    const inputVersionList = searchParams.get('version_list');
    if (inputVersionList && inputVersionList !== null) {
      setSelectVersionList(inputVersionList.split(','));
    }
    const inputIssueUrl = searchParams.get('issue_url');
    if (inputIssueUrl && inputIssueUrl !== null) {
      setIssueUrl(decodeURIComponent(inputIssueUrl));
    }
    if (inputIssueUrl && inputVersionList && inputIssueUrl.length > 0 && inputVersionList.length > 0) {
      searchCodeChangeWithIssueUrl(inputVersionList.split(','), decodeURIComponent(inputIssueUrl));
    }
  }, []);

  const columns: ColumnProps<DisplayMRAttrInfo>[] = [
    {
      title: '版本',
      dataIndex: 'version',
    },
    {
      title: 'mr标题',
      dataIndex: 'mr_title',
      render: (text: string, record: DisplayMRAttrInfo) => (
        <div>
          <Text
            icon={<IconExternalOpen />}
            link={{
              href: `https://bits.bytedance.net/devops/1486844930/code/detail/${record.mr_id}`,
              target: '_blank',
            }}
          >
            {record.mr_title}
          </Text>
          <br />
          <Text icon={<IconUser />}>{record.mr_author}</Text>
          <br />
          <Text icon={<IconPulse />}>{record.source}</Text>
          <br />
          <Text
            icon={<IconGit />}
            link={{
              href: `${record?.git_diff_infos.git_diff_txt_url.replace('json', 'txt')}`,
              target: '_blank',
            }}
          >
            git diff文件
          </Text>
          {/* <br />*/}
          {/* <Button theme="borderless" type="warning" onClick={() => meego(record)}>*/}
          {/*  meego提单*/}
          {/* </Button>*/}
        </div>
      ),
    },
    {
      title: '改动点',
      dataIndex: 'change_points',
      defaultSortOrder: 'descend',
      sorter: (a, b) => {
        const aSource = a?.source ?? 0;
        const bSource = b?.source ?? 0;
        if (aSource !== bSource) {
          return aSource === MRAttrInputSource.BACKTRACE ? 1 : -1;
        }
        const bIndex = b?.index ?? 0;
        const aIndex = a?.index ?? 0;
        return bIndex - aIndex;
      },
      render: (changePoints: MRAttrChangePoint[]) => {
        const result = [];
        for (const changePoint of changePoints) {
          result.push(
            <p style={{ width: 500, color: 'green' }}>
              <strong>{changePoint.path}</strong>
            </p>,
          );
          if (changePoint.method.length <= 0) {
            result.push(<Text strong>文件存在改动，但未能匹配到方法</Text>);
          }
          for (const methodName of changePoint.method) {
            result.push(
              <span style={{ width: 500, color: 'black' }}>
                <strong>{methodName}</strong>
              </span>,
            );
            result.push(<br />);
          }
          result.push(<br />);
        }
        return result;
      },
    },
    {
      title: '是否合入',
      dataIndex: 'is_merged',
      render: text => {
        const isMerged = text === true;
        const tagConfig = {
          true: { color: 'green' as TagColor, prefixIcon: <IconTickCircle />, text: '已合入' },
          false: { color: 'pink' as TagColor, prefixIcon: <IconClear />, text: '未合入' },
        };
        const tagProps = isMerged ? tagConfig.true : tagConfig.false;
        return (
          <Tag shape="circle" prefixIcon={tagProps.prefixIcon} color={tagProps.color} style={{ userSelect: 'text' }}>
            {tagProps.text}
          </Tag>
        );
      },
    },
  ];

  return (
    <div>
      <Select
        multiple
        style={{ width: 320 }}
        placeholder="版本"
        value={selectVersionList}
        onChange={onSelectVersionListChange}
      >
        {versionList.map(versionT => (
          <Select.Option key={versionT} value={versionT}>
            {versionT}
          </Select.Option>
        ))}
      </Select>
      <Input
        value={issueUrl}
        onChange={(value: string) => setIssueUrl(value)}
        placeholder="issue链接"
        style={{ width: 320, marginTop: 12, marginBottom: 12, marginLeft: 12, marginRight: 12 }}
      />
      <QualityToolsButton
        loading={searchLoading}
        toolName={ToolsName.MRProfilerCodeChangeIssue}
        title={'查询'}
        onClick={() => searchCodeChangeWithIssueUrl(selectVersionList, issueUrl)}
      />
      <br />
      <Table
        rowKey="id"
        loading={tableLoading}
        columns={columns}
        dataSource={attrInfo}
        pagination={false}
        bordered={true}
      />
    </div>
  );
};

export default MRAttr;
