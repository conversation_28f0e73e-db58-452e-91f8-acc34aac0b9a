import React, { useEffect, useState } from 'react';
import { Button, Select, Space, Table, TableProps, Tag } from 'antd';
import { PerformanceCompareValue, PerformanceResult, MetricDataType } from '@shared/typings/tea/alarm';
import QualityApprovalModal from '@/pages/quality/metric/version/components/QualityApprovalModal';
import QualityAlarmModal from '@/pages/quality/metric/version/components/QualityAlarmModal';
import { AlarmVersion, Business, BusinessName } from '@shared/typings/tea/metric';
import { filterNotEmptyStrings, get_enum_values, invoke_and_return, to_slardar_platform } from '@shared/utils/tools';
import { useModel } from '@edenx/runtime/model';
import AppSettingModule from '@/model/appSettingModel';
import {
  build,
  calculateDifference,
  getCurrentGrayVersion,
  getPerformanceAllData,
  transformCompareValue,
} from '@/pages/quality/metric/version/model/QualityDataClient';
import UserShow from '@/component/UserShow';
import { getVersions } from '@/utils/version';
import { logger } from '@/pages/quality/metric/version/utils/Logger';
import { PerformanceDataAlarmConfig } from '@/pages/quality/metric/version/data/PerformanceDataCompareParser';
import UserSettingModule from '@/model/userSettingModel';
import InviteGroupButton from '@/pages/quality/metric/version/components/InviteGroupButton';
import { myFilter, myGroupsBy, myTake } from '@shared/utils/DataParseUtils';
import { VersionType } from '@shared/utils/version_utils';
import { format, waringFormat } from '@shared/utils/DisplayUtils';
import { PlatformType } from '@pa/shared/dist/src/core';

export interface MetricDataDisplayType extends MetricDataType {
  compareValues: PerformanceCompareValue[];
}

const performResultColor: Map<PerformanceResult, string> = new Map([
  [PerformanceResult.stable, 'geekblue'],
  [PerformanceResult.optimization, 'green'],
  [PerformanceResult.deterioration, 'red'],
]);

export const PerformanceResultName: Readonly<Record<PerformanceResult, string>> = {
  [PerformanceResult.stable]: '稳定',
  [PerformanceResult.optimization]: '优化',
  [PerformanceResult.deterioration]: '劣化',
};

async function initVersionInfo(aid: number, platform: PlatformType) {
  if (!aid || !platform) {
    return {};
  }
  // 获取版本信息
  const alarmVersions = (await getVersions(aid, to_slardar_platform(platform))).filter(
    it => it.versionType === VersionType.GRAY || it.versionType >= VersionType.TF_GRAY,
  );
  logger.info(`aid:${aid} versions:${JSON.stringify(alarmVersions)}`);
  return myGroupsBy(alarmVersions, 'version');
}

const QualityCheckList: React.FC = () => {
  const [{ info }] = useModel(AppSettingModule);
  const [userSettingState] = useModel(UserSettingModule);
  const [versionGroup, setCurrentVersionGroup] = useState<Record<string, AlarmVersion[]>>({});
  // const [currentVersionCodes, setCurrentVersionCodes] = useState<string[]>([]);
  const [currentVersionName, setCurrentVersionName] = useState<string | undefined>();
  const currentVersionCodes =
    currentVersionName && versionGroup[currentVersionName]
      ? myTake(versionGroup[currentVersionName], 'versionCode')
      : [];
  const [currentVersionCode, setCurrentVersionCode] = useState<string | undefined>();

  const [compareVersionName, setCompareVersionName] = useState<string | undefined>();
  const compareVersionCodes =
    compareVersionName && versionGroup[compareVersionName]
      ? myTake(versionGroup[compareVersionName], 'versionCode')
      : [];
  const [compareVersionCode, setCompareVersionCode] = useState<string | undefined>();

  const [qualityData, setQualityData] = useState<MetricDataDisplayType[]>([]);
  const appId = info.businessInfo?.app_id?.toString();

  useEffect(() => {
    setCurrentVersionName(undefined);
    setCompareVersionName(undefined);
    setCurrentVersionCode(undefined);
    setCompareVersionCode(undefined);
    initVersionInfo(info?.businessInfo?.aid, info.platform).then(invoke_and_return(setCurrentVersionGroup));
  }, [info?.businessInfo?.aid, info.platform]);

  const columns: TableProps<MetricDataDisplayType>['columns'] = [
    {
      title: '指标名称',
      dataIndex: ['name', 'teaUrl'],
      key: 'name',
      render: (_, { name, teaUrl }) => (
        <a
          onClick={() => {
            window.open(teaUrl);
          }}
        >
          {name}
        </a>
      ),
    },
    {
      title: '负责人',
      dataIndex: 'owner',
      key: 'owner',
      render: (_, { owner }) => <UserShow email={owner ?? ''} />,
    },
    {
      title: '业务',
      dataIndex: 'business',
      key: 'business',
      render: (_, { business }) => <>{BusinessName[business ?? Business.MAIN_FRAMEWORK]}</>,
      filters: get_enum_values(Business).map(it => ({
        text: BusinessName[it],
        value: it,
      })),
      onFilter: (filterBusiness, performanceData) => performanceData.business === filterBusiness,
    },
    {
      title: '版本号',
      key: 'versionCode',
      dataIndex: 'versionCode',
    },
    {
      title: '指标',
      dataIndex: 'value',
      key: 'value',
      render: (_, { value }) => <>{value.toFixed(3)}</>,
    },
    {
      title: '对比版本',
      key: 'compareVersion',
      dataIndex: 'compareValues',
      render: (_, { compareValues }) => <>{compareValues[0].versionCode}</>,
    },
    {
      title: '对比数值',
      key: 'compareVersionValue',
      dataIndex: 'compareValues',
      render: (_, { compareValues }) => <>{format(compareValues[0].compareValue, 3)}</>,
    },
    {
      title: '绝对值变化',
      key: 'absoluteValue',
      dataIndex: 'compareValues',
      render: (_, { compareValues }) => <>{format(compareValues[0].absoluteValue, 3)}</>,
    },
    {
      title: '指标幅度',
      key: 'percentage',
      dataIndex: 'compareValues',
      render: (_, { compareValues }) => {
        // const color = performResultColor.get(PerformanceResult.optimization);
        const color = performResultColor.get(compareValues?.at(0)?.result?.result ?? PerformanceResult.stable);
        return (
          <Tag color={color}>
            <>{format(compareValues[0].percentage, 2)}%</>
          </Tag>
        );
      },
    },
    {
      title: '状态',
      hidden: true,
      key: 'status',
      dataIndex: ['compareValues', 'status'],
      render: (_, { compareValues }) => {
        // const color = performResultColor.get(PerformanceResult.optimization);
        const color = performResultColor.get(compareValues?.at(0)?.result?.result ?? PerformanceResult.stable);
        return (
          <Tag color={color}>
            <>{compareValues?.at(0)?.result?.result === PerformanceResult.deterioration ? '阻塞' : '通过'}</>
          </Tag>
        );
      },
    },
    {
      title: '操作',
      key: 'action',
      hidden: true,
      dataIndex: ['compareValues', 'metricId', 'name', 'value'],
      render: (_, { compareValues, metricId, name, value }) => (
        <Space size="middle">
          <QualityAlarmModal
            metricName={metricId}
            appId={appId}
            platform={info.platform}
            displayName={name ?? ''}
            businessName={info.businessInfo.app_name}
            versionCode={currentVersionCode ?? ''}
            modalText={`性能指标劣化${format(compareValues[0].absoluteValue)},
					幅度超过${format(compareValues?.at(0)?.percentage)}%，发起告警`}
            warningContent={`${waringFormat(
              name,
              value,
              compareValues[0]?.versionCode,
              compareValues[0]?.absoluteValue,
              compareValues[0]?.percentage,
            )}`}
          />
          <InviteGroupButton metricName={metricId} versionCode={getCurrentGrayVersion()} />
          <Button type="primary">豁免</Button>
          <QualityApprovalModal
            text={`性能指标劣化${format(compareValues[0].absoluteValue, 3)},
						幅度超过${format(compareValues?.at(0)?.percentage, 3)}%`}
          />
        </Space>
      ),
    },
  ];
  async function requestMetricData(versionCode: string | undefined) {
    if (!versionCode || !appId) {
      return;
    }
    // 拉取版本数据
    const metrics = await getPerformanceAllData(
      appId,
      info.platform,
      filterNotEmptyStrings([versionCode, compareVersionCode]),
    );
    if (!metrics) {
      logger.error('[requestMetricData] fetch performanceData empty!');
      return;
    }
    logger.log(`[requestMetricData] metrics:${JSON.stringify(metrics)}`);
    const currentVersionData = myFilter(metrics, 'versionCode', versionCode);
    const compareVersionData = myFilter(metrics, 'versionCode', compareVersionCode);
    const alarmConfigs: Map<string, PerformanceDataAlarmConfig> = build(currentVersionData, {
      amplitudeThreshold: 0,
      // differenceThreshold: 0,
    });
    // 劣化计算&判断
    // 同大版本的上一个小版本,用于计算环比
    const compareValues = calculateDifference(currentVersionData, compareVersionData, alarmConfigs);
    // 转换显示数据
    const displayQualityData = transformCompareValue(currentVersionData, compareValues);
    // 显示数据
    setQualityData(displayQualityData);
    logger.log(`displayQualityData:${JSON.stringify(displayQualityData)}`);
    return;
  }

  useEffect(() => {
    if (compareVersionCode !== undefined) {
      // requestPerformanceData(currentVersionCode).then();
      requestMetricData(currentVersionCode).then();
    }
  }, [currentVersionCode, compareVersionCode, appId]);

  return (
    <>
      {'版本：'}
      <Select
        key={`${appId}_base_select_version_name`}
        placeholder="选择当前版本"
        style={{ marginRight: 15, marginBottom: 10 }}
        value={currentVersionName}
        onChange={(value, option) => {
          setCurrentVersionName(value);
          setCurrentVersionCode(undefined);
        }}
      >
        {Object.keys(versionGroup).map(versionName => (
          <Select.Option key={`base_${versionName}`} value={versionName}>
            {versionName}
          </Select.Option>
        ))}
      </Select>
      {'版本号：'}
      <Select
        placeholder="选择当前版本"
        key={`${appId}_base_select_version_code`}
        style={{ marginRight: 15, marginBottom: 10 }}
        value={currentVersionCode}
        onChange={(value, option) => {
          setCurrentVersionCode(value);
          logger.debug(`[QualityCheckList] setCurrentVersionCode ${value}`);
        }}
      >
        {currentVersionCodes.map(versionCode => (
          <Select.Option key={`base_${versionCode}`} value={versionCode}>
            {versionCode}
          </Select.Option>
        ))}
      </Select>
      {'对比版本：'}
      <Select
        placeholder="选择对比大版本"
        key={`${appId}_compare_select_version_name`}
        value={compareVersionName}
        style={{ marginRight: 15, marginBottom: 10 }}
        onChange={(value, option) => {
          setCompareVersionName(value);
          setCompareVersionCode(undefined);
        }}
      >
        {Object.keys(versionGroup).map(versionName => (
          <Select.Option key={`compare_${versionName}`} value={versionName}>
            {versionName}
          </Select.Option>
        ))}
      </Select>
      {'对比版本号：'}
      <Select
        placeholder="选择对比小版本"
        key={`${appId}_compare_select_version_code`}
        style={{ marginRight: 15, marginBottom: 10 }}
        value={compareVersionCode}
        onChange={(value, _) => {
          setCompareVersionCode(value);
        }}
      >
        {compareVersionCodes
          .filter(v => v !== currentVersionCode)
          .map(versionCode => (
            <Select.Option key={`compare_${versionCode}`} value={versionCode}>
              {versionCode}
            </Select.Option>
          ))}
      </Select>
      <Table columns={columns} dataSource={qualityData} />
    </>
  );
};

export default QualityCheckList;
