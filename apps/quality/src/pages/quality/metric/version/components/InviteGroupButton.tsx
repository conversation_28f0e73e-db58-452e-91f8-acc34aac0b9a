import React, { useEffect, useState } from 'react';
import { Button, message, Popconfirm } from 'antd';
import { NetworkCode } from '@pa/shared/dist/src/core';
import { useModel } from '@edenx/runtime/model';
import UserSettingModule from '@/model/userSettingModel';
import { getGroupId, inviteGroup } from '@api/metricsAlarmSystem';
import AppSettingModule from '@/model/appSettingModel';

interface InviteButtonProps {
  metricName: string;
  versionCode: string;
}

enum InviteGroupStatus {
  INIT,
  HAS_INIT,
  INVITING,
  INVITED,
  FAILED,
}

const InviteButton: React.FC<InviteButtonProps> = ({ metricName, versionCode }) => {
  const [groupId, setGroupId] = useState<string | undefined>();
  const [userSettingState] = useModel(UserSettingModule);
  const [appSettingState] = useModel(AppSettingModule);
  const appId = Math.floor(appSettingState.info.id).toString();
  const isGroupExits = groupId !== undefined && groupId.length > 0;
  const [status, setStatus] = useState(InviteGroupStatus.INIT);
  // const platform = to_platform(appSettingState.info.platform);

  function isDisable(inviteGroupStatus: InviteGroupStatus) {
    return inviteGroupStatus === InviteGroupStatus.INIT;
  }

  function isLoading(inviteGroupStatus: InviteGroupStatus) {
    return inviteGroupStatus === InviteGroupStatus.INVITING;
  }

  useEffect(() => {
    getGroupId({
      query: {
        appId,
        platform: appSettingState.info.platform,
        metricName,
        versionCode,
      },
    }).then(rsp => {
      const chatId = rsp?.data?.groupId;
      console.debug(`InviteButton query group Id => ${chatId}`);
      setGroupId(chatId);
      setStatus(InviteGroupStatus.HAS_INIT);
    });
  }, []);

  const handleInviteGroup = async () => {
    if (status === InviteGroupStatus.INVITED) {
      if (groupId) {
        window.open(`https://applink.feishu.cn/client/chat/open?openChatId=${groupId}`);
      }
      return;
    }
    setStatus(InviteGroupStatus.INVITING);
    inviteGroup({
      data: {
        appId,
        metricName,
        platform: appSettingState.info.platform,
        versionCode,
        email: userSettingState.info.email,
      },
    })
      .then(r => {
        if (r.code === NetworkCode.Success) {
          setStatus(InviteGroupStatus.INVITED);
          setGroupId(r?.data?.chat_id);
          message.success(isGroupExits ? '已加入群聊' : '已创建群聊');
          window.open(`https://applink.feishu.cn/client/chat/open?openChatId=${r?.data?.chat_id}`);
        } else {
          setStatus(InviteGroupStatus.FAILED);
          message.error('加入群聊失败，请刷新重试');
        }
      })
      .catch(e => {
        console.log(e);
        setStatus(InviteGroupStatus.FAILED);
        message.error('加入群聊失败，请刷新重试');
      });
  };

  function joinGroupText(): string {
    return status === InviteGroupStatus.INVITED ? '打开群聊' : isGroupExits ? '加入群聊' : '创建群聊';
  }

  return (
    <Popconfirm
      title={`确定要${joinGroupText()}`}
      onConfirm={() => handleInviteGroup()}
      okText={'确定'}
      cancelText={'取消'}
      disabled={isDisable(status)}
    >
      <Button disabled={isDisable(status)} type={'primary'} loading={isLoading(status)}>
        {joinGroupText()}
      </Button>
    </Popconfirm>
  );
};

export default InviteButton;
