import React, { useState } from 'react';
import { <PERSON><PERSON>, Modal } from 'antd';
import { sendManuAlarmCard } from '@api/metricsAlarmSystem';
import { useModel } from '@edenx/runtime/model';
import UserSettingModule from '@/model/userSettingModel';
import { PlatformType } from '@pa/shared/dist/src/core';
import { logger } from '@/pages/quality/metric/version/utils/Logger';
interface QualityAlarmModalPros {
  modalText: string;
  metricName: string;
  appId: string;
  platform: PlatformType;
  displayName: string;
  businessName: string;
  versionCode: string;
  warningContent: string;
}

const QualityAlarmModal: React.FC<QualityAlarmModalPros> = ({
  modalText,
  metricName,
  appId,
  platform,
  displayName,
  businessName,
  versionCode,
  warningContent,
}) => {
  const [open, setOpen] = useState(false);
  const [isAlarming, setIsAlarming] = useState(false);
  const [confirmLoading, setConfirmLoading] = useState(false);
  const [userSettingState] = useModel(UserSettingModule);
  const showModal = () => {
    setOpen(true);
  };

  function createWarningCardInfo() {
    const title = `【${businessName}灰度】${platform}核心性能指标告警（${versionCode}）`;
    // 构造内容
    const detailContent = warningContent;
    // const brLine = "<br />";
    const mainContent = `${displayName}在灰度版本（${versionCode}）存在严重劣化，请及时介入归因，否则会影响版本灰度发布！`;

    return {
      title,
      detailContent,
      mainContent,
    };
  }

  const handleOk = async () => {
    setConfirmLoading(true);
    setIsAlarming(true);
    // await sendAlarmTest({ data: { msg: "指标告警通知测试" } });
    await sendManuAlarmCard({
      data: {
        msg: '指标手动告警通知',
        appId,
        versionCode,
        metricName,
        platform,
        ...createWarningCardInfo(),
        email: userSettingState.info.email,
      },
    })
      .then(r => {
        setConfirmLoading(false);
        setOpen(false);
        setIsAlarming(false);
        logger.info('发起告警通知成功');
      })
      .catch(e => {
        setConfirmLoading(false);
        setOpen(false);
        setIsAlarming(false);
        logger.info(`发起告警通知失败, ${e}`);
      });
  };

  const handleCancel = () => {
    console.log('Clicked cancel button');
    setOpen(false);
  };

  return (
    <>
      <Button type="primary" onClick={showModal}>
        告警
      </Button>
      <Modal
        title="告警通知确认"
        open={open}
        onOk={handleOk}
        confirmLoading={confirmLoading}
        onCancel={handleCancel}
        okText={<>确定</>}
        cancelText={<>取消</>}
      >
        <p>{isAlarming ? '发起告警通知中' : modalText}</p>
      </Modal>
    </>
  );
};

export default QualityAlarmModal;
