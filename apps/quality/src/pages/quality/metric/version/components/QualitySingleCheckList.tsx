import React, { useEffect, useMemo, useRef, useState } from 'react';
import { Select, DatePicker, type TabsProps, Tabs, Switch, Space } from 'antd';
import { AlarmVersion } from '@shared/typings/tea/metric';
import { to_slardar_platform } from '@shared/utils/tools';
import { useModel } from '@edenx/runtime/model';
import AppSettingModule from '@/model/appSettingModel';
import { getReleaseVersions, getVersions } from '@/utils/version';
import { logger } from '@/pages/quality/metric/version/utils/Logger';
import UserSettingModule from '@/model/userSettingModel';
import { CompareVersionCode, VersionType } from '@shared/utils/version_utils';
import { getAllMetrics, getMetricOfVersions } from '@api/metricsAlarmSystem';
import { DBTeaMetric } from '../../../../../../api/model/TeaMetricTable';
import { SlardarPlatformType } from '@pa/shared/dist/src/appSettings/appSettings';
import dayjs from 'dayjs';
import SimpleVersionTreeSelect from '@/pages/quality/metric/version/components/common/SimpleVersionTreeSelect';
import { buildTreeFromSimpleVersionList, buildTreeFromVersionList } from '@/component/VersionSelector/utils';
import MetricDateTable, { MetricTableData } from '@/pages/quality/metric/version/components/common/MetricDateTable';
import MetricDetailTable, {
  MetricDetailDisplayType,
} from '@/pages/quality/metric/version/components/common/MetricDetailTable';
import MetricChart from '@/pages/quality/metric/version/components/common/MetricChart';
import { PlatformType } from '@pa/shared/dist/src/core';

const { RangePicker } = DatePicker;

type TransformedItem = {
  time: string;
  timeStamp: number;
  [versionCode: string]: any;
};

function toTableData(data: MetricDetailDisplayType[]) {
  if (!data) {
    return;
  }

  const result = data.reduce((acc: MetricTableData[], curr) => {
    const { versionCode } = curr;
    // Check if the time entry already exists in the accumulator
    let timeEntry = acc.find(item => item.versionCode === versionCode);

    // If the time entry doesn't exist, create a new one
    if (!timeEntry) {
      timeEntry = { ...curr };
      acc.push(timeEntry);
    }

    // Add the versionCode and value to the time entry
    if (curr.timeStamp) {
      timeEntry[dayjs.unix(curr.timeStamp).format('YYYY-MM-DD')] = curr.value;
    }

    return acc;
  }, []);
  // 按照时间排序
  result.sort((a, b) => a.timeStamp - b.timeStamp);
  // Convert the result to an array
  return result;
}

function toChartData(metricDataList: MetricDetailDisplayType[]) {
  if (!metricDataList) {
    return;
  }

  const result = metricDataList.reduce((acc: TransformedItem[], curr) => {
    const time = dayjs.unix(curr.timeStamp).format('YYYY-MM-DD');
    // Check if the time entry already exists in the accumulator
    let timeEntry = acc.find(item => item.time === time);

    // If the time entry doesn't exist, create a new one
    if (!timeEntry) {
      timeEntry = { time, timeStamp: curr.timeStamp };
      acc.push(timeEntry);
    }

    // Add the versionCode and value to the time entry
    if (curr.versionCode) {
      timeEntry[curr.versionCode] = curr.value;
    }

    return acc;
  }, []);
  // 按照时间排序
  result.sort((a, b) => a.timeStamp - b.timeStamp);

  // Convert the result to an array
  return result;
}

export interface QualitySingleCheckListProps {
  metricId?: string;
  inStartTime?: number; // 单位s
  inEndTime?: number; // 单位s
  onChange?: (metricId?: string, startTime?: number, endTime?: number) => void;
}

const QualitySingleCheckList: React.FC<QualitySingleCheckListProps> = ({
  metricId,
  inStartTime,
  inEndTime,
  onChange,
}) => {
  const [{ info }] = useModel(AppSettingModule);
  const [userSettingState] = useModel(UserSettingModule);
  const [metricName, setMetricName] = useState(metricId);
  const [metrics, setMetrics] = useState<DBTeaMetric[]>();
  const [qualityData, setQualityData] = useState<MetricDetailDisplayType[]>([]);
  const appId = info.businessInfo?.app_id?.toString();
  const [starTime, setStartTime] = useState<number>(inStartTime ?? 0);
  const [endTime, setEndTime] = useState<number>(inEndTime ?? 0);
  const [versionCodes, setVersionCodes] = useState<string[]>();
  const chartData = toChartData(qualityData);
  const tableData = toTableData(qualityData);
  const [showChart, setShowChart] = useState(true);
  // 版本列表
  const [versions, setVersions] = useState<AlarmVersion[]>([]);
  useEffect(() => {
    getVersions(info?.businessInfo?.aid, to_slardar_platform(info.platform))
      .then(it => it.sort((a, b) => CompareVersionCode(b.versionCode, a.versionCode)))
      .then(setVersions);
  }, [info.platform, userSettingState]);

  useEffect(() => {
    if (onChange) {
      onChange(metricName, starTime, endTime);
    }
  }, [starTime, endTime, metricName]);

  async function requestMetricData() {
    if (!appId) {
      return;
    }
    // 拉取版本数据
    const rsp = await getAllMetrics({
      data: {
        appId,
        platform: info.platform,
      },
    });
    if (!rsp || !rsp.data) {
      logger.error('[requestMetricData] fetch performanceData empty!');
      return;
    }
    return rsp.data;
  }

  useEffect(() => {
    requestMetricData().then(it => {
      logger.info(`[requestMetricData] fetch performanceData size: ${it?.length}`);
      if (it) {
        setMetrics(it);
        if (it?.some(metric => metric?.Name === metricId) === false) {
          setMetricName(undefined);
          setStartTime(0);
          setEndTime(0);
          setVersionCodes(undefined);
        }
      }
    });
  }, [appId]);

  useEffect(() => {
    logger.log(`[requestMetricData] metrics:${JSON.stringify(metricName)}`);
    if (!metricName || !(starTime && endTime)) {
      setQualityData([]);
      return;
    }
    getMetricOfVersions({
      data: {
        appId,
        platform: info.platform,
        metricName,
        versionCodes,
      },
    }).then(rsp => {
      if (!rsp || !rsp.data) {
        return;
      }
      const t_metrics = rsp.data
        ?.filter(
          it => !((it?.versionCode?.length ?? 0) < 9 && info.platform === PlatformType.Android && info?.id === 177502),
        )
        ?.filter(it => it.timeStamp >= starTime && it.timeStamp <= endTime)
        ?.sort((a, b) => {
          if (!a || !b || !a.versionCode || !b.versionCode) {
            return 0;
          }
          const ret = b.versionCode.localeCompare(a.versionCode);
          if (ret !== 0) {
            return ret > 0 ? 1 : -1;
          }
          return b.timeStamp - a.timeStamp > 0 ? 1 : -1;
        });
      // 显示数据
      setQualityData(t_metrics);
    });
  }, [metricName, starTime, endTime, versionCodes]);

  const handleVersionSelected = (_versions: string[]) => {
    logger.debug(`handleVersionSelected: ${JSON.stringify(_versions)}`);
    setVersionCodes(_versions);
  };

  const items: TabsProps['items'] = [
    {
      key: '1',
      label: '时间维度',
      children: <MetricDateTable startTimestamp={starTime} endTimestamp={endTime} data={tableData} />,
    },
    {
      key: '2',
      label: '版本维度',
      children: <MetricDetailTable data={qualityData} />,
    },
  ];

  return (
    <div style={{ marginBottom: 15 }}>
      <div>
        {'指标：'}
        <Select
          showSearch
          optionFilterProp="children"
          placeholder="选择指标"
          value={metricName}
          style={{ marginRight: 15, marginBottom: 10, width: 350 }}
          onChange={(value, option) => {
            setMetricName(value);
          }}
        >
          {metrics !== undefined &&
            metrics.map(metric => (
              <Select.Option key={metric.Name} value={metric.Name}>
                {metric.DisplayName}
              </Select.Option>
            ))}
        </Select>
        {'时间：'}
        <RangePicker
          value={starTime > 0 && endTime > 0 ? [dayjs.unix(starTime), dayjs.unix(endTime)] : undefined}
          style={{ marginRight: 15, marginBottom: 10, width: 250 }}
          onChange={(value, dateString) => {
            console.log('Selected Time: ', value);
            console.log('Formatted Selected Time: ', dateString);
            setStartTime(dayjs(dateString[0]).unix());
            setEndTime(dayjs(dateString[1]).unix());
          }}
        />
        {'版本：'}
        <SimpleVersionTreeSelect versions={buildTreeFromSimpleVersionList(versions)} onChange={handleVersionSelected} />
        <span style={{ marginLeft: 15, marginBottom: 10 }}>{'显示趋势图：'}</span>
        <Switch
          checked={showChart}
          onChange={(checked, event) => {
            setShowChart(checked);
          }}
          checkedChildren={'显示'}
          unCheckedChildren={'不显示'}
        />
      </div>
      {showChart && chartData && <MetricChart data={chartData} />}
      <Tabs defaultActiveKey="1" items={items} />
    </div>
  );
};

export default QualitySingleCheckList;
