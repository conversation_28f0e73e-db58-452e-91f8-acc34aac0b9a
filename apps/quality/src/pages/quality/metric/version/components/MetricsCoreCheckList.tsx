import React, { useEffect, useState } from 'react';
import { Button, CheckboxOptionType, Checkbox, Space, Table, TableProps, Tag } from 'antd';
import { PerformanceCompareValue, PerformanceResult, MetricDataType } from '@shared/typings/tea/alarm';
import QualityApprovalModal from '@/pages/quality/metric/version/components/QualityApprovalModal';
import QualityAlarmModal from '@/pages/quality/metric/version/components/QualityAlarmModal';
import { Business, BusinessName } from '@shared/typings/tea/metric';
import { filterNotEmptyStrings, get_enum_values } from '@shared/utils/tools';
import { useModel } from '@edenx/runtime/model';
import AppSettingModule from '@/model/appSettingModel';
import {
  build,
  calculateDifference,
  getPerformanceCoreData,
  mergePerformanceCompareValue,
  transformCompareValue,
} from '@/pages/quality/metric/version/model/QualityDataClient';
import UserShow from '@/component/UserShow';
import { logger } from '@/pages/quality/metric/version/utils/Logger';
import { PerformanceDataAlarmConfig } from '@/pages/quality/metric/version/data/PerformanceDataCompareParser';
import InviteGroupButton from '@/pages/quality/metric/version/components/InviteGroupButton';
import { myFilter } from '@shared/utils/DataParseUtils';
import { format, waringFormat } from '@shared/utils/DisplayUtils';
import { getLastBigVersion, getLastSmallVersion } from '@api/metricsAlarmSystem';

export interface MetricDataDisplayType extends MetricDataType {
  compareValues: PerformanceCompareValue[];
}

const performResultColor: Map<PerformanceResult, string> = new Map([
  [PerformanceResult.stable, 'geekblue'],
  [PerformanceResult.optimization, 'green'],
  [PerformanceResult.deterioration, 'red'],
]);

export interface MetricsCoreCheckListProp {
  versionCode?: string;
}

const MetricsCoreCheckList: React.FC<MetricsCoreCheckListProp> = ({ versionCode }) => {
  const [{ info }] = useModel(AppSettingModule);
  const currentVersionCode = versionCode;
  const [qualityData, setQualityData] = useState<MetricDataDisplayType[]>([]);
  const appId = info.businessInfo?.app_id?.toString();
  const columns: TableProps<MetricDataDisplayType>['columns'] = [
    {
      title: '指标名称',
      dataIndex: ['name', 'teaUrl'],
      key: 'name',
      render: (_, { name, teaUrl }) => (
        <a
          onClick={() => {
            window.open(teaUrl);
          }}
        >
          {name}
        </a>
      ),
    },
    {
      title: '负责人',
      dataIndex: 'owner',
      key: 'owner',
      render: (_, { owner }) => <UserShow email={owner ?? ''} />,
    },
    {
      title: '业务',
      dataIndex: 'business',
      key: 'business',
      render: (_, { business }) => <>{BusinessName[business ?? Business.MAIN_FRAMEWORK]}</>,
      filters: get_enum_values(Business).map(it => ({
        text: BusinessName[it],
        value: it,
      })),
      onFilter: (filterBusiness, performanceData) => performanceData.business === filterBusiness,
    },
    {
      title: '版本号',
      key: 'versionCode',
      dataIndex: 'versionCode',
    },
    {
      title: '指标',
      dataIndex: 'value',
      key: 'value',
      render: (_, { value }) => <>{value.toFixed(3)}</>,
    },
    {
      title: '环比版本',
      key: 'h_compareVersion',
      dataIndex: 'compareValues',
      render: (_, { compareValues }) => <>{compareValues[0].versionCode}</>,
    },
    {
      title: '环比变化',
      key: 'h_diff',
      dataIndex: 'compareValues',
      render: (_, { compareValues }) => <>{format(compareValues[0].absoluteValue, 3)}</>,
    },
    {
      title: '环比幅度',
      key: 'h_amp',
      dataIndex: 'compareValues',
      render: (_, { compareValues }) => {
        // const color = performResultColor.get(PerformanceResult.optimization);
        const color = performResultColor.get(compareValues?.at(0)?.result?.result ?? PerformanceResult.stable);
        return (
          <Tag color={color}>
            <>{format(compareValues[0].percentage, 2)}%</>
          </Tag>
        );
      },
    },
    {
      title: '同比版本',
      key: 't_compareVersion',
      dataIndex: 'compareValues',
      render: (_, { compareValues }) => <>{compareValues[1].versionCode}</>,
    },
    {
      title: '同比变化',
      key: 't_diff',
      dataIndex: 'compareValues',
      render: (_, { compareValues }) => <>{format(compareValues[1].absoluteValue, 3)}</>,
    },
    {
      title: '同比幅度',
      key: 't_amp',
      dataIndex: 'compareValues',
      render: (_, { compareValues }) => {
        // const color = performResultColor.get(PerformanceResult.optimization);
        const color = performResultColor.get(compareValues?.at(1)?.result?.result ?? PerformanceResult.stable);
        return (
          <Tag color={color}>
            <>{format(compareValues[1].percentage, 2)}%</>
          </Tag>
        );
      },
    },
    {
      title: '状态',
      key: 'status',
      dataIndex: ['compareValues', 'status'],
      render: (_, { compareValues }) => {
        // const color = performResultColor.get(PerformanceResult.optimization);
        const color = performResultColor.get(compareValues?.at(0)?.result?.result ?? PerformanceResult.stable);
        return (
          <Tag color={color}>
            <>{compareValues?.at(0)?.result?.result === PerformanceResult.deterioration ? '阻塞' : '通过'}</>
          </Tag>
        );
      },
    },
    {
      title: '操作',
      key: 'action',
      dataIndex: ['compareValues', 'metricId', 'name', 'value'],
      render: (_, { compareValues, metricId, name, value }) => (
        <Space size="middle">
          <QualityAlarmModal
            metricName={metricId}
            appId={info.businessInfo.app_id.toString()}
            platform={info.platform}
            displayName={name ?? ''}
            businessName={info.businessInfo.app_name}
            versionCode={currentVersionCode ?? ''}
            modalText={`性能指标劣化${format(compareValues[0].absoluteValue)},
					幅度超过${format(compareValues?.at(0)?.percentage)}%，发起告警`}
            warningContent={`${waringFormat(
              name,
              value,
              compareValues[0]?.versionCode,
              compareValues[0]?.absoluteValue,
              compareValues[0]?.percentage,
            )}`}
          />
          {currentVersionCode && <InviteGroupButton metricName={metricId} versionCode={currentVersionCode} />}
          <Button type="primary">豁免</Button>
          <QualityApprovalModal
            text={`性能指标劣化${format(compareValues[0].absoluteValue, 3)},
						幅度超过${format(compareValues?.at(0)?.percentage, 3)}%`}
          />
        </Space>
      ),
    },
  ];
  function isHidden(key: string, checkList: string[]) {
    return (
      (['h_diff', 'h_amp'].includes(key) && !checkList.includes('h_compareVersion')) ||
      (['t_diff', 't_amp'].includes(key) && !checkList.includes('t_compareVersion'))
    );
  }

  const filterKeys = ['h_compareVersion', 'owner', 't_compareVersion', 'action', 'business'];
  const defaultCheckedList = filterKeys.filter(it => it !== 't_compareVersion');
  const [checkedList, setCheckedList] = useState(defaultCheckedList);
  const options = columns
    .filter(it => it.key && filterKeys.includes(it.key as string))
    .map(({ key, title }) => ({
      label: title,
      value: key,
    }));
  const newColumns = columns.map(item => ({
    ...item,
    hidden:
      (filterKeys.includes(item.key as string) && !checkedList.includes(item.key as string)) ||
      isHidden(item.key as string, checkedList),
  }));

  async function requestMetricData(_versionCode: string | undefined) {
    if (!_versionCode || !appId) {
      return;
    }
    // 获取当前上一个小版本
    const lastSmallVersionCode = (
      await getLastSmallVersion({
        data: {
          appId,
          platform: info.platform,
          versionCode: _versionCode,
        },
      })
    )?.toString();
    // 获取上个灰度版本的小版本
    const lastBigVersionCode = (
      await getLastBigVersion({
        data: {
          appId,
          platform: info.platform,
          versionCode: _versionCode,
        },
      })
    )?.toString();
    logger.info(
      `[requestMetricData] _versionCode => ${_versionCode} lastSmallVersionCode => ${lastSmallVersionCode} lastBigVersionCode => ${lastBigVersionCode}`,
    );
    // 拉取版本数据
    const metrics = await getPerformanceCoreData(
      appId,
      info.platform,
      filterNotEmptyStrings([_versionCode, lastSmallVersionCode, lastBigVersionCode]),
    );
    if (!metrics) {
      logger.error('[requestMetricData] fetch performanceData empty!');
      return;
    }
    logger.log(`[requestMetricData] metrics:${JSON.stringify(metrics)}`);
    const currentVersionData = myFilter(metrics, 'versionCode', _versionCode);
    const lastSmallVersionData = myFilter(metrics, 'versionCode', lastSmallVersionCode);
    const lastBigVersionData = myFilter(metrics, 'versionCode', lastBigVersionCode);
    logger.debug(`requestMetricData] lastSmallVersionData:${JSON.stringify(lastSmallVersionData)}`);

    const alarmConfigs: Map<string, PerformanceDataAlarmConfig> = build(currentVersionData, {
      amplitudeThreshold: 0,
      // differenceThreshold: 0,
    });
    // 劣化计算&判断
    // 同大版本的上一个小版本,用于计算环比
    const compareValuesOfLastSmallVersion = calculateDifference(currentVersionData, lastSmallVersionData, alarmConfigs);
    logger.debug(`compareValuesOfLastSmallVersion:${JSON.stringify(compareValuesOfLastSmallVersion)}`);
    // 上个大版本的同周期小版本,用于计算同比
    const compareValuesOfLastBigVersion = calculateDifference(currentVersionData, lastBigVersionData, alarmConfigs);
    logger.debug(`compareValuesOfLastBigVersion:${JSON.stringify(compareValuesOfLastBigVersion)}`);
    // 转换显示数据
    const displayQualityData = transformCompareValue(currentVersionData, compareValuesOfLastSmallVersion);
    const displayQualityDataV2 = mergePerformanceCompareValue(displayQualityData, compareValuesOfLastBigVersion);
    // 显示数据
    setQualityData(displayQualityDataV2);
    return;
  }

  useEffect(() => {
    if (currentVersionCode !== undefined) {
      // requestPerformanceData(currentVersionCode).then();
      requestMetricData(currentVersionCode).then();
    }
  }, [currentVersionCode, appId]);

  return (
    <>
      <Checkbox.Group
        value={checkedList}
        options={options as CheckboxOptionType[]}
        onChange={value => {
          setCheckedList(value as string[]);
        }}
      />

      <Table columns={newColumns} dataSource={qualityData} />
    </>
  );
};

export default MetricsCoreCheckList;
