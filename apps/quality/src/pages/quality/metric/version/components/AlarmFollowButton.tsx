import React from 'react';
import { useModel } from '@edenx/runtime/model';
import UserSettingModule from '@/model/userSettingModel';
import FollowButton from '@/pages/quality/metric/version/components/FollowButton';
import { getSubscribers, hasSubscribed, subscribe, unSubscribe } from '@api/metricsAlarmSystem';
import { PlatformType } from '@pa/shared/dist/src/core';
import AppSettingModule from '@/model/appSettingModel';
import { logger } from '@/pages/quality/metric/version/utils/Logger';

export interface AlarmFollowButtonProps {
  metricId: string;
  onStatusChange?: (followed: FollowStatus) => void;
}

export enum FollowStatus {
  Followed,
  UnFollowed,
}

export async function queryFollowingDefault(
  email: string,
  metricId: string,
  appId: string,
  platformType: PlatformType,
) {
  if (metricId) {
    return await hasSubscribed({
      data: {
        metric_name: metricId,
        user_email: email,
        appId,
        platformType,
      },
    });
  }
  return false;
}

export async function onFollowDefault(email: string, metricId: string, appId: string, platformType: PlatformType) {
  // 关注指标
  if (metricId) {
    // 订阅指标, 用于后续告警
    const result = await subscribe({
      data: {
        metric_name: metricId,
        user_email: email,
        appId,
        platformType,
      },
    });
    const subscribers = await getSubscribers({ data: { metric_name: metricId, appId, platformType } });
    logger.info(`result:${result} subscribers: ${JSON.stringify(subscribers)}`);
    if (result) {
      return result;
    }
  }
  return false;
}

export async function onUnFollowDefault(email: string, metricId: string, appId: string, platformType: PlatformType) {
  // 取消关注
  if (metricId) {
    // 订阅指标, 用于后续告警
    const result = await unSubscribe({
      data: {
        metric_name: metricId,
        user_email: email,
        appId,
        platformType,
      },
    });
    const subscribers = await getSubscribers({ data: { metric_name: metricId, appId, platformType } });
    logger.info(`result:${result} subscribers: ${JSON.stringify(subscribers)}`);
    if (result) {
      return result;
    }
  }
  return false;
}
const AlarmFollowButton: React.FC<AlarmFollowButtonProps> = ({ metricId, onStatusChange }) => {
  const [userSettingState] = useModel(UserSettingModule);
  const { email } = userSettingState.info;
  const [appSettingState] = useModel(AppSettingModule);
  const appId = appSettingState.info.businessInfo?.app_id?.toString();
  const platformType = appSettingState.info.businessInfo.platform;

  return (
    <FollowButton
      request={async () => queryFollowingDefault(email, metricId, appId, platformType)}
      onFollow={async () => {
        const success = await onFollowDefault(email, metricId, appId, platformType);
        if (success && onStatusChange) {
          onStatusChange(FollowStatus.Followed);
        }
        return success;
      }}
      onUnfollow={async () => {
        const success = await onUnFollowDefault(email, metricId, appId, platformType);
        if (success && onStatusChange) {
          onStatusChange(FollowStatus.UnFollowed);
        }
        return success;
      }}
    />
  );
};

export default AlarmFollowButton;
