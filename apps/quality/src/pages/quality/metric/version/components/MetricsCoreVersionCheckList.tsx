import React, { useEffect, useState } from 'react';
import { Button, CheckboxOptionType, Checkbox, Space, Table, TableProps, Tag, Select, Divider } from 'antd';
import { PerformanceCompareValue, PerformanceResult, MetricDataType } from '@shared/typings/tea/alarm';
import QualityApprovalModal from '@/pages/quality/metric/version/components/QualityApprovalModal';
import QualityAlarmModal from '@/pages/quality/metric/version/components/QualityAlarmModal';
import { AlarmVersion, Business, BusinessName } from '@shared/typings/tea/metric';
import { filterNotEmptyStrings, get_enum_values, invoke_and_return, to_slardar_platform } from '@shared/utils/tools';
import { useModel } from '@edenx/runtime/model';
import AppSettingModule from '@/model/appSettingModel';
import {
  build,
  calculateDifference,
  getPerformanceCoreData,
  getSomePerformanceCoreData,
  mergePerformanceCompareValue,
  transformCompareValue,
} from '@/pages/quality/metric/version/model/QualityDataClient';
import UserShow from '@/component/UserShow';
import { logger } from '@/pages/quality/metric/version/utils/Logger';
import { PerformanceDataAlarmConfig } from '@/pages/quality/metric/version/data/PerformanceDataCompareParser';
import InviteGroupButton from '@/pages/quality/metric/version/components/InviteGroupButton';
import { myFilter, myGroupsBy, myTake } from '@shared/utils/DataParseUtils';
import { format, waringFormat } from '@shared/utils/DisplayUtils';
import { getLastBigVersion, getLastSmallVersion, getSomeCorePerformanceData } from '@api/metricsAlarmSystem';
import { PlatformType } from '@pa/shared/dist/src/core';
import { getVersions } from '@/utils/version';
import { VersionType } from '@shared/utils/version_utils';

export interface MetricDataDisplayType extends MetricDataType {
  compareValues: PerformanceCompareValue[];
}

const performResultColor: Map<PerformanceResult, string> = new Map([
  [PerformanceResult.stable, 'geekblue'],
  [PerformanceResult.optimization, 'green'],
  [PerformanceResult.deterioration, 'red'],
]);

export interface MetricsCoreCheckListProp {
  versionCode?: string;
  version?: string;
  _compareVersion?: string;
  _compareVersionCode?: string;
  metricName: string;
}

const MetricsCoreVersionCheckList: React.FC<MetricsCoreCheckListProp> = ({
  version,
  versionCode,
  _compareVersion,
  _compareVersionCode,
  metricName,
}) => {
  const [{ info }] = useModel(AppSettingModule);
  const [versionGroup, setCurrentVersionGroup] = useState<Record<string, AlarmVersion[]>>({});
  const [currentVersionName, setCurrentVersionName] = useState<string | undefined>(`${version}`);
  const currentVersionCodes =
    currentVersionName && versionGroup[currentVersionName]
      ? myTake(versionGroup[currentVersionName], 'versionCode')
      : [];
  const [currentVersionCode, setCurrentVersionCode] = useState<string | undefined>(`${versionCode}`);

  const [compareVersionName, setCompareVersionName] = useState<string | undefined>(`${_compareVersion}`);
  const compareVersionCodes =
    compareVersionName && versionGroup[compareVersionName]
      ? myTake(versionGroup[compareVersionName], 'versionCode')
      : [];
  const [compareVersionCode, setCompareVersionCode] = useState<string | undefined>(`${_compareVersionCode}`);
  const [qualityData, setQualityData] = useState<MetricDataDisplayType[]>([]);
  const appId = info.businessInfo?.app_id?.toString();

  async function initVersionInfo(aid: number, platform: PlatformType) {
    if (!aid || !platform) {
      return {};
    }
    // 获取版本信息
    const alarmVersions = (await getVersions(aid, to_slardar_platform(platform))).filter(
      it => it.versionType === VersionType.GRAY || it.versionType >= VersionType.TF_GRAY,
    );
    logger.info(`aid:${aid} versions:${JSON.stringify(alarmVersions)}`);
    return myGroupsBy(alarmVersions, 'version');
  }

  useEffect(() => {
    // setCurrentVersionName(undefined);
    // setCompareVersionName(undefined);
    // setCurrentVersionCode(undefined);
    // setCompareVersionCode(undefined);
    initVersionInfo(info?.businessInfo?.aid, info.platform).then(invoke_and_return(setCurrentVersionGroup));
  }, [info?.businessInfo?.aid, info.platform]);
  const columns: TableProps<MetricDataDisplayType>['columns'] = [
    {
      title: '指标名称',
      dataIndex: ['name', 'teaUrl'],
      key: 'name',
      render: (_, { name, teaUrl }) => (
        <a
          onClick={() => {
            window.open(teaUrl);
          }}
        >
          {name}
        </a>
      ),
    },
    {
      title: '负责人',
      dataIndex: 'owner',
      key: 'owner',
      render: (_, { owner }) => <UserShow email={owner ?? ''} />,
    },
    {
      title: '业务',
      dataIndex: 'business',
      key: 'business',
      render: (_, { business }) => <>{BusinessName[business ?? Business.MAIN_FRAMEWORK]}</>,
      filters: get_enum_values(Business).map(it => ({
        text: BusinessName[it],
        value: it,
      })),
      onFilter: (filterBusiness, performanceData) => performanceData.business === filterBusiness,
    },
    {
      title: '版本号',
      key: 'versionCode',
      dataIndex: 'versionCode',
    },
    {
      title: '指标',
      dataIndex: 'value',
      key: 'value',
      render: (_, { value }) => <>{value.toFixed(3)}</>,
    },
    {
      title: '对比版本',
      key: 'h_compareVersion',
      dataIndex: 'compareValues',
      render: (_, { compareValues }) => <>{compareValues[0].versionCode}</>,
    },
    {
      title: '变化数值',
      key: 'h_diff',
      dataIndex: 'compareValues',
      render: (_, { compareValues }) => <>{format(compareValues[0].absoluteValue, 3)}</>,
    },
    {
      title: '变化幅度',
      key: 'h_amp',
      dataIndex: 'compareValues',
      render: (_, { compareValues }) => {
        // const color = performResultColor.get(PerformanceResult.optimization);
        const color = performResultColor.get(compareValues?.at(0)?.result?.result ?? PerformanceResult.stable);
        return (
          <Tag color={color}>
            <>{format(compareValues[0].percentage, 2)}%</>
          </Tag>
        );
      },
    },
    {
      title: '状态',
      key: 'status',
      dataIndex: ['compareValues', 'status'],
      render: (_, { compareValues }) => {
        // const color = performResultColor.get(PerformanceResult.optimization);
        const color = performResultColor.get(compareValues?.at(0)?.result?.result ?? PerformanceResult.stable);
        return (
          <Tag color={color}>
            <>{compareValues?.at(0)?.result?.result === PerformanceResult.deterioration ? '阻塞' : '通过'}</>
          </Tag>
        );
      },
    },
    {
      title: '操作',
      key: 'action',
      dataIndex: ['compareValues', 'metricId', 'name', 'value'],
      render: (_, { compareValues, metricId, name, value }) => (
        <Space size="middle">
          <QualityAlarmModal
            metricName={metricId}
            appId={info.businessInfo.app_id.toString()}
            platform={info.platform}
            displayName={name ?? ''}
            businessName={info.businessInfo.app_name}
            versionCode={currentVersionCode ?? ''}
            modalText={`性能指标劣化${format(compareValues[0].absoluteValue)},
					幅度超过${format(compareValues?.at(0)?.percentage)}%，发起告警`}
            warningContent={`${waringFormat(
              name,
              value,
              compareValues[0]?.versionCode,
              compareValues[0]?.absoluteValue,
              compareValues[0]?.percentage,
            )}`}
          />
          {currentVersionCode && <InviteGroupButton metricName={metricId} versionCode={currentVersionCode} />}
          <Button type="primary">豁免</Button>
          <QualityApprovalModal
            text={`性能指标劣化${format(compareValues[0].absoluteValue, 3)},
						幅度超过${format(compareValues?.at(0)?.percentage, 3)}%`}
          />
        </Space>
      ),
    },
  ];
  function isHidden(key: string, checkList: string[]) {
    return (
      (['h_diff', 'h_amp'].includes(key) && !checkList.includes('h_compareVersion')) ||
      (['t_diff', 't_amp'].includes(key) && !checkList.includes('t_compareVersion')) ||
      ['action', 'status'].includes(key)
    );
  }

  const filterKeys = ['h_compareVersion', 'owner', 'business'];
  const defaultCheckedList = filterKeys.filter(it => it !== 't_compareVersion');
  const [checkedList, setCheckedList] = useState(defaultCheckedList);
  const options = columns
    .filter(it => it.key && filterKeys.includes(it.key as string))
    .map(({ key, title }) => ({
      label: title,
      value: key,
    }));
  const newColumns = columns.map(item => ({
    ...item,
    hidden:
      (filterKeys.includes(item.key as string) && !checkedList.includes(item.key as string)) ||
      isHidden(item.key as string, checkedList),
  }));

  async function requestMetricData(_versionCode: string | undefined) {
    if (!_versionCode || !appId || !compareVersionCode) {
      return;
    }
    logger.info(
      `[requestMetricData] _versionCode => ${_versionCode} compareVersionCode => ${compareVersionCode} metricName => ${metricName}`,
    );
    // 拉取版本数据
    const metrics = await getSomePerformanceCoreData(
      appId,
      info.platform,
      filterNotEmptyStrings([_versionCode, compareVersionCode]),
      [metricName],
    );
    if (!metrics) {
      logger.error('[requestMetricData] fetch performanceData empty!');
      return;
    }
    logger.log(`[requestMetricData] metrics:${JSON.stringify(metrics)}`);
    const currentVersionData = myFilter(metrics, 'versionCode', _versionCode);
    const compareVersionData = myFilter(metrics, 'versionCode', compareVersionCode);
    logger.debug(`requestMetricData] lastSmallVersionData:${JSON.stringify(compareVersionCode)}`);

    const alarmConfigs: Map<string, PerformanceDataAlarmConfig> = build(currentVersionData, {
      amplitudeThreshold: 0,
      // differenceThreshold: 0,
    });
    // 劣化计算&判断
    // 同大版本的上一个小版本,用于计算环比
    const compareValues = calculateDifference(currentVersionData, compareVersionData, alarmConfigs);
    logger.debug(`compareVersionData:${JSON.stringify(compareVersionData)}`);
    // 转换显示数据
    const displayQualityData = transformCompareValue(currentVersionData, compareValues);
    // 显示数据
    setQualityData(displayQualityData);
    return;
  }

  useEffect(() => {
    if (currentVersionCode !== undefined) {
      // requestPerformanceData(currentVersionCode).then();
      requestMetricData(currentVersionCode).then();
    }
  }, [currentVersionCode, appId, compareVersionCode]);

  return (
    <>
      <div style={{ marginTop: 15 }}>
        {'版本：'}
        <Select
          key={`${appId}_base_select_version_name`}
          placeholder="选择当前版本"
          style={{ marginRight: 15, marginBottom: 10 }}
          value={currentVersionName}
          onChange={(value, option) => {
            setCurrentVersionName(value);
            setCurrentVersionCode(undefined);
          }}
        >
          {Object.keys(versionGroup).map(versionName => (
            <Select.Option key={`base_${versionName}`} value={versionName}>
              {versionName}
            </Select.Option>
          ))}
        </Select>
        {'版本号：'}
        <Select
          placeholder="选择当前版本"
          key={`${appId}_base_select_version_code`}
          style={{ marginRight: 15, marginBottom: 10 }}
          value={currentVersionCode}
          onChange={(value, option) => {
            setCurrentVersionCode(value);
            logger.debug(`[QualityCheckList] setCurrentVersionCode ${value}`);
          }}
        >
          {currentVersionCodes.map(it => (
            <Select.Option key={`base_${it}`} value={it}>
              {it}
            </Select.Option>
          ))}
        </Select>
        {'对比版本：'}
        <Select
          placeholder="选择对比大版本"
          key={`${appId}_compare_select_version_name`}
          value={compareVersionName}
          style={{ marginRight: 15, marginBottom: 10 }}
          onChange={(value, option) => {
            setCompareVersionName(value);
            setCompareVersionCode(undefined);
          }}
        >
          {Object.keys(versionGroup).map(versionName => (
            <Select.Option key={`compare_${versionName}`} value={versionName}>
              {versionName}
            </Select.Option>
          ))}
        </Select>
        {'对比版本号：'}
        <Select
          placeholder="选择对比小版本"
          key={`${appId}_compare_select_version_code`}
          style={{ marginRight: 15, marginBottom: 10 }}
          value={compareVersionCode}
          onChange={(value, _) => {
            setCompareVersionCode(value);
          }}
        >
          {compareVersionCodes
            .filter(v => v !== currentVersionCode)
            .map(it => (
              <Select.Option key={`compare_${it}`} value={it}>
                {it}
              </Select.Option>
            ))}
        </Select>
        <Checkbox.Group
          value={checkedList}
          options={options as CheckboxOptionType[]}
          onChange={value => {
            setCheckedList(value as string[]);
          }}
        />
      </div>
      <Table columns={newColumns} dataSource={qualityData} pagination={false} />
      <Divider />
    </>
  );
};

export default MetricsCoreVersionCheckList;
