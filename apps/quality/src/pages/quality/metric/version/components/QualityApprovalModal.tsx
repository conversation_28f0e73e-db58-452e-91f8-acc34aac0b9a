import React, { useState } from 'react';
import { But<PERSON>, Divider, Modal } from 'antd';
import TextArea from 'antd/es/input/TextArea';

export interface QualityApprovalModalText {
  text: string;
}

const QualityApprovalModal: React.FC<QualityApprovalModalText> = ({ text }) => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [reason, setReasonText] = useState('');
  const showModal = () => {
    setIsModalOpen(true);
  };

  const handleOk = () => {
    setIsModalOpen(false);
  };

  const handleCancel = () => {
    setIsModalOpen(false);
  };

  return (
    <>
      <Button type="primary" danger onClick={showModal}>
        申请豁免
      </Button>
      <Modal
        title="发起指标豁免申请流程"
        open={isModalOpen}
        onOk={handleOk}
        onCancel={handleCancel}
        okText={<>发起申请</>}
        cancelText={<>取消</>}
      >
        <Divider />
        <p>{`${text}\n目前存在阻塞版本，请判断指标劣化是否正常，填写豁免的理由，由指标负责人或者BM审批通过后准出。`}</p>
        <TextArea
          onChange={e => setReasonText(e.target.value)}
          autoSize={{ minRows: 3, maxRows: 5 }}
          placeholder="请输入申请理由"
        />
      </Modal>
    </>
  );
};

export default QualityApprovalModal;
