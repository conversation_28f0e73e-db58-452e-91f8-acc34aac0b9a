import React, { useEffect, useRef, useState } from 'react';
import { Tabs } from 'antd';
import type { TabsProps } from 'antd';
import QualityCheckList from '@/pages/quality/metric/version/components/QualityCheckList';
import QualityAlarmOrderRecordList from '@/pages/quality/metric/version/components/order/QualityAlarmOrderRecordList';
import QualityAlarmConfig from '@/pages/quality/metric/version/components/rules/QualityAlarmConfig';
import AlarmCoreConfig from '@/pages/quality/metric/version/components/config/AlarmCoreConfig';
import MetricsCoreCheckList from '@/pages/quality/metric/version/components/MetricsCoreCheckList';
import QualitySingleCheckList from '@/pages/quality/metric/version/components/QualitySingleCheckList';
import { useSearchParams } from '@edenx/runtime/router';
import { logger } from '@/pages/quality/metric/version/utils/Logger';
import { toNumber } from 'lodash';
import { useModel } from '@edenx/runtime/model';
import AppSettingModule from '@/model/appSettingModel';
import { getCurrentGrayVersion } from '@api/metricsAlarmSystem';

export enum QualityCheckListPanelSearchParams {
  TAB_TYPE = 'tab_type',
  METRIC_ID = 'metric_Id',
  START_TIME = 'start_time',
  END_TIME = 'end_time',
  APP_ID = 'appid',
}

const QualityCheckListPanel: React.FC = () => {
  const [searchParams, setSearchParams] = useSearchParams();
  const metricId = searchParams.get(QualityCheckListPanelSearchParams.METRIC_ID);
  const startTime = searchParams.get(QualityCheckListPanelSearchParams.START_TIME);
  const endTime = searchParams.get(QualityCheckListPanelSearchParams.END_TIME);
  const [{ info }] = useModel(AppSettingModule);
  const appId = info.businessInfo?.app_id?.toString();
  const [versionCode, setVersionCode] = useState<string>();

  useEffect(() => {
    getCurrentGrayVersion({
      data: {
        appId,
        platform: info.businessInfo?.platform,
      },
    }).then(r => {
      if (r) {
        setVersionCode(r.toString());
      }
    });
  }, [appId, info.businessInfo?.platform]);

  const items: TabsProps['items'] = [
    {
      key: 'core_check_list',
      label: '核心指标',
      children: <MetricsCoreCheckList versionCode={versionCode} />,
    },
    {
      key: 'version_check_list',
      label: '版本看板',
      children: <QualityCheckList />,
    },
    {
      key: 'single_check_list',
      label: '指标看板',
      children: (
        <QualitySingleCheckList
          key={`${appId}-QualitySingleCheckList`}
          metricId={metricId ?? undefined}
          inStartTime={toNumber(startTime)}
          inEndTime={toNumber(endTime)}
          onChange={(_metricId, _startTime, _endTime) => {
            if (_metricId) {
              searchParams.set(QualityCheckListPanelSearchParams.METRIC_ID, _metricId);
            }
            if (_startTime) {
              searchParams.set(QualityCheckListPanelSearchParams.START_TIME, _startTime.toString());
            }
            if (_endTime) {
              searchParams.set(QualityCheckListPanelSearchParams.END_TIME, _endTime.toString());
            }
            setSearchParams(searchParams);
          }}
        />
      ),
    },
    {
      key: 'alarm_core_config',
      label: '核心指标配置',
      children: <AlarmCoreConfig />,
    },
    {
      key: 'alarm_rule_config',
      label: '告警规则配置',
      children: <QualityAlarmConfig />,
    },
    {
      key: 'alarm_order_list',
      label: '工单列表',
      children: <QualityAlarmOrderRecordList />,
    },
  ];

  return (
    <>
      <Tabs
        defaultActiveKey={searchParams.get(QualityCheckListPanelSearchParams.TAB_TYPE) ?? 'core_check_list'}
        items={items}
        onChange={(key: string) => {
          logger.debug(key);
          searchParams.set(QualityCheckListPanelSearchParams.TAB_TYPE, key);
          setSearchParams(searchParams);
        }}
      />
    </>
  );
};

export default QualityCheckListPanel;
