import React, { useRef, useState } from 'react';
import { message } from 'antd';
import UserCard from '@/component/UserCard';
import { ActionType, ProColumns, ProTable } from '@ant-design/pro-components';
import { compact } from 'lodash';
// import { fetchSceneList, SceneOwnerListRecordData } from "./SceneOwnerData";
import { SceneOwnerConfigDetail } from 'api/model/SceneOwnerConfigTable';
import { fetchSceneList, updateSceneOwner } from '@api/slardar';

export interface SceneOwnerListRecordData {
  scene: string; // 场景vc名称
  platform: string; // 平台
  owner: string; // 负责人
  appid: number; // appid
}

export const SceneOwnerPage: React.FC = () => {
  // const [appSettingState] = useModel(AppSettingModule);
  // const [userSettingState] = useModel(UserSettingModule);
  const actionRef = useRef<ActionType>();
  const [editableKeys, setEditableRowKeys] = useState<React.Key[]>([]);
  const [onlyChange, setOnlyChange] = useState(true);
  const [pageSize, setPageSize] = useState(40);
  const [sceneList, setSceneList] = useState<SceneOwnerConfigDetail[]>([]);

  const columns: ProColumns<SceneOwnerListRecordData>[] = [
    {
      title: 'Scene',
      dataIndex: 'scene',
      key: 'scene',
      editable: (text, record, index) => false,
      width: 300,
      fixed: 'left',
      ellipsis: false,
    },
    {
      title: 'Owner',
      dataIndex: 'owner',
      key: 'owner',
      editable: (text, record, index) => true,
      width: 70,
      ellipsis: true,
      render: (val, row) => {
        if (row.owner !== undefined && row.owner.length > 0) {
          return (
            <UserCard
              email={`${row.owner}@bytedance.com`}
              hideAvatar={false}
              simpleUserData={{
                name: row.owner,
              }}
              triggerType="hover"
            />
          );
        }
      },
    },
    {
      title: '系统',
      dataIndex: 'platform',
      width: 50,
      align: 'center',
      editable: (text, record, index) => false,
      hideInSearch: true,
      filters: [...new Set(compact(sceneList.map(v => v.platform)))].map(v => ({ text: v, value: v })),
      onFilter: (value, record) => value === record.platform,
    },
    {
      title: '应用名称',
      dataIndex: 'appid',
      width: 50,
      align: 'center',
      hideInSearch: true,
      editable: (text, record, index) => false,
      hideInTable: !onlyChange,
      filters: [...new Set(compact(sceneList.map(v => v.appid)))].map(v => ({ text: v, value: v })),
      onFilter: (value, record) => value === record.appid,
    },
    {
      title: '操作',
      valueType: 'option',
      align: 'left',
      width: '10%',
      render: (z, record, _, action) => [
        <a
          key="editable"
          onClick={() => {
            action?.startEditable?.(record.scene);
          }}
        >
          更改owner
        </a>,
      ],
    },
  ];

  return (
    <>
      <ProTable<SceneOwnerListRecordData>
        columns={columns}
        actionRef={actionRef}
        editable={{
          type: 'single',
          editableKeys,
          actionRender: (row, _, defaultDoms) => [defaultDoms.save, defaultDoms.cancel],
          onSave: async (_, record, origin) => {
            console.log(record);
            const result = await updateSceneOwner({
              data: {
                aid: record.appid,
                scene: record.scene,
                platform: record.platform,
                owner: record.owner,
              },
            });
            if (result.success === false) {
              record.owner = origin.owner;
              await message.success(result.errormsg);
            } else {
              await message.success(`更新成功`);
            }
          },
          onChange: setEditableRowKeys,
        }}
        cardBordered
        params={{
          onlyChange,
        }}
        request={async (params, sort, filter) => {
          const res = await fetchSceneList({
            data: {
              scene: params.scene !== undefined ? params.scene : '',
              owner: params.owner !== undefined ? params.owner : '',
            },
          });
          setSceneList(res.data);
          return res;
        }}
        columnsState={{
          persistenceKey: 'pro-table-singe-demos',
          persistenceType: 'localStorage',
          defaultValue: {
            option: { fixed: 'right', disable: true },
          },
          onChange(value) {
            console.log('value: ', value);
          },
        }}
        rowKey="scene"
        search={{
          filterType: 'query',
          labelWidth: 'auto',
          collapsed: false,
          span: 8,
        }}
        options={false}
        pagination={{
          pageSize,
          pageSizeOptions: [10, 20, 40, 60, 80, 100, 500],
          showSizeChanger: true,
          onShowSizeChange: (current, size) => {
            setPageSize(size);
          },
        }}
        scroll={{
          y: '100%',
          scrollToFirstRowOnChange: true,
        }}
      />
    </>
  );
};
