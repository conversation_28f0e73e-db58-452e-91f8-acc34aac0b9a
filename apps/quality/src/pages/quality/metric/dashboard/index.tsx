import React, { useEffect, useState } from 'react';
import { DBTeaMetric } from '../../../../../api/model/TeaMetricTable';
import { getTeaSerialMetric } from '@api/tea';
import { Col, Collapse, CollapseProps, Row, Skeleton } from 'antd';
import { groupBy, toPairs } from 'lodash';
import { get_enum_value, get_enum_values, to_platform } from '@shared/utils/tools';
import { Business, BusinessName } from '@shared/typings/tea/metric';
import TeaOverallChart from '@/component/Charts/TeaOverallChart';
import { useModel } from '@edenx/runtime/model';
import AppSettingModule from '@/model/appSettingModel';
import dayjs from 'dayjs';
import { SubtitleText, TitleContainer, TitleText } from '@/pages/quality/metric/shared';

const TeaDashboard: React.FC = () => {
  const [loading, setLoading] = useState(true);
  const [metrics, setMetrics] = useState<DBTeaMetric[]>([]);

  const [{ info }] = useModel(AppSettingModule);

  const update = async () => {
    setLoading(true);
    await getTeaSerialMetric({
      data: {
        platform: to_platform(info.platform),
      },
    }).then(setMetrics);
    setLoading(false);
  };

  useEffect(() => {
    update();
  }, [info.platform]);

  const items: CollapseProps['items'] = toPairs(groupBy(metrics, it => it.Business)).map(([k, v]) => ({
    key: get_enum_value(Business, k),
    label: BusinessName[get_enum_value(Business, k)],
    children: (
      <Row gutter={[16, 24]}>
        {v.map(it => (
          <Col span={24} key={it.Name}>
            <TeaOverallChart teaMetric={it} />
          </Col>
        ))}
      </Row>
    ),
  }));

  const now = dayjs();
  const end_ts = now.subtract(1, 'd').format('YYYY-MM-DD');
  const start_ts = now.subtract(91, 'd').format('YYYY-MM-DD');

  return (
    <>
      <TitleContainer>
        <TitleText>性能品质大盘</TitleText>
        <SubtitleText>
          时间区间: {start_ts} ~ {end_ts} | 平台: {info.name} {info.platform}
        </SubtitleText>
      </TitleContainer>
      {loading ? (
        <Skeleton style={{ width: '100%', height: '100%' }} active />
      ) : (
        <Collapse style={{ width: '100%' }} items={items} defaultActiveKey={get_enum_values(Business)} />
      )}
    </>
  );
};

export default TeaDashboard;
