import React, { useEffect, useState } from 'react';
import { CrashListInfoAndReason, queryVersionDegradationIssues } from '@api/slardar';
import { Line } from '@ant-design/charts';
import { ProCard } from '@ant-design/pro-components';
import { Space, Table, TableProps, Tag } from 'antd';
import { useModel } from '@edenx/runtime/model';
import AppSettingModule from '@/model/appSettingModel';
import Link from 'antd/es/typography/Link';
import UserMessage from '@/component/UserMessage';
import { SlardarPlatformType } from '@pa/shared/dist/src/appSettings/appSettings';
import Paragraph from 'antd/lib/typography/Paragraph';
import { PlatformType } from '@pa/shared/dist/src/core';

export interface ChartData {
  dataX: string;
  dataY: number;
  category: string;
}

export const Degradation: React.FC = () => {
  const [appSettingState] = useModel(AppSettingModule);
  const [chartLoading, setChartLoading] = useState<boolean>(true);
  const [chartData, setChartData] = useState<ChartData[]>([]);
  const [versionIssuesMap, setVersionIssuesMap] = useState<Map<string, CrashListInfoAndReason[]>>(new Map());
  const [version, setVersion] = useState<string>('');
  const [issues, setIssues] = useState<CrashListInfoAndReason[] | undefined>([]);

  useEffect(() => {
    queryVersionDegradationIssues({
      data: {
        product: appSettingState.info.name,
      },
    }).then(value => {
      const data: ChartData[] = [];
      const issuesMap = new Map<string, CrashListInfoAndReason[]>();
      for (const versionDegradation of value) {
        data.push({
          dataX: versionDegradation.version,
          dataY: versionDegradation.javaDegradationIssues.length,
          category: 'java',
        });
        data.push({
          dataX: versionDegradation.version,
          dataY: versionDegradation.nativeDegradationIssues.length,
          category: 'native',
        });
        issuesMap.set(versionDegradation.version, versionDegradation.issueInfos);
      }
      setChartData(data);
      setChartLoading(false);
      setVersionIssuesMap(issuesMap);
      if (value.length > 0) {
        setVersion(value[value.length - 1].version);
      }
    });
  }, [appSettingState.info.name, appSettingState.info.platform]);

  useEffect(() => {
    const versionIssues = versionIssuesMap.get(version);
    setIssues(versionIssues);
  }, [version]);

  const config = {
    data: chartData,
    xField: 'dataX',
    yField: 'dataY',
    seriesField: 'category',
    loading: chartLoading,
  };

  const columns: TableProps<CrashListInfoAndReason>['columns'] = [
    {
      title: 'Issue详情',
      dataIndex: 'crash_file',
      render: (_, row) => (
        <>
          <Link href={row.slardar_url ? row.slardar_url : undefined} style={{ color: 'blue' }} target={'_blank'}>
            {row.crash_file ? row.crash_file : row.issue_id}
          </Link>
          <Paragraph copyable ellipsis={true} style={{ maxWidth: '80%', marginBottom: '0px' }}>
            {row.crash_line_number ? `line ${row.crash_line_number}` : 'No Lines!'}
          </Paragraph>
          <Paragraph copyable ellipsis={true} style={{ maxWidth: '80%', marginBottom: '0px' }}>
            {row.crash_clazz}
          </Paragraph>
          <Paragraph copyable ellipsis={true} style={{ maxWidth: '80%', marginBottom: '0px' }}>
            {row.crash_reason}
          </Paragraph>
        </>
      ),
    },
    {
      title: '处理人',
      dataIndex: 'managers',
      render: (_, row) => <UserMessage emails={row.managers ? row.managers : []} />,
    },
    {
      title: 'slardar状态',
      dataIndex: 'status',
      render: (_, row) => {
        if (row.status === 'new_created') {
          return <Tag color="yellow">新创建 </Tag>;
        } else if (row.status === 'close') {
          return <Tag color="blue">已关闭 </Tag>;
        } else if (row.status === 'be_processed_again') {
          return <Tag color="pink">重新打开 </Tag>;
        } else if (row.status === 'doing') {
          return <Tag color="purple">跟进中 </Tag>;
        } else if (row.status === 'long') {
          return <Tag color="green">长期跟进 </Tag>;
        } else if (row.status === 'done') {
          return <Tag color="purple">已处理 </Tag>;
        } else if (row.status === 'unassigned') {
          return <Tag color="red">未处理 </Tag>;
        }
      },
    },
    {
      title: '次数',
      dataIndex: 'count',
      render: (_, row) => {
        if (row.platform === SlardarPlatformType.iOS) {
          return (
            <Space direction={'vertical'}>
              <>{row.count}</>
            </Space>
          );
        } else {
          return <>{row.count}</>;
        }
      },
    },

    {
      title: '原因',
      dataIndex: 'reason',
      render: (_, row) => <>{row.reason}</>,
    },
  ];

  if (appSettingState.info.platform === PlatformType.iOS) {
    return <></>;
  }

  return (
    <div style={{ aspectRatio: 13 / 7, height: '100%', width: '100%' }}>
      <ProCard bordered={true} headerBordered={true} title={<div>{'版本逃逸'}</div>} size={'small'}>
        <Line
          {...config}
          onReady={chart => {
            chart.on('plot:click', (evt: any) => {
              const { x, y } = evt;
              const tooltip = chart.chart.getTooltipItems({ x, y });
              setVersion(tooltip[0].data.dataX);
            });
          }}
        />
      </ProCard>
      <Table columns={columns} dataSource={issues} />
    </div>
  );
};
