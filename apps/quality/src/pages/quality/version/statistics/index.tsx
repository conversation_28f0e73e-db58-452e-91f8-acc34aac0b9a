import { useModel } from '@edenx/runtime/model';
import AppSettingModule from '@/model/appSettingModel';
import React, { useEffect, useState } from 'react';
import { queryCrashRateMetric, queryHistoryCrashList } from '@api/slardar';
import { SlardarPlatformType } from '@pa/shared/dist/src/appSettings/appSettings';
import { ProCard } from '@ant-design/pro-components';
import { Line } from '@ant-design/charts';
import { Button, DatePicker, Select, Space, Table, TableProps, Tag } from 'antd';
import { CrashHistoryListInfo, CrashName, CrashType } from '@shared/typings/slardar/crash/issueListSearch';
import Link from 'antd/es/typography/Link';
import UserMessage from '@/component/UserMessage';
import { CrashType2Url, getSlardarIssueParams, getSlardarUrl } from '@shared/typings/slardar/common';
import { AxisCfg } from '@antv/g2/lib/interface';
import dayjs from 'dayjs';
import { PlatformType } from '@pa/shared/dist/src/core';

export interface ChartData {
  dataX: string;
  dataY: number;
  category: string;
}

function getPreviousFridayDate(): string {
  const today = new Date();
  const dayOfWeek = today.getDay();
  const daysToSubtract = dayOfWeek <= 4 ? 3 + dayOfWeek : dayOfWeek - 4; // 计算需要减去的天数

  const previousFriday = new Date(today);
  previousFriday.setDate(today.getDate() - daysToSubtract);

  return `${previousFriday.getFullYear()}.${(previousFriday.getMonth() + 1)
    .toString()
    .padStart(2, '0')}.${previousFriday.getDate().toString().padStart(2, '0')}`;
}

const SlardarStatistic: React.FC = () => {
  const [appSettingState] = useModel(AppSettingModule);
  const [data, setData] = useState<any>({});
  const [chartLoading, setChartLoading] = useState<boolean>(true);
  const [chartData, setChartData] = useState<ChartData[]>([]);
  const [timestamp, setTimestamp] = useState<number>(0);
  const [issueIds, setIssueIds] = useState<string[]>([]);
  const [versions, setVersions] = useState<string[]>([]);
  const [list, setList] = useState<CrashHistoryListInfo[]>([]);
  const [timeOptions, setTimeOptions] = useState<
    {
      label: string;
      value: string;
    }[]
  >([]);
  const [onSelected, setOnSelected] = useState<{
    crashType: string;
    time: string;
  }>({ crashType: CrashType.JavaCrash, time: getPreviousFridayDate() });
  const [tableLoading, setTableLoading] = useState<boolean>(false);
  const [startTime, setStartTime] = useState<number>(0);
  const [endTime, setEndTime] = useState<number>(Math.floor(Date.now() / 1000));
  useEffect(() => {
    if (onSelected && data && onSelected.crashType in data) {
      for (const d of data[onSelected.crashType]) {
        if (d.date === onSelected.time) {
          setTimestamp(d.Timestamp);
          setVersions(d.top_versions);
          setTableLoading(true);
          queryHistoryCrashList({
            data: {
              aid: appSettingState.info.businessInfo.aid,
              issueIds: d.issue_ids,
              crashType: onSelected.crashType as CrashType,
              platform:
                appSettingState.info.platform === PlatformType.Android
                  ? SlardarPlatformType.Android
                  : SlardarPlatformType.iOS,
              date: onSelected.time,
              timeStamp: d.Timestamp,
            },
          }).then(v => {
            setList(v.data);
            setTableLoading(false);
          });
        }
      }
    }
  }, [onSelected, data]);
  const columns: TableProps<CrashHistoryListInfo>['columns'] = [
    {
      title: 'Issue详情',
      dataIndex: 'crash_file',
      width: '15%',
      fixed: 'left',
      render: (_, row) => (
        <>
          <Link href={row?.slardar_url ? row.slardar_url : undefined} style={{ color: 'blue' }} target={'_blank'}>
            {row.crash_file}
          </Link>
          <br />
          <>
            {(onSelected.crashType as CrashType) !== CrashType.NativeCrash &&
            (onSelected.crashType as CrashType) !== CrashType.NativeOOM &&
            (onSelected.crashType as CrashType) !== CrashType.OOMCrash &&
            row.crash_line_number
              ? `line ${row.crash_line_number}`
              : ''}
          </>
          <br />
          <>{row.crash_clazz}</>
          <br />
          <>{row.crash_reason.substring(0, 200)}</>
        </>
      ),
    },
    {
      title: '处理人',
      dataIndex: 'managers',
      fixed: 'left',
      width: '10%',
      render: (_, row) => <UserMessage emails={row.managers ? row.managers : []} />,
    },
    {
      title: '分布详情',
      width: '8%',
      fixed: 'left',
      align: 'center',
      dataIndex: 'start_os_version',
      render: (_, row) => (
        <>
          {row.start_os_version} - {row.end_os_version}{' '}
        </>
      ),
    },
    {
      title: '本周用户数',
      width: '8%',
      fixed: 'left',
      align: 'center',
      dataIndex: 'user',
      render: (_, row) => (
        <Space direction={'vertical'}>
          {row.user}
          <Tag color={'green'}>Top{row.ranking}</Tag>
          <Tag color={'yellow'}>平均影响用户比例{(row.rate * 1000).toFixed(4)}‰</Tag>
        </Space>
      ),
    },
    {
      title: '上周用户数',
      width: '8%',
      fixed: 'left',
      align: 'center',
      dataIndex: 'last_user',
      render: (_, row) => (
        <Space direction={'vertical'}>
          {row.last_user}
          <Tag color={'green'}>Top{row.last_ranking}</Tag>
          <Tag color={'yellow'}>平均影响用户比例{(row.last_rate * 1000).toFixed(4)}‰</Tag>
        </Space>
      ),
    },
    {
      title: '平均影响用户比例对比',
      width: '8%',
      fixed: 'left',
      align: 'center',
      dataIndex: 'last_user',
      render: (_, row) => (
        <Space direction={'vertical'}>
          <Tag color={'green'}>劣化值{((row.rate - row.last_rate) * 1000).toFixed(4)}‰</Tag>
          <Tag color={'yellow'}>劣化率{(((row.rate - row.last_rate) / row.last_rate) * 100).toFixed(4)}%</Tag>
          <Tag color={'blue'}>
            Top{row.last_ranking}-{'>'}Top{row.ranking}
          </Tag>
        </Space>
      ),
    },
  ];
  const config = {
    data: chartData,
    xField: 'dataX',
    yField: 'dataY',
    seriesField: 'category',
    loading: chartLoading,
    yAxis: {
      label: {
        formatter: (v: number) => `${v}‰`,
      },
    } as unknown as AxisCfg,
    tooltip: {
      formatter: (datum: any) => ({ name: datum.category, value: `${datum.dataY.toFixed(4)}‰` }),
    },
  };
  useEffect(() => {
    const temp: ChartData[] = [];
    const total: {
      [date: string]: number;
    } = {};
    for (const crashType in data) {
      for (const d of data[crashType]) {
        temp.push({
          category: crashType,
          dataX: d.date,
          dataY: d.Value * 1000,
        });
        if (!(d.date in total)) {
          total[d.date] = 0;
        }
        total[d.date] += d.Value * 1000;
      }
    }
    const reserve: ChartData[] = [];
    for (const date in total) {
      reserve.push({
        category: 'total',
        dataX: date,
        dataY: total[date],
      });
    }
    temp.unshift(...reserve);
    setChartData(temp);
    setChartLoading(false);
  }, [data]);
  useEffect(() => {
    queryCrashRateMetric({
      data: {
        // appSettingState.info.name === "剪映" ? versionState.info.lvVersion : versionState.info.ccVersion,
        platform:
          appSettingState.info.platform === PlatformType.Android
            ? SlardarPlatformType.Android
            : SlardarPlatformType.iOS,
        start_time: startTime,
        end_time: endTime,
      },
    }).then((res: any) => {
      setData(res.data);
      const dates: string[] = [];
      console.log(res.data);
      for (const d in res.data) {
        for (const i of res.data[d]) {
          dates.push(i.date);
        }
      }
      console.log(dates);
      setTimeOptions(
        Array.from(new Set(dates))
          .reverse()
          .map(v => ({ label: v, value: v })),
      );
      console.log(timeOptions);
    });
  }, [appSettingState.info.platform, startTime, endTime]);
  return (
    <Space direction={'vertical'} style={{ width: '100%' }} size={'large'}>
      <ProCard
        bordered={true}
        headerBordered={true}
        extra={
          <Space>
            <>查询日期</>
            <DatePicker
              key="start_time"
              showTime
              format="YYYY-MM-DD"
              onChange={(date, dateString) => {
                if (date) {
                  setStartTime(Math.floor(date.valueOf() / 1000));
                } else {
                  setStartTime(0);
                }
              }}
            />
            <>-</>
            <DatePicker
              key="end_time"
              showTime
              format="YYYY-MM-DD"
              onChange={(date, dateString) => {
                if (date) {
                  setEndTime(Math.floor(date.valueOf() / 1000));
                } else {
                  setEndTime(Math.floor(dayjs().valueOf() / 1000));
                }
              }}
            />
          </Space>
        }
        title={
          <Space>
            <div>{'周Top3版本Top50历史问题劣化'}</div>
          </Space>
        }
        size={'small'}
        style={{ width: '100%' }}
      >
        <Line
          {...config}
          // onPlotClick={(v: any) => console.log("点击", v)}
        />
      </ProCard>
      <Space direction={'horizontal'}>
        <Button
          // id={`StabilityIssueList_${timestamp - 7 * 24 * 3600}_${timestamp}_${appSettingState.info.id}`}
          key={appSettingState.info.id}
          href={getSlardarUrl(
            appSettingState.info.businessInfo.aid,
            CrashType2Url[onSelected.crashType],
            appSettingState.info.platform.toString(),
            (onSelected.crashType as CrashType) === CrashType.JavaMemLeak ? `perf_v2/memory/activity` : `abnormal_list`,
            getSlardarIssueParams(
              timestamp - 7 * 24 * 3600,
              timestamp,
              'DAU TOP3 version',
              CrashType2Url[onSelected.crashType],
              appSettingState.info.platform === PlatformType.iOS ? 'iOS' : 'Android',
              Math.floor(appSettingState.info.id / 100) === 1775,
              [CrashType.OOMCrash, CrashType.JavaOOM, CrashType.NativeOOM].includes(onSelected.crashType as CrashType),
              onSelected.crashType as CrashType,
              undefined,
              undefined,
              86400,
            ),
          )}
          target={'_blank'}
          color={'blue'}
        >
          跳转 Slardar
        </Button>
        <>
          崩溃类型:
          <Select
            style={{ width: 150 }}
            value={{
              label: CrashName[onSelected.crashType as keyof typeof CrashName],
              value: onSelected.crashType,
            }}
            onSelect={v => setOnSelected({ crashType: v.value, time: onSelected.time })}
            labelInValue={true}
            options={[CrashType.JavaCrash, CrashType.JavaStartCrash, CrashType.ANR, CrashType.NativeCrash].map(v => ({
              label: CrashName[v.toString() as keyof typeof CrashName],
              value: v,
            }))}
          />
        </>
        <>
          查询日期:
          <Select
            style={{ width: '100%' }}
            value={onSelected.time}
            onSelect={v => setOnSelected({ crashType: onSelected.crashType, time: v })}
            options={timeOptions}
          />
        </>
        <>
          DAU Top3 版本: <Tag>{versions.join(',')}</Tag>
        </>
      </Space>
      <Table
        style={{ width: '100%' }}
        columns={columns}
        dataSource={list}
        pagination={{ pageSize: 20 }}
        loading={tableLoading}
      />
    </Space>
  );
};

export default SlardarStatistic;
