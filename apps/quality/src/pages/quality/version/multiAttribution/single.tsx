import React, { useEffect, useState } from 'react';
import { Col, Input, Row, Space, Spin, TabPane, Tabs } from '@douyinfe/semi-ui';
import DimensionResult, {
  MultiAttributionDimensionResultProps,
} from '@/pages/quality/version/multiAttribution/dimension';
import { getDimensionsDataNew, GetDimensionsDataResult } from '@/pages/tools/multiAttribution/Dimensions';
import { MultiAttributionType } from '@shared/multiAttribution/CommonModel';
import { useSearchParams } from '@edenx/runtime/router';
import { MultiAttributionTypeView } from '@/component/MultiAttribution/type';
import { MultiAttributionURLQuery, MultiAttributionURLQueryKey } from '@shared/multiAttribution/urlQuery';
import { QualityToolsButton, ToolsName } from '@shared/utils/QualityTools';

export interface SingleAttributionProps {
  type: MultiAttributionType;
  baseSlardarUrl?: string;
  targetSlardarUrl?: string;
}

/**
 * 单issue归因
 * @constructor
 */
const SingleAttribution: React.FC<SingleAttributionProps> = (props: SingleAttributionProps) => {
  const [searchParams, setSearchParams] = useSearchParams();
  const [multiAttributionType, setMultiAttributionType] = useState(props.type);
  const [baseSlardarUrlValue, setBaseSlardarUrlValueValue] = useState(props.baseSlardarUrl ?? '');
  const [targetSlardarUrlValue, setTargetSlardarUrlValue] = useState(props.targetSlardarUrl ?? '');
  const [loading, setLoading] = useState(false);

  const [dimensionResult, setDimensionResult] = useState<MultiAttributionDimensionResultProps>();
  const [compareGetDimensionsDataResult, setCompareGetDimensionsDataResult] = useState<GetDimensionsDataResult>();
  const [singleGetDimensionsDataResult, setSingleGetDimensionsDataResult] = useState<GetDimensionsDataResult>();

  useEffect(() => {
    if (multiAttributionType === MultiAttributionType.COMPARE) {
      if (
        compareGetDimensionsDataResult?.baseUrl === baseSlardarUrlValue &&
        compareGetDimensionsDataResult?.targetUrl === targetSlardarUrlValue
      ) {
        setDimensionResult({
          code: compareGetDimensionsDataResult.error(),
          message: compareGetDimensionsDataResult.message(),
          data: compareGetDimensionsDataResult.data,
        });
      } else {
        setDimensionResult({});
      }
    } else if (multiAttributionType === MultiAttributionType.DIMENSION) {
      if (singleGetDimensionsDataResult?.baseUrl === baseSlardarUrlValue) {
        setDimensionResult({
          code: singleGetDimensionsDataResult.error(),
          message: singleGetDimensionsDataResult.message(),
          data: singleGetDimensionsDataResult.data,
        });
      } else {
        setDimensionResult({});
      }
    }
  }, [multiAttributionType]);

  const onClickStartAnalysis = async () => {
    setLoading(true);
    if (multiAttributionType === MultiAttributionType.COMPARE) {
      if (
        compareGetDimensionsDataResult?.baseUrl !== baseSlardarUrlValue ||
        compareGetDimensionsDataResult?.targetUrl !== targetSlardarUrlValue
      ) {
        const result = await getDimensionsDataNew(baseSlardarUrlValue, targetSlardarUrlValue);
        if (result.isSuccess()) {
          setCompareGetDimensionsDataResult(result);
        } else {
          setCompareGetDimensionsDataResult(undefined);
        }
        setDimensionResult({
          code: result.error(),
          message: result.message(),
          data: result.data,
        });
      }
    } else if (multiAttributionType === MultiAttributionType.DIMENSION) {
      if (singleGetDimensionsDataResult?.baseUrl !== baseSlardarUrlValue) {
        const result = await getDimensionsDataNew(baseSlardarUrlValue);
        if (result.isSuccess()) {
          setSingleGetDimensionsDataResult(result);
        } else {
          setSingleGetDimensionsDataResult(undefined);
        }
        setDimensionResult({
          code: result.error(),
          message: result.message(),
          data: result.data,
        });
      }
    }
    setLoading(false);
  };

  // useEffect(() => {
  // 	if (baseSlardarUrlValue && targetSlardarUrlValue) {
  // 		onClickStartAnalysis().then();
  // 	}
  // }, []);

  const analysisResultTabs = [];
  analysisResultTabs.push({
    title: '其它维度',
    itemKey: '2',
    content: loading ? (
      <div style={{ padding: 10 }}>
        <Spin />
      </div>
    ) : (
      <DimensionResult code={dimensionResult?.code} message={dimensionResult?.message} data={dimensionResult?.data} />
    ),
  });

  const analysisResult = (
    <Space vertical align="start">
      分析结果：
      <Tabs type="card">
        {analysisResultTabs.map(tab => (
          // eslint-disable-next-line react/jsx-key
          <TabPane tab={tab.title} itemKey={tab.itemKey}>
            {tab.content}
          </TabPane>
        ))}
      </Tabs>
    </Space>
  );

  return (
    <div style={{ width: '100%', paddingTop: 10 }}>
      <div className="grid">
        <Row type="flex" justify="start" align="middle" gutter={[8, 8]} style={{ paddingBottom: 10 }}>
          <Col span={24}>
            <MultiAttributionTypeView
              disabled={loading}
              multiAttributionType={multiAttributionType}
              setMultiAttributionType={setMultiAttributionType}
            />
          </Col>
          <Col span={8}>
            <Input
              showClear
              placeholder={multiAttributionType === 1 ? '输入基准Slardar链接' : '输入Slardar链接'}
              value={baseSlardarUrlValue}
              onChange={value => {
                setBaseSlardarUrlValueValue(value);
                searchParams.set(MultiAttributionURLQueryKey.BASE_SLARDAR_URL, value);
                setSearchParams(searchParams);
              }}
            />
          </Col>
          <Col span={multiAttributionType === 1 ? 8 : 0}>
            <Input
              showClear
              placeholder="输入目标Slardar链接"
              value={targetSlardarUrlValue}
              onChange={value => {
                setTargetSlardarUrlValue(value);
                searchParams.set(MultiAttributionURLQueryKey.TARGET_SLARDAR_URL, value);
                setSearchParams(searchParams);
              }}
            />
          </Col>
          <Col span={8}>
            <QualityToolsButton
              theme="solid"
              toolName={ToolsName.SingleMultiAttribution}
              title={'开始分析'}
              type="primary"
              size="default"
              onClick={onClickStartAnalysis}
            />
          </Col>
        </Row>
        {analysisResult}
      </div>
    </div>
  );
};
export default SingleAttribution;

export function buildSingleAttributionProps(urlQuery: MultiAttributionURLQuery): SingleAttributionProps {
  return {
    type: urlQuery[MultiAttributionURLQueryKey.TYPE],
    baseSlardarUrl: urlQuery[MultiAttributionURLQueryKey.BASE_SLARDAR_URL],
    targetSlardarUrl: urlQuery[MultiAttributionURLQueryKey.TARGET_SLARDAR_URL],
  };
}
