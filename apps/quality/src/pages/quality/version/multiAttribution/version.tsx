import React, { useState } from 'react';
import { Button, Col, Row, Space, Tag, Typography } from '@douyinfe/semi-ui';
import { MultiAttributionDimensionResultProps } from '@/pages/quality/version/multiAttribution/dimension';
import { useModel } from '@edenx/runtime/model';
import AppSettingModule from '@/model/appSettingModel';
import { getDimensionsDataNew } from '@/pages/tools/multiAttribution/Dimensions';
import { MultiAttributionIssueListSortType, MultiAttributionType } from '@shared/multiAttribution/CommonModel';
import {
  MarketTools,
  MultiAttributionComparedIssueListData,
  MultiAttributionResponse,
} from '@shared/multiAttribution/market';
import MultiAttributionResult, {
  LoadResult,
  MultiAttributionResultTab,
} from '@/pages/quality/version/multiAttribution/result';
import { CrashType } from '@shared/typings/slardar/crash/issueListSearch';
import { PlatformType } from '@pa/shared/dist/src/core';
import { CrashType2Url, getSlardarIssueParams, getSlardarUrl } from '@shared/typings/slardar/common';
import { startCompareIssueListAnalysis } from '@api/MultiAttribution';
import { MultiAttributionURLQuery, MultiAttributionURLQueryKey } from '@shared/multiAttribution/urlQuery';
import { AppSetting } from '@pa/shared/dist/src/appSettings/appSettings';

export interface VersionMultiAttributionVersionInfo {
  versionCode?: string;
  updateVersionCode: string;
  releaseTime: number;
  startTime: number;
  endTime: number;
}

export interface VersionMultiAttributionProps {
  crashType: CrashType;
  baseVersionInfo: VersionMultiAttributionVersionInfo;
  targetVersionInfo: VersionMultiAttributionVersionInfo;
}

const VersionMultiAttribution: React.FC<VersionMultiAttributionProps> = props => {
  const [appSettingState] = useModel(AppSettingModule);
  const [isLoading, setIsLoading] = useState(false);
  const [activeTab, setActiveTab] = useState(MultiAttributionResultTab.ISSUE_LIST);
  const [sortType, setSortType] = useState(MultiAttributionIssueListSortType.TARGET_RANKING);

  const [comparedIssueListData, setComparedIssueListData] =
    useState<MultiAttributionResponse<MultiAttributionComparedIssueListData>>();
  const [dimensionResult, setDimensionResult] = useState<MultiAttributionDimensionResultProps>();

  // eslint-disable-next-line @typescript-eslint/no-use-before-define
  const baseUrl = buildSlardarMarketUrl(appSettingState.info, props.crashType, props.baseVersionInfo);
  // eslint-disable-next-line @typescript-eslint/no-use-before-define
  const targetUrl = buildSlardarMarketUrl(appSettingState.info, props.crashType, props.targetVersionInfo);
  // eslint-disable-next-line @typescript-eslint/no-use-before-define
  const baseVersionInfoView = getVersionInfoView(true, props.baseVersionInfo, baseUrl);
  // eslint-disable-next-line @typescript-eslint/no-use-before-define
  const targetVersionInfoView = getVersionInfoView(false, props.targetVersionInfo, targetUrl);

  const startIssueListAnalysis = async () => {
    setIsLoading(true);
    const result = await startCompareIssueListAnalysis({
      data: {
        baseSlardarMarketUrl: baseUrl,
        targetSlardarMarketUrl: targetUrl,
        totalCount: 200,
        sortType,
      },
    });
    setIsLoading(false);
    setComparedIssueListData(result);
  };

  const startDimensionAnalysis = async () => {
    setIsLoading(true);
    const result = await getDimensionsDataNew(baseUrl, targetUrl);
    setDimensionResult({
      code: result.error(),
      message: result.message(),
      data: result.data,
    });
    setIsLoading(false);
  };

  const onClickSortType = async (value: MultiAttributionIssueListSortType) => {
    if (comparedIssueListData === undefined || comparedIssueListData.data === undefined) {
      return;
    }
    setIsLoading(true);
    console.log(`prepare sort: ${comparedIssueListData.data.items}`);
    const result = MarketTools.sortComparedIssueList(comparedIssueListData.data.items, value);
    console.log(`sorted: ${result.items}`);
    const sortedData = comparedIssueListData;
    if (sortedData.data) {
      sortedData.data.items = result.items;
    }
    setSortType(value);
    setComparedIssueListData(sortedData);
    setIsLoading(false);
  };

  // eslint-disable-next-line @typescript-eslint/no-use-before-define
  const startButtonView = getStartButtonView(isLoading, async () => {
    if (!baseUrl || !targetUrl) {
      console.log(`Url not correct. base:${baseUrl} target:${targetUrl}`);
      return;
    }
    console.log(`开始分析: base:${baseUrl} target:${targetUrl} activeTab:${activeTab}`);
    if (activeTab === MultiAttributionResultTab.ISSUE_LIST) {
      await startIssueListAnalysis();
    } else if (activeTab === MultiAttributionResultTab.DIMENSION) {
      await startDimensionAnalysis();
    }
  });

  let issueListResult: LoadResult<MultiAttributionResponse<MultiAttributionComparedIssueListData>> | undefined,
    dimensionLoadResult: LoadResult<MultiAttributionDimensionResultProps> | undefined;
  if (isLoading) {
    issueListResult = { isLoading: true };
  } else if (comparedIssueListData) {
    issueListResult = { isLoading: false, data: comparedIssueListData };
  }
  if (isLoading) {
    dimensionLoadResult = { isLoading: true };
  } else if (dimensionResult) {
    dimensionLoadResult = { isLoading: false, data: dimensionResult };
  }

  return (
    <div style={{ width: '100%', paddingTop: 10 }}>
      <div className="grid">
        <Row type="flex" justify="start" align="middle" gutter={[8, 8]} style={{ paddingBottom: 10 }}>
          <Col>
            <Space vertical>
              <Row>
                <Col>{targetVersionInfoView}</Col>
              </Row>
              <Row>
                <Col>{baseVersionInfoView}</Col>
              </Row>
            </Space>
          </Col>
          <Col>
            <Space vertical>{startButtonView}</Space>
          </Col>
        </Row>
        <MultiAttributionResult
          multiAttributionType={MultiAttributionType.COMPARE}
          onActiveTabChange={tab => {
            setActiveTab(tab);
          }}
          onSortTypeChange={type => {
            setSortType(type);
            onClickSortType(type);
          }}
          issueListResult={issueListResult}
          dimensionResult={dimensionLoadResult}
        />
      </div>
    </div>
  );
};
export default VersionMultiAttribution;

export function buildVersionMultiAttributionProps(
  urlQuery: MultiAttributionURLQuery,
): VersionMultiAttributionProps | undefined {
  const crash_type = urlQuery[MultiAttributionURLQueryKey.CRASH_TYPE];
  if (!crash_type) {
    return undefined;
  }
  const base_version_code = urlQuery[MultiAttributionURLQueryKey.BASE_VERSION_CODE];
  if (!base_version_code) {
    return undefined;
  }
  const target_version_code = urlQuery[MultiAttributionURLQueryKey.TARGET_VERSION_CODE];
  if (!target_version_code) {
    return undefined;
  }
  const base_update_version_code = urlQuery[MultiAttributionURLQueryKey.BASE_UPDATE_VERSION_CODE];
  if (!base_update_version_code) {
    return undefined;
  }
  const target_update_version_code = urlQuery[MultiAttributionURLQueryKey.TARGET_UPDATE_VERSION_CODE];
  if (!target_update_version_code) {
    return undefined;
  }
  const base_release_time = urlQuery[MultiAttributionURLQueryKey.BASE_RELEASE_TIME];
  if (!base_release_time) {
    return undefined;
  }
  const target_release_time = urlQuery[MultiAttributionURLQueryKey.TARGET_RELEASE_TIME];
  if (!target_release_time) {
    return undefined;
  }
  const base_start_time = urlQuery[MultiAttributionURLQueryKey.BASE_START_TIME];
  if (!base_start_time) {
    return undefined;
  }
  const target_start_time = urlQuery[MultiAttributionURLQueryKey.TARGET_START_TIME];
  if (!target_start_time) {
    return undefined;
  }
  const base_end_time = urlQuery[MultiAttributionURLQueryKey.BASE_END_TIME];
  if (!base_end_time) {
    return undefined;
  }
  const target_end_time = urlQuery[MultiAttributionURLQueryKey.TARGET_END_TIME];
  if (!target_end_time) {
    return undefined;
  }
  return {
    crashType: crash_type,
    baseVersionInfo: {
      versionCode: base_version_code,
      updateVersionCode: base_update_version_code,
      releaseTime: base_release_time,
      startTime: base_start_time,
      endTime: base_end_time,
    },
    targetVersionInfo: {
      versionCode: target_version_code,
      updateVersionCode: target_update_version_code,
      releaseTime: target_release_time,
      startTime: target_start_time,
      endTime: target_end_time,
    },
  };
}

function formatTime(timestamp: number, locale = 'zh-CN'): string {
  const date = new Date(timestamp * 1000);
  const options: Intl.DateTimeFormatOptions = {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
    hour12: false,
  };
  return new Intl.DateTimeFormat(locale, options).format(date);
}

function getVersionInfoView(isBase: boolean, info: VersionMultiAttributionVersionInfo, slardarUrl: string) {
  return (
    <Space>
      <Typography.Text strong>{isBase ? '基准版本:' : '对比版本:'}</Typography.Text>
      <Tag color={'white'} size={'large'}>
        {`版本号：${info.updateVersionCode}`}
      </Tag>
      <Tag color={'white'} size={'large'}>
        {`放量时间：${formatTime(info.releaseTime)}`}
      </Tag>
      <Tag color={'white'} size={'large'}>
        {`时间段：${formatTime(info.startTime)} ~ ${formatTime(info.endTime)}`}
      </Tag>
      <Typography.Text link={{ href: `${slardarUrl}`, target: '_blank' }}>{`Slardar链接>>`}</Typography.Text>
    </Space>
  );
}

function getStartButtonView(isLoading: boolean, onClick: () => void) {
  return (
    <Button
      theme="solid"
      type="primary"
      size="default"
      disabled={isLoading}
      onClick={e => {
        onClick();
      }}
    >
      开始分析
    </Button>
  );
}

function buildSlardarMarketUrl(
  appSetting: AppSetting,
  crashType: CrashType,
  info: VersionMultiAttributionVersionInfo,
): string {
  const url = getSlardarUrl(
    appSetting.businessInfo.aid,
    CrashType2Url[crashType],
    appSetting.platform.toString(),
    crashType === CrashType.JavaMemLeak ? `perf_v2/memory/activity` : `abnormal_list`,
    getSlardarIssueParams(
      info.startTime,
      info.endTime,
      info.updateVersionCode,
      CrashType2Url[crashType],
      appSetting.platform === PlatformType.Android ? 'Android' : 'iOS',
      appSetting.name === '剪映',
      [CrashType.JavaOOM, CrashType.NativeOOM].includes(crashType),
      crashType,
    ),
  );
  return url;
}
