import React, { useEffect, useState } from 'react';
import { Col, Input, Notification, Row, Space, Spin, TabPane, Tabs } from '@douyinfe/semi-ui';
import DimensionResult, {
  MultiAttributionDimensionResultProps,
} from '@/pages/quality/version/multiAttribution/dimension';
import { startCompareIssueListAnalysis } from '@api/MultiAttribution';
import { MultiAttributionIssueList } from '@/component/MultiAttribution/issueList';
import { useModel } from '@edenx/runtime/model';
import AppSettingModule from '@/model/appSettingModel';
import VersionSettingModel from '@/model/versionSettingModel';
import { isSG } from '@/utils/region';
import { getDimensionsDataNew, GetDimensionsDataResult } from '@/pages/tools/multiAttribution/Dimensions';
import {
  MultiAttributionIssueListSortType,
  MultiAttributionRequestIssueCount,
  MultiAttributionType,
} from '@shared/multiAttribution/CommonModel';
import {
  MarketTools,
  MultiAttributionComparedIssueListData,
  MultiAttributionResponse,
} from '@shared/multiAttribution/market';
import { MultiAttributionTypeView } from '@/component/MultiAttribution/type';
import { useSearchParams } from '@edenx/runtime/router';
import { MultiAttributionURLQuery, MultiAttributionURLQueryKey } from '@shared/multiAttribution/urlQuery';
import { MultiAttributionRequestIssueCountSelector } from '@/component/MultiAttribution/issueCount';
import { QualityToolsButton, ToolsName } from '@shared/utils/QualityTools';

export interface MarketMultiAttributionProps {
  type: MultiAttributionType;
  baseSlardarUrl?: string;
  targetSlardarUrl?: string;
  issueSortType: MultiAttributionIssueListSortType;
}

const MarketMultiAttribution: React.FC<MarketMultiAttributionProps> = props => {
  const [searchParams, setSearchParams] = useSearchParams();
  const [appSettingState] = useModel(AppSettingModule);
  const [versionSettingState] = useModel(VersionSettingModel);
  const [sortType, setSortType] = useState(props.issueSortType);
  const [multiAttributionType, setMultiAttributionType] = useState(props.type);
  const [requestIssueListCount, setRequestIssueListCount] = useState(100);
  const [requestIssueListCountVisiable, setRequestIssueListCountVisiable] = useState(true);
  const [baseSlardarMarketUrlValue, setBaseSlardarMarketUrlValueValue] = useState(props.baseSlardarUrl ?? '');
  const [targetSlardarMarketUrlValue, setTargetSlardarMarketUrlValue] = useState(props.targetSlardarUrl ?? '');
  const [analysisResultTabsActiveKey, setAnalysisResultTabsActiveKey] = useState('1');
  const [loading, setLoading] = useState(false);
  const [comparedIssueListData, setComparedIssueListData] =
    useState<MultiAttributionResponse<MultiAttributionComparedIssueListData>>();
  const [dimensionResult, setDimensionResult] = useState<MultiAttributionDimensionResultProps>();
  const [compareGetDimensionsDataResult, setCompareGetDimensionsDataResult] = useState<GetDimensionsDataResult>();
  const [singleGetDimensionsDataResult, setSingleGetDimensionsDataResult] = useState<GetDimensionsDataResult>();

  useEffect(() => {
    if (analysisResultTabsActiveKey === '2') {
      setRequestIssueListCountVisiable(false);
    } else {
      setRequestIssueListCountVisiable(true);
    }
  }, [analysisResultTabsActiveKey]);

  useEffect(() => {
    if (multiAttributionType === MultiAttributionType.COMPARE) {
      if (
        compareGetDimensionsDataResult?.baseUrl === baseSlardarMarketUrlValue &&
        compareGetDimensionsDataResult?.targetUrl === targetSlardarMarketUrlValue
      ) {
        setDimensionResult({
          code: compareGetDimensionsDataResult.error(),
          message: compareGetDimensionsDataResult.message(),
          data: compareGetDimensionsDataResult.data,
        });
      } else {
        setDimensionResult({});
      }
    } else if (multiAttributionType === MultiAttributionType.DIMENSION) {
      if (singleGetDimensionsDataResult?.baseUrl === baseSlardarMarketUrlValue) {
        setDimensionResult({
          code: singleGetDimensionsDataResult.error(),
          message: singleGetDimensionsDataResult.message(),
          data: singleGetDimensionsDataResult.data,
        });
      } else {
        setDimensionResult({});
      }
    }
  }, [multiAttributionType]);

  const startDimensionAnalysis = async () => {
    if (multiAttributionType === MultiAttributionType.COMPARE) {
      if (
        compareGetDimensionsDataResult?.baseUrl !== baseSlardarMarketUrlValue ||
        compareGetDimensionsDataResult?.targetUrl !== targetSlardarMarketUrlValue
      ) {
        const result = await getDimensionsDataNew(baseSlardarMarketUrlValue, targetSlardarMarketUrlValue);
        if (result.isSuccess()) {
          setCompareGetDimensionsDataResult(result);
        } else {
          setCompareGetDimensionsDataResult(undefined);
        }
        setDimensionResult({
          code: result.error(),
          message: result.message(),
          data: result.data,
        });
      }
    } else if (multiAttributionType === MultiAttributionType.DIMENSION) {
      if (singleGetDimensionsDataResult?.baseUrl !== baseSlardarMarketUrlValue) {
        const result = await getDimensionsDataNew(baseSlardarMarketUrlValue);
        if (result.isSuccess()) {
          setSingleGetDimensionsDataResult(result);
        } else {
          setSingleGetDimensionsDataResult(undefined);
        }
        setDimensionResult({
          code: result.error(),
          message: result.message(),
          data: result.data,
        });
      }
    }
    setLoading(false);
  };

  const onClickStartAnalysis = async () => {
    console.log(
      `点击开始分析按钮, 分析类型：${multiAttributionType}, 基准Slardar链接：${baseSlardarMarketUrlValue}, 目标Slardar链接：${targetSlardarMarketUrlValue}`,
    );
    setLoading(true);
    if (multiAttributionType === MultiAttributionType.COMPARE) {
      if (analysisResultTabsActiveKey === '1') {
        const result = startCompareIssueListAnalysis({
          data: {
            baseSlardarMarketUrl: baseSlardarMarketUrlValue,
            targetSlardarMarketUrl: targetSlardarMarketUrlValue,
            totalCount: requestIssueListCount,
            sortType,
          },
        }).then(value => {
          setLoading(false);
          setComparedIssueListData(value);
        });
      } else if (analysisResultTabsActiveKey === '2') {
        await startDimensionAnalysis();
      }
    } else if (multiAttributionType === MultiAttributionType.DIMENSION) {
      await startDimensionAnalysis();
    }
  };

  const onAnalysisResultTabsChange = (value: string) => {
    console.log(`分析结果的Tab切换，value: ${value}`);
    setAnalysisResultTabsActiveKey(value);
    // if (value === "2") {
    // 	setComparedIssueListData(undefined);
    // }
  };

  const onClickSortType = async (value: MultiAttributionIssueListSortType) => {
    if (comparedIssueListData === undefined || comparedIssueListData.data === undefined) {
      return;
    }
    setLoading(true);
    console.log(`prepare sort: ${comparedIssueListData.data.items}`);
    const result = MarketTools.sortComparedIssueList(comparedIssueListData.data.items, value);
    console.log(`sorted: ${result.items}`);
    const sortedData = comparedIssueListData;
    if (sortedData.data) {
      sortedData.data.items = result.items;
    }
    setSortType(value);
    setComparedIssueListData(sortedData);
    setLoading(false);
  };

  const analysisResultTabs = [];
  if (multiAttributionType === MultiAttributionType.COMPARE) {
    analysisResultTabs.push({
      title: 'issue维度',
      itemKey: '1',
      content: loading ? (
        <div style={{ padding: 10 }}>
          <Spin />
        </div>
      ) : (
        <MultiAttributionIssueList data={comparedIssueListData} sortType={sortType} onClickSortType={onClickSortType} />
      ),
    });
  }
  analysisResultTabs.push({
    title: '其它维度',
    itemKey: '2',
    content: loading ? (
      <div style={{ padding: 10 }}>
        <Spin />
      </div>
    ) : (
      <DimensionResult code={dimensionResult?.code} message={dimensionResult?.message} data={dimensionResult?.data} />
    ),
  });

  const analysisResult = (
    <Space vertical align="start">
      分析结果：
      <Tabs type="card" activeKey={analysisResultTabsActiveKey} onChange={onAnalysisResultTabsChange}>
        {analysisResultTabs.map(tab => (
          // eslint-disable-next-line react/jsx-key
          <TabPane tab={tab.title} itemKey={tab.itemKey}>
            {tab.content}
          </TabPane>
        ))}
      </Tabs>
    </Space>
  );

  return (
    <div style={{ width: '100%', paddingTop: 10 }}>
      <div className="grid">
        <Row type="flex" justify="start" align="middle" gutter={[8, 8]} style={{ paddingBottom: 10 }}>
          <Col span={24}>
            <MultiAttributionTypeView
              disabled={loading}
              multiAttributionType={multiAttributionType}
              setMultiAttributionType={type => {
                setMultiAttributionType(type);
                setAnalysisResultTabsActiveKey(type === MultiAttributionType.COMPARE ? '1' : '2');
              }}
            />
          </Col>
          <Col span={8}>
            <Input
              showClear
              placeholder={
                multiAttributionType === MultiAttributionType.COMPARE
                  ? '输入基准Slardar大盘链接'
                  : '输入Slardar大盘链接'
              }
              value={baseSlardarMarketUrlValue}
              onChange={(value, e) => {
                setBaseSlardarMarketUrlValueValue(value);
                searchParams.set(MultiAttributionURLQueryKey.BASE_SLARDAR_URL, value);
                setSearchParams(searchParams);
                const oversea = value.includes('slardar-us');
                const isOversea = isSG(appSettingState.info);
                if (oversea !== isOversea) {
                  Notification.error({
                    content: 'Slardar 链接地区和右上角选择的App不匹配，slardar-us 请切换到 CapCut，其他请切换到剪映',
                    duration: 5,
                  });
                }
              }}
              disabled={loading}
            />
          </Col>
          <Col span={multiAttributionType === MultiAttributionType.COMPARE ? 8 : 0}>
            <Input
              showClear
              placeholder="输入目标Slardar大盘链接"
              value={targetSlardarMarketUrlValue}
              onChange={(value, e) => {
                setTargetSlardarMarketUrlValue(value);
                searchParams.set(MultiAttributionURLQueryKey.TARGET_SLARDAR_URL, value);
                setSearchParams(searchParams);
                const oversea = value.includes('slardar-us');
                const isOversea = isSG(appSettingState.info);
                if (oversea !== isOversea) {
                  Notification.error({
                    title: 'Slardar 链接地区和右上角选择的App不匹配，slardar-us 请切换到 CapCut，其他请切换到剪映',
                    duration: 5,
                    position: 'topLeft',
                  });
                }
              }}
              disabled={loading}
            />
          </Col>
          <Col span={8}>
            <Space>
              <MultiAttributionRequestIssueCountSelector
                disabled={loading}
                requestIssueCount={requestIssueListCount as MultiAttributionRequestIssueCount}
                onSelectedRequestIssueCount={value => {
                  setRequestIssueListCount(value);
                }}
              />
              <QualityToolsButton
                theme="solid"
                toolName={ToolsName.MarketMultiAttribution}
                title={'开始分析'}
                type="primary"
                size="default"
                disabled={loading}
                onClick={onClickStartAnalysis}
              />
            </Space>
          </Col>
        </Row>
        {analysisResult}
      </div>
    </div>
  );
};
export default MarketMultiAttribution;

export function buildMarketMultiAttributionProps(urlQuery: MultiAttributionURLQuery): MarketMultiAttributionProps {
  return {
    type: urlQuery[MultiAttributionURLQueryKey.TYPE],
    baseSlardarUrl: urlQuery[MultiAttributionURLQueryKey.BASE_SLARDAR_URL],
    targetSlardarUrl: urlQuery[MultiAttributionURLQueryKey.TARGET_SLARDAR_URL],
    issueSortType: urlQuery[MultiAttributionURLQueryKey.ISSUE_LIST_SORT_TYPE],
  };
}
