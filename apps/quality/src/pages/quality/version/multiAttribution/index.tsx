import React from 'react';
import { TabPane, Tabs } from '@douyinfe/semi-ui';
import MarketMultiAttribution, {
  buildMarketMultiAttributionProps,
} from '@/pages/quality/version/multiAttribution/market';
import SingleMultiAttribution, { buildSingleAttributionProps } from '@/pages/quality/version/multiAttribution/single';
import VersionMultiAttribution, {
  buildVersionMultiAttributionProps,
} from '@/pages/quality/version/multiAttribution/version';
import { useSearchParams } from '@edenx/runtime/router';
import { MultiAttributionScene } from '@shared/multiAttribution/CommonModel';
import { MultiAttributionURLQuery, MultiAttributionURLQueryKey } from '@shared/multiAttribution/urlQuery';

/**
 * 多维度归因Tab
 */
const MultiAttribution: React.FC = () => {
  const [searchParams, setSearchParams] = useSearchParams();

  const urlQuery = new MultiAttributionURLQuery(searchParams);
  const tab = urlQuery[MultiAttributionURLQueryKey.TAB];
  searchParams.set(MultiAttributionURLQueryKey.TAB, tab);
  setSearchParams(searchParams);

  let versionTab;
  const versionTabProps = buildVersionMultiAttributionProps(urlQuery);
  if (versionTabProps) {
    versionTab = <VersionMultiAttribution {...versionTabProps} />;
  } else {
    versionTab = <div style={{ padding: '10px' }}>版本归因所需要的参数不正确，无法展示页面</div>;
  }

  return (
    <Tabs
      type="line"
      defaultActiveKey={tab}
      onChange={activeKey => {
        searchParams.set(MultiAttributionURLQueryKey.TAB, activeKey);
        setSearchParams(searchParams);
      }}
    >
      <TabPane tab="大盘归因" itemKey={MultiAttributionScene.MARKET}>
        <MarketMultiAttribution {...buildMarketMultiAttributionProps(urlQuery)} />
      </TabPane>
      <TabPane tab="单issue归因" itemKey={MultiAttributionScene.ISSUE}>
        <SingleMultiAttribution {...buildSingleAttributionProps(urlQuery)} />
      </TabPane>
      <TabPane tab="版本归因" itemKey={MultiAttributionScene.VERSION}>
        {versionTab}
      </TabPane>
    </Tabs>
  );
};
export default MultiAttribution;
