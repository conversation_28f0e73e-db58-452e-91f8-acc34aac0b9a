import React from 'react';
import { DimensionData } from '@/pages/tools/multiAttribution/Dimensions';
import { Table } from '@douyinfe/semi-ui';
import { IconUnlink } from '@douyinfe/semi-icons';

export interface MultiAttributionDimensionResultProps {
  code?: number;
  message?: string;
  data?: DimensionData[][];
}

const DimensionResult: React.FC<MultiAttributionDimensionResultProps> = props => {
  const { code, message, data: data1 } = props;
  const baseDimensionData = data1?.[0];
  const targetDimensionData = data1?.[1];
  const dimensions = [
    ...new Set([
      ...(baseDimensionData?.map(item => item.dimension) ?? []),
      ...(targetDimensionData?.map(item => item.dimension) ?? []),
    ]),
  ];
  const result = dimensions.map(dimension => {
    const base = baseDimensionData?.find(i => i.dimension === dimension)?.data;
    const target = targetDimensionData?.find(i => i.dimension === dimension)?.data;
    const data = [
      ...new Set([...(base?.detail?.map(i => i.field) ?? []), ...(target?.detail?.map(i => i.field) ?? [])]),
    ].map(field => {
      const basePercent = base?.detail?.find(i => i.field === field)?.percent;
      const targetPercent = target?.detail?.find(i => i.field === field)?.percent;
      const diff = basePercent && targetPercent ? (targetPercent - basePercent) / basePercent : undefined;

      return {
        field: field && field?.length >= 128 ? field?.substring(0, 128) + '...' : field,
        basePercent: basePercent ? (100 * basePercent).toFixed(4) : '?',
        targetPercent: targetPercent ? (100 * targetPercent).toFixed(4) : '?',
        mark: diff ? Math.abs(diff) >= 0.2 : false,
        diff: diff ? (100 * diff).toFixed(4) : '?',
      };
    });

    return {
      title: dimension.description,
      data,
    };
  });

  if (code === 0) {
    return (
      <div>
        {result.map(value => {
          const render = (text: string) => {
            if (text === '?') {
              return <IconUnlink />;
            } else {
              return text;
            }
          };
          const columns = [
            {
              title: value.title,
              dataIndex: 'field',
            },
            {
              title: '基准URL(%)',
              dataIndex: 'basePercent',
              render,
            },
          ];
          if (targetDimensionData) {
            columns.push({
              title: '对比URL(%)',
              dataIndex: 'targetPercent',
              render,
            });
            columns.push({
              title: '差异(%)',
              dataIndex: 'diff',
              render,
            });
          }
          const handleRow = (record?: { mark: boolean }) => {
            if (record?.mark) {
              return {
                style: {
                  background: 'var(--semi-color-warning-light-default)',
                },
              };
            }
            return {};
          };
          return (
            <div key={value.title}>
              <br />
              <Table key={value.title} columns={columns} dataSource={value.data} onRow={handleRow} pagination={false} />
            </div>
          );
        })}
      </div>
    );
  } else if (code || message) {
    return (
      <div>
        发生错误，错误码:{code},错误提示:{message}
      </div>
    );
  } else {
    return <div style={{ padding: 10 }}>暂无结果：请输入链接后，点击「开始分析」按钮</div>;
  }
};

export default DimensionResult;
