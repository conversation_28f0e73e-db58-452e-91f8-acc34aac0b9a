import React, { useEffect, useState } from 'react';
import { MultiAttributionIssueListSortType, MultiAttributionType } from '@shared/multiAttribution/CommonModel';
import { MultiAttributionComparedIssueListData, MultiAttributionResponse } from '@shared/multiAttribution/market';
import DimensionResult, {
  MultiAttributionDimensionResultProps,
} from '@/pages/quality/version/multiAttribution/dimension';
import { GetDimensionsDataResult } from '@/pages/tools/multiAttribution/Dimensions';
import { Space, TabPane, Tabs, Typography, Divider, Spin } from '@douyinfe/semi-ui';
import { MultiAttributionIssueList, MultiAttributionIssueListProps } from '@/component/MultiAttribution/issueList';

const LoadingView: React.FC = () => (
  <div style={{ padding: 10 }}>
    <Spin />
  </div>
);

const EmptyView: React.FC<{ content: string }> = props => <div style={{ padding: 10 }}>{props.content}</div>;

export enum MultiAttributionResultTab {
  ISSUE_LIST = 'issue_list',
  DIMENSION = 'dimension',
}

export type LoadingType = { isLoading: true };
export type LoadedType<T> = { isLoading: false; data: T };
export type LoadResult<T> = LoadingType | LoadedType<T>;

export interface MultiAttributionResultProps {
  multiAttributionType: MultiAttributionType;
  onActiveTabChange?: (tab: MultiAttributionResultTab) => void;
  onSortTypeChange?: (sortType: MultiAttributionIssueListSortType) => void;
  issueListResult?: LoadResult<MultiAttributionResponse<MultiAttributionComparedIssueListData>>;
  dimensionResult?: LoadResult<MultiAttributionDimensionResultProps>;
}

const MultiAttributionResult: React.FC<MultiAttributionResultProps> = props => {
  const [sortType, setSortType] = useState(MultiAttributionIssueListSortType.TARGET_RANKING);

  const updateActiveTab = (tab: MultiAttributionResultTab) => {
    props.onActiveTabChange?.(tab);
  };

  const tabs = [];
  if (props.multiAttributionType === MultiAttributionType.COMPARE) {
    const { issueListResult } = props;
    let issueListContent;
    if (issueListResult) {
      issueListContent = issueListResult.isLoading ? (
        <LoadingView />
      ) : (
        <MultiAttributionIssueList
          data={issueListResult.data}
          sortType={sortType}
          onClickSortType={type => {
            setSortType(type);
            props.onSortTypeChange?.(type);
          }}
        />
      );
    } else {
      issueListContent = <EmptyView content={'暂无内容'} />;
    }
    tabs.push({
      title: 'issue列表',
      itemKey: MultiAttributionResultTab.ISSUE_LIST,
      content: issueListContent,
    });
  }
  const { dimensionResult } = props;
  let dimensionContent;
  if (dimensionResult) {
    dimensionContent = dimensionResult.isLoading ? <LoadingView /> : <DimensionResult {...dimensionResult.data} />;
  } else {
    dimensionContent = <EmptyView content={'暂无内容'} />;
  }
  tabs.push({
    title: '其它维度',
    itemKey: MultiAttributionResultTab.DIMENSION,
    content: dimensionContent,
  });

  return (
    <Space vertical align="start">
      <Divider margin={'10px'} />
      <Typography.Text strong>归因结果</Typography.Text>
      <Tabs
        type="card"
        // activeKey={activeTab}
        onChange={activeKey => {
          const tab = activeKey as MultiAttributionResultTab;
          updateActiveTab(tab);
        }}
      >
        {tabs.map(tab => (
          // eslint-disable-next-line react/jsx-key
          <TabPane tab={tab.title} itemKey={tab.itemKey}>
            {tab.content}
          </TabPane>
        ))}
      </Tabs>
    </Space>
  );
};
export default MultiAttributionResult;
