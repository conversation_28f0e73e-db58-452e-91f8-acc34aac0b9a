import { useModel } from '@edenx/runtime/model';
import AppSettingModule from '@/model/appSettingModel';
import React, { useEffect, useState } from 'react';
import { queryNewCrashList, queryNewCrashRateMetric } from '@api/slardar';
import { SlardarPlatformType } from '@pa/shared/dist/src/appSettings/appSettings';
import { ProCard } from '@ant-design/pro-components';
import { Line } from '@ant-design/charts';
import { Button, Select, Space, Table, TableProps, Tag } from 'antd';
import { CrashName, CrashNewListInfo, CrashType } from '@shared/typings/slardar/crash/issueListSearch';
import Link from 'antd/es/typography/Link';
import UserMessage from '@/component/UserMessage';
import { CrashType2Url, getSlardarIssueParams, getSlardarUrl } from '@shared/typings/slardar/common';
import { AxisCfg } from '@antv/g2/lib/interface';
import dayjs from 'dayjs';
import { PlatformType } from '@pa/shared/dist/src/core';

export interface ChartData {
  dataX: string;
  dataY: number;
  category: string;
}

const SlardarNewStatistic: React.FC = () => {
  const [appSettingState] = useModel(AppSettingModule);
  const [data, setData] = useState<any>({});
  const [chartLoading, setChartLoading] = useState<boolean>(true);
  const [chartData, setChartData] = useState<ChartData[]>([]);
  const [list, setList] = useState<CrashNewListInfo[]>([]);
  const [startVersion, setStartVersion] = useState<string>('');
  const [endVersion, setEndVersion] = useState<string>('');
  const [buttonTime, setButtonTime] = useState<{
    startTime: number;
    endTime: number;
  }>({ startTime: 0, endTime: 0 });
  const [versionCodeOptions, setVersionCodeOptions] = useState<
    {
      label: string;
      value: string;
    }[]
  >([]);
  const [onSelected, setOnSelected] = useState<{
    crashType: string;
    version_code: string;
  }>({ crashType: CrashType.JavaCrash, version_code: '' });
  const [tableLoading, setTableLoading] = useState<boolean>(false);
  const [startTime, setStartTime] = useState<number>(0);
  const [endTime, setEndTime] = useState<number>(Math.floor(Date.now() / 1000));
  useEffect(() => {
    if (!startVersion) {
      setStartTime(0);
    }
    if (!endVersion) {
      setEndTime(Math.floor(dayjs().valueOf() / 1000));
    }
    if (data) {
      let i = true;
      let j = true;
      for (const crashType in data) {
        if (!i && !j) {
          break;
        }
        for (const d of data[crashType]) {
          if (d.version_code === startVersion && i) {
            setStartTime(d.start_time);
            i = false;
          }
          if (d.version_code === endVersion && j) {
            setEndTime(d.start_time);
            j = false;
          }
        }
      }
    }
  }, [startVersion, endVersion]);
  useEffect(() => {
    setTableLoading(true);
    if (onSelected.crashType && onSelected.version_code && onSelected.crashType in data) {
      for (const d of data[onSelected.crashType]) {
        if (d.version_code === onSelected.version_code) {
          setButtonTime({
            startTime: d.start_time,
            endTime: d.end_time,
          });
          queryNewCrashList({
            data: {
              aid: appSettingState.info.businessInfo.aid,
              crashType: onSelected.crashType as CrashType,
              version_code: onSelected.version_code,
              platform:
                appSettingState.info.platform === PlatformType.Android
                  ? SlardarPlatformType.Android
                  : SlardarPlatformType.iOS,
              start_time: d.start_time,
              end_time: d.end_time,
            },
          }).then(v => {
            setList(v.data);
          });
        }
      }
    }
    setTableLoading(false);
  }, [onSelected, data]);
  const columns: TableProps<CrashNewListInfo>['columns'] = [
    {
      title: 'Issue详情',
      dataIndex: 'crash_file',
      width: '15%',
      fixed: 'left',
      render: (_, row) => (
        <>
          <Link href={row?.slardar_url ? row.slardar_url : undefined} style={{ color: 'blue' }} target={'_blank'}>
            {row.crash_file}
          </Link>
          <br />
          <>
            {(onSelected.crashType as CrashType) !== CrashType.NativeCrash &&
            (onSelected.crashType as CrashType) !== CrashType.NativeOOM &&
            (onSelected.crashType as CrashType) !== CrashType.OOMCrash &&
            row.crash_line_number
              ? `line ${row.crash_line_number}`
              : ''}
          </>
          <br />
          <>{row.crash_clazz}</>
          <br />
          <>{row.crash_reason.substring(0, 200)}</>
        </>
      ),
    },
    {
      title: '处理人',
      dataIndex: 'managers',
      fixed: 'left',
      width: '10%',
      render: (_, row) => <UserMessage emails={row.managers ? row.managers : []} />,
    },
    {
      title: '分布详情',
      width: '8%',
      fixed: 'left',
      align: 'center',
      dataIndex: 'start_os_version',
      render: (_, row) => (
        <>
          {row.start_os_version} - {row.end_os_version}{' '}
        </>
      ),
    },
    {
      title: '本周用户数',
      width: '8%',
      fixed: 'left',
      align: 'center',
      dataIndex: 'user',
      render: (_, row) => (
        <Space direction={'vertical'}>
          {row.user}
          <Tag color={'green'}>Top{row.ranking <= 2000 ? row.ranking : '2000+'}</Tag>
          <Tag color={'yellow'}>平均影响用户比例{(row.rate * 1000).toFixed(4)}‰</Tag>
        </Space>
      ),
    },
  ];
  const config = {
    data: chartData,
    xField: 'dataX',
    yField: 'dataY',
    seriesField: 'category',
    loading: chartLoading,
    yAxis: {
      label: {
        formatter: (v: number) => `${v}‰`,
      },
    } as unknown as AxisCfg,
    tooltip: {
      formatter: (datum: any) => ({ name: datum.category, value: `${datum.dataY.toFixed(4)}‰` }),
    },
  };

  useEffect(() => {
    const temp: ChartData[] = [];
    const total: {
      [date: string]: number;
    } = {};
    for (const crashType in data) {
      for (const d of data[crashType]) {
        temp.push({
          category: crashType,
          dataX: d.version_code,
          dataY: d.Value * 1000,
        });
        if (!(d.version_code in total)) {
          total[d.version_code] = 0;
        }
        total[d.version_code] += d.Value * 1000;
      }
    }
    const reserve: ChartData[] = [];
    for (const version_code in total) {
      reserve.push({
        category: 'total',
        dataX: version_code,
        dataY: total[version_code],
      });
    }
    temp.unshift(...reserve);
    setChartData(temp);
    setChartLoading(false);
  }, [data]);
  useEffect(() => {
    queryNewCrashRateMetric({
      data: {
        // appSettingState.info.name === "剪映" ? versionState.info.lvVersion : versionState.info.ccVersion,
        platform:
          appSettingState.info.platform === PlatformType.Android
            ? SlardarPlatformType.Android
            : SlardarPlatformType.iOS,
        start_time: startTime,
        end_time: endTime,
      },
    }).then((res: any) => {
      setData(res.data);
      const versionCodes: string[] = [];
      console.log(res.data);
      for (const d in res.data) {
        for (const i of res.data[d]) {
          versionCodes.push(i.version_code);
        }
      }
      setVersionCodeOptions(
        Array.from(new Set(versionCodes))
          .reverse()
          .map(v => ({ label: v, value: v })),
      );
      setOnSelected({ crashType: onSelected.crashType, version_code: versionCodes[versionCodes.length - 1] });
    });
  }, [appSettingState.info.platform, startTime, endTime]);
  return (
    <Space direction={'vertical'} style={{ width: '100%' }} size={'large'}>
      <ProCard
        bordered={true}
        headerBordered={true}
        title={
          <Space>
            <div>{'单版本新增问题劣化统计'}</div>
          </Space>
        }
        size={'small'}
        style={{ width: '100%' }}
        extra={
          <Space>
            <>覆盖版本</>
            <Select
              style={{ width: 150 }}
              value={startVersion}
              onSelect={v => setStartVersion(v)}
              options={versionCodeOptions}
              onClear={() => setStartVersion('')}
              allowClear={true}
            />
            <>-</>
            <Select
              style={{ width: 150 }}
              value={endVersion}
              onSelect={v => setEndVersion(v)}
              options={versionCodeOptions}
              onClear={() => setEndVersion('')}
              allowClear={true}
            />
          </Space>
        }
      >
        <Line
          {...config}
          // onPlotClick={(v: any) => console.log("点击", v)}
        />
      </ProCard>
      <Space direction={'horizontal'}>
        <Button
          // id={`StabilityIssueList_${timestamp - 7 * 24 * 3600}_${timestamp}_${appSettingState.info.id}`}
          key={appSettingState.info.id}
          href={getSlardarUrl(
            appSettingState.info.businessInfo.aid,
            CrashType2Url[onSelected.crashType],
            appSettingState.info.platform.toString(),
            (onSelected.crashType as CrashType) === CrashType.JavaMemLeak ? `perf_v2/memory/activity` : `abnormal_list`,
            getSlardarIssueParams(
              buttonTime.startTime,
              buttonTime.endTime,
              onSelected.version_code,
              CrashType2Url[onSelected.crashType],
              appSettingState.info.platform === PlatformType.iOS ? 'iOS' : 'Android',
              Math.floor(appSettingState.info.id / 100) === 1775,
              [CrashType.OOMCrash, CrashType.JavaOOM, CrashType.NativeOOM].includes(onSelected.crashType as CrashType),
              onSelected.crashType as CrashType,
              undefined,
              undefined,
              86400,
              true,
            ),
          )}
          target={'_blank'}
          color={'blue'}
        >
          跳转 Slardar
        </Button>
        <>
          崩溃类型:
          <Select
            style={{ width: 150 }}
            value={{
              label: CrashName[onSelected.crashType as keyof typeof CrashName],
              value: onSelected.crashType,
            }}
            onSelect={v => setOnSelected({ crashType: v.value, version_code: onSelected.version_code })}
            labelInValue={true}
            options={[CrashType.JavaCrash, CrashType.JavaStartCrash, CrashType.ANR, CrashType.NativeCrash].map(v => ({
              label: CrashName[v.toString() as keyof typeof CrashName],
              value: v,
            }))}
          />
        </>
        <>
          版本:
          <Select
            style={{ width: 150 }}
            value={onSelected.version_code}
            onSelect={v => setOnSelected({ crashType: onSelected.crashType, version_code: v })}
            options={versionCodeOptions}
          />
        </>
        <>
          拉取时间:{new Date(buttonTime.startTime * 1000).toISOString().slice(0, 10)}-
          {new Date(buttonTime.endTime * 1000).toISOString().slice(0, 10)}
        </>
      </Space>
      <Table
        style={{ width: '100%' }}
        columns={columns}
        dataSource={list}
        pagination={{ pageSize: 20, showSizeChanger: false }}
        loading={tableLoading}
      />
    </Space>
  );
};

export default SlardarNewStatistic;
