import { CrashName, CrashType } from '@shared/typings/slardar/crash/issueListSearch';
import React, { useEffect, useState } from 'react';
import { useModel } from '@edenx/runtime/model';
import AppSettingModule from '@/model/appSettingModel';
import { SlardarPlatformType } from '@pa/shared/dist/src/appSettings/appSettings';
import { queryCrashListWithIssueId, querySlardarCrashIssue, queryTop10SlardarCrashList } from '@api/slardar';
import { ColumnsType } from 'antd/es/table';
import { Descriptions, Divider, Table, Tag, Typography } from 'antd';
import { DBSlardarCrashIssue } from '../../../../../api/model/SlardarCrashIssueTable';
import { AlarmVersion } from '@shared/typings/tea/metric';
import {
  CrashType2Url,
  getSlardarIssueListParams,
  getSlardarIssueParams,
  getSlardarUrl,
} from '@shared/typings/slardar/common';
import dayjs from 'dayjs';
import { DeviceLevel } from '@shared/common';
import { PlatformType } from '@pa/shared/dist/src/core';

const { Title } = Typography;

interface SubListProps {
  versions: [AlarmVersion, AlarmVersion];
  total_value: [number, number];
  type: CrashType;
  platform: SlardarPlatformType;
  deviceLevel: DeviceLevel;
}

interface SubListTableData {
  level: [number | undefined, number | undefined];
  delta: number;
  issue: string;
  url: string;
}

const columns: ColumnsType<SubListTableData> = [
  {
    title: 'Level',
    key: 'level',
    width: '14%',
    render: (_, record) => {
      const [o, n] = record.level;
      if (!o && n) {
        return `New TOP ${n}`;
      } else if (o && !n) {
        return `Old TOP ${o}`;
      } else if (o && n) {
        return `Old TOP ${o} -> New TOP ${n}`;
      }
      return `Error`;
    },
  },
  {
    title: '对应用户比例',
    key: 'delta',
    width: '12%',
    render: (_, record) => {
      const [o, n] = record.level;
      const { delta } = record;
      return (
        <>
          <Tag color={delta > 0 ? '#ee1738' : '#1bdc1b'}>
            {o && n ? (delta > 0 ? '增长' : '下降') : delta > 0 ? '新增' : '修复'}
          </Tag>
          {delta > 0 ? '+' : ''}
          {(delta * 1000).toFixed(4)}‰
        </>
      );
    },
  },
  {
    title: 'Issue',
    key: 'issue',
    dataIndex: 'issue',
    render: (_, record) => {
      const { issue } = record;
      return issue.length > 512 ? `${issue.substring(0, 512)} ...（过长，已截断）` : issue;
    },
  },
  {
    title: '操作',
    key: 'operations',
    width: '9%',
    render: (_, record) => (
      <a href={record.url} target="_blank" rel="noreferrer">
        详情
      </a>
    ),
  },
];

function detailToIssueString(d: DBSlardarCrashIssue, t: CrashType): string {
  return `${d?.crash_file || ''}${
    t !== CrashType.NativeCrash && t !== CrashType.NativeOOM && d?.crash_line_number ? `:${d.crash_line_number}` : ''
  } | ${d?.crash_clazz || ''} | ${d?.crash_reason || ''}`
    .trimStart()
    .trimEnd();
}

const SubList: React.FC<SubListProps> = ({ versions, type, platform, total_value, deviceLevel }) => {
  const [old_ver, new_ver] = versions;
  const [appSettingState] = useModel(AppSettingModule);
  const [data, setData] = useState<SubListTableData[]>([]);

  useEffect(() => {
    Promise.all([
      queryTop10SlardarCrashList({
        data: {
          version_code: old_ver.versionCode,
          platform,
          crash_type: type,
          deviceLevel,
          aid: appSettingState.info.businessInfo.aid,
        },
      }),
      queryTop10SlardarCrashList({
        data: {
          version_code: new_ver.versionCode,
          platform,
          crash_type: type,
          deviceLevel,
          aid: appSettingState.info.businessInfo.aid,
        },
      }),
    ]).then(async ([old_list, new_list]) => {
      // 新的 top 10，对应上旧的
      const new_list_map_to_old = await queryCrashListWithIssueId({
        data: {
          platform,
          version_code: old_ver.versionCode,
          crash_type: type,
          issue_list: new_list.map(it => it.issue_id),
          deviceLevel,
          aid: appSettingState.info.businessInfo.aid,
        },
      });

      // 新版本问题的详细数据
      const new_detail = await querySlardarCrashIssue({
        data: {
          aid: appSettingState.info.businessInfo.aid,
          issue_list: new_list.map(it => it.issue_id),
        },
      });

      // 旧的 top 10，对应上新的
      const old_list_map_to_new = await queryCrashListWithIssueId({
        data: {
          platform,
          version_code: new_ver.versionCode,
          crash_type: type,
          issue_list: old_list.map(it => it.issue_id),
          deviceLevel,
          aid: appSettingState.info.businessInfo.aid,
        },
      });

      // 旧版本问题的详细数据
      const old_detail = await querySlardarCrashIssue({
        data: {
          aid: appSettingState.info.businessInfo.aid,
          issue_list: old_list
            .filter(it => !old_list_map_to_new.find(cur => cur.issue_id === it.issue_id))
            .map(it => it.issue_id),
        },
      });

      // 时间相关
      const new_ver_start = dayjs.unix(new_ver.timestamp).add(2, 'd').startOf('d').unix();
      const new_ver_end = Math.min(dayjs.unix(new_ver_start).add(6, 'd').unix(), dayjs().unix());
      const old_ver_start = dayjs.unix(old_ver.timestamp).add(2, 'd').startOf('d').unix();
      const old_ver_end = Math.min(dayjs.unix(old_ver_start).add(6, 'd').unix(), dayjs().unix());

      console.log('I am setting data!');
      setData([
        // 新的 top 10 全有，map 上旧的排名
        ...new_list.map((it): SubListTableData => {
          // 这才 10 个，就不搞 map 了，O(n) 复杂度也不会很高
          const old_issue = new_list_map_to_old.find(t => t.issue_id === it.issue_id);
          const old_rank = old_issue?.ranking;
          const new_issue_detail = new_detail.find(t => t.issue_id === it.issue_id)!;
          return {
            delta: it.user_rate - (old_issue?.user_rate || 0),
            level: [old_rank, it.ranking],
            issue: detailToIssueString(new_issue_detail, type),
            url: getSlardarUrl(
              appSettingState.info.businessInfo.aid,
              CrashType2Url[type],
              platform,
              `abnormal/detail/${CrashType2Url[type]}/${it.issue_id}`,
              getSlardarIssueParams(
                new_ver_start,
                new_ver_end,
                new_ver.versionCode,
                CrashType2Url[type],
                appSettingState.info.platform === PlatformType.iOS ? 'iOS' : 'Android',
                appSettingState.info.name === '剪映',
                [CrashType.OOMCrash, CrashType.JavaOOM, CrashType.NativeOOM].includes(type),
              ),
            ),
          };
        }),
        // 旧的 top 10 若已解决
        ...old_list
          .filter(it => !old_list_map_to_new.find(cur => cur.issue_id === it.issue_id))
          .map(
            (it): SubListTableData => ({
              delta: -it.user_rate,
              level: [it.ranking, undefined],
              issue: detailToIssueString(old_detail.find(t => t.issue_id === it.issue_id)!, type),
              url: getSlardarUrl(
                appSettingState.info.businessInfo.aid,
                CrashType2Url[type],
                platform,
                `abnormal/detail/${CrashType2Url[type]}/${it.issue_id}`,
                getSlardarIssueListParams(it.platform, it.version_code, old_ver_start, old_ver_end),
              ),
            }),
          ),
      ]);
    });
  }, [appSettingState.info, versions, type]);

  const [old_val, new_val] = total_value;

  return (
    <>
      <Title level={3}>{CrashName[type]}</Title>
      <Descriptions bordered size={'small'}>
        <Descriptions.Item label={'指标'}>
          {`${(old_val * 1000).toFixed(5)}‰ -> ${(new_val * 1000).toFixed(5)}‰`}
        </Descriptions.Item>
        <Descriptions.Item label={'总变化'}>
          <Tag color={old_val < new_val ? '#f39720' : '#3ee82b'}>{old_val < new_val ? '增长' : '下降'}</Tag>
          {old_val < new_val ? '+' : ''}
          {((new_val - old_val) * 1000).toFixed(5)}‰
        </Descriptions.Item>
      </Descriptions>
      <Table columns={columns} dataSource={data} pagination={false} />
      <Divider />
    </>
  );
};

export default SubList;
