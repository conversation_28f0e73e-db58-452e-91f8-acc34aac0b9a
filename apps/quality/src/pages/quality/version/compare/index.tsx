import React, { useEffect, useState } from 'react';
import VersionSelector from '@/component/VersionSelector';
import { buildTreeFromVersionList } from '@/component/VersionSelector/utils';
import { AlarmVersion } from '@shared/typings/tea/metric';
import { Button, Divider, message, Space, Tabs } from 'antd';
import { SearchOutlined } from '@ant-design/icons';
import Stability from '@/pages/quality/version/compare/stability';
import { useModel } from '@edenx/runtime/model';
import AppSettingModule from '@/model/appSettingModel';
import { SlardarPlatformType } from '@pa/shared/dist/src/appSettings/appSettings';
import UserSettingModule from '@/model/userSettingModel';
import { getReleaseVersions } from '@/utils/version';
import { CompareVersionCode } from '@shared/utils/version_utils';
import { PlatformType } from '@pa/shared/dist/src/core';
import { DeviceLevel } from '@shared/common';
import { QualityToolsButton, ToolsName } from '@shared/utils/QualityTools';

const Compare: React.FC = () => {
  // 版本列表
  const [versions, setVersions] = useState<AlarmVersion[]>([]);

  // 已经选择的“版本”
  const [selected, setSelected] = useState<string[]>([]);

  // 平台
  const [appSettingState] = useModel(AppSettingModule);
  const [userSettingState] = useModel(UserSettingModule);

  useEffect(() => {
    getReleaseVersions(
      appSettingState.info.platform === PlatformType.iOS ? SlardarPlatformType.iOS : SlardarPlatformType.Android,
      appSettingState.info.businessInfo.aid?.toString(),
    )
      .then(it => it.sort((a, b) => CompareVersionCode(b.versionCode, a.versionCode)))
      .then(setVersions);
  }, [appSettingState.info.platform, userSettingState]);

  const [stability, setStability] = useState(<></>);
  const [lowStability, setLowStability] = useState(<></>);
  const [highStability, setHighStability] = useState(<></>);

  return (
    <>
      <Space size="middle">
        选择您要对比的版本：
        <div style={{ width: '256px' }}>
          <VersionSelector
            data={buildTreeFromVersionList(versions)}
            onChange={v => {
              setSelected(Array.isArray(v) ? v : [v]);
            }}
            value={selected}
            isSingle={false}
          />
        </div>
        <QualityToolsButton
          toolName={ToolsName.QualityCompare}
          title={'搜寻'}
          type="primary"
          icon={<SearchOutlined />}
          onClick={() => {
            if (selected.length < 2) {
              message.error('您必须选择 2 个版本才能对比！');
            }
            const c = selected.sort(CompareVersionCode).map(it => versions.find(cur => cur.versionCode === it)!);
            setStability(<Stability versions={c} deviceLevel={DeviceLevel.ALL} />);
            setLowStability(<Stability versions={c} deviceLevel={DeviceLevel.LOW} />);
            setHighStability(<Stability versions={c} deviceLevel={DeviceLevel.HIGH} />);
          }}
        />
      </Space>
      <Divider />
      <Tabs
        onChange={(key: string) => {
          console.log(key);
        }}
        type="card"
        items={[
          {
            label: '稳定性',
            key: 'Stability',
            children: stability,
          },
          {
            label: '高端机稳定性',
            key: 'highStability',
            children: highStability,
          },
          {
            label: '低端机稳定性',
            key: 'lowStability',
            children: lowStability,
          },
        ]}
      />
    </>
  );
};

export default Compare;
