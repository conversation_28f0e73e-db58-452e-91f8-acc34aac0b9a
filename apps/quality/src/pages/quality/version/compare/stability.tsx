import React, { useEffect, useState } from 'react';
import { ColumnsType } from 'antd/es/table';
import { Divider, Table, Tag, Typography } from 'antd';
import { queryVersionQualityValue } from '@api/slardar';
import { CrashName, CrashType } from '@shared/typings/slardar/crash/issueListSearch';
import { groupBy } from 'lodash';
import { useModel } from '@edenx/runtime/model';
import AppSettingModule from '@/model/appSettingModel';
import { SlardarPlatformType } from '@pa/shared/dist/src/appSettings/appSettings';
import { AndroidCrash, iOSCrash } from '@shared/constants/slardar';
import StabilitySubList from '@/pages/quality/version/compare/stabilitySubList';
import { AlarmVersion, CompareVersionInfo } from '@shared/typings/tea/metric';
import { DeviceLevel } from '@shared/common';
import { PlatformType } from '@pa/shared/dist/src/core';

const { Title } = Typography;

export interface QualityVerCompStabProps {
  versions: AlarmVersion[];
  deviceLevel: DeviceLevel;
}

export interface CompareType {
  id: string;
  name: string;
  versions: CompareVersionInfo[];
}

function build_columns(versions: AlarmVersion[]): ColumnsType<CompareType> {
  const result: ColumnsType<CompareType> = [];
  result.push({
    title: '指标名称',
    dataIndex: 'name',
    key: 'name',
  });
  const baseVersion = versions[0].versionCode;
  result.push({
    title: versions[0].versionCode,
    dataIndex: versions[0].versionCode,
    key: versions[0].versionCode,
    render: (_, r) =>
      `${((r.versions.find(it => it.version === versions[0].versionCode)?.value ?? 0) * 1000).toFixed(4)}‰`,
  });
  for (let index = 1; index < versions.length; index++) {
    result.push({
      title: versions[index].versionCode,
      dataIndex: versions[index].versionCode,
      key: versions[index].versionCode,
      render: (_, r) =>
        `${((r.versions.find(it => it.version === versions[index].versionCode)?.value ?? 0) * 1000).toFixed(4)}‰`,
    });
    result.push({
      title: '变化率',
      key: `delta${index}`,
      width: '10%',
      render: (_, record) => (
        <Tag
          color={
            (record.versions.find(it => it.version === baseVersion)?.value ?? 0) >
            (record.versions.find(it => it.version === versions[index].versionCode)?.value ?? 0)
              ? '#87d068'
              : '#ff5500'
          }
        >
          {(record.versions.find(it => it.version === baseVersion)?.value ?? 0) <=
          (record.versions.find(it => it.version === versions[index].versionCode)?.value ?? 0)
            ? '+'
            : ''}
          {(
            ((record.versions.find(it => it.version === versions[index].versionCode)?.value ?? 0) /
              (record.versions.find(it => it.version === baseVersion)?.value ?? 0) -
              1) *
            100
          ).toFixed(2)}
          %
        </Tag>
      ),
    });
  }
  return result;
}

async function fetchData(versions: string[], deviceLevel: DeviceLevel): Promise<CompareType[]> {
  const values = await queryVersionQualityValue({
    data: {
      versions,
      deviceLevel,
    },
  });

  const grouped = groupBy(values.Data, it => it.metric_name);
  const ret: CompareType[] = [];
  for (const metric of Object.keys(grouped)) {
    if (!grouped[metric]) {
      continue;
    }
    ret.push({
      id: metric,
      name: CrashName[metric as keyof typeof CrashName],
      versions: grouped[metric].map(it => ({
        version: it.version_code,
        value: it.value,
      })),
    });
  }
  return ret;
}

function extractValue(c?: CompareType): [number, number] {
  return [c?.versions[0]?.value || 0, c?.versions[1]?.value || 0];
}

const Stability: React.FC<QualityVerCompStabProps> = ({ versions, deviceLevel }) => {
  // const [old_ver, new_ver] = versions;
  const [data, setData] = useState<CompareType[]>([]);
  const [appSettingState] = useModel(AppSettingModule);
  const platform =
    appSettingState.info.platform === PlatformType.iOS ? SlardarPlatformType.iOS : SlardarPlatformType.Android;

  useEffect(() => {
    fetchData(
      versions.map(it => it.versionCode),
      deviceLevel,
    ).then(it => {
      setData(it);
    });
  }, [versions]);
  if (versions.length !== 2) {
    return (
      <>
        <Table columns={build_columns(versions)} dataSource={data} pagination={false} />
      </>
    );
  }

  return (
    <>
      <Table columns={build_columns(versions)} dataSource={data} pagination={false} />
      <Divider />
      <Title level={2}>版本稳定性归因分析</Title>
      {(platform === SlardarPlatformType.iOS ? iOSCrash : AndroidCrash).map(it => (
        <StabilitySubList
          versions={[versions[0], versions[1]]}
          type={it}
          platform={platform}
          key={`${it}_${versions[0].versionCode}_${versions[1].versionCode}`}
          total_value={extractValue(data.find(cur => cur.id === CrashType[it]))}
          deviceLevel={deviceLevel}
        />
      ))}
    </>
  );
};

export default Stability;
