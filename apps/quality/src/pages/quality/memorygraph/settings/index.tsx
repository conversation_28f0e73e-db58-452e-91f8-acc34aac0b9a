import { Toast } from '@douyinfe/semi-ui';
import { startAnalyseMemoryGraph } from '@api/memorygraph';
import { useModel } from '@edenx/runtime/model';
import AppSettingModule from '@/model/appSettingModel';
import { QualityToolsButton, ToolsName } from '@shared/utils/QualityTools';

const MemoryGraphSettings: React.FC = () => {
  const [appSettingState] = useModel(AppSettingModule);

  const startAnalyseButtonOnclick = () => {
    const { aid } = appSettingState.info.businessInfo;
    const { platform } = appSettingState.info;
    const endTime = Math.floor((new Date().getTime() as number) / 1000);
    const startTime = endTime - 3600;
    startAnalyseMemoryGraph({
      data: { aid, platform, startTime, endTime },
    })
      .then(resp => {
        console.log(resp);
        Toast.success('分析完成');
      })
      .catch(err => {
        console.log(err);
        Toast.error(err);
      });
  };

  return (
    <div>
      <QualityToolsButton
        theme="solid"
        toolName={ToolsName.MemoryGraphAnalyze}
        title={'分析最近1小时Memorygraph'}
        type="secondary"
        style={{ marginRight: 8 }}
        onClick={startAnalyseButtonOnclick}
      />
    </div>
  );
};

export default MemoryGraphSettings;
