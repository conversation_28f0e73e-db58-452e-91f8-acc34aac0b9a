import { MemoryGraphIssueDetailRespItem } from '@api/memorygraph';

function getAsUriParameters(data: Record<string, any>): string {
  return Object.keys(data)
    .map(k => `${encodeURIComponent(k)}=${encodeURIComponent(data[k])}`)
    .join('&');
}

function getSlardarBaseUrl(aid: number) {
  const oversea = [3006, 7356].includes(aid);
  const baseParam: Record<string, any> = {
    aid,
    os: 'iOS',
    region: !oversea ? 'cn' : 'maliva',
    lang: 'zh',
    type: 'app',
  };
  if (oversea) {
    baseParam.subregion = 'row';
  }
  const method = !oversea
    ? 'https://slardar.bytedance.net/node/app_detail/'
    : 'https://slardar-us.bytedance.net/node/app_detail/';
  const url = `${method}?${getAsUriParameters(baseParam)}`;
  return url;
}

export function getMemoryGraphUrl(item: MemoryGraphIssueDetailRespItem | undefined): string {
  if (item === undefined) {
    return 'blank';
  }
  let url = getSlardarBaseUrl(item.aid);
  url = `${url}#/perf_v2/memory/case_detail/${item.event_id}`;
  const params = getAsUriParameters({
    params: JSON.stringify({
      crash_time: item.crash_time,
      url: item.url,
      tsList: [
        {
          crash_time: item.crash_time,
          event_id: item.event_id,
          url: item.url,
          insert_time: item.insert_time,
          last_scene: item.last_scene,
          memory_usage: item.memory_usage,
          free_memory_usage: item.free_memory_usage,
        },
      ],
      internal_session_id: item.internal_session_id,
      start_time: 0,
      end_time: 0,
      filters_conditions: {
        type: 'and',
        sub_conditions: [{ op: 'in', values: ['manual', 'cloud_control', 'online'], dimension: 'activate_manner' }],
      },
      group: true,
      is_abandoned: false,
    }).toString(),
  });
  url = `${url}?${params}`;
  return url;
}
