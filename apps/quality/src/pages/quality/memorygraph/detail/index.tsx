import React, { useEffect, useState } from 'react';
import { Divider, Input, Pagination, Select, Space, Table, Typography } from '@douyinfe/semi-ui';
import { IconLink } from '@douyinfe/semi-icons';
import { useModel } from '@edenx/runtime/model';
import AppSettingModule from '@/model/appSettingModel';
import {
  getMemoryGraphDetail,
  getMemoryGraphKeyIssues,
  MemoryGraphIssueDetailResp,
  MemoryGraphKeyIssueResp,
  MemoryGraphKeyIssueRespItem,
} from '@api/memorygraph';
import { useSearchParams } from '@edenx/runtime/router';
import { getMemoryGraphUrl } from '@/pages/quality/memorygraph/utils';
import { MAIN_HOST_HTTPS } from '@pa/shared/dist/src/appSettings/appSettings';
import { QualityToolsButton, ToolsName } from '@shared/utils/QualityTools';

export enum MemoryGraphIssueSearchParams {
  SelectedKeyClass = 'selected_key_class',
  Level = 'level',
  ShowDetail = 'show_detail',
  Degraded = 'degraded',
}

const MemoryGraphIssueList: React.FC = () => {
  const { Text } = Typography;
  const [appSettingState] = useModel(AppSettingModule);
  const [dataSource, setDataSource] = useState<MemoryGraphKeyIssueRespItem[]>([]);
  const [detailDataSource, setDetailDataSource] = useState<MemoryGraphIssueDetailResp>();
  const [searchParams, setSearchParams] = useSearchParams();
  const [showDetail, setShowDetail] = useState<boolean>(false);
  const [level, setLevel] = useState<string | undefined>();
  const [degraded, setDegraded] = useState<string | undefined>();
  const columns = [
    {
      title: '关键对象',
      dataIndex: 'key',
      render: (_: any, row: any) => <div style={{ width: 300 }}>{`${row.key}`}</div>,
    },
    {
      title: '异常数/异常率',
      dataIndex: 'count',
      render: (_: any, row: any) => <div>{`${row.count}/${(row.rate * 100).toFixed(2)}%`}</div>,
      sorter: (a: any, b: any) => (a.count - b.count > 0 ? 1 : -1),
    },
    {
      title: '影响用户数/影响用户率',
      dataIndex: 'user',
      render: (_: any, row: any) => <>{`${row.user_count}/${(row.user_rate * 100).toFixed(2)}%`}</>,
      sorter: (a: any, b: any) => (a.user_count - b.user_count > 0 ? 1 : -1),
    },
    {
      title: '起始版本-结束版本',
      dataIndex: 'version',
      render: (_: any, row: any) => <Space>{`${row.start_version_code}-${row.end_version_code}`}</Space>,
    },
    {
      title: '问题状态',
      dataIndex: 'status',
      render: (_: any, row: any) => <>{`${row.status}`}</>,
    },
    {
      title: 'Owner',
      dataIndex: 'owner',
      render: (_: any, row: any) => <>{`${row.owners}`}</>,
    },
    {
      title: '查看详情',
      dataIndex: 'detail',
      render: (_: any, row: any) => <div>issue详情</div>,
    },
  ];

  const fetchIssueData = () => {
    const { aid } = appSettingState.info.businessInfo;
    const { platform } = appSettingState.info;
    let is_degraded, newLevel;
    if (degraded === '1') {
      is_degraded = true;
    } else if (degraded === '0') {
      is_degraded = false;
    }

    if (level === 'P0') {
      newLevel = 0;
    } else if (level === 'P1') {
      newLevel = 1;
    } else if (level === 'P2') {
      newLevel = 2;
    }
    getMemoryGraphKeyIssues({
      data: { aid, platform, degraded: is_degraded, level: newLevel },
    })
      .then((ret: MemoryGraphKeyIssueResp) => {
        // console.log(`oyq ${JSON.stringify(ret)}`);
        setDataSource(ret.items);
      })
      .catch(err => console.error(err));
  };

  const fetchDetail = (currentPage: number) => {
    const { aid } = appSettingState.info.businessInfo;
    const { platform } = appSettingState.info;
    const selectedKeyClass = searchParams.get(MemoryGraphIssueSearchParams.SelectedKeyClass);
    const is_degraded = degraded === '1';
    let newLevel;
    if (level === 'P0') {
      newLevel = 0;
    } else if (level === 'P1') {
      newLevel = 1;
    } else if (level === 'P2') {
      newLevel = 2;
    }
    getMemoryGraphDetail({
      data: {
        aid,
        platform,
        key: selectedKeyClass ?? '',
        page: currentPage - 1,
        pageSize: 1,
        degraded: is_degraded,
        level: newLevel,
      },
    })
      .then((ret: MemoryGraphIssueDetailResp) => {
        // console.log(`oyq ret ${JSON.stringify(ret)}`);
        if (ret.code === 200) {
          setDetailDataSource(ret);
        }
      })
      .catch(err => console.error(err));
  };

  const fetchData = () => {
    const paramShowDetail = searchParams.get(MemoryGraphIssueSearchParams.ShowDetail) === 'true';
    setShowDetail(paramShowDetail);
    if (paramShowDetail) {
      fetchDetail(1);
    } else {
      fetchIssueData();
    }
  };

  useEffect(() => {
    if (
      appSettingState === undefined ||
      appSettingState.info === undefined ||
      appSettingState.info.businessInfo === undefined
    ) {
      return;
    }

    fetchData();

    const paramLevel = searchParams.get(MemoryGraphIssueSearchParams.Level);
    if (paramLevel) {
      setLevel(paramLevel);
    }
    const paramDegraded = searchParams.get(MemoryGraphIssueSearchParams.Degraded);
    if (paramDegraded) {
      setDegraded(paramDegraded);
    }
  }, [appSettingState, appSettingState.info, appSettingState.info.businessInfo]);

  return (
    <div
      style={{
        display: 'flex',
        flexDirection: 'column',
      }}
    >
      <div
        style={{
          width: '100%',
          minHeight: 80,
          display: 'flex',
          gap: 8,
          alignItems: 'center',
          border: '1px solid #ccc',
          marginBottom: 8,
        }}
      >
        <Input placeholder="KeyClass" size="large" style={{ marginLeft: 8 }} />
        <Input placeholder="App 大版本" size="large" />
        <Input placeholder="App 小版本" size="large" />
        <Input placeholder="Owner" size="large" />
        <Input placeholder="OOM 时间" size="large" />
        <Input placeholder="场景" size="large" />
        <Input placeholder="Channel" size="large" />
        <Input placeholder="Status" size="large" />
        <Select
          placeholder={'优先级'}
          value={level}
          style={{ minWidth: 100 }}
          onSelect={(value: string | number | any[] | Record<string, any> | undefined, option) => {
            setLevel(value as string);
            searchParams.set(MemoryGraphIssueSearchParams.Level, value as string);
            setSearchParams(searchParams);
          }}
        >
          <Select.Option value="P0">P0</Select.Option>
          <Select.Option value="P1">P1</Select.Option>
          <Select.Option value="P2">P2</Select.Option>
        </Select>
        <Select
          placeholder={'是否降级'}
          value={degraded}
          style={{ minWidth: 120 }}
          onSelect={(value: string | number | any[] | Record<string, any> | undefined, option) => {
            setDegraded(value as string);
            searchParams.set(MemoryGraphIssueSearchParams.Degraded, value as string);
            setSearchParams(searchParams);
          }}
        >
          <Select.Option value="1">是</Select.Option>
          <Select.Option value="0">否</Select.Option>
        </Select>
        <Input placeholder="设备类型" size="large" />
        <Input placeholder="OS Version" size="large" />
        <QualityToolsButton
          toolName={ToolsName.MemoryGraphDetail}
          title={'搜索'}
          theme="solid"
          type="primary"
          style={{ marginRight: 8 }}
          onClick={() => {
            fetchData();
          }}
        />
      </div>
      <div style={{ width: '100%', flexGrow: 1 }}>
        {showDetail ? (
          <div style={{ display: 'flex', flexDirection: 'column' }}>
            <div style={{ height: 100, border: '1px solid #ccc', marginBottom: 8 }}>
              <span style={{ fontSize: 20, fontWeight: 'bolder' }}>IssueDetail</span>
            </div>
            <div style={{ height: 100, border: '1px solid #ccc', marginBottom: 8 }}>
              <span style={{ fontSize: 20, fontWeight: 'bolder' }}>NodeDetail</span>
              <Divider margin="12px" />
              <span>class_name</span>
              <div>{detailDataSource?.item?.key}</div>
            </div>
            <div style={{ height: 100, border: '1px solid #ccc' }}>
              <span style={{ fontSize: 20, fontWeight: 'bolder' }}>MemoryGraphInfo</span>
              <Divider margin="12px" />
              <Text
                link={{ href: getMemoryGraphUrl(detailDataSource?.item), target: '_blank' }}
                icon={<IconLink />}
                underline
              >
                Slardar MemoryGraph Url
              </Text>
            </div>
            <Pagination
              total={detailDataSource?.count ?? 0}
              pageSize={1}
              showTotal
              style={{ marginBottom: 12 }}
              onPageChange={page => {
                fetchDetail(page);
              }}
            />
          </div>
        ) : (
          <Table
            columns={columns}
            dataSource={dataSource}
            pagination={false}
            onRow={(record: MemoryGraphKeyIssueRespItem, index: number) => ({
              onClick: () => {
                if (record) {
                  window.open(
                    `${MAIN_HOST_HTTPS}/quality/diagnosis/issue-attribution/memory-graph/detail?${MemoryGraphIssueSearchParams.ShowDetail}=true&${MemoryGraphIssueSearchParams.SelectedKeyClass}=${record.key}&${searchParams.toString()}`,
                  );
                }
              }, // 点击行
            })}
            style={{ border: '1px solid #ccc' }}
          />
        )}
      </div>
    </div>
  );
};

export default MemoryGraphIssueList;
