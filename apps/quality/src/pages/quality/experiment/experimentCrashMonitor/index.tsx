import { Button, DatePicker, Input, message, Radio, Select, Space, Tag } from 'antd';
import { useSearchParams } from '@edenx/runtime/router';
import dayjs, { Dayjs } from 'dayjs';
import React, { useState } from 'react';
import {
  CrashType,
  CrashType2Name,
  ExperimentTableInfo,
  HandleStatus,
  IndicatorType,
  Priority,
} from '@shared/libra/common';
import { forEach, keyBy, map } from 'lodash';
import { useModel } from '@edenx/runtime/model';
import AppSettingModule from '@/model/appSettingModel';
import { FilterConfirmProps } from 'antd/es/table/interface';
import { Avatar, NetworkCode, PlatformType, User } from '@pa/shared/dist/src/core';
import UserCard from '@/component/UserCard';
import { EditableProTable, ProColumns } from '@ant-design/pro-components';
import { SearchOutlined } from '@ant-design/icons';
import {
  createMeegoBug,
  inviteUser2Group,
  QueryExperimentList,
  updateEditableInfo,
  updateErrorStatus,
} from '@api/experiment';
import Link from 'antd/es/typography/Link';
import WhiteListBuilder from '@/component/WhiteListBuilder';
import { SlardarPlatformType } from '@pa/shared/dist/src/appSettings/appSettings';
import { MeegoStatus2Name, Status2Color } from '@shared/typings/meego';
import { QueryOrBuildMeegoView, UpdateMeegoLevel } from '@api/meego';
import UserSettingModule from '@/model/userSettingModel';
import ContrastListSelector from '@/component/WhiteListBuilder/ContrastGroupSelector';
import { LibraPatrolUrlSearchParams } from '@shared/libra/LibraPatrolInfo';

const { RangePicker } = DatePicker;
const { Option } = Select;
const AndroidCrashType = [
  CrashType.JavaCrash,
  CrashType.NativeCrash,
  CrashType.Anr,
  CrashType.LaunchCrash,
  CrashType.OOM,
  CrashType.NativeOOM,
];
const iOSCrashType = [CrashType.Jam, CrashType.Crash, CrashType.Stuck, CrashType.OOM];
type RangeValue = [Dayjs | null, Dayjs | null] | null;

interface ExperimentTableParams {
  update: number;
  pageSize: number;
  current?: number;
  updateValue: [dayjs.Dayjs | null, dayjs.Dayjs | null];
  createValue: [dayjs.Dayjs | null, dayjs.Dayjs | null];
  platform: string;
  sorter?: {
    key: string;
    order: string;
  }[];
  filters?: {
    columnKey: string;
    filter: string[];
  }[];
}

// const titleInput = useRef<InputRef>(null);

const buildSelectOptions = (enumData: typeof HandleStatus | typeof Priority) =>
  keyBy(
    map(enumData, (value: string | number, key) => ({
      text: value.toString(),
      status: key,
    })),
    value => value.text,
  );
// const searchInput = useRef<InputRef>(null);
const ExperimentCrashMonitor: React.FC = () => {
  const [experimentTotal, setExperimentTotal] = useState<number>(0);
  const [loading, setLoading] = useState<boolean>(false);
  const [pageSize, setPageSize] = useState<number>(20);
  const [createDates, setCreateDates] = useState<RangeValue>(null);
  const [createValue, setCreateValue] = useState<[Dayjs | null, Dayjs | null]>([null, null]);
  const [appSettingState] = useModel(AppSettingModule);
  const [updateDates, setUpdateDates] = useState<RangeValue>(null);
  const [updateValue, setUpdateValue] = useState<[Dayjs | null, Dayjs | null]>([null, null]);
  const [userSettings] = useModel(UserSettingModule);
  const [update, setUpdate] = useState<number>(0);
  const [meegoViewing, setMeegoViewing] = useState<boolean>(false);
  const [updating, setUpdating] = useState<boolean>(false);
  const [searchParams, setSearchParams] = useSearchParams();

  const flightIdFromURLSearchParam = () => searchParams.get(LibraPatrolUrlSearchParams.FlightId) ?? '';

  const handleSearch = (selectedKeys: string[], confirm: (param?: FilterConfirmProps) => void) => {
    confirm();
  };
  const handleReset = (clearFilters: () => void, confirm: (param?: FilterConfirmProps) => void) => {
    clearFilters();
    handleSearch([], confirm);
  };
  const [editableKeys, setEditableRowKeys] = useState<React.Key[]>([]);
  const createUserView = (user: User) => {
    const avatar = user.avatar as Avatar;
    return (
      <>
        <UserCard
          email={`${user.email}`}
          simpleUserData={{
            // name: user.name_cn,
            avatarUrl: avatar.avatar_72, // avatar_url,
          }}
          hideName={true}
          triggerType="hover"
        />
      </>
    );
  };
  const [tableInfo, setTableInfo] = useState<readonly ExperimentTableInfo[]>([]);

  function shareCell(row: ExperimentTableInfo, index: number | undefined) {
    if (index && index !== 0 && row.id === tableInfo[index - 1].id) {
      return { rowSpan: 0 };
    }
    let count = 0;
    for (let i = index ? index : 0; i < tableInfo.length; i++) {
      const cur = tableInfo[i];
      if (cur.id === row.id) {
        count++;
      } else {
        break;
      }
    }
    return { rowSpan: count };
  }

  const columns: ProColumns<ExperimentTableInfo>[] = [];
  columns.push(
    {
      title: '实验名称',
      width: '10%',
      editable: false,
      fixed: 'left',
      onCell: shareCell,
      key: 'name',
      render: (_, row) => {
        if (row.name) {
          return (
            <Link href={row.url} target="_blank">
              {row.name}
            </Link>
          );
        } else {
          return <></>;
        }
      },
      filterDropdown: ({ setSelectedKeys, selectedKeys, confirm, clearFilters }) => (
        <div style={{ padding: 8 }}>
          <Input
            placeholder={`Search Title`}
            value={selectedKeys[0]?.toString()}
            onChange={e => setSelectedKeys(e.target.value ? [e.target.value] : [])}
            onPressEnter={() => handleSearch(selectedKeys as string[], confirm)}
            style={{ marginBottom: 8, display: 'block' }}
          />
          <Space>
            <Button
              type="primary"
              onClick={() => handleSearch(selectedKeys as string[], confirm)}
              icon={<SearchOutlined />}
              size="small"
              style={{ width: 90 }}
            >
              Search
            </Button>
            <Button
              onClick={() => clearFilters && handleReset(clearFilters, confirm)}
              size="small"
              style={{ width: 90 }}
            >
              Reset
            </Button>
          </Space>
        </div>
      ),
      filterIcon: (filtered: boolean) => (
        <>
          <SearchOutlined style={{ color: filtered ? '#1890ff' : `#000` }} />
        </>
      ),
    },
    {
      title: '实验id',
      width: '5%',
      editable: false,
      fixed: true,
      key: 'id',
      onCell: shareCell,
      dataIndex: 'id',
      filterDropdown: ({ setSelectedKeys, selectedKeys, confirm, clearFilters }) => (
        <div style={{ padding: 8 }}>
          <Input
            placeholder={`Search id`}
            value={selectedKeys[0]?.toString() ?? flightIdFromURLSearchParam()}
            onChange={e => setSelectedKeys(e.target.value ? [e.target.value] : [])}
            onPressEnter={() => handleSearch(selectedKeys as string[], confirm)}
            style={{ marginBottom: 8, display: 'block' }}
          />
          <Space>
            <Button
              type="primary"
              onClick={() => {
                // 完成搜索
                handleSearch(selectedKeys as string[], confirm);
                // 添加 search param 路径
                searchParams.set(LibraPatrolUrlSearchParams.FlightId, selectedKeys[0]?.toString() ?? '');
                setSearchParams(searchParams);
              }}
              icon={<SearchOutlined />}
              size="small"
              style={{ width: 90 }}
            >
              Search
            </Button>
            <Button
              onClick={() => {
                // 删除 search param 路径
                searchParams.delete(LibraPatrolUrlSearchParams.FlightId);
                setSearchParams(searchParams);
                // 完成重置
                clearFilters && handleReset(clearFilters, confirm);
              }}
              size="small"
              style={{ width: 90 }}
            >
              Reset
            </Button>
          </Space>
        </div>
      ),
      filterIcon: (filtered: boolean) => (
        <>
          <SearchOutlined style={{ color: filtered || flightIdFromURLSearchParam().length > 0 ? '#1890ff' : `#000` }} />
        </>
      ),
    },
    {
      title: 'vid',
      width: '8%',
      editable: false,
      dataIndex: 'vId',
      key: 'vId',
      align: 'center',
      render: (_, record) => <>{record.vId}</>,
    },
  );
  for (const type of appSettingState.info.platform === PlatformType.Android ? AndroidCrashType : iOSCrashType) {
    columns.push({
      title: CrashType2Name[type],
      width: '10%',
      editable: false,
      align: 'center',
      key: 'detail',
      dataIndex: 'detail',
      render: (_, row) =>
        type in row.data ? (
          <Link
            href={row.data[type].url}
            style={{
              color: row.data[type]?.hasError === true ? 'red' : 'black',
              fontWeight: row.data[type]?.hasError === true ? 'bold' : 'normal',
            }}
            target={'_blank'}
          >
            {(row.data[type]?.value && row.data[type].value !== -1) || row.data[type]?.value === 0
              ? row.data[type]?.value
              : '暂无'}{' '}
            ({row.data[type]?.valueSum ? row.data[type]?.valueSum : '暂无'})
          </Link>
        ) : (
          <>暂无</>
        ),
    });
  }
  columns.push(
    {
      title: '实验组白名单',
      width: '5%',
      editable: false,
      filters: true,
      dataIndex: 'vId',
      key: 'safeTime',
      align: 'center',
      render: (_, record) => (
        <WhiteListBuilder
          time={record.vidSafeTime}
          appId={appSettingState.info.id}
          id={record.id}
          indicatorType={IndicatorType.Crash}
          vid={record.vId.toString()}
          vids={record.vIds}
          setUpdate={setUpdate}
        />
      ),
    },
    {
      title: '自选对照组',
      width: '5%',
      editable: false,
      onCell: shareCell,
      filters: true,
      dataIndex: 'vId',
      key: 'safeTime',
      align: 'center',
      render: (_, record) => (
        <ContrastListSelector
          appId={appSettingState.info.id}
          id={record.id}
          indicatorType={IndicatorType.Crash}
          defaultVid={record.contrastGroup}
          vids={record.vIds}
        />
      ),
    },
    {
      title: '白名单',
      width: '5%',
      editable: false,
      filters: true,
      onCell: shareCell,
      dataIndex: 'safeTime',
      key: 'safeTime',
      onFilter: false,
      valueEnum: { 1: { text: '是' }, 0: { text: '否' } },
      align: 'center',
      filterDropdown: ({ setSelectedKeys, selectedKeys, confirm, clearFilters }) => (
        <div style={{ padding: 8 }}>
          <Radio.Group
            onChange={e => {
              setSelectedKeys([e.target.value]);
              confirm();
            }}
            value={selectedKeys[0]}
          >
            <Radio value={1}>是</Radio>
            <Radio value={0}>否</Radio>
          </Radio.Group>
          <Button
            type="link"
            size="small"
            onClick={() => {
              setSelectedKeys([]);
              clearFilters?.();
              confirm();
            }}
          >
            重置
          </Button>
        </div>
      ),
      render: (_, record) => (
        <WhiteListBuilder
          time={record.safeTime}
          appId={appSettingState.info.id}
          id={record.id}
          indicatorType={IndicatorType.Crash}
          vids={record.vIds}
          setUpdate={setUpdate}
        />
      ),
    },
    {
      align: 'center',
      dataIndex: 'priority',
      filters: true,
      onCell: shareCell,
      editable: false,
      key: 'priority',
      onFilter: false,
      render: (_, row) => {
        if (row.priority === Priority.P0) {
          return <Tag color="error">P0 </Tag>;
        } else if (row.priority === Priority.P1) {
          return <Tag color="yellow">P1 </Tag>;
        } else if (row.priority === Priority.P2) {
          return <Tag color="blue">P2 </Tag>;
        } else if (row.priority === Priority.P3) {
          return <Tag color="gray">P3 </Tag>;
        } else {
          return <Tag color="gray">未定义 </Tag>;
        }
      },
      title: '风险等级',
      width: '5%',
    },
    {
      title: '误报',
      width: '5%',
      align: 'center',
      onCell: shareCell,
      editable: false,
      filters: true,
      key: 'isError',
      dataIndex: 'isError',
      onFilter: false,
      render: (_, row) => {
        if (row.isError) {
          return <Tag color="yellow">是</Tag>;
        } else {
          return <Tag color={'gray'}>否</Tag>;
        }
      },
    },
    {
      title: 'Meego 状态',
      width: '7%',
      align: 'center',
      onCell: shareCell,
      editable: false,
      filters: true,
      key: 'meego_status',
      dataIndex: 'meego_status',
      onFilter: false,
      render: (_, row) => {
        if (row.meego_status) {
          return <Tag color={Status2Color[row.meego_status]}>{MeegoStatus2Name[row.meego_status]}</Tag>;
        } else {
          return <Tag color={'gray'}>未提单</Tag>;
        }
      },
    },
    {
      title: 'Meego 优先级',
      width: '7%',
      align: 'center',
      onCell: shareCell,
      filters: true,
      key: 'meego_level',
      editable: false,
      dataIndex: 'meego_level',
      onFilter: false,
      render: (_, row) =>
        row.meego_level !== undefined ? (
          <Select
            defaultValue={row.meego_level}
            onChange={checked => {
              UpdateMeegoLevel({
                data: {
                  meego_url: `https://meego.feishu.cn/faceu/issue/detail/${row.meegoId}`,
                  priority: checked,
                  platform:
                    appSettingState.info.platform === PlatformType.Android
                      ? SlardarPlatformType.Android
                      : SlardarPlatformType.iOS,
                  version_code: '',
                  operator: userSettings.info.email,
                },
              }).then(v => {
                if (v.code === 'success') {
                  message.success('更改优先级成功');
                } else {
                  message.error('更新优先级失败');
                }
              });
              console.log(checked);
            }}
          >
            <Option value={0}>P0</Option>
            <Option value={1}>P1</Option>
            <Option value={2}>P2</Option>
            <Option value={3}>P3</Option>
          </Select>
        ) : (
          <Tag color={'gray'}>未提单</Tag>
        ),
    },
    {
      title: '跟进备注',
      width: '15%',
      onCell: shareCell,
      align: 'center',
      valueType: 'textarea',
      key: 'remark',
      dataIndex: 'remark',
    },
    {
      title: '操作',
      valueType: 'option',
      key: 'rowId',
      align: 'center',
      onCell: shareCell,
      width: '20%',
      render: (z, record, _, action) => (
        <Space>
          <Button
            key="editable"
            onClick={() => {
              action?.startEditable?.(record.rowId);
            }}
          >
            备注
          </Button>
          {record.meegoId ? (
            <>
              <Button
                loading={loading}
                onClick={() => {
                  setLoading(true);
                  inviteUser2Group({
                    data: { chatId: record.chatId!, email: userSettings.info.email },
                  }).then(r => {
                    if (r.code === NetworkCode.Success) {
                      message.success('已加入');
                      window.open(`https://applink.feishu.cn/client/chat/open?openChatId=${record.chatId}`);
                    } else {
                      message.error('请刷新重试');
                    }
                    setLoading(false);
                  });
                }}
              >
                加群
              </Button>
              <Button href={`https://meego.feishu.cn/faceu/issue/detail/${record.meegoId}`} target={'_blank'}>
                跳转 Meego
              </Button>
            </>
          ) : (
            <Button
              loading={loading}
              onClick={() => {
                setLoading(true);
                createMeegoBug({
                  data: {
                    id: record.id,
                    indicatorType: IndicatorType.Crash,
                    appId: appSettingState.info.id,
                  },
                }).then(r => {
                  if (r.code === NetworkCode.Success) {
                    setUpdate(dayjs().valueOf());
                  } else {
                    message.error('请刷新重试');
                  }
                  setLoading(false);
                });
              }}
            >
              关联Meego
            </Button>
          )}
        </Space>
      ),
    },
    {
      title: '首告时间',
      key: 'createTime',
      align: 'center',
      onCell: shareCell,
      width: '10%',
      editable: false,
      render: (_, info) => {
        if (!info.createTime) {
          return <>暂无</>;
        }
        const date = new Date(info.createTime);
        const dateFormatter = new Intl.DateTimeFormat('zh-CN', {
          timeZone: 'Asia/Shanghai',
          year: 'numeric',
          month: '2-digit',
          day: '2-digit',
        });
        const timeFormatter = new Intl.DateTimeFormat('zh-CN', {
          timeZone: 'Asia/Shanghai',
          hour: '2-digit',
          minute: '2-digit',
          second: '2-digit',
        });
        const formattedDate = {
          data: dateFormatter.format(date),
          time: timeFormatter.format(date),
        };
        return (
          <Space direction="vertical">
            <span>{formattedDate.data}</span>
            <span>{formattedDate.time}</span>
          </Space>
        );
      },
      filterDropdown: () => (
        <RangePicker
          showTime={{ format: 'HH:mm' }}
          format="YYYY-MM-DD HH:mm"
          value={createDates || createValue}
          defaultValue={[null, null]}
          onOk={val => {
            if (val && val[0] && val[1]) {
              setCreateValue([val[0], val[1]]);
            }
          }}
          disabledDate={cur =>
            !createDates || !createDates[0]
              ? cur.isAfter(dayjs().endOf('d'))
              : cur.isAfter(dayjs().endOf('d')) || cur.subtract(30, 'd').startOf('d').isAfter(createDates[0])
          }
          onOpenChange={isOpen => setCreateDates(isOpen ? [null, null] : null)}
          onCalendarChange={val => {
            setCreateDates(val);
            if (!val) {
              setCreateValue([null, null]);
            }
          }}
        />
      ),
    },
    {
      title: '更新时间',
      key: 'newErrorTime',
      align: 'center',
      width: '10%',
      onCell: shareCell,
      editable: false,
      render: (_, info) => {
        if (!info.newErrorTime) {
          return <>暂无</>;
        }
        const date = new Date(info.newErrorTime);

        const dateFormatter = new Intl.DateTimeFormat('zh-CN', {
          timeZone: 'Asia/Shanghai',
          year: 'numeric',
          month: '2-digit',
          day: '2-digit',
        });
        const timeFormatter = new Intl.DateTimeFormat('zh-CN', {
          timeZone: 'Asia/Shanghai',
          hour: '2-digit',
          minute: '2-digit',
          second: '2-digit',
        });
        const formattedDate = {
          data: dateFormatter.format(date),
          time: timeFormatter.format(date),
        };
        return (
          <Space direction="vertical">
            <span>{formattedDate.data}</span>
            <span>{formattedDate.time}</span>
          </Space>
        );
      },
      filterDropdown: () => (
        <RangePicker
          showTime={{ format: 'HH:mm' }}
          format="YYYY-MM-DD HH:mm"
          value={updateDates || updateValue}
          defaultValue={[null, null]}
          onOk={val => {
            if (val && val[0] && val[1]) {
              setUpdateValue([val[0], val[1]]);
            }
          }}
          disabledDate={cur =>
            !updateDates || !updateDates[0]
              ? cur.isAfter(dayjs().endOf('d'))
              : cur.isAfter(dayjs().endOf('d')) || cur.subtract(30, 'd').startOf('d').isAfter(updateDates[0])
          }
          onOpenChange={isOpen => setUpdateDates(isOpen ? [null, null] : null)}
          onCalendarChange={val => {
            setUpdateDates(val);
            if (!val) {
              setUpdateValue([null, null]);
            }
          }}
        />
      ),
    },
    {
      title: 'Owner',
      width: '10%',
      editable: false,
      key: 'rowId',
      onCell: shareCell,
      render: (_, row) => {
        const users = row.ownerInfo.map((user, index) => {
          const userView = createUserView(user);
          if ((index + 1) % 3 === 0) {
            return (
              <React.Fragment key={index}>
                {userView}
                <br />
              </React.Fragment>
            );
          } else {
            return <React.Fragment key={index}>{userView}</React.Fragment>;
          }
        });

        return (
          <div>
            <Space size={[6, 0]} wrap>
              {users}
            </Space>
          </div>
        );
      },
    },
  );

  const BuildMeegoView = () => {
    setMeegoViewing(true);
    QueryOrBuildMeegoView({
      data: {
        aid: appSettingState.info.businessInfo.aid,
        version: 'ALL',
        platform:
          appSettingState.info.platform === PlatformType.Android
            ? SlardarPlatformType.Android
            : SlardarPlatformType.iOS,
        category: [appSettingState.info.id.toString(), 'Crash'],
      },
    }).then(value => {
      if (value.err_msg || !value.view_id) {
        message.error(value.err_msg ? value.err_msg : '存在被删除缺陷单，联系 zhanglinwei.yimu 解决');
      } else {
        window.open(`https://meego.feishu.cn/faceu/issueView/${value.view_id}`, '_blank');
      }
      setMeegoViewing(false);
    });
  };
  const toolbar = {
    filter: (
      <Space>
        <Button
          key="broadcast"
          loading={updating}
          onClick={async () => {
            setUpdating(true);
            await updateErrorStatus();
            setUpdating(false);
            setUpdate(dayjs().valueOf());
          }}
        >
          更新误报状态
        </Button>
        <Button key="broadcast" onClick={BuildMeegoView} color={'bule'} loading={meegoViewing}>
          Meego 视图
        </Button>
      </Space>
    ),
  };
  return (
    <>
      <EditableProTable<ExperimentTableInfo, ExperimentTableParams>
        columns={columns}
        value={tableInfo}
        headerTitle={`实验总数: ${experimentTotal}`}
        toolbar={toolbar}
        params={{
          updateValue,
          createValue,
          platform: appSettingState.info.platform,
          pageSize,
          update,
        }}
        scroll={{
          x: 2500,
          y: 770,
          scrollToFirstRowOnChange: true,
        }}
        rowKey="rowId"
        // scroll={{
        //   x: 2500,
        //   y: 770,
        //   scrollToFirstRowOnChange: true,
        // }}
        pagination={{
          pageSize,
          pageSizeOptions: [20, 40, 60, 80],
          onShowSizeChange: (current, size) => {
            setPageSize(size);
          },
          showSizeChanger: true,
        }}
        onLoad={(dataSource: ExperimentTableInfo[]) => {
          setTableInfo(dataSource);
        }}
        recordCreatorProps={false}
        request={async (params, sorter, filters) => {
          console.log(filters);
          console.log(params);
          const filtersInfo: {
            columnKey: string;
            filter: any[];
          }[] = [];
          if (createValue[0] && createValue[1]) {
            filters.createTime = [createValue[0].valueOf(), createValue[1].valueOf()];
          } else {
            filters.createTime = null;
          }
          if (updateValue[0] && updateValue[1]) {
            filters.newErrorTime = [updateValue[0].valueOf(), updateValue[1].valueOf()];
          } else {
            filters.newErrorTime = null;
          }
          // 读取实验 id 筛选
          const urlFlightId = searchParams.get(LibraPatrolUrlSearchParams.FlightId);
          filters.id = urlFlightId ? [urlFlightId] : null;
          const current = params.current ? params.current : 1;
          forEach(filters, (filter, key) => {
            if (filter) {
              filtersInfo.push({
                columnKey: key,
                filter: filter.map(value => `${value}`),
              });
            }
          });
          console.log('filterInfo', filtersInfo);
          console.log(appSettingState.info.id);
          const result = await QueryExperimentList({
            data: {
              appId: appSettingState.info.id,
              indicatorType: IndicatorType.Crash,
              pageSize,
              current,
              filters: filtersInfo,
            },
          });
          setTableInfo(result.data as ExperimentTableInfo[]);
          setExperimentTotal(result.experimentTotal);
          return {
            data: result.data as ExperimentTableInfo[],
            total: result.total,
            success: true,
          };
        }}
        onChange={setTableInfo}
        editable={{
          type: 'single',
          editableKeys,
          actionRender: (row, _, defaultDoms) => [defaultDoms.save, defaultDoms.cancel],
          onSave: async (_, record) => {
            const result = await updateEditableInfo({
              data: {
                id: record.id,
                appId: appSettingState.info.id,
                indicatorType: IndicatorType.Crash,
                handleStatus: record.handleStatus,
                priority: record.priority,
                remark: record.remark,
              },
            });
            if (result.code !== NetworkCode.Success) {
              // await message.error(result.message);
            } else {
              await message.success('更新成功');
            }
          },
          onChange: setEditableRowKeys,
        }}
      />
    </>
  );
};

export default ExperimentCrashMonitor;
