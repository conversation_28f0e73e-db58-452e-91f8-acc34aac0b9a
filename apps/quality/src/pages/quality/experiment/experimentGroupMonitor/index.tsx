import { Button, DatePicker, Input, message, Space, Tag } from 'antd';
import dayjs, { Dayjs } from 'dayjs';
import React, { useState } from 'react';
import { ExperimentGroupTable, HandleStatus, IndicatorType, Priority } from '@shared/libra/common';
import { forEach, keyBy, map } from 'lodash';
import { useModel } from '@edenx/runtime/model';
import AppSettingModule from '@/model/appSettingModel';
import { FilterConfirmProps } from 'antd/es/table/interface';
import { Avatar, NetworkCode, User } from '@pa/shared/dist/src/core';
import UserCard from '@/component/UserCard';
import { EditableProTable, ProColumns } from '@ant-design/pro-components';
import { SearchOutlined } from '@ant-design/icons';
import { QueryExperimentList, updateEditableInfo } from '@api/experiment';
import Link from 'antd/es/typography/Link';

const { RangePicker } = DatePicker;

type DataItem = {
  name: string;
  state: string;
};

type RangeValue = [Dayjs | null, Dayjs | null] | null;

interface ExperimentTableParams {
  pageSize: number;
  current?: number;
  updateValue: [dayjs.Dayjs | null, dayjs.Dayjs | null];
  createValue: [dayjs.Dayjs | null, dayjs.Dayjs | null];
  platform: string;
  sorter?: {
    key: string;
    order: string;
  }[];
  filters?: {
    columnKey: string;
    filter: string[];
  }[];
}

// const titleInput = useRef<InputRef>(null);

const buildSelectOptions = (enumData: typeof HandleStatus | typeof Priority) =>
  keyBy(
    map(enumData, (value: string | number, key) => ({
      text: value.toString(),
      status: key,
    })),
    value => value.text,
  );
// const searchInput = useRef<InputRef>(null);
const ExperimentGroupMonitor: React.FC = () => {
  const [experimentTotal, setExperimentTotal] = useState<number>(0);
  const [loading, setLoading] = useState<boolean>(false);
  const [pageSize, setPageSize] = useState<number>(20);
  const [createDates, setCreateDates] = useState<RangeValue>(null);
  const [createValue, setCreateValue] = useState<[Dayjs | null, Dayjs | null]>([null, null]);
  const [appSettingState] = useModel(AppSettingModule);
  const [updateDates, setUpdateDates] = useState<RangeValue>(null);
  const [updateValue, setUpdateValue] = useState<[Dayjs | null, Dayjs | null]>([null, null]);
  const handleSearch = (selectedKeys: string[], confirm: (param?: FilterConfirmProps) => void) => {
    confirm();
  };
  const handleReset = (clearFilters: () => void, confirm: (param?: FilterConfirmProps) => void) => {
    clearFilters();
    handleSearch([], confirm);
  };
  const [editableKeys, setEditableRowKeys] = useState<React.Key[]>([]);
  const createUserView = (user: User) => {
    const avatar = user.avatar as Avatar;
    return (
      <>
        <UserCard
          email={`${user.email}`}
          simpleUserData={{
            // name: user.name_cn,
            avatarUrl: avatar.avatar_72, // avatar_url,
          }}
          hideName={true}
          triggerType="hover"
        />
      </>
    );
  };
  const [tableInfo, setTableInfo] = useState<readonly ExperimentGroupTable[]>([]);

  const columns: ProColumns<ExperimentGroupTable>[] = [];
  columns.push(
    {
      title: '实验名称',
      width: '5%',
      editable: false,
      fixed: 'left',
      key: 'name',
      render: (_, row) => {
        if (row.name) {
          return (
            <Link href={row.url} target="_blank">
              {row.name}
            </Link>
          );
        } else {
          return <></>;
        }
      },
      filterDropdown: ({ setSelectedKeys, selectedKeys, confirm, clearFilters }) => (
        <div style={{ padding: 8 }}>
          <Input
            placeholder={`Search Title`}
            value={selectedKeys[0]?.toString()}
            onChange={e => setSelectedKeys(e.target.value ? [e.target.value] : [])}
            onPressEnter={() => handleSearch(selectedKeys as string[], confirm)}
            style={{ marginBottom: 8, display: 'block' }}
          />
          <Space>
            <Button
              type="primary"
              onClick={() => handleSearch(selectedKeys as string[], confirm)}
              icon={<SearchOutlined />}
              size="small"
              style={{ width: 90 }}
            >
              Search
            </Button>
            <Button
              onClick={() => clearFilters && handleReset(clearFilters, confirm)}
              size="small"
              style={{ width: 90 }}
            >
              Reset
            </Button>
          </Space>
        </div>
      ),
      filterIcon: (filtered: boolean) => (
        <>
          <SearchOutlined style={{ color: filtered ? '#1890ff' : `#000` }} />
        </>
      ),
    },
    {
      title: '实验id',
      width: '5%',
      editable: false,
      fixed: true,
      key: 'id',
      dataIndex: 'id',
      filterDropdown: ({ setSelectedKeys, selectedKeys, confirm, clearFilters }) => (
        <div style={{ padding: 8 }}>
          <Input
            placeholder={`Search id`}
            value={selectedKeys[0]?.toString()}
            onChange={e => setSelectedKeys(e.target.value ? [e.target.value] : [])}
            onPressEnter={() => handleSearch(selectedKeys as string[], confirm)}
            style={{ marginBottom: 8, display: 'block' }}
          />
          <Space>
            <Button
              type="primary"
              onClick={() => handleSearch(selectedKeys as string[], confirm)}
              icon={<SearchOutlined />}
              size="small"
              style={{ width: 90 }}
            >
              Search
            </Button>
            <Button
              onClick={() => clearFilters && handleReset(clearFilters, confirm)}
              size="small"
              style={{ width: 90 }}
            >
              Reset
            </Button>
          </Space>
        </div>
      ),
      filterIcon: (filtered: boolean) => (
        <>
          <SearchOutlined style={{ color: filtered ? '#1890ff' : `#000` }} />
        </>
      ),
    },
  );
  columns.push({
    title: '开启天数',
    width: '5%',
    editable: false,
    align: 'center',
    key: 'day',
    dataIndex: 'day',
  });
  columns.push({
    title: '异常类型',
    width: '8%',
    editable: false,
    align: 'center',
    key: 'msg',
    dataIndex: 'msg',
    render: (_, row) => {
      const tag = [];
      for (const d of row.msg) {
        tag.push(
          <span>
            {d.split('\n').map((line, index) => (
              <div key={index}>{line}</div>
            ))}
          </span>,
        );
      }
      return tag;
    },
  });
  columns.push({
    title: '异常详情',
    width: '10%',
    editable: false,
    align: 'center',
    key: 'detail',
    dataIndex: 'detail',
    render: (_, row) => (
      <span>
        {row.detail.split('\n').map((line, index) => (
          <div key={index}>{line}</div>
        ))}
      </span>
    ),
  });
  columns.push(
    {
      align: 'center',
      dataIndex: 'priority',
      filters: true,
      key: 'priority',
      onFilter: false,
      render: (_, row) => {
        if (row.priority === Priority.P0) {
          return <Tag color="error">P0 </Tag>;
        } else if (row.priority === Priority.P1) {
          return <Tag color="yellow">P1 </Tag>;
        } else if (row.priority === Priority.P2) {
          return <Tag color="blue">P2 </Tag>;
        } else {
          return <Tag color="gray">未定义</Tag>;
        }
      },
      title: '风险等级',
      valueEnum: buildSelectOptions(Priority),
      valueType: 'select',
      width: '5%',
    },
    {
      title: '跟进状态',
      width: '5%',
      valueType: 'select',
      align: 'center',
      filters: true,
      key: 'handleStatus',
      dataIndex: 'handleStatus',
      onFilter: false,
      valueEnum: buildSelectOptions(HandleStatus),
      render: (_, row) => {
        if (row.handleStatus === HandleStatus.Error) {
          return <Tag color="yellow">误报 </Tag>;
        } else if (row.handleStatus === HandleStatus.Closed) {
          return <Tag color="blue">已关闭 </Tag>;
        } else if (row.handleStatus === HandleStatus.Suspend) {
          return <Tag color="pink">暂不处理 </Tag>;
        } else if (row.handleStatus === HandleStatus.Handling) {
          return <Tag color="purple">跟进中 </Tag>;
        } else if (row.handleStatus === HandleStatus.LongTerm) {
          return <Tag color="green">长期跟进 </Tag>;
        } else {
          return <Tag color="gray">待处理 </Tag>;
        }
      },
    },
    {
      title: '跟进备注',
      width: '15%',
      align: 'center',
      valueType: 'textarea',
      key: 'remark',
      dataIndex: 'remark',
    },
    {
      title: '首告时间',
      key: 'createTime',
      align: 'center',
      width: '5%',
      editable: false,
      render: (_, info) => {
        if (!info.createTime) {
          return <>暂无</>;
        }
        const date = new Date(info.createTime);
        const dateFormatter = new Intl.DateTimeFormat('zh-CN', {
          timeZone: 'Asia/Shanghai',
          year: 'numeric',
          month: '2-digit',
          day: '2-digit',
        });
        const timeFormatter = new Intl.DateTimeFormat('zh-CN', {
          timeZone: 'Asia/Shanghai',
          hour: '2-digit',
          minute: '2-digit',
          second: '2-digit',
        });
        const formattedDate = {
          data: dateFormatter.format(date),
          time: timeFormatter.format(date),
        };
        return (
          <Space direction="vertical">
            <span>{formattedDate.data}</span>
            <span>{formattedDate.time}</span>
          </Space>
        );
      },
      filterDropdown: () => (
        <RangePicker
          showTime={{ format: 'HH:mm' }}
          format="YYYY-MM-DD HH:mm"
          value={createDates || createValue}
          defaultValue={[null, null]}
          onOk={val => {
            if (val && val[0] && val[1]) {
              console.log('CHANGED');
              setCreateValue([val[0], val[1]]);
            }
          }}
          disabledDate={cur =>
            !createDates || !createDates[0]
              ? cur.isAfter(dayjs().endOf('d'))
              : cur.isAfter(dayjs().endOf('d')) || cur.subtract(30, 'd').startOf('d').isAfter(createDates[0])
          }
          onOpenChange={isOpen => setCreateDates(isOpen ? [null, null] : null)}
          onCalendarChange={val => {
            setCreateDates(val);
            if (!val) {
              setCreateValue([null, null]);
            }
          }}
        />
      ),
    },
    {
      title: '更新时间',
      key: 'newErrorTime',
      align: 'center',
      width: '5%',
      editable: false,
      render: (_, info) => {
        if (!info.newErrorTime) {
          return <>暂无</>;
        }
        const date = new Date(info.newErrorTime);

        const dateFormatter = new Intl.DateTimeFormat('zh-CN', {
          timeZone: 'Asia/Shanghai',
          year: 'numeric',
          month: '2-digit',
          day: '2-digit',
        });
        const timeFormatter = new Intl.DateTimeFormat('zh-CN', {
          timeZone: 'Asia/Shanghai',
          hour: '2-digit',
          minute: '2-digit',
          second: '2-digit',
        });
        const formattedDate = {
          data: dateFormatter.format(date),
          time: timeFormatter.format(date),
        };
        return (
          <Space direction="vertical">
            <span>{formattedDate.data}</span>
            <span>{formattedDate.time}</span>
          </Space>
        );
      },
      filterDropdown: () => (
        <RangePicker
          showTime={{ format: 'HH:mm' }}
          format="YYYY-MM-DD HH:mm"
          value={updateDates || updateValue}
          defaultValue={[null, null]}
          onOk={val => {
            if (val && val[0] && val[1]) {
              console.log('CHANGED');
              setUpdateValue([val[0], val[1]]);
            }
          }}
          disabledDate={cur =>
            !updateDates || !updateDates[0]
              ? cur.isAfter(dayjs().endOf('d'))
              : cur.isAfter(dayjs().endOf('d')) || cur.subtract(30, 'd').startOf('d').isAfter(updateDates[0])
          }
          onOpenChange={isOpen => setUpdateDates(isOpen ? [null, null] : null)}
          onCalendarChange={val => {
            setUpdateDates(val);
            if (!val) {
              setUpdateValue([null, null]);
            }
          }}
        />
      ),
    },
    {
      title: 'Owner',
      width: '8%',
      editable: false,
      key: 'rowId',
      render: (_, row) => {
        const users = row.ownerInfo.map((user, index) => {
          const userView = createUserView(user);
          if ((index + 1) % 3 === 0) {
            return (
              <React.Fragment key={index}>
                {userView}
                <br />
              </React.Fragment>
            );
          } else {
            return <React.Fragment key={index}>{userView}</React.Fragment>;
          }
        });

        return (
          <div>
            <Space size={[6, 0]} wrap>
              {users}
            </Space>
          </div>
        );
      },
    },
    // {
    // 	title: "操作",
    // 	valueType: "option",
    // 	key: "rowId",
    // 	align: "left",
    // 	width: "12%",
    // 	render: (z, record, _, action) => [
    // 		<Button
    // 			key="editable"
    // 			onClick={() => {
    // 				action?.startEditable?.(record.rowId);
    // 			}}
    // 		>
    // 			编辑
    // 		</Button>,
    // 		record.chatId ? (
    // 			<Button
    // 				loading={loading}
    // 				onClick={() => {
    // 					setLoading(true);
    // 					addUserToLarkChat({ data: { data: record.chatId } }).then((r: NetworkResult<string>) => {
    // 						if (r.code === NetworkCode.Success) {
    // 							message.success("已加入");
    // 							window.open(
    // 								`https://applink.feishu.cn/client/chat/open?openChatId=${record.chatId}`
    // 							);
    // 						} else {
    // 							message.error(r.message);
    // 							message.error("请刷新重试");
    // 							addExperimentLarkGroup({
    // 								data: { id: record.id, chatId: undefined },
    // 							});
    // 						}
    // 						setLoading(false);
    // 					});
    // 				}}
    // 			>
    // 				加入普通群
    // 			</Button>
    // 		) : (
    // 			<Button
    // 				loading={loading}
    // 				onClick={() => {
    // 					setLoading(true);
    // 					const emails: string[] = [];
    // 					record.ownerInfo.forEach((owner) => {
    // 						if (owner.email) {
    // 							emails.push(owner.email);
    // 						}
    // 					});
    // 					if (emails) {
    // 						createExperimentLarkGroup({
    // 							data: { name: record.name, emails },
    // 						}).then((res) => {
    // 							if (res.message) {
    // 								addExperimentLarkGroup({
    // 									data: { id: record.id, chatId: res.message },
    // 								});
    // 								message.success("拉群成功");
    // 							} else {
    // 								message.error("拉群失败，请重试");
    // 							}
    // 							setLoading(false);
    // 						});
    // 					} else {
    // 						message.error("缺少owner信息");
    // 					}
    // 				}}
    // 			>
    // 				拉普通群
    // 			</Button>
    // 		),
    // 		record.oncallId ? (
    // 			<Button
    // 				loading={loading}
    // 				onClick={() => {
    // 					setLoading(true);
    // 					if (record.oncallId) {
    // 						console.log(record.oncallId);
    // 						addOncallGroup({ data: { chatId: record.oncallId } }).then(() => setLoading(false));
    // 						chatId2Open({ data: { chatId: record.oncallId } }).then((res) =>
    // 							window.open(`https://applink.feishu.cn/client/chat/open?openChatId=${res.openId}`)
    // 						);
    // 					} else {
    // 						message.error("oncall群不存在，请刷新重试");
    // 					}
    // 					setLoading(false);
    // 				}}
    // 			>
    // 				加入oncall群
    // 			</Button>
    // 		) : (
    // 			<OncallForm
    // 				emails={record.ownerInfo.map((res) => res.email)}
    // 				groupName={`${record.name}实验劣化`}
    // 				id={record.id}
    // 				openIds={record.ownerInfo.map((res) => res.open_id)}
    // 				platform={appSettingState.info.platform}
    // 				priority={record.priority}
    // 				product={appSettingState.info.name === "剪映" ? "lv" : "cc"}
    // 				url={record.url}
    // 			/>
    // 		),
    //	],
    // }
  );

  return (
    <>
      <EditableProTable<ExperimentGroupTable, ExperimentTableParams>
        columns={columns}
        value={tableInfo}
        headerTitle={`实验总数: ${experimentTotal}`}
        params={{
          updateValue,
          createValue,
          platform: appSettingState.info.name,
          pageSize,
        }}
        scroll={{
          x: 2500,
          y: 770,
          scrollToFirstRowOnChange: true,
        }}
        rowKey="id"
        // scroll={{
        //   x: 2500,
        //   y: 770,
        //   scrollToFirstRowOnChange: true,
        // }}
        pagination={{
          pageSize,
          pageSizeOptions: [20, 40, 60, 80],
          onShowSizeChange: (current, size) => {
            setPageSize(size);
          },
          showSizeChanger: true,
        }}
        onLoad={(dataSource: ExperimentGroupTable[]) => {
          setTableInfo(dataSource);
        }}
        recordCreatorProps={false}
        request={async (params, sorter, filters) => {
          console.log(filters);
          console.log(params);
          const filtersInfo: {
            columnKey: string;
            filter: any[];
          }[] = [];
          if (createValue[0] && createValue[1]) {
            filters.createTime = [createValue[0].valueOf(), createValue[1].valueOf()];
          } else {
            filters.createTime = null;
          }
          if (updateValue[0] && updateValue[1]) {
            filters.newErrorTime = [updateValue[0].valueOf(), updateValue[1].valueOf()];
          } else {
            filters.newErrorTime = null;
          }
          const current = params.current ? params.current : 1;
          forEach(filters, (filter, key) => {
            if (filter) {
              filtersInfo.push({
                columnKey: key,
                filter: filter.map(value => `${value}`),
              });
            }
          });
          console.log('filterInfo', filtersInfo);
          console.log(Math.floor(appSettingState.info.id / 100));
          const result = await QueryExperimentList({
            data: {
              appId: Math.floor(appSettingState.info.id / 100),
              indicatorType: IndicatorType.GroupUser,
              pageSize,
              current,
              filters: filtersInfo,
            },
          });
          setTableInfo(result.data as ExperimentGroupTable[]);
          setExperimentTotal(result.experimentTotal);
          return {
            data: result.data as ExperimentGroupTable[],
            total: result.total,
            success: true,
          };
        }}
        onChange={setTableInfo}
        editable={{
          type: 'single',
          editableKeys,
          actionRender: (row, _, defaultDoms) => [defaultDoms.save, defaultDoms.cancel],
          onSave: async (_, record) => {
            const result = await updateEditableInfo({
              data: {
                id: record.id,
                appId: Math.floor(appSettingState.info.id / 100),
                indicatorType: IndicatorType.GroupUser,
                handleStatus: record.handleStatus,
                priority: record.priority,
                remark: record.remark,
              },
            });
            if (result.code !== NetworkCode.Success) {
              // await message.error(result.message);
            } else {
              await message.success('更新成功');
            }
          },
          onChange: setEditableRowKeys,
        }}
      />
    </>
  );
};

export default ExperimentGroupMonitor;
