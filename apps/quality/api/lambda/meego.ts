import { Api, Data, Get, Post, Query, useInject } from '@edenx/runtime/bff';
import { z } from 'zod';
import MeegoService from '../service/meego';
import SlardarCrashIssueModel from '../model/SlardarCrashIssueTable';
import { LVProductType, SlardarPlatformType } from '@pa/shared/dist/src/appSettings/appSettings';
import { current_region, Region } from '../utils/region';
import SlardarService from '../service/slardar/slardar';
import { versionCodeToMeegoVersion } from '@shared/utils/version_utils';
import LarkService from '@pa/backend/dist/src/third/lark';
import { AutoTestBugTitle2CrashType, crashType2Title } from '@shared/typings/slardar/common';
import AlarmVersionModel from '../model/AlarmVersionInfoTable';
import MeegoRawService from '../service/third/meego';
import BitsService from '../service/bits';
import { autoCreateWrapper, autoTestCreateWrapper } from '../service/meegoIssueCreateWrapper';
import { FieldValue } from '@shared/typings/meego';
import commonUtils from '@shared/utils/commonUtils';
import { currentProduct } from '../utils/basic';
import { parseSlardarLink } from '../utils/slardarLink';
import { DiscoverStag2Option } from '@shared/walle/consumeResult';
import { add_suffix_ne } from '@shared/utils/tools';
import { BytedLogger } from '@gulux/gulux/byted-logger';
import {
  Dreamina_SLARDAR_APP_ID,
  HYPIC_SLARDAR_APP_ID,
  SLARDAR_APP_ID,
  SLARDAR_RETOUCH_ID,
  SlardarAppId,
} from '../service/slardar/shared';
import { AlarmSchema, RuleSchema } from '@shared/typings/slardar/crash/ttMleaksFinderData';

import { getMeegoModuleConfig, getPlatformFromOS, processAlarmContexts } from '@shared/utils/MLeaksUtils';
import { getAllocation } from '../utils/allocation';
import { UserIdType } from '@pa/shared/dist/src/lark/userInfo';
import MessageService from '@pa/backend/dist/src/service/message';
import { MsgGrayVersionEnd } from '../service/libra/LibraCard';
import { LibraBaseInfo } from '@shared/libra/libraInfo';
import { LibraRegion } from '@shared/libra/commonLibra';
import SlardarIssueBugItemModel from '../model/SlardarIssueBugItem';

export const meegoCreateSlardarTestBug = Api(
  Post('/slardar/test/meego/create'),
  Data(
    z.object({
      testId: z.string(), // 实验id
      link: z.string(),
      priority: z.string(),
      operator: z.string().optional(),
      discoverStage: z.string().optional(),
      creator: z.string().optional(),
      watchers: z.array(z.string()).optional(),
      tags: z.array(z.string()).optional(),
      description: z.string().optional(),
      source: z.string().optional(),
    }),
  ),
  async ({ data }) => {
    const meegoService = useInject(MeegoService);
    const slardarService = useInject(SlardarService);
    const slardarIssueModel = useInject(SlardarCrashIssueModel);
    const larkService = useInject(LarkService);
    const alarmVersionModel = useInject(AlarmVersionModel);
    useInject(BytedLogger).info(`createTestBug req:${JSON.stringify(data)}`);
    const { aid, os, crashType, issueId } = parseSlardarLink(data.link);
    if (issueId && os && aid) {
      const platform = os.toLocaleLowerCase() === 'android' ? SlardarPlatformType.Android : SlardarPlatformType.iOS;
      const categories = [`issue_id-${issueId}`];
      let discoverVersion: string | null = null;
      let meegoTitle = '';
      let chatId: string | undefined;
      let desc = '';
      let finalAssigner = data.operator ? add_suffix_ne('@bytedance.com')(data.operator) : undefined;
      if ([3006, 1775].includes(aid)) {
        // 剪映提单
        discoverVersion = await alarmVersionModel.findLastGrayingVersion(platform, aid);
        let { title, assigner } = await slardarIssueModel.queryIssueTitle(aid, issueId, platform);
        // 库中不存在分配人，前端选择触发自动分配
        if (crashType && !assigner) {
          try {
            await slardarService.IssueConsumeWeb(aid, issueId, platform, crashType);
          } catch (e) {
            console.log(e);
          }
        }
        try {
          const issueInfo = await slardarService.getCrashInfoById(aid, issueId, platform, crashType);
          if (issueInfo.data) {
            title = `${issueInfo.data.title ? issueInfo.data.title : issueInfo.data.crash_file}(${
              issueInfo.data.crash_clazz
                ? issueInfo.data.crash_clazz.substring(0, 10)
                : issueInfo.data.event_detail
                  ? issueInfo.data.event_detail
                  : ''
            })`;
            if (issueInfo.data.managers && issueInfo.data.managers.length > 0) {
              assigner = issueInfo.data.managers[0];
            }
          }
          finalAssigner =
            assigner ??
            (await useInject(MeegoRawService).MeegoConsume(title, discoverVersion!, platform, crashType)).assigner;
          chatId = await slardarService.joinSlardarChatAndInviteMeegoBot(
            aid,
            finalAssigner,
            issueId,
            crashType,
            os.toLocaleLowerCase() === 'android' ? SlardarPlatformType.Android : SlardarPlatformType.iOS,
            title ? title : '',
            issueInfo.data.id,
          );
        } catch (e) {
          await larkService.quickLog(`提单兜底失败，异常${e}`);
        }
        meegoTitle = `[实验-${data.testId}][${discoverVersion}-${crashType2Title[crashType]}] ${title}`;
        categories.unshift('TikTok-lv');
        desc = [
          `实验链接: ${commonUtils.getLibraFlightUrl(data.testId, currentProduct() === LVProductType.cc)}`,
          `问题地址: ${data.link} ${data.description ?? ''}`,
          data.description ?? '',
          '注意: 缺陷发现版本为灰度中版本，不代表实际问题发生版本',
        ].join('\n');
        // 剪映提单end
      } else if ([2515, 7356].includes(aid) && data.operator) {
        // 醒图提单
        discoverVersion =
          os.toLocaleLowerCase() === 'android'
            ? await useInject(BitsService).pullAndroidHypicVersion()
            : await useInject(BitsService).pulliOSHypicVersion();
        let { title, assigner } = await slardarIssueModel.queryIssueTitle(aid, issueId, platform);
        // 库中不存在分配人，前端选择触发自动分配
        // if (crashType && !assigner) {
        //   try {
        //     await slardarService.IssueConsumeWeb(aid, issueId, platform, crashType);
        //   } catch (e) {
        //     console.log(e);
        //   }
        // }
        try {
          const issueInfo = await slardarService.getCrashInfoById(aid, issueId, platform, crashType);
          if (issueInfo.data) {
            title = `${issueInfo.data.title ? issueInfo.data.title : issueInfo.data.crash_file}(${
              issueInfo.data.crash_clazz
                ? issueInfo.data.crash_clazz.substring(0, 10)
                : issueInfo.data.event_detail
                  ? issueInfo.data.event_detail
                  : ''
            })`;
            if (issueInfo.data.managers && issueInfo.data.managers.length > 0) {
              assigner = issueInfo.data.managers[0];
            }
          }
          finalAssigner =
            assigner ??
            (await useInject(MeegoRawService).MeegoConsume(title, discoverVersion!, platform, crashType)).assigner;
          chatId = await slardarService.joinSlardarChatAndInviteMeegoBot(
            aid,
            finalAssigner,
            issueId,
            crashType,
            os.toLocaleLowerCase() === 'android' ? SlardarPlatformType.Android : SlardarPlatformType.iOS,
            title ? title : '',
            issueInfo.data.id,
          );
        } catch (e) {
          await larkService.quickLog(`提单兜底失败，异常${e}`);
        }

        meegoTitle = `[实验-${data.testId}][${discoverVersion}-${crashType2Title[crashType]}]issue_id:${issueId}`;
        categories.unshift('TikTok-Hypic');
        desc = `${data.link} ${data.description ?? ''}`;
        desc = [
          `实验链接: ${commonUtils.getLibraFlightUrl(data.testId, currentProduct() === LVProductType.cc)}`,
          `问题地址: ${data.link} ${data.description ?? ''}`,
          data.description ?? '',
          '注意: 缺陷发现版本为灰度中版本，不代表实际问题发生版本',
        ].join('\n');
        // 醒图提单end
      }
      const res = await meegoService.createSlardarTestBug(
        meegoTitle,
        data.testId,
        platform,
        categories,
        desc,
        data.priority,
        discoverVersion!,
        issueId,
        finalAssigner,
        data.discoverStage ?? DiscoverStag2Option.release,
        data.creator,
        chatId,
        // ra48enfq0: Rig助手  c082yoxfb: TikTok质量小助手-单issue稳定性
        data.source?.toLowerCase()?.includes('rig') ? 'ra48enfq0' : 'c082yoxfb',
        aid.toString(),
        data.watchers,
        data.tags,
      );
      useInject(BytedLogger).info(`createTestBug meegoCreate ret:${JSON.stringify(res)}`);
      if (res.ret === 0 && res.error_msg.includes('exists')) {
        // TODO 更新已有字段
      }
      return {
        ret: res.ret,
        data: res.data ? `https://meego.feishu.cn/faceu/issue/detail/${res.data}` : undefined,
        error_msg: res.error_msg ? res.error_msg : undefined,
        operator: res.operator,
        status: res.status,
        priority: Number(res.priority),
      };
    }
    return {
      ret: -1,
      error_msg: '未找到相关issue',
    };
  },
);

export const meegoOfflineSlardarBug = Api(
  Post('/slardar/meego/offline/create'),
  Data(
    z.object({
      aid: z.number(),
      platform: z.nativeEnum(SlardarPlatformType),
      version: z.string(),
      issueId: z.string(),
    }),
  ),
  async ({ data }) => {
    const slardarIssueBugItem = useInject(SlardarIssueBugItemModel);
    const meegoService = useInject(MeegoService);
    const existMeegoId = await slardarIssueBugItem.isExist(data.aid, data.platform, data.version, data.issueId, true);
    const logger = useInject(BytedLogger);
    logger.info(`existMeegoId: ${existMeegoId}`);
    if (existMeegoId) {
      const meegoInfo = await meegoService.getMeegoInfo([existMeegoId]);
      let operator: string[] | undefined, priority: number | undefined, status: string | undefined;
      if (existMeegoId in meegoInfo.data) {
        operator = meegoInfo.data[existMeegoId].issue_operator;
        priority = meegoInfo.data[existMeegoId].priority;
        status = meegoInfo.data[existMeegoId].status;
      }
      return {
        ret: 0,
        data: existMeegoId ? `https://meego.feishu.cn/faceu/issue/detail/${existMeegoId}` : undefined,
        operator: operator ? operator[0] : '',
        priority: Number(priority),
        status: status,
      };
    } else {
      return {
        ret: -1,
        error_msg: '未找到相关issue',
      };
    }
  },
);

export const meegoCreateSlardarBug = Api(
  Post('/slardar/meego/create'),
  Data(
    z.object({
      link: z.string(),
      discoverVersion: z.string(),
      priority: z.string(),
      assignee: z.string().optional(),
      discoverStage: z.string(),
      creator: z.string(),
      isConsume: z.boolean(),
      channel: z.string().optional(),
      isAutoCreated: z.boolean().optional(),
    }),
  ),
  async ({ data }) => {
    const meegoService = useInject(MeegoService);
    const slardarService = useInject(SlardarService);
    const slardarIssueModel = useInject(SlardarCrashIssueModel);
    const larkService = useInject(LarkService);
    const bitsService = useInject(BitsService);
    const logger = useInject(BytedLogger);
    let IsConsumed = !data.isConsume;
    const { aid, os, crashType, issueId } = parseSlardarLink(data.link);
    const platform = os === 'Android' ? SlardarPlatformType.Android : SlardarPlatformType.iOS;
    let chatId: string | undefined;
    if (issueId && aid) {
      const bmInfo = await bitsService.getBMInfo(platform, '', aid, data.discoverVersion);
      let { title, assigner } = await slardarIssueModel.queryIssueTitle(
        aid,
        issueId,
        os === 'Android' ? SlardarPlatformType.Android : SlardarPlatformType.iOS,
      );
      // 库中不存在分配人，前端选择触发自动分配；非剪C自动分配默认选择对应版本RD BM
      if (data.isConsume && crashType && !assigner && aid === SLARDAR_APP_ID()) {
        try {
          const res = await slardarService.IssueConsumeWeb(
            aid,
            issueId,
            os === 'Android' ? SlardarPlatformType.Android : SlardarPlatformType.iOS,
            crashType,
          );
          if (res && res.length > 0) {
            assigner = res[0];
            IsConsumed = true;
          }
        } catch (e) {
          console.log(e);
        }
      } else if (data.isConsume && crashType && !assigner) {
        assigner = bmInfo.bm ?? assigner;
        console.log('暂无自动分配能力，暂时分配为版本BM');
      }
      let { priority } = data;
      try {
        const issueInfo = await slardarService.getCrashInfoById(
          aid,
          issueId,
          os.toLocaleLowerCase() === 'android' ? SlardarPlatformType.Android : SlardarPlatformType.iOS,
          crashType,
        );
        if (issueInfo.data) {
          title = `${issueInfo.data.title ? issueInfo.data.title : issueInfo.data.crash_file}(${
            issueInfo.data.crash_clazz
              ? issueInfo.data.crash_clazz.substring(0, 10)
              : issueInfo.data.event_detail
                ? issueInfo.data.event_detail
                : ''
          })`;
          if (issueInfo.data.managers && issueInfo.data.managers.length > 0) {
            assigner = issueInfo.data.managers[0];
          }
          if (issueInfo.data.issue_level !== undefined && data.priority === 'X') {
            priority = issueInfo.data.issue_level.toString();
          }
          chatId = await slardarService.joinSlardarChatAndInviteMeegoBot(
            aid,
            data.creator,
            issueId,
            crashType,
            os.toLocaleLowerCase() === 'android' ? SlardarPlatformType.Android : SlardarPlatformType.iOS,
            title,
            issueInfo.data.id,
          );
        }
      } catch (e) {
        await larkService.quickLog(`提单兜底失败，异常${e}`);
        console.log(`slardar兜底完蛋了，使用旧的分配人，${e}`);
      }
      title = `[${data.discoverVersion}-${crashType2Title[crashType]}] ${title}`;
      // 兜底分配版本BM作为解决人
      if (!assigner && !data.assignee) {
        assigner = bmInfo.bm ?? assigner;
      }
      // 如果是cpu的异常先提给yanan
      if (crashType === 'ios_metricket_cpu') {
        assigner = 'huangyanan.1';
      }
      logger.info(
        `meegoCreateSlardarBug ${JSON.stringify({
          title,
          platform,
          discoverVersion: data.discoverVersion,
          link: data.link,
          priority,
          assignee: assigner,
          discoverStage: data.discoverStage,
          creator: data.creator,
          chatId,
          source: '5',
          isAutoCreated: data.isAutoCreated,
        })}`,
      );
      const res = await meegoService.createSlardarIssue(
        title,
        os.toLocaleLowerCase() === 'android' ? SlardarPlatformType.Android : SlardarPlatformType.iOS,
        data.discoverVersion,
        data.link,
        priority,
        assigner ? assigner : data.assignee,
        data.discoverStage,
        data.creator,
        chatId,
        '5',
        data.isAutoCreated,
      );
      return {
        ret: res.ret,
        data: res.data ? `https://meego.feishu.cn/faceu/issue/detail/${res.data}` : undefined,
        error_msg: res.error_msg ? res.error_msg : undefined,
        operator: res.operator,
        status: res.status,
        priority: Number(res.priority),
        IsConsumed,
      };
    }
    return {
      ret: -1,
      error_msg: '未找到相关issue',
    };
  },
);

/**
 * 自动测试提单开放接口，勿删
 */
export const meegoCreateSlardarAutoTestBug = Api(
  Post('/slardar/auto_test/meego/create'),
  Data(
    z.object({
      issue_id: z.string(),
      issue_title: z.string(),
      platform: z.nativeEnum(SlardarPlatformType), // Android/iOS
      discovery_version: z.string(), // 8.7.0
      resolve_version: z.string(),
      issue_link: z.string(),
      issue_priority: z.string(), // 0, 1, 2
      reporter: z.string(),
      issue_stage: z.string(),
      operator: z.string(),
      bug_channel: z.string(),
      watchers: z.array(z.string()),
      modules: z.any().optional(),
      category: z.any().optional(),
      isBindChat: z.boolean().optional(),
      is_retouch: z.boolean().optional(),
      is_comment: z.boolean().optional(),
      slardar_app: z.string().optional(),
      not_cover: z.boolean().optional(),
    }),
  ),
  async ({ data }) => {
    const logger = useInject(BytedLogger);
    const slardarService = useInject(SlardarService);
    const meegoService = useInject(MeegoService);
    logger.info(`开始提单,issue_id${data.issue_id}`);
    const existMeegoId = await meegoService.existMeegoId({ platform: data.platform, issue_id: data.issue_id });
    let aid: number = data.is_retouch ? SLARDAR_RETOUCH_ID() : SLARDAR_APP_ID();
    let watchers: string[] =
      data.platform === SlardarPlatformType.Android
        ? ['<EMAIL>']
        : data.watchers.map(v => (v.endsWith('@bytedance.com') ? v : `${v}@bytedance.com`));
    if (data.slardar_app) {
      aid = SlardarAppId(data.slardar_app);
      watchers = [data.operator];
    }
    if (existMeegoId) {
      const meegoLink = `https://meego.feishu.cn/faceu/issue/detail/${existMeegoId}`;
      logger.info(`通过issueID:${data.issue_id}查询到数据库中已存在的meego单:${meegoLink}`);
      if (data.is_comment) {
        try {
          await slardarService.addIssueComment(
            aid,
            data.issue_id,
            data.platform,
            `【纸飞机 稳定性发现已提过meego单】 版本：${data.discovery_version}  链接：${meegoLink}`,
          );
        } catch (e) {
          logger.error('稳定性-slardar增加评论失败', e);
        }
      }
      return {
        data: meegoLink,
        message: '重复性提单，issueID已经在数据库中存在',
        ret: 10002, // 重复性历史提单
      };
    }
    logger.info(`[自动化测试提单]开始`, data);
    let { operator } = data;
    let chatId: string | undefined;
    // 判断是否是醒图
    try {
      // 根据title获取到crashType
      const crashType = AutoTestBugTitle2CrashType(data.issue_title);
      if (!data.is_retouch && !data.not_cover) {
        logger.info(`获取到分配人${data.issue_id}`);
        const { assigner } = await useInject(SlardarCrashIssueModel).queryIssueTitle(aid, data.issue_id, data.platform);
        // 获取到操作员 醒图可以直接从data中获取operator，暂时还没有分配规则
        if (assigner) {
          operator = assigner.endsWith('@bytedance.com') ? assigner : `${assigner}@bytedance.com`;
        } else {
          if (crashType) {
            logger.info(`[自动化测试提单]触发自动分配${crashType}-${data.issue_id}-${data.platform}`);
            const res = await slardarService.IssueConsumeWeb(aid, data.issue_id, data.platform, crashType);
            if (res && res.length > 0) {
              operator = res[0];
            }
          }
        }
      }
      logger.info(`通过issueID-${data.issue_id}开始获取issueInfo`);
      if (crashType) {
        const issueInfo = await slardarService.getCrashInfoById(aid, data.issue_id, data.platform, crashType);
        if (issueInfo.data && !data.not_cover) {
          if (issueInfo.data.managers && issueInfo.data.managers.length > 0) {
            operator = issueInfo.data.managers[0].endsWith('@bytedance.com')
              ? issueInfo.data.managers[0]
              : `${issueInfo.data.managers[0]}@bytedance.com`;
          }
        }
        // 是否创建群聊，这里会绑定群
        if (data.isBindChat) {
          logger.info(`${data.issue_id}获取绑定群聊`);
          const start = Date.now();
          chatId = await slardarService.joinSlardarChatAndInviteMeegoBot(
            aid,
            data.reporter,
            data.issue_id,
            crashType,
            data.platform,
            data.issue_title,
            issueInfo.data.id,
          );
          const end = Date.now();
          logger.info(`群聊查询耗时: ${end - start} m,chatId:${chatId}`);
        }
      }
      // ios需要重新指定经办人
      if ((aid === 3006 || aid === 1775) && data.platform === SlardarPlatformType.iOS && !data.not_cover) {
        logger.info(`剪映&CC重新根据版本${data.discovery_version}分配经办人`);
        const VersionOperator = getAllocation(data.discovery_version);
        if (VersionOperator) {
          operator = VersionOperator;
        }
        logger.info(`经办人获取:${operator}`);
      }
      // if (
      //   crashType &&
      //   (aid === 3006 || aid === 1775) &&
      //   data.platform === SlardarPlatformType.iOS &&
      //   !data.not_cover &&
      //   ['crash', 'start'].includes(crashType)
      // ) {
      //   logger.info(`剪映&CC重新根据版本${data.discovery_version}分配经办人`);
      //   const VersionOperator = getAllocation(data.discovery_version);
      //   if (VersionOperator) {
      //     operator = VersionOperator;
      //   }
      //   logger.info(`经办人获取:${operator}`);
      // }
      // 验证此人是否合法
      const usekey = await useInject(MeegoRawService).requestMeegoUserInfos({ emails: [operator] });
      if (usekey.length === 0) {
        operator = data.operator;
      }
    } catch (e) {
      logger.info(`[自动化测试提单]触发自动分配失败，issue_id:${data.issue_id}, title:${data.issue_title} ${e}`);
    }
    logger.info(`自动化module:${JSON.stringify(data?.modules)}`);
    logger.info(`缺陷分类:${JSON.stringify(data?.category)}`);
    const res = await autoTestCreateWrapper(
      data.issue_id,
      data.issue_title,
      data.platform, // Android/iOS
      data.discovery_version, // 8.7.0
      data.resolve_version,
      data.issue_link,
      data.issue_priority, // 0, 1, 2
      data.reporter.endsWith('@bytedance.com') ? data.reporter : `${data.reporter}@bytedance.com`,
      data.issue_stage,
      operator.endsWith('@bytedance.com') ? operator : `${data.operator}@bytedance.com`,
      data.bug_channel,
      watchers,
      data.modules ? (data.modules as FieldValue) : undefined,
      chatId,
      data.category,
      data.is_retouch,
      aid,
    );
    logger.info(`[自动化测试提单]结束`, res);

    return {
      ret: res.ret,
      data: res.data ? `https://meego.feishu.cn/faceu/issue/detail/${res.data}` : undefined,
      error_msg: res.error_msg ? res.error_msg : undefined,
      operator: res.data ? res.operator : undefined,
      status: res.data ? res.status : undefined,
      priority: res.data ? Number(res.priority) : undefined,
    };
  },
);
/**
 * 剪映体验与服务的自动测试提单开放接口，勿删
 */
export const meegoCreateSlardarAutoBug = Api(
  Post('/meego/create_issue'),
  Data(
    z.object({
      issue_title: z.string(),
      platform: z.nativeEnum(SlardarPlatformType), // Android/iOS
      discovery_version: z.string(), // 8.7.0
      resolve_version: z.string(),
      fb_link: z.string(),
      fb_content: z.string(),
      issue_priority: z.string(), // 0, 1, 2
      reporter: z.string(),
      issue_stage: z.string(),
      operator: z.string(),
      bug_channel: z.string(),
      watchers: z.array(z.string()),
      isBindChat: z.boolean().optional(),
      chatId: z.string().optional(),
      businessName: z.string().optional(),
      appName: z.string().optional(),
    }),
  ),
  async ({ data }) => {
    const logger = useInject(BytedLogger);
    let chatId: string | undefined;
    if (data.isBindChat) {
      chatId = data.chatId;
    }
    const businessName = data.businessName ? data.businessName : '体验与服务';
    const appName = data.appName ? data.appName : 'App-剪映';
    const res = await autoCreateWrapper(
      data.issue_title,
      appName,
      businessName,
      data.platform, // Android/iOS
      data.discovery_version, // 8.7.0
      data.resolve_version,
      data.fb_link,
      data.fb_content,
      data.issue_priority, // 0, 1, 2
      data.reporter,
      data.issue_stage,
      data.operator,
      data.bug_channel,
      data.watchers,
      chatId,
    );
    logger.info(`[自动化测试提单]结束`, res);
    return {
      ret: res.ret,
      data: res.data ? `https://meego.feishu.cn/faceu/issue/detail/${res.data}` : undefined,
      error_msg: res.error_msg ? res.error_msg : undefined,
      operator: res.data ? res.operator : undefined,
      status: res.data ? res.status : undefined,
      priority: res.data ? Number(res.priority) : undefined,
    };
  },
);
export const getMeegoIssueInfoByUrl = Api(
  Get('/meego/issue/info'),
  Query(
    z.object({
      url: z.string(),
    }),
  ),
  async ({ query }) => {
    const meegoService = useInject(MeegoService);
    return await meegoService.getMeegoInfoByUrl(query.url);
  },
);
export const UpdateMeegoLevel = Api(
  Post('/slardar/meego/update'),
  Data(
    z.object({
      meego_url: z.string(),
      priority: z.number(),
      platform: z.nativeEnum(SlardarPlatformType),
      version_code: z.string(),
      operator: z.string(),
    }),
  ),
  async ({ data }) => {
    const meegoService = useInject(MeegoService);
    const meego_id = meegoService.getMeegoIdByUrl(data.meego_url);
    let BMVersion = versionCodeToMeegoVersion(data.version_code);
    if (current_region() === Region.SG) {
      const [major, minor, patch] = BMVersion.split('.').map(Number);
      BMVersion = `${major + 2}.${minor}.0`;
    }
    return await meegoService.updateIssue(
      meego_id,
      Number(data.priority),
      BMVersion,
      data.platform,
      undefined,
      data.operator,
    );
  },
);

export const QueryOrBuildMeegoView = Api(
  Post('/meego/create/view'),
  Data(
    z.object({
      aid: z.number(),
      version: z.string(),
      platform: z.nativeEnum(SlardarPlatformType),
      category: z.array(z.string()).optional(),
    }),
  ),
  async ({ data }) => {
    const meegoService = useInject(MeegoService);
    return await meegoService.createMeegoWorkView(data.aid, data.version, data.platform, data.category);
  },
);

export const BindIssueMeegoItem = Api(
  Post('/meego/bind/issue'),
  Data(
    z.object({
      aid: z.number(),
      version: z.string(),
      platform: z.nativeEnum(SlardarPlatformType),
      issue_id: z.string(),
      url: z.string(),
      meegoId: z.number(),
      issue_level: z.number(),
      operator: z.string(),
    }),
  ),
  async ({ data }) => {
    const res = await useInject(MeegoService).updateMeegoItem(
      data.aid,
      data.meegoId,
      data.url,
      data.version,
      data.platform,
      data.issue_level,
      data.operator,
    );
    if (res) {
      // 保存meego单
      await useInject(MeegoService).saveMeegoAndUpdateView(
        data.aid,
        data.platform,
        data.version,
        data.issue_id,
        data.meegoId,
      );
      // 同步至issue_list
      await useInject(SlardarCrashIssueModel).updateSlardarMeegoMap(
        data.aid,
        data.issue_id,
        `https://meego.feishu.cn/faceu/issue/detail/${data.meegoId}`,
        data.version,
      );
      // 返回实时meego信息
      const meegoInfo = await useInject(MeegoService).getMeegoInfo([data.meegoId]);
      let operator: string[] | undefined, priority: number | undefined, status: string | undefined;
      if (data.meegoId in meegoInfo.data) {
        operator = meegoInfo.data[data.meegoId].issue_operator;
        priority = meegoInfo.data[data.meegoId].priority;
        status = meegoInfo.data[data.meegoId].status;
      }
      return {
        ret: 0,
        data: data.meegoId,
        error_msg: undefined,
        operator: operator ? operator[0] : '',
        priority,
        status,
      };
    } else {
      return {
        ret: -1,
        error_msg: '未找到相关meego',
      };
    }
  },
);

export const QueryMeegoViewWorkItemList = Api(
  Post('/meego/query_meego_view_work_item_list'),
  Data(
    z.object({
      project_key: z.string(),
      view_id: z.string(),
      page_num: z.number().optional(),
      page_size: z.number().optional(),
    }),
  ),
  async ({ data }) => {
    const meegoService = useInject(MeegoRawService);
    return await meegoService.requestViewWorkItemList(data.project_key, data.view_id, data.page_num, data.page_size);
  },
);

export const TTMLeaksFinderCallbackAutoTestBug = Api(
  Post('/slardar/ttmleaks/meego/create'),
  Data(
    z.object({
      rule: RuleSchema.optional(),
      alarm: AlarmSchema.optional(),
    }),
  ),
  async ({ data }) => {
    const logger = useInject(BytedLogger);
    const { rule, alarm } = data;

    logger.info(`【TTMLeaks自动提单】开始处理, 规则配置:${JSON.stringify(rule)}, 告警数据:${JSON.stringify(alarm)}`);

    if (!alarm?.alarm_context_v2) {
      logger.info('【TTMLeaks自动提单】告警数据为空，无需处理');
      return { ret: 0, error_msg: '未发现有需要提单的数据' };
    }

    const platform = getPlatformFromOS(rule?.os);
    const crashType = alarm.alarm_context_v2.crash_type;
    const aid = SLARDAR_APP_ID();
    const reporter = '<EMAIL>';
    const moduleConfig = getMeegoModuleConfig(aid);

    logger.info(
      `【TTMLeaks自动提单】基础信息: 平台=${platform}, 崩溃类型=${crashType}, AID=${aid}, 提单人=${reporter}`,
    );
    logger.info(`【TTMLeaks自动提单】模块配置: ${JSON.stringify(moduleConfig)}`);

    try {
      logger.info('【TTMLeaks自动提单】开始处理告警上下文...');
      const result = await processAlarmContexts(alarm, {
        platform,
        crashType,
        aid,
        reporter,
        rule,
        moduleConfig,
        logger,
      });

      if (result) {
        logger.info(`【TTMLeaks自动提单】处理完成，返回结果: ${JSON.stringify(result)}`);
        return result;
      }
      logger.info('【TTMLeaks自动提单】处理完成，无需要处理的数据');
      return { ret: 0, error_msg: '处理完成,issue信息中没有数据需要提单' };
    } catch (error) {
      logger.error('【TTMLeaks自动提单】处理告警失败:', error);
      return { ret: -1, error_msg: `处理告警失败: ${error instanceof Error ? error.message : String(error)}` };
    }
  },
);

export const TestLibraCloneCard = Api(
  Post('/meego/test_libra_clone_card'),
  Data(
    z.object({
      appNames: z.array(z.string()),
      appIds: z.array(z.string()),
      flightId: z.string(),
      flightName: z.string(),
      flightUrl: z.string(),
      flightOwners: z.array(z.string()).optional(),
      flightType: z.string(),
      operator: z.string(),
      region: z.number(),
    }),
  ),
  async ({ data }) => {
    const meegoService = useInject(MessageService);
    const libraBaseInfo: LibraBaseInfo = {
      appNames: data.appNames,
      appIds: data.appIds,
      flightId: data.flightId,
      flightName: data.flightName,
      flightUrl: data.flightUrl,
      flightOwners: [],
      flightType: data.flightType,
      operator: data.operator,
    };

    const msg = new MsgGrayVersionEnd(libraBaseInfo, data.region, false, false);
    return await meegoService.sendNormalMsg(msg, UserIdType.openId, 'ou_288568c940e05d5a96e07227d7103b6a');
  },
);

export const AutoJoinGroupChat = Api(
  Post('/meego/invite/user/group'),
  Data(
    z.object({
      aid: z.number(),
      operator: z.string(),
      issue_id: z.string(),
      crashType: z.string(),
      platform: z.nativeEnum(SlardarPlatformType),
      title: z.string(),
    }),
  ),
  async ({ data }) => {
    const slardarService = useInject(SlardarService);
    return await slardarService.joinSlardarOpenChatAndInviteMeegoBot(
      data.aid,
      data.operator,
      data.issue_id,
      data.crashType,
      data.platform,
      data.title,
    );
  },
);
