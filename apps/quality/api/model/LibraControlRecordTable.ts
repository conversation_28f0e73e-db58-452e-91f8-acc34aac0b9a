import { Database, getModelForClass, Prop } from '@gulux/gulux/typegoose';
import { Injectable } from '@gulux/gulux';
import {
  ControlProcess,
  ControlStatus,
  FlightEventType,
  LibraControlRecord,
  LibraControlTakeOverType,
} from '@shared/libra/libraControl';

@Database('main') // 这里与配置中的 client 名字一致
export class LibraControlRecordTable implements LibraControlRecord {
  @Prop()
  app_id: string; // 产品Id
  @Prop()
  flight_id: string; // 实验id
  @Prop()
  flight_name: string; // 实验名称
  @Prop()
  user: string; // 操作人
  @Prop()
  event_type: FlightEventType; // 执行类型
  @Prop()
  version_resource: number; // 实验流量
  @Prop()
  region: string; // 实验在哪个控制面  枚举值: cn, sg,  va
  @Prop()
  owners: string[]; // 实验owner
  @Prop()
  tags: string[]; // 实验tags
  @Prop()
  configs: any[]; // 实验组配置
  @Prop()
  priority: number; // 实验层优先级
  @Prop()
  take_over: boolean;
  @Prop()
  take_over_reason: string;
  @Prop()
  take_over_logid?: string;
  @Prop()
  take_over_type?: LibraControlTakeOverType;
  @Prop()
  process_history: ControlProcess[];
  @Prop()
  create_ts: number;
  @Prop()
  status: ControlStatus;
  @Prop()
  chat_id?: string;
  _id: string;
}

@Injectable()
export class LibraControlRecordModel {
  private model = getModelForClass(LibraControlRecordTable);

  async save(...item: LibraControlRecord[]) {
    return await this.model.create(item);
  }

  async genericFind(
    query: Partial<Record<keyof LibraControlRecordTable, any>>,
    skip: number,
    limit: number,
    sort?: Partial<Record<keyof LibraControlRecordTable, number>>,
  ) {
    return {
      success: true,
      total: await this.model.countDocuments(query),
      data: await this.model.find(query).skip(skip).limit(limit).sort(sort).exec(),
    };
  }

  async queryById(id: string) {
    return this.model
      .findOne({
        _id: id,
      })
      .exec();
  }
}
