import { Inject, Injectable } from '@gulux/gulux';
import LibraNewInfoListDao from '../../dao/libra/LibraNewInfoListDao';
import {
  FlightType,
  FlightTypeName,
  LibraFlightCloseFullReleaseSubType,
  LibraFlightCloseMainType,
  LibraFlightCloseOfflineSubType,
  LibraFlightInfo,
  LibraFlightStatus,
  LibraFlightType,
  LibraInfoQueryProgress,
  LibraLayerType,
  LibraListQueryParams,
  LibraMeegoInfo,
  LibraMeegoTeamInfo,
  LibraMeegoTeamSimpleInfo,
  LibraNewInfo,
  LibraNewInfoEditaResultType,
  LibraNewInfoForAeolus,
  LibraNotifyType,
  LibraPatrolInfo,
  LibraStopType,
  LibraTrafficInfo,
  LibraVersionInfo,
  PaExtraInfo,
} from '@shared/libra/LibraNewInfo';
import { LibraAppIds, LibraRegion } from '@shared/libra/commonLibra';
import LibraAPIService from './LibraAPIService';
import { FilterRule, Flight, FlightListResponse } from '@shared/libra/flight';
import { FieldValue, Roles, WorkItemInfo } from '@shared/typings/meego';
import { LibraEvent, LibraEventType, LibraMeego } from '@shared/libra/libraInfo';
import MeegoRawService from '../third/meego';
import StoryRevenueTaskUpdateMeegoInfoService from '../storyRevenueReviewPlatform/StoryRevenueTaskUpdateMeegoInfoService';
import LarkService from '@pa/backend/dist/src/third/lark';
import ExperimentListInfoModel, { DBExperimentListInfo } from '../../model/ExperimentListInfoTable';
import { IndicatorType } from '@shared/libra/common';
import LibraService from '../third/libra';
import { NetworkCode, User } from '@pa/shared/dist/src/core';
import { ceil, omit } from 'lodash';
import { BytedLogger } from '@gulux/gulux/byted-logger';
import LibraInfoQueryProgressDao from '../../dao/libra/LibraInfoQueryProgressDao';
import { StoryRevenueMeegoFieldInfo } from '@shared/storyRevenueReviewPlatform/StoryRevenueMeegoFieldInfo';
import {
  calculateDayDiffResult,
  checkIfTeamIdMatchLibraRegion,
  convertLibraNewInfoToBaseInfo,
  getAppNameByLibraAppId,
  GetLibraMeegoTeamIdByDepartmentName,
  isP00Story,
  LarkChatIdsByMeegoTeamType,
  LastWeekInfoForLibraTopicGroups,
  libraDetailUrl,
  libraFlightCloseAttributionFullReleaseSubType2DisplayNameMap,
  libraFlightCloseAttributionMainType2DisplayNameMap,
  libraFlightCloseAttributionOfflineSubType2DisplayNameMap,
  libraFlightCloseAttributionReopenSubType2DisplayNameMap,
  libraFlightCloseAttributionReopenSubTypeCanPreInterceptType2DisplayNameMap,
  libraFlightCloseAttributionReopenSubTypeDetailType2DisplayNameMap,
  libraUrlOfPaperAirplane,
  MeegoProjectKeyFaceU,
  MeegoTeamExcludeMembers,
  MeegoTeamIds,
  MeegoTeamIdsByMeegoTeamType,
  MeegoTeamIdsForCapCutMobile,
  MeegoTeamIdsForCapCutPC,
  MeegoTeamIdsForCapCutWeb,
  MeegoTeamIdsForCommerce,
  MeegoTeamIdsForJianyingMobile,
  MeegoTeamIdsForJianyingPC,
  MeegoTeamIdsForJianyingServer,
  MeegoTeamIdsForJianyingWeb,
  MeegoTeamIdsForMultimedia,
  MeegoTeamIdsForRetouch,
  MeegoTeamShortIdByMeegoTeamStr,
  MeegoTeamType,
  ThisWeekInfoForLibraTopicGroups,
  YesterdayInfoForLibraTopicGroups,
} from '@shared/libra/libraManageUtils';
import LibraMeegoTeamInfoDao from '../../dao/libra/LibraMeegoTeamInfoDao';
import { TreeEnumOptions } from '@shared/utils/conditionFilter';
import LibraChangeEventDao from '../../dao/libra/LibraChangeEventDao';
import { getRealStopReason, LibraChangeEvent } from '@shared/libra/LibraChangeEvent';
import {
  LibraChangeListSummaryOfYesterday,
  LibraFinishCloseAttribution,
  LibraFinishCloseAttributionSummary,
  LibraMsgCategory,
  LibraNotFinishCloseAttributionSummary,
  LibraTopicGroupsCardType,
  LibraTopicGroupsSendCardProps,
} from './LibraCard';
import { UserData, UserIdType } from '@pa/shared/dist/src/lark/userInfo';
import MessageService from '@pa/backend/dist/src/service/message';
import { getUserDepartmentNameByEmail } from '@api/storyRevenueReviewPlatform';
import { add_suffix_ne } from '@shared/utils/tools';
import { MsgCategory, MsgStrategy, MsgTemplate, MsgType } from '@pa/shared/dist/src/message';
import dayjs from 'dayjs';
import timezone from 'dayjs/plugin/timezone';
import utc from 'dayjs/plugin/utc';
import { SettingsDetail } from '@shared/settings/SettingsDetail';
import { PublishStatus } from '@shared/settings/SettingsABTestConfig';
import SettingsService from '../third/settings';
import StoryRevenueTaskDao from '../../dao/storyRevenueReviewPlatform/StoryRevenueTaskDao';
import { getLibraVersionPlatformFromFilterRule } from '../../utils/libraUtil';

const MEEGO_USER_KEY_PATTERN = /^\d{19}$/;

@Injectable()
export class LibraNewInfoListService {
  @Inject()
  private libraNewInfoListDao: LibraNewInfoListDao;
  @Inject()
  private libraMeegoTeamInfoDao: LibraMeegoTeamInfoDao;
  @Inject()
  private libraApi: LibraAPIService;
  @Inject()
  private libra: LibraService;
  @Inject()
  private meegoService: MeegoRawService;
  @Inject()
  private lark: LarkService;
  // 有获取业务线逻辑，借用下
  @Inject()
  private storyRevenueTaskUpdateService: StoryRevenueTaskUpdateMeegoInfoService;
  @Inject()
  private patrolInfoModel: ExperimentListInfoModel;
  @Inject()
  private logger: BytedLogger;
  @Inject()
  private queryProgressDao: LibraInfoQueryProgressDao;
  @Inject()
  private libraChangeEventDao: LibraChangeEventDao;
  @Inject()
  private messageService: MessageService;
  @Inject()
  private libraAPI: LibraAPIService;
  @Inject()
  private settingsService: SettingsService;
  @Inject()
  private storyRevenueTaskDao: StoryRevenueTaskDao;

  // 新建一个 Libra 实验信息
  async create(data: LibraNewInfo) {
    await this.libraNewInfoListDao.save(data);
  }

  async update(data: LibraNewInfo) {
    return this.libraNewInfoListDao.update(data);
  }

  async list(query: any, page: number, pageSize: number): Promise<LibraNewInfo[] | null> {
    const infos = await this.libraNewInfoListDao.list(query, page, pageSize);
    if (infos) {
      await this.fetchPatrolInfo(infos);
    }
    return infos;
  }

  async listNoSort(query: any, page: number, pageSize: number): Promise<LibraNewInfo[] | null> {
    const infos = await this.libraNewInfoListDao.listNoSort(query, page, pageSize);
    // if (infos) {
    //   await this.fetchPatrolInfo(infos);
    // }
    return infos;
  }

  async listAll(query: any): Promise<LibraNewInfo[] | null> {
    const infos = await this.libraNewInfoListDao.listAll(query);
    if (infos) {
      await this.fetchPatrolInfo(infos);
    }
    return infos;
  }

  async findOne(query: any): Promise<LibraNewInfo | null> {
    const info = await this.libraNewInfoListDao.findOne(query);
    if (info) {
      await this.fetchPatrolInfo([info]);
    }
    return info;
  }

  async count(query: any): Promise<number> {
    return this.libraNewInfoListDao.count(query);
  }

  async updateLibraInfoVersions() {
    const query = {
      'meegoInfo.id': { $exists: true },
      'flightInfo.startTime': { $gt: 1729267200 },
    };
    const toUpdateList = await this.libraNewInfoListDao.list(query);
    for (const toUpdate of toUpdateList) {
      await this.createLibraNewInfo(toUpdate.flightInfo.region, toUpdate.libraAppId, toUpdate.flightInfo.id);
    }
  }

  async fetchPatrolInfo(libraNewInfos: LibraNewInfo[]) {
    const query = {
      indicatorType: IndicatorType.Crash,
      id: { $in: libraNewInfos.map(info => info.flightInfo.id) },
    };
    const partorlInfos: DBExperimentListInfo[] = await this.patrolInfoModel.query(query);
    for (const libraNewInfo of libraNewInfos) {
      const matchPatrolInfos = partorlInfos.filter(info => info.id === libraNewInfo.flightInfo.id.toString());
      const crashPatrolInfos = matchPatrolInfos.filter(info => info.indicatorType === IndicatorType.Crash);
      libraNewInfo.patrolInfo = { stabilityMetrics: crashPatrolInfos } as LibraPatrolInfo;
    }
  }

  async handleLibraEventReceived(libraEvent: LibraEvent) {
    const flightId = libraEvent.flight_info.flight_id;
    const appIds = libraEvent.app_info.app_ids;
    const isSG =
      appIds.includes(LibraAppIds.cc) ||
      appIds.includes(LibraAppIds.cc_web) ||
      appIds.includes(LibraAppIds.cc_pc) ||
      appIds.includes(LibraAppIds.hypic);
    const flight = await this.libraApi.queryFlight(isSG ? LibraRegion.SG : LibraRegion.CN, Number(flightId));
    if (!flight) {
      return;
    }
    // 创建实验信息 or 已有时更新信息
    await this.createLibraNewInfo(isSG ? LibraRegion.SG : LibraRegion.CN, flight.app_id, flight.id);
  }

  async clearLibraNewInfo() {
    await this.libraNewInfoListDao.clearAll();
  }

  async updateExistedLibraNewInfo() {
    let page = 1;
    const pageSize = 10;
    const totalPage = ceil((await this.libraNewInfoListDao.count({})) / pageSize);
    do {
      const libraNewInfos = await this.libraNewInfoListDao.list({}, page, pageSize);
      if (libraNewInfos) {
        const updatePromises = libraNewInfos.map(async libraNewInfo => {
          await this.createLibraNewInfo(
            libraNewInfo.flightInfo.region,
            libraNewInfo.libraAppId,
            libraNewInfo.flightInfo.id,
          );
        });
        await Promise.all(updatePromises);
      }
      page++;
    } while (page < totalPage);
  }

  async createLibraNewInfo(
    region: LibraRegion,
    app: number,
    flightId: number,
    extraInfo: PaExtraInfo | undefined = undefined,
  ) {
    const flight = await this.libraApi.queryFlight(region, flightId);
    if (flight === undefined) {
      return;
    }
    const meegoInfos: WorkItemInfo[] = [];
    const libraMeegoGroups: Record<string, LibraMeego[]> = {};
    for (const libraMeegoInfo of flight.meego_info.meego_array) {
      if (!libraMeegoGroups[libraMeegoInfo.meego_project]) {
        libraMeegoGroups[libraMeegoInfo.meego_project] = [];
      }
      libraMeegoGroups[libraMeegoInfo.meego_project].push(libraMeegoInfo);
    }
    for (const project of Object.keys(libraMeegoGroups)) {
      const workItemIds = libraMeegoGroups[project].map(meegoInfo => parseInt(meegoInfo.meego_story, 10));
      const groupMeegoInfos = await this.meegoService.requestWorkItem(project, 'story', workItemIds);
      meegoInfos.push(...(groupMeegoInfos.data ?? []));
    }
    const existedInfo = ((await this.libraNewInfoListDao.findOne({
      'flightInfo.id': flightId,
      libraAppId: app,
    })) ?? {}) as LibraNewInfo;
    const flightInfo = await this.constructFlightInfo(flight, region, existedInfo);
    const libraMeegoInfos = await this.constructMeegoInfo(meegoInfos, flight, existedInfo);
    existedInfo.flightInfo = flightInfo;
    existedInfo.meegoInfo = libraMeegoInfos;
    existedInfo.appName = getAppNameByLibraAppId(app);
    existedInfo.libraAppId = app;
    if (extraInfo) {
      existedInfo.extraInfo = extraInfo;
    }
    // 填充 Meego 业务团队信息
    const meegoTeamList = await this.getMeegoTeamList(MeegoTeamIds, MeegoProjectKeyFaceU);
    const updatedMeegoTeamInfo = await this.genMeegoTeamInfoForSingleLibraInfo(existedInfo, meegoTeamList);
    if (updatedMeegoTeamInfo && updatedMeegoTeamInfo.length > 0) {
      existedInfo.meegoTeamInfo = updatedMeegoTeamInfo;
    } else if (existedInfo.meegoTeamInfo && existedInfo.meegoTeamInfo.length > 0) {
      // 说明需要清空
      existedInfo.meegoTeamInfo = [];
    }
    await this.libraNewInfoListDao.save(existedInfo);
    return existedInfo;
  }

  async storeStopReason(app: number, flightId: number, stopType: number, stopReason: string) {
    const existedInfo = ((await this.libraNewInfoListDao.findOne({
      'flightInfo.id': flightId,
      libraAppId: app,
    })) ?? {}) as LibraNewInfo;
    if (!existedInfo) {
      this.logger.error(
        `[LibraNewInfoListService] storeStopReason error: extraInfo is undefined, app: ${app}, flightId: ${flightId}`,
      );
      return;
    }
    if (!existedInfo.extraInfo) {
      this.logger.error(
        `[LibraNewInfoListService] storeStopReason error: extraInfo is undefined, app: ${app}, flightId: ${flightId}`,
      );
      return;
    }
    existedInfo.extraInfo.stopType = stopType;
    existedInfo.extraInfo.stopDetailReason = stopReason;
    await this.libraNewInfoListDao.save(existedInfo);
  }

  async queryLibraStartTimeAndEndTimeByAPI(flightId: number, region: LibraRegion) {
    const flight = await this.libraApi.queryFlight(region, flightId);
    if (!flight) {
      return {};
    }
    dayjs.extend(utc);
    dayjs.extend(timezone);

    const startTimestamp = new Date(flight.start_time).getTime() / 1000;
    const startTimestampUtc8 = dayjs.tz(flight.start_time, 'Asia/Shanghai').unix();

    const endTimestamp = new Date(flight.end_time).getTime() / 1000;
    const endTimestampUtc8 = dayjs.tz(flight.end_time, 'Asia/Shanghai').unix();

    return {
      startTime: flight.start_time,
      endTime: flight.end_time,
      startTimestamp,
      startTimestampUtc8,
      endTimestamp,
      endTimestampUtc8,
    };
  }

  async fixLibraStartTimeAndEndTime(flightId: number) {
    // 查询存量数据
    const libraInfo = await this.libraNewInfoListDao.findById(flightId.toString());
    if (!libraInfo) {
      return {};
    }

    const flight = await this.libraApi.queryFlight(libraInfo.flightInfo.region, flightId);
    if (!flight) {
      return {};
    }
    dayjs.extend(utc);
    dayjs.extend(timezone);

    const startTimestampUtc8 = dayjs.tz(flight.start_time, 'Asia/Shanghai').unix();
    const endTimestampUtc8 = dayjs.tz(flight.end_time, 'Asia/Shanghai').unix();
    libraInfo.flightInfo.startTime = startTimestampUtc8;
    libraInfo.flightInfo.endTime = endTimestampUtc8;
    await this.libraNewInfoListDao.save(libraInfo);
    return libraInfo;
  }

  async backTrackHistoryLibraInfo() {
    const queryAppids = [147, 305, 295];
    const queryRegions = [LibraRegion.CN, LibraRegion.SG, LibraRegion.VA];
    const onProgressQuery = await this.queryProgressDao.findOne({
      isEnd: false,
    });
    let searchProgress: LibraInfoQueryProgress;
    if (onProgressQuery) {
      searchProgress = onProgressQuery;
    } else {
      const curTime = Date.now() / 1000;
      const halfYearAgo = curTime - 180 * 24 * 60 * 60;
      const start_time = `${new Date(halfYearAgo * 1000)
        .toLocaleDateString('zh-CN', {
          year: 'numeric',
          month: '2-digit',
          day: '2-digit',
          hour: 'numeric',
          minute: 'numeric',
          second: 'numeric',
          timeZone: 'Asia/Shanghai',
        })
        .split('/')
        .join('-')}`;
      const end_time = `${new Date(curTime * 1000)
        .toLocaleDateString('zh-CN', {
          year: 'numeric',
          month: '2-digit',
          day: '2-digit',
          hour: 'numeric',
          minute: 'numeric',
          second: 'numeric',
          timeZone: 'Asia/Shanghai',
        })
        .split('/')
        .join('-')}`;
      searchProgress = {
        queryId: Date.now().toString(),
        region: queryRegions[0],
        failedFlightIs: [],
        totalPage: Number.MAX_SAFE_INTEGER,
        isEnd: false,
        isPending: true,
        searchArgs: {
          app: queryAppids[0],
          page: 1,
          page_size: 100,
          time_type: 2,
          start_time,
          end_time,
        } as LibraListQueryParams,
      };
    }
    const isEnd = !queryRegions.includes(searchProgress.region);
    searchProgress.isEnd = isEnd;
    searchProgress.isPending = false;
    if (isEnd) {
      searchProgress.isEnd = true;
      searchProgress.isPending = false;
      await this.queryProgressDao.save(searchProgress);
      return;
    }
    if (!searchProgress) {
      return;
    }
    do {
      if (!searchProgress.searchArgs) {
        break;
      }
      let flightSearchRes: FlightListResponse | undefined;
      try {
        flightSearchRes = await this.libraApi.queryFlightList(searchProgress.region, searchProgress.searchArgs);
      } catch (e) {
        this.logger.info(`[LibraNewInfoListService] queryFlightList error: ${e}`);
        searchProgress.isEnd = false;
        searchProgress.isPending = true;
        await this.queryProgressDao.save(searchProgress);
        break;
      }
      searchProgress.totalPage = flightSearchRes?.page?.total_page ?? Number.MAX_SAFE_INTEGER;
      for (const flight of flightSearchRes?.flights ?? []) {
        try {
          await this.createLibraNewInfo(
            searchProgress.region ?? LibraRegion.CN,
            searchProgress.searchArgs.app,
            flight.id,
          );
        } catch (e) {
          this.logger.info(`[LibraNewInfoListService] createLibraNewInfo error: ${e}`);
          searchProgress.failedFlightIs.push(flight.id);
          await this.queryProgressDao.save(searchProgress);
        }
      }
      searchProgress.searchArgs.page = flightSearchRes?.page?.current_page
        ? flightSearchRes?.page?.current_page + 1
        : (searchProgress.searchArgs.page ?? 0) + 1;
      if (searchProgress.searchArgs.page > searchProgress.totalPage) {
        searchProgress.searchArgs.app = queryAppids[queryAppids.indexOf(searchProgress.searchArgs.app) + 1];
        searchProgress.searchArgs.page = 1;
        if (!searchProgress.searchArgs.app) {
          searchProgress.region = queryRegions[queryRegions.indexOf(searchProgress.region) + 1];
          searchProgress.searchArgs.app = queryAppids[0];
        }
        if (!searchProgress.region) {
          searchProgress.isEnd = true;
          searchProgress.isPending = false;
          await this.queryProgressDao.save(searchProgress);
          break;
        }
      }
      await this.queryProgressDao.save(searchProgress);
    } while (!searchProgress.isEnd);
    await this.queryProgressDao.save(searchProgress);
  }

  async batchCreateLibraNewInfo(region: LibraRegion, app: number) {
    const args = {
      app,
      page: 1,
      page_size: 100,
    } as LibraListQueryParams;
    const curTime = Date.now() / 1000;
    const halfYearAgo = curTime - 180 * 24 * 60 * 60;
    args.start_time = `${new Date(halfYearAgo * 1000)
      .toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: 'numeric',
        minute: 'numeric',
        second: 'numeric',
        timeZone: 'Asia/Shanghai',
      })
      .split('/')
      .join('-')}`;
    args.time_type = 2;
    const flightSearchRes = await this.libraApi.queryFlightList(region, args);
    const totalPage = flightSearchRes?.page.total_page ?? 0;
    for (let i = 1; i <= totalPage; i++) {
      try {
        let currentPageFlights: Flight[] = [];
        if (i === 1) {
          currentPageFlights = flightSearchRes?.flights ?? [];
        } else if (i !== 1) {
          args.page = i;
          currentPageFlights = (await this.libraApi.queryFlightList(region, args))?.flights ?? [];
        }
        const meegoInfos: WorkItemInfo[] = [];
        const libraMeegoGroups: Record<string, LibraMeego[]> = {};
        for (const flight of currentPageFlights) {
          for (const libraMeegoInfo of flight.meego_info.meego_array) {
            if (!libraMeegoGroups[libraMeegoInfo.meego_project]) {
              libraMeegoGroups[libraMeegoInfo.meego_project] = [];
            }
            libraMeegoGroups[libraMeegoInfo.meego_project].push(libraMeegoInfo);
          }
        }
        for (const project of Object.keys(libraMeegoGroups)) {
          const workItemIds = libraMeegoGroups[project].map(meegoInfo => parseInt(meegoInfo.meego_story, 10));
          const groupMeegoInfos = await this.meegoService.requestWorkItem(project, 'story', workItemIds);
          meegoInfos.push(...(groupMeegoInfos.data ?? []));
        }
        for (const flight of currentPageFlights) {
          const existedInfo =
            (await this.libraNewInfoListDao.findOne({
              'flightInfo.id': flight.id,
              libraAppId: app,
            })) ?? undefined;
          const flightInfo = await this.constructFlightInfo(flight, region, existedInfo);
          const libraMeegoInfos = await this.constructMeegoInfo(meegoInfos, flight, existedInfo);
          const libraNewInfo: LibraNewInfo = existedInfo ? existedInfo : ({} as LibraNewInfo);
          libraNewInfo.flightInfo = flightInfo;
          libraNewInfo.meegoInfo = libraMeegoInfos;
          libraNewInfo.appName = getAppNameByLibraAppId(app);
          libraNewInfo.libraAppId = app;
          await this.libraNewInfoListDao.save(libraNewInfo);
        }
      } catch (e) {
        this.logger.error(`batchCreateLibraNewInfo error: ${e}`);
      }
    }
  }

  getLibraFlightType(
    region: LibraRegion,
    libraName: string,
    libraDescription: string,
    libraTags: string[],
    filterRules: FilterRule[],
  ) {
    // 解析一下实验类型
    const tagsJoinStr = libraTags.join(',');
    const stringsToCheck = [libraName, libraDescription, tagsJoinStr];

    // 定义实验类型和关键词的映射（反转实验、长期实验、线下实验、容器实验优先判断）
    const typeKeywordMap: { [key: string]: string[] } = {
      [LibraFlightType.Invert]: ['反转'],
      [LibraFlightType.LongTerm]: ['长期'],
      [LibraFlightType.Offline]: ['内测', '测试', '自测', 'test', '调试'],
      [LibraFlightType.Container]: ['容器'],
    };

    // 遍历映射对象
    for (const [type, keywords] of Object.entries(typeKeywordMap)) {
      if (stringsToCheck.some(str => keywords.some(keyword => str.toLowerCase().includes(keyword.toLowerCase())))) {
        return Number(type) as LibraFlightType;
      }
    }

    // 若还未找到实验类型，则判断一下灰度 or 正式
    // 从过滤条件里面，获取版本相关信息
    const versionInfoByFilter = getLibraVersionPlatformFromFilterRule(filterRules, region, libraName, libraDescription);
    const isGray =
      (versionInfoByFilter.android.isHit && versionInfoByFilter.android.isBeta) ||
      (versionInfoByFilter.iphone.isHit && versionInfoByFilter.iphone.isBeta);
    if (isGray) {
      // 判定为灰度实验
      return LibraFlightType.Gray;
    }

    // @FIXME 以上都不是，暂时兜底判定为正式实验，待后续人工纠正
    return LibraFlightType.Release;
  }

  async constructFlightInfo(flight: Flight, region: LibraRegion, existedInfo?: LibraNewInfo): Promise<LibraFlightInfo> {
    const owners = await this.lark.getUserInfoByEmails(flight.owner.map(owner => `${owner}@bytedance.com`));
    const baseUser = await this.libraApi.queryBaseUser(region, flight.id.toString());
    const versionInfos = flight.versions.map(version => {
      const baseusers = baseUser?.baseuser;
      let baseUserInfo;
      if (baseusers) {
        baseUserInfo = baseusers?.find(baseuser => baseuser.vid === version.id);
      }
      return {
        vid: version.id,
        testUser: version.test_user.split(','),
        type: version.type,
        userNumber: baseUserInfo?.baseuser ?? 0,
        description: version.description,
        vname: baseUserInfo?.vname ?? '',
        weight: version.weight,
      } as LibraVersionInfo;
    });

    dayjs.extend(utc);
    dayjs.extend(timezone);

    const res: LibraFlightInfo = {
      ...existedInfo?.flightInfo,
      region,
      name: flight.name,
      description: flight.description,
      id: flight.id,
      versions: versionInfos,
      owners: owners ?? [],
      libraType: flight.type,
      startTime: dayjs.tz(flight.start_time, 'Asia/Shanghai').unix(),
      endTime: dayjs.tz(flight.end_time, 'Asia/Shanghai').unix(),
      status: flight.status,
      trafficInfo: {
        trafficLayer: flight.layer_name,
        startTrafficValue: flight.last_gradual_traffic?.start_traffic ?? '',
        endTrafficValue: flight.last_gradual_traffic?.end_traffic ?? '',
        currentTrafficValue: flight.last_gradual_traffic?.effective_traffic ?? flight.version_resource,
      } as LibraTrafficInfo,
      layerType: flight.layer_type,
      tags: flight.tags,
      userNumber: baseUser?.sum_baseuser ?? 0,
      filterRule: flight.filter_rule,
      filterType: flight.filter_type,
    } as LibraFlightInfo;
    // 更新一下实验类型（正式、灰度、反转、长期等）
    if (!existedInfo?.flightInfo?.isManualMarkedType) {
      // 没有手动更新过，才进行更新
      res.type = this.getLibraFlightType(region, res.name, res.description, res.tags, res.filterRule);
    }

    return res;
  }

  // 将 meego 上的 owner 字符串转换成 User 数组
  async convertOwnersStringToUsers(owners: string[]): Promise<User[]> {
    const result: User[] = [];
    const meegoUserKeys: string[] = [];
    const emails: string[] = [];
    for (const owner of owners) {
      const tmpOwner = owner.trim().replace('@bytedance.com', '');
      if (MEEGO_USER_KEY_PATTERN.test(tmpOwner)) {
        // meego user key 形式
        meegoUserKeys.push(tmpOwner);
      } else {
        // 普通的英文名
        emails.push(`${tmpOwner}@bytedance.com`);
      }
    }
    // meego user key 形式，通过 Meego API 查询
    if (meegoUserKeys.length > 0) {
      const meegoUserInfoList = await this.meegoService.requestMeegoUserInfos({
        user_keys: meegoUserKeys,
      });
      for (const meegoUserInfo of meegoUserInfoList) {
        result.push({
          name: meegoUserInfo.name_cn ?? meegoUserInfo.name_en,
          email: meegoUserInfo.email,
          avatar: meegoUserInfo.avatar_url,
          open_id: meegoUserInfo.out_id,
        });
      }
    }

    if (emails.length > 0) {
      const userInfos = await this.lark.getUserInfoByEmails(emails);
      if (userInfos !== undefined && userInfos.length > 0) {
        result.push(...userInfos);
      }
    }

    return result;
  }

  // 通过 meego user key 获得 User 对象，并返回 userKey <-> User 的映射关系表
  async getMeegoUserKeyAndUserInfoMap(allKeys: string[]): Promise<{ [key: string]: User }> {
    const result: { [key: string]: User } = {};
    const meegoUserKeys: string[] = [];
    const emails: string[] = [];
    for (const owner of allKeys) {
      const tmpOwner = owner.trim().replace('@bytedance.com', '');
      if (MEEGO_USER_KEY_PATTERN.test(tmpOwner)) {
        // meego user key 形式
        meegoUserKeys.push(tmpOwner);
      } else {
        // 普通的英文名
        emails.push(`${tmpOwner}@bytedance.com`);
      }
    }
    // meego user key 形式，通过 Meego API 查询
    if (meegoUserKeys.length > 0) {
      const meegoUserInfoList = await this.meegoService.requestMeegoUserInfos({
        user_keys: meegoUserKeys,
      });
      for (const meegoUserInfo of meegoUserInfoList) {
        result[meegoUserInfo.user_key] = {
          name: meegoUserInfo.name_cn ?? meegoUserInfo.name_en,
          email: meegoUserInfo.email,
          avatar: meegoUserInfo.avatar_url,
          open_id: meegoUserInfo.out_id,
        } as User;
      }
    }

    if (emails.length > 0) {
      const userInfos = await this.lark.getUserInfoByEmails(emails);
      if (userInfos !== undefined && userInfos.length > 0) {
        for (const userInfo of userInfos) {
          if (userInfo.email) {
            result[userInfo.email.replace('@bytedance.com', '')] = userInfo;
          }
        }
      }
    }

    return result;
  }

  getMeegoType(templateId: number): string {
    if (templateId === 248986) {
      return '产品需求';
    } else if (templateId === 268036) {
      return '技术需求';
    }
    return '其他';
  }

  // 获取 Meego 字段描述（根据字段 value 值）
  // work_item_status
  // field_2a112b
  getMeegoFiledLabel(matchedFieldKey: string, matchedValue: string): string {
    for (const fieldInfo of StoryRevenueMeegoFieldInfo) {
      const { field_key } = fieldInfo;
      if (field_key !== matchedFieldKey) {
        continue;
      }
      const { options } = fieldInfo;
      for (const option of options) {
        const { label, value } = option;
        if (value === matchedValue) {
          return label;
        }
      }
    }

    return '';
  }

  checkIsObject(value: any): value is Record<string, any> {
    return value !== null && value !== undefined && typeof value === 'object' && !Array.isArray(value);
  }

  async constructMeegoInfo(workItemInfos: WorkItemInfo[], flight: Flight, existedInfo?: LibraNewInfo) {
    const res: LibraMeegoInfo[] = [];
    for (const libraMeego of flight.meego_info.meego_array) {
      const meegoInfo = workItemInfos.find(
        workItemInfo =>
          workItemInfo.id === parseInt(libraMeego.meego_story, 10) &&
          ((libraMeego.meego_project_key && workItemInfo.project_key === libraMeego.meego_project_key) ||
            (libraMeego.meego_project && workItemInfo.simple_name === libraMeego.meego_project)),
      );
      if (meegoInfo) {
        // 一级业务线和二级业务线
        let primary_business = '';
        let secondary_business = '';
        // 上线区域
        const published_regions = [];
        // 上线应用
        const published_apps = [];
        // 上车版本
        let planingVersions: string[] = [];
        // 需求 PM
        let pmOwners: User[] = [];
        // 技术 Owner
        let techOwners: User[] = [];
        // 需求 DA
        let daOwners: User[] = [];
        // 业务 QA
        let clientQAs: User[] = [];
        let chatId = '';
        const { simple_name, work_item_type_key, fields, template_id, work_item_status } = meegoInfo;
        // 需求类型
        const type = this.getMeegoType(template_id);
        // 需求状态
        const { state_key } = work_item_status;
        const meego_status = this.getMeegoFiledLabel('work_item_status', state_key);
        // 需求优先级
        let priority = '';
        // 是否需要 AB 实验
        let needAB = false;
        // 实验设计文档
        let libraDesignDocUrl = '';
        const targetFieldKeys = [
          'business',
          'field_74de8f',
          'supported_apps',
          'planning_version',
          'chat_group',
          'role_owners',
          'field_1f5a50',
          'priority',
          'need_ab', // 是否需要 AB 实验
          'field_f8faa7', // 实验设计文档
        ];
        const filteredFields = fields.filter(field => targetFieldKeys.includes(field.field_key));
        for (const field of filteredFields) {
          const { field_key } = field;
          if (field_key === 'business') {
            // 获取一级业务线 和 二级业务线
            const business_line_value = field.field_value;
            if (typeof business_line_value === 'string') {
              const result = this.storyRevenueTaskUpdateService.getMeegoBusinessLineLabel(business_line_value);
              primary_business = result[0];
              secondary_business = result[1];
              console.log(`[StoryRevenueTaskUpdateMeegoInfoService] updateMeegoInfo, business_line_value: ${result}`);
            }
          } else if (field_key === 'field_74de8f') {
            // 上线区域
            const regions = field.field_value;
            if (regions && Array.isArray(regions)) {
              for (const region of regions) {
                published_regions.push((region as FieldValue).label);
              }
            }
          } else if (field_key === 'supported_apps') {
            // 上线应用
            const apps = field.field_value;
            if (apps && Array.isArray(apps)) {
              for (const app of apps) {
                published_apps.push((app as FieldValue).label);
              }
            }
          } else if (field_key === 'planning_version') {
            if ((field.field_value as number[]).length) {
              const versionIdList: { versionId: number; versionName: string }[] =
                (await this.meegoService.queryVersionById(meegoInfo.project_key, field.field_value as number[])) as {
                  versionId: number;
                  versionName: string;
                }[];
              if (versionIdList?.length) {
                planingVersions.push(...versionIdList.map(version => version.versionName));
              }
            }
          } else if (field_key === 'role_owners') {
            const rolesJson = JSON.stringify(field.field_value);
            const roles = JSON.parse(rolesJson) as Roles[];
            for (const role of roles) {
              if (role.owners && role.role === 'PM') {
                // 需求 PM
                pmOwners = await this.convertOwnersStringToUsers(role.owners);
              } else if (role.owners && role.role === 'role_501834') {
                // 技术 Owner
                techOwners = await this.convertOwnersStringToUsers(role.owners);
              } else if (role.owners && role.role === 'DA') {
                // DA
                daOwners = await this.convertOwnersStringToUsers(role.owners);
              } else if (role.owners && role.role === 'clientqa') {
                // 业务 QA
                clientQAs = await this.convertOwnersStringToUsers(role.owners);
              }
            }
          } else if (field_key === 'chat_group') {
            // 需求群
            chatId = field.field_value as string;
          } else if (field_key === 'priority') {
            // 需求优先级
            const priority_value = field.field_value;
            if (priority_value && this.checkIsObject(priority_value)) {
              priority = (priority_value as FieldValue).label;
            }
          } else if (field_key === 'need_ab') {
            // 是否开启 AB 实验
            needAB = field.field_value as boolean;
          } else if (field_key === 'field_f8faa7') {
            // 实验设计文档
            libraDesignDocUrl = field.field_value as string;
          }
        }
        const realVersionField = filteredFields.find(field => field.field_key === 'field_1f5a50');
        if (realVersionField) {
          if ((realVersionField.field_value as number[]).length) {
            const versionIdList: { versionId: number; versionName: string }[] =
              (await this.meegoService.queryVersionById(
                meegoInfo.project_key,
                realVersionField.field_value as number[],
              )) as {
                versionId: number;
                versionName: string;
              }[];
            if (versionIdList?.length) {
              planingVersions = versionIdList.map(version => version.versionName);
            }
          }
        }
        const meego_url = `https://meego.larkoffice.com/${simple_name}/${work_item_type_key}/detail/${meegoInfo.id}`;
        const flightMeegoInfo: LibraMeegoInfo = {
          id: meegoInfo.id,
          name: meegoInfo.name,
          url: meego_url,
          businessLine: [primary_business, secondary_business],
          releaseApps: published_apps,
          releaseVersion: planingVersions,
          pmOwners,
          techOwners,
          daOwners,
          chatId,
          type,
          clientQAs,
          priority,
          status: meego_status,
          releaseRegions: published_regions,
          needAB,
          libraDesignDocUrl,
        } as LibraMeegoInfo;
        res.push(flightMeegoInfo);
      }
    }
    return res;
  }

  async updateMeegoInfo(flightId: number) {
    // 先查询数据库是否存在
    const libraInfo = await this.libraNewInfoListDao.findById(flightId.toString());
    if (!libraInfo) {
      return {
        code: -1,
        msg: 'libra info not found',
        data: {},
      };
    }
    // 通过 Libra API 查询实验最新数据
    const flight = await this.libraApi.queryFlight(libraInfo.flightInfo.region, flightId);
    if (flight === undefined) {
      return {
        code: -1,
        msg: 'query flight info by libra api failed',
        data: {},
      };
    }
    const meegoInfos: WorkItemInfo[] = [];
    const libraMeegoGroups: Record<string, LibraMeego[]> = {};
    for (const libraMeegoInfo of flight.meego_info.meego_array) {
      if (!libraMeegoGroups[libraMeegoInfo.meego_project]) {
        libraMeegoGroups[libraMeegoInfo.meego_project] = [];
      }
      libraMeegoGroups[libraMeegoInfo.meego_project].push(libraMeegoInfo);
    }
    for (const project of Object.keys(libraMeegoGroups)) {
      const workItemIds = libraMeegoGroups[project].map(meegoInfo => parseInt(meegoInfo.meego_story, 10));
      const groupMeegoInfos = await this.meegoService.requestWorkItem(project, 'story', workItemIds);
      meegoInfos.push(...(groupMeegoInfos.data ?? []));
    }
    // 生成新的 Meego Info
    libraInfo.meegoInfo = await this.constructMeegoInfo(meegoInfos, flight, libraInfo);
    await this.libraNewInfoListDao.save(libraInfo);
    return {
      code: 0,
      msg: 'success',
      data: libraInfo,
    };
  }

  async editFlightInfo(
    flightId: number,
    libraAppId: number,
    keyPath: string,
    newValue: any,
    tmpValue: any,
    editEmail: string,
    editConfig?: any,
  ): Promise<{ result: LibraNewInfoEditaResultType; newInfo?: LibraNewInfo }> {
    const existedInfo = await this.libraNewInfoListDao.findOne({ 'flightInfo.id': flightId, libraAppId });
    if (!existedInfo) {
      return { result: LibraNewInfoEditaResultType.UnknownError, newInfo: undefined };
    }
    const keys = keyPath.split('.');
    let existedValue: any = existedInfo;
    keys.forEach(key => {
      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
      // @ts-ignore
      existedValue = existedValue[key];
    });
    if (
      typeof tmpValue === typeof existedValue ||
      ((existedValue === undefined || existedValue === null) && (tmpValue === undefined || tmpValue === null))
    ) {
      if (Array.isArray(tmpValue)) {
        if (!this.compareArray(tmpValue as any[], existedValue as any[])) {
          return { result: LibraNewInfoEditaResultType.Conflict, newInfo: existedInfo };
        }
      } else {
        if (tmpValue !== existedValue) {
          return { result: LibraNewInfoEditaResultType.Conflict, newInfo: existedInfo };
        }
      }
    } else {
      return { result: LibraNewInfoEditaResultType.UnknownError, newInfo: undefined };
    }
    const updateQuery: { [key: string]: any } = {};
    updateQuery[keyPath] = newValue;
    // @FIXME: 特殊逻辑，当 keyPath 为 'flightInfo.type' 时，需要更新 isManualMarkedType 为 true
    if (keyPath === 'flightInfo.type') {
      updateQuery['flightInfo.isManualMarkedType'] = true;
    }
    await this.libraNewInfoListDao.updateOne({ 'flightInfo.id': flightId, libraAppId }, updateQuery);
    const newInfo = await this.libraNewInfoListDao.findOne({ 'flightInfo.id': flightId, libraAppId });
    return { result: LibraNewInfoEditaResultType.Success, newInfo: newInfo ?? undefined };
  }

  // 获取 Meego 团队（通过 Meego API）
  async getMeegoTeamByMeegoAPI(teamIds: number[], projectKey: string): Promise<LibraMeegoTeamInfo[]> {
    if (teamIds?.length === 0) {
      return [];
    }

    // 最终返回结果
    const teamList: LibraMeegoTeamInfo[] = [];

    // Meego API 请求回包
    const res = await this.meegoService.queryMeegoTeam(projectKey);
    // // @FIXME: 支持配置方式(根据 team_id 过滤)
    // const filterIds: number[] = [
    //   38624, // 【剪映】-移动端-音视频-统计-实验
    //   38622, // 素材与内容-剪映
    //   38612, // 智能剪辑实验同步群
    //   38603, // 【无需关注】【剪映】-主框架-统计
    //   38600, // 【无需关注】【剪映】-基础剪辑-统计（持续更新）
    //   38227, // 剪映内容生态
    // ];

    // 找寻在 filterIds 里面的团队
    const meegoTeams = res.filter(team => {
      if (teamIds.includes(team.team_id)) {
        // 如果直接包含，则返回 true
        return true;
      }
      const shortMeegoTeamId = MeegoTeamShortIdByMeegoTeamStr(team.team_id.toString());
      return teamIds.includes(shortMeegoTeamId);
    });
    // 汇总所有的 keys
    const keysSet: Set<string> = new Set();
    for (const meegoTeam of meegoTeams) {
      meegoTeam.administrators.forEach(user => keysSet.add(user));
      meegoTeam.user_keys.forEach(user => keysSet.add(user));
    }
    const allUserKeys: string[] = Array.from(keysSet);

    // 按批次查询 User（每一个 batch 最大数目为 50）
    const batchSize = 50; // 每批次的大小
    const batches: string[][] = []; // 存储所有批次的数组
    // 按批次拆分
    for (let i = 0; i < allUserKeys.length; i += batchSize) {
      const batch = allUserKeys.slice(i, i + batchSize); // 截取当前批次
      batches.push(batch); // 将当前批次添加到 batches 数组中
    }
    const userInfoMap: { [key: string]: User } = {};
    for (const batch of batches) {
      Object.assign(userInfoMap, await this.getMeegoUserKeyAndUserInfoMap(batch));
    }

    // 遍历 meegoTeams 生成 LibraMeegoTeamInfo
    for (const meegoTeam of meegoTeams) {
      let teamId = meegoTeam.team_id;
      if (teamId > 1000000) {
        // 一般的 teamId 是 39000、39025、39067 这样的 5 位数，如果长度太大了，则转换为 short team id
        // 背景：meego 返回的原始 teamId 是 7495787514574438428 这种长度的 id, `number` 类型的精度承载不了，纸飞机自己转换一下
        teamId = MeegoTeamShortIdByMeegoTeamStr(teamId.toString());
      }
      const teamName = meegoTeam.team_name;
      const administratorKeys = meegoTeam.administrators;
      const userKeys = meegoTeam.user_keys;
      // 从 userInfoList 筛选出 administrators
      const administrators = administratorKeys.map(
        key =>
          userInfoMap[key] ||
          ({
            name: key,
            email: `${key}@bytedance.com`,
            open_id: '',
          } as User),
      );
      // 从 userInfoList 筛选出 users
      const users = userKeys.map(
        key =>
          userInfoMap[key] ||
          ({
            name: key,
            email: `${key}@bytedance.com`,
            open_id: '',
          } as User),
      );
      // 将 MeegoTeam 转换为 LibraMeegoTeamInfo
      teamList.push({
        teamId,
        teamName,
        administrators,
        members: users,
        projectKey,
      } as LibraMeegoTeamInfo);
    }

    return teamList;
  }

  // 获取 Meego 团队（通过纸飞机数据库）
  async getMeegoTeamList(teamIds: number[], projectKey: string): Promise<LibraMeegoTeamInfo[]> {
    if (teamIds?.length === 0) {
      return [];
    }

    return this.libraMeegoTeamInfoDao.listAll({
      teamId: {
        $in: teamIds,
      },
      projectKey,
    });
  }

  // 根据 email 查询团队信息
  async getMeegoTeamListByEmail(email: string): Promise<LibraMeegoTeamInfo[]> {
    return this.libraMeegoTeamInfoDao.listAll({
      'members.email': email,
    });
  }

  // 根据 email+libraRegion 获取 Meego 主责团队 Id（主责 teamId 是唯一的，但也有可能找不到为 0）
  async getMeegoMainTeamIdByEmailAndLibraRegion(email: string, libraRegion: LibraRegion): Promise<number> {
    if (email.length === 0 || email === '<EMAIL>' || email === '<EMAIL>') {
      // 校验一下非法值
      return 0;
    }

    // 先查询 MeegoTeam，看是否有唯一映射团队
    const belongedTeamList = await this.getMeegoTeamListByEmail(email);
    if (belongedTeamList && belongedTeamList.length === 1) {
      return belongedTeamList[0].teamId;
    } else {
      // 如果没有唯一映射团队（0个或多个），则尝试查询组织架构信息
      try {
        const departmentInfo = await getUserDepartmentNameByEmail({ data: { email } });
        // 根据邮箱，查询组织架构信息
        const departmentName = departmentInfo ? departmentInfo.departmentName : '';
        const belongedTeamId = GetLibraMeegoTeamIdByDepartmentName(departmentName, libraRegion);
        if (belongedTeamId > 0) {
          return belongedTeamId;
        }
      } catch (e) {
        this.logger.error(
          // eslint-disable-next-line max-len
          `[LibraEventBusHandler][handleFlightStop] getUserDepartmentNameByEmail(${email}) failed: ${JSON.stringify(e)}`,
        );
      }
    }

    return 0;
  }

  // 更新 Meego 团队（需要先通过 Meego API 查询数据，然后更新到纸飞机数据库）
  async updateMeegoTeamList(teamIds: number[], projectKey: string) {
    if (teamIds?.length === 0) {
      return {
        code: -1,
        message: 'teamIds are invalid!',
      };
    }

    const result = await this.getMeegoTeamByMeegoAPI(teamIds, projectKey);
    if (!result) {
      return {
        code: -1,
        message: 'get team list by meego api failed!',
      };
    }

    for (const team of result) {
      await this.libraMeegoTeamInfoDao.create(team);
    }
    return {
      code: 0,
      message: 'success',
    };
  }

  // 更新单个 Meego 团队信息
  async updateChatIdOfMeegoTeamInfo(teamId: number, chatId: string) {
    if (chatId.length === 0) {
      return {
        code: -1,
        message: 'chatId is invalid!',
      };
    }

    // 先拉取最新的 teamInfo
    const teamInfo = await this.libraMeegoTeamInfoDao.findOne({ teamId });
    if (!teamInfo) {
      return {
        code: -1,
        message: 'teamInfo is not exist!',
      };
    }
    // 更新 chatId
    teamInfo.larkChatId = chatId;
    return await this.libraMeegoTeamInfoDao.update(teamInfo);
  }

  // 为某个 LibraInfo 更新主责团队 MainTeamId
  async updateMainTeamOfLibraInfo(flightId: number, libraAppId: number, mainTeamId: number, shouldClear = false) {
    // 先拉取最新的 libraInfo
    const latestLibraInfo = await this.libraNewInfoListDao.findOne({
      'flightInfo.id': flightId,
      libraAppId,
    });
    if (!latestLibraInfo) {
      return {
        code: -1,
        message: 'libraInfo is not exist!',
      };
    }

    if (shouldClear) {
      // 说明是需要清除主责团队
      let didClear = false;
      if (latestLibraInfo.meegoTeamInfo && latestLibraInfo.meegoTeamInfo.length > 0) {
        for (const teamInfo of latestLibraInfo.meegoTeamInfo) {
          if (teamInfo.isMainTeam) {
            teamInfo.isMainTeam = false;
            didClear = true;
            break;
          }
        }
        if (didClear) {
          await this.libraNewInfoListDao.save(latestLibraInfo);
          // 直接返回
          return {
            code: 0,
            message: 'success',
            data: latestLibraInfo,
          };
        }
      }
    }

    // 更新 mainTeamId
    let mainTeamIdChanged = false;
    if (latestLibraInfo.meegoTeamInfo && latestLibraInfo.meegoTeamInfo.length > 0) {
      for (const teamInfo of latestLibraInfo.meegoTeamInfo) {
        if (teamInfo.teamId === mainTeamId) {
          // 找到了已经存在的 teamId，直接赋值为主责团队
          if (!teamInfo.isMainTeam) {
            teamInfo.isMainTeam = true;
            mainTeamIdChanged = true;
          }
        } else {
          if (teamInfo.isMainTeam) {
            // 将之前的主责团队，其设置为非主责团队
            teamInfo.isMainTeam = false;
            mainTeamIdChanged = true;
          }
        }
      }
    }

    // 最后统一更新下
    if (mainTeamIdChanged) {
      await this.libraNewInfoListDao.save(latestLibraInfo);
    }
    return {
      code: 0,
      message: 'success',
      data: latestLibraInfo,
    };
  }

  // 遍历存量 LibraInfo 数据，加上 Meego 团队信息
  async addMeegoTeamInfoToLibraInfo(teamIds: number[], projectKey: string, query?: any) {
    let recordFlightId = 0;
    try {
      // 拉取所有实验列表
      const libraInfoList = await this.libraNewInfoListDao.listAll(query ?? {});
      // 拉取所有团队信息
      const meegoTeamList = await this.getMeegoTeamList(teamIds, projectKey);
      for (const libraInfo of libraInfoList) {
        const updatedMeegoTeamInfo = await this.genMeegoTeamInfoForSingleLibraInfo(libraInfo, meegoTeamList);
        // 说明找到 Meego 团队信息，最后统一保存下
        let shouldUpdate = false;
        if (updatedMeegoTeamInfo && updatedMeegoTeamInfo.length > 0) {
          libraInfo.meegoTeamInfo = updatedMeegoTeamInfo;
          shouldUpdate = true;
        } else if (libraInfo.meegoTeamInfo && libraInfo.meegoTeamInfo.length > 0) {
          // 说明没有找到 Meego 团队信息，但是之前已经有了，需要清空
          libraInfo.meegoTeamInfo = [];
          shouldUpdate = true;
        }
        if (shouldUpdate) {
          recordFlightId = libraInfo.flightInfo.id;
          // 避免报错：MongoError: Performing an update on the path '_id' would modify the immutable field '_id'
          const rawData = libraInfo.toObject(); // 转换为普通对象
          const cleanedData = omit(rawData, ['_id']);
          await this.libraNewInfoListDao.save(cleanedData as LibraNewInfo);
        }
      }

      return {
        code: 0,
        message: 'success',
      };
    } catch (error) {
      // 打印一下错误
      this.logger.info(
        `[LibraNewInfoListService] addMeegoTeamInfoToLibraInfo error: ${error}, flightId: ${recordFlightId}`,
      );
      return {
        code: -1,
        message: `${error}`,
      };
    }
  }

  // 为已存在的 libraInfo 生成 MeegoTeamInfo
  async genMeegoTeamInfoForSingleLibraInfo(libraInfo: LibraNewInfo, meegoTeamList: LibraMeegoTeamInfo[]) {
    if (libraInfo.flightInfo.owners.length === 0) {
      return;
    }

    const updatedMeegoTeamInfo: LibraMeegoTeamSimpleInfo[] = [];
    const libraRegion = libraInfo.flightInfo.region;

    // 确定一下，libraInfo 里面是否有存量的信息（特别是主责团队），需要确保主责团队不被覆盖到
    let mainTeamId = 0;
    let firstOwnerTeamId = 0;
    if (libraInfo.meegoTeamInfo && libraInfo.meegoTeamInfo.length > 0) {
      for (const teamInfo of libraInfo.meegoTeamInfo) {
        if (teamInfo.isMainTeam && checkIfTeamIdMatchLibraRegion(teamInfo.teamId, libraRegion)) {
          mainTeamId = teamInfo.teamId;
          break;
        }
      }
    }

    let operatorEmail = '';
    if (mainTeamId === 0) {
      // 如果还未找到主责团队，则尝试添加一下主责团队
      // 规则 1：看实验操作人是否有归属业务团队（优先）
      // 规则 2：看实验第一个 Owner 是否有归属业务团队
      const libraChangeEventList = await this.libraChangeEventDao.listAll({
        flightId: libraInfo.flightInfo.id,
      });
      if (libraChangeEventList && libraChangeEventList.length > 0) {
        const libraOperator = libraChangeEventList[0].eventOperator;
        if (libraOperator.length > 0 && !(libraOperator === 'Libra' || libraOperator === 'OpenAPI')) {
          operatorEmail = add_suffix_ne('@bytedance.com')(libraOperator);
        }
      }
    }

    // 遍历实验 Owners，看看是否有在团队里面
    const ownerKeys = libraInfo.flightInfo.owners.map(owner => owner.email ?? '');
    for (const meegoTeam of meegoTeamList) {
      // meegoTeam 的 id 是否和 Libra 的 Region 匹配，即 CN 的实验，它的团队 id 也是需要是 CN 的
      const isTeamIdMatchLibraRegion = checkIfTeamIdMatchLibraRegion(meegoTeam.teamId, libraRegion);
      const originMembers = meegoTeam.members.map(member => member.email ?? '');
      // originMembers 中剔除 MeegoTeamExcludeMembers 包含的成员
      const members = originMembers.filter(member => !MeegoTeamExcludeMembers[meegoTeam.teamId].includes(member));
      if (operatorEmail.length > 0 && mainTeamId === 0) {
        // 如果还未找到主责团队，且操作人 email 不为空，则尝试根据操作人找到 mainTeamId
        if (members.includes(operatorEmail) && isTeamIdMatchLibraRegion) {
          mainTeamId = meegoTeam.teamId;
        }
      }
      if (firstOwnerTeamId === 0) {
        //  如果还未找到主责团队，则根据第一个实验 Owner 所属的团队判定
        const firstLibraOwner = ownerKeys.length > 0 ? ownerKeys[0] : '';
        if (firstLibraOwner.length > 0 && members.includes(firstLibraOwner) && isTeamIdMatchLibraRegion) {
          firstOwnerTeamId = meegoTeam.teamId;
        }
      }

      // 如果 ownerKeys 在 members 中，则将 meegoTeam 添加到 libraInfo.meegoTeamInfo 中
      if (ownerKeys.some(key => members.includes(key))) {
        // 需要做一下去重，避免重复添加 teamId 相同的数据
        if (updatedMeegoTeamInfo.some(team => team.teamId === meegoTeam.teamId)) {
          continue;
        }
        let isMainTeam = false;
        if (mainTeamId > 0) {
          isMainTeam = mainTeamId === meegoTeam.teamId;
        } else if (firstOwnerTeamId > 0) {
          isMainTeam = firstOwnerTeamId === meegoTeam.teamId;
        }
        updatedMeegoTeamInfo.push({
          teamId: meegoTeam.teamId,
          teamName: meegoTeam.teamName,
          projectKey: meegoTeam.projectKey,
          isMainTeam,
        });
      }
    }

    if (updatedMeegoTeamInfo.length === 1) {
      // 如果只有 1 个团队信息，那么它也就是主责团队
      updatedMeegoTeamInfo[0].isMainTeam = true;
    }

    return updatedMeegoTeamInfo;
  }

  // 获取 Meego 团队数据源（用于 TreeSelect）
  async getMeegoTeamTreeSelectDataSource() {
    // @FIXME: 支持配置方式(根据 team_id 过滤)
    const teamList = await this.getMeegoTeamList(MeegoTeamIds, MeegoProjectKeyFaceU);
    const treeData: TreeEnumOptions[] = [];
    for (const team of teamList) {
      const { teamId, teamName } = team;
      const treeNode: TreeEnumOptions = {
        label: teamName,
        value: teamName,
        key: `${teamId}`,
        children: [],
      };
      treeData.push(treeNode);
    }
    return treeData;
  }

  // 将制定人员拉入到指定的群
  async inviteMembersToChatGroup(chatId: string, members: string[]) {
    const batchSize = 50;
    if (members.length < batchSize) {
      const res = await this.lark.addUserToChatGroup(chatId, UserIdType.openId, members);
      this.logger.info(`[inviteMembersToChatGroup] res: ${JSON.stringify(res)}`);
    } else {
      // 如果 members 数量大于 50，则需要分批次处理
      const batches: string[][] = [];
      for (let i = 0; i < members.length; i += batchSize) {
        const batch = members.slice(i, i + batchSize);
        batches.push(batch);
      }
      for (const batch of batches) {
        const res = await this.lark.addUserToChatGroup(chatId, UserIdType.openId, batch);
        this.logger.info(`[inviteMembersToChatGroup] by batch, res: ${JSON.stringify(res)}`);
      }
    }
  }

  // 话题群发送卡片消息
  async didSendNotifyCardToTopicGroups(sendCardProps: LibraTopicGroupsSendCardProps) {
    const {
      cardType,
      query,
      dateRange,
      fixedTeamIds,
      sendWhenDataEmpty,
      sendPrivate,
      customTitle,
      customChatId,
      inviteLibraOperatorsJoinGroup,
      dontTryToFindMainTeamId,
    } = sendCardProps;

    // 遍历 LibraChangeEvent 列表中，ts 在 [startOfThisWeekTimestamp, endOfThisWeekTimestamp] 内的所有实验；然后再进一步筛选包含在 teamIds 里面的实验
    const libraChangeEventList = await this.libraChangeEventDao.listAll(query);

    // 从 libraChangeEventList 中提取出 flightId 列表
    const flightIds = libraChangeEventList.map(event => event.flightId);
    console.log(`flightIds: ${flightIds}`);
    // 由于 libraChangeEventList 没有包含完整的 libraNewInfo 信息，需要通过 flightIds 再完整获取一遍
    const libraNewInfoList = await this.libraNewInfoListDao.listAll({
      'flightInfo.id': {
        $in: flightIds,
      },
    });
    // 将 libraChangeEventList 根据 ts 排序（最新的时间排在前面）
    libraChangeEventList.sort((a, b) => b.ts - a.ts);
    // 结合 libraNewInfoList 和 libraChangeEventList 生成飞书卡片通知
    // 将 libraNewInfoList 和 libraChangeEventList 通过 flightId 配对
    const finalMap: Map<number, { event: LibraChangeEvent; libraInfo: LibraNewInfo }> = new Map();
    for (const event of libraChangeEventList) {
      const libraInfo = libraNewInfoList.find(info => info.flightInfo.id === event.flightId);
      if (libraInfo) {
        finalMap.set(event.flightId, { event, libraInfo });
      }
    }

    // 分移动端、PC、Web 推送（发送三张卡片）
    const lvMobileMap: Map<number, { event: LibraChangeEvent; libraInfo: LibraNewInfo }> = new Map();
    const ccMobileMap: Map<number, { event: LibraChangeEvent; libraInfo: LibraNewInfo }> = new Map();
    const pcMap: Map<number, { event: LibraChangeEvent; libraInfo: LibraNewInfo }> = new Map();
    const ccWebMap: Map<number, { event: LibraChangeEvent; libraInfo: LibraNewInfo }> = new Map();
    const lvWebMap: Map<number, { event: LibraChangeEvent; libraInfo: LibraNewInfo }> = new Map();
    const serverMap: Map<number, { event: LibraChangeEvent; libraInfo: LibraNewInfo }> = new Map();
    const commerceMap: Map<number, { event: LibraChangeEvent; libraInfo: LibraNewInfo }> = new Map();
    const multiMediaMap: Map<number, { event: LibraChangeEvent; libraInfo: LibraNewInfo }> = new Map();
    const retouchMap: Map<number, { event: LibraChangeEvent; libraInfo: LibraNewInfo }> = new Map();
    const filteredLibraChangeEventList: LibraChangeEvent[] = [];
    for (const [key, value] of finalMap) {
      const { event } = value;
      const { libraInfo } = value;
      // 筛选出移动端、PC、Web、Server 实验
      if (libraInfo.meegoTeamInfo && libraInfo.meegoTeamInfo.length > 0) {
        // 获取 teamInfo 里面的所有 teamId
        let teamIds = libraInfo.meegoTeamInfo.map(team => team.teamId);
        if (fixedTeamIds && fixedTeamIds.every(item => teamIds.includes(item))) {
          // 如果指定了 teamId，则只推送指定的 teamId 对应的实验
          teamIds = [...fixedTeamIds];
        }
        // 先查询看是否有主责团队
        const mainTeamIds = libraInfo.meegoTeamInfo.filter(team => team.isMainTeam).map(team => team.teamId);
        let mainTeamId = 0;
        if (mainTeamIds.length > 0) {
          // 如果有主责团队，则优先使用主责团队（将该实验划分到主责团队对应的 team）
          mainTeamId = mainTeamIds[0];
        } else if (!dontTryToFindMainTeamId) {
          // 如果没有主责团队，则根据实验的关闭人，尝试寻找主责团队
          mainTeamId = await this.getMeegoMainTeamIdByEmailAndLibraRegion(
            add_suffix_ne('@bytedance.com')(event.eventOperator),
            libraInfo.flightInfo.region,
          );
        }
        if (mainTeamId > 0) {
          // 找到了主责团队，优先推给主责团队。但是也要确保 teamIds 包含这个主责团队 Id，否则就不推送了
          if (teamIds.includes(mainTeamId)) {
            if (MeegoTeamIdsForJianyingMobile.includes(mainTeamId) && libraInfo.libraAppId === 147) {
              lvMobileMap.set(Number(key), value);
              filteredLibraChangeEventList.push(event);
            } else if (MeegoTeamIdsForCapCutMobile.includes(mainTeamId) && libraInfo.libraAppId === 305) {
              ccMobileMap.set(Number(key), value);
              filteredLibraChangeEventList.push(event);
            } else if (MeegoTeamIdsForJianyingPC.includes(mainTeamId) && libraInfo.libraAppId === 399) {
              pcMap.set(Number(key), value);
              filteredLibraChangeEventList.push(event);
            } else if (
              MeegoTeamIdsForCapCutPC.includes(mainTeamId) &&
              (libraInfo.libraAppId === 360 || libraInfo.libraAppId === 368)
            ) {
              // PC 的话，剪映 & CapCut 放到一起
              pcMap.set(Number(key), value);
              filteredLibraChangeEventList.push(event);
            } else if (
              MeegoTeamIdsForCapCutWeb.includes(mainTeamId) &&
              (libraInfo.libraAppId === 381 || libraInfo.libraAppId === 360 || libraInfo.libraAppId === 305)
            ) {
              // CapCutWeb 包括 CapCut Web(381)、CapCut PC(360)、CapCut App(305) 的实验
              ccWebMap.set(Number(key), value);
              filteredLibraChangeEventList.push(event);
            } else if (
              MeegoTeamIdsForJianyingWeb.includes(mainTeamId) &&
              (libraInfo.libraAppId === 1071 || libraInfo.libraAppId === 399 || libraInfo.libraAppId === 147)
            ) {
              // 剪映 Web 包括 剪映 Web(1071)、剪映 PC(399)、剪映 App(147) 的实验
              lvWebMap.set(Number(key), value);
              filteredLibraChangeEventList.push(event);
            } else if (MeegoTeamIdsForJianyingServer.includes(mainTeamId)) {
              if (mainTeamId === 39000) {
                // 剪C-视频工具-Server（剪映和 CapCut 都包含）
                if (libraInfo.libraAppId === 147 || libraInfo.libraAppId === 305 || libraInfo.libraAppId === 295) {
                  serverMap.set(Number(key), value);
                  filteredLibraChangeEventList.push(event);
                }
              } else if (libraInfo.libraAppId === 147) {
                // 其余情况，均是返回剪映实验（过滤 CapCut）
                serverMap.set(Number(key), value);
                filteredLibraChangeEventList.push(event);
              }
            } else if (
              MeegoTeamIdsForCommerce.includes(mainTeamId) &&
              (libraInfo.libraAppId === 147 ||
                libraInfo.libraAppId === 305 ||
                libraInfo.libraAppId === 255 ||
                libraInfo.libraAppId === 399 ||
                libraInfo.libraAppId === 360)
            ) {
              commerceMap.set(Number(key), value);
              filteredLibraChangeEventList.push(event);
            } else if (
              MeegoTeamIdsForMultimedia.includes(mainTeamId) &&
              (libraInfo.libraAppId === 147 ||
                libraInfo.libraAppId === 305 ||
                libraInfo.libraAppId === 399 ||
                libraInfo.libraAppId === 360)
            ) {
              multiMediaMap.set(Number(key), value);
              filteredLibraChangeEventList.push(event);
            } else if (
              MeegoTeamIdsForRetouch.includes(mainTeamId) &&
              (libraInfo.libraAppId === 255 || libraInfo.libraAppId === 367 || libraInfo.libraAppId === 371)
            ) {
              retouchMap.set(Number(key), value);
              filteredLibraChangeEventList.push(event);
            }
          }
        } else {
          // 没有主责团队，则按照 teamIds 进行划分（一个实验可能隶属于多个 team Id）
          if (teamIds.some(id => MeegoTeamIdsForJianyingMobile.includes(id)) && libraInfo.libraAppId === 147) {
            lvMobileMap.set(Number(key), value);
            filteredLibraChangeEventList.push(event);
          }
          if (teamIds.some(id => MeegoTeamIdsForCapCutMobile.includes(id)) && libraInfo.libraAppId === 305) {
            ccMobileMap.set(Number(key), value);
            filteredLibraChangeEventList.push(event);
          }
          if (teamIds.some(id => MeegoTeamIdsForJianyingPC.includes(id)) && libraInfo.libraAppId === 399) {
            pcMap.set(Number(key), value);
            filteredLibraChangeEventList.push(event);
          }
          if (
            teamIds.some(id => MeegoTeamIdsForCapCutPC.includes(id)) &&
            (libraInfo.libraAppId === 360 || libraInfo.libraAppId === 368)
          ) {
            // PC 的话，剪映 & CapCut 放到一起
            pcMap.set(Number(key), value);
            filteredLibraChangeEventList.push(event);
          }
          if (
            teamIds.some(id => MeegoTeamIdsForCapCutWeb.includes(id)) &&
            (libraInfo.libraAppId === 381 || libraInfo.libraAppId === 360 || libraInfo.libraAppId === 305)
          ) {
            // CapCutWeb 包括 CapCut Web(381)、CapCut PC(360)、CapCut App(305) 的实验
            ccWebMap.set(Number(key), value);
            filteredLibraChangeEventList.push(event);
          }
          if (
            teamIds.some(id => MeegoTeamIdsForJianyingWeb.includes(id)) &&
            (libraInfo.libraAppId === 1071 || libraInfo.libraAppId === 399 || libraInfo.libraAppId === 147)
          ) {
            // 剪映 Web 包括 剪映 Web(1071)、剪映 PC(399)、剪映 App(147) 的实验
            lvWebMap.set(Number(key), value);
            filteredLibraChangeEventList.push(event);
          }
          if (teamIds.some(id => MeegoTeamIdsForJianyingServer.includes(id))) {
            if (teamIds.some(id => [39000].includes(id))) {
              // 剪C-视频工具-Server（剪映和 CapCut 都包含）
              if (libraInfo.libraAppId === 147 || libraInfo.libraAppId === 305 || libraInfo.libraAppId === 295) {
                serverMap.set(Number(key), value);
                filteredLibraChangeEventList.push(event);
              }
            } else if (libraInfo.libraAppId === 147) {
              // 其余情况，均是返回剪映实验（过滤 CapCut）
              serverMap.set(Number(key), value);
              filteredLibraChangeEventList.push(event);
            }
          }
          if (
            teamIds.some(id => MeegoTeamIdsForCommerce.includes(id)) &&
            (libraInfo.libraAppId === 147 ||
              libraInfo.libraAppId === 305 ||
              libraInfo.libraAppId === 255 ||
              libraInfo.libraAppId === 399 ||
              libraInfo.libraAppId === 360)
          ) {
            commerceMap.set(Number(key), value);
            filteredLibraChangeEventList.push(event);
          }
          if (
            teamIds.some(id => MeegoTeamIdsForMultimedia.includes(id)) &&
            (libraInfo.libraAppId === 147 ||
              libraInfo.libraAppId === 305 ||
              libraInfo.libraAppId === 399 ||
              libraInfo.libraAppId === 360)
          ) {
            multiMediaMap.set(Number(key), value);
            filteredLibraChangeEventList.push(event);
          }
          if (
            teamIds.some(id => MeegoTeamIdsForRetouch.includes(id)) &&
            (libraInfo.libraAppId === 255 || libraInfo.libraAppId === 367 || libraInfo.libraAppId === 371)
          ) {
            retouchMap.set(Number(key), value);
            filteredLibraChangeEventList.push(event);
          }
        }
      }
    }

    // Msg Data Array
    const msgDataArray: {
      dataMap: Map<number, { event: LibraChangeEvent; libraInfo: LibraNewInfo }>;
      teamType: MeegoTeamType;
      chatIds: string[];
    }[] = [];
    msgDataArray.push({
      dataMap: lvMobileMap,
      teamType: MeegoTeamType.LV_Mobile,
      chatIds: LarkChatIdsByMeegoTeamType(MeegoTeamType.LV_Mobile),
    });
    msgDataArray.push({
      dataMap: ccMobileMap,
      teamType: MeegoTeamType.CC_Mobile,
      chatIds: LarkChatIdsByMeegoTeamType(MeegoTeamType.CC_Mobile),
    });
    msgDataArray.push({
      dataMap: pcMap,
      teamType: MeegoTeamType.LV_PC,
      chatIds: LarkChatIdsByMeegoTeamType(MeegoTeamType.LV_PC),
    });
    msgDataArray.push({
      dataMap: ccWebMap,
      teamType: MeegoTeamType.CC_Web,
      chatIds: LarkChatIdsByMeegoTeamType(MeegoTeamType.CC_Web),
    });
    msgDataArray.push({
      dataMap: lvWebMap,
      teamType: MeegoTeamType.LV_Web,
      chatIds: LarkChatIdsByMeegoTeamType(MeegoTeamType.LV_Web),
    });
    msgDataArray.push({
      dataMap: serverMap,
      teamType: MeegoTeamType.LV_Server,
      chatIds: LarkChatIdsByMeegoTeamType(MeegoTeamType.LV_Server),
    });
    msgDataArray.push({
      dataMap: commerceMap,
      teamType: MeegoTeamType.Commerce,
      chatIds: LarkChatIdsByMeegoTeamType(MeegoTeamType.Commerce),
    });
    msgDataArray.push({
      dataMap: multiMediaMap,
      teamType: MeegoTeamType.Multimedia,
      chatIds: LarkChatIdsByMeegoTeamType(MeegoTeamType.Multimedia),
    });
    msgDataArray.push({
      dataMap: retouchMap,
      teamType: MeegoTeamType.Retouch,
      chatIds: LarkChatIdsByMeegoTeamType(MeegoTeamType.Retouch),
    });

    // 发送飞书卡片通知
    for (const msgData of msgDataArray) {
      const { dataMap, teamType, chatIds } = msgData;
      let shouldSend = chatIds.length > 0;
      if (!sendWhenDataEmpty) {
        shouldSend = shouldSend && dataMap.size > 0;
      }
      if (shouldSend) {
        const meegoTeamIds = MeegoTeamIdsByMeegoTeamType(teamType);
        const meegoTeamInfos = await this.getMeegoTeamList(meegoTeamIds, MeegoProjectKeyFaceU);
        let msg: MsgTemplate = {
          name: '',
          type: MsgType.DirectChat,
          category: MsgCategory.Experiment,
          subCategory: LibraMsgCategory.Patrol,
          strategy: MsgStrategy.Manual,
          msgContent: '',
        }; // 默认消息
        if (cardType === LibraTopicGroupsCardType.ChangeNotifyOfYesterday) {
          // 昨日实验变更列表
          msg = new LibraChangeListSummaryOfYesterday(cardType, dataMap, dateRange, teamType, meegoTeamInfos);
        } else if (
          cardType === LibraTopicGroupsCardType.CloseAttributionNotFinishNotifyOfYesterday ||
          cardType === LibraTopicGroupsCardType.CloseAttributionNotFinishNotifyOfThisWeek ||
          cardType === LibraTopicGroupsCardType.CloseAttributionNotFinishNotifyOfLastWeek ||
          cardType === LibraTopicGroupsCardType.CloseAttributionNotFinishNotifyOfCustomDaysDuration
        ) {
          // 未完成归因实验列表
          msg = new LibraNotFinishCloseAttributionSummary(
            cardType,
            dataMap,
            dateRange,
            teamType,
            meegoTeamInfos,
            customTitle,
            fixedTeamIds,
          );
        } else if (
          cardType === LibraTopicGroupsCardType.CloseAttributionDidFinishNotifyOfYesterday ||
          cardType === LibraTopicGroupsCardType.CloseAttributionDidFinishNotifyOfThisWeek ||
          cardType === LibraTopicGroupsCardType.CloseAttributionDidFinishNotifyOfLastWeek
        ) {
          // 已完成归因实验列表
          msg = new LibraFinishCloseAttributionSummary(cardType, dataMap, dateRange, teamType, meegoTeamInfos);
        }
        if (msg.name.length > 0) {
          let userIdType = UserIdType.chatId;
          let sendId = chatIds[0];
          if (sendPrivate) {
            userIdType = UserIdType.openId;
            sendId = 'ou_4595be854888d986de5e9c8914183828'; // @FIXME 先临时转发给个人 <EMAIL>
          } else if (customChatId) {
            // 有自定义的 chatId，则使用自定义的 chatId
            sendId = customChatId;
          }
          // 如果要邀请操作者进群，则在此触发操作
          if (inviteLibraOperatorsJoinGroup && userIdType === UserIdType.chatId) {
            // 分析 dataMap，根据 event 和 libraInfo 找到 user 的 openId
            const openIds: string[] = [];
            for (const [key, value] of dataMap) {
              if (value.event.eventOperator !== 'Libra' && value.event.eventOperator !== 'OpenAPI') {
                const operatorOpenId = value.libraInfo.flightInfo.owners.filter(
                  item => item.email === add_suffix_ne('@bytedance.com')(value.event.eventOperator),
                );
                if (operatorOpenId && operatorOpenId.length > 0 && operatorOpenId[0].open_id) {
                  openIds.push(operatorOpenId[0].open_id);
                }
              }
            }
            await this.inviteMembersToChatGroup(sendId, openIds);
          }
          const ret = await this.messageService.sendNormalMsg(msg, userIdType, sendId);
          if (ret.code !== NetworkCode.Success) {
            this.logger.warn(
              `[sendLibraMsg] send msg failed, msg: ${JSON.stringify(msg)}, ret: ${JSON.stringify(ret)}`,
            );
          }
        }
      }
    }

    return filteredLibraChangeEventList;
  }

  // 获取昨日变更的所有实验，并完成话题群播报
  async libraChangeNotifyOfYesterday(sendPrivate?: boolean, customQuery?: any) {
    const { startOfYesterdayTimestamp, endOfYesterdayTimestamp, formattedYesterdayStr } =
      YesterdayInfoForLibraTopicGroups();

    const query = customQuery ?? {
      ts: {
        $gte: startOfYesterdayTimestamp,
        $lte: endOfYesterdayTimestamp,
      },
      'libraNewInfo.meegoTeamInfo.teamId': {
        $in: MeegoTeamIds,
      },
    };
    return await this.didSendNotifyCardToTopicGroups({
      cardType: LibraTopicGroupsCardType.ChangeNotifyOfYesterday,
      query,
      dateRange: formattedYesterdayStr,
      sendWhenDataEmpty: true,
      sendPrivate,
    });
  }

  // 昨日填写了实验关闭归因的实验列表
  async libraCloseAttributionFinishedNotifyOfYesterday(sendPrivate?: boolean, customQuery?: any) {
    const { startOfYesterdayTimestamp, endOfYesterdayTimestamp, formattedYesterdayStr } =
      YesterdayInfoForLibraTopicGroups();

    const query = customQuery ?? {
      'flightInfo.status': {
        $in: [LibraFlightStatus.Ended, LibraFlightStatus.Released],
      },
      meegoTeamInfo: {
        $ne: [],
      },
      'meegoTeamInfo.teamId': {
        $in: MeegoTeamIds,
      },
      'flightInfo.closeAttributionInfo': {
        $exists: true,
      },
      'flightInfo.closeAttributionInfo.updateTime': {
        $gte: startOfYesterdayTimestamp,
        $lte: endOfYesterdayTimestamp,
      },
    };

    // 查询实验列表
    const libraNewInfoList = await this.libraNewInfoListDao.listAll(query);
    const flightIds = libraNewInfoList.map(info => info.flightInfo.id);
    const eventListQuery = {
      flightId: {
        $in: flightIds,
      },
    };

    return await this.didSendNotifyCardToTopicGroups({
      cardType: LibraTopicGroupsCardType.CloseAttributionDidFinishNotifyOfYesterday,
      query: eventListQuery,
      dateRange: formattedYesterdayStr,
      sendWhenDataEmpty: false,
      sendPrivate,
    });
  }

  // 本周未填写实验关闭归因的实验列表
  async libraCloseAttributionNotFinishedNotifyOfThisWeek(sendPrivate?: boolean, customQuery?: any) {
    const { startOfThisWeekTimestamp, endOfThisWeekTimestamp, thisWeekDayDuration } = ThisWeekInfoForLibraTopicGroups();

    // 本周已经关闭的、在 TeamIds 里面、实验关闭归因为空的实验列表
    const query = customQuery ?? {
      $and: [
        {
          eventType: {
            $in: [LibraEventType.FlightStop],
          },
        },
        {
          ts: {
            $gte: startOfThisWeekTimestamp,
            $lte: endOfThisWeekTimestamp,
          },
        },
        {
          'libraNewInfo.meegoTeamInfo.teamId': {
            $in: MeegoTeamIds,
          },
        },
        {
          $or: [
            {
              'libraNewInfo.flightInfo.closeAttributionInfo': {
                $exists: false,
              },
            },
            {
              'libraNewInfo.flightInfo.closeAttributionInfo': null,
            },
            {
              'libraNewInfo.flightInfo.closeAttributionInfo': {
                $type: 'array',
                $eq: [],
              },
            },
          ],
        },
      ],
    };

    return await this.didSendNotifyCardToTopicGroups({
      cardType: LibraTopicGroupsCardType.CloseAttributionNotFinishNotifyOfThisWeek,
      query,
      dateRange: thisWeekDayDuration,
      sendWhenDataEmpty: false,
      sendPrivate,
    });
  }

  // 上周未填写实验关闭归因的实验列表
  async libraCloseAttributionNotFinishedNotifyOfLastWeek(sendPrivate?: boolean, customQuery?: any) {
    const { startOfLastWeekTimestamp, endOfLastWeekTimestamp, lastWeekDayDuration } = LastWeekInfoForLibraTopicGroups();

    // 上周已经关闭的、在 TeamIds 里面、实验关闭归因为空的实验列表
    const query = customQuery ?? {
      $and: [
        {
          eventType: {
            $in: [LibraEventType.FlightStop],
          },
        },
        {
          ts: {
            $gte: startOfLastWeekTimestamp,
            $lte: endOfLastWeekTimestamp,
          },
        },
        {
          'libraNewInfo.meegoTeamInfo.teamId': {
            $in: MeegoTeamIds,
          },
        },
        {
          $or: [
            {
              'libraNewInfo.flightInfo.closeAttributionInfo': {
                $exists: false,
              },
            },
            {
              'libraNewInfo.flightInfo.closeAttributionInfo': null,
            },
            {
              'libraNewInfo.flightInfo.closeAttributionInfo': {
                $type: 'array',
                $eq: [],
              },
            },
          ],
        },
      ],
    };

    return await this.didSendNotifyCardToTopicGroups({
      cardType: LibraTopicGroupsCardType.CloseAttributionNotFinishNotifyOfLastWeek,
      query,
      dateRange: lastWeekDayDuration,
      sendWhenDataEmpty: false,
      sendPrivate,
    });
  }

  // 尚未填写实验关闭归因的实验列表通知，并且自定义时间段、自定义标题、自定义 Meego 虚拟团队、自定义 chatId
  async libraCloseAttributionNotFinishedNotifyOfCustomDaysDuration(
    startTimeStr: string,
    endTimeStr: string,
    customTitle: string,
    meegoTeamIds: number[],
    chatId: string,
    sendPrivate?: boolean,
    customQuery?: any,
    inviteLibraOperatorsJoinGroup?: boolean,
  ) {
    // 注册插件
    dayjs.extend(utc);
    dayjs.extend(timezone);

    const startDate = dayjs.tz(startTimeStr, 'Asia/Shanghai');
    if (!startDate.isValid()) {
      return;
    }
    const startTimestamp = startDate.unix();

    const endDate = dayjs.tz(endTimeStr, 'Asia/Shanghai');
    if (!endDate.isValid()) {
      return;
    }
    const endTimestamp = endDate.unix();

    if (startTimestamp > endTimestamp) {
      // 时间非法
      return;
    }

    const query = customQuery ?? {
      $and: [
        {
          eventType: {
            $in: [LibraEventType.FlightStop],
          },
        },
        {
          ts: {
            $gte: startTimestamp,
            $lte: endTimestamp,
          },
        },
        {
          'libraNewInfo.meegoTeamInfo.teamId': {
            $in: meegoTeamIds,
          },
        },
        {
          $or: [
            {
              'libraNewInfo.flightInfo.closeAttributionInfo': {
                $exists: false,
              },
            },
            {
              'libraNewInfo.flightInfo.closeAttributionInfo': null,
            },
            {
              'libraNewInfo.flightInfo.closeAttributionInfo': {
                $type: 'array',
                $eq: [],
              },
            },
          ],
        },
      ],
    };

    const formatStartDay = dayjs(startTimestamp * 1000)
      .utc() // 将时间标记为 UTC
      .tz('Asia/Shanghai') // 转换为东八区
      .format('YYYY-MM-DD');
    const formatEndDay = dayjs(endTimestamp * 1000)
      .utc() // 将时间标记为 UTC
      .tz('Asia/Shanghai') // 转换为东八区
      .format('YYYY-MM-DD');

    return await this.didSendNotifyCardToTopicGroups({
      cardType: LibraTopicGroupsCardType.CloseAttributionNotFinishNotifyOfCustomDaysDuration,
      query,
      dateRange: `${formatStartDay} ~ ${formatEndDay}`,
      fixedTeamIds: meegoTeamIds,
      sendWhenDataEmpty: false,
      sendPrivate,
      customTitle,
      customChatId: chatId,
      inviteLibraOperatorsJoinGroup,
      dontTryToFindMainTeamId: false,
    });
  }

  async createCloseAttributionNotFinishedLarkGroup(startTimeStr: string, endTimeStr: string) {
    dayjs.extend(utc);
    dayjs.extend(timezone);
    const startDay = dayjs(startTimeStr)
      .utc() // 将时间标记为 UTC
      .tz('Asia/Shanghai') // 转换为东八区
      .format('YYYY-MM-DD');
    const endDay = dayjs(endTimeStr)
      .utc() // 将时间标记为 UTC
      .tz('Asia/Shanghai') // 转换为东八区
      .format('YYYY-MM-DD');

    // 创建飞书群
    const emails = [
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
    ];
    const groupUserIds = await this.lark.batchGetUserId(UserIdType.userId, { emails });
    const result = await this.lark.createLarkGroup(
      {
        user_id_type: UserIdType.userId,
      },
      {
        name: `未填写实验关闭归因的实验列表（${startDay} ~ ${endDay}）`,
        description: `用于定期汇总未填写实验关闭归因的实验列表`,
        owner_id: groupUserIds[0].user_id,
        user_id_list: groupUserIds
          .filter((value: UserData) => value.user_id)
          .map((value: UserData) => value.user_id)
          .filter(value => value),
        bot_id_list: ['cli_9c8628b7b1f1d102'],
      },
    );
    return result.chat_id ?? '';
  }

  async libraCloseAttributionNotFinishedBatchNotifyByMeegoTeam(
    startTimeStr: string,
    endTimeStr: string,
    shouldCreateLarkGroup?: boolean,
    sendPrivate?: boolean,
    customQuery?: any,
    inviteLibraOperatorsJoinGroup?: boolean,
  ) {
    // 各个方向进行未填写推送
    let pcChatId = '';
    const meegoTeamList = await this.getMeegoTeamList(MeegoTeamIds, MeegoProjectKeyFaceU);
    for (const meegoTeam of meegoTeamList) {
      const meegoTeamId = meegoTeam.teamId;
      const meegoTeamName = meegoTeam.teamName;
      if (MeegoTeamIdsForJianyingPC.includes(meegoTeamId) || MeegoTeamIdsForCapCutPC.includes(meegoTeamId)) {
        // PC 的先不处理，单独合并处理
        pcChatId = meegoTeam.larkChatId ?? '';
        continue;
      }

      let chatId = meegoTeam.larkChatId ?? '';
      if (shouldCreateLarkGroup) {
        // 需要独立创建群
        chatId = await this.createCloseAttributionNotFinishedLarkGroup(startTimeStr, endTimeStr);
      }

      if (chatId.length > 0) {
        await this.libraCloseAttributionNotFinishedNotifyOfCustomDaysDuration(
          startTimeStr,
          endTimeStr,
          '未填写关闭归因的实验列表',
          [meegoTeamId],
          chatId,
          sendPrivate,
          customQuery,
          inviteLibraOperatorsJoinGroup,
        );
      }
    }

    // PC 的单独处理
    if (shouldCreateLarkGroup) {
      // 需要独立创建群
      pcChatId = await this.createCloseAttributionNotFinishedLarkGroup(startTimeStr, endTimeStr);
    }
    if (pcChatId.length > 0) {
      await this.libraCloseAttributionNotFinishedNotifyOfCustomDaysDuration(
        startTimeStr,
        endTimeStr,
        `未填写关闭归因的实验列表`,
        [...MeegoTeamIdsForJianyingPC, ...MeegoTeamIdsForCapCutPC],
        pcChatId,
        sendPrivate,
        customQuery,
        inviteLibraOperatorsJoinGroup,
      );
    }
  }

  async libraCloseAttributionNotFinishedBatchNotifyOfCustomDaysDuration(
    startTimeStr: string,
    endTimeStr: string,
    chatId?: string,
    sendPrivate?: boolean,
    customQuery?: any,
    inviteLibraOperatorsJoinGroup?: boolean,
  ) {
    // 如果指定了 chatId，就是往指定群推送；若没有则创建飞书群
    let innerChatId = chatId ?? '';

    if (innerChatId.length === 0) {
      // 创建飞书群
      innerChatId = await this.createCloseAttributionNotFinishedLarkGroup(startTimeStr, endTimeStr);
    }

    // 各个方向进行未填写推送
    const meegoTeamList = await this.getMeegoTeamList(MeegoTeamIds, MeegoProjectKeyFaceU);
    for (const meegoTeam of meegoTeamList) {
      const meegoTeamId = meegoTeam.teamId;
      const meegoTeamName = meegoTeam.teamName;
      if (MeegoTeamIdsForJianyingPC.includes(meegoTeamId) || MeegoTeamIdsForCapCutPC.includes(meegoTeamId)) {
        // PC 的先不处理，单独合并处理
        continue;
      }
      await this.libraCloseAttributionNotFinishedNotifyOfCustomDaysDuration(
        startTimeStr,
        endTimeStr,
        `【${meegoTeamName}】未填写列表`,
        [meegoTeamId],
        innerChatId,
        sendPrivate,
        customQuery,
        inviteLibraOperatorsJoinGroup,
      );
    }
    // PC 的单独处理
    await this.libraCloseAttributionNotFinishedNotifyOfCustomDaysDuration(
      startTimeStr,
      endTimeStr,
      `【PC】未填写列表`,
      [...MeegoTeamIdsForJianyingPC, ...MeegoTeamIdsForCapCutPC],
      innerChatId,
      sendPrivate,
      customQuery,
      inviteLibraOperatorsJoinGroup,
    );
  }

  async convertLibraInfoListForAeolusDashboard(libraInfoList: LibraNewInfo[] | null): Promise<LibraNewInfoForAeolus[]> {
    const ret: LibraNewInfoForAeolus[] = [];
    if (!libraInfoList) {
      return ret;
    }
    dayjs.extend(utc);
    dayjs.extend(timezone);

    // 先查询一遍已经关闭的实验
    const closedFlightIdsSet = new Set<number>();
    for (const info of libraInfoList) {
      if (info.flightInfo.status === LibraFlightStatus.Ended || info.flightInfo.status === LibraFlightStatus.Released) {
        closedFlightIdsSet.add(info.flightInfo.id);
      }
    }
    const closedFlightIds = Array.from(closedFlightIdsSet);
    const closedFlightsReasonMap = new Map<number, string>();
    const closedFlightsStopTimeMap = new Map<number, number>(); // 已关闭实验的关闭时间
    if (closedFlightIds && closedFlightIds.length > 0) {
      const libraChangeEventList = await this.libraChangeEventDao.listAll({ flightId: { $in: closedFlightIds } });
      if (libraChangeEventList !== null && libraChangeEventList !== undefined && libraChangeEventList.length > 0) {
        for (const closedFlightId of closedFlightIds) {
          const libraChangeEvent = libraChangeEventList.find(event => event.flightId === closedFlightId);
          if (
            libraChangeEvent !== null &&
            libraChangeEvent !== undefined &&
            libraChangeEvent.eventType === LibraEventType.FlightStop
          ) {
            closedFlightsReasonMap.set(closedFlightId, getRealStopReason(libraChangeEvent.operationReason));
            closedFlightsStopTimeMap.set(closedFlightId, libraChangeEvent.ts);
          }
        }
      }
    }

    for (const libraInfo of libraInfoList) {
      const app_id = libraInfo.libraAppId;
      const app = getAppNameByLibraAppId(app_id);
      const token = '';
      const flight_id = libraInfo.flightInfo.id;
      const flight_type = FlightTypeName[libraInfo.flightInfo.libraType as FlightType] ?? '';
      const display_name = libraInfo.flightInfo.name;
      const flight_end_date = dayjs(libraInfo.flightInfo.endTime * 1000)
        .utc() // 将时间标记为 UTC
        .tz('Asia/Shanghai') // 转换为东八区
        .format('YYYY-MM-DD HH:mm:ss');
      const flight_start_date = dayjs(libraInfo.flightInfo.startTime * 1000)
        .utc() // 将时间标记为 UTC
        .tz('Asia/Shanghai') // 转换为东八区
        .format('YYYY-MM-DD HH:mm:ss');
      const flight_owner = libraInfo.flightInfo.owners.map(owner => owner.email ?? '').join(',');
      const latest_flight_status = libraInfo.flightInfo.status;
      let business_line = '';
      const businessLineSet = new Set<string>();
      const meegoIdSet = new Set<number>();
      const meegoNameSet = new Set<string>();
      const meegoTypeSet = new Set<string>();
      const meegoTeamNameSet = new Set<string>();
      const meegoUrlSet = new Set<string>();
      const meegoPmSet = new Set<string>();
      const releaseVersionSet = new Set<string>();
      if (libraInfo.meegoInfo && libraInfo.meegoInfo.length > 0) {
        for (const meego of libraInfo.meegoInfo) {
          let businessLine = meego.businessLine.join('/'); // 一级业务线和二级业务线使用'/'拼接
          if (businessLine.endsWith('/')) {
            businessLine = businessLine.substring(0, businessLine.length - 1);
          }
          const meegoPm = meego.pmOwners?.map(item => item.name).join(',') ?? '';
          const meegoReleaseVersion = meego.releaseVersion.join(',') ?? '';
          businessLineSet.add(businessLine);
          meegoIdSet.add(meego.id);
          meegoNameSet.add(meego.name);
          meegoTypeSet.add(meego.type ?? '');
          meegoUrlSet.add(meego.url);
          meegoPmSet.add(meegoPm);
          releaseVersionSet.add(meegoReleaseVersion);
        }
      }
      let meego_main_team_name = '';
      if (libraInfo.meegoTeamInfo && libraInfo.meegoTeamInfo.length > 0) {
        for (const teamInfo of libraInfo.meegoTeamInfo) {
          meegoTeamNameSet.add(teamInfo.teamName);
          if (teamInfo.isMainTeam) {
            // 主责团队
            meego_main_team_name = teamInfo.teamName;
          }
        }
      }

      if (businessLineSet.size > 0) {
        // 业务线
        const businessLineList = Array.from(businessLineSet);
        business_line = businessLineList.join(',');
      }

      // 实验层
      const flight_layer = libraInfo.flightInfo.trafficInfo.trafficLayer;
      // 实验流量
      const flight_version_resource = libraInfo.flightInfo.trafficInfo.currentTrafficValue;
      // 实验分流策略
      let flight_hash_strategy = '';
      if (libraInfo.flightInfo.layerType === LibraLayerType.uid) {
        flight_hash_strategy = 'uid';
      } else if (libraInfo.flightInfo.layerType === LibraLayerType.did) {
        flight_hash_strategy = 'did';
      } else if (libraInfo.flightInfo.layerType === LibraLayerType.rid) {
        flight_hash_strategy = 'rid';
      } else if (libraInfo.flightInfo.layerType === LibraLayerType.union) {
        flight_hash_strategy = 'union';
      } else if (libraInfo.flightInfo.layerType === LibraLayerType.uuid) {
        flight_hash_strategy = 'uuid';
      } else if (libraInfo.flightInfo.layerType === LibraLayerType.cdid) {
        flight_hash_strategy = 'cdid';
      } else if (libraInfo.flightInfo.layerType === LibraLayerType.ssid) {
        flight_hash_strategy = 'ssid';
      } else if (libraInfo.flightInfo.layerType === LibraLayerType.webid) {
        flight_hash_strategy = 'webid';
      } else if (libraInfo.flightInfo.layerType === LibraLayerType.pkid) {
        flight_hash_strategy = 'pkid';
      } else if (libraInfo.flightInfo.layerType === LibraLayerType.pureuid) {
        flight_hash_strategy = 'pureuid';
      }
      // 实验tags
      const flight_tags = libraInfo.flightInfo.tags.join(',');
      // 查询日期
      const search_date = '';
      // 区域（cn、sg、va）
      let region = '';
      if (libraInfo.flightInfo.region === LibraRegion.CN) {
        region = 'cn';
      } else if (libraInfo.flightInfo.region === LibraRegion.SG) {
        region = 'sg';
      } else if (libraInfo.flightInfo.region === LibraRegion.VA) {
        region = 'va';
      }
      // 实验链接
      const flight_url = libraDetailUrl(libraInfo.flightInfo.region, libraInfo.flightInfo.id);

      let meego_name = '';
      let meego_id = '';
      let meego_type = '';
      let meego_url = '';
      let meego_pm = '';
      let release_version = '';
      if (meegoIdSet.size > 0) {
        // Meego 需求信息
        const meegoNameList = Array.from(meegoNameSet);
        const meegoIdList = Array.from(meegoIdSet);
        const meegoTypeList = Array.from(meegoTypeSet);
        const meegoUrlList = Array.from(meegoUrlSet);
        const meegoPmList = Array.from(meegoPmSet);
        const releaseVersionList = Array.from(releaseVersionSet);
        meego_name = meegoNameList.join(',');
        meego_id = meegoIdList.join(',');
        meego_type = meegoTypeList.join(',');
        meego_url = meegoUrlList.join(',');
        meego_pm = meegoPmList.join(',');
        release_version = releaseVersionList.join(',');
      }

      let meego_team_name = '';
      if (meegoTeamNameSet.size > 0) {
        // Meego 团队信息
        const meegoTeamNameList = Array.from(meegoTeamNameSet);
        meego_team_name = meegoTeamNameList.join(',');
      }

      const flight_stop_reason = closedFlightsReasonMap.get(libraInfo.flightInfo.id) ?? '';
      const flight_close_timestamp = closedFlightsStopTimeMap.get(libraInfo.flightInfo.id) ?? 0;

      // 实验关闭归因分析
      // 实验关闭归因和 flight_id 的映射表
      const flightIdToFlightCloseAttributionMap = new Map<
        number,
        {
          mainTypeName: string;
          subTypeName: string;
          subTypeDetailName: string;
          customReason: string;
          canPreInterceptType: string; // 可前置拦截问题类型（用于实验重开-代码相关的关闭原因）
        }
      >();
      if (libraInfo.flightInfo.closeAttributionInfo) {
        const { closeAttributionInfo } = libraInfo.flightInfo;
        // 主要分类
        const mainTypeName = libraFlightCloseAttributionMainType2DisplayNameMap[closeAttributionInfo.mainType];
        // 细分分类
        let subTypeName = '';
        // 详细原因
        let subTypeDetailName = '';
        // 自定义原因填写补充
        let customReason = '';
        // 可前置拦截问题类型（用于实验重开-代码相关的关闭原因）
        let canPreInterceptType = '';
        if (closeAttributionInfo.reopenSubType !== undefined) {
          // 细分分类(实验重开)
          subTypeName = libraFlightCloseAttributionReopenSubType2DisplayNameMap[closeAttributionInfo.reopenSubType];
          if (closeAttributionInfo.reopenSubTypeDetailType !== undefined) {
            // 当 LibraFlightCloseReopenType 时，对应的子分类(详细原因)
            subTypeDetailName =
              libraFlightCloseAttributionReopenSubTypeDetailType2DisplayNameMap[
                closeAttributionInfo.reopenSubTypeDetailType
              ];
          }
        } else if (closeAttributionInfo.fullReleaseSubType !== undefined) {
          // 细分分类(实验全量)
          subTypeName =
            libraFlightCloseAttributionFullReleaseSubType2DisplayNameMap[closeAttributionInfo.fullReleaseSubType];
        } else if (closeAttributionInfo.offlineSubType !== undefined) {
          // 细分分类(实验下线)
          subTypeName = libraFlightCloseAttributionOfflineSubType2DisplayNameMap[closeAttributionInfo.offlineSubType];
        }

        if (closeAttributionInfo.customReason !== undefined) {
          // 自定义原因填写
          customReason = closeAttributionInfo.customReason;
        }

        if (closeAttributionInfo.reopenSubTypeCanPreInterceptType !== undefined) {
          // 可前置拦截问题类型（用于实验重开-代码相关的关闭原因）
          canPreInterceptType =
            libraFlightCloseAttributionReopenSubTypeCanPreInterceptType2DisplayNameMap[
              closeAttributionInfo.reopenSubTypeCanPreInterceptType
            ];
          if (
            canPreInterceptType &&
            (canPreInterceptType.startsWith('是，') || canPreInterceptType.startsWith('否，'))
          ) {
            // 把“是”和“否”截取掉
            canPreInterceptType = `${canPreInterceptType.slice(2)}`;
          }
        }

        const flightCloseAttributionInfo: {
          mainTypeName: string;
          subTypeName: string;
          subTypeDetailName: string;
          customReason: string;
          canPreInterceptType: string;
        } = {
          mainTypeName,
          subTypeName,
          subTypeDetailName,
          customReason,
          canPreInterceptType,
        };
        flightIdToFlightCloseAttributionMap.set(libraInfo.flightInfo.id, flightCloseAttributionInfo);
      }
      const flightCloseAttributionInfo = flightIdToFlightCloseAttributionMap.get(libraInfo.flightInfo.id);
      ret.push({
        app_id,
        app,
        token,
        flight_id,
        flight_type,
        display_name,
        flight_end_date,
        flight_start_date,
        flight_owner,
        latest_flight_status,
        business_line,
        flight_layer,
        flight_version_resource,
        flight_hash_strategy,
        flight_tags,
        search_date,
        region,
        flight_url,
        meego_name,
        meego_id,
        meego_type,
        meego_url,
        meego_pm,
        release_version,
        flight_stop_reason,
        meego_team_name,
        meego_main_team_name,
        flight_close_main_type_name: flightCloseAttributionInfo?.mainTypeName ?? '',
        flight_close_sub_type_name: flightCloseAttributionInfo?.subTypeName ?? '',
        flight_close_sub_type_detail_name: flightCloseAttributionInfo?.subTypeDetailName ?? '',
        flight_close_custom_reason: flightCloseAttributionInfo?.customReason ?? '',
        flight_close_reopen_can_pre_intercept_type: flightCloseAttributionInfo?.canPreInterceptType ?? '',
        flight_close_timestamp,
      } as LibraNewInfoForAeolus);
    }
    return ret;
  }

  compareArray(lh: any[], rh: any[]): boolean {
    if ((lh?.length ?? 0) !== (rh?.length ?? 0)) {
      return false;
    }
    for (let i = 0; i < lh?.length; i++) {
      if (lh[i] !== rh[i]) {
        return false;
      }
    }
    return true;
  }

  // 获取实验的全量周期（从“实验开启”到“实验全量”）
  // 补充：暂时只计算剪映 App 和 CapCut App 的实验，并且只计算 Settings SDK 实验（可以通过 Settings API 拿到固化状态）
  async getFlightFullPeriod(libraStartTimestamp: number, libraStopTimestamp: number) {
    const result: {
      flightId: number;
      flightName: string;
      flightUrl: string;
      flightStartTime: string;
      flightEndTime: string;
      flightStopTime: string;
      fullDuration: number;
      fullDurationInWorkDays: number;
      appName: string;
      paLibraUrl: string;
      eventType: string;
    }[] = [];

    dayjs.extend(utc);
    dayjs.extend(timezone);

    // const libraAppIds = [147, 305];
    // const libraStartTimestamp = 1735660800; // 开启实验在 2025-01-01 00:00:00 以后
    // const libraStopTimestamp = 1743436799; // 实验关闭在 2025-03-31 23:59:59 之前
    const eventListFlightFull = await this.libraChangeEventDao.listAllWithNoAggregate({
      eventType: LibraEventType.FlightFull,
      ts: {
        $gte: libraStartTimestamp,
        $lte: libraStopTimestamp,
      },
    });
    const flightFullIds = Array.from(new Set(eventListFlightFull.map(item => item.flightId))); // 去重 Flight Id
    const flightFullLibraInfos = await this.libraNewInfoListDao.listAll({
      'flightInfo.id': { $in: flightFullIds },
    });
    if (flightFullLibraInfos && flightFullLibraInfos.length > 0) {
      for (const flightId of flightFullIds) {
        const flightFullEvent = eventListFlightFull.find(event => event.flightId === flightId);
        const libraInfo = flightFullLibraInfos.find(info => info.flightInfo.id === flightId);
        if (!flightFullEvent || !libraInfo) {
          continue;
        }
        const dayDiff = calculateDayDiffResult(libraInfo.flightInfo.startTime, flightFullEvent.ts);
        result.push({
          flightId: libraInfo.flightInfo.id,
          flightName: libraInfo.flightInfo.name,
          flightUrl: libraDetailUrl(libraInfo.flightInfo.region, libraInfo.flightInfo.id),
          flightStartTime: dayjs(libraInfo.flightInfo.startTime * 1000)
            .utc() // 将时间标记为 UTC
            .tz('Asia/Shanghai') // 转换为东八区
            .format('YYYY-MM-DD HH:mm:ss'),
          flightEndTime: dayjs(libraInfo.flightInfo.endTime * 1000)
            .utc() // 将时间标记为 UTC
            .tz('Asia/Shanghai') // 转换为东八区
            .format('YYYY-MM-DD HH:mm:ss'),
          flightStopTime: dayjs(flightFullEvent.ts * 1000)
            .utc() // 将时间标记为 UTC
            .tz('Asia/Shanghai') // 转换为东八区
            .format('YYYY-MM-DD HH:mm:ss'),
          fullDuration: dayDiff.totalDays,
          fullDurationInWorkDays: dayDiff.workdays,
          appName: getAppNameByLibraAppId(libraInfo.libraAppId),
          paLibraUrl: libraUrlOfPaperAirplane(libraInfo.flightInfo.id, libraInfo.libraAppId),
          eventType: LibraEventType.FlightFull,
        });
      }
    }

    const eventListFlightStop = await this.libraChangeEventDao.listAllWithNoAggregate({
      eventType: LibraEventType.FlightStop,
      ts: {
        $gte: libraStartTimestamp,
        $lte: libraStopTimestamp,
      },
    });
    const flightStopIds = Array.from(new Set(eventListFlightStop.map(item => item.flightId))); // 去重 Flight Id
    const flightStopLibraInfos = await this.libraNewInfoListDao.listAll({
      'flightInfo.id': { $in: flightStopIds },
    });

    // 查询这些关闭实验的 settings key 是否固化
    if (!flightStopLibraInfos || flightStopLibraInfos.length === 0) {
      return result;
    }

    for (const libraInfo of flightStopLibraInfos) {
      const checkSettings = ([FlightType.SettingsClientSDK, FlightType.SettingsClientNormal] as string[]).includes(
        libraInfo.flightInfo?.libraType ?? '',
      );
      if (!checkSettings) {
        continue;
      }
      const flightDetail = await this.libraAPI.queryFlight(libraInfo.flightInfo.region, libraInfo.flightInfo.id);
      if (flightDetail?.settings_item_ids && flightDetail?.settings_item_ids.length > 0) {
        for (const itemId of flightDetail.settings_item_ids) {
          const abConfigs = await this.settingsService.querySettingsItemABConfigs(itemId);
          const curFlight = abConfigs?.find(config => config?.flight_id === libraInfo.flightInfo.id);
          if (curFlight?.publish_status === PublishStatus.Published) {
            // 通过 event_list 获取实验关闭时间
            const flightStopEvent = eventListFlightStop.find(event => event.flightId === libraInfo.flightInfo.id);
            if (!flightStopEvent) {
              continue;
            }
            // 已固化
            const dayDiff = calculateDayDiffResult(libraInfo.flightInfo.startTime, flightStopEvent.ts);
            result.push({
              flightId: libraInfo.flightInfo.id,
              flightName: libraInfo.flightInfo.name,
              flightUrl: libraDetailUrl(libraInfo.flightInfo.region, libraInfo.flightInfo.id),
              flightStartTime: dayjs(libraInfo.flightInfo.startTime * 1000)
                .utc() // 将时间标记为 UTC
                .tz('Asia/Shanghai') // 转换为东八区
                .format('YYYY-MM-DD HH:mm:ss'),
              flightEndTime: dayjs(libraInfo.flightInfo.endTime * 1000)
                .utc() // 将时间标记为 UTC
                .tz('Asia/Shanghai') // 转换为东八区
                .format('YYYY-MM-DD HH:mm:ss'),
              flightStopTime: dayjs(flightStopEvent.ts * 1000)
                .utc() // 将时间标记为 UTC
                .tz('Asia/Shanghai') // 转换为东八区
                .format('YYYY-MM-DD HH:mm:ss'),
              fullDuration: dayDiff.totalDays,
              fullDurationInWorkDays: dayDiff.workdays,
              appName: getAppNameByLibraAppId(libraInfo.libraAppId),
              paLibraUrl: libraUrlOfPaperAirplane(libraInfo.flightInfo.id, libraInfo.libraAppId),
              eventType: LibraEventType.FlightStop,
            });
          }
        }
      }
    }

    return result;
  }

  // 获取正在进行中的实验 id（新接口）
  async getInProgressFlightIds(libraStartTimestampMin: number, libraStartTimestampMax: number): Promise<number[]> {
    const query: any = {
      libraAppId: { $in: [147, 305] },
      'flightInfo.status': { $in: [1] }, // 进行中
    };
    if (libraStartTimestampMin > 0 && libraStartTimestampMax > 0) {
      query['flightInfo.startTime'] = {
        $gte: libraStartTimestampMin,
        $lte: libraStartTimestampMax,
      };
    } else if (libraStartTimestampMin > 0) {
      query['flightInfo.startTime'] = {
        $gte: libraStartTimestampMin,
      };
    } else if (libraStartTimestampMax > 0) {
      query['flightInfo.startTime'] = {
        $lte: libraStartTimestampMax,
      };
    }

    const flightList = await this.libraNewInfoListDao.listAll(query);
    if (!flightList || flightList.length === 0) {
      return [];
    }

    const flightIds = [];
    for (const flight of flightList) {
      flightIds.push(flight.flightInfo.id);
    }
    return flightIds;
  }

  // 获取可能全量的全部实验 id（新接口）
  async getPossibleFullFlightIds(
    libraStartTimestampMin: number,
    libraStartTimestampMax: number,
    libraStopTimestampMin: number,
    libraStopTimestampMax: number,
  ): Promise<number[]> {
    const query: any = {
      libraAppId: { $in: [147, 305] },
      'flightInfo.status': { $in: [0, 91] },
    };
    if (libraStartTimestampMin > 0 && libraStartTimestampMax > 0) {
      query['flightInfo.startTime'] = {
        $gte: libraStartTimestampMin,
        $lte: libraStartTimestampMax,
      };
    } else if (libraStartTimestampMin > 0) {
      query['flightInfo.startTime'] = {
        $gte: libraStartTimestampMin,
      };
    } else if (libraStartTimestampMax > 0) {
      query['flightInfo.startTime'] = {
        $lte: libraStartTimestampMax,
      };
    }
    if (libraStopTimestampMin > 0 && libraStopTimestampMax > 0) {
      query['flightInfo.endTime'] = {
        $gte: libraStopTimestampMin,
        $lte: libraStopTimestampMax,
      };
    } else if (libraStopTimestampMin > 0) {
      query['flightInfo.endTime'] = {
        $gte: libraStopTimestampMin,
      };
    } else if (libraStopTimestampMax > 0) {
      query['flightInfo.endTime'] = {
        $lte: libraStopTimestampMax,
      };
    }
    const flightList = await this.libraNewInfoListDao.listAll(query);
    // 进一步过滤，只考虑“正式实验”，过滤掉灰度实验
    if (!flightList || flightList.length === 0) {
      return [];
    }

    const flightIds = [];
    for (const flight of flightList) {
      const isGray = this.isGrayFlightInner(flight);
      if (isGray) {
        // 如果是灰度实验，则不考虑。只计算正式试验。
        continue;
      }
      flightIds.push(flight.flightInfo.id);
    }
    return flightIds;
  }

  // 判断一个实验是否是灰度实验
  async isGrayFlight(flightId: number): Promise<boolean> {
    const flightInfo = await this.libraNewInfoListDao.findOne({ 'flightInfo.id': flightId });
    if (!flightInfo) {
      return false;
    }
    return this.isGrayFlightInner(flightInfo);
  }

  isGrayFlightInner(flightInfo: LibraNewInfo) {
    const filterRule = flightInfo.flightInfo.filterRule ?? [];
    // 从过滤条件里面，获取版本相关信息
    const versionInfoByFilter = getLibraVersionPlatformFromFilterRule(
      filterRule,
      flightInfo.flightInfo.region,
      flightInfo.flightInfo.name,
      flightInfo.flightInfo.description,
    );
    const isGray =
      (versionInfoByFilter.android.isHit && versionInfoByFilter.android.isBeta) ||
      (versionInfoByFilter.iphone.isHit && versionInfoByFilter.iphone.isBeta);
    return isGray;
  }

  // 获取可能全量的全部实验 id
  // @FIXME: 接口数据不置信，不要使用，后续考虑删掉
  async getFlightIdsForPossibleFullFlight(libraStartTimestamp: number, libraStopTimestamp: number) {
    // const libraAppIds = [147, 305];
    // const libraStartTimestamp = 1735660800; // 开启实验在 2025-01-01 00:00:00 以后
    // const libraStopTimestamp = 1743436799; // 实验关闭在 2025-03-31 23:59:59 之前

    // 纸飞机上，通过人工标记为“实验全量”的实验（优先考虑）
    const flightStopLibraInfos = await this.libraNewInfoListDao.listAll({
      'flightInfo.startTime': { $gte: libraStartTimestamp },
      'flightInfo.endTime': { $lte: libraStopTimestamp },
      libraAppId: { $in: [147, 305] },
      'flightInfo.status': { $in: [0, 91] },
      'flightInfo.closeAttributionInfo.mainType': LibraFlightCloseMainType.FullRelease,
    });
    const flightStopIds = Array.from(new Set(flightStopLibraInfos.map(info => info.flightInfo.id)));

    // Libra 事件标记为“实验全量”的实验
    const eventListFlightFull = await this.libraChangeEventDao.listAllWithNoAggregate({
      eventType: LibraEventType.FlightFull,
      ts: {
        $gte: libraStartTimestamp,
        $lte: libraStopTimestamp,
      },
    });
    const flightFullIds = Array.from(new Set(eventListFlightFull.map(item => item.flightId))); // 去重 Flight Id
    // 暂时只查询剪映和 CapCut 的实验
    const flightFullLibraInfos = await this.libraNewInfoListDao.listAll({
      'flightInfo.id': { $in: flightFullIds },
      'flightInfo.startTime': { $gte: libraStartTimestamp },
      libraAppId: { $in: [147, 305] },
    });
    const filteredFlightFullIds = Array.from(new Set(flightFullLibraInfos.map(info => info.flightInfo.id)));
    const finalFlightFullIds = [];
    for (const flightId of filteredFlightFullIds) {
      if (!flightStopIds.includes(flightId)) {
        finalFlightFullIds.push(flightId);
      }
    }

    return {
      flightFullIds: finalFlightFullIds,
      flightStopIds,
    };
  }

  async getFlightInfoWithTime(libraAppId: number, version: string, minTimestamp: number, maxTimestamp: number) {
    const allLibraInfos = await this.libraNewInfoListDao.listAll({
      libraAppId,
      'flightInfo.startTime': { $gte: minTimestamp, $lte: maxTimestamp },
    });
    if (!allLibraInfos || allLibraInfos.length === 0) {
      return undefined;
    }
    // 过滤版本
    const filteredLibraInfos = allLibraInfos.filter(info => {
      const versionInfo = getLibraVersionPlatformFromFilterRule(
        info.flightInfo.filterRule,
        info.flightInfo.region,
        info.flightInfo.name,
        info.flightInfo.description,
      );
      const isHit =
        (versionInfo.android.isHit && versionInfo.android.version === version) ||
        (versionInfo.iphone.isHit && versionInfo.iphone.version === version);
      const hitBeta =
        (versionInfo.android.isHit && versionInfo.android.isBeta) ||
        (versionInfo.iphone.isHit && versionInfo.iphone.isBeta);
      return isHit && hitBeta;
    });
    if (!filteredLibraInfos || filteredLibraInfos.length === 0) {
      return undefined;
    }
    return filteredLibraInfos;
  }

  // 根据 LibraEventType + FlightId，获取单个实验的全量周期
  // @FIXME: 接口数据不置信，不要使用，后续考虑删掉
  async getSingleFlightFullPeriod(
    eventType: LibraEventType,
    flightId: number,
    libraStartTimestamp: number,
    libraStopTimestamp: number,
  ) {
    if (!(eventType === LibraEventType.FlightFull || eventType === LibraEventType.FlightStop)) {
      return {};
    }
    // 根据 flightId 查询 event change 和 实验信息
    const libraInfo = await this.libraNewInfoListDao.findById(flightId.toString());
    if (!libraInfo) {
      return {};
    }

    const eventList = await this.libraChangeEventDao.listAllWithNoAggregate({
      eventType: {
        $in: [LibraEventType.FlightFull, LibraEventType.FlightStop],
      },
      ts: {
        $gte: libraStartTimestamp,
        $lte: libraStopTimestamp,
      },
      flightId,
    });

    let meegoName = '';
    let meegoUrl = '';
    let meegoType = '';
    if (libraInfo.meegoInfo && libraInfo.meegoInfo.length > 0) {
      const meegoNameList = Array.from(new Set(libraInfo.meegoInfo.map(info => info.name)));
      meegoName = meegoNameList.join(',');
      const meegoUrlList = Array.from(new Set(libraInfo.meegoInfo.map(info => info.url)));
      meegoUrl = meegoUrlList.join(',');
      const meegoTypeList = Array.from(new Set(libraInfo.meegoInfo.map(info => info.type ?? '')));
      meegoType = meegoTypeList.join(',');
    }

    const changeEvent = eventList && eventList.length > 0 ? eventList[0] : undefined;
    const dayDiff = calculateDayDiffResult(
      libraInfo.flightInfo.startTime,
      changeEvent ? changeEvent.ts : libraInfo.flightInfo.endTime,
    );

    if (meegoName.length > 0 && meegoType.length === 0) {
      // 再拉取一下 meego 类型信息
      const flight = await this.libraApi.queryFlight(libraInfo.flightInfo.region, flightId);
      if (flight === undefined) {
        return {};
      }
      const meegoInfos: WorkItemInfo[] = [];
      const libraMeegoGroups: Record<string, LibraMeego[]> = {};
      for (const libraMeegoInfo of flight.meego_info.meego_array) {
        if (!libraMeegoGroups[libraMeegoInfo.meego_project]) {
          libraMeegoGroups[libraMeegoInfo.meego_project] = [];
        }
        libraMeegoGroups[libraMeegoInfo.meego_project].push(libraMeegoInfo);
      }
      for (const project of Object.keys(libraMeegoGroups)) {
        const workItemIds = libraMeegoGroups[project].map(meegoInfo => parseInt(meegoInfo.meego_story, 10));
        const groupMeegoInfos = await this.meegoService.requestWorkItem(project, 'story', workItemIds);
        meegoInfos.push(...(groupMeegoInfos.data ?? []));
      }

      for (const libraMeego of flight.meego_info.meego_array) {
        const meegoInfo = meegoInfos.find(
          workItemInfo =>
            workItemInfo.id === parseInt(libraMeego.meego_story, 10) &&
            ((libraMeego.meego_project_key && workItemInfo.project_key === libraMeego.meego_project_key) ||
              (libraMeego.meego_project && workItemInfo.simple_name === libraMeego.meego_project)),
        );
        if (meegoInfo) {
          const { template_id } = meegoInfo;
          // 需求类型
          const type = this.getMeegoType(template_id);
          if (meegoType.length === 0) {
            meegoType = type;
          } else {
            meegoType = `${meegoType},${type}`;
          }
        }
      }
    }

    if (eventType === LibraEventType.FlightFull || eventType === LibraEventType.FlightStop) {
      return {
        flightId: libraInfo.flightInfo.id,
        flightName: libraInfo.flightInfo.name,
        flightUrl: libraDetailUrl(libraInfo.flightInfo.region, libraInfo.flightInfo.id),
        flightStartTime: dayjs(libraInfo.flightInfo.startTime * 1000)
          .utc() // 将时间标记为 UTC
          .tz('Asia/Shanghai') // 转换为东八区
          .format('YYYY-MM-DD HH:mm:ss'),
        flightEndTime: dayjs(libraInfo.flightInfo.endTime * 1000)
          .utc() // 将时间标记为 UTC
          .tz('Asia/Shanghai') // 转换为东八区
          .format('YYYY-MM-DD HH:mm:ss'),
        flightStopTime: dayjs(changeEvent ? changeEvent.ts * 1000 : libraInfo.flightInfo.endTime * 1000)
          .utc() // 将时间标记为 UTC
          .tz('Asia/Shanghai') // 转换为东八区
          .format('YYYY-MM-DD HH:mm:ss'),
        fullDuration: dayDiff.totalDays,
        fullDurationInWorkDays: dayDiff.workdays,
        appName: getAppNameByLibraAppId(libraInfo.libraAppId),
        paLibraUrl: libraUrlOfPaperAirplane(libraInfo.flightInfo.id, libraInfo.libraAppId),
        eventType,
        meegoName,
        meegoUrl,
        meegoType,
      };
    }

    if (eventType === LibraEventType.FlightStop) {
      // const checkSettings = ([FlightType.SettingsClientSDK, FlightType.SettingsClientNormal] as string[]).includes(
      //   libraInfo.flightInfo?.libraType ?? '',
      // );
      // if (!checkSettings) {
      //   return {};
      // }
      // const flightDetail = await this.libraAPI.queryFlight(libraInfo.flightInfo.region, libraInfo.flightInfo.id);
      // if (flightDetail?.settings_item_ids && flightDetail?.settings_item_ids.length > 0) {
      //   for (const itemId of flightDetail.settings_item_ids) {
      //     const abConfigs = await this.settingsService.querySettingsItemABConfigs(itemId);
      //     const curFlight = abConfigs?.find(config => config?.flight_id === libraInfo.flightInfo.id);
      //     if (curFlight?.publish_status === PublishStatus.Published) {
      //       // 通过 event_list 获取实验关闭时间
      //       const flightStopEvent = eventList.find(event => event.flightId === libraInfo.flightInfo.id);
      //       if (!flightStopEvent) {
      //         continue;
      //       }
      //       let meegoName = '';
      //       let meegoUrl = '';
      //       let meegoType = '';
      //       if (libraInfo.meegoInfo && libraInfo.meegoInfo.length > 0) {
      //         const meegoNameList = Array.from(new Set(libraInfo.meegoInfo.map(info => info.name)));
      //         meegoName = meegoNameList.join(',');
      //         const meegoUrlList = Array.from(new Set(libraInfo.meegoInfo.map(info => info.url)));
      //         meegoUrl = meegoUrlList.join(',');
      //         const meegoTypeList = Array.from(new Set(libraInfo.meegoInfo.map(info => info.type ?? '')));
      //         meegoType = meegoTypeList.join(',');
      //       }
      //       // 已固化
      //       return {
      //         flightId: libraInfo.flightInfo.id,
      //         flightName: libraInfo.flightInfo.name,
      //         flightUrl: libraDetailUrl(libraInfo.flightInfo.region, libraInfo.flightInfo.id),
      //         flightStartTime: dayjs(libraInfo.flightInfo.startTime * 1000)
      //           .utc() // 将时间标记为 UTC
      //           .tz('Asia/Shanghai') // 转换为东八区
      //           .format('YYYY-MM-DD HH:mm:ss'),
      //         flightEndTime: dayjs(libraInfo.flightInfo.endTime * 1000)
      //           .utc() // 将时间标记为 UTC
      //           .tz('Asia/Shanghai') // 转换为东八区
      //           .format('YYYY-MM-DD HH:mm:ss'),
      //         flightStopTime: dayjs(flightStopEvent.ts * 1000)
      //           .utc() // 将时间标记为 UTC
      //           .tz('Asia/Shanghai') // 转换为东八区
      //           .format('YYYY-MM-DD HH:mm:ss'),
      //         fullDuration: dayDiff.totalDays,
      //         fullDurationInWorkDays: dayDiff.workdays,
      //         appName: getAppNameByLibraAppId(libraInfo.libraAppId),
      //         paLibraUrl: libraUrlOfPaperAirplane(libraInfo.flightInfo.id, libraInfo.libraAppId),
      //         eventType: LibraEventType.FlightStop,
      //         meegoName,
      //         meegoUrl,
      //         meegoType,
      //       };
      //     }
      //   }
      // }
    }

    return {};
  }

  // 根据需求收益 task id 查询对应的实验信息，用于评估需求的全量周期
  async queryMeegoFullPeriodByStoryRevenueTask(meegoId: string, reviewPeriodId: string) {
    // 先通过 reviewPeriodId + meegoId 查询 task info
    const taskInfo = await this.storyRevenueTaskDao.find(meegoId, reviewPeriodId);
    if (!taskInfo) {
      return [];
    }

    // 再通过 task info 查询实验信息
    if (
      !taskInfo.experimentInfo ||
      taskInfo.experimentInfo.subExperimentCount === 0 ||
      taskInfo.experimentInfo.subExperimentList.length === 0
    ) {
      return [];
    }

    // 获取实验列表
    const experimentList = taskInfo.experimentInfo.subExperimentList;
    const result: {
      meegoId: string;
      meegoName: string;
      meegoUrl: string;
      meegoType: string;
      meegoFullDuration: number;
      meegoFullDurationInWorkDays: number;
      flightId: number;
      flightName: string;
      flightUrl: string;
      flightStartTime: string;
      flightEndTime: string;
      flightStopTime: string;
      appName: string;
      paLibraUrl: string;
    }[] = [];
    for (const experiment of experimentList) {
      const flightId = Number(experiment.libraFlightId);
      const libraInfo = await this.libraNewInfoListDao.findOne({
        'flightInfo.id': flightId,
      });
      if (!libraInfo) {
        continue;
      }
      if (
        !(
          libraInfo.flightInfo.status === LibraFlightStatus.Ended ||
          libraInfo.flightInfo.status === LibraFlightStatus.Released
        )
      ) {
        // 实验不是结束状态，不考虑
        continue;
      }
      // 通过 libra api 查询实验
      const flightDetail = await this.libraAPI.queryFlight(libraInfo.flightInfo.region, libraInfo.flightInfo.id);
      // 有 settings key
      if (flightDetail?.settings_item_ids && flightDetail?.settings_item_ids.length > 0) {
        for (const itemId of flightDetail.settings_item_ids) {
          const abConfigs = await this.settingsService.querySettingsItemABConfigs(itemId);
          const curFlight = abConfigs?.find(config => config?.flight_id === libraInfo.flightInfo.id);
          if (curFlight?.publish_status === PublishStatus.Published) {
            // 实验已经固化，则查询实验的关闭时间
            const eventList = await this.libraChangeEventDao.listAllWithNoAggregate({
              eventType: {
                $in: [LibraEventType.FlightFull, LibraEventType.FlightStop],
              },
              flightId,
            });
            let libraStopTime = libraInfo.flightInfo.endTime;
            if (eventList && eventList.length > 0) {
              libraStopTime = eventList[0].ts;
            }
            // 查询 Meego 信息，获取“需求线内评审完成”时间
            const meegoInfos = await this.meegoService.requestWorkItem(MeegoProjectKeyFaceU, 'story', [
              Number(meegoId),
            ]);
            if (meegoInfos.err_code !== 0 || !meegoInfos.data || meegoInfos.data.length === 0) {
              // 查询 Meego 信息失败
              continue;
            }
            const meegoInfo = meegoInfos.data[0];
            const { state_times } = meegoInfo;
            for (const stateTime of state_times) {
              if (stateTime.name === '线内评审' && stateTime.state_key === 'state_77') {
                // 是毫秒，转换成秒
                const finishReviewTime = stateTime.end_time / 1000;
                // 获取需求的全量周期
                const dayDiff = calculateDayDiffResult(finishReviewTime, libraStopTime);
                const libraMeegoInfo = libraInfo.meegoInfo?.find(info => info.id.toString() === meegoId);
                result.push({
                  meegoId,
                  meegoName: libraMeegoInfo?.name ?? '',
                  meegoUrl: libraMeegoInfo?.url ?? '',
                  meegoType: this.getMeegoType(meegoInfo.template_id),
                  meegoFullDuration: dayDiff.totalDays,
                  meegoFullDurationInWorkDays: dayDiff.workdays,
                  flightId: libraInfo.flightInfo.id,
                  flightName: libraInfo.flightInfo.name,
                  flightUrl: libraDetailUrl(libraInfo.flightInfo.region, libraInfo.flightInfo.id),
                  flightStartTime: dayjs(libraInfo.flightInfo.startTime * 1000)
                    .utc() // 将时间标记为 UTC
                    .tz('Asia/Shanghai') // 转换为东八区
                    .format('YYYY-MM-DD HH:mm:ss'),
                  flightEndTime: dayjs(libraInfo.flightInfo.endTime * 1000)
                    .utc() // 将时间标记为 UTC
                    .tz('Asia/Shanghai') // 转换为东八区
                    .format('YYYY-MM-DD HH:mm:ss'),
                  flightStopTime: dayjs(libraStopTime * 1000)
                    .utc() // 将时间标记为 UTC
                    .tz('Asia/Shanghai') // 转换为东八区
                    .format('YYYY-MM-DD HH:mm:ss'),
                  appName: getAppNameByLibraAppId(libraInfo.libraAppId),
                  paLibraUrl: libraUrlOfPaperAirplane(libraInfo.flightInfo.id, libraInfo.libraAppId),
                });
              }
            }
          }
        }
      }
    }

    if (result.length > 0) {
      return result;
    }

    return [];
  }

  // 更新单个 Libra Info 的 MeegoType 信息（通过实验 id）
  async updateMeegoTypeOfLibraInfo(flightId: number) {
    // 根据 flightId 查询 实验信息
    const libraInfo = await this.libraNewInfoListDao.findById(flightId.toString());
    if (!libraInfo) {
      return {
        code: -1,
        message: 'libraInfo not found',
      };
    }

    // 根据实验信息获取 Meego 信息
    const meegoInfoList = libraInfo.meegoInfo;
    if (!meegoInfoList || meegoInfoList.length === 0) {
      return {
        code: -1,
        message: 'meegoInfo not found',
      };
    }

    const newMeegoInfoList: LibraMeegoInfo[] = [];
    let shouldUpdateLibraInfo = false;
    for (const meegoInfo of meegoInfoList) {
      if (meegoInfo.type !== undefined) {
        // 如果已经有 meego type，直接 continue
        newMeegoInfoList.push(meegoInfo);
        continue;
      }

      if (!shouldUpdateLibraInfo) {
        shouldUpdateLibraInfo = true;
      }

      //  如果没有 MeegoType，则进行查询并赋值
      // 首先通过需求收益平台查询，若找得到，则直接赋值
      const taskInfoList = await this.storyRevenueTaskDao.list({
        'meegoInfo.id': meegoInfo.id.toString(),
        terminated: false,
      });
      if (taskInfoList && taskInfoList.length > 0) {
        const taskInfo = taskInfoList[0];
        if (taskInfo.meegoInfo && taskInfo.meegoInfo.type.length > 0) {
          // 通过 task info 已有的需求信息赋值
          meegoInfo.type = taskInfo.meegoInfo.type;
          newMeegoInfoList.push(meegoInfo);
          continue;
        }
      }

      // 若 MeegoType 还是为空，则通过 Meego API 查询
      const meegoInfos = await this.meegoService.requestWorkItem(MeegoProjectKeyFaceU, 'story', [meegoInfo.id]);
      if (meegoInfos.err_code !== 0 || !meegoInfos.data || meegoInfos.data.length === 0) {
        // 查询 Meego 信息失败，无法更新 MeegoType
        newMeegoInfoList.push(meegoInfo);
        continue;
      }
      const meegoWorkItem = meegoInfos.data[0];
      const { template_id } = meegoWorkItem;
      // 需求类型
      meegoInfo.type = this.getMeegoType(template_id);
      newMeegoInfoList.push(meegoInfo);
    }

    if (shouldUpdateLibraInfo) {
      libraInfo.meegoInfo = newMeegoInfoList;
      await this.libraNewInfoListDao.save(libraInfo);
    }

    return {
      code: 0,
      message: 'success',
    };
  }

  async tryToAddCloseAttributionOfLibraInfoByLibraReason(libraInfo: LibraNewInfo, libraEvent?: LibraEvent) {
    if (libraInfo.flightInfo.closeAttributionInfo) {
      // 已经有关闭归因，则无需再处理
      return false;
    }

    let shouldUpdateLibraInfo = false;
    if (libraInfo.flightInfo.stopReasonType === LibraStopType.Offline) {
      // 自动填充实验下线归因
      shouldUpdateLibraInfo = true;
      libraInfo.flightInfo.closeAttributionInfo = {
        mainType: LibraFlightCloseMainType.Offline,
      };
      if (libraInfo.flightInfo.stopReasonDetail === 'CollectData') {
        //  回收数据，准备推全
        libraInfo.flightInfo.closeAttributionInfo.offlineSubType =
          LibraFlightCloseOfflineSubType.CollectDataReadyToFullRelease;
      } else if (libraInfo.flightInfo.stopReasonDetail === 'OnlineEmergency') {
        // 线上应急（指标异常、用户反馈等），关闭止损
        libraInfo.flightInfo.closeAttributionInfo.offlineSubType = LibraFlightCloseOfflineSubType.OnlineEmergency;
      } else if (libraInfo.flightInfo.stopReasonDetail === 'UserNegativeFeedback') {
        // 用户负向反馈
        libraInfo.flightInfo.closeAttributionInfo.offlineSubType = LibraFlightCloseOfflineSubType.UserNegativeFeedback;
      } else if (libraInfo.flightInfo.stopReasonDetail === 'IndicatorNotMatch') {
        // 指标不符合预期或需要继续迭代
        libraInfo.flightInfo.closeAttributionInfo.offlineSubType = LibraFlightCloseOfflineSubType.IndicatorNotMatch;
      } else {
        // 其他
        libraInfo.flightInfo.closeAttributionInfo.offlineSubType = LibraFlightCloseOfflineSubType.Others;
        if (libraInfo.flightInfo.stopReasonDetail) {
          libraInfo.flightInfo.closeAttributionInfo.customReason = libraInfo.flightInfo.stopReasonDetail;
        }
      }
    } else if (libraInfo.flightInfo.stopReasonType === LibraStopType.FullRelease) {
      // 自动填充实验全量归因
      shouldUpdateLibraInfo = true;
      libraInfo.flightInfo.closeAttributionInfo = {
        mainType: LibraFlightCloseMainType.FullRelease,
        fullReleaseSubType: LibraFlightCloseFullReleaseSubType.Normal,
      };
    }

    // 统一更新一下 updateUser 和 updateTime
    if (shouldUpdateLibraInfo && libraInfo.flightInfo.closeAttributionInfo) {
      if (libraEvent) {
        libraInfo.flightInfo.closeAttributionInfo.updateUser = add_suffix_ne('@bytedance.com')(
          libraEvent.event.event_operator,
        );
        libraInfo.flightInfo.closeAttributionInfo.updateTime = Number(libraEvent.ts);
      } else {
        // 尝试获取一下实验关闭人
        const eventList = await this.libraChangeEventDao.listAllWithNoAggregate({
          eventType: {
            $in: [LibraEventType.FlightStop],
          },
          flightId: libraInfo.flightInfo.id,
        });
        if (eventList && eventList.length > 0) {
          libraInfo.flightInfo.closeAttributionInfo.updateUser = add_suffix_ne('@bytedance.com')(
            eventList[0].eventOperator,
          );
          libraInfo.flightInfo.closeAttributionInfo.updateTime = eventList[0].ts;
        }
      }
    }

    return shouldUpdateLibraInfo;
  }

  // 更新单个 Libra Info 的实验关闭归因（通过实验 id），主要是实验全量和实验下线两类，不包括实验重开
  async updateCloseAttributionOfLibraInfoByLibraReason(flightId: number) {
    // 根据 flightId 查询 实验信息
    const libraInfo = await this.libraNewInfoListDao.findById(flightId.toString());
    if (!libraInfo) {
      return {
        code: -1,
        message: 'libraInfo not found',
      };
    }

    // 尝试自动填充实验关闭归因
    const shouldUpdateLibraInfo = await this.tryToAddCloseAttributionOfLibraInfoByLibraReason(libraInfo);

    if (shouldUpdateLibraInfo) {
      // 更新一下关闭归因
      await this.libraNewInfoListDao.save(libraInfo);
      return {
        code: 0,
        message: 'update closeAttributionInfo of libra info success',
      };
    }

    return {
      code: 0,
      message: 'do not need to update closeAttributionInfo',
    };
  }
}
