/* eslint-disable max-depth */
/* eslint-disable max-lines-per-function */
import { Inject, Injectable } from '@gulux/gulux';
import { genericRequest, slardarPersonalToken } from './slardarApiWrapper';
import { FlexMetaResponseData } from '@shared/typings/slardar/flex/meta';
import { FlexFilter, FlexGroupBy, FlexMeasure, FlexSeriesResponseData } from '@shared/typings/slardar/flex/querySeries';
import { FlexFilterCandidateResponseData } from '@shared/typings/slardar/flex/queryCandidate';
import {
  ANDROID_CHANEL_NOT_IN,
  ANDROID_OFFLINE_CHANNEL_NOT_IN,
  crashType2Title,
  CrashType2Url,
  getSlardarUrl,
  GranularityType,
  SlardarCrashCommon,
} from '@shared/typings/slardar/common';
import {
  Condition,
  CrashIosMemoryListV2Data,
  CrashListResult,
  CrashType,
  GetCrashFieldPercentRequest,
  GetCrashFieldPercentResponseData,
  GetCrashFiledPercentParams,
  GetCrashIosMemoryListV2,
  GetCrashListRequest,
  GetNodeDetail,
  GetNodeDetailData,
  GetNodeList,
  GetNodeListDataItem,
} from '@shared/typings/slardar/crash/issueListSearch';
import { HYPIC_SLARDAR_APP_ID, RegionType, SLARDAR_APP_ID } from './shared';
import { GetCrashTrendRequest, GetCrashTrendResponseData } from '@shared/typings/slardar/crash/trend';
import {
  DeviceLevel,
  highAndroidCondition,
  highIosCondition,
  lowAndroidCondition,
  lowIosCondition,
} from '@shared/common';
import SlardarCrashIssueListModel, { DBSlardarCrashIssueList } from '../../model/SlardarCrashIssueListTable';
import SlardarCrashIssueModel, { DBSlardarCrashIssue } from '../../model/SlardarCrashIssueTable';
import { useInject } from '@edenx/runtime/bff';
import MeegoService from '../meego';
import { current_region, Region, utcOffset } from '../../utils/region';
import AlarmVersionModel from '../../model/AlarmVersionInfoTable';
import { groupBy, mergeWith } from 'lodash';
import {
  GetCrashEventListRequest,
  GetSymboLicateAddressListResult,
  IssueEventDetailResult,
  IssueEventListItem,
  IssueEventListResult,
} from '@shared/typings/slardar/crash/issueLocation';
import commonUtils from '@shared/utils/commonUtils';
import { getSlardarIssueLink, parseSlardarLink } from '../../utils/slardarLink';
import {
  CommentTextResponseData,
  GetIssueDetailResponseData,
  GetPercentResponseData,
} from '@shared/typings/slardar/crash/issueDetail';
import dayjs from 'dayjs';
import { AndroidLevelProcessor } from './AndroidLevelProcessor';
import { iOSLevelProcessor } from './iOSLevelProcessor';
import WalleService from '../third/walle';
import LarkService from '@pa/backend/dist/src/third/lark';
import {
  AndroidTypeOf,
  correctVersionCode,
  IosTypeOf,
  versionCodeToMeegoVersion,
  versionCodeToVersion,
  VersionType,
} from '@shared/utils/version_utils';
import { RedisClient } from '@gulux/gulux/redis';
import utc from 'dayjs/plugin/utc';
import { AutoAssignment, Config } from '@shared/typings/slardar/crash/issueAutoAssign';
import { CreateOrJoinData } from '@shared/typings/slardar/crash/issueChat';
import MeegoRawService from '../third/meego';
import { BytedLogger } from '@gulux/gulux/byted-logger';
import AirplaneConfigService from '../AirplaneConfigService';
import { VersionConfigKeys } from '@shared/aircraftConfiguration';
import SlardarConfig from './SlardarConfig';
import SceneOwnerConfigModel from 'api/model/SceneOwnerConfigTable';
import { SlardarResponse } from '@shared/typings/slardar/response';
import { GetNodeGroupListRequest, GroupItem, MemoryGraphDetail } from '../../model/MemoryGraphModels';
import { RetouchAndroidLevelProcessorV1 } from './RetouchAndroidLevelProcessorV1';
import { RetouchIOSLevelProcessorV1 } from './RetouchIOSLevelProcessorV1';
import { SlardarPlatformType } from '@pa/shared/dist/src/appSettings/appSettings';
import { SymboLicateAddressList } from '@shared/typings/slardar/crash/ttMleaksFinderData';
import { RuleDisplayData } from '@shared/typings/slardar/alarm/crashAlarmDetail';
import { NetworkCode } from '@pa/shared/dist/src/core';

@Injectable()
export default class SlardarService {
  @Inject()
  private slardarCrashIssueModel: SlardarCrashIssueModel;
  @Inject()
  private alarmVersionModel: AlarmVersionModel;
  @Inject()
  private redis: RedisClient;
  @Inject()
  private logger: BytedLogger;
  @Inject()
  private config: SlardarConfig;

  static getCommentBody(aid: number, issue_id: string, platform: SlardarPlatformType) {
    return {
      aid,
      os: platform,
      issue_id,
    };
  }
  static buildCommentBody(aid: number, issue_id: string, platform: SlardarPlatformType, comment: string) {
    return {
      aid,
      os: platform,
      issue_id,
      comment_text: comment,
      sdk: false,
      type: '',
      sub_issue_id: '',
    };
  }

  private static buildFilterList(version_list: string[] = [], other_filters?: FlexFilter[]): FlexFilter[] | undefined {
    const ver: FlexFilter[] =
      version_list.length <= 0
        ? []
        : [
            {
              filter_name: 'update_version_code',
              op: 'in',
              type: '',
              values: version_list,
            },
          ];
    const ret = other_filters ? other_filters.concat(ver) : ver;
    return ret.length <= 0 ? undefined : ret;
  }

  private static aidIsCN(aid: number): boolean {
    return [1128, 32, 2329, 10001, 150121, 2515, 1775, 5032].includes(aid);
  }

  private static buildDetailBody(aid: number, issue_id: string, platform: SlardarPlatformType, crashType: string) {
    return {
      aid,
      os: platform,
      region: current_region() === Region.SG ? 'maliva' : 'cn',
      sdk: false,
      start_time: Math.floor(dayjs().subtract(1, 'day').valueOf() / 1000),
      end_time: Math.floor(dayjs().valueOf() / 1000),
      crash_type: crashType,
      issue_id,
      sub_issue_id: '',
      filters_conditions: { type: 'and', sub_conditions: [] },
      versions_conditions: {},
      granularity: 86400,
      token: '',
      token_type: 0,
      event_ids: [],
      crash_time_type: 'insert_time',
    };
  }

  private static buildMostLastSceneBody(
    issue_id: string,
    platform: SlardarPlatformType,
    crashType: string,
    version: string,
  ) {
    return {
      aid: SLARDAR_APP_ID(),
      os: platform,
      region: current_region() === Region.SG ? 'maliva' : 'cn',
      sdk: false,
      start_time: Math.floor(dayjs().subtract(1, 'day').valueOf() / 1000),
      end_time: Math.floor(dayjs().valueOf() / 1000),
      crash_type: crashType,
      issue_id,
      field: 'last_scene',
      sub_issue_id: '',
      filters_conditions: {
        type: 'and',
        sub_conditions: [
          {
            dimension: 'raw_update_version_code',
            op: 'in',
            values: [version],
            groupKey: null,
            type: 'expression',
          },
        ],
      },
      versions_conditions: {},
      granularity: 86400,
      token: '',
      token_type: 0,
      event_ids: [],
      crash_time_type: 'insert_time',
    };
  }

  private static buildChatBody(
    aid: number,
    platform: SlardarPlatformType,
    issue_id: string,
    crashType: string,
    title: string,
    user: string,
    id?: number,
  ) {
    return {
      aid,
      crash_type: crashType,
      id,
      issue_id,
      os: platform,
      region: current_region() === Region.SG ? 'maliva' : 'cn',
      sdk: false,
      sub_issue_id: '',
      title,
      user,
    };
  }

  private static buildTopVersionTrendBody(
    aid: number,
    os: 'Android' | 'iOS',
    start_time: number,
    end_time: number,
    crash_type: string,
    granularity = 60,
    additional_params: Partial<SlardarCrashCommon> = {},
  ): GetCrashTrendRequest {
    const ret: GetCrashTrendRequest = {
      aid,
      os,
      region: this.aidIsCN(aid) ? 'cn' : 'maliva',
      start_time,
      end_time,
      order_by: 'user_descend',
      anls_dim: [],
      shortCutKey: 'custom',
      sdk: false,
      ios_issue_id_version: 'v2',
      filters_conditions: {
        type: 'and',
        sub_conditions:
          os === 'Android'
            ? [
                {
                  dimension: 'channel',
                  op: 'not_in',
                  type: 'expression',
                  values: ANDROID_CHANEL_NOT_IN,
                },
              ]
            : [],
      },
      granularity: granularity.toFixed(0),
      trend_types: ['count', 'count_start', 'active', 'user_active'],
      crash_type,
      versions_conditions: {
        type: 'and',
        sub_conditions: [
          {
            dimension:
              os === 'iOS' // && crash_type === "ios_metric_kit_crash"
                ? 'raw_update_version_code'
                : 'update_version_code',
            op: 'in',
            values: ['DAU TOP3 version'],
            type: 'expression',
          },
        ],
      },
    };
    if (aid === 1775 && os === 'Android') {
      ret.filters_conditions!.sub_conditions!.push({
        dimension: 'filters_map',
        groupKey: 'custom tag',
        map_key: 'is_bytest_privacy_rom',
        op: 'not_in',
        type: 'map',
        values: ['true'],
      });
    }
    mergeWith(ret, additional_params, (dst, src) => {
      if (Array.isArray(dst)) {
        return dst.concat(src);
      }
    });
    return current_region() !== Region.SG
      ? ret
      : {
          subregion: 'row',
          ...ret,
        };
  }
  private static buildCrashTrendBody(
    aid: number,
    os: 'Android' | 'iOS',
    start_time: number,
    end_time: number,
    version: string,
    crash_type: string,
    granularity = 60,
  ) {
    const ret: GetCrashTrendRequest = {
      aid,
      os,
      region: this.aidIsCN(aid) ? 'cn' : 'maliva',
      start_time,
      end_time,
      order_by: 'user_descend',
      anls_dim: [],
      shortCutKey: 'custom',
      crash_time_type: 'insert_time',
      ios_issue_id_version: 'v2',
      filters_conditions: {
        type: 'and',
        sub_conditions:
          os === 'Android'
            ? [
                {
                  dimension: 'channel',
                  op: 'not_in',
                  type: 'expression',
                  values: ANDROID_CHANEL_NOT_IN,
                },
                {
                  dimension: 'app_version',
                  op: 'in',
                  type: 'expression',
                  values: [version],
                },
              ]
            : [],
      },
      granularity: granularity.toFixed(0),
      trend_types: ['count', 'count_start', 'active', 'user_active'],
      crash_type,
    };
    if (aid === 1775 && os === 'Android') {
      ret.filters_conditions!.sub_conditions!.push({
        dimension: 'filters_map',
        groupKey: 'custom tag',
        map_key: 'is_bytest_privacy_rom',
        op: 'not_in',
        type: 'map',
        values: ['true'],
      });
    }
    return ret;
  }
  private static buildTrendBody(
    aid: number,
    os: 'Android' | 'iOS',
    start_time: number,
    end_time: number,
    version: string | undefined,
    crash_type: string,
    granularity = 60,
    additional_params: Partial<SlardarCrashCommon> = {},
    isNew = false,
    deviceLevel: DeviceLevel,
  ): GetCrashTrendRequest {
    const ret: GetCrashTrendRequest = {
      aid,
      os,
      region: this.aidIsCN(aid) ? 'cn' : 'maliva',
      start_time,
      end_time,
      order_by: 'user_descend',
      anls_dim: [],
      shortCutKey: '',
      filters_conditions: {
        type: 'and',
        sub_conditions:
          os === 'Android'
            ? [
                {
                  dimension: 'channel',
                  op: 'not_in',
                  type: 'expression',
                  values: ANDROID_CHANEL_NOT_IN,
                },
                // { dimension: "update_version_code", op: "in", values: [version], type: "expression" },
              ]
            : [],
      },
      versions_conditions: {
        type: 'and',
        sub_conditions:
          version && version.length > 0
            ? [
                {
                  dimension:
                    os === 'iOS' // && crash_type === "ios_metric_kit_crash"
                      ? 'raw_update_version_code'
                      : 'update_version_code',
                  op: 'in',
                  values: [version],
                  type: 'expression',
                },
              ]
            : [],
      },
      granularity: granularity.toFixed(0),
      trend_types: ['count', 'count_start', 'active', 'user_active'],
      crash_type,
    };
    if (aid === 1775 && os === 'Android') {
      ret.filters_conditions!.sub_conditions!.push({
        dimension: 'filters_map',
        groupKey: 'custom tag',
        map_key: 'is_bytest_privacy_rom',
        op: 'not_in',
        type: 'map',
        values: ['true'],
      });
    }
    if (deviceLevel === DeviceLevel.LOW) {
      ret.filters_conditions!.sub_conditions!.push(
        os === SlardarPlatformType.Android ? lowAndroidCondition[0] : lowIosCondition[0],
      );
    }
    if (deviceLevel === DeviceLevel.HIGH) {
      ret.filters_conditions!.sub_conditions!.push(
        os === SlardarPlatformType.Android ? highAndroidCondition[0] : highIosCondition[0],
      );
    }
    if (isNew) {
      ret.is_new = true;
    }
    mergeWith(ret, additional_params, (dst, src) => {
      if (Array.isArray(dst)) {
        return dst.concat(src);
      }
    });
    return current_region() !== Region.SG
      ? ret
      : {
          subregion: 'row',
          ...ret,
        };
  }

  async queryMeasureList(platform: SlardarPlatformType, region: RegionType = 'cn') {
    const resp = await genericRequest<FlexMetaResponseData>('/api_v2/app/flex_v2/meta', 'POST', {
      aid: SLARDAR_APP_ID(),
      os: platform,
      lang: 'zh',
      region,
    });
    return resp.data.measure_category_meta_list!;
  }

  async JoinIssueChat(
    aid: number,
    platform: SlardarPlatformType,
    issue_id: string,
    crashType: string,
    title: string,
    user: string,
    id?: number,
  ) {
    return (
      await genericRequest<CreateOrJoinData>(
        '/api_v2/app/crash/issue/create_or_join_chat',
        'POST',
        SlardarService.buildChatBody(aid, platform, issue_id, crashType, title, user, id),
      )
    )?.data;
  }

  async querySeries(
    aid: number,
    start_time: number,
    end_time: number,
    granularity: GranularityType,
    measure_name: string,
    platform: SlardarPlatformType,
    versionList: string[] = [],
    groupByList: FlexGroupBy[] = [],
    other_filters?: FlexFilter[],
    from?: string,
  ) {
    const resp = await genericRequest<FlexSeriesResponseData>('/api_v2/app/flex_v2/query_series', 'POST', {
      aid,
      os: platform,
      lang: 'zh',
      region: current_region() === Region.SG ? 'maliva' : 'cn',
      start_time,
      end_time,
      granularity,
      group_by_list: groupByList,
      filter_list: SlardarService.buildFilterList(versionList, other_filters),
      measure_list: [
        {
          raw_measure_list: [
            {
              measure_name,
            },
          ],
          type: 'monomial',
        },
      ],
      need_time_rollup: true,
    });
    await this.quotaCountIncr(aid, `metrics:${platform}:${measure_name}-${from}`);
    return resp.data;
  }

  async queryFilterCandidate(
    filter_name: string,
    start_time: number,
    end_time: number,
    measure_name: string,
    platform: SlardarPlatformType,
    region: RegionType = 'cn',
  ) {
    const resp = await genericRequest<FlexFilterCandidateResponseData>('/api_v2/app/flex_v2/query_candidate', 'POST', {
      aid: SLARDAR_APP_ID(),
      os: platform,
      lang: 'zh',
      region,
      start_time,
      end_time,
      measure_list: [
        {
          raw_measure_list: [
            {
              measure_name,
            },
          ],
          type: 'monomial',
        },
      ],
      filter_name,
    });
    return resp.data.candidate_list;
  }

  async querySeriesRaw(
    aid: number,
    region: string,
    os: string,
    start_time: number,
    end_time: number,
    granularity: string,
    group_by_list: FlexGroupBy[] = [],
    measure_list: FlexMeasure[],
    filter_list: FlexFilter[] = [],
    need_time_rollup = true,
  ): Promise<FlexSeriesResponseData> {
    const body = {
      aid,
      region,
      os,
      start_time,
      end_time,
      granularity,
      group_by_list,
      filter_list,
      measure_list,
      lang: 'zh',
      need_time_rollup,
      sdk: false,
      time_shift_list: [],
      cond_settings: {
        exclude_null: 'false',
      },
    };
    const start = performance.now();
    const resp = await genericRequest<FlexSeriesResponseData>('/api_v2/app/flex_v2/query_series', 'POST', body);
    // await this.quotaCountIncr(aid, `metrics:${platform}:${measure_name}`);
    this.logger.info(
      `Query Slardar series finished. cost: ${performance.now() - start}ms, body: ${JSON.stringify(body)}, resp: ${JSON.stringify(resp)}`,
    );
    return resp.data;
  }

  async GetNodeDetailRequest(aid: number, os: string, tos_key: string, session_id: string, ptr: string) {
    const body: GetNodeDetail = {
      aid,
      os,
      lang: current_region() === Region.SG ? 'en' : 'zh',
      region: current_region() === Region.SG ? 'maliva' : 'cn',
      tos_key,
      session_id,
      ptr,
      sdk: false,
    };

    this.logger.info(`oyq /api_v2/app/memory_graph/get_node_detail body: ${JSON.stringify(body)}`);
    const resp = await genericRequest<GetNodeDetailData>('/api_v2/app/memory_graph/get_node_detail', 'POST', body);
    return resp.data;
  }

  async GetNodeListRequest(
    aid: number,
    os: string,
    tos_key: string,
    session_id: string,
    is_grouped_with_size: boolean,
    is_abandoned: boolean,
    node_group_list_index: number,
    page: number,
    size: number,
    keyword = '',
  ) {
    const body: GetNodeList = {
      aid,
      os,
      lang: current_region() === Region.SG ? 'en' : 'zh',
      region: current_region() === Region.SG ? 'maliva' : 'cn',
      tos_key,
      session_id,
      is_grouped_with_size,
      is_abandoned,
      node_group_list_index,
      page,
      size,
      matrix_uuid: '',
      tag_index: 0,
      keyword,
      sdk: false,
    };
    this.logger.info(`oyq /api_v2/app/memory_graph/get_node_list body: ${JSON.stringify(body)}`);
    const resp = await genericRequest<GetNodeListDataItem[]>('/api_v2/app/memory_graph/get_node_list', 'POST', body);
    return resp.data;
  }

  async crashIosMemoryListV2(
    aid: number,
    os: string,
    deviceId: string,
    start_time: number,
    end_time: number,
    pageSize: number,
    pageNum: number,
    sub_conditions?: Condition[],
  ) {
    const body: GetCrashIosMemoryListV2 = {
      aid,
      crash_time_type: 'insert_time',
      device_id: deviceId,
      start_time,
      end_time,
      page_size: pageSize,
      page_num: pageNum,
      os,
      sdk: false,
      region: current_region() === Region.SG ? 'maliva' : 'cn',
      filters_conditions: {
        type: 'and',
        sub_conditions: sub_conditions ?? [],
      },
    };

    this.logger.info(`oyq /api_v2/app/crash/ios_memory/list_v2 body: ${JSON.stringify(body)}`);
    await this.quotaCountIncr(aid, `metrics:${os}:MemoryGraph:memorygraph:list_v2`);
    const resp = await genericRequest<CrashIosMemoryListV2Data>('/api_v2/app/crash/ios_memory/list_v2', 'POST', body);
    return resp.data;
  }

  async crashIssueList(
    aid: number,
    crash_type: string,
    platform: SlardarPlatformType,
    pageSize: number,
    start_time: number,
    end_time: number,
    granularity: GranularityType,
    update_version_code?: string,
    version?: string,
    pageNum = 1,
    additional_params: Partial<SlardarCrashCommon> = {},
    isNew = false,
    deviceLevel = DeviceLevel.ALL,
    sub_conditions?: Condition[],
    not_version_code?: string[],
    isOffline = false,
  ) {
    const body: GetCrashListRequest = {
      region: current_region() === Region.SG ? 'maliva' : 'cn',
      aid,
      crash_time_type: 'insert_time',
      os: platform,
      crash_type,
      start_time,
      end_time,
      pgno: pageNum,
      pgsz: pageSize,
      granularity: Number(granularity),
      ios_issue_id_version: 'v2',
      order_by: 'user_descend',
      filters_conditions: {
        type: 'and',
        sub_conditions: [] as Condition[],
      },
    };
    if (current_region() !== Region.SG && platform === SlardarPlatformType.Android) {
      body.filters_conditions!.sub_conditions!.push({
        dimension: 'filters_map',
        groupKey: 'custom tag',
        map_key: 'is_bytest_privacy_rom',
        op: 'not_in',
        type: 'map',
        values: ['true'],
      });
    }
    if (version) {
      body.filters_conditions.sub_conditions!.push({
        dimension: 'app_version',
        op: 'in',
        values: [version],
        type: 'expression',
      });
      // body.is_new = true;
    }
    if (update_version_code && !isOffline) {
      body.filters_conditions.sub_conditions!.push({
        dimension: platform === SlardarPlatformType.iOS ? 'raw_update_version_code' : 'update_version_code',
        op: 'in',
        values: [update_version_code],
        type: 'expression',
      });
    } else if (not_version_code && !isOffline) {
      body.filters_conditions.sub_conditions!.push({
        dimension: platform === SlardarPlatformType.iOS ? 'raw_update_version_code' : 'update_version_code',
        op: 'not_in',
        values: not_version_code,
        type: 'expression',
      });
    }
    if (sub_conditions) {
      body.filters_conditions.sub_conditions!.push(...sub_conditions);
    }
    if (isNew) {
      body.is_new = true;
    }
    if (platform === SlardarPlatformType.Android && !isOffline) {
      body.filters_conditions.sub_conditions!.push({
        dimension: 'channel',
        op: 'not_in',
        type: 'expression',
        values: ANDROID_CHANEL_NOT_IN,
      });
    }
    if (platform === SlardarPlatformType.Android && isOffline) {
      body.filters_conditions.sub_conditions!.push({
        dimension: 'channel',
        op: 'not_in',
        type: 'expression',
        values: ANDROID_OFFLINE_CHANNEL_NOT_IN[aid],
      });
    }
    if (deviceLevel === DeviceLevel.LOW) {
      body.filters_conditions.sub_conditions!.push(
        platform === SlardarPlatformType.Android ? lowAndroidCondition[0] : lowIosCondition[0],
      );
    }
    if (deviceLevel === DeviceLevel.HIGH) {
      body.filters_conditions.sub_conditions!.push(
        platform === SlardarPlatformType.Android ? highAndroidCondition[0] : highIosCondition[0],
      );
    }
    mergeWith(body, additional_params, (dst, src) => {
      if (Array.isArray(dst)) {
        return dst.concat(src);
      }
    });
    this.logger.info(`oyq /api_v2/app/crash/issue/list/search body: ${JSON.stringify(body)}`);
    const resp = await genericRequest<CrashListResult>('/api_v2/app/crash/issue/list/search', 'POST', body);
    await this.quotaCountIncr(aid, `crash: deviceLevel=${deviceLevel} ${platform}:${crash_type}`);
    this.logger.info(` total: ${resp.data.total}`);
    this.logger.info(` data online: ${resp.data}`);
    return resp.data;
  }

  async crashIssueListSearch(body: GetCrashListRequest) {
    const start = performance.now();
    const resp = await genericRequest<CrashListResult>('/api_v2/app/crash/issue/list/search', 'POST', body);
    await this.quotaCountIncr(body.aid, `crash:${body.os}:${body.crash_type}`);
    this.logger.info(
      `Query Slardar list finished. cost: ${performance.now() - start}ms, body: ${JSON.stringify(body)}, resp: ${JSON.stringify(resp)}`,
    );
    return resp.data;
  }

  async getCrashInfoById(aid: number, issue_id: string, platform: SlardarPlatformType, crash_type: string) {
    const resp = await genericRequest<GetIssueDetailResponseData>(
      `/api_v2/app/crash/issue/detail?lang=zh${current_region() === Region.SG ? '&region=maliva' : ''}`,
      'POST',
      SlardarService.buildDetailBody(aid, issue_id, platform, crash_type),
    );
    await this.quotaCountIncr(aid, `crash:${platform}:${crash_type}:crash_info`);
    return resp;
  }

  async getMostLastScene(
    aid: number,
    issue_id: string,
    platform: SlardarPlatformType,
    crash_type: string,
    version: string,
  ) {
    const resp = await genericRequest<GetPercentResponseData>(
      `/api_v2/app/crash/issue/field/percent?lang=zh${current_region() === Region.SG ? '&region=maliva' : ''}`,
      'POST',
      SlardarService.buildMostLastSceneBody(issue_id, platform, crash_type, version),
    );
    await this.quotaCountIncr(aid, `crash:${platform}:${crash_type}:crash_info`);
    return resp;
  }

  async getSceneList() {
    const sceneOwnerConfigModel = useInject(SceneOwnerConfigModel);
    return await sceneOwnerConfigModel.querySceneList();
  }

  async updateSceneOwner(appid: number, platform: string, scene: string, owner: string) {
    const sceneOwnerConfigModel = useInject(SceneOwnerConfigModel);
    return await sceneOwnerConfigModel.updateSceneOwner(appid, platform, scene, owner);
  }

  async insertSceneList(sceneList: any[]) {
    const sceneOwnerConfigModel = useInject(SceneOwnerConfigModel);
    await sceneOwnerConfigModel.insertSceneList(sceneList);
  }

  async querySceneOwner(appid: number, platform: string, scene: string) {
    const sceneOwnerConfigModel = useInject(SceneOwnerConfigModel);
    return await sceneOwnerConfigModel.querySceneOwner(appid, platform, scene);
  }

  async querySceneExist(scene: string) {
    const sceneOwnerConfigModel = useInject(SceneOwnerConfigModel);
    return await sceneOwnerConfigModel.querySceneExist(scene);
  }

  async getCommentList(aid: number, issue_id: string, platform: SlardarPlatformType) {
    return await genericRequest<CommentTextResponseData[]>(
      `/api_v2/app/comment/get?lang=zh${current_region() === Region.SG ? '&region=maliva' : ''}`,
      'POST',
      SlardarService.getCommentBody(aid, issue_id, platform),
    );
  }

  async addIssueComment(aid: number, issue_id: string, platform: SlardarPlatformType, comment: string) {
    const resp = await genericRequest<GetIssueDetailResponseData>(
      `/api_v2/app/comment/add?lang=zh${current_region() === Region.SG ? '&region=maliva' : ''}`,
      'POST',
      SlardarService.buildCommentBody(aid, issue_id, platform, comment),
    );
    // await this.quotaCountIncr(`crash:${platform}:${}:crash_info`);
    return resp;
  }
  async getTopVersionCrashRate(
    aid: number,
    os: 'Android' | 'iOS',
    start_time: number,
    end_time: number,
    crash_type: string,
    granularity = 60,
    additional_params: Partial<SlardarCrashCommon> = {},
  ) {
    const resp = await genericRequest<GetCrashTrendResponseData>(
      `/api_v2/app/crash/trend?lang=zh${current_region() === Region.SG ? '&region=maliva' : ''}`,
      'POST',
      SlardarService.buildTopVersionTrendBody(
        aid,
        os,
        start_time,
        end_time,
        crash_type,
        granularity,
        additional_params,
      ),
    );
    // fixme: 分类统计
    await this.quotaCountIncr(aid, `crash:version:${os}:${crash_type}`);
    return resp.data;
  }

  /**
   * 获取维度数据
   * @param params
   */
  async getCrashFiledPercent(params: GetCrashFiledPercentParams) {
    if (!params.field) {
      params.field = 'filters';
    }

    const body: GetCrashFieldPercentRequest = {
      aid: params.aid,
      os: params.platform,
      region: current_region() === Region.SG ? 'maliva' : 'cn',
      sdk: false,
      start_time: params.start_time,
      end_time: params.end_time,
      crash_type: params.crash_type,
      crash_time_type: params.crash_time_type,
      issue_id: params.issue_id,
      filters_conditions: params.filters_conditions,
      limit: 10,
      granularity: params.granularity,
      field: params.field,
      map_key: params.map_key,
    };

    return await genericRequest<GetCrashFieldPercentResponseData>(
      body.issue_id ? `/api_v2/app/crash/issue/field/percent` : `/api_v2/app/crash/field/percent?lang=zh`,
      'POST',
      body,
    );
  }

  /**
   * 针对Android Native Crash，判断某个issue是否集中在32位设备上
   * @param d
   */
  async isMostly32BitDevice(d: DBSlardarCrashIssueList) {
    if (d.crash_type !== CrashType.NativeCrash) {
      return false;
    }
    const version = d.version ? d.version : versionCodeToVersion(d.version_code);
    const time = await this.alarmVersionModel.findGrayingTime(version);
    // 这里不进行版本过滤，灰度量相对较少，直接使用大盘数据更置信
    const response = await this.getCrashFiledPercent({
      aid: d.aid,
      platform: d.platform,
      start_time: time.start_time,
      end_time: time.end_time,
      issue_id: d.issue_id,
      crash_type: 'native',
      map_key: 'is_64_devices',
    });

    if (response.errno === 200) {
      const fieldPercent = response.data.detail?.find(item => item.field === 'false');
      if (fieldPercent?.percent && fieldPercent.percent > 0.9) {
        return true;
      }
    }

    return false;
  }

  async getCrashTrend(body: GetCrashTrendRequest): Promise<GetCrashTrendResponseData> {
    const resp = await genericRequest<GetCrashTrendResponseData>('/api_v2/app/crash/trend', 'POST', body);
    await this.quotaCountIncr(body.aid ?? 0, `crash:version:${body.os}:${body.crash_type}`);
    return resp.data;
  }

  async getCrashRate(
    aid: number,
    os: 'Android' | 'iOS',
    start_time: number,
    end_time: number,
    version: string | undefined,
    crash_type: string,
    granularity = 60,
    additional_params: Partial<SlardarCrashCommon> = {},
    deviceLevel: DeviceLevel,
    isNew = false,
  ) {
    const resp = await genericRequest<GetCrashTrendResponseData>(
      `/api_v2/app/crash/trend?lang=zh${current_region() === Region.SG ? '&region=maliva' : ''}`,
      'POST',
      SlardarService.buildTrendBody(
        aid,
        os,
        start_time,
        end_time,
        version,
        crash_type,
        granularity,
        additional_params,
        isNew,
        deviceLevel,
      ),
    );
    // fixme: 分类统计
    await this.quotaCountIncr(aid, `crash:${os}:${crash_type}`);
    return resp.data;
  }

  async getCrashTrendRate(
    aid: number,
    os: 'Android' | 'iOS',
    start_time: number,
    end_time: number,
    version: string,
    crash_type: string,
    granularity = 60,
  ) {
    const data = SlardarService.buildCrashTrendBody(aid, os, start_time, end_time, version, crash_type, granularity);
    this.logger.info(`getCrashTrendRate /api_v2/app/crash/trend body: ${JSON.stringify(data)}`);
    const resp = await genericRequest<GetCrashTrendResponseData>(
      `/api_v2/app/crash/trend?lang=zh${current_region() === Region.SG ? '&region=maliva' : ''}`,
      'POST',
      data,
    );
    return resp.data;
  }

  async autoLevelProcessor(
    aid: number,
    list: DBSlardarCrashIssueList[],
    issues: DBSlardarCrashIssue[],
    platform?: SlardarPlatformType | undefined,
    isAggregated = false,
  ): Promise<DBSlardarCrashIssueList[]> {
    const date = new Date();
    const timeStamp = date.getTime();
    this.logger.info(`oyq ppe autoLevelProcessor start with ${timeStamp}`);

    const slardarCrashIssueListModel = useInject(SlardarCrashIssueListModel);
    const grouped_list = groupBy(list, it => it.version_code);
    // find all versions
    const versions = (await this.alarmVersionModel.findVersions(aid, Object.keys(grouped_list)))
      .sort((a, b) => a.timestamp - b.timestamp)
      .map(v => ({ version_code: v.version_code }));
    if ('' in grouped_list) {
      versions.push({ version_code: '' });
    }
    const AndroidProcessor = new AndroidLevelProcessor(list, issues);
    const iOSProcessor = new iOSLevelProcessor(list, issues);
    const RetouchAndroidProcessor = new RetouchAndroidLevelProcessorV1(list, issues);
    const RetouchIOSProcessor = new RetouchIOSLevelProcessorV1(list, issues);
    // 缓存对比所需参数，减少IO
    await iOSProcessor.buildParams(aid);
    await AndroidProcessor.buildParams(aid);
    await RetouchAndroidProcessor.buildParams(aid);
    await RetouchIOSProcessor.buildParams(aid);
    let processed = 0;
    const total = list.length;
    const getProcessor = (_aid: number, _platform: SlardarPlatformType) => {
      switch (_aid) {
        case HYPIC_SLARDAR_APP_ID():
          return _platform === SlardarPlatformType.Android ? RetouchAndroidProcessor : RetouchIOSProcessor;
        case SLARDAR_APP_ID():
        default:
          return _platform === SlardarPlatformType.Android ? AndroidProcessor : iOSProcessor;
      }
    };

    this.logger.info(`oyq ppe 555 with ${timeStamp} ${JSON.stringify(versions)} ${JSON.stringify(grouped_list)}`);
    for (const v of versions) {
      const bill_of_lading: DBSlardarCrashIssueList[] = [];
      const cur: DBSlardarCrashIssueList[] = [];
      if (v.version_code in grouped_list) {
        const consume_promise: Promise<void>[] = [];
        for (const d of grouped_list[v.version_code]) {
          // const originIssueLevel = d.issue_level;
          if (platform && d.platform !== platform) {
            // 如果指定了Platform，platform 不符合不进行定级
            continue;
          }
          this.logger.info(`oyq ppe 666 with ${timeStamp} ${JSON.stringify(d)} ${isAggregated}`);
          const [issue_level, issue_level_reason] = await getProcessor(d.aid, d.platform).calculateLevel(
            d,
            isAggregated,
          );

          // ios 检查是否是历史问题，新增检查崩溃数是否大于3，大于3则需要提单
          if (d.platform === SlardarPlatformType.iOS && issue_level_reason === '非新问题,不自动开单') {
            if (d.count < 3) {
              bill_of_lading.push(d);
            }
          }

          this.logger.info(`oyq ppe 777 with ${timeStamp} ${JSON.stringify(d)} ${issue_level} ${issue_level_reason}`);
          d.issue_level = issue_level as number;
          if (platform === SlardarPlatformType.iOS) {
            d.issue_level_reason = issue_level_reason as string;
          }
          d.issue_level_time = Date.now();
          const slardarUrl = getSlardarUrl(
            d.aid,
            CrashType2Url[d.crash_type],
            d.platform,
            CrashType.JavaMemLeak === d.crash_type
              ? `perf_v2/memory/detail/activity/${d.issue_id}`
              : `abnormal/detail/${CrashType2Url[d.crash_type]}/${d.issue_id}`,
          );
          // console.info(
          // 	"autoLevel: ",
          // 	`aid:${d.aid} platform: ${d.platform}, issue_level: ${d.issue_level}, issue_ranking: ${d.ranking}, issue_version: ${d.version}, issue_version_code: ${d.version_code} issue_type: ${d.crash_type}, issue_url: ${slardarUrl}`
          // );

          if (
            (await this.config.autoConsumeAid()).includes(d.aid) &&
            d.issue_level <= 2 &&
            current_region() !== Region.LOCAL
          ) {
            if (
              [CrashType.JavaStartCrash, CrashType.JavaCrash, CrashType.NativeCrash].includes(d.crash_type) || // Android P0/P1/P2 Crash自动分配
              (d.issue_level <= 1 && [CrashType.ANR].includes(d.crash_type)) || // Android P0/P1 ANR自动分配
              (d.issue_level <= 1 &&
                [CrashType.Crash, CrashType.WatchDog, CrashType.CpuMeTriket].includes(d.crash_type)) // iOS P0/P1 Crash/WatchDog自动分配
            ) {
              consume_promise.push(
                this.autoIssueConsume(d.aid, d.issue_id, d.status, d.platform, d.crash_type, issues, {
                  version: d.version,
                  version_code: d.version_code,
                }),
              );
            }
          }
          d.is_warning = d.issue_level <= 1;
          this.logger.info(`oyq ppe 888 with ${timeStamp} push ${JSON.stringify(d)}`);
          cur.push(d);
          if (++processed % 100 === 0) {
            this.logger.info(`Leveled ${processed} of ${total} issues`);
          }
        }
        this.logger.info(`保存 ${v.version_code} 共 ${cur.length} 条`);
        // 同步保存后同步建单，防止重复提单
        await Promise.all(consume_promise);
        // 版本聚合Issues仅参与定级与路由，不单独开单 // Android的灰度提单也不在这里处理了
        this.logger.info(`oyq ppe 999 with ${timeStamp} save ${JSON.stringify(cur)}`);
        await slardarCrashIssueListModel.save(cur, true);
        if (platform !== SlardarPlatformType.Android && !isAggregated && current_region() !== Region.LOCAL) {
          // if (aid === HYPIC_SLARDAR_APP_ID()) {
          //   this.logger.info('醒图暂时不进行自动提单');
          // }
          // 去掉不需要提单的issue
          const filter_list = grouped_list[v.version_code].filter(item => !bill_of_lading.includes(item));
          await this.autoCreateBugProcessor(filter_list, true, true);
        }
      }
    }
    return list;
  }

  async autoOfflineLevelProcessor(
    aid: number,
    list: DBSlardarCrashIssueList[],
    issues: DBSlardarCrashIssue[],
    platform?: SlardarPlatformType | undefined,
    isAggregated = false,
  ): Promise<DBSlardarCrashIssueList[]> {
    const date = new Date();
    const timeStamp = date.getTime();
    this.logger.info(`oyq ppe autoLevelProcessor start with ${timeStamp}`);

    const slardarCrashIssueListModel = useInject(SlardarCrashIssueListModel);
    const grouped_list = groupBy(list, it => it.version);
    const cleanedVersions = Object.keys(grouped_list).map(key => key.replace(/-offline/g, ''));
    // 传入去掉 '-offline' 后的版本数组
    // find all versions
    // const versionsList = (await this.alarmVersionModel.findVersionsByVersion(aid, cleanedVersions, platform))
    //   .sort((a, b) => a.timestamp - b.timestamp)
    //   .map(v => ({ version: v.version }));
    // const uniqueVersions = Array.from(new Map(versionsList.map(item => [item.version, item])).values());
    const versions = cleanedVersions;
    if ('' in grouped_list) {
      versions.push('');
    }
    const AndroidProcessor = new AndroidLevelProcessor(list, issues);
    const iOSProcessor = new iOSLevelProcessor(list, issues);
    const RetouchAndroidProcessor = new RetouchAndroidLevelProcessorV1(list, issues);
    const RetouchIOSProcessor = new RetouchIOSLevelProcessorV1(list, issues);
    // 缓存对比所需参数，减少IO
    await iOSProcessor.buildParams(aid);
    await AndroidProcessor.buildParams(aid);
    await RetouchAndroidProcessor.buildParams(aid);
    await RetouchIOSProcessor.buildParams(aid);
    let processed = 0;
    const total = list.length;
    const getProcessor = (_aid: number, _platform: SlardarPlatformType) => {
      switch (_aid) {
        case HYPIC_SLARDAR_APP_ID():
          return _platform === SlardarPlatformType.Android ? RetouchAndroidProcessor : RetouchIOSProcessor;
        case SLARDAR_APP_ID():
        default:
          return _platform === SlardarPlatformType.Android ? AndroidProcessor : iOSProcessor;
      }
    };

    this.logger.info(`oyq ppe 555 with ${timeStamp} ${JSON.stringify(versions)} ${JSON.stringify(grouped_list)}`);
    for (const v of versions) {
      const bill_of_lading: DBSlardarCrashIssueList[] = [];
      const cur: DBSlardarCrashIssueList[] = [];
      const versionWithOffline = `${v}-offline`;
      if (versionWithOffline in grouped_list) {
        const consume_promise: Promise<void>[] = [];
        for (const d of grouped_list[versionWithOffline]) {
          // const originIssueLevel = d.issue_level;
          if (d.version && d.version.includes('-offline')) {
            d.version = d.version.replace(/-offline/g, '');
          }
          if (platform && d.platform !== platform) {
            // 如果指定了Platform，platform 不符合不进行定级
            continue;
          }
          this.logger.info(`oyq ppe 666 with ${timeStamp} ${JSON.stringify(d)} ${isAggregated}`);
          const [issue_level, issue_level_reason] = await getProcessor(d.aid, d.platform).calculateLevel(
            d,
            isAggregated,
          );

          // ios 检查是否是历史问题，新增检查崩溃数是否大于3，大于3则需要提单
          if (d.platform === SlardarPlatformType.iOS && issue_level_reason === '非新问题,不自动开单') {
            if (d.count < 3) {
              bill_of_lading.push(d);
            }
          }

          this.logger.info(`oyq ppe 777 with ${timeStamp} ${JSON.stringify(d)} ${issue_level} ${issue_level_reason}`);
          d.issue_level = issue_level as number;
          if (platform === SlardarPlatformType.iOS) {
            d.issue_level_reason = issue_level_reason as string;
          }
          d.issue_level_time = Date.now();
          const slardarUrl = getSlardarUrl(
            d.aid,
            CrashType2Url[d.crash_type],
            d.platform,
            CrashType.JavaMemLeak === d.crash_type
              ? `perf_v2/memory/detail/activity/${d.issue_id}`
              : `abnormal/detail/${CrashType2Url[d.crash_type]}/${d.issue_id}`,
          );
          // console.info(
          // 	"autoLevel: ",
          // 	`aid:${d.aid} platform: ${d.platform}, issue_level: ${d.issue_level}, issue_ranking: ${d.ranking}, issue_version: ${d.version}, issue_version_code: ${d.version_code} issue_type: ${d.crash_type}, issue_url: ${slardarUrl}`
          // );

          if (
            (await this.config.autoConsumeAid()).includes(d.aid) &&
            d.issue_level <= 2 &&
            current_region() !== Region.LOCAL
          ) {
            if (
              [CrashType.JavaStartCrash, CrashType.JavaCrash, CrashType.NativeCrash].includes(d.crash_type) || // Android P0/P1/P2 Crash自动分配
              (d.issue_level <= 1 && [CrashType.ANR].includes(d.crash_type)) || // Android P0/P1 ANR自动分配
              (d.issue_level <= 1 &&
                [CrashType.Crash, CrashType.WatchDog, CrashType.CpuMeTriket].includes(d.crash_type)) // iOS P0/P1 Crash/WatchDog自动分配
            ) {
              consume_promise.push(
                this.autoIssueConsume(d.aid, d.issue_id, d.status, d.platform, d.crash_type, issues, {
                  version: d.version,
                  version_code: d.version_code,
                }),
              );
            }
          }
          d.is_warning = d.issue_level <= 1;
          d.version = `${d.version}-offline`;
          this.logger.info(`oyq ppe 888 with ${timeStamp} push ${JSON.stringify(d)}`);
          cur.push(d);
          if (++processed % 100 === 0) {
            this.logger.info(`Leveled ${processed} of ${total} issues`);
          }
        }
        this.logger.info(`保存 ${v} 共 ${cur.length} 条`);
        // 同步保存后同步建单，防止重复提单
        await Promise.all(consume_promise);
        // 版本聚合Issues仅参与定级与路由，不单独开单 // Android的灰度提单也不在这里处理了
        this.logger.info(`oyq ppe 999 with ${timeStamp} save ${JSON.stringify(cur)}`);
        await slardarCrashIssueListModel.save(cur, true);
        if (platform !== SlardarPlatformType.Android && !isAggregated && current_region() !== Region.LOCAL) {
          if (aid === HYPIC_SLARDAR_APP_ID()) {
            this.logger.info('醒图暂时不进行自动提单');
          }
          // 去掉不需要提单的issue
          const filter_list = grouped_list[v].filter(item => !bill_of_lading.includes(item));
          await this.autoCreateBugProcessor(filter_list, true, true);
        }
      }
    }
    return list;
  }

  async autoCreateBugProcessor(
    list: DBSlardarCrashIssueList[],
    androidCreatable: boolean,
    iosCreateable: boolean,
    creator?: string,
  ) {
    const meegoService = useInject(MeegoService);
    const versionService = useInject(AlarmVersionModel);
    const airplaneConfigService = useInject(AirplaneConfigService);
    const slardarCrashIssueListModel = useInject(SlardarCrashIssueListModel);
    // 获取72小时内的Android 版本
    const android72HVersions = (await versionService.findVersionsIn(72 * 60, []))
      .filter(value => value.platform === SlardarPlatformType.Android)
      .map(value => value.version_code);

    for (const d of list) {
      // 获取issue黑名单
      const blackList: string[] = await airplaneConfigService.queryConfigItemByName(
        VersionConfigKeys.autoLevelBlackList,
        d.platform,
      );
      // console.log(`在处理${d.issue_id}`);
      if (
        d.issue_level > 1 ||
        !(await this.config.autoCreateCrashType()).includes(d.crash_type) ||
        blackList.includes(d.issue_id)
      ) {
        continue;
      }
      this.logger.info(
        `[autoCreateBugProcessor] 触发自动建单: ${d.aid}-${d.crash_type}-${d.issue_id}-${d.version_code}`,
      );
      if (!(await this.config.autoCreateAid()).includes(d.aid)) {
        continue;
      }
      const cur_version = d.version ? d.version : versionCodeToMeegoVersion(d.version_code);
      const versionType = AndroidTypeOf(d.version_code);
      // console.log(`当前优先级P${d.issue_level}`);
      // 找到所有issue_id相同中issue_level最小的issue_level,比较
      let cur_level = 3;
      let issue_link = getSlardarIssueLink(d.aid, d.crash_type, d.platform, d.issue_id, d.version_code);
      if (d.issue_level_reason) {
        issue_link = `${issue_link}【提单原因】${d.issue_level_reason}`;
      }
      const issueInfo = await this.slardarCrashIssueModel.getIssueInfoById(d.aid, d.issue_id, d.platform);
      const startVersion = await this.getStartVersion(d);
      const curMinVersionCode = this.getCurMinVersionCode(d.version_code);
      // console.log(`提单负责人${assignee}`);
      const pattern = /detail\/(\d+)/;
      let BMVersion = cur_version;
      if (current_region() === Region.SG) {
        const [major, minor, patch] = BMVersion.split('.').map(Number);
        BMVersion = `${major + 2}.${minor}.${patch}`;
      }
      if (issueInfo?.meego_map && issueInfo?.meego_map[cur_version]) {
        const temp = await meegoService.getIssuePriorityByUrl(issueInfo.meego_map[cur_version]);
        cur_level = temp.cur_level;
        const match = issueInfo.meego_map[cur_version].match(pattern);
        const meegoId = match ? match[1] : 0;
        const prev_level = d.version
          ? await slardarCrashIssueListModel.findPrevIssueListLevelByVersion(
              d.aid,
              d.issue_id,
              d.version,
              d.platform,
              d.crash_type,
            )
          : await slardarCrashIssueListModel.findPrevIssueListLevel(
              d.aid,
              d.issue_id,
              d.version_code,
              d.platform,
              d.crash_type,
            );
        if (prev_level !== undefined && prev_level > d.issue_level && d.issue_level < cur_level && cur_level <= 2) {
          // 相比上次纸飞机定级有升级，并且meego单定级低于当前定级
          this.logger.info(
            `upgradeIssue ${d.issue_id} prev_level:${prev_level} cur_level:${cur_level} d.issue_level:${d.issue_level}`,
          );
          const res = await meegoService.updateIssue(Number(meegoId), d.issue_level, BMVersion, d.platform);
          this.logger.info(`upgradeIssue res:${JSON.stringify(res)}`);
          /* // TODO:触发升级逻辑，提醒BM留意
                    try {
                        await this.sendUpdateWarnCard(
                            d.version_code,
                            BMVersion,
                            d.platform,
                            meegoId.toString(),
                            d.issue_level,
                            prev_level,
                            cur_level,
                            issue_link
                        );
                    } catch (e) {
                        await larkService.quickLog(JSON.stringify(e));
                    } */
        }
      } else if (
        ((d.issue_level < 2 || (startVersion && Number(startVersion) >= curMinVersionCode)) && // Android 新增问题都会提单
          (!issueInfo?.meego_map || !issueInfo.meego_map[cur_version]) && // 无提单历史
          android72HVersions.includes(d.version_code) && // Android 仅72小时内的灰度版本自动提单
          d.platform === SlardarPlatformType.Android &&
          versionType === VersionType.GRAY &&
          androidCreatable) ||
        (d.issue_level <= 1 && d.platform === SlardarPlatformType.iOS && iosCreateable)
      ) {
        if (d.crash_type === CrashType.NativeCrash && d.issue_level === 1) {
          // FIXME: @guozhi Native P1暂时不开单
          continue;
        }
        this.logger.info(
          `[autoCreateBugProcessor] 新提meego单: ${d.aid}-${d.crash_type}-${d.issue_id}-${d.version_code}`,
        );
        if (await this.config.muteAutoCreateMeego()) {
          continue;
        }
        const { crashType } = parseSlardarLink(issue_link);
        const title = `[${cur_version}-${
          crashType ? crashType2Title[crashType] : 'undefined'
        }]-${issueInfo?.crash_file}(${issueInfo?.crash_clazz})`;
        // console.log(`自动提单，提单标题${title}`);
        let chatId: string | undefined;
        if (crashType) {
          chatId = await this.joinSlardarChatAndInviteMeegoBot(
            d.aid,
            creator
              ? creator
              : (await useInject(MeegoRawService).MeegoConsume(title, cur_version, d.platform, d.crash_type)).reporter,
            d.issue_id,
            crashType,
            d.platform,
            title,
          );
        }
        const res = await meegoService.createSlardarIssue(
          title,
          d.platform,
          cur_version,
          issue_link,
          d.issue_level.toString(),
          issueInfo?.managers[0],
          this.getDiscoverStage(d.aid, d.platform, d.version_code),
          creator,
          chatId,
          '5',
          true,
        );
        // console.log(`提单结果`, res);
        if (res.data) {
          // console.log("开始保存提单结果");
          await this.slardarCrashIssueModel.updateSlardarMeegoMap(
            d.aid,
            d.issue_id,
            `https://meego.feishu.cn/faceu/issue/detail/${res.data}`,
            cur_version,
          );
          // console.log("兜底检查是否存在历史提单，并更新历史提单");
          const temp = await meegoService.getIssuePriorityByUrl(
            `https://meego.feishu.cn/faceu/issue/detail/${res.data}`,
          );
          cur_level = temp.cur_level;
          if (cur_level !== d.issue_level) {
            await meegoService.updateIssue(
              res.data,
              cur_level < d.issue_level ? cur_level : d.issue_level,
              BMVersion,
              d.platform,
            );
          }
        }
      }
    }
  }

  getDiscoverStage(aid: number, platform: SlardarPlatformType, version_code: string) {
    let versionType = VersionType.UNKNOWN;
    if (platform === SlardarPlatformType.Android) {
      versionType = AndroidTypeOf(version_code);
    } else {
      versionType = IosTypeOf(version_code, aid === 1775);
    }
    switch (versionType) {
      case VersionType.GRAY:
        return 'option_12'; // 灰度阶段
      case VersionType.SINGLE_GRAY:
        return 'option_12';
      case VersionType.UNKNOWN:
        return 'option_12';
      case VersionType.TF_GRAY:
        return 'option_12';
      case VersionType.SMALL_TF:
        return 'option_12';
      case VersionType.SMALL:
        return 'jk5c1n7xz'; // 小流量阶段
      case VersionType.FULL_TF:
        return 'option_11'; // 上线前阶段
      case VersionType.FULL:
        return 'option_11';
      case VersionType.ONLINE:
        return 'option_11'; // 线上阶段
      case VersionType.MAJOR_VERSION:
        return 'option_11';
      default:
        return 'option_12';
    }
  }

  // / 获取筛选过后的最后一个 Event 信息
  async getLastEventFilteredByJobInfo(
    aid: number,
    start_time: number,
    end_time: number,
    crash_type: string,
    issue_id: string,
    platform: string,
    is_oversea: boolean,
  ) {
    console.log('zsy startTime', start_time);
    console.log('zsy endTime', end_time);
    let page_no = 1;
    let page_size = 100;

    const resp = await this.getIssueEventListFilteredByJobInfo(
      aid,
      crash_type,
      issue_id,
      platform,
      is_oversea,
      start_time,
      end_time,
      page_no,
      page_size,
    );

    if (resp.data === null && resp.errmsg !== 'success') {
      return { errmsg: resp.errmsg, data: null };
    }
    console.log('zsy getLastEventFilteredByJobInfo resp', resp);

    const result = resp.data;
    if (result.total && result.total > 100) {
      page_size = result.total % 100;
      page_no = Math.floor(result.total / 100) * 100 + 1;
      if (page_size === 0) {
        page_no = page_no - 100;
        page_size = 100;
      }
      const lastResp = await this.getIssueEventListFilteredByJobInfo(
        aid,
        crash_type,
        issue_id,
        platform,
        is_oversea,
        start_time,
        end_time,
        page_no,
        page_size,
      );
      if (lastResp.data === null && lastResp.errmsg !== 'success') {
        return { errmsg: lastResp.errmsg, data: null };
      }
      return { errmsg: 'success', data: lastResp.data.result?.pop() };
    }
    return { errmsg: 'success', data: result.result?.pop() };
  }

  // / 根据 jobInfo 来获取对应的 event 列表
  async getIssueEventListFilteredByJobInfo(
    aid: number,
    crash_type: string,
    issue_id: string,
    platform: string,
    is_oversea: boolean,
    start_time: number,
    end_time: number,
    page_no: number,
    page_size: number,
  ) {
    const region = is_oversea ? 'us' : 'cn';
    const lang = is_oversea ? 'en' : 'zh';
    const granularity = 3600;
    const fc: Condition = {
      type: 'and',
      sub_conditions: [
        {
          dimension: 'filters_map',
          groupKey: 'custom tag',
          map_key: 'lv_job_info',
          op: 'regex',
          value: '^.+$',
          type: 'map',
        },
      ],
      // 正则有问题  ^[\s\S].[^\s][\s\S]*$
    };
    const resp = await genericRequest<IssueEventListResult>('/api_v2/app/crash/event/list', 'POST', {
      aid,
      crash_time_type: 'insert_time',
      os: platform,
      crash_type,
      start_time,
      end_time,
      pgno: page_no,
      pgsz: page_size,
      lang,
      region,
      issue_id,
      granularity: Number(granularity),
      filters_conditions: fc,
      token_type: 0,
      token: '',
    });
    await this.quotaCountIncr(aid, `crash:${platform}:${crash_type}:issue_event_list_by_job`);
    return resp;
  }

  async getIssueEventList(req: GetCrashEventListRequest) {
    const resp = await genericRequest<IssueEventListResult>('/api_v2/app/crash/event/list', 'POST', req);
    await this.quotaCountIncr(req.aid, `crash:${req.os}:${req.crash_type}:issue_event_list`);
    return resp.data;
  }

  // / 查询 event 的详情
  async queryEventDetail(crash_type: string, platform: string, is_oversea: boolean, event: IssueEventListItem) {
    const region = is_oversea ? 'us' : 'cn';
    const lang = is_oversea ? 'en' : 'zh';

    const params = {
      aid: event.aid,
      issue_id: event.issue_id,
      event_id: event.event_id,
      crash_time: event.crash_time,
      device_id: event.device_id,
      os: platform,
      crash_type,
      lang,
      region,
    };

    const params_str = commonUtils.toUrlParamsRecord(params).toString();
    const resp = await genericRequest<IssueEventDetailResult>(`/api_v2/app/crash/event/log/get?${params_str}`, 'GET');
    await this.quotaCountIncr(Number(event.aid), `crash:${platform}:${crash_type}:event_email`);
    return resp.data;
  }

  async autoIssueConsume(
    aid: number,
    issue_id: string,
    issue_status: string,
    platform: SlardarPlatformType,
    crash_type: CrashType,
    issues: DBSlardarCrashIssue[],
    extraArgs: any,
  ) {
    try {
      const issue_info =
        issues.find(d => d.issue_id === issue_id) ||
        (await this.slardarCrashIssueModel.getIssueInfoById(aid, issue_id, platform));
      // 状态为重新打开和新创建才触发分配
      if (
        issue_info &&
        issue_info.managers.length <= 0 &&
        ['new_created', 'be_processed_again'].includes(issue_status)
      ) {
        await this.IssueConsume(aid, issue_id, platform, crash_type, extraArgs);
      }
    } catch (e) {
      this.logger.error(
        `Walle${current_region() === Region.SG ? '-us' : ''}-自动分配触发失败, IssueId:${issue_id} platform:${platform} crash_type:${crash_type} 失败原因：${e})`,
      );
    }
  }

  async IssueConsume(
    aid: number,
    issue_id: string,
    platform: SlardarPlatformType,
    crash_type: CrashType,
    extraArgs: any = null,
    returnAssigneesWhenConsumed = true,
    notCreateBugAfterConsumed = false,
    notJudgeLevelAfterConsumed = true,
    isForceUpdateSlardarIfDBExisted = false,
  ) {
    const res = await useInject(WalleService).issueConsume(
      aid,
      issue_id,
      platform,
      CrashType2Url[crash_type],
      returnAssigneesWhenConsumed,
      notCreateBugAfterConsumed,
      notJudgeLevelAfterConsumed,
      isForceUpdateSlardarIfDBExisted,
      extraArgs,
    );
    if (res.ret === 0) {
      if (res.data.all_assignees.length > 0) {
        await this.slardarCrashIssueModel.updateManagers(aid, issue_id, platform, res.data.all_assignees);
      }
      // 测试使用
      // await useInject(LarkService).quickLog(
      // 	`Walle${current_region() === Region.SG ? "-us" : ""}自动分配成功\n` +
      // 		`IssueId:${issue_id} platform:${platform} crash_type:${crash_type}` +
      // 		`分配人：${res.data.all_assignees})`
      // );
      return res.data.all_assignees;
    } else {
      this.logger.error(
        `Walle${current_region() === Region.SG ? '-us' : ''}-自动分配触发失败, IssueId:${issue_id} platform:${platform} crash_type:${crash_type} 失败原因：${res.error_msg}(ret=${res.ret})`,
      );
      return undefined;
    }
  }

  async IssueConsumeWeb(aid: number, issue_id: string, platform: SlardarPlatformType, link_crash_type: string) {
    const res = await useInject(WalleService).issueConsume(aid, issue_id, platform, link_crash_type, true, false);
    if (res.ret === 0) {
      if (res.data.all_assignees.length > 0) {
        await this.slardarCrashIssueModel.updateManagers(aid, issue_id, platform, res.data.all_assignees);
      }
      // 测试使用
      // await useInject(LarkService).quickLog(
      // 	`Walle${current_region() === Region.SG ? "-us" : ""}自动分配成功\n` +
      // 		`IssueId:${issue_id} platform:${platform} crash_type:${link_crash_type}` +
      // 		`分配人：${res.data.all_assignees})`
      // );
      return res.data.all_assignees;
    } else {
      this.logger.error(
        `Walle${current_region() === Region.SG ? '-us' : ''}-自动分配触发失败, IssueId:${issue_id} platform:${platform} crash_type:${link_crash_type} 失败原因：${res.error_msg}(ret=${res.ret}))`,
      );
      return undefined;
    }
  }

  async autoAssignGet(os: SlardarPlatformType, aid: number = SLARDAR_APP_ID()) {
    const ret = await genericRequest<Config>('/api_v2/app/crash/issue/auto_assign/get', 'POST', {
      aid,
      os,
      region: current_region() === Region.SG ? 'maliva' : 'cn',
      sdk: false,
      type: 'app',
    });
    return ret.data;
  }

  async autoAssignUpdate(os: SlardarPlatformType, config: Config, aid: number = SLARDAR_APP_ID()) {
    return await genericRequest<any>('/api_v2/app/crash/issue/auto_assign/update', 'POST', {
      aid,
      os,
      region: current_region() === Region.SG ? 'maliva' : 'cn',
      sdk: false,
      type: 'app',
      config,
    } as AutoAssignment);
  }

  async quotaUsageExpire() {
    const today = dayjs().utcOffset(utcOffset());
    const today_str = today.format('YYYYMMDD');
    const expireAid = async (aid: number) => {
      const key = `slardar:quota:${aid}:${today_str}`;
      const ttl = await this.redis.ttl(key);
      if (ttl === -1) {
        await this.redis.expire(key, 86400 * 3);
      }
    };
    await expireAid(SLARDAR_APP_ID());
    await expireAid(HYPIC_SLARDAR_APP_ID());
  }

  async saveQueryConfig(os: SlardarPlatformType, query_config: string) {
    return genericRequest<string>('/api_v2/app/flexible_consumption/v1/save_query_config', 'POST', {
      aid: SLARDAR_APP_ID(),
      os,
      region: current_region() === Region.SG ? 'maliva' : 'cn',
      sdk: false,
      query_config,
    } as any);
  }

  async GetSymboLicateAddressList(
    aid: number,
    platform: SlardarPlatformType,
    issueId: string,
    startTime: number,
    endTime: number,
    pageNo: number,
    pageSize: number,
    crash_type: CrashType,
    sub_conditions?: Condition[],
  ): Promise<GetSymboLicateAddressListResult> {
    const data = await this.getCrashEventListRequest(
      aid,
      platform,
      issueId,
      startTime,
      endTime,
      pageNo,
      pageSize,
      crash_type,
      sub_conditions,
    );
    const result = data?.result?.at(0);
    if (result) {
      const rsp = await this.queryEventDetail(crash_type, platform, current_region() === Region.SG, result);
      this.logger.info(`queryEventDetail rsp:${JSON.stringify(rsp)}`);
      const params = {
        lang: 'zh',
        region: current_region() === Region.SG ? 'maliva' : 'cn',
        aid,
        os: platform,
        issue_id: issueId,
        sdk: false,
        crash_time: result.crash_time,
        device_id: result.device_id,
        event_id: result.event_id,
        log_type: 'custom_address_analysis',
        crash_type,
      };
      const params_str = commonUtils.toUrlParamsRecord(params).toString();
      this.logger.info(`GetSymboLicateAddressList params:${params_str}`);
      await this.quotaCountIncr(aid, `symbo:${platform}:SymboLicate:address`);
      let response: SlardarResponse<SymboLicateAddressList>;
      try {
        response = await genericRequest<SymboLicateAddressList>(
          `/api_v2/app/crash/event/log/symbolicate_address_list?${params_str}`,
          'GET',
        );
        this.logger.info(`GetSymboLicateAddressList response:${JSON.stringify(response)}`);
      } catch (e) {
        this.logger.error(`GetSymboLicateAddressList error:${e}`);
        return {
          symbol: undefined,
          version: result.update_version_code,
          leaks_cycle_key_class: rsp?.custom?.leaks_cycle_key_class,
          leaks_retain_cycle: rsp?.custom?.leaks_retain_cycle,
        };
      }

      if (response?.errno === 200) {
        return {
          symbol: response.data?.results,
          version: result.update_version_code,
          leaks_cycle_key_class: rsp?.custom?.leaks_cycle_key_class,
          leaks_retain_cycle: rsp?.custom?.leaks_retain_cycle,
        };
      } else {
        return {
          symbol: undefined,
          version: result.update_version_code,
          leaks_cycle_key_class: rsp?.custom?.leaks_cycle_key_class,
          leaks_retain_cycle: rsp?.custom?.leaks_retain_cycle,
        };
      }
    }
    return { symbol: undefined, version: undefined, leaks_cycle_key_class: undefined, leaks_retain_cycle: undefined };
  }

  async joinSlardarChatAndInviteMeegoBot(
    aid: number,
    operator: string,
    issue_id: string,
    crashType: string,
    platform: SlardarPlatformType,
    title: string,
    id?: number,
  ) {
    const chatId = await this.JoinIssueChat(
      aid,
      platform,
      issue_id,
      crashType,
      title,
      operator.substring(0, operator.indexOf('@')),
      id,
    );
    this.logger.info(`${issue_id}加群结果${JSON.stringify(chatId)}`);
    if (chatId?.lark_open_id) {
      try {
        const res = await useInject(LarkService).inviteBotToChat(chatId.lark_open_id, 'cli_9b49ba549431510c');
        if (res.code !== 0) {
          this.logger.info(`拉取机器人失败${res.msg}`);
        }
      } catch (e) {
        this.logger.info(`拉群失败${e}`);
      }
    }
    this.logger.info(`${issue_id}加群结束,chatId:${chatId}`);
    return chatId.lark_chat_id;
  }

  async joinSlardarOpenChatAndInviteMeegoBot(
    aid: number,
    operator: string,
    issue_id: string,
    crashType: string,
    platform: SlardarPlatformType,
    title: string,
    id?: number,
  ) {
    const chatId = await this.JoinIssueChat(
      aid,
      platform,
      issue_id,
      crashType,
      title,
      operator.substring(0, operator.indexOf('@')),
      id,
    );
    this.logger.info(`${issue_id}加群结果${chatId}`);
    if (chatId?.lark_open_id) {
      try {
        const res = await useInject(LarkService).inviteBotToChat(chatId.lark_open_id, 'cli_9b49ba549431510c');
        if (res.code !== 0) {
          this.logger.info(`拉取机器人失败${res.msg}`);
        }
        return { code: NetworkCode.Success, chatId: chatId.lark_open_id };
      } catch (e) {
        this.logger.info(`拉群失败${e}`);
      }
    } else {
      return { code: NetworkCode.Error };
    }
  }

  async getCrashEventListRequest(
    aid: number,
    platform: SlardarPlatformType,
    issueId: string,
    startTime: number,
    endTime: number,
    pageNo: number,
    pageSize: number,
    crash_type = 'oom_crash',
    sub_conditions?: Condition[],
  ): Promise<IssueEventListResult | null> {
    const body = {
      aid,
      os: platform,
      lang: 'zh',
      region: current_region() === Region.SG ? 'maliva' : 'cn',
      start_time: startTime,
      end_time: endTime,
      crash_type,
      issue_id: issueId,
      sub_issue_id: '',
      filters_conditions: {
        type: 'and',
        sub_conditions: sub_conditions ?? [],
      },
      versions_conditions: {},
      granularity: 3600,
      token: '',
      token_type: 0,
      event_ids: [],
      crash_time_type: 'insert_time',
      pgsz: pageSize,
      pgno: pageNo,
      sdk: false,
    };

    const response = await genericRequest<IssueEventListResult>(`/api_v2/app/crash/event/list`, 'POST', body);

    await this.quotaCountIncr(aid, `metrics:${platform}:MemoryGraph:list`);
    if (response.errno === 200) {
      return response.data;
    } else {
      return null;
    }
  }

  async getIssueDetail(
    aid: number,
    os: string,
    issueId: string,
    startTime: number,
    endTime: number,
  ): Promise<GetIssueDetailResponseData | null> {
    const response = await genericRequest<GetIssueDetailResponseData>(`/api_v2/app/crash/issue/detail`, 'POST', {
      aid,
      os,
      lang: 'zh',
      region: current_region() === Region.SG ? 'maliva' : 'cn',
      sdk: false,
      start_time: startTime,
      end_time: endTime,
      crash_type: 'oom_crash',
      issue_id: issueId,
      sub_issue_id: '',
      filters_conditions: {
        type: 'and',
        sub_conditions: [
          {
            dimension: 'is_memory_graph',
            op: 'eq',
            value: 'true',
            type: 'expression',
          },
        ],
      },
      versions_conditions: {},
      granularity: 3600,
      token: '',
      token_type: 0,
      event_ids: [],
      crash_time_type: 'insert_time',
    });

    await this.quotaCountIncr(aid, `metrics:${os}:MemoryGraph:detail`);
    if (response.errno === 200) {
      return response.data;
    } else {
      return null;
    }
  }

  async getNodeGroupList(aid: number, platform: SlardarPlatformType, tosKey: string) {
    const body: GetNodeGroupListRequest = {
      aid,
      is_abandoned: false,
      is_grouped_with_size: true,
      keyword: '',
      lang: 'zh',
      os: platform,
      sdk: false,
      tos_key: tosKey,
      region: current_region() === Region.SG ? 'maliva' : 'cn',
    };
    const response = await genericRequest<GroupItem[]>(`/api_v2/app/memory_graph/get_node_group_list`, 'POST', body);
    await this.quotaCountIncr(aid, `memory:${platform}:MemoryGraph:memorygraph:node:group:list`);
    if (response.errno === 200) {
      return response.data;
    } else {
      return null;
    }
  }

  async getMemoryGraphDetail(aid: number, platform: SlardarPlatformType, crashTime: number, eventId: string) {
    const params = {
      aid,
      lang: 'zh',
      os: platform,
      region: current_region() === Region.SG ? 'maliva' : 'cn',
      crash_time: crashTime,
      event_id: eventId,
    };

    const params_str = commonUtils.toUrlParamsRecord(params).toString();
    await this.quotaCountIncr(aid, `memory:${platform}:MemoryGraph:memorygraph:get:info`);
    const response = await genericRequest<MemoryGraphDetail>(
      `/api_v2/app/memory_graph/get_memory_graph_info?${params_str}`,
      'GET',
    );
    if (response.errno === 200) {
      return response.data;
    } else {
      return null;
    }
  }

  async sendMemoryGraphHeartbeat(aid: number, platform: SlardarPlatformType, tosKey: string) {
    const body = {
      aid,
      lang: 'zh',
      os: platform,
      region: current_region() === Region.SG ? 'maliva' : 'cn',
      sdk: false,
      tos_key: tosKey,
    };

    await this.quotaCountIncr(aid, `memory:${platform}:MemoryGraph:memorygraph:heartbeat`);
    const response = await genericRequest<number>(`/api_v2/app/memory_graph/send_heartbeat`, 'POST', body);
    if (response && response.errno === 200) {
      return response.data;
    } else {
      return -1;
    }
  }

  async getAlog(aid: number, platform: SlardarPlatformType, time: string, did: string, limit: number) {
    const body = {
      aid,
      device_id: did,
      limit,
      log_type: 'alog',
      os: platform,
      region: current_region() === Region.SG ? 'maliva' : 'cn',
      sdk: false,
      time,
    };
    const response = await genericRequest<string[]>(`/api_v2/app/log/get`, 'POST', body);
    await this.quotaCountIncr(aid, `app:${platform}:app:get:alog`);
    if (response.errno === 200) {
      return response.data;
    } else {
      return [];
    }
  }

  async getCrashAlarmDetail(aid: number, os: SlardarPlatformType, id: number) {
    const params = { aid, os: os.toString(), id };
    const params_str = commonUtils.toUrlParamsRecord(params).toString();
    const response = await genericRequest<RuleDisplayData>(`/api_v2/app/alerts/crash/detail2?${params_str}`, 'GET');
    await this.quotaCountIncr(aid, `app:${os}:app:detail:GetCrashAlarmDetail2`);
    this.logger.info(`GetCrashAlarmDetail2 response:${JSON.stringify(response)}`);
    return response;
  }

  async addCrashAlarm(rule: RuleDisplayData) {
    const body = {
      aid: rule.aid,
      os: rule.os,
      region: rule.region,
      rule,
    };
    const response = await genericRequest<string[]>(`/api_v2/app/alerts/crash/add2`, 'POST', body);
    await this.quotaCountIncr(rule.aid, `app:${rule.os}:app:alarm_crash:AddCrashAlarm2`);
    this.logger.info(`AddCrashAlarm2 response:${JSON.stringify(response)}`);
    return response;
  }

  async updateCrashAlarmStatus(aid: number, os: SlardarPlatformType, ruleId: number) {
    const body = {
      aid,
      os,
      rule_id: ruleId,
    };
    const response = await genericRequest<string[]>(`/api_v2/app/alarm_rule/update_status`, 'POST', body);
    await this.quotaCountIncr(aid, `app:${os}:app:list:UpdateAlarmRuleStatus`);
    this.logger.info(`UpdateAlarmRuleStatus response:${JSON.stringify(response)}`);
    return response;
  }

  private async quotaCountIncr(aid: number, key: string): Promise<number | undefined> {
    if (slardarPersonalToken()) {
      dayjs.extend(utc);
      const today = dayjs().utcOffset(utcOffset()).format('YYYYMMDD');
      return this.redis.hincrby(`slardar:quota:${aid}:${today}`, key, 1);
    }
    return undefined;
  }

  private async getStartVersion(d: DBSlardarCrashIssueList): Promise<string | undefined> {
    const start_version = await this.slardarCrashIssueModel.getStartVersionByIssueId(d.aid, d.issue_id, d.platform);
    return start_version ? correctVersionCode(start_version) : undefined;
  }

  private getCurMinVersionCode(version_code: string) {
    const cur_version = versionCodeToVersion(version_code);
    return Number(`${cur_version.replaceAll('.', '')}0000`);
  }
}
