import { Inject, Injectable } from '@gulux/gulux';
import { CrashListInfo, CrashName, CrashType } from '@shared/typings/slardar/crash/issueListSearch';
import {
  Card,
  CardActionElement,
  CardActionValue,
  CardButtonAction,
  CardButtonType,
  CardCallbackType,
  CardContentElement,
  CardElement,
  CardElementTag,
  CardElementTagV2,
  CardHeader,
  CardInfo,
  CardMarkdownElement,
  CardNoteElement,
  CardTemplate,
  CardTextTag,
  TitleTag,
} from '@pa/shared/dist/src/lark/larkCard';
import { LVProductType, MAIN_HOST_HTTPS, SlardarPlatformType } from '@pa/shared/dist/src/appSettings/appSettings';
import { current_region, Region } from '../utils/region';
import { AndroidCrash, iOSCrash } from '@shared/constants/slardar';
import { MeegoStatus2Name } from '@shared/typings/meego';
import { DBLibraInfo } from '../model/LibraInfoTable';
import { LibraPlatform, LibraRegion } from '@shared/libra/commonLibra';
import { useInject } from '@edenx/runtime/bff';
import LarkService from '@pa/backend/dist/src/third/lark';
import { getAppId } from '../utils/libraUtil';
import { genPaperSlardarUrl } from '../utils/slardarLink';
import { AppId2Name, ExperimentCard, IndicatorType, Type2CardTitle } from '@shared/libra/common';
import { DBExperimentShareInfo } from '../model/ExperimentShareInfoTable';
import { currentProduct, currentProductName } from '../utils/basic';
import { buildRecordUrl, getFlightEventName, LibraControlRecord } from '@shared/libra/libraControl';
import commonUtils from '@shared/utils/commonUtils';
import { add_suffix_ne } from '@shared/utils/tools';
import { HYPIC_SLARDAR_APP_ID, SLARDAR_APP_ID } from './slardar/shared';
import { assignUtils } from './assign/assignUtils';
import { MRAttrChangePoint, MRAttrInfo, MRAttrResultData, MRAttrVersionedInfo } from '@shared/mrProfiler/mrProfiler';
import Tools from '@shared/multiAttribution/Tools';
import {
  LibraCreateState,
  LibraLaunchNotifyCardInfo,
  LibraRemindDataReviewNotifyCardInfo,
  LibraTrafficChangeNotifyCardInfo,
} from '@shared/libra/LibraNotifyInfo';
import {
  libraFormatTimestamp,
  libraDetailUrl,
  libraCreateUrl,
  formatTimeDifference,
} from '@shared/libra/libraManageUtils';
import MeegoRawService from './third/meego';

@Injectable()
export default class LarkCardService {
  @Inject()
  private meego: MeegoRawService;
  buildHeader(title: string, template: CardTemplate): CardHeader {
    return {
      title: {
        content: title,
        tag: TitleTag.plain_text,
      },
      template,
    };
  }

  buildBaseCard(info: CardInfo): Card {
    return {
      config: {},
      header: this.buildHeader(info.title, info.template),
      elements: [],
    };
  }

  async SlardarCrashIssueWarnCard(
    crashIssueInfos: CrashListInfo[],
    platform: SlardarPlatformType,
    versionCode: string,
    version: string,
    bm: string,
    tips?: {
      [crashType: string]: boolean;
    },
    aid = 1775,
    userCount = 0,
    totalUserCount = 0,
  ): Promise<Card> {
    const larkService = useInject(LarkService);
    const isOversea = current_region() === Region.SG;
    const slardarUrl = genPaperSlardarUrl(isOversea, platform, version, versionCode);
    const getProductName = () => {
      if (aid === SLARDAR_APP_ID()) {
        return current_region() !== Region.SG ? '剪映' : 'Capcut';
      } else if (aid === HYPIC_SLARDAR_APP_ID()) {
        return current_region() !== Region.SG ? '醒图' : 'Hypic';
      }
    };
    const baseCard = this.buildBaseCard({
      title: `${getProductName()} ${version}-${platform}-${versionCode} 稳定性问题`,
      template: CardTemplate.yellow,
      config: { enable_forward: false },
    });
    const elements: CardElement[] = [];
    if (bm) {
      if (aid === SLARDAR_APP_ID()) {
        elements.push({
          tag: CardElementTag.markdown,
          content: `**请稳定性Owner高优跟进各方向指标情况，避免稳定性不达标造成版本延期**`,
        } as CardMarkdownElement);
      } else {
        elements.push({
          tag: CardElementTag.markdown,
          content: `BM需要推动严重问题的Owner第一优先级解决，避免版本Delay。<at email=${bm}></at>`,
        } as CardMarkdownElement);
      }
    }

    if (userCount > 0) {
      let userCountInfo = `${versionCode}放量: ${Tools.formatNumber(userCount)}`;
      if (totalUserCount !== 0) {
        userCountInfo += `\n${version}累计放量: ${Tools.formatNumber(totalUserCount)}`;
      }
      elements.push({
        tag: CardElementTag.markdown,
        content: userCountInfo,
      } as CardMarkdownElement);
      elements.push({ tag: CardElementTag.hr });
    }

    const IssueInfoOfKind: {
      [key: string]: CrashListInfo[];
    } = {};
    const ownerEmails: string[] = [];
    for (const info of crashIssueInfos) {
      if (info.meego_operator) {
        for (const operator of info.meego_operator) {
          if (!ownerEmails.includes(operator)) {
            ownerEmails.push(operator);
          }
        }
      }
      if (info.managers) {
        for (const managers of info.managers) {
          if (!ownerEmails.includes(managers)) {
            ownerEmails.push(`${managers}@bytedance.com`);
          }
        }
      }
      const type = info.crash_type;
      if (!IssueInfoOfKind[type]) {
        IssueInfoOfKind[type] = [];
      }
      IssueInfoOfKind[type].push(info);
    }
    const userInfos = await larkService.getUserInfoByEmails(ownerEmails);
    const userNameMap: {
      [email: string]: string;
    } = {};
    if (userInfos) {
      for (const userInfo of userInfos) {
        if (userInfo.email && userInfo.name) {
          userNameMap[userInfo.email] = userInfo.name;
        }
      }
    }
    for (const crashType of platform === SlardarPlatformType.Android ? AndroidCrash : iOSCrash) {
      if (
        [
          CrashType.JavaOOM,
          CrashType.NativeOOM,
          CrashType.Lag,
          CrashType.SeriousLag,
          CrashType.JavaMemLeak,
          CrashType.JavaSmallInstance,
          CrashType.NativeMemLeak,
        ].includes(crashType)
      ) {
        continue;
      }
      let index = 1;
      const crashBm = assignUtils.getCrashBM(crashType, aid)
        ? add_suffix_ne('@bytedance.com')(assignUtils.getCrashBM(crashType, aid))
        : '';
      let content = `**${CrashName[crashType]} ${crashBm ? `<at email=${crashBm}></at>` : ''}**\n`;
      if (!(crashType in IssueInfoOfKind)) {
        content += '当前无必解问题\n';
      } else {
        for (const issueInfo of IssueInfoOfKind[crashType]) {
          const isShow = !(
            (issueInfo.meego_status && ['RESOLVED', 'CLOSED', 'REJECT'].includes(issueInfo.meego_status)) ||
            ['close', 'done'].includes(issueInfo.status)
          );
          const crash_clazz = issueInfo.crash_clazz.replace(/\[/g, '&#91;').replace(/\]/g, '&#93;');
          if (!isShow) {
            content += '~~';
          }
          content += `${index}.【P${issueInfo.issue_level}】[${
            issueInfo.baseRanking ? `Top${issueInfo.baseRanking}` : 'New'
          }->Top${issueInfo.ranking}] [${issueInfo.crash_file ? issueInfo.crash_file : 'slardar链接'}${
            crashType === CrashType.NativeCrash && crash_clazz
              ? ` ${crash_clazz.length <= 25 ? crash_clazz : `${crash_clazz.slice(0, 22)}... `}`
              : ''
          }](${issueInfo.slardar_url})[owners:`;
          for (const owner of issueInfo.managers) {
            content += isShow ? `<at email=${owner}@bytedance.com></at>` : `@${userNameMap[`${owner}@bytedance.com`]}`;
          }
          if (issueInfo.managers) {
            content += ']';
          }
          if (issueInfo.meego_url && issueInfo.meego_status && issueInfo.meego_operator) {
            content += `[meego链接](${issueInfo.meego_url})-Meego状态:${
              MeegoStatus2Name[issueInfo.meego_status]
            }-meego优先级:P${issueInfo.meego_level}-meego处理人:${
              isShow ? `<at email=${issueInfo.meego_operator[0]}></at>` : `@${userNameMap[issueInfo.meego_operator[0]]}`
            }`;
          }
          if (!isShow) {
            content += '~~';
          }
          content += '\n';
          index += 1;
        }
      }
      if (tips && crashType in tips && tips[crashType]) {
        content += `**<font color="red">以上播报issue无法覆盖版本劣化量，请<at email=${crashBm ?? bm}></at>参与人工归因！</font>**`;
      }
      elements.push({
        tag: CardElementTag.div,
        text: {
          tag: CardTextTag.lark_md,
          content,
        },
      } as CardContentElement);
      elements.push({
        tag: CardElementTag.hr,
      } as CardContentElement);
    }
    elements.push({
      tag: CardElementTag.action,
      actions: [
        {
          tag: CardElementTag.button,
          url: slardarUrl,
          text: {
            tag: CardTextTag.plain_text,
            content: `纸飞机`,
          },
          type: CardButtonType.primary,
        } as CardButtonAction,
      ],
    } as CardActionElement);
    elements.push({
      tag: CardElementTag.div,
      text: {
        tag: CardTextTag.lark_md,
        content: '<font color=gray>有问题请在群里@版本Master，guozhi.kevin<font color=gray>',
      },
    } as CardContentElement);
    baseCard.elements = elements;
    return baseCard;
  }
  // 单条指标告警
  createMetricsAlarmSingleCard(
    title: string,
    mainContent: string,
    detailContent: string,
    actionTitle: string,
    data: any,
  ): Card {
    const metricsAlarmCard = this.buildBaseCard({
      title,
      template: CardTemplate.red,
      config: { enable_forward: true },
    });
    const elements: CardElement[] = [];
    if (mainContent) {
      elements.push({
        tag: CardElementTag.div,
        text: {
          tag: CardTextTag.lark_md,
          content: mainContent,
        },
      } as CardElement);
      elements.push({ tag: CardElementTag.hr });
    }
    elements.push({
      tag: CardElementTag.div,
      text: {
        tag: CardTextTag.lark_md,
        content: detailContent,
      },
    } as CardElement);
    elements.push({
      tag: CardElementTag.action,
      actions: [
        {
          tag: CardElementTag.button,
          text: {
            tag: CardTextTag.plain_text,
            content: actionTitle,
          },
          type: CardButtonType.primary,
          value: {
            cardCallbackType: CardCallbackType.MetricsAlarm,
            ...data,
          } as CardActionValue,
        } as CardButtonAction,
      ],
    } as CardActionElement);
    metricsAlarmCard.elements = elements;
    return metricsAlarmCard;
  }

  createLibraRemindDataReviewNotifyCard(info: LibraRemindDataReviewNotifyCardInfo): Card | undefined {
    const libraRegion = info.libraInfo.flightInfo.region;
    const libraId = info.libraInfo.flightInfo.id;
    const libraUrl = libraDetailUrl(libraRegion, libraId);
    const nowInMillisecond = Math.floor(new Date().getTime());
    const daysDiff = formatTimeDifference(info.libraInfo.flightInfo.startTime * 1000, nowInMillisecond); // 转化为毫秒
    const baseCard = this.buildBaseCard({
      title: `实验提醒：请及时回收实验数据`,
      template: CardTemplate.yellow,
      config: { enable_forward: true, update_multi: true },
    });
    const elements: CardElement[] = [];
    elements.push({
      tag: CardElementTag.div,
      text: {
        content: `实验：[${info.libraInfo.flightInfo.name}](${libraUrl})`,
        tag: CardTextTag.lark_md,
      },
    } as CardContentElement);
    // elements.push({
    //   tag: CardElementTag.div,
    //   text: {
    //     content: `需求：[${info.meegoName}](${info.meegoUrl})`,
    //     tag: CardTextTag.lark_md,
    //   },
    // } as CardContentElement);
    let baseUsers = '';
    const baseUsersCount = info.libraInfo.flightInfo.versions.length;
    for (let i = 0; i < baseUsersCount; i++) {
      const version = info.libraInfo.flightInfo.versions[i];
      if (i === baseUsersCount - 1) {
        baseUsers = `${baseUsers}v${i}: ${version.userNumber}`;
      } else {
        baseUsers = `${baseUsers}v${i}: ${version.userNumber}, `;
      }
    }
    elements.push({
      tag: CardElementTag.div,
      text: {
        content: `进组人数：${baseUsers}`,
        tag: CardTextTag.lark_md,
      },
    } as CardContentElement);
    let owners = '';
    for (const owner of info.libraInfo.flightInfo.owners) {
      owners = `${owners}<at email=${owner.email}></at>`;
    }
    elements.push({
      tag: CardElementTag.div,
      text: {
        content: `Owner：${owners}`,
        tag: CardTextTag.lark_md,
      },
    } as CardContentElement);
    elements.push({
      tag: CardElementTag.div,
      text: {
        content: `实验已开启 ${daysDiff}，请实验 Owner 及时关注每日数据`,
        tag: CardTextTag.lark_md,
      },
    } as CardContentElement);
    baseCard.elements = elements;
    return baseCard;
  }

  createNoLibraNotifyCard(
    appId: number,
    version: string,
    fullVersionDesc: string,
    versionStageName: string,
    meegoId: number,
    meegoName: string,
  ) {
    const baseCard = this.buildBaseCard({
      title: `实验提醒：请及时创建实验`,
      template: CardTemplate.yellow,
      config: { enable_forward: true, update_multi: true },
    });
    const meegoUrl = `https://meego.larkoffice.com/faceu/story/detail/${meegoId}`;
    const elements: CardElement[] = [];
    elements.push({
      tag: CardElementTag.div,
      text: {
        content: `**${fullVersionDesc} 已于 ${versionStageName} 开始灰度，需求：[${meegoName}](${meegoUrl})尚未创建实验，请及时创建实验。**`,
        tag: CardTextTag.lark_md,
      },
    } as CardContentElement);
    baseCard.elements = elements;
    return baseCard;
  }

  create100PercentGrayNotifyCard(info: LibraLaunchNotifyCardInfo): Card | undefined {
    const libraRegion = info.libraInfo.flightInfo.region;
    const libraId = info.libraInfo.flightInfo.id;
    const libraUrl = libraDetailUrl(libraRegion, libraId);
    const createLibraUrl = libraCreateUrl(libraRegion);
    const baseCard = this.buildBaseCard({
      title: `实验提醒：灰度实验请开启100%流量`,
      template: CardTemplate.yellow,
      config: { enable_forward: true, update_multi: true },
    });
    const elements: CardElement[] = [];
    elements.push({
      tag: CardElementTag.div,
      text: {
        // eslint-disable-next-line max-len
        content: `**${info.fullVersionDesc} 已于 ${libraFormatTimestamp(info.startTime)} 开始灰度，请实验 Owner 将流量开启至 100%。**（若已开启或该实验非灰度实验请忽略本消息）`,
        tag: CardTextTag.lark_md,
      },
    } as CardContentElement);
    elements.push({
      tag: CardElementTag.div,
      text: {
        content: `实验：[${info.libraInfo.flightInfo.name}](${libraUrl})`,
        tag: CardTextTag.lark_md,
      },
    } as CardContentElement);
    // elements.push({
    //   tag: CardElementTag.div,
    //   text: {
    //     content: `需求：[${info.meegoName}](${info.meegoUrl})`,
    //     tag: CardTextTag.lark_md,
    //   },
    // } as CardContentElement);
    let owners = '';
    for (const owner of info.libraInfo.flightInfo.owners) {
      owners = `${owners}<at email=${owner.email}></at>`;
    }
    elements.push({
      tag: CardElementTag.div,
      text: {
        content: `Owner：${owners}`,
        tag: CardTextTag.lark_md,
      },
    } as CardContentElement);
    elements.push({
      tag: CardElementTag.action,
      actions: [
        {
          tag: CardElementTag.button,
          text: {
            tag: CardTextTag.plain_text,
            content: '实验链接',
          },
          type: CardButtonType.primary,
          url: `${libraUrl}`,
        } as CardButtonAction,
        {
          tag: CardElementTag.button,
          text: {
            tag: CardTextTag.plain_text,
            content: '不再提醒',
          },
          type: CardButtonType.primary,
          value: {
            cardCallbackType: CardCallbackType.LibraLaunchGrayNotifyNotSendAnymore,
            ...(info as any),
          } as CardActionValue,
        } as CardButtonAction,
      ],
    } as CardActionElement);
    baseCard.elements = elements;
    return baseCard;
  }

  createLibraLaunchGrayNotifyCard(info: LibraLaunchNotifyCardInfo): Card | undefined {
    const libraRegion = info.libraInfo.flightInfo.region;
    const libraId = info.libraInfo.flightInfo.id;
    const libraUrl = libraDetailUrl(libraRegion, libraId);
    const createLibraUrl = libraCreateUrl(libraRegion);
    const baseCard = this.buildBaseCard({
      title: `实验提醒：请开启灰度实验`,
      template: CardTemplate.yellow,
      config: { enable_forward: true, update_multi: true },
    });
    const elements: CardElement[] = [];
    elements.push({
      tag: CardElementTag.div,
      text: {
        // eslint-disable-next-line max-len
        content: `**${info.fullVersionDesc} 已于 ${libraFormatTimestamp(info.startTime)} 开始集成测试，请实验 Owner 发起灰度实验，并将流量开启至 100%。**（若已开启请忽略本消息）`,
        tag: CardTextTag.lark_md,
      },
    } as CardContentElement);
    // 操作按钮
    elements.push({
      tag: CardElementTag.div,
      text: {
        content: `实验：[${info.libraInfo.flightInfo.name}](${libraUrl})`,
        tag: CardTextTag.lark_md,
      },
    } as CardContentElement);
    // elements.push({
    //   tag: CardElementTag.div,
    //   text: {
    //     content: `需求：[${info.meegoName}](${info.meegoUrl})`,
    //     tag: CardTextTag.lark_md,
    //   },
    // } as CardContentElement);
    let owners = '';
    for (const owner of info.libraInfo.flightInfo.owners) {
      owners = `${owners}<at email=${owner.email}></at>`;
    }
    elements.push({
      tag: CardElementTag.div,
      text: {
        content: `Owner：${owners}`,
        tag: CardTextTag.lark_md,
      },
    } as CardContentElement);
    elements.push({
      tag: CardElementTag.action,
      actions: [
        {
          tag: CardElementTag.button,
          text: {
            tag: CardTextTag.plain_text,
            content: '开启灰度实验',
          },
          type: CardButtonType.primary,
          url: `${libraUrl}`,
        } as CardButtonAction,
        {
          tag: CardElementTag.button,
          text: {
            tag: CardTextTag.plain_text,
            content: '不再提醒',
          },
          type: CardButtonType.primary,
          value: {
            cardCallbackType: CardCallbackType.LibraLaunchGrayNotifyNotSendAnymore,
            ...(info as any),
          } as CardActionValue,
        } as CardButtonAction,
      ],
    } as CardActionElement);
    baseCard.elements = elements;
    return baseCard;
  }

  createLibraCloseGrayNotifyCard(info: LibraLaunchNotifyCardInfo, createState: LibraCreateState): Card | undefined {
    const libraRegion = info.libraInfo.flightInfo.region;
    const libraId = info.libraInfo.flightInfo.id;
    const libraUrl = libraDetailUrl(libraRegion, libraId);
    const createLibraUrl = libraCreateUrl(libraRegion);
    const baseCard = this.buildBaseCard({
      title: `实验提醒：请关闭灰度实验`,
      template: CardTemplate.yellow,
      config: { enable_forward: true, update_multi: true },
    });
    const elements: CardElement[] = [];
    elements.push({
      tag: CardElementTag.div,
      text: {
        // eslint-disable-next-line max-len
        content: `**${info.fullVersionDesc} 已于 ${libraFormatTimestamp(info.startTime)} ${info.versionStageName}，请实验 Owner 关闭灰度实验。**（若已关闭请忽略本消息）`,
        tag: CardTextTag.lark_md,
      },
    } as CardContentElement);
    elements.push({
      tag: CardElementTag.div,
      text: {
        content: `实验：[${info.libraInfo.flightInfo.name}](${libraUrl})`,
        tag: CardTextTag.lark_md,
      },
    } as CardContentElement);
    // elements.push({
    //   tag: CardElementTag.div,
    //   text: {
    //     content: `需求：[${info.meegoName}](${info.meegoUrl})`,
    //     tag: CardTextTag.lark_md,
    //   },
    // } as CardContentElement);
    let owners = '';
    let domainHost = MAIN_HOST_HTTPS;
    if (process.env.NODE_ENV === 'development') {
      domainHost = 'http://localhost:8080';
    }

    const sheetUrl = `${domainHost}/libra/lark-sidebar-submit?libra_flight_id=${info.libraInfo.flightInfo.id}&sidebar_type=0&is_stop_gray=true`;

    for (const owner of info.libraInfo.flightInfo.owners) {
      owners = `${owners}<at email=${owner.email}></at>`;
    }
    elements.push({
      tag: CardElementTag.div,
      text: {
        content: `Owner：${owners}`,
        tag: CardTextTag.lark_md,
      },
    } as CardContentElement);
    elements.push({
      tag: CardElementTag.action,
      actions: [
        {
          tag: CardElementTag.button,
          text: {
            tag: CardTextTag.plain_text,
            content: '关闭灰度实验',
          },
          type: CardButtonType.primary,
          url: `https://applink.feishu.cn/client/web_url/open?mode=sidebar-semi&width=480&reload=false&url=${encodeURIComponent(sheetUrl)}`,
        } as CardButtonAction,
        {
          tag: CardElementTagV2.button,
          url: `https://applink.feishu.cn/client/web_url/open?mode=sidebar-semi&width=1000&url=${encodeURIComponent(`${domainHost}/libra/create?appid=177501&version=15.3.0&flightId=${info.libraInfo.flightInfo.id}`)}`,
          text: {
            tag: CardTextTag.plain_text,
            content: '手动创建正式实验',
          },
          type: CardButtonType.primary,
        } as CardButtonAction,
        ...(createState === LibraCreateState.Uncreated
          ? [
              {
                tag: CardElementTag.button,
                text: {
                  tag: CardTextTag.plain_text,
                  content: '自动创建正式实验',
                },
                type: CardButtonType.primary,
                value: {
                  cardCallbackType: CardCallbackType.LibraCreateRelease,
                  ...(info as any),
                } as CardActionValue,
              } as CardButtonAction,
            ]
          : []),
        ...(createState === LibraCreateState.Creating
          ? [
              {
                tag: CardElementTag.button,
                text: {
                  tag: CardTextTag.plain_text,
                  content: '创建中...',
                },
                type: CardButtonType.default,
                disabled: true,
              } as CardButtonAction,
            ]
          : []),
        ...(createState === LibraCreateState.Failed
          ? [
              {
                tag: CardElementTag.button,
                text: {
                  tag: CardTextTag.plain_text,
                  content: '创建失败',
                },
                type: CardButtonType.danger,
                disabled: true,
              } as CardButtonAction,
            ]
          : []),
        ...(createState === LibraCreateState.Created
          ? [
              {
                tag: CardElementTag.button,
                text: {
                  tag: CardTextTag.plain_text,
                  content: '已创建',
                },
                type: CardButtonType.default,
                disabled: true,
              } as CardButtonAction,
            ]
          : []),
        {
          tag: CardElementTag.button,
          text: {
            tag: CardTextTag.plain_text,
            content: '不再提醒',
          },
          type: CardButtonType.primary,
          value: {
            cardCallbackType: CardCallbackType.LibraCloseGrayNotifyNotSendAnymore,
            ...(info as any),
          } as CardActionValue,
        } as CardButtonAction,
      ],
    } as CardActionElement);
    baseCard.elements = elements;
    return baseCard;
  }

  createLibraLaunchReleaseNotifyCard(info: LibraLaunchNotifyCardInfo): Card | undefined {
    const libraRegion = info.libraInfo.flightInfo.region;
    const libraId = info.libraInfo.flightInfo.id;
    const libraUrl = libraDetailUrl(libraRegion, libraId);
    const createLibraUrl = libraCreateUrl(libraRegion);
    const baseCard = this.buildBaseCard({
      title: `实验提醒：请开启正式实验`,
      template: CardTemplate.yellow,
      config: { enable_forward: true, update_multi: true },
    });
    let domainHost = MAIN_HOST_HTTPS;
    if (process.env.NODE_ENV === 'development') {
      domainHost = 'http://localhost:8080';
    }
    const elements: CardElement[] = [];
    elements.push({
      tag: CardElementTag.div,
      text: {
        // eslint-disable-next-line max-len
        content: `**${info.fullVersionDesc} 已于 ${libraFormatTimestamp(info.startTime)} 完成全量，请实验 Owner 开启正式实验。**（若已开启请忽略本消息）`,
        tag: CardTextTag.lark_md,
      },
    } as CardContentElement);
    elements.push({
      tag: CardElementTag.div,
      text: {
        content: `实验：[${info.libraInfo.flightInfo.name}](${libraUrl})`,
        tag: CardTextTag.lark_md,
      },
    } as CardContentElement);
    // elements.push({
    //   tag: CardElementTag.div,
    //   text: {
    //     content: `需求：[${info.meegoName}](${info.meegoUrl})`,
    //     tag: CardTextTag.lark_md,
    //   },
    // } as CardContentElement);
    let owners = '';
    for (const owner of info.libraInfo.flightInfo.owners) {
      owners = `${owners}<at email=${owner.email}></at>`;
    }
    elements.push({
      tag: CardElementTag.div,
      text: {
        content: `Owner：${owners}`,
        tag: CardTextTag.lark_md,
      },
    } as CardContentElement);
    elements.push({
      tag: CardElementTag.action,
      actions: [
        {
          tag: CardElementTag.button,
          text: {
            tag: CardTextTag.plain_text,
            content: '开启正式实验',
          },
          type: CardButtonType.primary,
          // eslint-disable-next-line max-len
          url: `${domainHost}/libra/create?appid=${info.appId}&version=${info.version}&isRelease=true&libraUrl=${libraUrl}`,
        } as CardButtonAction,
        // {
        //   tag: CardElementTag.button,
        //   text: {
        //     tag: CardTextTag.plain_text,
        //     content: '关闭灰度实验',
        //   },
        //   type: CardButtonType.primary,
        //   url: `${libraUrl}`,
        // } as CardButtonAction,
        {
          tag: CardElementTag.button,
          text: {
            tag: CardTextTag.plain_text,
            content: '不再提醒',
          },
          type: CardButtonType.primary,
          value: {
            cardCallbackType: CardCallbackType.LibraLaunchReleaseNotifyNotSendAnymore,
            ...(info as any),
          } as CardActionValue,
        } as CardButtonAction,
      ],
    } as CardActionElement);
    baseCard.elements = elements;
    return baseCard;
  }

  createLibraTrafficChangeNotifyCard(info: LibraTrafficChangeNotifyCardInfo) {
    const baseCard = this.buildBaseCard({
      title: `实验流量变更提醒`,
      template: CardTemplate.yellow,
      config: { enable_forward: true, update_multi: true },
    });
    const elements: CardElement[] = [];
    elements.push({
      tag: CardElementTag.div,
      text: {
        content: `${info.appNames.join(',')} [${info.flightName}](${info.flightUrl}) 流量变更为： ${info.traffic}`,
        tag: CardTextTag.lark_md,
      },
    } as CardContentElement);
    elements.push({
      tag: CardElementTag.div,
      text: {
        content: `变更方式：${info.trafficChangeType === 'increase' ? '增加流量' : '减少流量'}`,
        tag: CardTextTag.lark_md,
      },
    } as CardContentElement);
    // elements.push({
    //   tag: CardElementTag.div,
    //   text: {
    //     content: `需求：[${info.meegoName}](${info.meegoUrl})`,
    //     tag: CardTextTag.lark_md,
    //   },
    // } as CardContentElement);
    let owners = '';
    for (const owner of info.flightOwners) {
      owners = `${owners}<at email=${owner.email}></at>`;
    }
    elements.push({
      tag: CardElementTag.div,
      text: {
        content: `Owner：${owners}`,
        tag: CardTextTag.lark_md,
      },
    } as CardContentElement);
    baseCard.elements = elements;
    return baseCard;
  }

  libraPatrolCardJumpURL(data: ExperimentCard, info: DBExperimentShareInfo): string {
    let appid = data.appId;
    // 简单的推断一下，是否是 Android 平台，则优先跳到 Android
    let isAndroid = false;
    if (info.name.includes('Android') || info.name.includes('android') || info.name.includes('安卓')) {
      isAndroid = true;
    } else if (info.meego_owners && 'Android' in info.meego_owners) {
      isAndroid = true;
    }
    // 处理一下 appid，确保能定位到具体的应用
    if (appid === 1775) {
      // 剪映 App
      appid = isAndroid ? 177502 : 177501;
    } else if (appid === 3006) {
      // CapCut App
      appid = isAndroid ? 300602 : 300601;
    } else if (appid === 886) {
      // 剪映专业版-Windows
      appid = 2020092383;
    } else if (appid === 3704) {
      // 剪映专业版-Mac
      appid = 2020092892;
    } else if (appid === 359289) {
      // CapCut 专业版
      appid = 35928901; // CapCut专业版-Windows
      // appid = 35928902; // CapCut专业版-Mac
    } else if (appid === 2515) {
      // 醒图 App
      appid = isAndroid ? 251502 : 251501;
    } else if (appid === 7356) {
      // Hypic App
      appid = isAndroid ? 2020093924 : 2020093988;
    }

    if (data.indicatorType === IndicatorType.Crash) {
      // https://pa.bytedance.net/libra/patrol/stability?appid=177501&flight_id=3032352
      return `${MAIN_HOST_HTTPS}/libra/patrol/stability?appid=${appid}&flight_id=${data.id}`;
    }

    return `${MAIN_HOST_HTTPS}/libra/patrol/stability`;
  }

  createExperimentCard(Data: ExperimentCard[], info: DBExperimentShareInfo): Card {
    const baseCard = this.buildBaseCard({
      title: `纸飞机实验巡检异常上下文`,
      template: CardTemplate.green,
      config: { enable_forward: true, update_multi: true },
    });
    const elements: CardElement[] = [];
    elements.push({
      tag: CardElementTag.div,
      text: {
        content: `实验名称：[${info.name}](${info.url})`,
        tag: CardTextTag.lark_md,
      },
    } as CardContentElement);
    elements.push({
      tag: CardElementTag.div,
      text: {
        content: `实验id：${info.id}`,
        tag: CardTextTag.lark_md,
      },
    } as CardContentElement);
    if (info.meego_owners) {
      let viewers = '';
      if ('PM' in info.meego_owners) {
        viewers = `${viewers} PM:<at email=${info.meego_owners.PM}></at>`;
      }
      if (!('Android' in info.meego_owners || 'iOS' in info.meego_owners)) {
        if ('Server' in info.meego_owners) {
          viewers = `${viewers} Server-RD:<at email=${info.meego_owners.Server}></at>`;
        }
        if ('FE' in info.meego_owners) {
          viewers = `${viewers} FE:<at email=${info.meego_owners.FE}></at>`;
        }
      }
      if ('clientqa' in info.meego_owners) {
        viewers = `${viewers} 业务QA:<at email=${info.meego_owners.clientqa}></at>`;
      }
      if (Data.filter(v => v && v.indicatorType === IndicatorType.GroupUser)) {
        if ('DA' in info.meego_owners) {
          viewers = `${viewers} DA:<at email=${info.meego_owners.DA}></at>`;
        }
      }
      elements.push({
        tag: CardElementTag.div,
        text: {
          content: `请${viewers}关注`,
          tag: CardTextTag.lark_md,
        },
      } as CardContentElement);
    } else {
      // elements.push({
      // 	tag: CardElementTag.div,
      // 	text: {
      // 		content: `请<at id="all"></at>关注`,
      // 		tag: CardTextTag.lark_md,
      // 	},
      // } as CardContentElement);
    }
    for (const data of Data) {
      if (!data) {
        continue;
      }
      elements.push({
        tag: CardElementTag.hr,
      });
      const title = `**${AppId2Name[data.appId]}-${Type2CardTitle[data.indicatorType]}**`;
      elements.push({
        tag: CardElementTag.div,
        text: {
          content: title,
          tag: CardTextTag.lark_md,
        },
      } as CardContentElement);
      if (data.indicatorType === IndicatorType.Crash) {
        if (data.appId % 10 === 1 && info.meego_owners && 'iOS' in info.meego_owners) {
          elements.push({
            tag: CardElementTag.div,
            text: {
              content: `请iOS-RD:<at email=${info.meego_owners.iOS}></at>关注跟进`,
              tag: CardTextTag.lark_md,
            },
          } as CardContentElement);
        }
        if (data.appId % 10 === 2 && info.meego_owners && 'Android' in info.meego_owners) {
          elements.push({
            tag: CardElementTag.div,
            text: {
              content: `请Android-RD:<at email=${info.meego_owners.Android}></at>关跟进注`,
              tag: CardTextTag.lark_md,
            },
          } as CardContentElement);
        }
      }
      elements.push({
        tag: CardElementTag.div,
        text: {
          content: `优先级：**${data.priority === 0 ? `<font color="red">P${data.priority}</font>` : `P${data.priority}`}**`,
          tag: CardTextTag.lark_md,
        },
      } as CardContentElement);
      elements.push({
        tag: CardElementTag.div,
        text: {
          content: `Meego优先级:P${data.meego_level} Meego状态:${
            MeegoStatus2Name[data.meego_status]
          } [Meego详情](https://meego.feishu.cn/faceu/issue/detail/${data.meegoId})`,
          tag: CardTextTag.lark_md,
        },
      } as CardContentElement);
      elements.push({
        tag: CardElementTag.div,
        text: {
          content: `问题列表:
${data.detail}`,
          tag: CardTextTag.lark_md,
        },
      } as CardContentElement);
    }
    elements.push({
      tag: CardElementTag.action,
      actions: [
        {
          tag: CardElementTag.button,
          text: {
            tag: CardTextTag.plain_text,
            content: '纸飞机',
          },
          type: CardButtonType.primary,
          url:
            Data.length > 0 ? this.libraPatrolCardJumpURL(Data[0], info) : `${MAIN_HOST_HTTPS}/libra/patrol/stability`,
        } as CardButtonAction,
        {
          tag: CardElementTag.button,
          text: {
            tag: CardTextTag.plain_text,
            content: '跟进文档',
          },
          type: CardButtonType.primary,
          url: 'https://bytedance.larkoffice.com/docx/Ow2ddYE8joEWdNxsi2UcyuDnnfb?from=from_copylink',
        } as CardButtonAction,
      ],
    } as CardActionElement);
    baseCard.elements = elements;
    return baseCard;
  }

  async testingLibraTipCard(dbLibraInfo: DBLibraInfo, stage: string, versionCode: string): Promise<Card> {
    const appId = getAppId(dbLibraInfo.platform, current_region() !== Region.SG ? LVProductType.lv : LVProductType.cc);
    const baseCard = this.buildBaseCard({
      title: '实验未开始放量提醒',
      template: CardTemplate.red,
      config: { enable_forward: false },
    });
    const elements: CardElement[] = [];
    elements.push({
      tag: CardElementTag.div,
      text: {
        tag: CardTextTag.lark_md,
        content: `${current_region() !== Region.SG ? '剪映' : 'Capcut'} ${
          dbLibraInfo.platform === LibraPlatform.android ? 'Android' : 'iOS'
        } ${dbLibraInfo.version}版本当前处于${stage}阶段，小版本号${versionCode}，检测到你有以下实验还未开始放量`,
      },
    } as CardContentElement);
    elements.push({ tag: CardElementTag.hr });
    elements.push({
      tag: CardElementTag.div,
      text: {
        tag: CardTextTag.lark_md,
        content: `[${dbLibraInfo.name}](${dbLibraInfo.flight_url}) `,
      },
    } as CardContentElement);
    elements.push({
      tag: CardElementTag.action,
      actions: [
        {
          tag: CardElementTag.button,
          url: `${MAIN_HOST_HTTPS}/releaseProcess/overview?appid=${appId.app_id}&version=${dbLibraInfo.version}`,
          text: {
            tag: CardTextTag.plain_text,
            content: `查看版本详情`,
          },
          type: CardButtonType.primary,
        } as CardButtonAction,
      ],
    } as CardActionElement);
    elements.push({
      tag: CardElementTag.div,
      text: {
        tag: CardTextTag.lark_md,
        content: `实验流程规范👉[剪映&CapCut A/B 实验流程SOP](https://bytedance.larkoffice.com/wiki/wikcn3FSeBpV6tP78qanqPVAd3b) `,
      },
    } as CardContentElement);
    baseCard.elements = elements;
    return baseCard;
  }

  async runningLibraTipCard(dbLibraInfo: DBLibraInfo, stage: string, versionCode: string): Promise<Card> {
    const appId = getAppId(dbLibraInfo.platform, current_region() !== Region.SG ? LVProductType.lv : LVProductType.cc);
    const baseCard = this.buildBaseCard({
      title: '注意观察实验数据提醒',
      template: CardTemplate.yellow,
      config: { enable_forward: false },
    });
    const elements: CardElement[] = [];
    elements.push({
      tag: CardElementTag.div,
      text: {
        tag: CardTextTag.lark_md,
        content: `${current_region() !== Region.SG ? '剪映' : 'Capcut'} ${
          dbLibraInfo.platform === LibraPlatform.android ? 'Android' : 'iOS'
        } ${
          dbLibraInfo.version
        }版本当前处于${stage}阶段，小版本号${versionCode}，检测到你有以下实验已经开始放量，当前放量${
          dbLibraInfo.numerical_traffic * 100
        }%，注意观察实验数据`,
      },
    } as CardContentElement);
    elements.push({ tag: CardElementTag.hr });
    elements.push({
      tag: CardElementTag.div,
      text: {
        tag: CardTextTag.lark_md,
        content: `[${dbLibraInfo.name}](${dbLibraInfo.flight_url}) `,
      },
    } as CardContentElement);
    elements.push({
      tag: CardElementTag.action,
      actions: [
        {
          tag: CardElementTag.button,
          url: `${MAIN_HOST_HTTPS}/releaseProcess/overview?appid=${appId.app_id}&version=${dbLibraInfo.version}`,
          text: {
            tag: CardTextTag.plain_text,
            content: `查看版本详情`,
          },
          type: CardButtonType.primary,
        } as CardButtonAction,
      ],
    } as CardActionElement);
    elements.push({
      tag: CardElementTag.div,
      text: {
        tag: CardTextTag.lark_md,
        content: `实验流程规范👉[剪映&CapCut A/B 实验流程SOP](https://bytedance.larkoffice.com/wiki/wikcn3FSeBpV6tP78qanqPVAd3b) `,
      },
    } as CardContentElement);
    baseCard.elements = elements;
    return baseCard;
  }

  libraTakeOverCard(record: LibraControlRecord) {
    const baseCard = this.buildBaseCard({
      title: `【${currentProductName()}】实验操作已被纸飞机管控`,
      template: CardTemplate.red,
      config: { enable_forward: false },
    });
    const elements: CardElement[] = [];
    elements.push({
      tag: CardElementTag.div,
      text: {
        tag: CardTextTag.lark_md,
        content: `<at email="${add_suffix_ne('@bytedance.com')(
          record.user,
        )}"></at> 你的以下 Libra 实验操作已被纸飞机管控，Libra 将不会执行操作。\n\n管控原因：**${
          record.take_over_reason
        }**，请跳转纸飞机查看并根据实验SOP进行处理！`,
      },
    } as CardContentElement);
    elements.push({ tag: CardElementTag.hr });
    elements.push({
      tag: CardElementTag.div,
      text: {
        tag: CardTextTag.lark_md,
        content: `[${record.flight_name ?? record.flight_id}](${commonUtils.getLibraFlightUrl(
          record.flight_id,
          currentProduct() === LVProductType.cc,
        )}) 执行 **${getFlightEventName(record)}**`,
      },
    } as CardContentElement);
    elements.push({
      tag: CardElementTag.action,
      actions: [
        {
          tag: CardElementTag.button,
          url: buildRecordUrl(record, currentProduct() !== LVProductType.cc),
          text: {
            tag: CardTextTag.plain_text,
            content: `查看管控记录`,
          },
          type: CardButtonType.primary,
        } as CardButtonAction,
      ],
    } as CardActionElement);
    elements.push({
      tag: CardElementTag.div,
      text: {
        tag: CardTextTag.lark_md,
        content: `处理手册📌[实验被纸飞机管控后处理手册](https://bytedance.larkoffice.com/wiki/JIKywsI0WiShQPksqE5czgo9nbh) `,
      },
    } as CardContentElement);
    elements.push({
      tag: CardElementTag.div,
      text: {
        tag: CardTextTag.lark_md,
        content: `实验流程规范👉[剪映&CapCut A/B 实验流程SOP](https://bytedance.larkoffice.com/wiki/wikcn3FSeBpV6tP78qanqPVAd3b) `,
      },
    } as CardContentElement);
    baseCard.elements = elements;
    return baseCard;
  }

  libraProcessCard(record: LibraControlRecord) {
    const baseCard = this.buildBaseCard({
      title: `【${currentProductName()}】纸飞机已自动放量`,
      template: CardTemplate.green,
      config: { enable_forward: false },
    });
    const elements: CardElement[] = [];
    elements.push({
      tag: CardElementTag.div,
      text: {
        tag: CardTextTag.lark_md,
        content: `你的以下 Libra 实验操作已通过纸飞机管控，纸飞机已调用 Libra 接口进行自动操作。`,
      },
    } as CardContentElement);
    elements.push({ tag: CardElementTag.hr });
    elements.push({
      tag: CardElementTag.div,
      text: {
        tag: CardTextTag.lark_md,
        content: `[${record.flight_name ?? record.flight_id}](${commonUtils.getLibraFlightUrl(
          record.flight_id,
          currentProduct() === LVProductType.cc,
        )}) 执行 **${getFlightEventName(record)}**`,
      },
    } as CardContentElement);
    elements.push({
      tag: CardElementTag.action,
      actions: [
        {
          tag: CardElementTag.button,
          url: buildRecordUrl(record, currentProduct() !== LVProductType.cc),
          text: {
            tag: CardTextTag.plain_text,
            content: `查看管控记录`,
          },
          type: CardButtonType.primary,
        } as CardButtonAction,
      ],
    } as CardActionElement);
    elements.push({
      tag: CardElementTag.div,
      text: {
        tag: CardTextTag.lark_md,
        content: `实验流程规范👉[剪映&CapCut A/B 实验流程SOP](https://bytedance.larkoffice.com/wiki/wikcn3FSeBpV6tP78qanqPVAd3b) `,
      },
    } as CardContentElement);
    baseCard.elements = elements;
    return baseCard;
  }

  buildMrAttrFailedCard(msg: string) {
    const baseCard = this.buildBaseCard({
      title: 'MR归因异常',
      template: CardTemplate.red,
    });
    const elements: CardElement[] = [];
    elements.push({
      tag: CardElementTag.markdown,
      content: `**${msg}**`,
    } as CardMarkdownElement);
    elements.push({ tag: CardElementTag.hr });
    elements.push({
      tag: CardElementTag.markdown,
      content: `**MR归因命令格式：**\n\`\`\`Bash\nmr_attr --version xxx --url xxx\nmr_attr --version=xxx --url=xxx\n\`\`\``,
    } as CardMarkdownElement);
    elements.push({ tag: CardElementTag.hr });
    elements.push({
      tag: CardElementTag.note,
      elements: [
        {
          tag: CardTextTag.standard_icon,
          token: 'robot_outlined',
        },
        {
          tag: CardTextTag.plain_text,
          content: '归因出错了',
        },
      ],
    } as CardNoteElement);
    elements.push({
      tag: CardElementTag.div,
      text: {
        tag: CardTextTag.lark_md,
        content: `如果有其它疑问请联系<at email=<EMAIL>></at> <at email=<EMAIL>></at>`,
      },
    } as CardContentElement);

    baseCard.elements = elements;
    return baseCard;
  }

  buildMrAttrMrInfoElement(mrInfo: MRAttrInfo): CardMarkdownElement {
    const mrUrl = `https://bits.bytedance.net/devops/1486844930/code/detail/${mrInfo.mr_id}`;
    return {
      tag: CardElementTag.markdown,
      content: `**[${mrInfo.mr_title}](${mrUrl})** <at email=${mrInfo.mr_author}@bytedance.com></at>`,
    };
  }

  buildMrAttrChangePointElement(changePoint: MRAttrChangePoint): CardMarkdownElement {
    let content = `- <font color="green">${changePoint.path}</font>`;
    for (let i = 0; i < changePoint.method.length; i++) {
      if (i === 3) {
        // 最多展示三个方法
        break;
      }
      const method = changePoint.method[i];
      content += `\n\t- ${method}`;
    }
    return {
      tag: CardElementTag.markdown,
      content,
    };
  }

  buildMrAttrOneVersionElement(versionedMrInfo: MRAttrVersionedInfo): CardElement[] {
    const elements: CardElement[] = [];
    elements.push({
      tag: CardElementTag.markdown,
      content: `**版本 ${versionedMrInfo.version}**`,
    } as CardMarkdownElement);

    for (let i = 0; i < versionedMrInfo.mr_infos.length; i++) {
      if (i === 3) {
        // 每个版本最多展示3个MR
        break;
      }
      const mrInfo = versionedMrInfo.mr_infos[i];
      elements.push(this.buildMrAttrMrInfoElement(mrInfo));

      for (let j = 0; j < mrInfo.change_points.length; j++) {
        if (j === 3) {
          // 每个MR最多展示3个变更点
          break;
        }
        const changePoint = mrInfo.change_points[j];
        elements.push(this.buildMrAttrChangePointElement(changePoint));
      }

      elements.push({ tag: CardElementTag.hr });
    }

    return elements;
  }

  buildMrAttrSuccessCard(data: MRAttrResultData, detailUrl: string) {
    const baseCard = this.buildBaseCard({
      title: 'MR归因结果',
      template: CardTemplate.green,
    });
    const elements: CardElement[] = [];
    for (let i = 0; i < data.versioned_mr_infos.length; i++) {
      if (i === 2) {
        // 最多展示两个版本
        break;
      }
      const versionedMrInfo = data.versioned_mr_infos[i];
      elements.push(...this.buildMrAttrOneVersionElement(versionedMrInfo));
    }

    elements.push({
      tag: CardElementTag.action,
      actions: [
        {
          tag: CardElementTag.button,
          text: {
            tag: CardTextTag.plain_text,
            content: '查看完整归因详情',
          },
          type: CardButtonType.primary,
          url: detailUrl,
        } as CardButtonAction,
      ],
    } as CardActionElement);

    elements.push({
      tag: CardElementTag.note,
      elements: [
        {
          tag: CardTextTag.standard_icon,
          token: 'emoji_outlined',
        },
        {
          tag: CardTextTag.plain_text,
          content: '本次归因结果是否有用？',
        },
      ],
    } as CardNoteElement);

    elements.push({
      tag: CardElementTag.action,
      actions: [
        {
          tag: CardElementTag.button,
          text: {
            tag: CardTextTag.plain_text,
            content: '👍🏻 非常有用',
          },
          type: CardButtonType.primary,
          url: 'https://applink.feishu.cn/client/chat/open?openChatId=oc_0fa8087405834d423f9d9c71cedad699',
        } as CardButtonAction,
        {
          tag: CardElementTag.button,
          text: {
            tag: CardTextTag.plain_text,
            content: '去反馈',
          },
          type: CardButtonType.default,
          url: 'https://applink.feishu.cn/client/chat/open?openChatId=oc_0fa8087405834d423f9d9c71cedad699',
        } as CardButtonAction,
      ],
    } as CardActionElement);

    baseCard.elements = elements;
    return baseCard;
  }

  buildMrAttrSuccessSimpleCard(detailUrl: string) {
    const baseCard = this.buildBaseCard({
      title: 'MR归因结果',
      template: CardTemplate.green,
    });
    const elements: CardElement[] = [];

    elements.push({
      tag: CardElementTag.action,
      actions: [
        {
          tag: CardElementTag.button,
          text: {
            tag: CardTextTag.plain_text,
            content: '👉 查看归因结果',
          },
          type: CardButtonType.primary,
          url: detailUrl,
        } as CardButtonAction,
      ],
    } as CardActionElement);

    baseCard.elements = elements;
    return baseCard;
  }
}
