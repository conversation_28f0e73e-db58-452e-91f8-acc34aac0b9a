import Network from '../utils/network';
import {
  LibraAppIds,
  LibraModel,
  LibraPlatform,
  LibraResult,
  LibraSecret,
  LibraToken,
} from '@shared/libra/commonLibra';
import {
  BETA_COMBINE_TYPE_GRAY,
  BETA_COMBINE_TYPE_RELEASE,
  BetaCombineGroup,
  Flight,
  FlightListResponse,
  UserHit,
} from '@shared/libra/flight';
import {
  AppSetting,
  AppSettingId,
  AppSettingSymbol,
  LVProductType,
  ProductType,
} from '@pa/shared/dist/src/appSettings/appSettings';
import { Inject, Injectable } from '@gulux/gulux';
import { BytedLogger } from '@gulux/gulux/byted-logger';
import { filterLibraByVersion } from '../../utils/libraUtil';
import { currentProduct } from '../../utils/basic';
import { HttpResponse } from '@shared/utils/api';
import { NetworkCode } from '@pa/shared/dist/src/core';
import { LibraGroupInfo } from '@shared/libra/LibraGroupInfo';

const cnUrl = 'https://data.bytedance.net/dataopen/open-apis';
const sgUrl = 'https://sg-data.bytedance.net/dataopen/open-apis';

@Injectable()
export default class LibraService {
  @Inject()
  private logger: BytedLogger;

  getLibraKey(product: ProductType): LibraSecret {
    if (product === LVProductType.lv) {
      return {
        app_id: 'cli_418fdf2d7e4b25',
        app_secret: '0DzBJJjyVWFjlimdVKYFcvrZHG2uH4Nj',
      } as LibraSecret;
    } else {
      return {
        app_id: 'cli_41d5862c55c5c0',
        app_secret: 'YMWQ4pfpL3RXz9uhrvFYPg5pnmF8b26D',
      } as LibraSecret;
    }
  }
  async createLibraNetwork(product: ProductType): Promise<Network> {
    const baseUrl = product === LVProductType.lv ? cnUrl : sgUrl;
    const token = await this.requestLibraToken(product, baseUrl);
    const headers: {
      [key: string]: any;
    } = {
      Authorization: token,
    };
    this.logger.info(`requestLibraToken ${product}= ${token}`);
    return new Network(baseUrl, headers);
  }

  async requestLibraToken(product: ProductType, baseUrl: string): Promise<string | undefined> {
    const network = new Network(baseUrl);
    const libraSecret = this.getLibraKey(product);
    const result = await network.post<LibraResult<LibraToken>>('/v1/authorization', {
      app_id: libraSecret.app_id,
      app_secret: libraSecret.app_secret,
    });
    if (result.data) {
      this.logger.info(`[requestLibraToken]${product}:from network:${JSON.stringify(result.data)}`);
      return result.data.access_token;
    } else {
      return;
    }
  }

  /**
   * 查询实验列表API
   * /open/flight-list/
   */
  async queryFlightList(product: ProductType, args: any): Promise<FlightListResponse | undefined> {
    const network = await this.createLibraNetwork(product);
    const result = await network.get<LibraResult<FlightListResponse>>(`/libra/openapi/v1/open/flight-list/`, args);
    return result.data;
  }

  /**
   * 循环查询对应应用的所有未关闭实验列表
   */
  async queryAllFlightListByProduct(
    product: ProductType,
    libraPlatform: LibraPlatform,
    libraModel: string,
  ): Promise<Flight[]> {
    let response: Flight[] = [];
    const appId = product === LVProductType.lv ? LibraAppIds.lv : LibraAppIds.cc;
    let page = 1;
    let result = await this.queryFlightList(product, {
      app: Number(appId),
      device_platform: libraPlatform,
      product: Number(libraModel),
      page,
      page_size: 50,
    });
    if (result && 'flights' in result) {
      response = response.concat(result.flights);
      let lastStatus = result.flights[result.flights.length - 1].status;
      while (lastStatus === 1 || lastStatus === 3) {
        this.logger.info(`queryAllFlightListByProduct:page=${page}lastStatus=${lastStatus}`);
        page = page + 1;
        result = await this.queryFlightList(product, {
          app: appId,
          device_platform: 'android',
          product: libraModel,
          page,
          page_size: 50,
        });
        if (result && 'flights' in result) {
          lastStatus = result.flights[result.flights.length - 1].status;
          response = response.concat(result.flights);
        } else {
          lastStatus = 0;
        }
      }
    }
    let flightResponse: Flight[] = [];
    for (const responseItem of response) {
      if (responseItem.status === 1 || responseItem.status === 3) {
        flightResponse = flightResponse.concat(responseItem);
      }
    }
    return flightResponse;
  }

  // ###################################### 以下是具体业务逻辑的组合 ###################################################

  /**
   * 实验状态变化的时候接收webhook，更新数据库的实验状态status和放量数据version_resource；
   * @param libraModel
   * @param versionStage
   */
  async updateLibraInfoByLibraHook(libraModel: LibraModel, versionStage: string) {
    // const flights = this.queryFlightList(
    //   productType,
    //   platform,
    //   version,
    //   libraModel,
    // );
    // let libraInfo: LibraInfo[] = [];
    // for (const flightElement of flights) {
    //   libraInfo = libraInfo.concat(
    //     this.ctx.service.utils.libraUtil.formatFlight2Libra(
    //       productType,
    //       platform,
    //       version,
    //       flightElement,
    //     ),
    //   );
    // }
  }

  /**
   * 版本状态更新的时候查询当前版本未开启的实验进行提醒，并更新tipTime数据库数据.
   */
  // async tipNotOpenLibraOwner(productType: ProductType, platform: LibraPlatform, version: string) {}

  /**
   * 1. 每次版本状态变化的时候，查询版本实验并更新数据库，
   * 2. 将同一个人的所有实验集成到一个卡片上提醒owner进行实验观察
   * @param productType
   * @param platform
   * @param version
   */
  // async updateLibraInfoByVersionProcessChange(productType: ProductType, platform: LibraPlatform, version: string) {}

  /**
   * 根据产品，平台，模块，版本 查询对应的实验列表
   * @param productType lv || cc
   * @param platform android || iphone
   * @param libraModel 444
   * @param version 11.9.0
   */
  async queryAllFlightListByVersion(
    productType: ProductType,
    platform: LibraPlatform,
    version: string,
    libraModel: LibraModel,
  ): Promise<Flight[]> {
    const flights = await this.queryAllFlightListByProduct(productType, platform, libraModel);
    return filterLibraByVersion(productType, platform, version, flights);
  }

  async queryFlightDetailById(flight_id: string) {
    const network = await this.createLibraNetwork(currentProduct());
    const result = await network.get<HttpResponse<{ flight: Flight }>>(
      `/libra/openapi/v1/open/flight/${flight_id}/view/`,
    );
    this.logger.info(`[queryFlightDetailById] ${flight_id} res: ${JSON.stringify(result)}`);
    return result.code === NetworkCode.Success ? result.data?.flight : undefined;
  }

  async queryFlightHistoryById(flight_id: string) {
    const network = await this.createLibraNetwork(currentProduct());
    const result = await network.get<HttpResponse<any>>(`/libra/openapi/v1/open/flight/${flight_id}/history/`);
    this.logger.info(`[queryFlightHistoryById] ${flight_id} res: ${JSON.stringify(result)}`);
    return result.code === NetworkCode.Success ? result.data : undefined;
  }

  /**
   * 平滑放量 https://data.bytedance.net/dataopen/libra/document/49
   * @param flight_id
   * @param event_type
   * @param version_resource
   */
  async smoothChangeFlightResource(flight_id: string, event_type: string, version_resource: number) {
    this.logger.info(`[smoothChangeFlightResource] ${flight_id} ${event_type} ${version_resource}`);
    const network = await this.createLibraNetwork(currentProduct());
    const args = {
      event_type,
      version_resource,
    };
    const result = await network.post<HttpResponse<any>>(
      `/libra/openapi/v1/open/flight/${flight_id}/smooth-change-flight-resource/`,
      args,
    );
    this.logger.info(`[smoothChangeFlightResource] res: ${JSON.stringify(result)}`);
    return result;
  }

  /**
   * 获取众内测组合实验组
   * @param libra_app_id
   * @param beta_combine_key
   */
  async queryBetaCombineGroup(libra_app_id: number, beta_combine_key: string) {
    this.logger.info(`[queryBetaCombineGroup] ${beta_combine_key}`);
    const result = await this.queryFlightList(currentProduct(), {
      app: libra_app_id,
      flights_key: beta_combine_key,
    });
    this.logger.info(`[queryBetaCombineGroup] res: ${JSON.stringify(result)}`);
    return {
      beta_combine_key,
      gray_flight: result?.flights?.find(v => v.beta_combine_type === BETA_COMBINE_TYPE_GRAY),
      release_flight: result?.flights?.find(v => v.beta_combine_type === BETA_COMBINE_TYPE_RELEASE),
      all_flights: result?.flights ?? [],
    } as BetaCombineGroup;
  }

  /**
   * 重置实验流量
   * @param flight_id
   * @param version_resource
   */
  async resetFlightResource(flight_id: string, version_resource: number) {
    this.logger.info(`[resetFlightResource] ${flight_id} ${version_resource}`);
    const network = await this.createLibraNetwork(currentProduct());
    const args = {
      version_resource,
    };
    const result = await network.post<HttpResponse<any>>(
      `/libra/openapi/v1/open/flight/${flight_id}/reset-flight-resource/`,
      args,
    );
    this.logger.info(`[resetFlightResource] res: ${JSON.stringify(result)}`);
    return result;
  }

  async queryFlightGroupInfo(flight_id: string) {
    this.logger.info(`[queryFlightGroupInfo] ${flight_id}`);
    const network = await this.createLibraNetwork(currentProduct());
    const result = await network.get<HttpResponse<LibraGroupInfo>>(
      `/libra/openapi/v1/open/flight/${flight_id}/get-baseuser/`,
    );
    this.logger.info(`[queryFlightGroupInfo] res: ${JSON.stringify(result)}`);
    return result.code === NetworkCode.Success ? result.data : undefined;
  }
}
