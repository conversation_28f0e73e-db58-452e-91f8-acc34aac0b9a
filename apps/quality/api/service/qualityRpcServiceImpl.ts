import { QualityRpcService, QualityRpcServiceSymbol } from '@pa/backend/dist/src/rpc/qualityRpcService';
import { Inject, Injectable } from '@gulux/gulux';
import { CardCallback, CardCallbackSubType } from '@pa/shared/dist/src/lark/larkCard';
import IssueAutoPush from './slardar/IssueAutoPush';
import { BytedLogger } from '@gulux/gulux/byted-logger';
import { useInject } from '@edenx/runtime/bff';
import LarkService from '@pa/backend/dist/src/third/lark';
import ReleaseAutoChangeSettings from './ReleaseAutoChangeSettings';
import {
  GetLogActionInfoRequest,
  GetLogActionInfoResponse,
  GetLogIssueInfoRequest,
  GetLogIssueInfoResponse,
} from '@pa/shared/dist/src/alog/models';
import AlogService from './alog/AlogServer';
import { LynxUpdateService } from './LynxUpdateService';
import { inviteGroupFromLark, searchRecords, sendAlarmTestToPerson } from '@api/metricsAlarmSystem';
import SlardarValueModel from '../model/SlardarValueTable';
import { DeviceLevel } from '@shared/common';
import ExperimentService from './experiment/experiment';
import { LibraNotifyService } from './libra/LibraNotifyService';
import { LibraEventBusHandler } from './libra/LibraEventBusHandler';
import { LibraControlService } from './libra/LibraControlService';
import { closeFlightById, fetchUserMemberList, libraAttribution } from '@api/libra';
import { PlatformType } from '@pa/shared/dist/src/core';
import { AlarmRecordParser } from '@shared/metriec/AlarmRecordParser';
import IssueDiagnosisService from './reliability-resolved/diagnosis/issue_diagnosis';
import { LibraNewInfoListService } from './libra/LibraNewInfoListService';
import { LibraPaControlRecordService } from './libra/LibraPaControlRecordService';
import { AbnormalFlightReportInfoService } from './libra/AbnormalFlightReportInfoService';
import BlameService from './assign/blame';
import { CustomAssignArgs } from '@pa/shared/dist/src/assign/customAssignArgs';
import { BlameModel } from '@pa/shared/dist/src/assign/blameModel';
import { getMeegoTreeValue } from '@shared/storyRevenueReviewPlatform/StoryRevenuePlatformUtils';
import LibraPatrolConfigDao from '../dao/libra/LibraPatrolConfigDao';
import { LibraPatrolConfig } from '@shared/libra/LibraPatrolInfo';
import { RequestLibraPatrolConfigDaoType } from '@pa/shared/dist/src/libra/LibraAttributionModel';
import LibraNewInfoListDao from '../dao/libra/LibraNewInfoListDao';
import { getLibraVersionPlatformFromFilterRule } from '../utils/libraUtil';
import { LibraVersionPlatform } from '@shared/libra/libraInfo';
import SlardarCircuitBreakerService from './circuitBreaker/slardarCircuitBreaker';
import { LibraRegion } from '@shared/libra/commonLibra';
import LarkCardService from './larkCard';
import { LibraCreateState } from '@shared/libra/LibraNotifyInfo';
import { LibraNewInfo } from '@shared/libra/LibraNewInfo';

@Injectable({ id: QualityRpcServiceSymbol })
export default class QualityRpcServiceImpl implements QualityRpcService {
  @Inject()
  private issueAutoPush: IssueAutoPush;
  @Inject()
  private logger: BytedLogger;
  @Inject()
  private lynxUpdateService: LynxUpdateService;
  @Inject()
  private releaseAutoChangeSettings: ReleaseAutoChangeSettings;
  @Inject()
  private alogService: AlogService;
  @Inject()
  private experimentService: ExperimentService;
  @Inject()
  private libraNotifyService: LibraNotifyService;
  @Inject()
  private eventBusHandler: LibraEventBusHandler;
  @Inject()
  private libraControlService: LibraControlService;
  @Inject()
  private issueDiagnosisService: IssueDiagnosisService;
  @Inject()
  private libraNewInfoListService: LibraNewInfoListService;
  @Inject()
  private libraPaControlRecordService: LibraPaControlRecordService;
  @Inject()
  private abnormalFlightReportInfoService: AbnormalFlightReportInfoService;
  @Inject()
  private blameService: BlameService;
  @Inject()
  private libraPatrolConfigDao: LibraPatrolConfigDao;
  @Inject()
  private circuitService: SlardarCircuitBreakerService;

  async dispatchQualityCardAction(data: CardCallback) {
    this.logger.info(`qualityLarkCardCallback: ${JSON.stringify(data)}`);
    const { cardCallbackSubType } = data.action.value;
    const userInfo = await useInject(LarkService).getUserInfo(data.open_id);
    switch (cardCallbackSubType) {
      case CardCallbackSubType.IssueSolving:
        this.issueAutoPush.actionSolving(Number(data.action.value.meegoId), userInfo?.email ?? '');
        break;
      case CardCallbackSubType.IssueRequestReAssign:
        this.issueAutoPush.actionRequestReAssign(Number(data.action.value.meegoId), userInfo?.email ?? '');
        break;
      case CardCallbackSubType.IssueQAPush:
        this.issueAutoPush.actionQAPush(Number(data.action.value.meegoId), userInfo?.email ?? '');
        break;
      case CardCallbackSubType.IssueQAOnCall:
        this.issueAutoPush.actionQAOnCall(Number(data.action.value.meegoId), userInfo?.email ?? '');
        break;
      case CardCallbackSubType.IssueCrashBMOnCall:
        this.issueAutoPush.actionCrashBMOnCall(Number(data.action.value.meegoId), userInfo?.email ?? '');
        break;
      case CardCallbackSubType.IssueReAssign:
        this.issueAutoPush.actionReAssign(
          Number(data.action.value.meegoId),
          userInfo?.email ?? '',
          data.action.input_value ?? '',
        );
        break;
      case CardCallbackSubType.IssueRefresh:
        this.issueAutoPush.pushByMeegoIdWhenCreate(Number(data.action.value.meegoId));
        break;
      case CardCallbackSubType.IssueABTestAnalyze:
        this.issueDiagnosisService.handleABTestAnalyzeAction(data.action.value.chatId, data.action.value.slardarUrl);
        break;
      default:
        break;
    }
    return {
      code: 0,
    };
  }

  async queryJwtToken(accessKey: string) {
    return await this.lynxUpdateService.queryJwtToken(accessKey);
  }

  addReviewVersion(data: any): Promise<any> {
    return this.releaseAutoChangeSettings.addReviewVersion(data);
  }

  clearReviewVersion(data: any): Promise<any> {
    return this.releaseAutoChangeSettings.clearReviewVersion(data);
  }

  getAlogActionInfos(data: GetLogActionInfoRequest): Promise<GetLogActionInfoResponse> {
    return this.alogService.getLogActionInfo(data.url, data.aggregateCount);
  }

  getAlogIssueInfo(data: GetLogIssueInfoRequest): Promise<GetLogIssueInfoResponse> {
    return this.alogService.getAlogIssueInfo(data.url);
  }

  async notifyMetricsAlarm(data: any): Promise<any> {
    return inviteGroupFromLark({
      data: {
        appId: data.appId,
        metricName: data.metricName,
        versionCode: data.versionCode,
        email: data.email,
        platform: data.platform,
        cardInfoJson: data.cardInfoJson,
      },
    });
  }

  async testMetricsAlarm(): Promise<void> {
    await sendAlarmTestToPerson({
      data: {
        msg: '测试',
        email: '<EMAIL>',
      },
    });
  }

  async isMainArchFocusExp(flightId: string) {
    return this.experimentService.isMainArchFocusExp(flightId);
  }

  async fetchSlardarValues(versionCode: number, timeStamp: number) {
    const slardarValueModel = useInject(SlardarValueModel);
    const values = await slardarValueModel.query({
      VersionCode: versionCode,
      Timestamp: timeStamp,
      device_level: DeviceLevel.ALL,
    });
    console.log(`[data]:${{ versionCode, timeStamp }} [values]:${JSON.stringify(values)}`);
  }

  async requestLibraPatrolConfigDao(data: any, type: RequestLibraPatrolConfigDaoType) {
    if (type === RequestLibraPatrolConfigDaoType.Create) {
      const service = useInject(LibraPatrolConfigDao);
      return await service.save(data as LibraPatrolConfig);
    } else if (type === RequestLibraPatrolConfigDaoType.GetList) {
      const service = useInject(LibraPatrolConfigDao);
      return await service.list(data.quary, data.page, data.pageSize);
    } else if (type === RequestLibraPatrolConfigDaoType.Count) {
      const service = useInject(LibraPatrolConfigDao);
      return await service.count(data.quary);
    } else if (type === RequestLibraPatrolConfigDaoType.Update) {
      const service = useInject(LibraPatrolConfigDao);
      return await service.updateOne(
        { metric_group_id: data.metric_group_id, metric_patrol_type: data.metric_patrol_type },
        data as LibraPatrolConfig,
      );
    } else if (type === RequestLibraPatrolConfigDaoType.Delete) {
      const service = useInject(LibraPatrolConfigDao);
      const res = await service.findOne(data);
      if (res && res.metric_list && res.metric_list.length !== 0) {
        return {
          result: -1,
          message: '该配置下有指标，无法删除，请先删除指标',
        };
      }
      await service.deleteOne(data);
      return {
        result: 0,
        message: '删除成功',
      };
    }
  }
  async getMeegoTree(fieldKey: string) {
    return getMeegoTreeValue(fieldKey);
  }
  async getUserMemberList(): Promise<unknown> {
    return await fetchUserMemberList({
      data: {},
    });
  }
  async getBusinessInfoById(id: number) {
    try {
      const newInfo = await useInject(LibraNewInfoListDao).findOne({ 'flightInfo.id': id });
      let allBusinessLines: string[] = [];
      if (newInfo && newInfo.meegoInfo && Array.isArray(newInfo.meegoInfo)) {
        newInfo.meegoInfo.forEach(item => {
          if (item && Array.isArray(item.businessLine)) {
            // 将每个 businessLine 数组合并到 allBusinessLines 中
            allBusinessLines = allBusinessLines.concat(item.businessLine);
          }
        });
      }
      return allBusinessLines;
    } catch (error) {
      return [];
    }
  }
  // 根据 APP 版本通知开启灰度实验
  async libraLaunchGrayNotify(
    appId: number,
    grayVersion: string,
    fullVersionDesc: string,
    versionStageName: string,
    startTime: number,
  ) {
    await this.libraNotifyService.launchGrayNotify(appId, grayVersion, fullVersionDesc, versionStageName, startTime);
    return {
      code: 0,
      message: 'success',
    };
  }

  // 根据 APP 版本通知灰度实验开启100%流量
  async libraGray100PercentNotify(
    appId: number,
    grayVersion: string,
    fullVersionDesc: string,
    versionStageName: string,
    startTime: number,
    meegoIds: number[],
  ) {
    await this.libraNotifyService.gray100PercentNotify(
      appId,
      grayVersion,
      fullVersionDesc,
      versionStageName,
      startTime,
      meegoIds,
    );
    return {
      code: 0,
      message: 'success',
    };
  }

  // 不再发送100%灰度实验提醒
  async forbidLibra100PercentGrayNotify(libraInfo: any) {
    await this.libraNotifyService.forbid100PercentGrayNotify(libraInfo);
    return {
      code: 0,
      message: 'success',
    };
  }

  // 根据 APP 版本通知关闭灰度实验
  async libraCloseGrayNotify(
    appId: number,
    grayVersion: string,
    fullVersionDesc: string,
    versionStageName: string,
    startTime: number,
  ) {
    await this.libraNotifyService.closeGrayNotify(appId, grayVersion, fullVersionDesc, versionStageName, startTime);
    return {
      code: 0,
      message: 'success',
    };
  }

  // 根据 APP 版本通知开启正式实验
  async libraLaunchReleaseNotify(
    appId: number,
    releaseVersion: string,
    fullVersionDesc: string,
    versionStageName: string,
    startTime: number,
  ) {
    await this.libraNotifyService.launchReleaseNotify(
      appId,
      releaseVersion,
      fullVersionDesc,
      versionStageName,
      startTime,
    );
    return {
      code: 0,
      message: 'success',
    };
  }

  // 不再发送开启灰度实验提醒
  async forbidLibraLaunchGrayNotify(libraInfo: any) {
    await this.libraNotifyService.forbidLaunchGrayNotify(libraInfo);
    return {
      code: 0,
      message: 'success',
    };
  }

  // 不再发送关闭灰度实验提醒
  async forbidLibraCloseGrayNotify(libraInfo: any) {
    await this.libraNotifyService.forbidCloseGrayNotify(libraInfo);
    return {
      code: 0,
      message: 'success',
    };
  }
  // 一键创建正式实验
  async forbidLibraCreateRelease(info: any, messageId: string) {
    try {
      const { libraInfo } = info;
      console.log(`创建正式实验:${JSON.stringify(libraInfo)}`);
      const res = await this.libraNotifyService.createReleaseFromGray(
        libraInfo.flightInfo.id,
        libraInfo.flightInfo.owners[0].email,
        libraInfo.libraAppId,
      );
      if (res.code === 0) {
        console.log(`创建正式实验成功:${JSON.stringify(res)}`);
        const msg = useInject(LarkCardService).createLibraCloseGrayNotifyCard(info, LibraCreateState.Created);
        await useInject(LarkService).updateCard(JSON.stringify(msg), messageId);
      } else {
        console.log(`创建正式实验失败:${JSON.stringify(res)}`);
        const msg = useInject(LarkCardService).createLibraCloseGrayNotifyCard(info, LibraCreateState.Failed);
        await useInject(LarkService).updateCard(JSON.stringify(msg), messageId);
      }
    } catch (error) {
      console.log(`创建正式实验失败: ${error}`);
      const msg = useInject(LarkCardService).createLibraCloseGrayNotifyCard(info, LibraCreateState.Failed);
      await useInject(LarkService).updateCard(JSON.stringify(msg), messageId);
      return {
        code: -1,
        message: `创建正式实验失败: ${error}`,
      };
    }
  }
  async closeLibra(info: any, messageId: string) {
    console.log(`实验关闭:${JSON.stringify(info)}`);
    console.log(`实验关闭:${JSON.stringify(messageId)}`);
    const res = await closeFlightById({
      data: {
        flightId: info?.libraInfo.flightInfo.id.toString(),
        region: info?.libraInfo.flightInfo.region,
        operator: info?.libraInfo.flightInfo.owners[0].email?.split('@')[0],
      },
    });
    console.log(`实验关闭结果:${JSON.stringify(res)}`);
    // if (res.code === 0) {
    //   const msg = useInject(LarkCardService).createLibraCloseGrayNotifyCard(info, true, true);
    //   await useInject(LarkService).updateCard(JSON.stringify(msg), messageId);
    // } else {
    //   const msg = useInject(LarkCardService).createLibraCloseGrayNotifyCard(info, true, false);
    //   await useInject(LarkService).updateCard(JSON.stringify(msg), messageId);
    // }
    return res;
  }
  // 不再发送开启正式实验提醒
  async forbidLibraLaunchReleaseNotify(libraInfo: any) {
    await this.libraNotifyService.forbidLaunchReleaseNotify(libraInfo);
    return {
      code: 0,
      message: 'success',
    };
  }

  async forwardCNLibraEvent(data: any, eventbusId: string) {
    await this.eventBusHandler.didReceiveEvent(data, eventbusId);
  }

  async overdueLibraNotify(appId: number, version: string, meegoVersion: string, fullReleaseTime: number) {
    await this.libraNotifyService.overdueLibraNotify(appId, version, meegoVersion, fullReleaseTime);
  }

  async checkGrayLibraOk(storyId: number) {
    return this.libraControlService.checkGrayLibraOk(storyId);
  }

  async libraAttribution(dids: string, appid: number, isOverseas: boolean) {
    return await libraAttribution({ data: { dids, appid, isOverseas } });
  }

  async checkHasGrayLibra(meegoId: string) {
    return await this.libraControlService.checkHasGrayLibra(Number(meegoId));
  }

  async checkGrayLibra100Percent(meegoId: number) {
    return await this.libraControlService.checkGrayLibra100Percent(meegoId);
  }

  async storeLibraGray100PercentExemptForm(
    appId: number[],
    flightId: number,
    flightName: string,
    influence: string,
    exemptReason: string,
    improveMeasure: string,
    meegoId: number,
    meegoName: string,
    ownerEmail: string,
    version: string[],
  ) {
    return await this.libraPaControlRecordService.storeHostExemptRecord(
      appId,
      flightId,
      flightName,
      influence,
      exemptReason,
      improveMeasure,
      meegoId,
      meegoName,
      ownerEmail,
      version,
    );
  }

  async saveAbnormalFLightInfo(meegoId: number, flightId: number, type: number, version?: string, appId?: number) {
    return this.abnormalFlightReportInfoService.saveAbnormalFLightInfo(meegoId, flightId, type, version, appId);
  }

  async getMetricAlarmRecord(appId: number, version: string) {
    const rsp = await searchRecords({
      data: {
        appId: appId.toString(),
        platform: PlatformType.Android,
      },
    });
    if (rsp.code === 0) {
      return AlarmRecordParser.parse(rsp.data);
    }
    return;
  }

  async getBlameModelByStack(args: CustomAssignArgs, nativeStack: boolean): Promise<BlameModel> {
    return this.blameService.getBlameModelByStackReal(args, nativeStack);
  }

  async getLibraNewInfoList(query: unknown) {
    return this.libraNewInfoListService.listAll(query);
  }

  async libraNewInfoFindOne(query: unknown) {
    return this.libraNewInfoListService.findOne(query);
  }

  async createLibraNewInfo(region: number, app: number, flightId: number) {
    let innerRegion = LibraRegion.UNKNOWN;
    if (region === LibraRegion.CN) {
      innerRegion = LibraRegion.CN;
    } else if (region === LibraRegion.SG) {
      innerRegion = LibraRegion.SG;
    } else if (region === LibraRegion.VA) {
      innerRegion = LibraRegion.VA;
    }
    return this.libraNewInfoListService.createLibraNewInfo(innerRegion, app, flightId);
  }

  async getLibraPatrolConfig(appId: number, type: string, query?: unknown) {
    if (query) {
      return await this.libraPatrolConfigDao.list(query, 1, Number.MAX_SAFE_INTEGER);
    }
    return await this.libraPatrolConfigDao.listAll({
      libra_app_id: appId,
      metric_patrol_type: type,
    });
  }

  async getFlightInfoWithTime(libraId: number, version: string, minTimestamp: number, maxTimestamp: number) {
    return this.libraNewInfoListService.getFlightInfoWithTime(libraId, version, minTimestamp, maxTimestamp);
  }

  async getExperimentsByReleaseVersion(releaseVersion: string, appId?: number): Promise<LibraNewInfo[]> {
    const query: any = {
      'meegoInfo.releaseVersion': { $in: [releaseVersion] },
    };

    if (appId) {
      query.libraAppId = appId;
    }

    const experiments = await this.libraNewInfoListService.listAll(query);
    return experiments || [];
  }

  async getExperimentsByMeegoInfo(meegoId?: number, meegoName?: string): Promise<LibraNewInfo[]> {
    const query: any = {};

    if (meegoId) {
      query['meegoInfo.id'] = meegoId;
    }

    if (meegoName) {
      query['meegoInfo.name'] = { $regex: meegoName, $options: 'i' };
    }

    // 确保有meego信息
    query.meegoInfo = { $exists: true, $ne: [] };

    const experiments = await this.libraNewInfoListService.listAll(query);
    return experiments || [];
  }
  async getLibraVersionPlatform(flightId: number) {
    const query = {
      'flightInfo.id': flightId,
    };
    const flightInfo = await this.libraNewInfoListService.findOne(query);
    if (flightInfo) {
      return getLibraVersionPlatformFromFilterRule(
        flightInfo.flightInfo.filterRule,
        flightInfo.flightInfo.region,
        flightInfo.flightInfo.name,
        flightInfo.flightInfo.description,
      );
    }
    return {
      android: {
        isHit: false,
        minVersionCode: 0,
        version: '',
        isBeta: false,
      },
      iphone: {
        isHit: false,
        minVersionCode: 0,
        version: '',
        isBeta: false,
      },
    } as LibraVersionPlatform;
  }

  async updateCircuitAlarm(aid: number, os: string, ruleIdList: number[], versionCode: string) {
    return this.circuitService.updateCircuitAlarm(aid, os, ruleIdList, versionCode);
  }
}
