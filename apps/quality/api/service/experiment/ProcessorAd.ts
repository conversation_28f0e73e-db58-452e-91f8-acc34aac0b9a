import { AbstractProcessor } from './abstractProcessor';
import { ExperimentLTData, ExperimentLTTable, HandleStatus, IndicatorType, LTRequestInfo } from '@shared/libra/common';
import { DBExperimentListInfo, ExperimentListInfoTable } from '../../model/ExperimentListInfoTable';
import dayjs from 'dayjs';
import { PlatformType, User } from '@pa/shared/dist/src/core';
import { DBExperimentShareInfo } from '../../model/ExperimentShareInfoTable';
import { useInject } from '@edenx/runtime/bff';
import LarkService from '@pa/backend/dist/src/third/lark';
import {
  adMetricListForEn,
  adMetricListForNotEn,
  adRatios,
  getMectricAdForScene,
  inland_list,
  LibraPatrolConfig,
  metricAdSencesByLabel,
  metricAdTransFromToAirplane,
  patrolAppName,
} from '@shared/libra/LibraPatrolInfo';
import { LibraFlightInfo, LibraNewInfo } from '@shared/libra/LibraNewInfo';
import { FlightMetricConclusionParams } from '@shared/libra/FlightMetricConclusion';
import { current_region, Region } from '../../utils/region';
import { FlightReportRequest } from '@shared/libra/flight';
import {
  calculateStuckPriority,
  getBusinessMetricPriority,
  getBusinessRequestLevel,
  getHighPriority,
  getLibraBaseInfo,
  getMetricPriority,
} from '../libra/LibraPatrol/utils/CommonUtils';
import { FieldValue, FieldValuePair } from '@shared/typings/meego';
import { getMeegoInfo } from '@shared/libra/LibraCreate';
import { Injectable } from '@gulux/gulux';

interface ErrorDataItem {
  data: number;
  hasError: boolean;
  proportion: number;
}

interface AdMetricItem {
  value: number;
  p_value: {
    key: string;
    value: number;
  }[];
}

interface ErrorData {
  data: any;
  vId: string;
  scene: string;
}

interface ExperimentADData {
  vId: string;
  scenes?: string;
  // 广告营收
  ad_revenue: {
    data: number;
    hasError: boolean;
  };
  // ARPU($1/1000)
  ARPU: {
    data: number;
    hasError: boolean;
  };
  // 展示数
  show_count: {
    data: number;
    hasError: boolean;
  };
  CPM: {
    data: number;
    hasError: boolean;
  };
  // 请求量
  request_count: {
    data: number;
    hasError: boolean;
  };
  // 请求成功数
  request_success_count: {
    data: number;
    hasError: boolean;
  };
  // 点击数
  click_count: {
    data: number;
    hasError: boolean;
  };
  // 点击成功率
  request_count_rate: {
    data: number;
    hasError: boolean;
  };
  // 展示率
  show_rate: {
    data: number;
    hasError: boolean;
  };
  // 点击数
  click_count_rate: {
    data: number;
    hasError: boolean;
  };
}

type AdResultType = {
  [adScene: string]: {
    [vid: string]: {
      [metricGroupId: string]: {
        [metricName: string]: AdMetricItem;
      };
    };
  };
};
@Injectable()
export class ProcessorAd extends AbstractProcessor {
  async testPatrol(flightId: number, libraAppId: number) {
    const flight = (await this.rpcManager
      .getQuality(true)
      .getLibraNewInfoList({ 'flightInfo.id': flightId })) as LibraNewInfo[];
    // 测试代码
    // const flight = await this.libraNewInfoDao.listAll({ 'flightInfo.id': flightId });
    if (!flight || flight.length === 0) {
      this.logger.info(`testPatrol flight not found: ${flightId}`);
      return;
    }
    const whiteList = await this.getWhiteList();
    const adResult = await this.getAdMetric(libraAppId, [flight[0].flightInfo], whiteList);
    // for (const item of adResult) {
    //   for (const d of item.data) {
    //     d.scenes = '不限';
    //   }
    // }
    if (adResult.length > 0) {
      await this.rpcManager.getHost().addExperimentOtherInfo(adResult, IndicatorType.AD);
    }
  }
  async getExperimentPatrolData(appCheckList: number[]) {
    this.logger.info(`getExperimentPatrolData appCheckList: ${JSON.stringify(appCheckList)}`);
    const whiteList = await this.getWhiteList();
    for (const libraAppId of appCheckList) {
      const aName = patrolAppName[libraAppId];
      const flights = await this.libraAbService.getNeedCheckFlightList([1], libraAppId);
      if (flights.length === 0) {
        this.logger.info(`getExperimentPatrolData flights length === 0: ${libraAppId}`);
        continue;
      }
      const adResult = await this.getAdMetric(libraAppId, flights, whiteList);
      if (adResult.length > 0) {
        await this.rpcManager.getHost().addExperimentOtherInfo(adResult, IndicatorType.AD);
      }
    }
  }

  async getMetricResult(libraAppId: number, detail: LibraFlightInfo, metrics: LibraPatrolConfig[]) {
    const adResult: AdResultType = {};
    for (const metric of metrics) {
      let res;
      if (metric.dim_id && metric.dim_id > 0) {
        const req: FlightMetricConclusionParams = {
          start_date: '',
          end_date: '',
          view_type: 'merge',
          merge_type: 'total',
          mult_cmp_corr: 1,
          metric_group: metric.metric_group_id,
          dim: metric.dim_id,
          dim_vals: metric.dim_values.join(','),
          selected_metric_ids: '',
        };
        res = await this.getLibraMetricDataWithDim(
          inland_list.includes(libraAppId) ? 0 : 1,
          detail.id,
          libraAppId,
          req,
        );
      } else {
        const req: FlightReportRequest = {
          start_date: '',
          end_date: '',
          view_type: 'merge',
          merge_type: 'total',
          mult_cmp_corr: 1,
          metric_group: metric.metric_group_id,
        };
        res = await this.getLibraMetricData(inland_list.includes(libraAppId) ? 0 : 1, detail.id, libraAppId, req);
      }
      if (res && res.report_page_metrics) {
        for (const pageMetric of res.report_page_metrics) {
          const { vid } = pageMetric;
          const metricName = pageMetric.metric_name;
          const metricNameList = metric.metric_list.map(m => m.metric_name);
          if (metricNameList.includes(metricName) || metricNameList.includes(pageMetric.metric_description)) {
            const adScene = pageMetric.dim_value_name ?? 'all';
            const item: AdMetricItem = {
              value: pageMetric.value ?? -1,
              p_value: pageMetric.p_value ?? [],
            };
            if (!adResult[adScene]) {
              adResult[adScene] = {};
            }
            if (!adResult[adScene][vid]) {
              adResult[adScene][vid] = {};
            }
            if (!adResult[adScene][vid][metric.metric_group_id]) {
              adResult[adScene][vid][metric.metric_group_id] = {};
            }
            adResult[adScene][vid][metric.metric_group_id][pageMetric.metric_name] = item;
          }
        }
      }
    }
    return adResult;
  }

  getSceneKey(adScene: string) {
    if (adScene.includes('内容生态与分发')) {
      return 'ecology';
    }
    if (adScene.includes('视频工具与素材')) {
      return 'tool';
    }
    if (adScene.includes('图像工具')) {
      return 'tool';
    }
    if (adScene.includes('全局需求')) {
      return 'global';
    }
    return 'others';
  }

  async getAdMetric(libraAppId: number, details: LibraFlightInfo[], whiteList: { [name: string]: any }) {
    const metricIds = await this.getLibraPatrolConfig(libraAppId, IndicatorType.AD, PlatformType.Android);
    const uploadCoreList = [];
    for (const detail of details) {
      const adResult = await this.getMetricResult(libraAppId, detail, metricIds);
      const fidCoreMetricInfo = getLibraBaseInfo(libraAppId, detail);
      const vids = detail.versions.map(v => v.vid);
      if (vids.length <= 1) {
        continue;
      }
      const vCount = vids.length;
      const moduleResult = await this.getModule(detail);
      let sceneKey = this.getSceneKey(moduleResult.module);
      if (moduleResult.isAd) {
        sceneKey = 'ad';
      }
      this.logger.info(`Ad Patrol getAdMetric: ${sceneKey}`);
      const adSceneByLabel = metricAdSencesByLabel[libraAppId][sceneKey];
      for (const metricScene of adSceneByLabel) {
        let detailHasError = false;
        let priority = '否';
        // const duration = dayjs().diff(dayjs(detail.startTime), 'day');
        const currentTime = Date.now() / 1000;
        const duration = Math.floor((currentTime - detail.startTime) / (24 * 60 * 60));
        let compareVid = 0;
        let isCompareRecord = false;
        let compareData = {};
        for (let index = 0; index < vCount; index++) {
          const vid = vids[index];
          if (!adResult[metricScene]) {
            continue;
          }
          const metricResultForVid = adResult[metricScene][vid];
          if (index === 0) {
            compareVid = vid;
            compareData = metricResultForVid;
          } else {
            const checkResult = this.checkAdMetric(
              libraAppId,
              detail.id,
              vid,
              compareData,
              metricResultForVid,
              duration,
              metricIds,
            );
            if (checkResult.userMin !== -1 && checkResult.error) {
              const compareErrorData = {
                vId: compareVid.toString(),
                scenes: metricScene,
                ...checkResult.compareErrorData,
              };
              const errorData = {
                vId: vid.toString(),
                scenes: metricScene,
                ...checkResult.errorData,
              };
              if (checkResult.error) {
                detailHasError = true;
                priority = getHighPriority(checkResult.priority, priority);
                if (!isCompareRecord) {
                  fidCoreMetricInfo.data.push(compareErrorData);
                  isCompareRecord = true;
                }
                fidCoreMetricInfo.data.push(errorData);
              }
            }
          }
        }
        if (whiteList && whiteList.ad && whiteList.ad.includes(fidCoreMetricInfo.id)) {
          continue;
        }
        if (detailHasError) {
          fidCoreMetricInfo.priority = priority;
          uploadCoreList.push(fidCoreMetricInfo);
        }
      }
    }
    return uploadCoreList;
  }

  getPvalue(compare: AdMetricItem, vid: number): number {
    const pValueList = compare.p_value;
    for (const pValueDic of pValueList) {
      if (pValueDic.key === vid.toString()) {
        const pValue = pValueDic.value;
        return pValue ?? -999;
      }
    }
    return -999;
  }

  enoughToDiff(
    currentUser: number,
    compareUser: number,
    userMin: number,
    metricName: string,
    currentMetric: { [key: string]: { value: number } },
    compareMetric: { [key: string]: { value: number } },
  ): boolean {
    // total_ad_request = current_user * current_request_count[0] + compare_user * compare_request_count[0]
    if (metricName.includes('广告核心指标组CUPED')) {
      // 各实验组进组人数大于10000，各组用户数*请求量大于3000
      const currentRequestCount = Object.entries(currentMetric)
        .filter(([key]) => key.startsWith('请求量'))
        .map(([, value]) => value.value);
      const compareRequestCount = Object.entries(compareMetric)
        .filter(([key]) => key.startsWith('请求量'))
        .map(([, value]) => value.value);

      if (currentRequestCount.length === 0 || compareRequestCount.length === 0) {
        return false;
      }

      return (
        userMin > 50000 && currentUser * currentRequestCount[0] > 3000 && compareUser * compareRequestCount[0] > 3000
      );
    } else {
      return userMin > 50000;
    }
  }

  initAdErrorData(aid: number, metricIds: LibraPatrolConfig[]) {
    const errorInit: ErrorDataItem = {
      data: 0,
      hasError: false,
      proportion: 0,
    };
    const errorData: { [key: string]: ErrorDataItem } = {};
    const compareErrorData: { [key: string]: ErrorDataItem } = {};

    const adMetricList = metricIds.map(metric => metric.metric_list.map(metric2 => metric2.metric_name)).flat();

    if (adMetricList) {
      adMetricList.forEach(metricName => {
        // TypeScript 中对象复制可以使用展开运算符
        errorData[metricName] = { ...errorInit };
        compareErrorData[metricName] = { ...errorInit };
      });
    }

    return [errorData, compareErrorData];
  }

  checkAdMetric(
    aid: number,
    fid: number,
    vid: number,
    compare: any,
    current: any,
    duration: number,
    metricIds: LibraPatrolConfig[],
  ): {
    text: string;
    total: number;
    error: boolean;
    priority: string;
    errorData: { [key: string]: ErrorDataItem };
    compareErrorData: { [key: string]: ErrorDataItem };
    userMin: number;
  } {
    let total = 0;
    let isAdError = false;
    const errorSheetInfo: { [key: string]: string } = {};
    let priority = '否';
    const initData = this.initAdErrorData(aid, metricIds);
    const errorDataMap = initData[0];
    const compareErrorMap = initData[1];
    let userMin = 0;

    for (let i = 0; i < metricIds.length; i++) {
      const metric = metricIds[i];
      const metricId = metric.metric_group_id;
      const metricGroupName = metric.metric_group_name;
      let currentMetric, compareMetric;
      if (current[metricId] && compare[metricId]) {
        currentMetric = current[metricId];
        compareMetric = compare[metricId];
        const currentUser = currentMetric.User?.value || 0;
        const compareUser = compareMetric.User?.value || 0;
        userMin = Math.min(currentUser, compareUser);

        if (!this.enoughToDiff(currentUser, compareUser, userMin, metricGroupName, currentMetric, compareMetric)) {
          continue;
        }

        const filteredCurrentMetric: { [key: string]: { value: number } } = {};
        for (const key in currentMetric) {
          if (getMectricAdForScene(metricGroupName)?.includes(key)) {
            filteredCurrentMetric[key] = currentMetric[key];
          }
        }

        for (const key in filteredCurrentMetric) {
          const currentValue = currentMetric[key].value;
          const compareValue = compareMetric[key].value;

          if (!compareValue || !currentValue) {
            continue;
          }

          const value = currentValue - compareValue;
          const pValue = this.getPvalue(compareMetric[key], vid);
          const metricWithKey = metric.metric_list.find((m: any) => m.metric_name === key);
          let isGetWorse = false;
          if (metricWithKey) {
            if (metricWithKey.is_reversed && value > 0) {
              isGetWorse = true;
            } else if (!metricWithKey.is_reversed && value < 0) {
              isGetWorse = true;
            }
          }
          if (!isGetWorse) {
            continue;
          }
          const proportion = Math.abs(value / compareValue);
          let level = getMetricPriority(aid, value, userMin);
          if (proportion > 0.2) {
            level = 'P1';
          }
          if (proportion > 0.3) {
            level = 'P0';
          }

          if (metricWithKey) {
            level = getBusinessMetricPriority(metricWithKey, proportion, level);
          }
          if (pValue !== -999 && pValue < 0.05) {
            if (value !== 0 && proportion > adRatios) {
              total++;

              if (metricGroupName in errorSheetInfo) {
                continue;
              } else {
                errorSheetInfo[key] = `${metricGroupName}_${proportion}; `;
              }
              if (metricWithKey) {
                level = getBusinessRequestLevel(metricWithKey, proportion, level);
              }
              priority = getHighPriority(level, priority);
              isAdError = true;
              errorDataMap[key].hasError = true;
            }
          }
        }
      } else {
        continue;
      }

      const metricsList = metricIds[i].metric_list;
      const filteredMetricsList = metricsList.filter(item => item.metric_name !== 'User').map(item => item.metric_name);

      if (isAdError) {
        for (const metricAd of filteredMetricsList) {
          errorDataMap[metricAd].data = currentMetric[metricAd]?.value || 0;
          compareErrorMap[metricAd].data = compareMetric[metricAd]?.value || 0;
        }
      }
    }

    let text = `${vid}异常总数：${total}; `;
    for (const key in errorSheetInfo) {
      text += `${key}异常：${errorSheetInfo[key]}; `;
    }

    return {
      text,
      total,
      error: isAdError,
      priority,
      errorData: errorDataMap,
      compareErrorData: compareErrorMap,
      userMin,
    };
  }

  isAdMeego(infoValue: FieldValuePair) {
    if (!infoValue) {
      return false;
    }
    const fieldValue = infoValue.field_value;
    if (!Array.isArray(fieldValue)) {
      return false;
    }
    const value = fieldValue[0] as FieldValue;
    if (value) {
      if (value.label === '商业化' && value.children && value.children.label === '广告') {
        return true;
      }
    }
    return false;
  }

  async getModule(detail: LibraFlightInfo) {
    let libraInfo = await this.libraNewInfoDao.findById(detail.id.toString());
    if (current_region() === Region.CN) {
      const res = (await this.rpcManager
        .getQuality(true)
        .getLibraNewInfoList({ 'flightInfo.id': detail.id })) as LibraNewInfo[];
      if (res && res.length > 0) {
        libraInfo = res[0];
      }
    }
    const result = {
      module: 'other',
      isAd: false,
    };
    if (libraInfo && libraInfo.meegoInfo && libraInfo.meegoInfo.length > 0) {
      const meegoId = libraInfo.meegoInfo[0].id;
      this.logger.info(`Ad Patrol getModule, meegoId: ${meegoId}`);
      const meegoInfo = await this.meegoService.requestWorkItem('faceu', 'story', [meegoId]);
      this.logger.info(`Ad Patrol getModule, ${JSON.stringify(meegoInfo)}`);
      if (meegoInfo && meegoInfo.data && meegoInfo.data.length > 0) {
        const meegoDetail = meegoInfo.data[0];
        const businessInfo = getMeegoInfo(meegoInfo.data[0]);
        const businessField = '';
        const filteredFields = meegoDetail.fields.find(dict => dict.field_key === 'field_527021');
        if (filteredFields) {
          const module = (filteredFields.field_value as FieldValue[])[0].label;
          result.isAd = this.isAdMeego(filteredFields);
          result.module = module;
        }
      }
    }
    this.logger.info(`Ad Patrol getModule, ${JSON.stringify(result)}`);
    return result;
  }
  async addExperimentInfo(infos: LTRequestInfo[]) {}
}
