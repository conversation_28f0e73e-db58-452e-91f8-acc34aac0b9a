import { TeaSDK } from '@dp/tea-sdk-node';
import { httpPlugin } from '@logsdk/node-plugin-http';

export const tea = new TeaSDK({
  app_id: 517881,
  logLevel: 'verbose',
});
const httpInstance = httpPlugin({
  channel: 'cn',
  // 线下验证数据时使用
  // event_verify_url: 'https://mcs.bytedance.net/v1/list_test',
});
httpInstance.on('end', ({ lastEvents, hasSucceed }) => {
  console.log(`tea upload one：${hasSucceed}`);
});
httpInstance.on('end', ({ event, hasSucceed }) => {
  console.log(`tea upload all finish：${hasSucceed}`);
});
tea.use(httpInstance);

export const TeaEvent = {
  AUTO_FLOW: 'AUTO_FLOW',
  FEATURE_FINISH: 'FEATURE_FINISH',
  MULTI_LANGUAGE: 'multi_language_update',
  APPROVAL: 'APPROVAL',
  BOT_INTERACT_LLM: 'BOT_INTERACT_LLM',
  BOT_INTERACT: 'BOT_INTERACT',
  CONFILICT_CI: 'conflict_ci',
  SHOW_VERSION_STAGE: 'show_version_stage',
  PUSH_VERSION_MSG: 'push_version_msg',
};

export function teaCollect(eventName: string, params: any) {
  tea.collect(eventName, params, {
    user: {
      user_unique_id: 'tanhaiyang',
    },
  });
}

export function teaCollectWithUser(eventName: string, params: any, user: string) {
  tea.collect(eventName, params, {
    user: {
      user_unique_id: user,
    },
  });
}
