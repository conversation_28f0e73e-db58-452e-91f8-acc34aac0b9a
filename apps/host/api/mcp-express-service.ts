import { Injectable, Inject } from '@gulux/gulux';
import { BytedLogger } from '@gulux/gulux/byted-logger';
import { randomUUID } from 'node:crypto';
import { Server } from '@modelcontextprotocol/sdk/server/index.js';
import { StreamableHTTPServerTransport } from '@modelcontextprotocol/sdk/server/streamableHttp.js';
import {
  isInitializeRequest,
  CallToolRequestSchema,
  ListToolsRequestSchema,
  ListPromptsRequestSchema,
  GetPromptRequestSchema,
} from '@modelcontextprotocol/sdk/types.js';
import McpToolHandlers from './service/mcp/McpToolHandlers';
import { useInject } from '@edenx/runtime/bff';
import { McpToolRsp } from '@shared/mcp/mcp-info';

/**
 * MCP-Express服务类
 * 用于创建和管理Express应用
 */
@Injectable()
export default class McpExpressService {
  private static instance: McpExpressService;
  // Map to store transports by session ID
  private transports: { [sessionId: string]: StreamableHTTPServerTransport } = {};
  // Tool 调用结果存储
  private toolCallResults: { [mcpCallId: string]: McpToolRsp } = {};
  private mcpToolHandlers: McpToolHandlers;
  private logger: BytedLogger;

  static getInstance(): McpExpressService {
    if (!McpExpressService.instance) {
      McpExpressService.instance = new McpExpressService();
    }
    return McpExpressService.instance;
  }

  constructor() {
    this.transports = {};
    this.toolCallResults = {};

    this.logger = useInject(BytedLogger);
    this.mcpToolHandlers = useInject(McpToolHandlers);
  }

  // Reusable handler for GET and DELETE requests
  handleSessionRequest = async (req: any, res: any) => {
    const sessionId = req.headers['mcp-session-id'] as string | undefined;
    if (!sessionId || !this.transports[sessionId]) {
      // res.status(400).send('Invalid or missing session ID');
      res.writeHead(400, { 'Content-Type': 'text/plain' });
      res.end('Invalid or missing session ID');
      return;
    }

    const transport = this.transports[sessionId];
    await transport.handleRequest(req, res);
  };

  // Handle Tool-Call ahead
  handleToolCallAhead = async (req: any, res: any, body: any, mcpToolsCallId: string) => {
    if ('method' in body && body.method === 'tools/call') {
      // 说明是 tools 调用，预先做好结果查询
      const { params } = body;
      if (params) {
        const { name } = params;
        const parseArguments = params.arguments;
        if (name && parseArguments) {
          // 给 args 塞一下 callKey
          parseArguments._mcpToolsCallId = mcpToolsCallId;
          const result = await this.mcpToolHandlers.didHandle(name, parseArguments);
          // 记录一下结果
          this.toolCallResults[mcpToolsCallId] = result;
        }
      }
    }
  };

  // Return Tool-Call result
  returnToolCallResult = (requestCallback: any) => {
    try {
      const toolName = (requestCallback.params.name as string) ?? 'unknown-tool';
      const args = requestCallback.params.arguments;
      if (args && '_mcpToolsCallId' in args) {
        const callId = args._mcpToolsCallId;
        const result = this.toolCallResults[callId];
        if (result) {
          // 删除掉 callId
          delete this.toolCallResults[callId];
          return result;
        }
      }
      return {
        content: [
          {
            type: 'text',
            text: `mcp tool execute failed! [${toolName}] undefined result`,
          },
        ],
      };
    } catch (e) {
      this.logger.error(`mcp tool 执行失败: ${e}`);
      return {
        content: [
          {
            type: 'text',
            text: `mcp tool execute failed! error: ${e}`,
          },
        ],
      };
    }
  };

  // Handle POST requests for client-to-server communication
  handleMcpPost = async (req: any, res: any, body: any, mcpCallId: string) => {
    // Handle POST requests for client-to-server communication
    // Check for existing session ID
    const sessionId = req.headers['mcp-session-id'] as string | undefined;
    let transport: StreamableHTTPServerTransport;

    if (sessionId && this.transports[sessionId]) {
      // Reuse existing transport
      transport = this.transports[sessionId];
    } else if (!sessionId && isInitializeRequest(body)) {
      // New initialization request
      transport = new StreamableHTTPServerTransport({
        sessionIdGenerator: () => randomUUID(),
        onsessioninitialized: tmpSessionId => {
          // Store the transport by session ID
          this.transports[tmpSessionId] = transport;
        },
      });
      // const mySessionId = randomUUID();
      // // New initialization request
      // transport = new StreamableHTTPServerTransport({
      //   sessionIdGenerator: () => mySessionId,
      // });
      // // 同步存储 transport
      // this.transports[mySessionId] = transport;
      //
      // // 设置 sessionId，假设 StreamableHTTPServerTransport 有对应的属性或方法
      // // 若有 setSessionId 方法则使用该方法，若为属性则直接赋值
      // if ('setSessionId' in transport) {
      //   (transport as any).setSessionId(mySessionId);
      // } else {
      //   (transport as any).sessionId = mySessionId;
      // }

      // Clean up transport when closed
      transport.onclose = () => {
        if (transport.sessionId) {
          delete this.transports[transport.sessionId];
        }
      };
      const server = new Server(
        {
          name: '纸飞机', // 纸飞机 MCP 服务名
          version: '1.0.0',
        },
        {
          capabilities: {
            tools: {},
          },
          instructions: '纸飞机研效平台智能助手(pa.bytedance.net)，提供版本查询，实验管理，MR 助手，代码分析等功能。',
        },
      );

      // Define available tools
      const allTools = this.mcpToolHandlers.allToolDefinitions();
      server.setRequestHandler(ListToolsRequestSchema, async () => ({
        tools: allTools,
      }));

      // Handle tool execution
      // @ts-ignore
      server.setRequestHandler(CallToolRequestSchema, async (request: any) => this.returnToolCallResult(request));

      // Connect to the MCP server
      await server.connect(transport);
    } else {
      // Invalid request
      res.writeHead(400, { 'Content-Type': 'application/json' });
      res.end(
        JSON.stringify({
          jsonrpc: '2.0',
          error: {
            code: -32000,
            message: 'Bad Request: No valid session ID provided',
          },
          id: null,
        }),
      );
      return;
    }

    // Handle the request
    try {
      await this.handleToolCallAhead(req, res, body, mcpCallId);
      await transport.handleRequest(req, res, body);
    } catch (reqError) {
      this.logger.error(`处理请求时出错: ${reqError}`);
      res.writeHead(500, { 'Content-Type': 'application/json' });
      res.end(
        JSON.stringify({
          jsonrpc: '2.0',
          error: {
            code: -32001,
            message: 'Internal Server Error',
          },
          id: null,
        }),
      );
    }
  };
}
