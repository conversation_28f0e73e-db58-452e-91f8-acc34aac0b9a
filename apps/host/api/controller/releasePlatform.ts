import { Body, Controller, Get, Post, Query } from '@gulux/application-http';
import { Inject } from '@gulux/gulux';
import { VersionStageCheckListService } from '../service/releasePlatform/versionStageCheckListService';
import VersionStageCheckListDao, {
  VersionStageCheckListItemQuery,
} from '../service/dao/releasePlatform/VersionStageCheckListDao';
import { CheckItemStatus, ItemType, VersionStageCheckItem } from '@shared/releasePlatform/versionStageInfoCheckList';
import { BytedLogger } from '@gulux/gulux/byted-logger';
import VersionReleaseService from '../service/releasePlatform/versionReleaseService';
import { ProductType } from '@shared/process/versionProcess';
import TestFlightStageService from '../service/releasePlatform/stageServices/testFlightStageService';
import { VersionProcessInfoService } from '../service/releasePlatform/versionProcessInfoService';
import VersionProcessInfoDao from '../service/dao/releasePlatform/VersionProcessInfoDao';
import VersionUtilService from '../service/utils/VersionUtilService';
import { ReleasePlatformUtilService } from '../service/releasePlatform/releasePlatformUtil';
import BugResolveRatioCheckItemAgent from '../service/releasePlatform/checkItemAgents/bugResolveRatioCheckItemAgent';
import LarkService from '@pa/backend/dist/src/third/lark';
import FunctionalBugCheckItemAgent from '../service/releasePlatform/checkItemAgents/functionalBugCheckItemAgent';
import { VersionStageChecklistStatus } from '@shared/releasePlatform/versionStage';
import { getOnProgressStages } from '@shared/releasePlatform/releasePlatformUtils';
import MetricCheckItemAgent from '../service/releasePlatform/checkItemAgents/metricCheckItemAgent';
import BusinessConfigService from '@pa/backend/dist/src/service/businessConfig';
import { MeegoVersionWriteBackService } from '../service/handler/bits/MeegoVersionWriteBackService';
import { Api, useInject } from '@edenx/runtime/bff';
import VersionFeatureService from '../service/releasePlatform/versionFeatureService';
import { ReleasePlatformMsgRecordDao } from '../service/dao/releasePlatform/ReleasePlatformMsgRecordDao';
import RpcProxyManager from '@pa/backend/dist/src/rpc/proxy';
import { CircuitBreakerTicketType } from '@shared/circuitBreaker/circuitBreakerTicket';
import CircuitBreakerService from '../service/circuitBreaker/circuitBreakerService';

@Controller('')
export default class ReleasePlatformController {
  @Inject()
  private checkListService: VersionStageCheckListService;

  @Inject()
  private logger: BytedLogger;

  @Inject()
  private lark: LarkService;

  @Inject()
  private versionReleaseService: VersionReleaseService;

  @Inject()
  private testFlightService: TestFlightStageService;

  @Inject()
  private businessConfigService: BusinessConfigService;

  @Inject()
  private versionInfoService: VersionProcessInfoService;

  @Inject()
  private versionInfoDao: VersionProcessInfoDao;

  @Inject()
  private versionUtil: VersionUtilService;

  @Inject()
  private checklistDao: VersionStageCheckListDao;

  @Inject()
  private releasePlatformUtil: ReleasePlatformUtilService;

  @Inject()
  protected bugResolveAgent: BugResolveRatioCheckItemAgent;

  @Inject()
  private functionalBugAgent: FunctionalBugCheckItemAgent;

  @Inject()
  private metricCheckItemAgent: MetricCheckItemAgent;

  @Inject()
  private meegoVersionWriteBackService: MeegoVersionWriteBackService;

  @Inject()
  private releasePlatformMsgRecordDao: ReleasePlatformMsgRecordDao;

  @Inject()
  private rpcService: RpcProxyManager;

  @Inject()
  private circuitBreakerService: CircuitBreakerService;

  @Inject()
  private versionFeatureService: VersionFeatureService;

  @Post('/release_platform_api/update_version_info')
  async updateVersionInfo(@Body() params: { app_id: number; version: string }) {
    this.logger.info(`[Release Platform][createVersionInfo] params: ${params}`);
    const appInfo = await this.businessConfigService.appID2AppInfo(params.app_id);
    if (appInfo) {
      const versionInfo = await this.versionInfoService.forceUpdateVersionInfo(appInfo, params.version);
      this.logger.info(`[Release Platform][createVersionInfo] info: ${versionInfo}`);
    }
  }
  @Post('/open/release_platform_api/update_version_bm_info')
  async updateVersionBMInfo(@Body() params: { app_id: number; version: string }) {
    this.logger.info(`[Release Platform][updateVersionBMInfo] params: ${params}`);
    const appInfo = await this.businessConfigService.appID2AppInfo(params.app_id);
    if (appInfo) {
      const versionInfo = await this.versionInfoService.updateBMVersionInfo(appInfo, params.version);
      this.logger.info(`[Release Platform][updateVersionBMInfo] info: ${versionInfo}`);
    }
    return {
      code: 0,
      message: 'success',
    };
  }
  @Post('/release_platform_api/create_version_info')
  async createVersionInfo(@Body() params: { app_id: number; version: string }) {
    this.logger.info(`[Release Platform][createVersionInfo] params: ${params}`);
    const appInfo = await this.businessConfigService.appID2AppInfo(params.app_id);
    if (appInfo) {
      const versionInfo = await this.versionInfoService.createNewVersionV2(appInfo, params.version);
      this.logger.info(`[Release Platform][createVersionInfo] info: ${versionInfo}`);
    }
  }

  @Post('/open/release_platform_api/create_version_info_with_time')
  async createVersionInfoWithTime(@Body() params: { app_id: number; version: string; timestamp: number }) {
    this.logger.info(`[Release Platform][create_version_info_with_time] params: ${JSON.stringify(params)}`);
    const appInfo = await this.businessConfigService.appID2AppInfo(params.app_id);
    if (appInfo) {
      const versionInfo = await this.versionInfoService.createNewVersionV2(appInfo, params.version, params.timestamp);
      this.logger.info(`[Release Platform][create_version_info_with_time] info: ${JSON.stringify(versionInfo)}`);
    }
  }

  @Post('/release_platform_api/braodcast_version_status')
  async broadcastVersionStatus(@Body() params: { app_id: number; version: string }) {
    this.logger.info('[Release Platform] start broadcast versions status');
    await this.versionReleaseService.triggerVersionStatusBoradCast();
  }

  @Post('/release_platform_api/update_version_inhouse')
  async updateVersionInhouse() {
    // this.logger.info(`[Release Platform][createVersionInfo] params: ${params}`);
    const versionInfos = await this.versionInfoDao.findAllVersion();
    if (!versionInfos) {
      return;
    }
    for (const versionInfo of versionInfos) {
      versionInfo.is_inhouse = 0;
      await this.versionInfoDao.updateByCriteria(
        { app_id: versionInfo.app_id, version: versionInfo.version },
        versionInfo,
      );
    }
  }

  @Post('/release_platform_api/delete_version_info')
  async deleteVersionInfo(@Body() params: { app_id: number; version: string }) {
    this.logger.info(`[Release Platform][deleteVersionInfo] params: ${params}`);
    await this.versionInfoDao.deleteByCriteria({
      version: params.version,
      app_id: params.app_id,
    });
  }

  @Post('/release_platform_api/find_checklist')
  async findChecklist(@Body() params: { app_id: number; version: string; stage: string }) {
    const query: VersionStageCheckListItemQuery = {
      app_id: params.app_id,
      version: params.version,
      stage: params.stage,
    };
    return await this.checkListService.getVersionStageCheckList(query);
  }

  @Post('/release_platform_api/delete_checklist')
  async deleteChecklist(@Body() params: { app_id: number; version: string; stage: string }) {
    const query: VersionStageCheckListItemQuery = {
      app_id: params.app_id,
      version: params.version,
      stage: params.stage,
    };
    return await this.checklistDao.delete(query);
  }

  @Post('/release_platform_api/update_check_item')
  async updateCheckItem(
    @Body()
    params: {
      app_id: number;
      version: string;
      stage: string;
      check_item_id: string;
      update_data: VersionStageCheckItem;
    },
  ) {
    try {
      await this.checkListService.updateCheckItem(
        params.app_id,
        params.version,
        params.stage,
        params.check_item_id,
        params.update_data,
      );
    } catch (e) {
      return {
        code: -1,
        message: 'failed',
      };
    }
    return {
      code: 0,
      message: 'success',
    };
  }

  @Post('/release_platform_api/check_version_status')
  async checkVersionStatus() {
    this.logger.info('[Release Platform] start check versions status');
    await this.versionReleaseService.findProgressVersionAndCheckStatus();
  }

  @Post('/open/release_platform_api/check_smallFlow_release')
  async checkAppStorePackageRelease() {
    this.logger.info('[Release Platform] start check versions status');
    await this.versionReleaseService.checkPackageRelease();
    return 0;
  }

  @Post('/open/release_platform_api/check_integration_test_package')
  async checkIntegrationTestPackageResult() {
    this.logger.info('[Release Platform] check_integration_test_package');
    await this.versionReleaseService.checkIntegrationPackageResult();
    return 0;
  }

  @Post('/release_platform_api/check_specific_version_status')
  async checkSpecificVersionStatus(@Body() params: { version: string; app_id: number; refresh: number }) {
    this.logger.info(
      `[Release Platform] [checkSpecificVersionStatus] app id: ${params.app_id}, version: ${params.version}`,
    );
    const versionInfo = await this.versionInfoDao.findOneByCriteria({ app_id: params.app_id, version: params.version });
    if (versionInfo) {
      try {
        await this.versionReleaseService.checkVersionProcess(versionInfo, params.refresh === 1);
      } catch (e) {
        console.log(e);
      }
    }
  }

  @Post('/release_platform_api/turn_version_next_stage')
  async turnVersionToNextStage(@Body() params: { version: string; app_id: number; stage: string }) {
    this.logger.info(`[Release Platform] [turnVersionToNextStage] params: ${params}`);
    await this.versionReleaseService.completeStage(params.app_id, params.version, params.stage);
  }

  @Post('/release_platform_api/test_flight_released')
  async testFlightReleased(
    @Body() params: { version: string; platform: string; product: ProductType; versionCode: string },
  ) {
    this.logger.info(`[Release Platform] [testFlightReleased] params: ${params}`);
    await this.testFlightService.testFlightReleased(
      params.version,
      params.platform,
      params.product,
      params.versionCode,
    );
  }

  @Post('/release_platform_api/exempt_business_bug')
  async exemptBusinessBug(
    @Body()
    params: {
      issue_id: number;
      business_name: string;
      app_id: number;
      version: string;
      stage: string;
      email: string;
      approve: boolean;
    },
  ) {
    const userInfo = await this.lark.getUserInfoByEmail(params.email);
    if (!userInfo) {
      return;
    }
    await this.bugResolveAgent.businessSingleBugExemptApprovedImpl(
      params.issue_id,
      params.business_name,
      params.app_id,
      params.version,
      params.stage,
      userInfo,
      params.approve,
    );
  }

  @Post('/release_platform_api/exempt_bug_item')
  async exemptBugItem(
    @Body()
    params: {
      issue_id: number;
      app_id: number;
      version: string;
      stage: string;
      email: string;
      approve: boolean;
    },
  ) {
    const userInfo = await this.lark.getUserInfoByEmail(params.email);
    if (!userInfo) {
      return;
    }
    await this.functionalBugAgent.bugExemptApproveImpl(
      params.issue_id,
      params.app_id,
      params.version,
      params.stage,
      userInfo,
      params.approve,
    );
  }

  @Post('/release_platform_api/full_release_pass')
  async fullReleasePass(
    @Body()
    params: {
      app_id: number;
      version: string;
      approve: boolean;
      small_flow_pass: boolean;
    },
  ) {
    await this.versionReleaseService.fullReleaseCheckApproveImpl(
      params.app_id,
      params.version,
      params.approve,
      params.small_flow_pass,
    );
  }

  @Post('/release_platform_api/fetch_onprogress_version_and_checklist_by_name')
  async fetchOnProgressVersionAndChecklistByName(
    @Body()
    params: {
      app_name: string;
      platform: string;
    },
  ) {
    const appInfoList = await this.businessConfigService.getAppList();
    const appInfo = appInfoList.find(it => it.app_name === params.app_name && it.platform === params.platform);
    if (!appInfo) {
      return [];
    }
    const onProgressVersionInfos = await this.fetchOnProgressVersionAndChecklist({
      app_id: appInfo.app_id,
    });
    return { version_status: onProgressVersionInfos };
  }

  @Post('/release_platform_api/fetch_onprogress_version_and_checklist')
  async fetchOnProgressVersionAndChecklist(
    @Body()
    params: {
      app_id: number;
    },
  ) {
    const onProgressVersionInfos = await this.versionInfoDao.findOnProgressVersions(params.app_id);
    const checklistStatusList: VersionStageChecklistStatus[] = [];
    if (!onProgressVersionInfos) {
      return checklistStatusList;
    }
    for (const onProgressVersionInfo of onProgressVersionInfos) {
      const onProgressStages = getOnProgressStages(onProgressVersionInfo.version_stages);
      const checklistStatus: VersionStageChecklistStatus = {
        versionInfo: onProgressVersionInfo,
        stageCheckStatus: [],
      };
      for (const onProgressStage of onProgressStages) {
        const checklist = await this.checklistDao.getVersionStageCheckList(
          onProgressVersionInfo.version,
          onProgressStage.stage_name,
          onProgressVersionInfo.app_id,
        );
        if (checklist) {
          checklistStatus.stageCheckStatus.push({ stageInfo: onProgressStage, checklist });
        }
      }
      if (checklistStatus.stageCheckStatus.length > 0) {
        checklistStatusList.push(checklistStatus);
      }
    }
    return checklistStatusList;
  }

  @Post('/release_platform_api/metric_approval')
  async metric_approval(
    @Body()
    params: {
      app_id: number;
      version: string;
      approve: boolean;
      small_flow_pass: boolean;
    },
  ) {
    await this.metricCheckItemAgent.metric_approval(params.app_id);
  }

  @Get('open/release_platform_api/bits_pre_check')
  async bitsPreCheck(
    @Query()
    params: {
      appCloudId: string;
      technologyStack: number;
      version: string;
      stage: string;
      pipeline_exec_id: number;
      workflow_exec_id: number;
      pipeline_job_id: number;
      workflow_job_id: number;
      bits_app_id: number;
    },
  ) {
    this.logger.info(`[Release Platform] [bitsPreCheck] params: ${params}`);
    // TODO 测试代码
    return {
      Access: false,
    };
    const releasePlatformStageName = this.releasePlatformUtil.convertBitsStageToReleasePlatformStage(params.stage);
    const checklist = await this.checkListService.getVersionStageCheckList({
      app_id: params.bits_app_id,
      version: params.version,
      stage: releasePlatformStageName,
    });
    let access = false;
    if (!checklist) {
      this.logger.info(`[Release Platform] [bitsPreCheck] checklist is empty`);
      return {
        Access: access,
      };
    }
    const preCheckItems = checklist!.check_items.filter(item => item.item_type !== ItemType.BitsAfterCheck);
    access = preCheckItems.every(item => item.status === CheckItemStatus.Exempt);
    return {
      Access: access,
    };
  }

  @Post('open/release_platform_api/get_flight_start_after_code_frozen')
  async getFlightStartAfterCodeFrozen(
    @Body()
    params: {
      app_id: number;
      version: string;
      startTime: number;
      endTime: number;
    },
  ) {
    const versionInfo = await this.versionInfoDao.findOneByCriteria({
      app_id: params.app_id,
      version: params.version,
    });
    if (!versionInfo) {
      return {
        code: -1,
        message: 'version not found',
      };
    }
    const codeFrozenStage = versionInfo.version_stages.find(it => it.stage_name === 'integration');
    if (!codeFrozenStage) {
      return {
        code: -1,
        message: 'code frozen stage not found',
      };
    }
    const codeFrozenStageEndTime = codeFrozenStage.end_time + 10 * 60 * 1000;
    const flights = await this.rpcService
      .getQuality(true)
      .getFlightInfoWithTime(params.app_id, params.version, params.startTime, params.endTime);
    return {
      code: 0,
      message: 'success',
      data: flights,
    };
  }

  @Get('open/release_platform_api/bits_after_check')
  async bitsAfterCheck(
    @Query()
    params: {
      appCloudId: string;
      technologyStack: number;
      version: string;
      stage: string;
      pipeline_exec_id: number;
      workflow_exec_id: number;
      pipeline_job_id: number;
      workflow_job_id: number;
      bits_app_id: number;
    },
  ) {
    this.logger.info(`[Release Platform] [bitsPreCheck] params: ${params}`);
    const releasePlatformStageName = this.releasePlatformUtil.convertBitsStageToReleasePlatformStage(params.stage);
    const checklist = await this.checkListService.getVersionStageCheckList({
      app_id: params.bits_app_id,
      version: params.version,
      stage: releasePlatformStageName,
    });
    let access = false;
    if (!checklist) {
      this.logger.info(`[Release Platform] [bitsPreCheck] checklist is empty`);
      return {
        Access: access,
      };
    }
    const afterCheckItems = checklist.check_items.filter(item => item.item_type === ItemType.BitsAfterCheck);
    access = afterCheckItems.every(item => item.status === CheckItemStatus.Exempt);
    return {
      Access: access,
    };
  }

  @Get('/open/release_platform_api/measure')
  async releasePlatformMeasure(
    @Query()
    params: {
      measure_type: string;
    },
  ) {
    switch (params.measure_type) {
      case 'control_measure':
        return {
          code: 0,
          ret: await this.versionReleaseService.accessControlMeasure(),
        };
      case 'message_measure':
        const msgRecords = await this.releasePlatformMsgRecordDao.findMsgRecordsNeedMeasure();
        return {
          code: 0,
          ret: msgRecords,
        };
      default:
        return {
          code: -1,
          ret: 'not support measure',
        };
    }
  }

  @Get('/open/release_platform_api/aeolus/circuitBreakerData')
  async getFeedbackCircuitBreakerData(@Query() params: { type: string }) {
    switch (params.type) {
      case CircuitBreakerTicketType.Feedback.toString():
        return await this.circuitBreakerService.getAllCircuitBreakerRecords(CircuitBreakerTicketType.Feedback);
      case CircuitBreakerTicketType.Slardar.toString():
        return await this.circuitBreakerService.getAllCircuitBreakerRecords(CircuitBreakerTicketType.Slardar);
      default:
        return { code: -1, ret: 'error type' };
    }
  }

  /**
   * 测试商业化广告子仓分支拉出通知接口
   */
  @Post('/open/release_platform_api/test_unified_ad_checkout')
  async testUnifiedAdCheckOut(@Body() params: { version: string; isOverSea?: boolean; isRetouch?: boolean }) {
    try {
      this.logger.info(`[Test Unified Ad CheckOut] params: ${JSON.stringify(params)}`);

      const { version, isOverSea = false, isRetouch = false } = params;

      if (!version) {
        return {
          code: -1,
          message: 'version parameter is required',
        };
      }

      await this.versionFeatureService.sendUnifiedAdCheckOutInfo(version, isOverSea, isRetouch);

      // 构建分支名称用于描述
      let branchName: string, productName: string;
      if (isRetouch) {
        branchName = isOverSea ? `release/hypic/${version}` : `release/retouch/${version}`;
        productName = isOverSea ? 'hypic' : '醒图';
      } else {
        branchName = isOverSea ? `overseas/release/${version}` : `release/${version}`;
        productName = isOverSea ? 'cc' : '剪映';
      }

      return {
        code: 0,
        message: 'success',
        data: {
          version,
          isOverSea,
          isRetouch,
          branchName,
          productName,
          description: `已触发商业化广告子仓 ${branchName} 分支检查通知（${productName}）`,
        },
      };
    } catch (error) {
      this.logger.error(`[Test Unified Ad CheckOut] error: ${JSON.stringify(error)}`);
      return {
        code: -1,
        message: `执行失败`,
      };
    }
  }
}
