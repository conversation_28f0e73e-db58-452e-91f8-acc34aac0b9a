import { Body, Controller, Get, Post, Query } from '@gulux/application-http';
import { Inject } from '@gulux/gulux';
import { ModelType } from '@gulux/gulux/typegoose';
import { ExperimentInfoTable } from '../model/ExperimentInfo';
import { ExperimentDataTable } from '../model/ExperimentData';
import BitsService from '../service/third/bits';
import MrComponentsService from '../service/mrComponent';
import { RepoInfo } from '@shared/mrCallback';
import LarkService from '@pa/backend/dist/src/third/lark';
import LarkCardService from '../service/larkCard';
import { UserIdType } from '@pa/shared/dist/src/lark/userInfo';
import { MessageType } from '@pa/shared/dist/src/lark/larkCard';
import BitsForceMergeService from '../service/bitsForceMerge';
import { BuildJobType, JumpRequestParams, JumpResponseParams } from '@shared/bits/releaseDayJumpParams';
import ReleaseDayJump from '../service/releaseDayJump';
import { PublishTaskInfo, ThirdLockData } from '@shared/bits/webHook';
import repos from '@shared/gitlab/repos';
import MrModelService from '../service/model/mrModule';
import { BytedLogger } from '@gulux/gulux/byted-logger';
import { compact } from 'lodash';
import VersionProcessDao from '../service/dao/VersionProcessDao';
import MeegoService from '../service/third/meego';
import { MrCheckHandler } from '../utils/MrCheckHandler';
import BugFixMrHandler from '../service/handler/mrCheck/bugFixMrHandler';
import VeUpdateMrHandler from '../service/handler/mrCheck/veUpdateMrHandler';
import FeatureMrHandler from '../service/handler/mrCheck/featureMrHandler';
import { LVProductType, RetouchProductType } from '@shared/process/versionProcess';
import { NetworkCode, PlatformType } from '@pa/shared/dist/src/core';
import PackageQuotaManageDao from '../service/dao/PackageQuotaManage';
import { PackageQuotaManage, PackageSizeCIRecord } from '@shared/bits/packageSize';
import { MrState } from '@shared/bits/mrInfo';
import { PackageQuotaManageTable } from '../model/PackageManageModel';
import MrClosedServer from '../service/handler/bits/mrClosed';
import MrMergedPackageServer from '../service/handler/bits/mrMergedPackage';
import { HTTPResponse, Res } from '@gulux/gulux/application-http';
import OnCallService from '../service/third/oncall';
import { useInject } from '@edenx/runtime/bff';
import BusMrConflictCheckService from '../service/bus/BusMrConflictCheck';
import ZhongkuiService from '../service/third/zhongkui';
import MergeRequestService from '../service/mergeRequest';
import RpcProxyManager from '@pa/backend/dist/src/rpc/proxy';
import { AppSettingId } from '@pa/shared/dist/src/appSettings/appSettings';
import CustomBuildDao from '../service/dao/releasePlatform/CustomBuildDao';
import { CustomBuildParam, CustomBuildRecord, CustomBuildResultType, CustomBuildType } from '@shared/customBuild';
import HandlerUtils from '../service/handler/customBuild/handlerUtils';
import MrListService from '../service/mrList';
import { add_suffix_ne } from '@pa/shared/dist/src/utils/tools';
import { BitsAppIdToAid, BitsResult } from '@shared/bits';
import { nanoid } from 'nanoid';
import TFVersionCodeDao from '../service/dao/releasePlatform/TFVersionCodeDao';
import PackageSizeCIRecordDao from '../service/dao/PackageSizeCIRecordDao';
import { MrAutoSyncRecordDao } from '../service/dao/mr/MrAutoSyncRecordDao';
import dayjs from 'dayjs';
import CodeFreezeMrService from '../service/CodeFreezeMrService';
import versionUtils from '../utils/versionUtils';
import VersionProcessInfoDao from '../service/dao/releasePlatform/VersionProcessInfoDao';
import GitLabService from '../service/third/gitlab';
import { AppCommonInfo, appIdToAppInfoImpl } from '@shared/approval/ApprovalConfig';

@Controller('')
export default class BitsController {
  @Inject(ExperimentInfoTable)
  private experimentInfoModel: ModelType<ExperimentInfoTable>;

  @Inject(ExperimentDataTable)
  private experimentDataModel: ModelType<ExperimentDataTable>;

  @Inject()
  private bits: BitsService;

  @Inject()
  private mrComponent: MrComponentsService;

  @Inject()
  private lark: LarkService;

  @Inject()
  private larkCard: LarkCardService;

  @Inject()
  private bitsForceMerge: BitsForceMergeService;

  @Inject()
  private releaseDayJump: ReleaseDayJump;

  @Inject()
  private mrModel: MrModelService;

  @Inject()
  private logger: BytedLogger;

  @Inject()
  private rpcProxy: RpcProxyManager;

  @Inject()
  private versionProcessDao: VersionProcessDao;

  @Inject()
  private meego: MeegoService;

  @Inject()
  private bugFixMrHandler: BugFixMrHandler;

  @Inject()
  private veUpdateMrHandler: VeUpdateMrHandler;

  @Inject()
  private featureMrHandler: FeatureMrHandler;

  @Inject()
  private packageQuotaManageDao: PackageQuotaManageDao;

  @Inject(PackageQuotaManageTable)
  private packageQuotaManageModel: ModelType<PackageQuotaManageTable>;

  @Inject()
  private mrMergedPackageHandler: MrMergedPackageServer;

  @Inject()
  private mrClosedHandler: MrClosedServer;

  @Inject()
  private conflictCheckService: BusMrConflictCheckService;

  @Inject()
  private customBuildDao: CustomBuildDao;

  @Inject()
  private buildHandlerUtils: HandlerUtils;
  @Inject()
  private mrList: MrListService;
  @Inject()
  private tfVersionCodeDao: TFVersionCodeDao;
  @Inject()
  private packageSizeCIRecordDao: PackageSizeCIRecordDao;
  @Inject()
  private mrAutoSyncRecordDao: MrAutoSyncRecordDao;
  @Inject()
  private codeFreezeMrService: CodeFreezeMrService;
  @Inject()
  private versionProcessInfoDao: VersionProcessInfoDao;
  @Inject()
  private gitlab: GitLabService;

  @Post('/deleteExperimentInfo')
  async deleteExperimentInfo() {
    await this.experimentInfoModel.deleteMany({});
    await this.experimentDataModel.deleteMany({});
    return {
      status: 0,
    };
  }

  @Post('/recommend_components')
  async recommendComponents(@Body() repoInfos: RepoInfo[]) {
    await this.mrComponent.recommendComponents(repoInfos);
  }

  @Post('/tipAllGrayOpenMr')
  async tipAllGrayOpenMr(@Body() req: any) {
    const realMrId = await this.bits.searchAllMr(req);
    const mrReviewerInfo = await this.bits.getMrInfoAndReviewer(realMrId);
    const chatId = await this.lark.searchChats(req.lark_group);
    if (chatId !== '' && mrReviewerInfo.length > 0) {
      const projectBranch = `${req.group_name} ${req.target_branch}`;
      const messageCard = this.larkCard.buildGrayIssueListCard(projectBranch, mrReviewerInfo);
      await this.lark.sendMessage(UserIdType.chatId, chatId, JSON.stringify(messageCard), MessageType.interactive);
    }
    return {
      code: 0,
      message: 'success',
      data: realMrId,
    };
  }

  @Post('/forceMergeMr')
  async forceMergeMr(@Body() { mr_id, user_id }: { mr_id: number; user_id: string }) {
    return await this.bitsForceMerge.forceMergeMrPublic(mr_id, user_id);
  }

  @Post('/releaseDayJumpBuild')
  async releaseDayJumpBuild(@Body() params: JumpRequestParams): Promise<JumpResponseParams> {
    if (params.buildType === BuildJobType.base) {
      return {
        shouldJump: false,
        message: '当前节点为必要性打包节点',
      };
    }
    return await this.releaseDayJump.checkJump(params.mrId);
  }

  @Post('/webhook/bitsThirdLock')
  async bitsThirdLock(@Body() data: ThirdLockData) {
    this.logger.info(`[MR Merge Check] ${JSON.stringify(data)}`);
    if (data.dependencies) {
      const shouldCheckProjectId = repos.androidRepos
        .map(repo => repo.projectId)
        .filter(repo => ![390636, 242286, 649515, 514925, 134284].includes(repo));
      const notMatchRepo = data.dependencies.filter(
        value => shouldCheckProjectId.includes(value.project_id) && value.target_branch !== data.host.target_branch,
      );

      if (notMatchRepo.length > 0 && data.host.project_id !== repos.pcMainRepo.projectId) {
        return {
          status: false,
          message: `仓库【${notMatchRepo
            .map(value => repos.searchProjectInfo({ projectId: value.project_id })?.projectName)
            .join('、')}】目标分支与主仓【${data.host.target_branch}】不同\n禁止合入`,
        };
      }
    }
    const conflictCheckResult = await this.conflictCheckService.checkMrCanMerged(
      data.host.mr_iid,
      data.host.project_id,
      data.host.mr_id,
    );
    this.logger.info(`[MR Merge Check] [Conflict Check] result ${JSON.stringify(conflictCheckResult)}`);
    if (!conflictCheckResult.status) {
      return { status: false, message: conflictCheckResult.message };
    }
    const zhongkuiRes = await useInject(ZhongkuiService).accessLaneSync(data);
    this.logger.info(`[MR Merge Check] zhongkui:${JSON.stringify(zhongkuiRes)}`);
    return zhongkuiRes;
  }

  @Post('/webhook/retryBitsThirdLock')
  async retryBitsThirdLock(@Query() query: any) {
    if (query && query.project_id && query.mr_iid) {
      await this.mrModel.deleteMrLockInfo(query);
      return '重试成功';
    }
    return '未找到锁';
  }

  @Post('/bits/doMrApprove')
  async doMrApprove(@Body() body: any) {
    this.logger.info(`doMrApprove:body:${JSON.stringify(body)}`);
    const realMrIds = await this.bits.searchMr(body);
    // 先查询自己是不是已经review过了
    const mrReviewerInfoList = await this.bits.getMrInfoAndReviewer(realMrIds);
    for (const { mr_id, reviewInfo } of mrReviewerInfoList) {
      for (const info of reviewInfo) {
        if (info.approved === 'running' && info.username === `${body.reviewer_name}`) {
          const result = await this.bits.doMrApproveByMrId(body.bitsToken, mr_id);
          this.logger.info(`need doMrApprove:MRID:${mr_id}:${JSON.stringify(result)}`);
        }
      }
    }
    return {
      code: 0,
      message: 'success',
      data: realMrIds,
    };
  }

  @Get('/bits/getVeBm')
  async getVeBm(@Query() query: any) {
    this.logger.info(`getVeBmx:query:${JSON.stringify(query)}`);
    const result = await this.bits.getVeBm(query.aid, query.version);
    return {
      code: 0,
      message: 'success',
      data: {
        oncall: result,
      },
    };
  }

  @Get('/bits/getCrashBm')
  async getCrashBm(@Query() query: any) {
    // 通过值班表拿oncall
    const config: Record<string, any> = {};
    config.business = {
      value: 'technology',
    };
    config.technology = {
      value: ['性能稳定性', '实验/灰度/发版稳定性'],
    };
    const res = await useInject(OnCallService).queryDutyInfoFromDB('剪映 Android', JSON.stringify(config));
    return {
      code: 0,
      message: 'success',
      data: {
        oncall: res?.primary_users,
      },
    };
  }

  @Post('/release/changeReviewCurrentVersion')
  async releaseChangeReviewVersion(@Body() body: any) {
    return await this.rpcProxy.getQuality(true).addReviewVersion(body);
  }

  @Post('/release/resetReviewCurrentVersion')
  async releaseResetReviewVersion(@Body() body: any) {
    return await this.rpcProxy.getQuality(true).clearReviewVersion(body);
  }

  @Post('/bitsJobFieldInfoSubmit')
  async bitsJobFieldInfoSubmit(@Body() { jobId }: { jobId: number }) {
    const result = await this.bits.fetchJobInfo(jobId);
    const errorStep = compact(result?.steps ?? []).filter(
      value => value.stageName === 'build' && value.status === 'failed',
    );

    if (result && errorStep.length > 0) {
      const peopleMap =
        "<a href='https://bytedance.feishu.cn/docx/GAo6dOFCqoLiDnxxw7pcPqVcnld' target='_blank'>找人地图</a>";
      const message = `${result.errorMsg}\n 出错步骤：【${errorStep[0].stepName}】\n请先努力尝试解决\n如果遇到无法解决的问题,按照${peopleMap}请求帮助。`;
      await this.bits.postJobErrorMsg(jobId, message);
    }
    return {
      status: 0,
    };
  }

  @Post('/getVersionGroupNameToQuality')
  async getVersionGroupNameToQuality(@Body() { version, platform }: any) {
    const groupName = `LV-${platform}`;
    const chatGroupName = await this.bits.getVersionGroupName(groupName, version);
    const chatId = await this.lark.searchChats(chatGroupName);
    return {
      chatId,
      status: 1,
    };
  }

  @Post('/getCrashBMInfoToQuality')
  async getCrashBMInfo(@Body() { product, platform, version }: any) {
    const bmInfo = await this.versionProcessDao.getCrashBMInfo(platform, version, product);
    return {
      bm: bmInfo,
      status: 1,
    };
  }

  @Post('/requestVersionMaster')
  async requestVersionMaster(@Body() { groupName, version }: any) {
    return this.bits.requestVersionMaster(groupName, version);
  }

  @Post('/meego/queryVersionId')
  async queryVersionId(@Body() { projectKey, versionName }: any) {
    return await this.meego.queryVersionId(projectKey, versionName);
  }

  @Get('/checkGrayMrTicket')
  async checkGrayMrTicket(@Query() { mr_id, platform }: any, @Res() resp: HTTPResponse) {
    let forbiddenReason = '';
    const forbiddenHandler: string[] = [];
    const mrInfo = await this.bits.getMrInfo({ mrId: mr_id });
    if (!mrInfo) {
      return {};
    }
    const checkList: MrCheckHandler[] = [this.bugFixMrHandler, this.veUpdateMrHandler, this.featureMrHandler];
    for (const check of checkList) {
      if (check.canHandle(mrInfo, platform)) {
        const ret = await check.handler(mrInfo, platform);
        if (ret !== '') {
          if (forbiddenReason !== '') {
            forbiddenReason += ' & ';
          }
          forbiddenReason += ret;
          forbiddenHandler.push(check.handlerName);
        }
      }
    }
    if (forbiddenReason !== '') {
      resp.status = 403;
      return {
        reason: forbiddenReason,
        handler: forbiddenHandler,
      };
    }
    return {};
  }

  @Get('/open/bits/relation/list')
  async bitsMrListProxy(@Query() query: any) {
    return {
      data: await this.bits.getMrRelationList(query.mr_id),
    };
  }

  @Post('/open/bits/packageSizeCheck')
  async packageSizeCheck(
    @Body()
    {
      product,
      platform,
      mrId,
      task_id,
      base_task_id,
      target_size,
      base_size,
      androidOwner,
      iosOwner,
      is_apk_build_size,
    }: any,
  ) {
    this.logger.info(
      `[PackageQuotaManage]${mrId} from Api ${product} ${platform} ${task_id} ${base_task_id} ${target_size} ${base_size} ${androidOwner} ${iosOwner} ${is_apk_build_size}`,
    );
    const result = await this.packageQuotaManageDao.packageSizeCheck(
      product === 'VideoFusion' ? LVProductType.lv : LVProductType.cc,
      platform === PlatformType.Android ? PlatformType.Android : PlatformType.iOS,
      mrId,
      base_task_id,
      task_id,
      target_size,
      base_size,
      androidOwner,
      iosOwner,
      is_apk_build_size,
    );
    return {
      data: result,
    };
  }

  @Post('/open/bits/packageSizeReCheck')
  async packageSizeReCheck(@Body() body: any) {
    const mrID = body.mr_id;
    const { owner } = body;
    const { platform } = body;

    const platfromType = PlatformType.iOS;
    this.logger.info(`[PackageQuotaManager] recheck package size. mrID: ${mrID} owner: ${owner} platfrom: ${platform}`);

    if (!mrID || !owner || !platform) {
      return {
        pass: false,
        message: `后置检测参数不完整，请联系 <EMAIL> 处理 mrID: ${mrID} owner: ${owner} platfrom: ${platform}`,
      };
    }

    // 后置卡口需要保证剪映 & cc 均通过，剪映检测通过后再检测 cc
    let result = await this.packageQuotaManageDao.recheckPackage('lv', platfromType, mrID, owner);
    if (result.pass) {
      result = await this.packageQuotaManageDao.recheckPackage('cc', platfromType, mrID, owner);
    }

    return {
      pass: result.pass,
      message: result.message,
    };
  }

  @Post('/open/bits/packageSizeCheckIos')
  async packageSizeCheckIos(@Body() body: any) {
    const product = body.target.package_info.bundle_id === 'com.lemon.lvoverseas' ? LVProductType.cc : LVProductType.lv;
    const { platform } = body.target.package_info;
    const { mrIID } = body.target.mr_bind_info[0];
    const { projectID } = body.target.mr_bind_info[0];
    const { task_id } = body.target;
    const base_task_id = body.base.task_id;
    const target_size = body.target.package_info.standard_size;
    const base_size = body.base.package_info.standard_size;
    const owner = body.target.task_info.env.package_size_ios_owner;
    const mrInfo = await this.bits.getMrInfo({
      projectId: Number(projectID),
      iid: Number(mrIID),
    });
    const mrId = mrInfo?.id ?? 0;
    this.logger.info(
      `[PackageQuotaManageIos]${mrId} from Api ${product} ${platform} ${task_id} ${base_task_id} ${target_size} ${base_size} ${owner}`,
    );
    const result = await this.packageQuotaManageDao.packageSizeCheckIos(
      product,
      PlatformType.iOS,
      mrId,
      base_task_id,
      task_id,
      target_size,
      base_size,
      '',
      owner,
      '',
    );
    // const result = await this.packageQuotaManageDao.packageSizeCheckIos(
    //   LVProductType.lv,
    //   PlatformType.iOS,
    //   7152066,
    //   'pk150-base-502acbaf93',
    //   'mr-pipeline-79a4a2e98d',
    //   524044875,
    //   523891527,
    //   '',
    //   '<EMAIL>',
    //   '',
    // );
    const failReasons: string[] = [];
    if (result.check.length > 0) {
      failReasons.push(result.check);
    }
    if (result.check1a.length > 0) {
      failReasons.push(result.check1a);
    }
    if (result.check1b.length > 0) {
      failReasons.push(result.check1b);
    }
    if (result.check2.length > 0) {
      failReasons.push(result.check2);
    }
    if (result.pass) {
      return {};
    }
    return {
      fail_reasons: failReasons,
    };
  }

  @Post('/open/bits/packageInfoDelete')
  async packageInfoDelete(@Body() { platform }: any) {
    const packageInfoList = await this.packageQuotaManageModel
      .find({
        platform,
        year: 2024,
        quarter: 1,
      })
      .exec();
    let result: string[] = [];
    for (const aElement of packageInfoList) {
      const pendingMr = aElement.pendingMrInfo;
      for (const mrSizeDiffInfo of pendingMr) {
        const { mrId } = mrSizeDiffInfo;
        const mrInfo = await this.bits.getMrInfo({ mrId });
        if (mrInfo && (mrInfo.state === MrState.merged || mrInfo.state === MrState.closed)) {
          result = result.concat(
            `${aElement.product} ${aElement.platform} ${aElement.business} ${mrSizeDiffInfo.mrId} ${mrInfo.state}`,
          );
          if (mrInfo.state === MrState.merged) {
            await this.mrMergedPackageHandler.handler(
              JSON.stringify({
                username: mrSizeDiffInfo.owner,
                group_name: mrInfo.group_name,
                mr_id: mrSizeDiffInfo.mrId,
                project_id: mrInfo.project_id,
              }),
            );
          }
          if (mrInfo.state === MrState.closed) {
            await this.mrClosedHandler.handler(
              JSON.stringify({
                username: mrSizeDiffInfo.owner,
                group_name: mrInfo.group_name,
                mr_id: mrSizeDiffInfo.mrId,
                project_id: mrInfo.project_id,
              }),
            );
          }
        }
      }
    }
    return {
      data: result,
    };
  }

  @Post('/open/bits/packageSizeMrInfoRecord')
  async packageSizeMrInfoRecord(
    @Body()
    {
      mrId,
      baseCommit,
      branchCommit,
      packageSizeIOSOwner,
      mrIID,
      projectId,
      appId,
      ciMrDependecies,
    }: PackageSizeCIRecord,
  ) {
    this.logger.info(
      `packageSizeMrInfoRecord:${JSON.stringify({
        mrId,
        baseCommit,
        branchCommit,
        packageSizeIOSOwner,
        mrIID,
        projectId,
        appId,
        ciMrDependecies,
      })}`,
    );
    const existRecord = await this.packageSizeCIRecordDao.findByCriteria({
      mrId,
    });
    if (existRecord) {
      existRecord.baseCommit = baseCommit;
      existRecord.branchCommit = branchCommit;
      existRecord.packageSizeIOSOwner = packageSizeIOSOwner;
      existRecord.mrIID = mrIID;
      existRecord.projectId = projectId;
      existRecord.appId = appId;
      existRecord.ciMrDependecies = ciMrDependecies;
      await this.packageSizeCIRecordDao.updateByCriteria({ mrId }, existRecord);
      return {
        code: 0,
        message: 'success',
      };
    } else {
      await this.packageSizeCIRecordDao.create({
        mrId,
        baseCommit,
        branchCommit,
        packageSizeIOSOwner,
        mrIID,
        projectId,
        appId,
        ciMrDependecies,
      });
      return {
        code: 0,
        message: 'success',
      };
    }
  }

  @Post('/open/bits/packageInfoUpdate')
  async packageInfoUpdate(@Body() { platform, business, quota, year, quarter }: any) {
    const packageInfoList = await this.packageQuotaManageModel
      .find({
        platform,
        year,
        quarter,
        business,
      } as PackageQuotaManage)
      .exec();
    for (const element of packageInfoList) {
      element.quota = quota;
      await this.packageQuotaManageModel
        .updateOne(
          {
            product: element.product,
            platform,
            year,
            quarter,
            business,
          },
          element,
        )
        .exec();
    }
    return {
      data: { platform, business, year, quarter, quota },
    };
  }

  @Post('/open/bits/oldPackageInfoUpdate')
  async oldPackageInfoDelete(@Body() { year, quarter, product, business, mrId, realSize }: any) {
    const info = await this.packageQuotaManageModel
      .findOne({
        year,
        quarter,
        platform: PlatformType.Android,
        business,
        product,
      } as PackageQuotaManage)
      .exec();
    if (info) {
      for (const mrInfo of info.mergedMrInfo) {
        if (mrInfo.mrId === mrId) {
          info.usedQuota = info.usedQuota - mrInfo.diffSize + realSize;
          mrInfo.diffSize = realSize;
        }
      }
      await this.packageQuotaManageModel
        .updateOne(
          {
            year,
            quarter,
            platform: PlatformType.Android,
            business,
            product,
          },
          info,
        )
        .exec();
    }
    return {
      data: 'success',
    };
  }

  @Post('/open/bits/updateOldPackageInfo')
  async updateOldPackageInfo(@Body() { year, quarter, business, product, quota }: any) {
    const info = await this.packageQuotaManageModel
      .findOne({
        year,
        quarter,
        platform: PlatformType.Android,
        business,
        product,
      } as PackageQuotaManage)
      .exec();
    if (info) {
      info.quota = quota;
      await this.packageQuotaManageModel
        .updateOne(
          {
            year,
            quarter,
            platform: PlatformType.Android,
            business,
            product,
          },
          info,
        )
        .exec();
    }
    return {
      data: 'success',
    };
  }

  @Post('/open/bits/adoptQuarterBizUsage')
  async adoptQuarterBizUsage(@Body() { year, quarter, business, product, usedQuota }: any) {
    const info = await this.packageQuotaManageModel
      .findOne({
        year,
        quarter,
        platform: PlatformType.Android,
        business,
        product,
      } as PackageQuotaManage)
      .exec();
    let lastUsedQuota = 0;
    if (info) {
      lastUsedQuota = info.usedQuota;
      info.usedQuota = usedQuota;
      await this.packageQuotaManageModel
        .updateOne(
          {
            year,
            quarter,
            platform: PlatformType.Android,
            business,
            product,
          },
          info,
        )
        .exec();
    }
    return {
      data: 'success',
      used: `${usedQuota}`,
      lastUsedQuota: `${lastUsedQuota}`,
    };
  }

  @Post('/open/bits/getMrAfterSystemTest')
  async getMrAfterSystemTest(@Body() data: { version: string; platform: PlatformType; beforeGray?: boolean }) {
    const mrInfos = await this.mrList.getMrAfterSystemTestImpl(data.version, data.platform, data.beforeGray);
    return this.jsonToCsv(mrInfos);
  }

  @Post('/open/bits/getMrAfterSystemTestOfLV')
  async getMrAfterSystemTestOfLV(@Body() data: { version: string; platform: PlatformType; beforeGray?: boolean }) {
    const mrInfos = await this.mrList.getMrAfterSystemTestImplOfLV(data.version, data.platform, data.beforeGray);
    return this.jsonToCsv(mrInfos);
  }

  @Post('/open/bits/getMrAfterSystemTestOfCC')
  async getMrAfterSystemTestOfCC(@Body() data: { version: string; platform: PlatformType; beforeGray?: boolean }) {
    const mrInfos = await this.mrList.getMrAfterSystemTestImplOfCC(data.version, data.platform, data.beforeGray);
    return this.jsonToCsv(mrInfos);
  }

  @Post('/open/bits/getMrAfterSystemTest_json')
  async getMrAfterSystemTestForJson(@Body() data: { version: string; platform: PlatformType }) {
    const mrInfos = await this.mrList.getMrAfterSystemTestImpl(data.version, data.platform);
    return JSON.stringify(mrInfos);
  }

  @Post('/open/bits/retouch/getMrAfterSystemTest_json')
  async getRetouchMrAfterSystemTestJson(@Body() data: { appId: number; version: string }) {
    const mrInfos = await this.mrList.getRetouchMrAfterSystemTestImpl(data.appId, data.version);
    return JSON.stringify(mrInfos);
  }

  @Post('/open/bits/retouch/getMrAfterSystemTest')
  async getRetouchMrAfterSystemTest(@Body() data: { appId: number; version: string }) {
    const mrInfos = await this.mrList.getRetouchMrAfterSystemTestImpl(data.appId, data.version);
    return this.jsonToCsv(mrInfos);
  }

  @Post('/open/bits/aeolus/getMRAfterGrayV2')
  async aeolusGetMRAfterGrayV2(@Body() data: { appId?: number; version?: string }) {
    const _getPrevVersion = (version: string) => {
      const versionArray = version.split('.');
      const major = Number(versionArray[0]);
      const minor = Number(versionArray[1]);
      if (minor > 0) {
        return `${major}.${minor - 1}.0`;
      } else {
        return `${major - 1}.9.0`;
      }
    };
    const _func = (appId: number, appInfo: AppCommonInfo, version: string) => {
      switch (appInfo.product) {
        case RetouchProductType.retouch:
        case RetouchProductType.hypic:
          return this.mrList.getRetouchMrAfterSystemTestImpl(appId, version).then(mrInfos =>
            mrInfos.map(mrInfo => {
              mrInfo.app_name_slot = appInfo.productName;
              mrInfo.platform_slot = appInfo.platform.toString();
              mrInfo.version_slot = version;
              return mrInfo;
            }),
          );
        default:
          return [];
      }
    };

    const appIds = [
      AppSettingId.RETOUCH_ANDROID,
      AppSettingId.RETOUCH_IOS,
      AppSettingId.HYPIC_ANDROID,
      AppSettingId.HYPIC_IOS,
    ];
    const mrInfos = [];
    for (const appId of appIds) {
      const appInfo = appIdToAppInfoImpl(appId.toString());
      const versionInfo = await this.versionProcessInfoDao.findLatestNVersionInfo({ app_id: appId }, 2);
      if (!versionInfo || versionInfo.length < 2 || !appInfo) {
        continue;
      }
      versionInfo.splice(0, 1); // 删除第一个版本，一般是未进行或者进行中；保留剩下;
      let firstVersion = versionInfo[0].version;
      // 15.8.x 替换成 15.8.0
      if (firstVersion.split('.')[2] !== '0') {
        firstVersion = `${firstVersion.split('.')[0]}.${firstVersion.split('.')[1]}.0`;
      }
      const versions = [firstVersion, _getPrevVersion(firstVersion)];
      const result = await Promise.all(versions.map(version => _func(appId, appInfo, version)));
      mrInfos.push(...result.flat());
    }
    return mrInfos;
  }

  @Post('/open/bits/aeolus/getMRAfterGray')
  async aeolusGetMRAfterGray(@Body() data: { beforeGray?: boolean }) {
    const androidVersionsInfo = await this.versionProcessInfoDao.findLatestNVersionInfo(
      { app_id: AppSettingId.LV_ANDROID },
      2,
    );
    const iosVersionsInfo = await this.versionProcessInfoDao.findLatestNVersionInfo({ app_id: AppSettingId.LV_IOS }, 2);
    if (!androidVersionsInfo || androidVersionsInfo.length < 2 || !iosVersionsInfo || iosVersionsInfo.length < 2) {
      return;
    }
    androidVersionsInfo.splice(0, 1); // 删除第一个版本，一般是未进行或者进行中；保留剩下
    iosVersionsInfo.splice(0, 1); // 删除第一个版本，一般是未进行或者进行中；保留剩下
    let androidFirstVersion = androidVersionsInfo[0].version;
    let iosFirstVersion = iosVersionsInfo[0].version;
    // 15.8.x 替换成 15.8.0
    if (androidFirstVersion.split('.')[2] !== '0') {
      androidFirstVersion = `${androidFirstVersion.split('.')[0]}.${androidFirstVersion.split('.')[1]}.0`;
    }
    if (iosFirstVersion.split('.')[2] !== '0') {
      iosFirstVersion = `${iosFirstVersion.split('.')[0]}.${iosFirstVersion.split('.')[1]}.0`;
    }
    const _getPrevVersion = (version: string) => {
      const versionArray = version.split('.');
      const major = Number(versionArray[0]);
      const minor = Number(versionArray[1]);
      if (minor > 0) {
        return `${major}.${minor - 1}.0`;
      } else {
        return `${major - 1}.9.0`;
      }
    };
    const androidVersions = [androidFirstVersion, _getPrevVersion(androidFirstVersion)];
    const iosVersions = [iosFirstVersion, _getPrevVersion(iosFirstVersion)];
    const _func = (version: string, platform: PlatformType) =>
      this.mrList.getMrAfterSystemTestImpl(version, platform, data.beforeGray).then(mrInfos =>
        mrInfos.map(mrInfo => {
          mrInfo.app_name_slot = '剪映';
          mrInfo.platform_slot = platform.toString();
          mrInfo.version_slot = version;
          return mrInfo;
        }),
      );

    // 异步调用getMrAfterSystemTestImpl并回收结果
    const androidPromise = androidVersions.map(version => _func(version, PlatformType.Android));
    const iosPromise = iosVersions.map(version => _func(version, PlatformType.iOS));
    const androidResult = await Promise.all(androidPromise);
    const iosResult = await Promise.all(iosPromise);
    // 合并结果
    const androidMrInfos = androidResult.flat();
    const iosMrInfos = iosResult.flat();
    return [...androidMrInfos, ...iosMrInfos];
  }

  @Post('/open/check_bugfix_level_up')
  async checkBugfixLevelUp(@Body() params: { mrId: number }) {
    const meegoResult = await this.bits.getBindMeegoTaskInfo({
      mr_id: params.mrId,
    });
    const resList: any = {};
    for (const mrMeego of meegoResult) {
      const res = await this.meego.checkMeegoLevelUp(mrMeego.task_id);
      resList[mrMeego.task_id] = res;
    }
    return resList;
  }

  jsonToCsv(jsonArray: any[], delimiter = ',') {
    if (jsonArray.length === 0) {
      return '';
    }

    // 获取对象的所有键，以生成CSV的列标题
    const keys = Object.keys(jsonArray[0]);

    // 创建CSV的头行
    const header = keys.join(delimiter);

    // 创建CSV的内容行
    const rows = jsonArray.map(obj =>
      keys
        .map(key => {
          // 确保每个值都用引号包裹，以处理包含逗号的值
          const value = obj[key] === undefined || obj[key] === null ? '' : obj[key];
          return `"${String(value).replace(/"/g, '""')}"`; // 转义双引号
        })
        .join(delimiter),
    );

    // 组合头行和内容行
    return [header, ...rows].join('\n');
  }

  @Post('/open/create_placeholder_mr')
  async createPlaceHolderMR(@Body() params: { version: string; isCC: boolean }) {
    return useInject(MergeRequestService).createPlaceholderMR(params.version, params.isCC);
  }

  @Get('/open/get_build_branch')
  async getBuildBranch(
    @Query() params: { bits_app_id: number; version: string; task_id: number; task_flow_id: number },
  ) {
    this.logger.info(`get_build_branch params: ${JSON.stringify(params)}`);
    const buildRecord = await this.customBuildDao.findByCriteria({
      tfTaskFlowId: params.task_flow_id,
    });
    if (buildRecord) {
      return {
        code: 200,
        data: buildRecord.lvBranch,
        message: 'success',
      };
    }
    return {
      code: 200,
      data: '',
      message: 'success',
    };
  }

  @Post('/open/webhook/iOS/autoTFBuild')
  async sendIOSTFAutoBuildMessages(@Body() hookData: PublishTaskInfo) {
    this.logger.info(`[sendIOSTFAutoBuildMessages] [body数据]: ${JSON.stringify(hookData)}`);
    const customBuild = await this.customBuildDao.findByCriteria({
      tfTaskFlowId: hookData.TaskFlowId,
    });
    if (!customBuild) {
      this.logger.error('[sendIOSTFAutoBuildMessages] customBuild is undefined!');
      return {
        code: 0,
        message: 'success',
      };
    }
    const tfVersionCode = await this.tfVersionCodeDao.findOneByCriteria({
      version: customBuild.version,
      appId: customBuild.appId as number,
      buildResult: CustomBuildResultType.building,
    });
    if (!tfVersionCode) {
      this.logger.error('[sendIOSTFAutoBuildMessages] tfVersionCode is undefined!');
      return {
        code: 0,
        message: 'success',
      };
    }
    if (hookData.TaskStatus !== 'SUCCESS') {
      // 失败发送给zhengbolun.patlon
      const user = await this.lark.getUserIdByEmail(add_suffix_ne('@bytedance.com')('zhengbolun.patlon'));
      const info = JSON.stringify(hookData);
      const card = this.larkCard.buildSimpleActionCard(`TF 构建失败: ${info}`);
      if (user) {
        await this.lark.sendCardMessage(UserIdType.userId, user.user_id, card);
      }
      customBuild.buildResult = CustomBuildResultType.failed;
      await this.customBuildDao.updateByCriteria({ tfTaskFlowId: hookData.TaskFlowId }, customBuild);
      const buildUser = await this.lark.getUserIdByEmail(add_suffix_ne('@bytedance.com')(customBuild.username));
      if (buildUser) {
        await this.lark.sendCardMessage(UserIdType.userId, buildUser.user_id, card);
      }
      tfVersionCode.buildResult = CustomBuildResultType.failed;
      await this.tfVersionCodeDao.updateByCriteria(
        { version: customBuild.version, appId: customBuild.appId as number },
        tfVersionCode,
      );
      return {
        code: 0,
        message: 'success',
      };
    }
    if (!hookData.TaskName.includes('产物上传TestFlight')) {
      return {
        code: 0,
        message: 'success',
      };
    }
    const taskInput = JSON.parse(hookData.TaskInput);
    customBuild.buildResult = CustomBuildResultType.success;
    await this.customBuildDao.updateByCriteria({ tfTaskFlowId: hookData.TaskFlowId }, customBuild);
    const card = this.larkCard.buildiOSAndPCSuccessCard(
      PlatformType.iOS,
      hookData.TaskLink,
      customBuild.detailVersionCode,
      customBuild.airPlaneTaskName,
    );
    const userId = await this.lark.getUserIdByEmail(`${customBuild.username}@bytedance.com`);
    if (userId) {
      await this.lark.sendCardMessage(UserIdType.openId, userId.open_id, card);
    }
    if (customBuild.needSendGroup && customBuild.version) {
      let sendVersion = customBuild.version;
      if (customBuild.appId === AppSettingId.CC_IOS) {
        sendVersion = versionUtils.cc2lvVersion(customBuild.version);
      }
      const versionMeego = await useInject(VersionProcessDao).findVersionMeego(sendVersion);
      if (versionMeego?.iosMeegoId) {
        const addResult = await this.meego.addBotToMeegoChat(versionMeego.iosMeegoId, 'cli_9c8628b7b1f1d102');
        const iOSchatId = addResult?.chatId;
        if (iOSchatId) {
          await this.lark.sendCardMessage(UserIdType.chatId, iOSchatId, card);
        }
      }
    }
    tfVersionCode.buildResult = CustomBuildResultType.success;
    await this.tfVersionCodeDao.updateByCriteria(
      { version: customBuild.version, appId: customBuild.appId as number },
      tfVersionCode,
    );
    return {
      code: 0,
      message: 'success',
    };
  }

  @Get('/open/get_build_mr')
  async getBuildMr(@Query() params: { bits_app_id: number; version: string; task_id: number; task_flow_id: number }) {
    this.logger.info(`getBuildMr params: ${JSON.stringify(params)}`);
    const buildRecord = await this.customBuildDao.findByCriteria({
      tfTaskFlowId: params.task_flow_id,
    });
    if (buildRecord && buildRecord.mrLink) {
      return {
        code: 200,
        data: buildRecord.mrLink,
        message: 'success',
      };
    }
    return {
      code: 200,
      data: '',
      message: 'success',
    };
  }

  @Get('/open/get_tf_version')
  async getTfVersion(@Query() params: { bits_app_id: number; version: string; task_id: number; task_flow_id: number }) {
    // async getTfVersion(@Query() params: any) {
    this.logger.info(`get_tf_version params: ${JSON.stringify(params)}`);
    // return {
    //   code: 200,
    //   data: 152099,
    //   message: 'updateVersionList not found',
    // };
    if (!params.task_flow_id) {
      return {
        code: 200,
        data: 10001,
        message: 'updateVersionList not found',
      };
    }
    const buildRecord = await this.customBuildDao.findByCriteria({
      tfTaskFlowId: Number(params.task_flow_id),
    });
    if (buildRecord !== null && buildRecord.tfFiveVersion && buildRecord.tfFiveVersion !== 10001) {
      return {
        code: 200,
        data: buildRecord.tfFiveVersion,
        message: 'success',
      };
    }
    const aid = BitsAppIdToAid(Number(params.bits_app_id));
    const { version } = params;
    const taskId = params.task_id;
    const updateVersionList = await this.bits.searchArtifact(
      aid,
      'update_version',
      version,
      ['GRAY', 'OFFICIAL'],
      PlatformType.iOS,
    );
    if (!updateVersionList || updateVersionList.length === 0) {
      return {
        code: 200,
        data: 10001,
        message: 'updateVersionList not found',
      };
    }
    const buildingVersionList = await this.customBuildDao.findBuildTFRecord(params.bits_app_id);
    if (buildingVersionList && buildingVersionList.length > 0) {
      updateVersionList.push(...buildingVersionList.map(v => v.detailVersionCode));
    }
    // 过滤掉最后两位数大于80的版本号
    const filteredUpdateVersionList = updateVersionList.filter((v: string) => {
      const num = parseInt(v.replace(/\./g, ''), 10);
      return num % 100 < 80;
    });
    const sortedUpdateVersionList = filteredUpdateVersionList.sort((a: string, b: string) => {
      const numA = parseInt(a.replace(/\./g, ''), 10);
      const numB = parseInt(b.replace(/\./g, ''), 10);
      return numB - numA;
    });
    const latestUpdateVersion =
      sortedUpdateVersionList && sortedUpdateVersionList.length > 0
        ? parseInt(sortedUpdateVersionList[0].replace(/\./g, ''), 10)
        : parseInt(`${version.replace(/\./g, '')}00`, 10);
    if (!buildRecord) {
      const record: CustomBuildRecord = {
        lvBranch: '',
        buildType: CustomBuildType.TF_TEST,
        buildId: nanoid(),
        buildResult: CustomBuildResultType.building,
        username: '',
        buildParams: {
          arch: PlatformType.iOS,
          appId: params.bits_app_id,
          lvBranch: '',
          type: CustomBuildType.TF_TEST,
          repos: [],
          extra: {
            mrInfo: {
              id: 0,
              title: '手动触发',
              url: '',
            },
          },
        } as CustomBuildParam,
        tfFiveVersion: latestUpdateVersion + 1,
        tfTaskFlowId: params.task_flow_id,
      };
      // await this.customBuildDao.create(record);
    } else if (buildRecord.tfFiveVersion === 10001) {
      buildRecord.tfFiveVersion = latestUpdateVersion + 1;
      await this.customBuildDao.updateByCriteria({ tfTaskFlowId: params.task_flow_id }, buildRecord!);
    }
    return {
      code: 200,
      data: latestUpdateVersion + 1,
      message: '',
    };
  }

  @Get('/open/auto_synchronization_trigger')
  async triggerAutoSynchronization(@Query() params: { mr_id?: number }) {
    const mrId = params?.mr_id;
    if (mrId) {
      await this.codeFreezeMrService.triggerAutoSyncBitsJobs(mrId);
    }
  }

  @Get('/open/auto_synchronization')
  async postAutoSynchronization(@Query() params: { mr_id?: number }) {
    const MAX_AUTO_SYNC_COUNT = 1;
    const { mr_id } = params;
    if (!mr_id) {
      return {
        code: NetworkCode.Error,
        data: {},
        message: 'no contain mr id',
      };
    }
    // 关联MR
    const mrRelationList = await this.bits.getMrRelationList(mr_id);
    const relationsId = mrRelationList?.map(it => it.id) ?? [];
    const allMrIds = relationsId.concat(mr_id);
    this.logger.info(`[postAutoSynchronization] allMrIds: ${JSON.stringify(allMrIds)}`);
    let isAllowAutoSync = true;
    // 自动同步标志，每个端都有一个标识，如果默认打开状态，其中有一个关闭则不执行自动同步
    for (const mrId of allMrIds) {
      const custom_params = await this.bits.getMrCustomInfo(0, 0, mrId);
      isAllowAutoSync = (custom_params?.auto_merge_develop ?? true) && isAllowAutoSync;
      this.logger.info(
        `[postAutoSynchronization] mrId: ${mrId}, custom_params: ${JSON.stringify(custom_params)} isAllowAutoSync: ${isAllowAutoSync}`,
      );
    }

    if (!isAllowAutoSync) {
      this.logger.info(`[postAutoSynchronization] mr_id: ${mr_id} is not allow isAllowAutoSync.`);
      return {
        code: NetworkCode.Success,
        data: {},
        message: `mr_id: ${mr_id} is not allow isAllowAutoSync.`,
      };
    }
    const mrInfo = await this.bits.getMrInfo({ mrId: mr_id });
    if (!mrInfo) {
      this.logger.info(`[postAutoSynchronization] mr_id: ${mr_id} get mrInfo fail.`);
      return {
        code: NetworkCode.Success,
        data: {},
        message: `mr_id: ${mr_id} get mrInfo fail!`,
      };
    }
    // mr有票据才会命中同步机制
    const hasTicket = mrInfo.tags.some(tag => tag.name.startsWith('ticket:integration:'));
    if (!hasTicket) {
      this.logger.info(`[postAutoSynchronization] mr_id: ${mr_id} has not get ticket.`);
      return {
        code: NetworkCode.Success,
        data: {},
        message: `mr_id: ${mr_id} has not get ticket!`,
      };
    }
    // 最近同步间隔小于5min，不进行同步
    const lastSyncTime = await this.mrAutoSyncRecordDao.getAutoSyncLastTime(allMrIds);
    const now = dayjs().unix();
    if (now - lastSyncTime < 60 * 5) {
      this.logger.info(
        `[postAutoSynchronization] auto sync not allow sync in ${now - lastSyncTime}s，must exceed 5 min`,
      );
      return {
        code: NetworkCode.Success,
        data: {},
        message: `auto sync not allow sync in ${now - lastSyncTime}s，must exceed 5 min`,
      };
    }

    // 同步的最大请求次数，超过最大更新次数MAX_AUTO_SYNC_COUNT，不自动同步
    const autoSyncCount = await this.mrAutoSyncRecordDao.getAutoSyncCount(allMrIds);
    if (autoSyncCount >= MAX_AUTO_SYNC_COUNT) {
      this.logger.info(
        `[postAutoSynchronization] auto sync reach max count: ${MAX_AUTO_SYNC_COUNT}, current count: ${autoSyncCount}`,
      );
      return {
        code: NetworkCode.Success,
        data: {},
        message: `auto sync reach max count: ${MAX_AUTO_SYNC_COUNT}, current count: ${autoSyncCount}`,
      };
    }
    // 更新MR id
    await this.mrAutoSyncRecordDao.update(mr_id, relationsId);
    await this.lark.sendTextMessage(UserIdType.userId, '622fgag2', `发起自动化更新，MrId：${mr_id}`);
    let finalResult = true;
    // 不包含移动端mr info
    if (mrInfo && !(await this.bits.checkMrContainsLvRepo(mrInfo))) {
      this.logger.info(`[postAutoSynchronization] not contain lv mobile repo`);
      return {
        code: NetworkCode.Success,
        data: {},
        message: 'not contain lv mobile repo',
      };
    }

    // 开始执行同步操作
    if (mrInfo) {
      const result = await this.bits.mergeTarget(mrInfo.project_id, mrInfo.iid);
      this.logger.info(
        `merge target main mrId:${mr_id} project_id:${mrInfo.project_id} iid: ${mrInfo.iid} result: ${JSON.stringify(result)}`,
      );
      finalResult = finalResult && (result as BitsResult<null>)?.code === 200;
    }
    if (mrRelationList) {
      for (const mrRelationInfo of mrRelationList) {
        const result = await this.bits.mergeTarget(mrRelationInfo.project_id, mrRelationInfo.iid);
        this.logger.info(
          `merge target relation mrId :${mrRelationInfo.id} project_id: ${mrRelationInfo.project_id} iid: ${mrRelationInfo.iid} result: ${JSON.stringify(result)}`,
        );
        finalResult = finalResult && (result as BitsResult<null>).code === 200;
      }
    }
    return {
      code: NetworkCode.Success,
      data: {
        sync_result: finalResult,
        autoSyncCount: autoSyncCount + 1,
      },
      message: 'success',
    };
  }

  @Get('/open/get_mr_diff_info')
  async getMrDiffPatch(@Query() params: { mr_id: number; repo: string }, @Res() res: HTTPResponse) {
    const { mr_id, repo } = params;
    const targetRepo = repos.main_repos.find(r => repo.includes(r.projectName));
    if (!targetRepo) {
      res.status = 404;
      return `目标仓库[${repo}]不在支持范围内`;
    }
    let mrInfo = await this.bits.getMrInfo({ mrId: mr_id });
    if (mrInfo && mrInfo.project_id !== targetRepo.projectId) {
      mrInfo = undefined;
      const relateMrInfos = await this.bits.getMrRelationList(mr_id);
      if (relateMrInfos) {
        for (const mr of relateMrInfos) {
          if (mr.project_id === targetRepo.projectId) {
            mrInfo = await this.bits.getMrInfo({ mrId: mr.id });
          }
        }
      }
    }
    if (!mrInfo) {
      res.status = 404;
      return `获取不到目标仓库对应MR [${repo}]`;
    }
    return mrInfo;
  }
}
