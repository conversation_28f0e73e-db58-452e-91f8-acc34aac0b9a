import { HTTPRequest, HTTPResponse, Req, Res } from '@gulux/gulux/application-http';
import { Body, Controller, Post, Get, Delete } from '@gulux/application-http';
import { Inject } from '@gulux/gulux';
import McpExpressService from '../mcp-express-service';
import { PaMcpCallId } from '@shared/mcp/mcp-info';

@Controller('')
export default class McpController {
  @Post('/mcp')
  async getPostFeatures(@Req() req: HTTPRequest, @Res() res: HTTPResponse) {
    // 处理 tools/call 和 prompts/get 场景，得到 pa-mcp-call-key
    const mcpCallId = (req.headers[PaMcpCallId] as string) ?? 'no-key';
    await McpExpressService.getInstance().handleMcpPost(req.req, res.res, req.body, mcpCallId);
  }

  @Get('/mcp')
  async getGetFeatures(@Req() req: HTTPRequest, @Res() res: HTTPResponse) {
    await McpExpressService.getInstance().handleSessionRequest(req.req, res.res);
  }

  @Delete('/mcp')
  async getDeleteFeatures(@Req() req: HTTPRequest, @Res() res: HTTPResponse) {
    await McpExpressService.getInstance().handleSessionRequest(req.req, res.res);
  }
}
