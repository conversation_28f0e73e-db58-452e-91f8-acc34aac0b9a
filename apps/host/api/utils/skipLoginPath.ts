const skipLoginPath = [
  '/api/oauth2/login',
  '/api/oauth2/logout',
  '/api/oauth2/callback',
  '/api/mrWebHook',
  '/api/recordDependency',
  '/api/bitsTestUri',
  '/api/deleteExperimentInfo',
  '/api/addExperimentFeedback',
  '/api/clean/MonitorModel',
  '/api/addExperimentInfo',
  '/api/experiment_info',
  '/api/gitlabWebHook',
  '/api/bitsBuildCallback',
  '/api/handlerBitsWebHook',
  '/api/recommend_components',
  '/api/versionBlocking',
  '/api/businessAllInfo',
  '/api/queryWorkItemMeta',
  '/api/queryWorkItemDetail',
  '/api/queryModelIssueFixRate',
  '/api/checkStoryMrReady',
  '/api/queryStoryProgress',
  '/api/updateStoryProgress',
  '/api/checkFeatureMrReady',
  '/api/queryCurrentIssueState',
  '/api/createSlardarIssue',
  '/api/createSlardarIssueLegacy',
  '/api/walleCreateSlardarBug',
  '/api/get_export_share_info',
  '/api/update_experiment_share_info_business',
  '/api/get_business_by_id',
  '/api/webhook/larkCardCallback',
  '/api/webhook/mrMergeCheck',
  '/api/webhook/checkBusMrsConflict',
  '/api/webhook/meegoRequest',
  '/api/webhook/bitsCallBack',
  '/api/webhook/bitsAtomCallBack',
  '/api/webhook/bitsThirdLock',
  '/api/webhook/consulEvent',
  '/api/webhook/gitlab',
  '/api/webhook/gitlab_merge_request',
  '/api/webhook/sendVersionMessages',
  '/api/webhook/cc/android/startRelease',
  '/api/webhook/cc/android/fullTimeRelease',
  '/api/webhook/lv/android/fullTimeRelease',
  '/api/tipAllGrayOpenMr',
  '/api/forceMergeMr',
  '/api/grayPublishMessage',
  '/api/onCall/queryOnCallOwner',
  '/api/onCall/queryModelInfo',
  '/api/onCall/version',
  '/api/onCall/business',
  '/api/onCall/saveOnCallInfo',
  '/api/onCall/modelDB',
  '/api/onCall/dutyDB',
  '/api/onCall/customInstruction',
  '/api/onCall/lvCapCutHook',
  '/api/bits/doMrApprove',
  '/api/bits/getVeBm',
  '/api/bits/getCrashBm',
  '/api/startVersion',
  '/api/addAuthSetting',
  '/api/releaseDayJumpBuild',

  '/api/callback/buildResult',
  '/api/customBuild/ve',
  '/api/customBuild/releaseDebug',
  '/api/customBuild/lastReleaseInfo',
  '/api/customBuild/buildInfo',
  '/api/customBuild/meegoDebug',
  '/api/createTestStoryByVersion',
  '/api/createTestStoryByProductVersion',

  '/api/plugins/checkByCustom',
  '/api/plugins/checkByMr',
  '/api/plugins/result',
  '/api/lark/message',
  '/api/remainStoryRiskByVersion',
  '/api/remainStoryMrMergeByVersion',
  '/api/webhook/retryBitsThirdLock',
  '/api/release/changeReviewCurrentVersion',
  '/api/release/resetReviewCurrentVersion',
  '/api/bitsJobFieldInfoSubmit',
  '/api/testRPC',
  '/api/getVersionGroupNameToQuality',
  '/api/getCrashBMInfoToQuality',
  '/api/meego/issue/detail',
  '/api/meego/issue/update/priority',
  '/api/requestVersionMaster',
  '/api/meego/queryVersionId',
  '/api/checkGrayMrTicket',
  '/api/warnUser',
  '/api/monitor/card/test',
  '/api/send_certificate_expire_msg',
  '/api/testApi',
  '/api/calendarWorkSpaceEventAll',
  '/api/queryHistoryBugProgress',
  '/api/remainIssueByRdForVersion',
  '/api/component/source/repo/record',
  '/api/component/source/repo/search',
  '/api/component/artifact/search',
  '/api/component/artifact/update',
  '/api/component/artifact/add',
  '/api/component/artifact/deps/update',
  '/api/component/artifact/deps/search',
  '/api/component/search',
  '/api/component/delete',
  '/api/settings/refreshUselessSettings',
  '/api/settings/updateUselessSettingsWhichUsedInCode',
  '/api/settings/getUselessSettingKeys',
  '/api/settings/updateMeegosInfoOfUselessSettings',
  '/api/settings/handleColletedUselessSettingsDataAndNotify',
  '/api/publishInfo',
  '/api/version/create',
  '/api/version/create_new_version_info',
  '/api/subscription/delete',
  '/api/subscription/deleteAll',
  '/api/version/delete',
  '/api/checklistInfo/deleteAll',
  '/api/checklistTemplate/deleteAll',
  '/api/cpInformList/deleteAll',
  '/api/cpInformList/deleteOne',
  '/api/branchFlow/checkBranchFlow',
  '/api/branchFlow/checkFlowProgress',
  '/api/branchFlow/getTaskInfo',
  '/api/branchFlow/clear',
  '/api/open/openQuickMRPipeline',
  '/api/open/closeQuickMRPipeline',
  '/api/open/requestAutoResolveConflict',
  '/api/open/reportConflictInfo',

  '/api/goofy/get-garfish-app-list',

  '/api/version_integration/sync_batch_submrs',
  '/api/version_integration/cancel_batch_submr',
  '/api/version_integration/create_batch_mr',
  '/api/version_integration/add_sub_batch',
  '/api/version_integration/check_batch_submrs',
  '/api/version_integration/check_onbatch_mr',
  '/api/version_integration/check_mr_state',
  '/api/version_integration_tools/create_batch_mr',
  '/api/version_integration_tools/cancel_batch_protect',
  '/api/version_integration_tools/add_batch_sub_mr',
  '/api/version_integration_tools/check_mr_conflioct',
  '/api/version_integration_tools/fetch_all_batch_mr_infos',

  '/api/release_platform_api/fetch_onprogress_version_and_checklist',
  '/api/release_platform_api/find_checklist',
  '/api/release_platform_api/full_release_pass',
  '/api/version_auto_drive/clear_auto_drive_action_log',
  '/api/release_platform_api/check_version_status',
  '/api/release_platform_api/update_check_item',
  '/api/release_platform_api/turn_version_next_stage',
  '/api/release_platform_api/test_flight_released',
  '/api/release_platform_api/create_version_info',
  '/api/release_platform_api/delete_version_info',
  '/api/release_platform_api/update_version_inhouse',
  '/api/release_platform_api/exempt_business_bug',
  '/api/release_platform_api/update_version_info',
  '/api/release_platform_api/braodcast_version_status',
  '/api/release_platform_lambda/update_version_feature_info',
  '/api/release_platform_lambda/count_version_feature_info',
  '/api/release_platform_lambda/clear_version_feature_info',
  '/api/release_platform_lambda/analyze_submit_prds',
  '/api/release_platform_lambda/update_rig_submit_prd',
  '/api/release_platform_lambda/get_prd_analyze_detail',
  '/api/release_platform_lambda/send_features_card',
  '/api/release_platform_lambda/generate_version_review_doc',
  '/api/release_platform_lambda/fetch_version_feature_list',
  '/api/release_platform_api/fetch_onprogress_version_and_checklist_by_name',
  '/api/codeProcess/bitsJobStart',
  '/api/codeProcess/bitsJobFinish',
  '/api/conflict_check/compare_mrs_conflict',
  '/api/webhook/bytestMRCrash',
  '/api/webhook/reviewPlatformCallback',
  '/api/release_platform_api/check_specific_version_status',
  '/api/release_platform_api/delete_checklist',
  '/api/release_platform_api/exempt_bug_item',
  '/api/fetch_bus_mrs',
  '/api/release_platform/fetch_bus_mrs_with_relations',
  '/api/release_platform/fetch_mrs_with_relations',
  '/api/release_platform/get_mr_merge_schedule_result',
  '/api/release_platform/create_mr_merge_schedule_result',
  '/api/release_platform/get_mr_specific_conflict_info',

  '/api/version_auto_drive/push_gray_delay_warning',
  '/api/version_auto_drive/apply_gray_package_delay',
  '/api/version_auto_drive/evaluate_gray_delay',
  '/api/version_auto_drive/send_gray_delay_evaluate_card',
  '/api/version_auto_drive/check_release_record',
  '/api/update_ve_and_code_version',
  '/api/open/android/update_ve_version',
  '/api/open/ios/update_ve_version',
  '/api/story_revenue_review_platform/update_meego_libra_map_info',
  '/api/story_revenue_review_platform/batch_update_meego_libra_map_info',
  '/api/story_revenue_review_platform/delete_meego_libra_map_info',
  '/api/story_revenue_review_platform/query_story_revenue_review_period_info_list',
  '/api/story_revenue_review_platform/create_story_revenue_task',
  '/api/story_revenue_review_platform/create_story_revenue_tasks_in_batch',
  '/api/story_revenue_review_platform/query_story_revenue_task_list',
  '/api/story_revenue_review_platform/update_story_revenue_task',
  '/api//story_revenue_review_platform/delete_story_revenue_task',
  '/api/story_revenue_review_platform/create_story_revenue_review_period_info',
  '/api/story_revenue_review_platform/update_story_revenue_review_period_info',
  'api/story_revenue_review_platform/create_story_revenue_libra_meta_info',
  'api/story_revenue_review_platform/update_story_revenue_libra_meta_info',

  'api/flight_manager_platform/get_meego_team_list',
  'api/flight_manager_platform/get_meego_team_list_by_meego_api',
  'api/flight_manager_platform/update_meego_team_list',

  '/api/release_package_submit_info/fetch_all_release_package_submit_info',
  '/api/release_package_submit_info/fetch_release_package_submit_info_by_version_and_stage',
  '/api/release_package_submit_info/add_release_package_submit_info',
  '/api/release_package_submit_info/update_release_package_submit_info',
  '/api/release_package_submit_info/delete_all_release_package_submit_info',
  '/api/release_package_submit_info/create_reject_handle_group',
  '/api/release_package_submit_info/send_reject_notice_card_test',
  '/api/release_package_submit_info/fetch_release_package_submit_info_by_appid_and_full_version',
  '/api/airplane_config/query',
  '/api/webhook/release/general/check_submit_status',

  '/api/release_platform/transaction/sync_approval',
  '/api/release_platform/submit/info/version-find',
  '/api/release_platform/delete_messages',
  '/api/release_platform/transaction/clear',
  '/api/release_platform/version_transaction_find',
  '/api/release_platform/version_report/get_doc_content',
  '/api/release_platform/version_report/get_chart_content',
  '/api/release_platform/feedbacks/analyze_labels',
  '/api/release_platform/feedbacks/test',
  '/api/release_platform/feedbacks/test2',
  '/api/release_platform/feedbacks/test_pc',
  '/api/release_platform/feedbacks/delete_all',
  '/api/release_platform/feedbacks/get_meego_work_item_info',
  '/api/release_platform/feedbacks/get_user_feedback_bug',
  '/api/release_platform/feedbacks/cronjob_update_check_item_status',
  '/api/release_platform/feedbacks/get_recent_feedback_info',
  '/api/release_platform/feedbacks/update_feedback_follow_status_init_to_voc',
  '/api/release_platform/transaction/find',
  '/api/get_user_info_by_open_id',
  '/api/get_user_info',
  '/api/batch_get_user_info_by_open_id',
  '/api/airplane_config/query',
  '/api/search_lark_chat',

  '/api/release_platform/independent_grey/approval/create',
  '/api/release_platform/independent_grey/approval/create_test',
  '/api/release_platform/independent_grey/approval/subscribe',
  '/api/release_platform/independent_grey/checklist_create',
  '/api/release_platform/independent_grey/approval/delete_all',
  '/api/release_platform/independent_grey/stage_info/create',
  '/api/release_platform/independent_grey/meego_test',

  '/api/query_jwt_token',
  '/api/auto_update_lynx_template',
  '/api/create_mr_for_update_template',
  '/api/query_black_list',
  '/api/fetch_current_and_next_versions',

  '/api/pa_coze_api/test_flow',
  '/api/pa_coze_api/test_llm_bot',
  '/api/find_checklist',

  '/api/pa_docs/create_new_doc',
  '/api/report_tea_event',
  '/api/get_stage_summary',
  '/api/fetch_app_all_version_info',
  '/api/fetch_version_info',
  '/api/update_check_item',
  '/api/release_platform_api/delete_check_item',
  // 架构工具
  '/api/engineer-arch/ios/component/author_departments',

  '/api/release_platform/version_report/get_all_spreadsheet_content',
  '/api/release_platform/version_report/get_all_spreadsheet_content_plain',
  '/api/release_platform/version_report/get_spreadsheet_token',
  '/api/release_platform/version_report/get_spreadsheet_sub_sheets',
  '/api/release_platform/version_report/update_single_block',
  '/api/release_platform/version_report/get_all_table_info',
  '/api/release_platform/version_report/get_all_doc_content',
  '/api/release_platform/version_report/get_version_transaction_info',
  '/api/release_platform/version_report/get_all_spreadsheet_info',
  '/api/release_platform/version_report/sheet_fill_service_test',
  '/api/release_platform/version_report/update_spreadsheet_content',
  '/api/release_platform/version_report/get_view_work_item_config',
  '/api/release_platform/version_report/update_condition_view',
  '/api/release_platform/version_report/sheet_fill_agent/unexpected_merge_agent',
  '/api/release_platform/version_report/sheet_fill_agent/requirement_quality_summary_agent',
  '/api/release_platform/version_report/sheet_fill_agent/gray_quality_summary_agent',
  '/api/release_platform/version_report/sheet_fill_agent/version_quality_summary_agent',
  '/api/release_platform/version_report/sheet_fill_agent/inserted_requirement_agent',
  '/api/release_platform/version_report/sheet_fill_agent/version_quality_key_issues_agent',
  '/api/release_platform/independent_grey/approval/delete_all_v2',
  '/api/release_platform/version_report/get_aeolus_test',
  '/api/release_platform/version_report/all_agent_test',
  '/api/release_platform/version_report/sheet_fill_agent/RDCoreMetricAgent',
  '/api/release_platform/version_report/sheet_fill_agent/RDStageMetric',
  '/api/release_platform/version_report/sheet_fill_agent/HighRoundMRAgent',
  '/api/release_platform/version_report/generate_rd_report_test',
  '/api/release_platform/version_report/generate_rd_report_test_copy',
  '/api/release_platform/version_report/rd_doc_test',
  '/api/release_platform/transaction/find',
  '/api/engineer-arch/ios/noti/editor_common_added',
  '/api/vscode_extension_version',

  // TBC 项目
  '/api/tbc/code_sync_notify',
  '/api/tbc/branch_sync',
  '/api/tbc/test_close',
  '/api/tbc/check_if_valid_for_in_process_branch_sync_mr',
  '/api/tbc/fetch_ttp_opened_mr_list',
  '/api/tbc/create_ttp_release_branch_and_mr',
  '/api/tbc/ttp_code_frozen_finished_notify',
  '/api/tbc/create_mr',
  '/api/tbc/test',

  // MCP
  '/api/mcp',
];
export default skipLoginPath;
