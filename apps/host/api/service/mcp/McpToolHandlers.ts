import { Inject, Injectable } from '@gulux/gulux';
import Mcp<PERSON><PERSON><PERSON>ibraHand<PERSON> from './tool-handlers/McpToolLibraHandler';
import { McpToolBaseHandler, McpToolDefinition, McpToolRsp } from '@shared/mcp/mcp-info';
import McpToolVersionHandler from './tool-handlers/McpToolVersionHandle';
import McpToolCodeFreezeHandler from './tool-handlers/McpToolCodeFreezeHandler';
import McpToolMrApprovalHandler from './tool-handlers/McpToolMrApprovalHandler';
import McpToolExperimentByVersionHandler from './tool-handlers/McpToolExperimentByVersionHandler';
import McpToolExperimentsByMeegoHandler from './tool-handlers/McpToolExperimentsByMeegoHandler';
import McpToolComponentInfoHandler from './tool-handlers/McpToolComponentInfoHandler';

@Injectable()
export default class McpToolHandlers {
  @Inject()
  private libraHandler: McpToolLibraHandler;
  @Inject()
  private versionHandler: McpToolVersionHandler;
  @Inject()
  private codeFreezeHandler: McpToolCodeFreezeHandler;
  @Inject()
  private mrApprovalHandler: McpToolMrApprovalHandler;
  @Inject()
  private experimentByVersionHandler: McpToolExperimentByVersionHandler;
  @Inject()
  private experimentsByMeegoHandler: McpToolExperimentsByMeegoHandler;
  @Inject()
  private componentInfoHandler: McpToolComponentInfoHandler;

  // 所有 mcp tool handler
  allToolHandlers(): McpToolBaseHandler[] {
    return [
      this.libraHandler,
      this.versionHandler,
      this.codeFreezeHandler,
      this.mrApprovalHandler,
      this.experimentByVersionHandler,
      this.experimentsByMeegoHandler,
      this.componentInfoHandler,
    ];
  }

  // 所有 mcp tool definition
  allToolDefinitions(): McpToolDefinition[] {
    return this.allToolHandlers().flatMap(handler => handler.toolDefinition());
  }

  // handle 分发处理
  async didHandle(toolName: string, args: any) {
    const givenHandler = this.allToolHandlers().find(handler =>
      handler.toolDefinition().find((def: McpToolDefinition) => def.name === toolName),
    );
    if (givenHandler) {
      return await givenHandler.toolHandler(toolName, args);
    }

    return {
      content: [
        {
          type: 'text',
          text: `未找到对应的 tool handler: ${toolName}`,
        },
      ],
    } as McpToolRsp;
  }
}
