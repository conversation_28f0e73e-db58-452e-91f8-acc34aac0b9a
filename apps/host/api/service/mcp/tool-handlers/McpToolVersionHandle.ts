import { Inject, Injectable } from '@gulux/gulux';
import { BytedLogger } from '@gulux/gulux/byted-logger';
import { McpToolBaseHandler, McpToolDefinition, McpToolRsp } from '@shared/mcp/mcp-info';
import VersionProcessInfoDao from '../../dao/releasePlatform/VersionProcessInfoDao';
import {
  BugMrForbidPreviewDesc,
  fullDesc,
  fullReleaseStageNames,
  getNextNotStartStage,
  getOnProgressStages,
  getStageFullDisplayName,
  getStageInfo,
  grayStageNames,
  smallFlowStageNames,
  sortVersions,
  VersionFullReleasePlan,
} from '@shared/releasePlatform/releasePlatformUtils';
import BusinessConfigService from '@pa/backend/dist/src/service/businessConfig';
import { BusinessAppInfo, MAIN_HOST_HTTPS } from '@pa/shared/dist/src/appSettings/appSettings';
import StageServiceFactory from '../../releasePlatform/stageServiceFactory';
import {
// 由于模块没有导出 BaseReleaseStageExtraData，暂时注释掉该导入
// BaseReleaseStageExtraData,
  formatTimestamp2Month,
// 由于模块没有导出 SubGrayExtraData，暂时注释掉该导入
// SubGrayExtraData,
  VersionProcessInfo,
  VersionProcessStatus,
  VersionStageStatus,
} from '@shared/releasePlatform/versionStage';
import { BmType } from '@shared/bits/bmInfo';
import { BaseReleaseStageExtraData, SubGrayExtraData } from '@shared/releasePlatform/versionStageInfoCheckList';
@Injectable()
export default class McpToolVersionHandler implements McpToolBaseHandler {
  @Inject()
  private logger: BytedLogger;
  @Inject()
  private versionProcessInfoDao: VersionProcessInfoDao;
  @Inject()
  private bussinessConfigService: BusinessConfigService;
  @Inject()
  private stageServiceFactory: StageServiceFactory;

  toolDefinition(): McpToolDefinition[] {
    return [
      {
        name: 'get_version_progress',
        description: '查询剪映/CapCut/醒图的版本进度',
        inputSchema: {
          type: 'object',
          properties: {
            appName: { type: 'string' },
            platform: { type: 'string' },
            version: { type: 'string', optional: true },
          },
          required: ['appName', 'platform'],
        },
      },
      {
        name: 'get_mr_access_info',
        description: '查询剪映/CapCut/醒图的MR能否准入',
        inputSchema: {
          type: 'object',
          properties: {
            appName: { type: 'string' },
            platform: { type: 'string' },
            version: { type: 'string', optional: true },
          },
          required: ['appName', 'platform'],
        },
      },
      {
        name: 'get_version_release_time_info',
        description: '查询剪映/CapCut/醒图的版本全量/提审时间',
        inputSchema: {
          type: 'object',
          properties: {
            appName: { type: 'string' },
            platform: { type: 'string' },
            version: { type: 'string', optional: true },
          },
          required: ['appName', 'platform'],
        },
      },
      {
        name: 'get_version_bm_info',
        description: '查询剪映/CapCut/醒图的版本BM',
        inputSchema: {
          type: 'object',
          properties: {
            appName: { type: 'string' },
            platform: { type: 'string' },
            version: { type: 'string', optional: true },
          },
          required: ['appName', 'platform'],
        },
      },
      {
        name: 'get_small_version_info',
        description: '查询剪映/CapCut/醒图的小版本号',
        inputSchema: {
          type: 'object',
          properties: {
            appName: { type: 'string' },
            platform: { type: 'string' },
            version: { type: 'string', optional: true },
          },
          required: ['appName', 'platform'],
        },
      },
    ];
  }

  async toolHandler(toolName: string, args: any): Promise<McpToolRsp> {
    switch (toolName) {
      case 'get_version_progress':
        return await this.handleGetVersionInfo(toolName, args);
      case 'get_mr_access_info':
        return await this.handleGetVersionInfo(toolName, args);
      case 'get_version_release_time_info':
        return await this.handleGetVersionInfo(toolName, args);
      case 'get_version_bm_info':
        return await this.handleGetVersionInfo(toolName, args);
      case 'get_small_version_info':
        return await this.handleGetVersionInfo(toolName, args);
      default:
        return {
          content: [
            {
              type: 'text',
              text: `未找到对应的 tool handler: ${toolName}`,
            },
          ],
        };
    }
  }

  private async handleGetVersionInfo(toolName: string, args: any): Promise<McpToolRsp> {
    const { appName, platform, version } = args;

    // 参数校验
    if (!appName || typeof appName !== 'string') {
      return {
        content: [
          {
            type: 'text',
            text: 'appName 不能为空，且必须是字符串',
          },
        ],
      } as McpToolRsp;
    }
    if (!platform || typeof platform !== 'string') {
      return {
        content: [
          {
            type: 'text',
            text: 'platform 不能为空，且必须是字符串',
          },
        ],
      } as McpToolRsp;
    }
    const appInfoList = await this.bussinessConfigService.getAppList();
    const appInfo = appInfoList.find(it => {
      let match = true;
      if (appName) {
        match = match && it.app_name === appName;
      }
      if (platform) {
        match = match && it.platform === platform;
      }
      return match;
    });
    if (!appInfo) {
      return {
        content: [
          {
            type: 'text',
            text: '未查询到对应的app信息',
          },
        ],
      } as McpToolRsp;
    }
    // 构建查询条件
    const listQuery: { [key: string]: any } = {
      app_id: appInfo.app_id,
    };
    if (version) {
      listQuery.version = version;
    } else {
      listQuery.status = VersionProcessStatus.OnProgress;
    }
    // 查询版本信息
    const versionProcessInfoList = await this.versionProcessInfoDao.list(listQuery);

    if (!versionProcessInfoList?.length) {
      return {
        content: [
          {
            type: 'text',
            text: '未查询到版本信息',
          },
        ],
      } as McpToolRsp;
    }
    let displayVersions;
    const sortedVersions = sortVersions(versionProcessInfoList).slice(0, 3);

    if (toolName === 'get_version_progress') {
      displayVersions = await this.convertVersionProgressToKeyValue(sortedVersions, appInfo);
    } else if (toolName === 'get_mr_access_info') {
      displayVersions = this.convertMrAccessToKeyValue(sortedVersions, appInfo);
    } else if (toolName === 'get_version_release_time_info') {
      displayVersions = this.convertReleaseTimeToKeyValue(sortedVersions, appInfo);
    } else if (toolName === 'get_version_bm_info') {
      displayVersions = this.convertBMInfoToKeyValue(sortedVersions, appInfo);
    } else if (toolName === 'get_small_version_info') {
      displayVersions = this.convertUpdateCodeToKeyValue(sortedVersions, appInfo);
    }

    // 返回查询结果
    return {
      content: [
        {
          type: 'text',
          text: `版本信息：${JSON.stringify(displayVersions)}`,
        },
      ],
    } as McpToolRsp;
  }

  private async convertVersionProgressToKeyValue(versionInfos: VersionProcessInfo[], appInfo: BusinessAppInfo) {
    const versionInfoPromises = versionInfos.map(async versionInfo => {
      const result: Record<string, any> = {
        版本号: versionInfo.version,
      };

      if (versionInfo.status === VersionProcessStatus.End) {
        result['版本状态'] = `已全量，${fullDesc(versionInfo)}`;
      } else if (versionInfo.status === VersionProcessStatus.Fail) {
        result['版本状态'] = '已跳版';
      } else {
        result['正在进行的阶段'] = getOnProgressStages(versionInfo.version_stages).map(onProgressStage => {
          const mainStage = onProgressStage.parent_stage_name?.length
            ? getStageInfo(versionInfo.version_stages, onProgressStage.parent_stage_name)
            : onProgressStage;

          const subStage = onProgressStage.parent_stage_name?.length ? onProgressStage : undefined;
          const stageService = this.stageServiceFactory.getServiceForStageName(
            onProgressStage.stage_name,
            onProgressStage.parent_stage_name,
          );

          const stageSummary = stageService ? stageService.stageSummary(versionInfo, mainStage!, subStage) : '';

          return {
            阶段名称: getStageFullDisplayName(versionInfo, onProgressStage.stage_name),
            阶段摘要: stageSummary,
          };
        });

        const nextStage = getNextNotStartStage(versionInfo.version_stages);
        if (nextStage) {
          const mainStage = nextStage.parent_stage_name?.length
            ? getStageInfo(versionInfo.version_stages, nextStage.parent_stage_name)
            : nextStage;
          const subStage = nextStage.parent_stage_name?.length ? nextStage : undefined;
          const stageService = this.stageServiceFactory.getServiceForStageName(
            nextStage.stage_name,
            nextStage.parent_stage_name,
          );
          const notStartSummary = stageService
            ? await stageService.notStartSummary(versionInfo, mainStage!, subStage)
            : '';
          result['下一阶段'] = {
            阶段名称: getStageFullDisplayName(versionInfo, nextStage.stage_name),
            阶段摘要: notStartSummary,
          };
        }
      }
      return result;
    });

    return {
      '应用名称-平台-版本状态标题': `${appInfo.app_name}-${appInfo.platform}-版本状态`,
      版本信息: await Promise.all(versionInfoPromises),
      跳转链接: `${MAIN_HOST_HTTPS}/release/list?appid=${appInfo.app_id}`,
    };
  }

  private convertMrAccessToKeyValue(versionInfos: VersionProcessInfo[], appInfo: BusinessAppInfo) {
    return {
      '应用名称-平台-版本状态标题': `${appInfo.app_name}-${appInfo.platform}-MR准入信息`,
      版本信息: versionInfos.map(versionInfo => {
        let accessInfo = '';
        if (versionInfo.status === VersionProcessStatus.End || versionInfo.status === VersionProcessStatus.Fail) {
          accessInfo = '版本已结束，不允许合入';
        } else if (versionInfo.version_stages[0].status === VersionStageStatus.NotStart) {
          accessInfo = '未封版，无限制';
        } else {
          accessInfo = BugMrForbidPreviewDesc(versionInfo).join('，');
        }
        return {
          版本号: versionInfo.version,
          准入信息: accessInfo,
        };
      }),
      跳转链接: `${MAIN_HOST_HTTPS}/release/list?appid=${appInfo.app_id}`,
    };
  }

  private convertReleaseTimeToKeyValue(versionInfos: VersionProcessInfo[], appInfo: BusinessAppInfo) {
    return {
      '应用名称-平台-版本状态标题': `${appInfo.app_name}-${appInfo.platform}-全量/提审时间`,
      卡片模板: 'blue',
      版本信息: versionInfos.map(versionInfo => {
        const plan = VersionFullReleasePlan(versionInfo, appInfo).reverse();
        return {
          版本号: versionInfo.version,
          全量计划: plan.join('，'),
        };
      }),
      跳转链接: `${MAIN_HOST_HTTPS}/release/list?appid=${appInfo.app_id}`,
    };
  }

  private convertBMInfoToKeyValue(versionInfos: VersionProcessInfo[], appInfo: BusinessAppInfo) {
    const qualityBmTypeList = [
      BmType.anr_crash,
      BmType.oom_crash,
      BmType.java_crash,
      BmType.native_crash,
      BmType.iosoom,
      BmType.crash,
      BmType.ioswatchdog,
    ];

    return {
      '应用名称-平台-版本状态标题': `${appInfo.app_name}-${appInfo.platform}-BM信息`,
      版本信息: versionInfos.map(versionInfo => {
        let atQualityBm = '';
        for (const atQualityBmElement of qualityBmTypeList) {
          if (versionInfo.bmInfo[atQualityBmElement]?.email !== undefined) {
            atQualityBm += `<at email="${versionInfo.bmInfo[atQualityBmElement].email}"></at>`;
          }
        }
        return {
          版本号: versionInfo.version,
          RDBM: versionInfo.bmInfo[BmType.rd]?.email ?? '未指定',
          QABM: versionInfo.bmInfo[BmType.qa]?.email ?? '未指定',
          质量BM: atQualityBm.length > 0 ? atQualityBm : '未指定',
        };
      }),
      跳转链接: `${MAIN_HOST_HTTPS}/release/list?appid=${appInfo.app_id}`,
    };
  }

  private convertUpdateCodeToKeyValue(versionInfos: VersionProcessInfo[], appInfo: BusinessAppInfo) {
    return {
      '应用名称-平台-版本状态标题': `${appInfo.app_name}-${appInfo.platform}-小版本号信息`,
      版本信息: versionInfos.map(versionInfo => {
        const versionUpdateCodeDescs: string[] = [];
        for (const grayStageName of grayStageNames) {
          const stageInfo = getStageInfo(versionInfo.version_stages, grayStageName);
          if (stageInfo) {
            for (const subStage of stageInfo.sub_stages) {
              const extraData = subStage.extra_data as SubGrayExtraData;
              if (extraData?.versionCode) {
                versionUpdateCodeDescs.push(
                  `${subStage.display_name}：${extraData.versionCode}，发布时间：${formatTimestamp2Month(
                    extraData.releaseTime * 1000,
                  )}`,
                );
              }
            }
          }
        }
        for (const smallFlowStageName of smallFlowStageNames) {
          const stageInfo = getStageInfo(versionInfo.version_stages, smallFlowStageName);
          const extraData = stageInfo?.extra_data as BaseReleaseStageExtraData;
          if (extraData?.releaseInfo) {
            versionUpdateCodeDescs.push(
              `小流量：${extraData.releaseInfo.update_version ?? '未知'}，发布时间：${
                extraData.releaseInfo.publish_time
                  ? formatTimestamp2Month(extraData.releaseInfo.publish_time * 1000)
                  : '未知'
              }`,
            );
          }
        }
        for (const fullReleaseStageName of fullReleaseStageNames) {
          const stageInfo = getStageInfo(versionInfo.version_stages, fullReleaseStageName);
          const extraData = stageInfo?.extra_data as BaseReleaseStageExtraData;
          if (extraData?.releaseInfo) {
            versionUpdateCodeDescs.push(
              `全量包：${extraData.releaseInfo.update_version ?? '未知'}，发布时间：${
                extraData.releaseInfo.publish_time
                  ? formatTimestamp2Month(extraData.releaseInfo.publish_time * 1000)
                  : '未知'
              }`,
            );
          }
        }
        return {
          版本号: versionInfo.version,
          小版本号信息: versionUpdateCodeDescs.length > 0 ? versionUpdateCodeDescs.join('，') : '未查询到任何发布版本',
        };
      }),
      跳转链接: `${MAIN_HOST_HTTPS}/release/list?appid=${appInfo.app_id}`,
    };
  }

}
