import { HostRpcService, HostRpcServiceSymbol } from '@pa/backend/dist/src/rpc/hostRpcService';
import { Inject, Injectable } from '@gulux/gulux';
import { MsgTemplate } from '@pa/shared/dist/src/message';
import MessageService from '@pa/backend/dist/src/service/message';
import { UserIdType } from '@pa/shared/dist/src/lark/userInfo';
import { FetchVersionLogParams } from '@pa/shared/dist/src/bits';
import BitsService from './third/bits';
import ExperimentSource from './dao/ExperimentSource';
import AlogActionLarkCardService from './alogActionLarkCard';
import { GetLogActionInfoResponse, GetLogIssueInfoResponse } from '@pa/shared/dist/src/alog/models';
import AcceptanceFeatureNotificationService from './acceptanceCenter/acceptanceFeatureNotificationService';
import { VersionProcessInfoService } from './releasePlatform/versionProcessInfoService';
import {
  ExperimentRequestInfo,
  GeneralRequestInfo,
  GroupRequestInfo,
  IndicatorType,
  LTRequestInfo,
} from '@shared/experiment/experimentInfo';
import CircuitBreakerService from './circuitBreaker/circuitBreakerService';

@Injectable({ id: HostRpcServiceSymbol })
export default class HostRpcServiceImpl implements HostRpcService {
  @Inject()
  private messageService: MessageService;
  @Inject()
  private bitsService: BitsService;
  @Inject()
  private experimentSource: ExperimentSource;
  @Inject()
  private alogActionLarkCardService: AlogActionLarkCardService;
  @Inject()
  private acceptanceFeatureNotificationService: AcceptanceFeatureNotificationService;
  @Inject()
  private versionProcessInfoService: VersionProcessInfoService;
  @Inject()
  private circuitBreakerService: CircuitBreakerService;

  async isAfterThirdGray(version: string, appId: number) {
    return this.versionProcessInfoService.isAfterThirdGray(version, appId);
  }

  async isFullRelease(version: string, appId: number) {
    return this.versionProcessInfoService.isFullRelease(version, appId);
  }

  async sendNormalMsg(msg: MsgTemplate, userIdType: UserIdType, targetId: string) {
    return this.messageService.sendNormalMsg(msg, userIdType, targetId);
  }

  async fetchBitsVersionLog(params: FetchVersionLogParams) {
    const request = this.bitsService.createRequest();
    const resp = await request.get<any>('/v1/release/release_record/list', params);
    return resp?.data?.release_records || [];
  }

  async fetchExperimentErrorList(id: string) {
    return this.experimentSource.getFlightErrorList(id);
  }

  async getLast2Version(appId: number) {
    return await this.versionProcessInfoService.getLast2Version(appId);
  }

  async getExperimentWhiteList() {
    return await this.experimentSource.getWhiteList();
  }

  async addExperimentInfo(data: unknown) {
    return await this.experimentSource.addExperimentInfo(data);
  }

  async addExperimentLTInfo(data: unknown) {
    return await this.experimentSource.addExperimentLTInfo(data as LTRequestInfo[]);
  }

  async addExperimentGroupInfo(data: unknown) {
    return await this.experimentSource.addExperimentGroupInfo(data as GroupRequestInfo[]);
  }

  async addExperimentFeedbackInfo(data: unknown) {
    return await this.experimentSource.addFeedbackInfo(data);
  }

  async addExperimentOtherInfo(data: unknown, type: string) {
    return await this.experimentSource.addExperimentOtherInfo(data as ExperimentRequestInfo[], type as IndicatorType);
  }

  async getRetouchExperimentWarningVersion(appId: number) {
    const version = await this.versionProcessInfoService.getRetouchExperimentWarningVersion(appId);
    return {
      version,
    };
  }

  async getLibraControlSetting() {
    return await this.versionProcessInfoService.getLibraControlSetting();
  }

  buildErrorLarkCard(errorMessage: string) {
    return this.alogActionLarkCardService.buildErrorLarkCard(errorMessage);
  }

  buildNoActionLarkCard() {
    return this.alogActionLarkCardService.buildNoActionLarkCard();
  }

  buildAlogUserActionLarkCard(actionResponse: GetLogActionInfoResponse, issueInfo: GetLogIssueInfoResponse) {
    return this.alogActionLarkCardService.buildAlogUserActionLarkCard(actionResponse, issueInfo);
  }

  async getMeegoChatId(meego: string, meegoId: number, pmEmail: string | undefined) {
    const chatId = await this.acceptanceFeatureNotificationService.getMeegoChatId(meego, meegoId, pmEmail);
    return {
      chatId,
    };
  }

  async slardarAlarmCluster(body: any): Promise<boolean> {
    return await this.circuitBreakerService.slardarAlarmClusterAndCreate(body);
  }

  async getVersionIntegrationTime(version: string, appId: number) {
    return await this.versionProcessInfoService.getVersionIntegrationTime(version, appId);
  }

  async addExperimentCommercialInfo(data: unknown) {
    return await this.experimentSource.addExperimentCommercialInfo(data as GeneralRequestInfo[]);
  }

  async getPullOfflineVersion(appId: number) {
    return await this.versionProcessInfoService.getAllVersions(appId);
  }

  async getPullVersionsByIntegrationTime(appId: number) {
    return await this.versionProcessInfoService.getPullVersionsByIntegrationTime(appId);
  }
}
