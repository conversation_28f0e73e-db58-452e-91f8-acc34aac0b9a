import { CustomBuildBaseHandler } from '../../../utils/customBuildBaseHandler';
import { BuildResult } from '@shared/customBuild/buildResult';
import { CustomBuildRecord } from '@shared/customBuild';
import { UserIdType } from '@pa/shared/dist/src/lark/userInfo';
import { CardElement, CardElementTag, CardMarkdownElement, MessageType } from '@pa/shared/dist/src/lark/larkCard';
import { PlatformType } from '@pa/shared/dist/src/core';
import { Inject, Injectable } from '@gulux/gulux';
import LarkService from '@pa/backend/dist/src/third/lark';
import LarkCardService from '../../../service/larkCard';
import { BytedLogger } from '@gulux/gulux/byted-logger';
import HandlerUtils from './handlerUtils';

@Injectable()
export default class PreciseTestBuildHandler implements CustomBuildBaseHandler {
  handlerName = 'PreciseTestBuildHandler';

  @Inject()
  private lark: LarkService;

  @Inject()
  private larkCard: LarkCardService;

  @Inject()
  private logger: BytedLogger;

  @Inject()
  private handlerUtils: HandlerUtils;

  async handlerCancel(data: BuildResult, customInfo: CustomBuildRecord) {
    const extra = customInfo.extra as { version: string; chatId: string };
    const card = this.larkCard.buildSimpleActionCard('覆盖率包构建取消', data.data.job.jobURL, '点击跳转构建地址');
    await this.lark.sendMessage(UserIdType.chatId, extra.chatId, JSON.stringify(card), MessageType.interactive);
  }

  async handlerError(data: BuildResult, customInfo: CustomBuildRecord) {
    const extra = customInfo.extra as { version: string; chatId: string };
    const card = this.larkCard.buildSimpleActionCard('覆盖率包构建失败', data.data.job.jobURL, '点击跳转构建地址');
    await this.lark.sendMessage(UserIdType.chatId, extra.chatId, JSON.stringify(card), MessageType.interactive);
  }

  async handlerSuccess(data: BuildResult, customInfo: CustomBuildRecord) {
    const extra = customInfo.extra as {
      version: string;
      chatId: string;
      onlyCC?: boolean;
    };
    if (data?.data?.job) {
      const card = await this.handlerUtils.buildSuccessCard(data.data.job, customInfo);
      if (card.code === 0 && card.card) {
        const element = card.card.elements.find((value: CardElement) => value.tag === CardElementTag.markdown);
        if (element) {
          const markdownElement = element as CardMarkdownElement;
          markdownElement.content += `\n**构建版本:**\t\t\t\t${extra.version}`;
        }
      }
      this.logger.info(JSON.stringify(card.card));
      // 卡片发到剪映封板群
      await this.lark.sendMessage(
        UserIdType.chatId,
        extra.chatId && extra.chatId !== '' ? extra.chatId : '',
        JSON.stringify(card.card),
        MessageType.interactive,
      );
    }
  }
}
