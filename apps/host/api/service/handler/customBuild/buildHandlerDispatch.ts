import { Inject, Injectable } from '@gulux/gulux';
import { BytedLogger } from '@gulux/gulux/byted-logger';
import { TemplateBuildExtraInfo, TemplatePackageType } from '@shared/bits/templateBuildExtraInfo';
import { PlatformType } from '@pa/shared/dist/src/core';
import { CustomBuildResultType, CustomBuildType } from '@shared/customBuild';
import {
  BuildResult,
  BuildResultArtifactType,
  BuildResultJobType,
  BuildResultType,
} from '@shared/customBuild/buildResult';
import ComponentModelService from '../../../service/model/customBuildModel';
import BitsService from '../../../service/third/bits';
import { CustomBuildBaseHandler } from '../../../utils/customBuildBaseHandler';
import ETestBuildHandler from './eTextHandler';
import MrMergedBuildHandler from './mrMergedBuildHandler';
import OutBuildHandler from './outBuildHandler';
import PreciseTestBuildHandler from './preciseTestBuildHandler';
import QaAutoBuildHandler from './qaAutoBuildHandler';
import RdBuildHandler from './rdBuildHandler';
import SmokeTestBuildHandler from './smokeTestBuildHandler';
import UgTestBuildHandler from './ugTestBuildHandler';
import VeBuildHandler from './veBuildHandler';
import { OutBuildBuildHandler } from './outbuildBuildHandler';
import UserStoryBuildHandler from './userStoryBuildHandler';

@Injectable()
export default class BuildHandlerDispatch {
  @Inject()
  rdBuildHandler: RdBuildHandler;

  @Inject()
  mrMergedBuildHandler: MrMergedBuildHandler;

  @Inject()
  outBuildHandler: OutBuildHandler;

  @Inject()
  veBuildHandler: VeBuildHandler;

  @Inject()
  qaAutoBuildHandler: QaAutoBuildHandler;

  @Inject()
  eTestHandler: ETestBuildHandler;

  @Inject()
  smokeTestBuildHandler: SmokeTestBuildHandler;

  @Inject()
  ugTestBuildHandler: UgTestBuildHandler;

  // @Inject()
  // publishModuleHandler: PublishModuleHandler;

  @Inject()
  preciseTestBuildHandler: PreciseTestBuildHandler;

  @Inject()
  outBuildBuildHandler: OutBuildBuildHandler;

  @Inject()
  private logger: BytedLogger;

  @Inject()
  private bits: BitsService;

  @Inject()
  private componentModelService: ComponentModelService;
  @Inject()
  userStoryBuildHandler: UserStoryBuildHandler;

  private getHandler(key: CustomBuildType): CustomBuildBaseHandler | undefined {
    const handlerMap: Partial<Record<CustomBuildType, keyof BuildHandlerDispatch>> = {
      [CustomBuildType.RD]: 'rdBuildHandler',
      [CustomBuildType.MR_MERGED]: 'mrMergedBuildHandler',
      [CustomBuildType.OUT]: 'outBuildHandler',
      [CustomBuildType.VE]: 'veBuildHandler',
      [CustomBuildType.QA_INTEGRATION_TESTING]: 'qaAutoBuildHandler',
      [CustomBuildType.E_TEST]: 'eTestHandler',
      [CustomBuildType.SMOKE_TEST]: 'smokeTestBuildHandler',
      [CustomBuildType.UG_TEST]: 'ugTestBuildHandler',
      // [CustomBuildType.PUBLISH_MODULE]: 'publishModuleHandler',
      [CustomBuildType.PRECISE_TEST]: 'preciseTestBuildHandler',
      [CustomBuildType.OUT_BUILD]: 'outBuildBuildHandler',
      [CustomBuildType.USER_STORY]: 'userStoryBuildHandler',
      [CustomBuildType.USER_STORY_TF]: 'userStoryBuildHandler',
      [CustomBuildType.USER_STORY_SCHEDULE]: 'userStoryBuildHandler',
    };
    return this[handlerMap[key]!] as unknown as CustomBuildBaseHandler | undefined;
  }

  async dispatch(data: BuildResult) {
    if (data.events_type === BuildResultType.job) {
      try {
        if (data.data.job.jobResult !== '') {
          const jobResult = JSON.parse(data.data.job.jobResult) as TemplateBuildExtraInfo;
          if (jobResult.packageType === TemplatePackageType.CUSTOM_BUILD) {
            await this.dispatchCustomBuild(data, jobResult);
          }
        }
      } catch (e) {
        this.logger.info(`dispatchBuildError req:${JSON.stringify(data)} err:${JSON.stringify(e)}`);
        throw e;
      }
    }
  }

  async dispatchCustomBuild(data: BuildResult, jobResult: TemplateBuildExtraInfo) {
    const buildInfo = await this.componentModelService.searchCustomBuildInfo(jobResult.buildId);
    if (!buildInfo) {
      this.logger.error(`buildInfoRecordNotFound ${jobResult.buildId}`);
      return;
    }

    if (data.events_job_type === BuildResultJobType.success) {
      buildInfo.buildResult = CustomBuildResultType.success;
      buildInfo.artifacts = data.data.job.artifacts ?? []; //
      if (buildInfo.buildParams.arch === PlatformType.iOS) {
        const ipaInfo = data.data.job.artifacts?.find(value => value.type === BuildResultArtifactType.ipa);

        if (ipaInfo) {
          const result = await this.bits.getMrPackageUrl(ipaInfo.name, ipaInfo.url);
          buildInfo.artifacts.push({
            name: ipaInfo.name,
            url: result.data,
            size: ipaInfo.size,
            type: BuildResultArtifactType.iOS_INSTALL,
            md5: ipaInfo.md5,
          });
        }
      }
    } else if (data.events_job_type === BuildResultJobType.cancel) {
      buildInfo.buildResult = CustomBuildResultType.cancel;
    } else if (data.events_job_type === BuildResultJobType.failed) {
      buildInfo.buildResult = CustomBuildResultType.failed;
    }
    await this.componentModelService.recordCustomBuild(buildInfo);

    const buildTypes = Array.isArray(buildInfo.buildType) ? buildInfo.buildType : [buildInfo.buildType];
    for (const buildType of buildTypes) {
      const handler = this.getHandler(buildType);
      if (!handler) {
        continue;
      }
      switch (data.events_job_type) {
        case BuildResultJobType.success:
          await handler.handlerSuccess(data, buildInfo);
          break;
        case BuildResultJobType.cancel:
          await handler.handlerCancel(data, buildInfo);
          break;
        case BuildResultJobType.failed:
          await handler.handlerError(data, buildInfo);
          break;
        case BuildResultJobType.running:
          if (handler.handlerRunning) {
            await handler.handlerRunning(data, buildInfo);
          }
          break;
        default:
          // Handle any other cases or do nothing
          break;
      }
    }
  }
}
