import { BuildResultArtifactType, BuildResultJob } from '@shared/customBuild/buildResult';
import { UserIdType } from '@pa/shared/dist/src/lark/userInfo';
import { Card, MessageType } from '@pa/shared/dist/src/lark/larkCard';
import { CustomBuildRecord, CustomBuildType } from '@shared/customBuild';
import { PlatformType } from '@pa/shared/dist/src/core';
import { encodeData2Qr } from '../../../utils/qrCode';
import { Inject, Injectable } from '@gulux/gulux';
import LarkService from '@pa/backend/dist/src/third/lark';
import LarkCardService from '../../../service/larkCard';
import { JobStepInfo } from '@shared/bits/jobInfo';
import QaTestService from '../../releasePlatform/qaTestService';

@Injectable()
export default class HandlerUtils {
  @Inject()
  private lark: LarkService;

  @Inject()
  private larkCard: LarkCardService;

  @Inject()
  private qaTestService: QaTestService;

  async buildCardAndSendForSuccess(
    userIdType: UserIdType,
    id: string,
    job: BuildResultJob | JobStepInfo,
    customInfo: CustomBuildRecord,
  ) {
    const cardInfo = await this.buildSuccessCard(job, customInfo);
    if (cardInfo.code === 0) {
      await this.lark.sendMessage(userIdType, id, JSON.stringify(cardInfo.card), MessageType.interactive);
    } else if (cardInfo.code === -1) {
      const card = this.larkCard.buildSimpleActionCard(
        '构建完成，打包产物二维码生成失败',
        job.jobURL,
        '点击跳转打包地址',
      );
      await this.lark.sendMessage(userIdType, id, JSON.stringify(card), MessageType.interactive);
    } else {
      const card = this.larkCard.buildSimpleActionCard('构建完成，未找到打包产物', job.jobURL, '点击跳转打包地址');
      await this.lark.sendMessage(userIdType, id, JSON.stringify(card), MessageType.interactive);
    }
  }

  async buildSuccessCard(
    job: BuildResultJob | JobStepInfo,
    customInfo: CustomBuildRecord,
  ): Promise<{ code: number; card?: Card }> {
    if (customInfo.buildParams.arch === PlatformType.Android) {
      const apk = job?.artifacts?.find(value => value.type === BuildResultArtifactType.apk);
      const mapping = job?.artifacts?.find(value => value.type === BuildResultArtifactType.mapping);
      if (apk) {
        const imgKey = await this.uploadApk2Lark(apk.url);
        if (imgKey) {
          let title = customInfo.buildParams.isOversea ? 'Android-CapCut' : 'Android-剪映';
          if (customInfo.buildParams.buildTarget === 'dreamina') {
            title = customInfo.buildParams.isOversea ? 'Android-Dreamina' : 'Android-即梦';
          }
          let card = this.larkCard.createPackageSuccessCard({
            title: `【${title}】构建结果通知`,
            apkUrl: apk.url,
            buildUrl: job.jobURL,
            apkName: apk.name,
            branch: customInfo.lvBranch,
            apkImgKey: imgKey,
            commit: customInfo.commit,
            mappingUrl: mapping?.url,
            params: customInfo.buildParams,
            createdAt: customInfo.createdAt,
          });
          if (
            Array.isArray(customInfo.buildType) &&
            customInfo.buildType.includes(CustomBuildType.QA_INTEGRATION_TESTING) &&
            customInfo.buildType.includes(CustomBuildType.MR_MERGED)
          ) {
            const branch = customInfo.lvBranch;
            const branchArr = branch.split('/');
            if (branchArr.length > 1) {
              const version = branchArr[branchArr.length - 1];
              const notIntegrationMr = await this.qaTestService.getNotIntegrationMrAfterTimestamp(
                version,
                PlatformType.Android,
                customInfo.createdAt ? new Date(customInfo.createdAt).getTime() : 0,
              );
              card = this.larkCard.createPackageSuccessCard({
                title: `【${title}】构建结果通知`,
                apkUrl: apk.url,
                buildUrl: job.jobURL,
                apkName: apk.name,
                branch: customInfo.lvBranch,
                apkImgKey: imgKey,
                commit: customInfo.commit,
                mappingUrl: mapping?.url,
                params: customInfo.buildParams,
                createdAt: customInfo.createdAt,
                notIntegrationMr,
              });
            }
          }
          return { code: 0, card };
        } else {
          return { code: -1 };
        }
      }
    } else {
      const ipaInfo = job.artifacts?.find(value => value.type === BuildResultArtifactType.ipa);
      const iOS_INSTALL = customInfo?.artifacts?.find(value => value.type === BuildResultArtifactType.iOS_INSTALL);
      if (ipaInfo) {
        const imgKey = await this.uploadApk2Lark(iOS_INSTALL?.url ? iOS_INSTALL?.url : ipaInfo?.url);
        if (imgKey) {
          let card = this.larkCard.createPackageSuccessIOSCard({
            title: `【${customInfo.buildParams.isOversea ? 'iOS-CapCut' : 'iOS-剪映'}】构建结果通知`,
            ipaUrl: ipaInfo?.url,
            buildUrl: job.jobURL,
            iOS_INSTALLName: iOS_INSTALL?.name ? iOS_INSTALL?.name : ipaInfo?.name,
            branch: customInfo.lvBranch,
            iOS_INSTALLImgKey: imgKey,
            commit: customInfo.commit,
            params: customInfo.buildParams,
            MrTitle: customInfo.extra.title,
            MrUrl: customInfo.extra.mr_detail_url,
            MrOwner: customInfo.extra.author,
            createdAt: customInfo.createdAt,
          });
          if (
            Array.isArray(customInfo.buildType) &&
            customInfo.buildType.includes(CustomBuildType.QA_INTEGRATION_TESTING) &&
            customInfo.buildType.includes(CustomBuildType.MR_MERGED)
          ) {
            const branch = customInfo.lvBranch;
            const branchArr = branch.split('/');
            if (branchArr.length > 1) {
              const version = branchArr[branchArr.length - 1];
              const notIntegrationMr = await this.qaTestService.getNotIntegrationMrAfterTimestamp(
                version,
                PlatformType.iOS,
                customInfo.createdAt ? new Date(customInfo.createdAt).getTime() : 0,
              );
              card = this.larkCard.createPackageSuccessIOSCard({
                title: `【${customInfo.buildParams.isOversea ? 'iOS-CapCut' : 'iOS-剪映'}】构建结果通知`,
                ipaUrl: ipaInfo?.url,
                buildUrl: job.jobURL,
                iOS_INSTALLName: iOS_INSTALL?.name ? iOS_INSTALL?.name : ipaInfo?.name,
                branch: customInfo.lvBranch,
                iOS_INSTALLImgKey: imgKey,
                commit: customInfo.commit,
                params: customInfo.buildParams,
                MrTitle: customInfo.extra.title,
                MrUrl: customInfo.extra.mr_detail_url,
                MrOwner: customInfo.extra.author,
                createdAt: customInfo.createdAt,
                notIntegrationMr,
              });
            }
          }
          return { code: 0, card };
        } else {
          return { code: -1 };
        }
      }
    }
    return { code: -1 };
  }

  async buildAndroidSuccessCard(job: BuildResultJob | JobStepInfo, customInfo: CustomBuildRecord) {
    const apk = job?.artifacts?.find(value => value.type === BuildResultArtifactType.apk);
    const mapping = job?.artifacts?.find(value => value.type === BuildResultArtifactType.mapping);
    if (apk) {
      const imgKey = await this.uploadApk2Lark(apk.url);
      if (imgKey) {
        let title = customInfo.buildParams.isOversea ? 'Android-CapCut' : 'Android-剪映';
        if (customInfo.buildParams.buildTarget === 'dreamina') {
          title = customInfo.buildParams.isOversea ? 'Android-Dreamina' : 'Android-即梦';
        }
        const card = this.larkCard.buildAndroidAutoBuildSuccessCard({
          title: `【${title}】打包成功`,
          apkUrl: apk.url,
          buildUrl: job.jobURL,
          apkName: apk.name,
          branch: customInfo.lvBranch,
          apkImgKey: imgKey,
          commit: customInfo.commit,
          mappingUrl: mapping?.url,
          params: customInfo.buildParams,
          createdAt: customInfo.createdAt,
        });
        return { code: 0, card };
      } else {
        return { code: -1 };
      }
    }
  }

  async uploadApk2Lark(url: string): Promise<string | undefined> {
    const result = await encodeData2Qr(url);
    return await this.lark.uploadImage(result);
  }
}
