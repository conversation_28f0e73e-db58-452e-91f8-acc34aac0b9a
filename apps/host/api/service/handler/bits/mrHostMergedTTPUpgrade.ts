import { <PERSON><PERSON><PERSON><PERSON> } from '../../../utils/BaseHandler';
import { MrHostMerged } from '@shared/bits/webHook';
import { MrInfo, MrType } from '@shared/bits/mrInfo';
import { Inject, Injectable } from '@gulux/gulux';
import { BytedLogger } from '@gulux/gulux/byted-logger';
import MrModelService from '../../../service/model/mrModule';
import BitsService from '../../third/bits';
import GitLabService from '../../third/gitlab';
import { NetworkCode, PlatformType } from '@pa/shared/dist/src/core';
import { DependencyLock } from '@shared/project';
import MscService from '../../third/msc';
import TccService from '../../third/tcc';
import { TTPSubCodebaseAndroidConfig } from '@shared/tbc/TBCSubCodebaseAndroidInfo';
import { RedisClient } from '@gulux/gulux/redis';
import { UserIdType } from '@pa/shared/dist/src/lark/userInfo';
import {
  TBCSubCodebaseBranchFailCard,
  TBCSubCodebaseMRFailCard,
  TBCSubCodebaseMRSuccessCard,
  TBCSubCodebaseNormalUpgradeNotifySuccessCard,
  TBCSubCodebaseUpgradeFailCard,
  TBCSubCodebaseUpgradeNotifySuccessCard,
  TBCSubCodebaseUpgradeSuccessCard,
} from '../../tbc/TBCSubCodebaseCard';
import MessageService from '@pa/backend/dist/src/service/message';
import BitsConfigV2Service, { MrRepos } from '../../bitsConfigV2';
import repos from '@shared/gitlab/repos';

/**
 * 用于宿主合入后，用于触发 ttp 组件升级的处理器
 */
@Injectable()
export default class MrHostMergedTTPUpgradeHandler implements BaseHandler {
  handlerName = 'MrHostMergedTTPUpgradeHandler';

  @Inject()
  private logger: BytedLogger;

  @Inject()
  private mrModel: MrModelService;

  @Inject()
  private bits: BitsService;

  @Inject()
  private tcc: TccService;

  @Inject()
  private messageService: MessageService;

  @Inject()
  private bitsConfigV2Service: BitsConfigV2Service;

  @Inject()
  private gitlab: GitLabService;

  @Inject()
  private msc: MscService;

  @Inject()
  private redis: RedisClient;

  canHandle(event: string): boolean {
    return event === 'mr_host_merged';
  }

  async handler(data: string) {
    this.logger.info(`[${this.handlerName}]handler:${data}`);
    const mergeData: MrHostMerged = JSON.parse(data);
    if (!mergeData.mr_id) {
      return;
    }
    this.logger.info(`[${this.handlerName}]handler:${mergeData.mr_id}`);
    const bitsMrInfo = await this.bits.getMrInfo({
      mrId: mergeData.mr_id,
    });
    if (!bitsMrInfo) {
      return;
    }
    const realPlatform = await this.bits.getMrRealLvPlatform(bitsMrInfo);
    const androidSubMr = realPlatform.get(PlatformType.Android);
    if (!androidSubMr) {
      // 非 android 不处理
      return;
    }
    const mrRelationInfoList = await this.bits.getMrRelationList(bitsMrInfo.id);
    if (!mrRelationInfoList || mrRelationInfoList.length === 0) {
      // 非含子仓 mr，直接忽略
      return;
    }
    // 只针对含有子仓的 mr
    const tccResult = await this.tcc.getTccModel<TTPSubCodebaseAndroidConfig>('ttp_sub_codebase_android_config');
    if (!tccResult) {
      this.logger.error(`[${this.handlerName}]handler:tccResult error`);
      return;
    }
    const dependencyResult = await this.gitlab.getFileFromRepository(
      `${bitsMrInfo.project_id}`,
      bitsMrInfo.target_branch,
      'dependency-lock.json',
    );
    if (!dependencyResult || !dependencyResult.data) {
      this.logger.error(`[${this.handlerName}]handler:dependencyResult error`);
      return;
    }
    const dependencies = JSON.parse(dependencyResult.data) as DependencyLock;
    for (const relationMrs of mrRelationInfoList) {
      for (const subTTPCodebase of tccResult.sub_ttp_codebase) {
        if (subTTPCodebase.project_id === relationMrs.project_id) {
          // 831414:Compliance ;134284:RetouchMiddleware 这类的子仓可以主动通过触发 ttp 组件升级，而不用先触发普通组件升级
          this.ttpUpgrade(
            bitsMrInfo,
            dependencies,
            subTTPCodebase.artifactId,
            subTTPCodebase.groupId,
            subTTPCodebase.repo_id.toString(),
          ).then();
        }
      }
      for (const subTTPCodebase of tccResult.sub_normal_ttp_codebase) {
        if (subTTPCodebase.project_id === relationMrs.project_id) {
          // 这类子仓，目前无法直接通过 api 来触发 ttp 升级，需要先触发普通组件升级，然后再触发 ttp 组件升级
          this.handleSubNormalTtpCodebase(
            bitsMrInfo,
            subTTPCodebase.artifactId,
            subTTPCodebase.groupId,
            subTTPCodebase.repo_id,
            relationMrs.target_branch,
            dependencies,
          ).then();
        }
      }
    }
  }

  // getTTPUpgradeStatus 定时任务，轮询 ttp 组件升级状态
  async getTTPUpgradeStatus(): Promise<void> {
    const tccResult = await this.tcc.getTccModel<TTPSubCodebaseAndroidConfig>('ttp_sub_codebase_android_config');
    if (tccResult) {
      for (const subTTPCodebase of tccResult.sub_normal_ttp_codebase) {
        await this.checkTTPUpgradeStatus(
          String(subTTPCodebase.repo_id),
          subTTPCodebase.groupId,
          subTTPCodebase.artifactId,
          true,
        );
      }
      for (const subTTPCodebase of tccResult.sub_ttp_codebase) {
        await this.checkTTPUpgradeStatus(
          String(subTTPCodebase.repo_id),
          subTTPCodebase.groupId,
          subTTPCodebase.artifactId,
          false,
        );
      }
    }
    return;
  }

  // checkTTPUpgradeStatus 检测 ttp 组件升级情况
  private async checkTTPUpgradeStatus(
    repo_id: string,
    groupId: string,
    artifactId: string,
    isNormalSub: boolean,
  ): Promise<void> {
    const redisKey = `sub_ttp_${repo_id}`;
    this.logger.info(
      `[${this.handlerName}] checkTTPUpgradeStatus ${redisKey} ${repo_id} ${groupId}:${artifactId} ${isNormalSub}`,
    );
    const redisResult = await this.redis.get(redisKey);
    if (!redisResult) {
      return;
    }
    this.logger.info(`[${this.handlerName}] checkTTPUpgradeStatus ${redisResult}`);
    const versions = JSON.parse(redisResult) as string[];
    let versionsFinal: string[] = versions;
    for (const version of versions) {
      this.logger.info(`[${this.handlerName}] checkTTPUpgradeStatus ${repo_id} ${groupId}:${artifactId}:${version}`);
      const historyDetail = await this.msc.getTTPUpgradeHistoryDetail(String(repo_id), version);
      if (historyDetail && historyDetail.builds && historyDetail.builds.length > 0) {
        for (const build of historyDetail.builds) {
          if (build.status === 2) {
            // 构建成功
            this.logger.info(`[${this.handlerName}] checkTTPUpgradeStatus success ${build.build_url}`);
            const msg = new TBCSubCodebaseUpgradeSuccessCard(groupId, artifactId, version, String(repo_id));
            this.messageService.sendNormalMsg(msg, UserIdType.chatId, 'oc_20bd3222a7d5719b99bdddeb605cf078').then();
            versionsFinal = versionsFinal.filter(item => item !== version);
            if (isNormalSub) {
              // 如果子仓组件是先普通升级，再 ttp 升级，这里需要把版本号写回 dependency-lock.json 里面，并提交 mr
              const redisBranchKey = `sub_ttp_${repo_id}_${version}`;
              const redisBranchResult = await this.redis.get(redisBranchKey);
              if (redisBranchResult) {
                await this.submitTTPMr(version, groupId, artifactId, redisBranchResult);
              }
            }
          } else if (build.status === 3) {
            // 构建失败
            this.logger.info(`[${this.handlerName}] checkTTPUpgradeStatus fail ${build.build_url}`);
            const msg = new TBCSubCodebaseUpgradeFailCard(groupId, artifactId, version, String(repo_id));
            this.messageService.sendNormalMsg(msg, UserIdType.chatId, 'oc_20bd3222a7d5719b99bdddeb605cf078').then();
            versionsFinal = versionsFinal.filter(item => item !== version);
          } else {
            // 构建进行时
            this.logger.info(`[${this.handlerName}] checkTTPUpgradeStatus ${version}`);
          }
        }
      } else {
        versionsFinal = versions;
      }
    }
    await this.redis.set(redisKey, JSON.stringify(versionsFinal));
    await this.redis.expire(redisKey, 24 * 60 * 60);
  }
  // submitTTPMr 提交一个 mr
  private async submitTTPMr(version: string, groupId: string, artifactId: string, targetBranch: string) {
    // 从 redisBranchResult 创建一个新的分支，然后修改 dependency-lock.json 里面的版本号，提交 mr
    const tempBranch = `temp/update_dependency_${groupId}_${artifactId}_${version}`;
    this.logger.info(
      `[${this.handlerName}] submitTTPMr ${groupId}:${artifactId}:${version} ${targetBranch} ${tempBranch}`,
    );
    // test
    // const tempBranch = `temp/update_dependency_com.lemon.lv_commerce-oversea_16.8.0.10.oversea`;
    const ccProjectId = repos.androidTTPMainRepo.projectId; // 写死 cc 主仓
    const branchInfoResult = await this.gitlab.getBranchInfo(ccProjectId, tempBranch);
    if (branchInfoResult.code !== 0) {
      // 如果分支不存在
      const createBranchResult = await this.gitlab.createBranch(ccProjectId, tempBranch, targetBranch);
      if (createBranchResult.code === NetworkCode.Error) {
        const msg = new TBCSubCodebaseBranchFailCard(groupId, artifactId, version, tempBranch, targetBranch);
        this.messageService.sendNormalMsg(msg, UserIdType.chatId, 'oc_20bd3222a7d5719b99bdddeb605cf078').then();
        return;
      }
    }
    const fileContentResult = await this.gitlab.getFileFromRepository(ccProjectId, tempBranch, 'dependency-lock.json');
    if (!fileContentResult.data) {
      return;
    }
    const dependencyLock = JSON.parse(fileContentResult.data) as DependencyLock;
    for (const dependency of dependencyLock.dependencies) {
      if (dependency.groupId === groupId && dependency.artifactId === artifactId) {
        if (dependency.version === version) {
          return;
        }
        dependency.version = version;
      } else if (dependency.flavorType && dependency.targets) {
        for (const target of dependency.targets) {
          if (`${dependency.artifactId}-${target.name}` === artifactId && dependency.groupId === groupId) {
            if (target.version === version) {
              return;
            }
            target.version = version;
          }
        }
      }
    }
    // 提交 commit
    await this.gitlab.createCommit(ccProjectId, `feat: update ${groupId}:${artifactId} to ${version}`, tempBranch, [
      {
        action: 'update',
        file_path: 'dependency-lock.json',
        content: JSON.stringify(dependencyLock, null, 4),
      },
    ]);
    // 创建 MR
    const createAndroidMRRet = await this.innerCreateMR(
      ccProjectId,
      groupId,
      artifactId,
      version,
      tempBranch,
      targetBranch,
    );
    if (createAndroidMRRet.code === NetworkCode.Error) {
      // 创建失败了
      const msg = new TBCSubCodebaseMRFailCard(groupId, artifactId, version, tempBranch, targetBranch);
      this.messageService.sendNormalMsg(msg, UserIdType.chatId, 'oc_20bd3222a7d5719b99bdddeb605cf078').then();
      return;
    }
    if (createAndroidMRRet.code === NetworkCode.Success && createAndroidMRRet.data) {
      // 发送通知
      const mrInfo: MrInfo | undefined = await this.bits.getMrInfo({
        mrId: createAndroidMRRet.data.mr_id,
      });
      if (mrInfo) {
        const msg = new TBCSubCodebaseMRSuccessCard(groupId, artifactId, version, mrInfo);
        this.messageService.sendNormalMsg(msg, UserIdType.chatId, 'oc_20bd3222a7d5719b99bdddeb605cf078').then();
      }
    }
  }
  // innerCreateMR 提交 Android cc 的 mr
  private async innerCreateMR(
    projectId: number,
    groupId: string,
    artifactId: string,
    version: string,
    sourcesBranch: string,
    targetBranch: string,
  ) {
    const userName = 'xulu.luke';
    const mrRepos: MrRepos[] = [];
    mrRepos.push({
      projectId,
      sourcesBranch,
      targetBranch,
      isHost: true,
      platform: PlatformType.Android,
    });
    const mrParams = await this.bitsConfigV2Service.buildMrConfig({
      title: `CC TTP 子仓组件${groupId}:${artifactId} to ${version}更新`,
      repos: mrRepos,
      targetVersion: '',
      author: userName,
      type: MrType.merge,
      customFields: {
        range_of_influence: '纸飞机后台发起MR，和对应操作人进行确认',
        how_to_test_issue: '纸飞机后台发起MR，和对应操作人进行确认',
        the_test_results: 1,
        disable_compress_ld_for_inhouse: true, // iOS 屏蔽压缩链接器
        MR_FORBID_PACKAGE_CHECK: true, // iOS 屏蔽包大小检测
      },
      wip: false,
      review_start_type: 1,
    });
    mrParams.mr_reviewers = []; // 置空一下 Reviewer
    mrParams.review_fetch_mode = 2;

    // 创建 MR
    return await this.bits.createMr(mrParams, userName);
  }

  // handleSubNormalTtpCodebase 先触发普通组件升级，再触发 ttp 组件升级
  private async handleSubNormalTtpCodebase(
    mrInfo: MrInfo,
    artifactId: string,
    groupId: string,
    repo_id: number,
    target_branch: string,
    dependencies: DependencyLock,
  ) {
    // 这类子仓，目前无法直接通过 api 来触发 ttp 升级，需要走单独的一套逻辑
    if (dependencies.dependencies && dependencies.dependencies.length > 0) {
      // test 测试定时逻辑
      // this.getTTPUpgradeStatus().then();
      // return;
      this.logger.info(`[${this.handlerName}]handleSubNormalTtpCodebase:artifactId${groupId}:${artifactId}`);
      for (const dependency of dependencies.dependencies) {
        let newVersion = '';
        if (dependency.flavorType && dependency.targets) {
          // flavor 组件
          for (const target of dependency.targets) {
            if (`${dependency.artifactId}-${target.name}` === artifactId && dependency.groupId === groupId) {
              newVersion = `${target.version}-cc-ttp`; // 这里默认在原来版本的基础上，加上后缀
              break;
            }
          }
        } else if (dependency.artifactId === artifactId && dependency.groupId === groupId && dependency.version) {
          newVersion = `${dependency.version}-cc-ttp`; // 这里默认在原来版本的基础上，加上后缀
        }
        if (newVersion !== '') {
          this.logger.info(`[${this.handlerName}]handleSubNormalTtpCodebase:newVersion${newVersion}`);
          this.msc
            .repoUpgrade(repo_id, target_branch, '纸飞机自动触发', newVersion, '<EMAIL>')
            .then(async () => {
              await this.msc.triggerTTPBuildByRepoId(repo_id.toString(), 'TTP(US)', newVersion);
              await this.saveIntoRedis(String(repo_id), newVersion);
              await this.saveBranchIntoRedis(String(repo_id), newVersion, mrInfo.target_branch);
              await this.normalTTPLarkNotify(mrInfo, groupId, artifactId, newVersion, String(repo_id));
            });
        }
      }
    }
  }

  // ttpUpgrade 触发 ttp 组件升级
  private async ttpUpgrade(
    mrInfo: MrInfo,
    dependencies: DependencyLock,
    artifactId: string,
    groupId: string,
    repo_id: string,
  ) {
    if (dependencies.dependencies && dependencies.dependencies.length > 0) {
      this.logger.info(`[${this.handlerName}]ttpUpgrade:artifactId${groupId}:${artifactId}`);
      // test 测试定时逻辑
      // this.getTTPUpgradeStatus().then();
      // return;
      for (const dependency of dependencies.dependencies) {
        let newVersion = '';
        if (dependency.flavorType && dependency.targets) {
          // flavor 组件
          for (const target of dependency.targets) {
            if (`${dependency.artifactId}-${target.name}` === artifactId && dependency.groupId === groupId) {
              newVersion = target.version;
            }
          }
        } else if (dependency.artifactId === artifactId && dependency.groupId === groupId && dependency.version) {
          // 非 flavor 组件
          newVersion = dependency.version;
        }
        if (newVersion !== '') {
          this.logger.info(`[${this.handlerName}]ttpUpgrade:newVersion${newVersion}`);
          this.msc.triggerTTPBuildByRepoId(repo_id, 'TTP(US)', newVersion).then();
          this.saveIntoRedis(repo_id, newVersion).then();
          this.ttpLarkNotify(mrInfo, groupId, artifactId, newVersion, repo_id).then();
        }
      }
    }
  }

  // saveIntoRedis 保存到 redis 中
  private async saveIntoRedis(repo_id: string, dependencyVersion: string) {
    const redisKey = `sub_ttp_${repo_id}`;
    this.logger.info(`[${this.handlerName}] saveIntoRedis ${redisKey} ${dependencyVersion}`);
    const redisResult = await this.redis.get(redisKey);
    if (redisResult) {
      this.logger.info(`[${this.handlerName}]${redisResult}`);
      const versions = JSON.parse(redisResult) as string[];
      if (!versions.includes(dependencyVersion)) {
        versions.push(dependencyVersion);
        await this.redis.set(redisKey, JSON.stringify(versions));
        await this.redis.expire(redisKey, 24 * 60 * 60);
      }
    } else {
      const versions: string[] = [];
      versions.push(dependencyVersion);
      await this.redis.set(redisKey, JSON.stringify(versions));
      await this.redis.expire(redisKey, 24 * 60 * 60);
    }
  }

  private async saveBranchIntoRedis(repo_id: string, dependencyVersion: string, hostBranch: string) {
    const redisKey = `sub_ttp_${repo_id}_${dependencyVersion}`;
    this.logger.info(`[${this.handlerName}] saveBranchIntoRedis ${redisKey}`);
    await this.redis.set(redisKey, hostBranch);
    await this.redis.expire(redisKey, 24 * 60 * 60);
  }

  // ttpLarkNotify 子仓组件 ttp 升级通知
  private async ttpLarkNotify(mrInfo: MrInfo, groupId: string, artifactId: string, version: string, repo_id: string) {
    const chatId = 'oc_20bd3222a7d5719b99bdddeb605cf078'; // TTP环境打包监控群
    const msg = new TBCSubCodebaseUpgradeNotifySuccessCard(mrInfo, groupId, artifactId, version, repo_id);
    this.messageService.sendNormalMsg(msg, UserIdType.chatId, chatId).then();
  }

  // normalTTPLarkNotify 子仓组件普通 + ttp 升级通知
  private async normalTTPLarkNotify(
    mrInfo: MrInfo,
    groupId: string,
    artifactId: string,
    version: string,
    repo_id: string,
  ) {
    const chatId = 'oc_20bd3222a7d5719b99bdddeb605cf078'; // TTP环境打包监控群
    const msg = new TBCSubCodebaseNormalUpgradeNotifySuccessCard(mrInfo, groupId, artifactId, version, repo_id);
    this.messageService.sendNormalMsg(msg, UserIdType.chatId, chatId).then();
  }
}
