import { <PERSON>Handler } from '../../../utils/BaseHandler';
import { ReleaseCheckOutInfo } from '@shared/bits/webHook';
import { UserIdType } from '@pa/shared/dist/src/lark/userInfo';
import { MessageType } from '@pa/shared/dist/src/lark/larkCard';
import { ChatInfo, PlatformType } from '@pa/shared/dist/src/core';
import { MrInfo } from '@shared/bits/mrInfo';
import { SpaceType } from '@shared/bus/busType';
import { BytedLogger } from '@gulux/gulux/byted-logger';
import { Inject, Injectable } from '@gulux/gulux';
import { ModelType } from '@gulux/gulux/typegoose';
import { VersionProcessTable } from '../../../model/VersionProcessInfo';
import CustomBuildService from '../../customBuild';
import LarkService from '@pa/backend/dist/src/third/lark';
import LarkCardService from '../../larkCard';
import MeegoOldService from '../../meegoOld';
import CollectAbnormalMultiMrService from '../../tasks/collectAbnormalMultiMr';
import AirplaneConfigService from '../../AirplaneConfigService';
import { VersionConfigKeys } from '@shared/aircraftConfiguration';
import CreateNewVersionV2 from '../consul/createNewVersionV2';
import { useInject } from '@edenx/runtime/bff';
import VersionProcessDao from '../../dao/VersionProcessDao';
import VersionProcessInfoDao from '../../dao/releasePlatform/VersionProcessInfoDao';
import VersionFeatureService from '../../releasePlatform/versionFeatureService';
import { AppSettingId } from '@pa/shared/dist/src/appSettings/appSettings';
import MergeRequestService from '../../mergeRequest';
import { cc2lvVersion, lv2ccVersion } from '../../../utils/versionUtils';
import ReleasePlatformMessageService, {
  ReleasePlatformMessageGroupType,
  ReleasePlatformMessageSource,
} from '../../releasePlatform/releasePlatformMessageService';
import { VersionTransactionType } from '@shared/releasePlatform/versionTransaction';
import { MsgStrategy } from '@pa/shared/dist/src/message';
import { ReleasePlatformTestChatId } from '../../releasePlatform/releasePlatformUtil';
import axios from 'axios';

@Injectable()
export default class ReleaseCheckOutInformService implements BaseHandler {
  handlerName = 'ReleaseCheckOutInformService';

  canHandle(event: string): boolean {
    return event === 'codefrozen_succeed';
  }

  @Inject()
  private logger: BytedLogger;

  @Inject(VersionProcessTable)
  private versionProcessModel: ModelType<VersionProcessTable>;

  @Inject()
  private customBuild: CustomBuildService;

  @Inject()
  private lark: LarkService;

  @Inject()
  private larkCard: LarkCardService;

  @Inject()
  private meegoOldService: MeegoOldService;

  @Inject()
  private collectAbnormalMultiMr: CollectAbnormalMultiMrService;

  @Inject()
  private configService: AirplaneConfigService;

  @Inject()
  private versionProcessInfoDao: VersionProcessInfoDao;

  @Inject()
  private versionFeatureService: VersionFeatureService;

  @Inject()
  private mergeRequest: MergeRequestService;

  @Inject()
  private messageService: ReleasePlatformMessageService;

  async informdata(version: string, groupname: string) {
    const url = 'https://vo816h2r.fn.bytedance.net';
    let platform: string, app: string;
    switch (groupname) {
      case SpaceType.Android:
        platform = 'Android';
        app = 'LV';
        break;
      case SpaceType.iOS:
        platform = 'iOS';
        app = 'LV';
        break;
      case SpaceType.PC:
        platform = 'PC';
        app = 'LV';
        break;
      case SpaceType.Retouch_iOS:
        platform = 'iOS';
        app = 'Retouch';
        break;
      case SpaceType.Retouch_Android:
        platform = 'Android';
        app = 'Retouch';
        break;
      case SpaceType.TTP_Android:
        platform = 'Android';
        app = 'capcut';
        break;
      case SpaceType.TTP_iOS:
        platform = 'iOS';
        app = 'capcut';
        break;
      default:
        platform = 'null';
        app = 'null';
        break;
    }
    try {
      const res = await axios.post(url, {
        version,
        platform,
        App: app,
      });
    } catch (error) {
      this.logger.error(`[informdataError]: ${JSON.stringify(error)}`);
    }
  }

  async handler(data: string) {
    try {
      this.logger.info(`[releaseCheckoutInform]:${JSON.stringify(data)}`);
      const releaseCheckoutData: ReleaseCheckOutInfo = JSON.parse(data);
      await this.informdata(releaseCheckoutData.integration_version.version, releaseCheckoutData.group_name);
      if (releaseCheckoutData.group_name === SpaceType.iOS) {
        // TODO 埋点封板完成时，版本集成构建开始
        // 触发一次iOS集成包构建
        const result = await this.customBuild.customBuildWithMrMerged({
          target_branch: `release/${releaseCheckoutData.integration_version.version}`,
          platform: PlatformType.iOS,
        } as MrInfo);
        // await this.updateExtraData(releaseCheckoutData.integration_version.version, PlatformType.iOS, result);
        // TODO 埋点封板完成时，版本集成出包，vT2
      } else if (releaseCheckoutData.group_name === SpaceType.Android) {
        const chatInfoList = (await this.configService.queryConfigItem(
          '1775',
          VersionConfigKeys.checkoutReleaseChatIds,
        )) as ChatInfo[];
        chatInfoList.map(value => value.chat_id);
        const chat_ids: string[] = chatInfoList.map(value => value.chat_id).filter(value => value) as string[];
        const card = this.larkCard.buildReleaseCheckoutInformCard(
          releaseCheckoutData.integration_version.version,
          false,
        );

        for (const chat_id of chat_ids) {
          await this.lark.sendMessage(UserIdType.chatId, chat_id, JSON.stringify(card), MessageType.interactive).then();
        }
        // 触发一次Android集成包构建
        const result = await this.customBuild.customBuildWithMrMerged({
          target_branch: `release/${releaseCheckoutData.integration_version.version}`,
          platform: PlatformType.Android,
        } as MrInfo);
        // await this.updateExtraData(releaseCheckoutData.integration_version.version, PlatformType.Android, result);
        // Android 逻辑或者双端只需要执行一次的通用逻辑
        // 封板完成时，检查集成测试单已创建的情况下，立刻触发一次集成包构建
        const currentVersion = releaseCheckoutData.integration_version.version;
        const versionMeego = await useInject(VersionProcessDao).findVersionMeego(currentVersion);
        // TODO 埋点封板完成时，版本集成构建开始
        if (versionMeego?.androidMeegoId) {
          await this.customBuild.customBuildWithVersion(versionMeego.androidMeegoId);
          await this.customBuild.customBuildWithUgTest(versionMeego.androidMeegoId);
        }
        // TODO 埋点封板完成时，版本集成出包，vT2
        // TODO @liuhao 更新时间

        for (const p of [PlatformType.Android, PlatformType.iOS]) {
          // 触发更新一下纸飞机需求列表
          await this.meegoOldService.queryStoryProgressInfoEasy(releaseCheckoutData.integration_version.version, p);
          // 创建BM群
          await this.collectAbnormalMultiMr.getOrCreateBMGroupChat(currentVersion, p);
        }
        // 触发创建下个版本，防止包大小错误
        await useInject(CreateNewVersionV2).handler(currentVersion);
        await this.versionFeatureService.updateVersionFeatureInfo(
          AppSettingId.LV_IOS,
          releaseCheckoutData.integration_version.version,
        );
        await this.versionFeatureService.updateVersionFeatureInfo(
          AppSettingId.LV_ANDROID,
          releaseCheckoutData.integration_version.version,
        );
        await this.versionFeatureService.sendVersionFeatureInfoCard(
          1775,
          releaseCheckoutData.integration_version.version,
        );
        setTimeout(() => {
          this.versionFeatureService.sendUnifiedAdCheckOutInfo(
            releaseCheckoutData.integration_version.version,
            false,
            false,
          );
        }, 30000);
        // setTimeout(() => {
        //   this.mergeRequest.createPreciseTestMR(releaseCheckoutData.integration_version.version);
        // }, 60000);
      } else if (releaseCheckoutData.group_name === SpaceType.TTP_iOS) {
        // 通知一下分支拉出
        const chatInfoList = (await this.configService.queryConfigItem(
          '1775',
          VersionConfigKeys.checkoutReleaseChatIds,
        )) as ChatInfo[];
        chatInfoList.map(value => value.chat_id);
        const chat_ids: string[] = chatInfoList.map(value => value.chat_id).filter(value => value) as string[];
        const card = this.larkCard.buildReleaseCheckoutInformCard(
          releaseCheckoutData.integration_version.version,
          true,
        );
        for (const chat_id of chat_ids) {
          await this.lark.sendMessage(UserIdType.chatId, chat_id, JSON.stringify(card), MessageType.interactive).then();
        }
        // cc 触发构建
        const result = await this.customBuild.customBuildWithMrMerged({
          target_branch: `overseas/release/${releaseCheckoutData.integration_version.version}`,
          platform: PlatformType.iOS,
        } as MrInfo);
      } else if (releaseCheckoutData.group_name === SpaceType.TTP_Android) {
        const result = await this.customBuild.customBuildWithMrMerged({
          target_branch: `overseas/release/${releaseCheckoutData.integration_version.version}`,
          platform: PlatformType.Android,
        } as MrInfo);
        setTimeout(() => {
          this.versionFeatureService.sendUnifiedAdCheckOutInfo(
            releaseCheckoutData.integration_version.version,
            true,
            false,
          );
        }, 30000);
      } else if (releaseCheckoutData.group_name === SpaceType.Retouch_iOS) {
        const chatId = 'oc_7d9eda316a806f13a9a7549bd37dcd04';
        const card = this.larkCard.buildPCOrRetouchReleaseCheckoutInformCard(
          releaseCheckoutData.integration_version.version,
          PlatformType.iOS,
        );
        await this.lark.sendMessage(UserIdType.chatId, chatId, JSON.stringify(card), MessageType.interactive);
        const versionInfo = await this.versionProcessInfoDao.findOneByCriteria({
          app_id: 251501,
          version: releaseCheckoutData.integration_version.version,
        });
        if (versionInfo) {
          const integrationTestStage = versionInfo?.version_stages?.find(
            v => v?.stage_name === 'retouch_ios_integrationTest',
          );
          if (integrationTestStage) {
            integrationTestStage.real_start_time = Math.floor(new Date().getTime() / 1000);
            await this.versionProcessInfoDao.updateVersionProcessInfo(versionInfo);
          }
        }
      } else if (releaseCheckoutData.group_name === SpaceType.Retouch_Android) {
        const chatId = 'oc_7d9eda316a806f13a9a7549bd37dcd04';
        const card = this.larkCard.buildPCOrRetouchReleaseCheckoutInformCard(
          releaseCheckoutData.integration_version.version,
          PlatformType.Android,
        );
        await this.lark.sendMessage(UserIdType.chatId, chatId, JSON.stringify(card), MessageType.interactive);
        const versionInfo = await this.versionProcessInfoDao.findOneByCriteria({
          app_id: 251502,
          version: releaseCheckoutData.integration_version.version,
        });
        if (versionInfo) {
          const integrationTestStage = versionInfo?.version_stages?.find(
            v => v?.stage_name === 'retouch_adr_integrationTest',
          );
          if (integrationTestStage) {
            integrationTestStage.real_start_time = Math.floor(new Date().getTime() / 1000);
            await this.versionProcessInfoDao.updateVersionProcessInfo(versionInfo);
          }
        }
        setTimeout(() => {
          this.versionFeatureService.sendUnifiedAdCheckOutInfo(
            releaseCheckoutData.integration_version.version,
            false,
            true,
          );
        }, 30000);
      } else if (releaseCheckoutData.group_name === SpaceType.PC) {
        const chatId = 'oc_83bdad3e1650e16c3bad8dbf763a0da7';
        const card = this.larkCard.buildPCOrRetouchReleaseCheckoutInformCard(
          releaseCheckoutData.integration_version.version,
          PlatformType.PC,
        );
        this.lark.sendMessage(UserIdType.chatId, chatId, JSON.stringify(card), MessageType.interactive).then();
      }
    } catch (e) {
      this.logger.info(`[releaseCheckoutInformErr] ${JSON.stringify(e)}`);
    }
  }

  async updateExtraData(version: string, platform: PlatformType, result: any) {
    this.logger.info(`[updateExtraData] ${JSON.stringify(result)}`);
    let appId = 177501;
    if (platform === PlatformType.Android) {
      appId = 177502;
    }
    const currentVersion = await this.versionProcessInfoDao.findOneByCriteria({
      version,
      app_id: appId,
    });
    if (currentVersion) {
      let integrationTest = currentVersion.version_stages.find(it => it.stage_name === 'integrationTest');
      if (appId === 177502) {
        integrationTest = currentVersion.version_stages.find(it => it.stage_name === 'adr_integrationTest');
      }
      if (integrationTest) {
        const subTest = integrationTest.sub_stages.find(it => it.stage_name === 'sub_integration_test');
        if (subTest && result && result.result.data) {
          subTest.extra_data = {
            build_id: result.result.data.buildId,
            cc_build_id: result.overseaResult.data.buildId,
          };
          await this.versionProcessInfoDao.updateByCriteria({ version, app_id: appId }, currentVersion, false);
        }
      }
    }
  }
}
