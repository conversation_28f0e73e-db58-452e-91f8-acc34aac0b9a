import { Inject, Injectable } from '@gulux/gulux';
import TBCAdCodeSyncRecordDao from '../dao/tbc/TBCAdCodeSyncRecordDao';
import GitLabService from '../third/gitlab';
import {
  TBCAdBranchSyncFinishedNotifyCard,
  TBCAdBranchSyncNotFinishedNotifyCard,
  TBCAdCodeSyncCard,
} from './TBCAdCodeSyncCard';
import MessageService from '@pa/backend/dist/src/service/message';
import { nanoid } from 'nanoid';
import { BytedLogger } from '@gulux/gulux/byted-logger';
import { UserIdType } from '@pa/shared/dist/src/lark/userInfo';
import { dayjs } from '@pa/shared/dist/src/utils/dayjs';
import { ConflictedType, MrInfo, MrState, MrType } from '@shared/bits/mrInfo';
import { TBC_AD_BRANCH_CONFIG, TBC_AD_REPO_CONFIG } from '@shared/tbc/TBCAdCodeSyncRecordInfo';
import BitsService from '../third/bits';
import BitsConfigV2Service, { MrRepos } from '../bitsConfigV2';
import LarkService from '@pa/backend/dist/src/third/lark';
import { NetworkCode, NetworkResult, PlatformType } from '@pa/shared/dist/src/core';
import { PublishType } from '@shared/bits/createMrData';

@Injectable()
export class TBCAdCodeSyncService {
  @Inject()
  tbcAdCodeSyncRecordDao: TBCAdCodeSyncRecordDao;

  @Inject()
  gitlabService: GitLabService;

  @Inject()
  messageService: MessageService;

  @Inject()
  private logger: BytedLogger;

  @Inject()
  private bits: BitsService;

  @Inject()
  private bitsConfigV2Service: BitsConfigV2Service;

  @Inject()
  private larkService: LarkService;

  async getRecords() {
    return this.tbcAdCodeSyncRecordDao.find({}, null, { sort: { createTime: -1 } });
  }

  // 修改为通过 bits 合入
  async syncBranchAndCreateMr(
    projectId: number,
    sourceBranch: string,
    targetBranch: string,
    userName: string,
    raiseReview = true,
  ) {
    if (projectId !== 536633 && projectId !== 540549) {
      return {
        message: 'projectId 无效',
        code: NetworkCode.Error,
      } as NetworkResult<any>;
    }
    const mappingName = (name: string) => name.split('/').join('_');
    const midBranch = `p/tbc.robot.merge.ad/${mappingName(sourceBranch)}_to_${mappingName(targetBranch)}_${nanoid(6)}`;
    const ret: NetworkResult<any> = await this.gitlabService.compare(
      projectId.toString(),
      targetBranch,
      sourceBranch,
      false,
    );
    if (ret.code === NetworkCode.Error) {
      return ret;
    }
    if (!ret.data?.diffs || ret.data?.diffs.length === 0) {
      return {
        message: '分支无差异，无需创建MR',
        code: NetworkCode.Error,
      } as NetworkResult<any>;
    }

    // 判断一下是否已经有分支同步 MR 发起
    const syncRecord = await this.tbcAdCodeSyncRecordDao.findOne({
      originSourceBranch: sourceBranch,
      targetBranch,
      projectId,
      inProgress: true,
    });
    if (syncRecord) {
      return {
        message: `已有分支同步 MR 发起，请稍后再试：${syncRecord.mrUrl}`,
        code: NetworkCode.Error,
      } as NetworkResult<any>;
    }

    // 创建中间临时分支
    await this.gitlabService.createBranch(projectId, midBranch, sourceBranch);

    // 构建 MR 参数
    const mrRepos: MrRepos[] = [];
    mrRepos.push({
      projectId,
      sourcesBranch: midBranch,
      targetBranch,
      isHost: true,
      platform: projectId === 536633 ? PlatformType.Android : PlatformType.iOS, // 1: Android, 2: iOS
    });

    const mrParams = await this.bitsConfigV2Service.buildMrConfig({
      title: `[TTP-Ad] Merge ${sourceBranch} to ${targetBranch} [中间分支： ${midBranch}]`,
      repos: mrRepos,
      targetVersion: '',
      author: userName,
      type: MrType.merge,
      customFields: {
        range_of_influence: '纸飞机后台发起MR，和对应操作人进行确认',
        how_to_test_issue: '纸飞机后台发起MR，和对应操作人进行确认',
        the_test_results: 1,
        disable_compress_ld_for_inhouse: true, // iOS 屏蔽压缩链接器
        MR_FORBID_PACKAGE_CHECK: true, // iOS 屏蔽包大小检测
      },
      wip: false,
      review_start_type: raiseReview ? 0 : 1,
    });
    if (!raiseReview) {
      mrParams.mr_reviewers = []; // 置空一下 Reviewer
      mrParams.review_fetch_mode = 2;
    }
    if (projectId === 536633) {
      mrParams.hosts[0].components = [
        {
          host_project_id: projectId,
          repos: [
            {
              component_id: 42133,
              publish_type: PublishType.auto,
              version: {
                correct: true,
              },
            },
            {
              component_id: 49874,
              publish_type: PublishType.auto,
              version: {
                correct: true,
              },
            },
            {
              component_id: 49947,
              publish_type: PublishType.auto,
              version: {
                correct: true,
              },
            },
            {
              component_id: 42081,
              publish_type: PublishType.auto,
              version: {
                correct: true,
              },
            },
            {
              component_id: 42079,
              publish_type: PublishType.auto,
              version: {
                correct: true,
              },
            },
            {
              component_id: 50622,
              publish_type: PublishType.auto,
              version: {
                correct: true,
              },
            },
            {
              component_id: 41200,
              publish_type: PublishType.auto,
              version: {
                correct: true,
              },
            },
          ],
        },
      ];
    }
    // 创建 MR
    const result = await this.bits.createMr(mrParams, userName);

    if (result.code === NetworkCode.Success && result.data) {
      // 创建 MR 成功，获取 MR 信息
      const mrInfo = await this.bits.getMrInfo({
        mrId: result.data.mr_id,
      });

      if (mrInfo) {
        // 保存记录
        const record = await this.tbcAdCodeSyncRecordDao.create({
          projectId,
          sourceBranch,
          sourceShadowBranch: midBranch,
          targetBranch,
          mrId: mrInfo.id,
          mrIid: mrInfo.iid,
          mrUrl: mrInfo.mr_detail_url,
          creator: userName,
          createTime: dayjs(new Date()).tz('Asia/Shanghai').unix(),
          status: MrState.opened,
          title: `[TTP-Ad] Merge ${sourceBranch} to ${targetBranch}`,
        });

        // 发送通知
        const userIdType = UserIdType.chatId;
        const sendId =
          projectId === 540549 ? 'oc_34df7b6f49f7be1095228f3a5242df26' : 'oc_e5da2283546028c45f0e9b57a4cd93e1'; // TBC-广告仓库分支同步
        const card = new TBCAdCodeSyncCard(record);
        await this.messageService.sendNormalMsg(card, userIdType, sendId);

        return record;
      }
    }

    return null;
  }

  // 定时同步广告分支
  async syncAdBranches() {
    const results = [];
    for (const [projectId, config] of Object.entries(TBC_AD_BRANCH_CONFIG)) {
      try {
        const result = await this.syncBranchAndCreateMr(
          Number(projectId),
          config.sourceBranch,
          config.targetBranch,
          projectId === '536633' ? 'chenkuikui' : 'zhangjingshuo.gz',
        );
        results.push({
          projectId,
          repoName: TBC_AD_REPO_CONFIG[projectId],
          result,
        });
      } catch (error) {
        this.logger.error(`Failed to sync branches for project ${projectId}: ${error}`);
        results.push({
          projectId,
          repoName: TBC_AD_REPO_CONFIG[projectId],
          error,
        });
      }
    }
    return results;
  }

  // 更新广告分支同步记录状态
  async updateAdCodeSyncRecordWhenMrClosedOrMerged(mrId: number, timestamp: number, isMerged: boolean) {
    const record = await this.tbcAdCodeSyncRecordDao.findOne({ mrId });
    if (record) {
      if (isMerged) {
        const mrInfo: MrInfo | undefined = await this.bits.getMrInfo({
          mrId: record.mrId,
        });
        if (mrInfo) {
          const msg = new TBCAdBranchSyncFinishedNotifyCard(record);

          const userData = await this.larkService.batchGetUserId(UserIdType.openId, {
            emails: [record.creator.concat('@bytedance.com')],
          });
          if (userData && userData.length > 0) {
            await this.messageService.sendNormalMsg(msg, UserIdType.openId, userData[0].user_id);
          }
          await this.messageService.sendNormalMsg(
            msg,
            UserIdType.chatId,
            record.projectId === 540549 ? 'oc_34df7b6f49f7be1095228f3a5242df26' : 'oc_e5da2283546028c45f0e9b57a4cd93e1',
          ); // 在群里也发一下
        }
      }
      const currentRecord = record;
      currentRecord.status = isMerged ? MrState.merged : MrState.closed;
      await this.tbcAdCodeSyncRecordDao.save(currentRecord);
      return currentRecord;
    }
  }

  // 检查进行中的广告分支同步 MR 是否有报错
  async checkIfValidForInProcessAdCodeSyncMr() {
    const syncRecordList = await this.tbcAdCodeSyncRecordDao.find({ status: MrState.opened });
    if (syncRecordList && syncRecordList.length > 0) {
      for (const syncRecord of syncRecordList) {
        const mrInfo = await this.bits.getMrInfo({
          mrId: syncRecord.mrId,
        });
        if (mrInfo) {
          const userData = await this.larkService.batchGetUserId(UserIdType.openId, {
            emails: [mrInfo.author.concat('@bytedance.com')],
          });
          if (
            (mrInfo.conflicted === ConflictedType.conflicted || mrInfo.pipeline_status === 'failed') &&
            mrInfo.state !== 'closed'
          ) {
            // 有冲突，或者流水线没过，通知处理
            const msg = new TBCAdBranchSyncNotFinishedNotifyCard(
              mrInfo,
              syncRecord.sourceBranch,
              syncRecord.projectId === 536633 ? 'faceu-android/unified_adloader' : 'faceu-ios/LVUniteAd',
            );
            if (userData && userData.length > 0) {
              await this.messageService.sendNormalMsg(msg, UserIdType.openId, userData[0].user_id);
            }
            const sendId =
              mrInfo.project_id === 540549
                ? 'oc_34df7b6f49f7be1095228f3a5242df26'
                : 'oc_e5da2283546028c45f0e9b57a4cd93e1'; // TBC-广告仓库分支同步
            await this.messageService.sendNormalMsg(msg, UserIdType.chatId, sendId);
          }
        }
      }
    }
    return {
      code: 0,
      message: 'success',
    };
  }
}
