import { Inject, Injectable } from '@gulux/gulux';
import { BytedLogger } from '@gulux/gulux/byted-logger';
import PcCrashRecordDao from '../dao/releasePlatform/PcCrashRecordDao';
import { PcCrashRecordModelTable } from '../../model/releasePlatform/PcCrashRecordModel';


// If you created an interface for PcCrashRecord, import it here.
// e.g. import { IPcCrashRecord } from '@shared/releasePlatform/pcCrashRecord';

// Use the model's class directly for data types if no separate interface is defined.
type PcCrashRecord = PcCrashRecordModelTable;

@Injectable()
export class PcCrashRecordService {
  @Inject()
  private pcCrashRecordDao: PcCrashRecordDao;

  @Inject()
  private logger: BytedLogger;

  async createPcCrashRecord(data: PcCrashRecord): Promise<PcCrashRecord> {
    try {
      const newRecord = await this.pcCrashRecordDao.create(data);
      this.logger.info('[PcCrashRecordService] Record created successfully', { issue_id: newRecord.issue_id });
      return newRecord;
    } catch (error) {
      this.logger.error('[PcCrashRecordService] Error creating record', { error, data });
      throw error;
    }
  }

  async getPcCrashRecordById(issue_id: string): Promise<PcCrashRecord | null> {
    try {
      return await this.pcCrashRecordDao.findById(issue_id);
    } catch (error) {
      this.logger.error('[PcCrashRecordService] Error finding record by ID', { error, issue_id });
      throw error;
    }
  }

  async getAllPcCrashRecords(filter: any = {}): Promise<PcCrashRecord[]> {
    try {
      return await this.pcCrashRecordDao.findAll(filter);
    } catch (error) {
      this.logger.error('[PcCrashRecordService] Error finding all records', { error, filter });
      throw error;
    }
  }

  async updatePcCrashRecord(issue_id: string, data: Partial<PcCrashRecord>): Promise<PcCrashRecord | null> {
    try {
      const updatedRecord = await this.pcCrashRecordDao.update(issue_id, data);
      if (updatedRecord) {
        this.logger.info('[PcCrashRecordService] Record updated successfully', { issue_id });
      }
      return updatedRecord;
    } catch (error) {
      this.logger.error('[PcCrashRecordService] Error updating record', { error, issue_id, data });
      throw error;
    }
  }

  async deletePcCrashRecord(issue_id: string): Promise<boolean> {
    try {
      const result = await this.pcCrashRecordDao.delete(issue_id);
      const success = (result.deletedCount || 0) > 0;
      if (success) {
        this.logger.info('[PcCrashRecordService] Record deleted successfully', { issue_id });
      }
      return success;
    } catch (error) {
      this.logger.error('[PcCrashRecordService] Error deleting record', { error, issue_id });
      throw error;
    }
  }

  async deleteMultiplePcCrashRecords(filter: any): Promise<number> {
    try {
      const result = await this.pcCrashRecordDao.deleteMany(filter);
      const count = result.deletedCount || 0;
      this.logger.info(`[PcCrashRecordService] ${count} records deleted successfully`, { filter });
      return count;
    } catch (error) {
      this.logger.error('[PcCrashRecordService] Error deleting multiple records', { error, filter });
      throw error;
    }
  }

  // async updateMultiplePcCrashRecords(filter: any, update: Partial<PcCrashRecord>): Promise<number> {
  //   try {
  //     const result = await this.pcCrashRecordDao.updateMany(filter, update);
  //     const count = result.modifiedCount || 0;
  //     this.logger.info(`[PcCrashRecordService] ${count} records updated successfully`, { filter, update });
  //     return count;
  //   } catch (error) {
  //     this.logger.error('[PcCrashRecordService] Error updating multiple records', { error, filter, update });
  //     throw error;
  //   }
  // }

  async countPcCrashRecords(filter: any = {}): Promise<number> {
    try {
      return await this.pcCrashRecordDao.count(filter);
    } catch (error) {
      this.logger.error('[PcCrashRecordService] Error counting records', { error, filter });
      throw error;
    }
  }
}