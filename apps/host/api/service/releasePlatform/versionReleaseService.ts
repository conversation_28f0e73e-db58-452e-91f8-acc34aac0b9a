import { Inject, Injectable } from '@gulux/gulux';
import { VersionProcessInfoService } from './versionProcessInfoService';
import VersionProcessInfoDao from '../dao/releasePlatform/VersionProcessInfoDao';
import { BytedLogger } from '@gulux/gulux/byted-logger';
import {
  competeStage,
  SubIntegrationExtraData,
  turnStageToSpecificSubStage,
  VersionProcessInfo,
  VersionProcessStatus,
  VersionStageInfo,
  VersionStageStatus,
} from '@shared/releasePlatform/versionStage';
import { VersionStageCheckListService } from './versionStageCheckListService';
import {
  AppleFullReleaseExtraData,
  CheckItemStatus,
  ItemType,
  PCManualCheckItemName,
  SmallFlowExtraData,
  SubmitPackageCheckStatus,
  SubTestFlightExtraData,
  VersionStageCheckItem,
  VersionStageCheckList,
} from '@shared/releasePlatform/versionStageInfoCheckList';
import { CallBackToast, Card, CardButtonType, CardCallback, CardTemplate } from '@pa/shared/dist/src/lark/larkCard';
import { Temporal } from '@js-temporal/polyfill';
import commonUtils from '../../utils/commonUtils';
import VersionReleaseCardService from './VersionReleaseCard';
import { sample } from 'lodash';
import LarkService from '@pa/backend/dist/src/third/lark';
import TestFlightStageService from './stageServices/testFlightStageService';
import { EarlyWarningNotifyStrategyService } from './earlyWarningNotifyStrategyService';
import StageServiceFactory from './stageServiceFactory';
import VersionUtilService from '../utils/VersionUtilService';
import { UserData, UserIdType } from '@pa/shared/dist/src/lark/userInfo';
import { CreateChatArgs } from '@pa/shared/dist/src/lark/createChat';
import { AppId2Name } from '@shared/experiment/experimentInfo';
import { ChatInfo, NetworkCode, PlatformType, User } from '@pa/shared/dist/src/core';
import ReleasePlatformMessageService, {
  ReleasePlatformMessageGroupType,
  ReleasePlatformMessageSource,
} from './releasePlatformMessageService';
import {
  FunctionalBugItemInfo,
  ManuallyCheckItemInfo,
  ManuallyCheckItemStatus,
  StabilityMetricItemInfo,
  StabilityMetricsItemStatus,
} from '@shared/releasePlatform/versionStageCheckItemInfo';
import { ReleasePlatformUtilService } from './releasePlatformUtil';
import { BmType } from '@shared/bits/bmInfo';
import { VersionAutoDriveService } from './versionAutoDrive/versionAutoDriveService';
import ComponentModelService from '../model/customBuildModel';
import { CustomBuildParam, CustomBuildResultType, CustomBuildType } from '@shared/customBuild';
import { VersionCodeFrozenService } from './codeFrozenService';
import MeegoService from '../third/meego';
import { QATestStage } from '@shared/releasePlatform/QATestConfig';
import QATestConfigDao from '../dao/releasePlatform/QATestConfigDao';
import QaTestService from './qaTestService';
import CustomBuildService from '../customBuild';
import AirplaneConfigService from '../AirplaneConfigService';
import { VersionConfigKeys } from '@shared/aircraftConfiguration';
import axios from 'axios';
import {
  completeVersionStage,
  getNextNotStartStage,
  getNextVersionStage,
  getOnProgressStages,
  getStageInfo,
  isPC,
  markStageDelayIfNeeded,
  sortVersions,
  startVersionStage,
} from '@shared/releasePlatform/releasePlatformUtils';
import { ReleasePlatformBotReactCardService } from './releasePlatformBotReactCard';
import { DevelopmentEfficiencyService } from '../efficiency/DevelopmentEfficiencyService';
import { EfficiencyVersionStage } from '@shared/effeciency/BasicEfficiency';
import dayjs from 'dayjs';
import { useInject } from '@edenx/runtime/bff';
import ReleasePackageSubmitInfoDao from '../dao/releasePlatform/ReleasePackageSubmitInfoDao';
import { ReleasePackageSubmitInfoService } from './releasePackageSubmitService';
import { versionStageRejectHandleCardService } from './versionStageRejectHandleCardService';
import {
  AppSetting,
  AppSettingId,
  AppSettingSymbol,
  BusinessAppInfo,
} from '@pa/shared/dist/src/appSettings/appSettings';
import BusinessConfigService from '@pa/backend/dist/src/service/businessConfig';
import { SortSubmitInfos } from '../utils/releasePackageSubmitInfo';
import VersionReportService from './versionReview/VersionReportService';
import ForceTurnVersionStageInfoDao from '../dao/releasePlatform/forceTurnVersionStageInfoDao';
import { VersionTransactionType } from '@shared/releasePlatform/versionTransaction';
import { MsgStrategy } from '@pa/shared/dist/src/message';

@Injectable()
export default class VersionReleaseService {
  @Inject()
  private versionProcessService: VersionProcessInfoService;
  @Inject()
  private versionProcessDao: VersionProcessInfoDao;
  @Inject()
  private logger: BytedLogger;
  @Inject()
  private checklistService: VersionStageCheckListService;
  @Inject()
  private versionReleaseCard: VersionReleaseCardService;
  @Inject()
  private lark: LarkService;
  @Inject()
  private testFlightStageService: TestFlightStageService;
  @Inject()
  private earlyWarningNotifyStrategyService: EarlyWarningNotifyStrategyService;
  @Inject()
  private stageServiceFactory: StageServiceFactory;
  @Inject()
  private versionUtil: VersionUtilService;
  @Inject()
  private releasePlatformUtils: ReleasePlatformUtilService;
  @Inject()
  private messageDao: ReleasePlatformMessageService;
  @Inject()
  private autoDriveService: VersionAutoDriveService;
  @Inject()
  private componentModelService: ComponentModelService;
  @Inject()
  private codeFrozenService: VersionCodeFrozenService;
  @Inject()
  private meego: MeegoService;
  @Inject()
  private qaConfigDao: QATestConfigDao;
  @Inject()
  private qaTestService: QaTestService;
  @Inject()
  private customBuildService: CustomBuildService;
  @Inject()
  private businessConfigService: BusinessConfigService;
  @Inject()
  private releasePlatformBotReactCard: ReleasePlatformBotReactCardService;
  @Inject()
  private configService: AirplaneConfigService;
  @Inject()
  private releasePackageSubmitInfoDao: ReleasePackageSubmitInfoDao;
  @Inject()
  private releasePackageSubmitService: ReleasePackageSubmitInfoService;
  @Inject()
  private submitHandleCardService: versionStageRejectHandleCardService;
  @Inject()
  private reviewReportService: VersionReportService;
  @Inject()
  private forceTurnVersionStageInfoDao: ForceTurnVersionStageInfoDao;
  async findProgressVersionAndCheckStatus(refresh = true) {
    const testGroupId = await this.lark.chatId2OpenChatId('7403276103596720131');
    const onProgressVersionInfos = await this.versionProcessDao.findOnProgressVersions();
    if (!onProgressVersionInfos) {
      this.logger.info(`[Version Release] [checkVersionStatus] no on progress version`);
      return;
    }
    const canPush = await this.versionUtil.isLarkCardCanPush();
    if (!canPush) {
      return;
    }
    // 需要用到多个版本状态的自驱，暂时放在这里
    // await this.checkLvOverseasBranch(onProgressVersionInfos);

    const results = [];
    for (const info of onProgressVersionInfos) {
      const result = await this.checkVersionProcess(info, refresh);
      results.push(result);
    }
    await this.checkIntegrationPackageResult();
    return results;
  }

  async checkPackageRelease() {
    const onProgressVersionInfos = await this.versionProcessDao.findOnProgressVersions();
    if (!onProgressVersionInfos) {
      this.logger.info(`[Version Release] [checkSmallFlowPackageRelease] no on progress version`);
      return;
    }
    const pushPromise = onProgressVersionInfos.map(async info => await this.checkPackageRleaseTime(info));
    await Promise.all(pushPromise);
  }

  async checkPackageRleaseTime(versionInfo: VersionProcessInfo) {
    await this.autoDriveService.checkPauseReleaseAction(versionInfo);
  }

  private isParticularVersion(versionInfo: VersionProcessInfo) {
    const beforeTTPVersion = ['14.3.0', '14.2.0'];
    if (versionInfo.app_id === AppSettingId.LV_IOS && versionInfo.version === '15.7.0') {
      return true;
    }
    if (versionInfo.app_id === AppSettingId.CC_IOS && versionInfo.version === '13.7.0') {
      return true;
    }
    if (
      (versionInfo.app_id === AppSettingId.CC_IOS || versionInfo.app_id === AppSettingId.CC_ANDROID) &&
      beforeTTPVersion.includes(versionInfo.version)
    ) {
      return true;
    }
    return false;
  }

  async checkVersionProcess(versionInfo: VersionProcessInfo, refresh = true) {
    this.logger.info(
      `[Version Release] [checkVersionProcess] appId: ${versionInfo.app_id}, version: ${versionInfo.version} start`,
    );
    const appInfo = await this.businessConfigService.appID2AppInfo(versionInfo.app_id);
    try {
      if (refresh && appInfo && !isPC(versionInfo) && !this.isParticularVersion(versionInfo)) {
        const newestInfo = await this.versionProcessService.updateVersionInfo(appInfo, versionInfo.version);
        if (newestInfo) {
          await this.checkVersionStage(newestInfo, refresh);
          await this.reviewReportService.genVersionReviewReport(newestInfo);
        }
      } else {
        await this.checkVersionStage(versionInfo, refresh);
        await this.reviewReportService.genVersionReviewReport(versionInfo);
      }
    } catch (e) {
      const card = this.versionReleaseCard.buildCheckListExceptionCard(versionInfo, e as Error);
      // await this.lark.sendCardMessage(UserIdType.chatId, 'oc_568745868df8983bf70f7f453a8e4924', card);
    }
  }

  async attemptCompleteParentStage(versionInfo: VersionProcessInfo, subStage: VersionStageInfo) {
    if (!subStage.parent_stage_name) {
      return;
    }
    const parentStage = getStageInfo(versionInfo.version_stages, subStage.parent_stage_name);
    if (!parentStage) {
      return;
    }
    const notCompleteSubStage = parentStage.sub_stages.find(it => it.status !== VersionStageStatus.Complete);
    if (notCompleteSubStage) {
      return;
    }
    const checklist = await this.checklistService.getVersionStageCheckList(
      { app_id: versionInfo.app_id, version: versionInfo.version, stage: parentStage.stage_name },
      false,
    );
    const canComplete = await this.stageServiceFactory
      .getServiceForStageName(parentStage.stage_name, parentStage.parent_stage_name)
      ?.canComplete(versionInfo, parentStage, checklist);
    if (canComplete) {
      competeStage(parentStage);
    }
    await this.attemptCompleteParentStage(versionInfo, parentStage);
  }

  async checkVersionStage(versionInfo: VersionProcessInfo, refreshChecklist: boolean) {
    // 流转逻辑
    // 找到当前正在进行的所有阶段，进行canComplete检查（默认：是否到达预定的持续时间&checklist是否完成。其他为stageService定制逻辑）
    const onProgressStages = getOnProgressStages(versionInfo.version_stages);
    // 如果canComplete为true，直接完成对应阶段
    for (const onProgressStage of onProgressStages) {
      let checklist: VersionStageCheckList | undefined;
      try {
        checklist = await this.checklistService.getVersionStageCheckList(
          { app_id: versionInfo.app_id, version: versionInfo.version, stage: onProgressStage.stage_name },
          refreshChecklist,
        );
      } catch (e) {
        const card = this.versionReleaseCard.buildCheckListExceptionCard(versionInfo, e as Error, onProgressStage);
        await this.lark.sendCardMessage(UserIdType.chatId, 'oc_568745868df8983bf70f7f453a8e4924', card);
      }
      const canComplete = await this.stageServiceFactory
        .getServiceForStageName(onProgressStage.stage_name, onProgressStage.parent_stage_name)
        ?.canComplete(versionInfo, onProgressStage, checklist);
      if (
        canComplete &&
        onProgressStage.sub_stages.filter(it => it.status !== VersionStageStatus.Complete).length === 0
      ) {
        completeVersionStage(versionInfo, onProgressStage);
        await this.attemptCompleteParentStage(versionInfo, onProgressStage);
      }
    }
    // 找到当前第一个未启动的阶段，如果开始时间大于当前时间，则不处理
    const currentTime = Date.now() / 1000;
    const nextNotStartStage = getNextNotStartStage(versionInfo.version_stages);
    const stillOnProgressStages = onProgressStages.filter(it => it.status !== VersionStageStatus.Complete);
    if (!nextNotStartStage && !stillOnProgressStages.length) {
      versionInfo.status = VersionProcessStatus.End;
    }
    if (nextNotStartStage && currentTime > nextNotStartStage?.start_time) {
      // 检查未启动阶段所依赖的阶段是否都是已完成状态，如果都已完成，则开始此阶段
      // 如果未启动阶段的开始时间=0，则只检查依赖阶段是否已完成
      const onProgressNeededStages = nextNotStartStage?.version_phase?.need_approve_stages?.filter(it => {
        const stageInfo = getStageInfo(versionInfo.version_stages, it);
        return stageInfo?.status === VersionStageStatus.OnProgress;
      });
      if (!onProgressNeededStages?.length && !nextNotStartStage.version_phase?.mannully_start) {
        startVersionStage(versionInfo, nextNotStartStage);
        try {
          await this.checklistService.getVersionStageCheckList(
            { app_id: versionInfo.app_id, version: versionInfo.version, stage: nextNotStartStage.stage_name },
            true,
          );
        } catch (e) {}
        await this.stageServiceFactory
          .getServiceForStageName(nextNotStartStage.stage_name, nextNotStartStage.parent_stage_name)
          ?.startStage(versionInfo);
      }
    }

    // is_delay只对开始时间大于0的阶段进行标记
    versionInfo.version_stages.forEach(it => markStageDelayIfNeeded(versionInfo, it));
    await this.versionProcessDao.updateByCriteria(
      { app_id: versionInfo.app_id, version: versionInfo.version },
      versionInfo,
    );
  }

  async turnVersiontoStage(appId: number, version: string, stageName: string) {
    const versionInfo: VersionProcessInfo | null | undefined = await this.versionProcessDao.findOneByCriteria({
      app_id: appId,
      version,
    } as VersionProcessInfo);
    if (!versionInfo) {
      return;
    }
    const stageInfo = getStageInfo(versionInfo.version_stages, stageName);
    if (!stageInfo) {
      return;
    }
    for (const stage of versionInfo.version_stages) {
      if (stage.stage_name !== stageInfo.stage_name && !stage.sub_stages.find(it => it.stage_name === stageName)) {
        competeStage(stage);
      }
      startVersionStage(versionInfo, stageInfo);
      await this.checklistService.getVersionStageCheckList(
        { app_id: versionInfo.app_id, version: versionInfo.version, stage: stageInfo.stage_name },
        true,
      );
    }
    await this.versionProcessDao.updateByCriteria(
      { app_id: versionInfo.app_id, version: versionInfo.version },
      versionInfo,
    );
  }

  async completeStage(appId: number, version: string, stageName: string) {
    const versionInfo = await this.versionProcessDao.findOneByCriteria({
      app_id: appId,
      version,
    } as VersionProcessInfo);
    if (!versionInfo) {
      return undefined;
    }
    const stageInfo = getStageInfo(versionInfo.version_stages, stageName);
    if (!stageInfo) {
      return undefined;
    }
    const notCompleteSubStage = stageInfo.sub_stages.find(it => it.status !== VersionStageStatus.Complete);
    if (!notCompleteSubStage) {
      competeStage(stageInfo);
    }
    this.logger.info(
      `[Version Release] [completeStage] appId: ${appId}, version: ${version}, stage: ${stageName} complete`,
    );
    await this.checkVersionStage(versionInfo, false);
    return versionInfo;
  }

  async turnVersionStageToNext(appId: number, version: string) {
    const versionInfo = await this.versionProcessDao.findOneByCriteria({
      app_id: appId,
      version,
    } as VersionProcessInfo);
    if (!versionInfo) {
      return null;
    }
    const currentTime = Temporal.Now.zonedDateTimeISO(commonUtils.defaultTimeZone).epochSeconds;
    const onProgressStage = versionInfo.version_stages.find(it => it.status === VersionStageStatus.OnProgress);
    const nextStage = getNextVersionStage(versionInfo, onProgressStage);
    // const nextStageIndex = versionInfo.version_stages.findIndex(it => it.status === VersionStageStatus.NotStart);
    if (!onProgressStage) {
      if (nextStage) {
        if (nextStage.status === VersionStageStatus.NotStart) {
          startVersionStage(versionInfo, nextStage);
          nextStage.real_start_time = currentTime;
          if (nextStage.sub_stages.length > 0) {
            startVersionStage(versionInfo, nextStage.sub_stages[0]);
          }
        }
      } else {
        versionInfo.status = VersionProcessStatus.End;
      }
    } else {
      if (onProgressStage.sub_stages.length > 0) {
        const onProgressSubStage = onProgressStage.sub_stages.find(it => it.status === VersionStageStatus.OnProgress);
        const nextSubStage = getNextVersionStage(versionInfo, onProgressSubStage);
        onProgressSubStage ? (onProgressSubStage.status = VersionStageStatus.Complete) : {};
        nextSubStage
          ? startVersionStage(versionInfo, nextSubStage)
          : (onProgressStage.status = VersionStageStatus.Complete);
      } else {
        onProgressStage.status = VersionStageStatus.Complete;
      }
      if (onProgressStage.status === VersionStageStatus.Complete) {
        if (nextStage) {
          if (nextStage.status === VersionStageStatus.NotStart) {
            startVersionStage(versionInfo, nextStage);
            nextStage.real_start_time = currentTime;
            if (nextStage.sub_stages.length > 0) {
              startVersionStage(versionInfo, nextStage.sub_stages[0]);
            }
          }
        } else {
          versionInfo.status = VersionProcessStatus.End;
        }
      }
    }
    const stageService = this.stageServiceFactory.getServiceForStageName(nextStage?.stage_name ?? '');
    await stageService?.checkStageStatus(versionInfo);
    await this.versionProcessDao.updateByCriteria(
      { app_id: versionInfo.app_id, version: versionInfo.version },
      versionInfo,
    );
    return versionInfo;
  }

  async createVersionRejectGroup(data: CardCallback) {
    const { appId, version } = data.action.value;
    const versionProcessInfo = await this.versionProcessService.getCurrentVersionProcessInfo(Number(appId), version);
    if (!versionProcessInfo) {
      this.logger.info(`[Version Release] [createVersionRejectGroup] no version process info`);
      return;
    }
    if (versionProcessInfo.version_reject_group_chat_id) {
      return this.versionReleaseCard.buildEnterRejectGroupCard(
        versionProcessInfo.version_reject_group_chat_id,
        Number(appId),
      );
    }
    const userSet = new Set<string>();
    // test
    userSet.add('<EMAIL>');
    userSet.add('<EMAIL>');
    // userSet.add('<EMAIL>');
    // userSet.add('<EMAIL>');
    // userSet.add('<EMAIL>');
    const allDevelopers = await this.lark.batchGetUserId(UserIdType.userId, {
      emails: [...userSet],
    });
    const createCharUsers = allDevelopers.map((value: UserData) => value.user_id).filter(value => value);
    const createGroupArgs = await this.buildCreateChatArgs(
      `${AppId2Name[versionProcessInfo.app_id]}-${versionProcessInfo.version}拒审处理专项群`,
      sample(allDevelopers)!.user_id,
      createCharUsers,
    );
    const createGroupResult = await this.lark.createLarkGroup(
      {
        user_id_type: UserIdType.userId,
        set_bot_manager: false,
      },
      createGroupArgs,
    );
    if (!createGroupResult) {
      this.logger.info(`[Version Release] [createVersionRejectGroup] create group failed`);
      return;
    }
    versionProcessInfo.version_reject_group_chat_id = createGroupResult.chat_id;
    await this.versionProcessService.updateVersionProcessInfo(versionProcessInfo);
    this.logger.info(`[Version Release] [createVersionRejectGroup] create group success`);
    return this.versionReleaseCard.buildEnterRejectGroupCard(createGroupResult.chat_id, Number(appId));
  }

  private async buildCreateChatArgs(title: string, ownerId: string, userIds: string[]): Promise<CreateChatArgs> {
    return {
      name: title,
      description: title,
      owner_id: ownerId,
      user_id_list: userIds.filter(value => value),
      bot_id_list: ['cli_9c8628b7b1f1d102'],
    };
  }

  async versionEndNotification(app_id: number, version: string, docUrl: string) {
    const chatId = 'oc_5c6576269441f3797806a7a0e6f333c4';
    const versionInfo = await this.versionProcessService.getCurrentVersionProcessInfo(app_id, version);
    if (!versionInfo) {
      this.logger.error(`[SmallFlowStageService] appStoreRejectNotification 没有找到版本信息`);
      return;
    }
    const title = `[${AppId2Name[versionInfo.app_id]}-${versionInfo.version}] 版本总结报告`;
    const description = `**${AppId2Name[versionInfo.app_id]}-${versionInfo.version}已发布完成，请填写版本总结报告**`;
    const card = this.versionReleaseCard.buildDescriptionCardWithJumpUrlButton(
      title,
      CardTemplate.blue,
      description,
      '版本总结报告',
      docUrl,
      CardButtonType.primary,
    );
    await this.lark.sendCardMessage(UserIdType.chatId, chatId, card);
  }

  // stage 参数为两个组合'stage1,stage2'，代表当前阶段和被影响的灰度阶段
  async grayPackageDelayEvaluate(data: CardCallback): Promise<Card | CallBackToast | undefined> {
    const { appId, version, stage, isApproved, userIds } = data.action.value;
    const userIdList = userIds.split(',');
    const stages = stage.split(',');
    if (stages.length < 2) {
      return {
        toast: {
          type: 'error',
          content: '未知错误，请联系管理员',
        },
      } as CallBackToast;
    }
    if (userIdList.includes(data.user_id)) {
      if (isApproved === 'start') {
        this.startPackageDelayEvaluate(Number(appId), version, stages[0], stages[1]).then(res => {
          this.updateGrayDelayNotiCard(Number(appId), version, stages[0], stages[1], isApproved);
        });
      } else if (isApproved === 'package') {
        if (stages[1].split('@')[1] === '0') {
          stages[1] = 'testflight_pre_check';
        }
        this.turnVersiontoStage(Number(appId), version, stages[1]).then(res => {
          this.updateGrayDelayNotiCard(Number(appId), version, stages[0], stages[1], isApproved);
        });
      } else if (isApproved === 'skip') {
        this.skipGrayStage(Number(appId), version, stages[0], stages[1]).then(res => {
          this.updateGrayDelayNotiCard(Number(appId), version, stages[0], stages[1], isApproved);
        });
      }
      this.lark.batchGetUserInfo([data.user_id], UserIdType.userId).then(res => {
        if (res && res[0]) {
          this.grayPackageDelayEvaluateImpl(
            Number(appId),
            version,
            stages[0],
            stages[1],
            res[0],
            isApproved === 'true',
          );
        }
      });
      return undefined;
    } else {
      return {
        toast: {
          type: 'error',
          content: '无评估权限',
        },
      } as CallBackToast;
    }
  }

  async startPackageDelayEvaluate(appId: number, version: string, currentStage: string, grayStage: string) {
    const versionInfo = await this.versionProcessDao.findOneByCriteria({ app_id: appId, version });
    if (!versionInfo) {
      return;
    }
    const currentStageInfo = getStageInfo(versionInfo.version_stages, currentStage);
    if (!currentStageInfo) {
      return;
    }
    const checklist = await this.checklistService.getVersionStageCheckList({
      app_id: appId,
      version,
      stage: currentStage,
    });
    if (!checklist) {
      return;
    }
    const nearestGraySubStage = getStageInfo(versionInfo.version_stages, grayStage);
    if (!nearestGraySubStage) {
      return;
    }
    const grayData = nearestGraySubStage.extra_data as SubTestFlightExtraData;
    if (!grayData) {
      return;
    }
    const blockCheckItem = checklist?.check_items.filter(it => it.status !== CheckItemStatus.Exempt);
    if (!blockCheckItem) {
      return;
    }
    const approverSet = new Set<User>();
    for (const item of blockCheckItem) {
      if (item.item_type === ItemType.FunctionalBug) {
        if (this.getOperator(item).email !== undefined) {
          approverSet.add(this.getOperator(item));
        }
      } else if (item.item_type === ItemType.BugResolveRatio) {
        const blockBusinessItem = this.releasePlatformUtils.getBlockBusinessItem(item);
        for (const businessItem of blockBusinessItem) {
          if (businessItem.owner !== undefined) {
            approverSet.add(businessItem.owner);
          }
        }
      } else if (item.owner && item.owner) {
        approverSet.add(item.owner);
      }
    }
    const clientQAUserInfo = await this.lark.getUserInfoByEmail(
      this.releasePlatformUtils.getClientQA(versionInfo.app_id),
    );
    if (clientQAUserInfo) {
      approverSet.add(clientQAUserInfo);
    }
    const qaBmInfo = await this.lark.getUserInfoByEmail(versionInfo.bmInfo[BmType.qa].email);
    if (qaBmInfo) {
      approverSet.add(qaBmInfo);
    }
    const tfExtraData = nearestGraySubStage.extra_data as SubTestFlightExtraData;
    tfExtraData.packageDelayNeedApprovers = Array.from(approverSet);
    tfExtraData.packageDelayActualApprovers = [];
    tfExtraData.packageDelayRejectApprover = undefined;
    nearestGraySubStage.is_skip = false;
    nearestGraySubStage.extra_data = tfExtraData;
    const card = this.versionReleaseCard.buildGrayDelayEvaluateCard(
      versionInfo,
      currentStageInfo,
      blockCheckItem ?? [],
      nearestGraySubStage,
    );
    tfExtraData.packageDelayMessageId = await this.messageDao.sendVersionMessage(
      ReleasePlatformMessageGroupType.BmReleaseGroup,
      card,
      versionInfo,
      Array.from(approverSet),
    );
    return versionInfo;
  }

  getOperator(checkItem: VersionStageCheckItem): User {
    const itemInfo = checkItem.item_info as FunctionalBugItemInfo;
    return itemInfo.operator;
  }

  async grayPackageDelayEvaluateImpl(
    appId: number,
    version: string,
    currentStage: string,
    grayStage: string,
    user: User,
    approve = true,
  ) {
    const versionInfo = await this.versionProcessDao.findOneByCriteria({ app_id: appId, version });
    if (!versionInfo) {
      return;
    }
    const currentStageInfo = getStageInfo(versionInfo.version_stages, currentStage);
    if (!currentStageInfo) {
      return;
    }
    const checklist = await this.checklistService.getVersionStageCheckList({
      app_id: appId,
      version,
      stage: currentStage,
    });
    if (!checklist) {
      return;
    }
    const stageInfo = getStageInfo(versionInfo.version_stages, grayStage);
    if (!stageInfo) {
      return;
    }
    const grayData = stageInfo.extra_data as SubTestFlightExtraData;
    if (!grayData) {
      return;
    }
    const needApproverEmails: string[] | undefined = grayData.packageDelayNeedApprovers
      ?.map(it => it.email)
      .filter((it): it is string => it !== undefined);
    if (!needApproverEmails) {
      return;
    }
    if (!needApproverEmails.includes(user.email ?? 'null')) {
      return;
    }
    if (approve) {
      const approversSet = new Set(grayData.packageDelayActualApprovers ?? []);
      approversSet.add(user);
      grayData.packageDelayActualApprovers = Array.from(approversSet);
      if (grayData.packageDelayActualApprovers.length === (grayData.packageDelayNeedApprovers?.length ?? 999)) {
        grayData.packageTime = grayData.packageTime + 7200;
        stageInfo.is_delay = true;
      }
    } else {
      stageInfo.is_skip = true;
      grayData.packageDelayRejectApprover = user;
    }
    const card = this.versionReleaseCard.buildGrayDelayEvaluateCard(
      versionInfo,
      currentStageInfo,
      checklist.check_items.filter(it => it.status === CheckItemStatus.Blocked),
      stageInfo,
    );
    if (grayData.packageDelayMessageId !== undefined) {
      await this.lark.updateCard(JSON.stringify(card), grayData.packageDelayMessageId);
    }
    await this.versionProcessDao.updateByCriteria({ version, app_id: appId }, versionInfo, false);
  }

  async updateGrayDelayNotiCard(
    appId: number,
    version: string,
    currentStage: string,
    grayStage: string,
    action: string,
  ) {
    const versionInfo = await this.versionProcessDao.findOneByCriteria({ app_id: appId, version });
    if (!versionInfo) {
      return;
    }
    const currentStageInfo = getStageInfo(versionInfo.version_stages, currentStage);
    if (!currentStageInfo) {
      return;
    }
    const nearestGraySubStage = getStageInfo(versionInfo.version_stages, grayStage);
    if (!nearestGraySubStage) {
      return;
    }
    const extraData = nearestGraySubStage.extra_data as SubTestFlightExtraData;
    if (!extraData) {
      return;
    }
    const rdBmInfo = await this.lark.getUserInfoByEmail(versionInfo.bmInfo[BmType.rd].email);
    const qaBmInfo = await this.lark.getUserInfoByEmail(versionInfo.bmInfo[BmType.qa].email);
    const managerInfo = await this.lark.getUserInfoByEmail('<EMAIL>');
    const newNotiCard = this.versionReleaseCard.buildGrayDelayNotiCard(
      versionInfo,
      currentStageInfo,
      [],
      nearestGraySubStage,
      [rdBmInfo?.user_id ?? '', qaBmInfo?.user_id ?? '', managerInfo?.user_id ?? ''],
      action,
    );
    if (extraData.confirmMessageId) {
      await this.lark.updateCard(JSON.stringify(newNotiCard), extraData.confirmMessageId);
    }
  }

  async skipGrayStage(appId: number, version: string, currentStage: string, grayStage: string) {
    const versionInfo = await this.versionProcessDao.findOneByCriteria({ app_id: appId, version });
    if (!versionInfo) {
      return;
    }
    const currentStageInfo = getStageInfo(versionInfo.version_stages, currentStage);
    if (!currentStageInfo) {
      return;
    }
    const nearestGraySubStage = getStageInfo(versionInfo.version_stages, grayStage);
    if (!nearestGraySubStage) {
      return;
    }
    nearestGraySubStage.is_skip = true;
    await this.versionProcessDao.updateByCriteria({ app_id: appId, version }, versionInfo);
  }

  async checkIntegrationPackageResult() {
    const onProgressVersionInfos = await this.versionProcessDao.findOnProgressVersions();
    if (!onProgressVersionInfos) {
      this.logger.info(`[Version Release] [checkSmallFlowPackageRelease] no on progress version`);
      return;
    }
    let versionInfo;
    for (const v of onProgressVersionInfos) {
      this.logger.info(`[Version Release] [checkIntegrationPackageResult] version:${v.version}, appId:${v.app_id}`);
      const currentStageInfo = getStageInfo(v.version_stages, 'sub_integration_test');
      if (!currentStageInfo) {
        continue;
      }
      versionInfo = v;
      this.logger.info(
        `[Version Release] [checkIntegrationPackageResult] stage name:${currentStageInfo.stage_name}, status:${currentStageInfo.status}`,
      );
      const extraData = currentStageInfo.extra_data as SubIntegrationExtraData;
      if (!extraData) {
        continue;
      }
      this.logger.info(`[Version Release] [checkIntegrationPackageResult] extraData:${JSON.stringify(extraData)}`);
      if (extraData.build_record !== undefined && extraData.cc_build_record !== undefined) {
        continue;
      }
      const buildResult = await this.componentModelService.searchCustomBuildInfo(extraData.build_id);
      if (!buildResult || buildResult.buildResult !== CustomBuildResultType.success) {
        this.logger.error(`[Version Release] [checkIntegrationPackageResult] build result not success`);
        continue;
      }
      extraData.build_record = buildResult;
      const ccBuildResult = await this.componentModelService.searchCustomBuildInfo(extraData.cc_build_id);
      if (!ccBuildResult || ccBuildResult.buildResult !== CustomBuildResultType.success) {
        this.logger.error(`[Version Release] [checkIntegrationPackageResult] cc build result not success`);
        continue;
      }
      extraData.cc_build_record = ccBuildResult;
      await this.versionProcessDao.updateByCriteria({ app_id: v.app_id, version: v.version }, v);
    }
    if (versionInfo === undefined) {
      this.logger.info(`[Version Release] [checkIntegrationPackageResult] no on progress version`);
      return;
    }
  }

  async startQAUserStoryTest(data: CardCallback) {
    const { action } = data;
    const { value } = action;
    const versionInfo = await this.versionProcessDao.findOneByCriteria({
      version: value.version,
      app_id: Number(value.appId),
    });
    if (!versionInfo) {
      this.logger.error(`[Version Release] [startQATest] version info not found`);
      return;
    }
    // if (value.QATestStage === QATestStage.QA_TEST_STAGE_INTEGRATION) {
    // 大群发送通知
    // await this.checkAllIntegrationPackageResult();
    // // 给poc发送通知
    // if (versionInfo.app_id === 177501) {
    //   await this.sendPociOSIntegrationTestStage(value, versionInfo, pocList);
    // } else if (versionInfo.app_id === 177502) {
    //   await this.sendPocAdrIntegrationTestStage(value, versionInfo, pocList);
    // }
    // } else if (value.QATestStage === QATestStage.QA_TEST_STAGE_USER_STORY) {
    //   // 给poc发送通知
    //   if (versionInfo.app_id === 177501) {
    //     await this.sendPocIOSUserStoryTestStage(value, versionInfo, pocList);
    //   } else if (versionInfo.app_id === 177502) {
    //     await this.sendPocAdrUserStoryTestStage(value, versionInfo, pocList);
    //   }
    // }
    // 用户故事开始
    if (value.QATestStage === QATestStage.QA_TEST_STAGE_USER_STORY) {
      // // 给poc发送通知
      // if (Number(value.appId) === 177501 || Number(value.appId) === 300601) {
      //   await this.qaTestService.sendPocIOSUserStoryTestStage(value, versionInfo, pocList);
      // } else if (Number(value.appId) === 177502 || Number(value.appId) === 300602) {
      //   await this.qaTestService.sendPocAdrUserStoryTestStage(value, versionInfo, pocList);
      // }
      const efficiencyService = useInject(DevelopmentEfficiencyService);
      await efficiencyService.writeEfficiencyNodePartial(
        Number(value.appId),
        value.version,
        EfficiencyVersionStage.USER_STORY_START,
        dayjs().unix(),
      );
      // 自动打包
      if (value.appId === '177501') {
        await this.customBuildService.customBuild({
          lvBranch: `release/${value.version}`,
          repos: [],
          type: [CustomBuildType.USER_STORY],
          arch: PlatformType.iOS,
          isOversea: false,
          isUserStory: true,
          extra: {
            bm: versionInfo.bmInfo[BmType.qa].email,
          },
        } as CustomBuildParam);
        // 调试页TF包
        await this.customBuildService.customBuild({
          lvBranch: `release/${value.version}`,
          repos: [],
          type: [CustomBuildType.USER_STORY_TF],
          arch: PlatformType.iOS,
          isOversea: false,
          isUserStory: true,
          isTFInUserStory: true,
          extra: {
            bm: versionInfo.bmInfo[BmType.qa].email,
          },
        } as CustomBuildParam);
      } else if (value.appId === '177502') {
        const stageInfo = getStageInfo(versionInfo.version_stages, 'full_release_precheck');
        if (stageInfo) {
          startVersionStage(versionInfo, stageInfo);
          await this.versionProcessDao.updateByCriteria(
            { app_id: versionInfo.app_id, version: versionInfo.version },
            versionInfo,
            false,
          );
        }
        const result = await this.customBuildService.customBuild({
          lvBranch: `release/${value.version}`,
          repos: [],
          type: [CustomBuildType.USER_STORY],
          arch: PlatformType.Android,
          isOversea: false,
          isUserStory: true,
          extra: {
            bm: versionInfo.bmInfo[BmType.qa].email,
          },
        } as CustomBuildParam);
        if (result.code !== NetworkCode.Success) {
          this.logger.error(`[Version Release] [startQATest] custom build failed`);
          return {
            toast: {
              type: 'error',
              content: '发起打包失败',
            },
          } as CallBackToast;
        }
      } else if (value.appId === '300601') {
        await this.customBuildService.customBuild({
          lvBranch: `overseas/release/${value.version}`,
          repos: [],
          type: [CustomBuildType.USER_STORY],
          arch: PlatformType.iOS,
          isOversea: true,
          isUserStory: true,
          extra: {
            bm: versionInfo.bmInfo[BmType.qa].email,
          },
        });
        // 调试页TF包
        await this.customBuildService.customBuild({
          lvBranch: `overseas/release/${value.version}`,
          repos: [],
          type: [CustomBuildType.USER_STORY_TF],
          arch: PlatformType.iOS,
          isOversea: true,
          isUserStory: true,
          isTFInUserStory: true,
          extra: {
            bm: versionInfo.bmInfo[BmType.qa].email,
          },
        } as CustomBuildParam);
      } else if (value.appId === '300602') {
        const stageInfo = getStageInfo(versionInfo.version_stages, 'full_release_precheck');
        if (stageInfo) {
          startVersionStage(versionInfo, stageInfo);
          await this.versionProcessDao.updateByCriteria(
            { app_id: versionInfo.app_id, version: versionInfo.version },
            versionInfo,
            false,
          );
        }
        const result = await this.customBuildService.customBuild({
          lvBranch: `overseas/release/${value.version}`,
          repos: [],
          type: [CustomBuildType.USER_STORY],
          arch: PlatformType.Android,
          isOversea: true,
          isUserStory: true,
          extra: {
            bm: versionInfo.bmInfo[BmType.qa].email,
          },
        });
        if (result.code !== NetworkCode.Success) {
          this.logger.error(`[Version Release] [startQATest] custom build failed`);
          return {
            toast: {
              type: 'error',
              content: '发起打包失败',
            },
          } as CallBackToast;
        }
      }
    }
    return {
      toast: {
        type: 'success',
        content: '发起打包成功',
      },
    } as CallBackToast;
  }

  async fullReleaseCheckApprove(data: CardCallback) {
    const { appId, version, userIds, isApproved, id } = data.action.value;
    const userIdList = userIds.split(',');
    if (userIdList.includes(data.user_id)) {
      this.fullReleaseCheckApproveImpl(Number(appId), version, isApproved === 'true', id === '1').then(res => {
        this.logger.info(`[Version Release] [fullReleaseCheckApprove] ${res}`);
      });
    } else {
      return {
        toast: {
          type: 'error',
          content: '无评估权限',
        },
      } as CallBackToast;
    }
  }

  async fullReleaseCheckApproveImpl(appId: number, version: string, approve: boolean, smallFlowPass: boolean) {
    const versionInfo = await this.versionProcessDao.findOneByCriteria({
      version,
      app_id: appId,
    });
    if (!versionInfo) {
      return;
    }
    const fullReleaseStage = versionInfo.version_stages.find(it => it.stage_name === 'submit');
    if (!fullReleaseStage) {
      return false;
    }
    const extraData = fullReleaseStage.extra_data as AppleFullReleaseExtraData;
    if (!extraData) {
      return false;
    }
    const smallFlowStage = versionInfo.version_stages.find(it => it.stage_name === 'smallFlow');
    if (!smallFlowStage) {
      return false;
    }
    if (!approve) {
      if (extraData.approveCheckMessageId) {
        const newCard = this.versionReleaseCard.buildFullReleasePackageConfirmCard(
          smallFlowPass,
          versionInfo,
          [],
          approve,
        );
        await this.lark.updateCard(JSON.stringify(newCard), extraData.approveCheckMessageId);
      }
      competeStage(smallFlowStage);
      startVersionStage(versionInfo, fullReleaseStage.sub_stages[0]);
      await this.versionProcessDao.updateVersionProcessInfo(versionInfo);
      return true;
    }
    const fullReleaseCheckStage = fullReleaseStage?.sub_stages.find(it => it.stage_name === 'full_release_all_check');
    if (
      !fullReleaseCheckStage ||
      (fullReleaseCheckStage.status !== VersionStageStatus.OnProgress &&
        fullReleaseCheckStage.status !== VersionStageStatus.NotStart)
    ) {
      return false;
    }
    for (const mainStage of versionInfo.version_stages) {
      if (mainStage.stage_name !== 'submit') {
        competeStage(mainStage);
      } else {
        if (mainStage.status !== VersionStageStatus.Complete) {
          startVersionStage(versionInfo, mainStage);
          turnStageToSpecificSubStage(fullReleaseStage, fullReleaseCheckStage.stage_name);
        }
        break;
      }
    }

    const checklist = await this.checklistService.getVersionStageCheckList({
      app_id: appId,
      version,
      stage: fullReleaseCheckStage.stage_name,
    });
    if (!checklist) {
      return false;
    }
    if (smallFlowPass) {
      checklist.check_items.forEach(it => {
        if (it.item_type === ItemType.Slardar) {
          const sii = it.item_info as StabilityMetricItemInfo;
          it.status = CheckItemStatus.Exempt;
          if (sii) {
            sii.status = StabilityMetricsItemStatus.Pass;
          }
          return;
        }
        if (it.check_item_id.includes('feedback_check')) {
          const mii = it.item_info as ManuallyCheckItemInfo;
          it.status = CheckItemStatus.Exempt;
          if (mii) {
            mii.status = ManuallyCheckItemStatus.Pass;
          }
          return;
        }
      });
    }
    const smallFlowExtraData = smallFlowStage?.extra_data as SmallFlowExtraData;
    if (smallFlowExtraData) {
      extraData.checkStatus = smallFlowExtraData.checkStatus;
      extraData.releaseInfo = smallFlowExtraData.releaseInfo;
    }
    await this.versionProcessDao.updateVersionProcessInfo(versionInfo);
    await this.checklistService.updateVersionStageCheckList(
      version,
      fullReleaseCheckStage.stage_name,
      appId,
      checklist,
    );
    if (extraData.approveCheckMessageId) {
      const newCard = this.versionReleaseCard.buildFullReleasePackageConfirmCard(
        smallFlowPass,
        versionInfo,
        [],
        approve,
      );
      await this.lark.updateCard(JSON.stringify(newCard), extraData.approveCheckMessageId);
    }
    return true;
  }

  async checkLvOverseasBranch(versionInfos: VersionProcessInfo[]) {
    const iOSVersionInfos = versionInfos.filter(value => value.app_id === 177501);
    const adrVersionInfos = versionInfos.filter(value => value.app_id === 177502);
    for (const iOSVersionInfo of iOSVersionInfos) {
      const userStoryStage = iOSVersionInfo.version_stages.find(it => it.stage_name === 'user_story');
      if (userStoryStage?.status !== VersionStageStatus.NotStart) {
        this.logger.info(
          `[Version Release] [checkLvOverseasBranch] iOS user story stage start, try checkout overseas, version: ${iOSVersionInfo.version}`,
        );
        const adrVersionInfo = adrVersionInfos.find(it => it.version === iOSVersionInfo.version);
        if (iOSVersionInfo.overseas_branch_checkouted && adrVersionInfo?.overseas_branch_checkouted) {
          this.logger.info(
            `[Version Release] [checkLvOverseasBranch] iOS user story stage start, already checkouted, version: ${iOSVersionInfo.version}`,
          );
          continue;
        }
        const adrUserStoryStage = adrVersionInfo?.version_stages.find(it => it.stage_name === 'full_release_precheck');
        if (adrVersionInfo && adrUserStoryStage?.status !== VersionStageStatus.NotStart) {
          const needCheckout = await this.releasePlatformUtils.needCheckoutCCReleaseBranch(iOSVersionInfo);
          if (!needCheckout) {
            this.logger.info(
              `[Version Release] [checkLvOverseasBranch] iOS and Android story stage start, do not need checkout overseas, version: ${iOSVersionInfo.version}`,
            );
            iOSVersionInfo.overseas_branch_checkouted = true;
            adrVersionInfo.overseas_branch_checkouted = true;
            continue;
          }
          this.logger.info(
            `[Version Release] [checkLvOverseasBranch] iOS and Android story stage start, start checkout overseas, version: ${iOSVersionInfo.version}`,
          );
          const res = await this.releasePlatformUtils.checkoutCCReleaseBranch(iOSVersionInfo);
          if (res.code === NetworkCode.Success) {
            iOSVersionInfo.overseas_branch_checkouted = true;
            adrVersionInfo.overseas_branch_checkouted = true;
            this.logger.info(
              `[Version Release] [checkLvOverseasBranch] iOS and Android story stage start, checkout overseas success, version: ${iOSVersionInfo.version}`,
            );
          } else {
            this.logger.info(
              `[Version Release] [checkLvOverseasBranch] iOS and Android story stage start, checkout overseas failed, version: ${iOSVersionInfo.version}`,
            );
          }
        } else {
          this.logger.info(
            `[Version Release] [checkLvOverseasBranch] Android user stroy not start, do not checkout overseas, version: ${iOSVersionInfo.version}`,
          );
        }
      }
    }
  }

  async pcBuildPackage(appId: number, version: string, stageName: string) {
    //     curl --location 'https://robot.cn.goofy.app/buildMacStore' \
    // --header 'Content-Type: application/json' \
    // --data '{
    //     "timestamp": 1730376706000
    //   }'
    const url = 'https://robot.cn.goofy.app/buildMacStore';
    const versionInfo = await this.versionProcessDao.findOneByCriteria({
      version,
      app_id: appId,
    });
    if (!versionInfo) {
      return {
        code: NetworkCode.Error,
        message: 'version not found',
      };
    }
    const stageInfo = getStageInfo(versionInfo.version_stages, stageName);
    if (!stageInfo) {
      return {
        code: NetworkCode.Error,
        message: 'stage not found',
      };
    }
    const timeStamp = stageInfo.start_time + 3600;
    if (timeStamp <= 3600) {
      return {
        code: NetworkCode.Error,
        message: 'start time is 0',
      };
    }
    return await axios.post(url, {
      timestamp: timeStamp,
    });
  }
  async triggerVersionStatusBoradCast() {
    const canPush = await this.versionUtil.isLarkCardCanPush();
    if (!canPush) {
      return;
    }
    const businessConfigs = await this.businessConfigService.getConfigList(); // PC端没有配置，886分组下无版本信息可同步
    for (const businessConfig of businessConfigs) {
      const chatInfo = (await this.configService.queryConfigItem(
        businessConfig.business_id.toString(),
        VersionConfigKeys.versionStatusBraodCastGroups,
      )) as ChatInfo[];
      if (!chatInfo?.length && process.env.NODE_ENV !== 'development') {
        continue;
      }
      const versionInfoGroups: { appInfo: BusinessAppInfo; versionInfos: VersionProcessInfo[] }[] = [];
      for (const businessAppInfo of businessConfig.app_list) {
        const versionInfos = await this.versionProcessDao.findOnProgressVersions(businessAppInfo.app_id);
        if (!versionInfos) {
          continue;
        }
        const startedVersions = versionInfos.filter(
          it => it.version_stages[0] && it.version_stages[0].status !== VersionStageStatus.NotStart,
        );
        if (!startedVersions.length) {
          continue;
        }
        const sortedVersionInfos = sortVersions(startedVersions);
        const versionInfoGroup = { appInfo: businessAppInfo, versionInfos: sortedVersionInfos };
        versionInfoGroups.push(versionInfoGroup);
      }
      if (versionInfoGroups.length) {
        const card = this.releasePlatformBotReactCard.buildVersionStatusBroadCastCard(versionInfoGroups);
        if (process.env.NODE_ENV === 'development') {
          await this.messageDao.sendVersionMessage(
            ReleasePlatformMessageGroupType.VersionReleaseGroup,
            card,
            versionInfoGroups[0].versionInfos[0],
          );
        } else if (chatInfo?.length) {
          for (const chat of chatInfo) {
            if (chat.chat_id) {
              await this.lark.sendCardMessage(UserIdType.chatId, chat.chat_id, card);
            }
          }
        }
      }
    }
  }

  async appSubmitDidAppealing(appId: number, version: string) {
    const versionInfo = await this.versionProcessDao.findOneByCriteria({
      version,
      app_id: appId,
    });
    if (!versionInfo) {
      return;
    }
    const onProgressMainStages = versionInfo.version_stages.filter(it => it.status === VersionStageStatus.OnProgress);
    for (const onProgressMainStage of onProgressMainStages) {
      const extraInfo = onProgressMainStage.extra_data as AppleFullReleaseExtraData;
      if (extraInfo) {
        extraInfo.checkStatus = SubmitPackageCheckStatus.Appealing;
      }
    }
    await this.versionProcessDao.updateVersionProcessInfo(versionInfo);
  }

  async updateSubmitCheckStatus(submitId: string, newStatus: SubmitPackageCheckStatus) {
    await this.releasePackageSubmitService.updateCheckStatus(submitId, newStatus);
    const submitInfo = await this.releasePackageSubmitInfoDao.findOne(submitId);
    if (!submitInfo) {
      return;
    }
    const versionInfo = await this.versionProcessDao.getCurrentVersionProcessInfo(
      submitInfo.app_id,
      submitInfo.version,
    );
    if (!versionInfo) {
      return;
    }
    if (versionInfo) {
      // 触发一次自驱
      // this.versionProcessDao.updateByCriteria({ app_id, version }, versionInfo);
    }
  }

  async submitInfoRejected(appId: number, version: string) {
    const submitInfos = await this.releasePackageSubmitService.fetchByAppIdAndVersion(appId, version);
    const latestInfo = SortSubmitInfos(submitInfos)[0];
    if (!latestInfo) {
      return;
    }
    await this.updateSubmitCheckStatus(latestInfo.submit_id, SubmitPackageCheckStatus.Reject);
  }

  async silentReleaseNotify(data: CardCallback) {
    const { appId, version, userIds, product } = data.action.value;
    if (userIds !== data.user_id) {
      return {
        toast: {
          type: 'error',
          content: '无操作权限',
        },
      } as CallBackToast;
    }
    this.versionProcessDao
      .findOneByCriteria({
        app_id: Number(appId),
        version: version as string,
      })
      .then(async versionInfo => {
        if (!versionInfo) {
          return;
        }
        const submitInfos = await this.releasePackageSubmitService.fetchByAppIdAndVersion(Number(appId), version);
        const info = submitInfos.find(it => it.submit_id === product);
        if (!info) {
          return;
        }
        info.release_noti_silent = true;
        await this.releasePackageSubmitService.saveOrUpdateInfo(info);
        const newCard = await this.submitHandleCardService.releaseNotifyCard(info, versionInfo);
        await this.lark.updateCard(JSON.stringify(newCard), info.release_noti_message ?? '');
      });
  }

  async accessControlMeasure() {
    const appIds: number[] = [
      177501, 177502, 300601, 300602, 251501, 251502, 2020093988, 2020093924, 2020092383, 2020092892, 35928901,
      35928902, 244127338754, 225469550850,
    ];
    const versionAccessControlMeasureInfo: any[] = [];
    for (const appId of appIds) {
      const onProgressVersions = await this.versionProcessDao.findOnProgressVersions(appId);
      if (!onProgressVersions || onProgressVersions.length === 0) {
        return 'not record';
      }
      for (const versionInfo of onProgressVersions) {
        const versionStagesNum = versionInfo.version_stages.length;
        const accessControlNum = versionInfo.version_stages.reduce(
          (preNum: number, it: VersionStageInfo) =>
            preNum + (it.sub_stages && it.sub_stages.length !== 0 ? it.sub_stages.length : 1),
          0,
        );
        const passControlNum = versionInfo.version_stages.reduce((preNum: number, it: VersionStageInfo) => {
          if (it.sub_stages && it.sub_stages.length !== 0) {
            return it.sub_stages.reduce(
              (p: number, s: VersionStageInfo) => p + (s.status === VersionStageStatus.Complete ? 1 : 0),
              preNum,
            );
          } else {
            return preNum + (it.status === VersionStageStatus.Complete ? 1 : 0);
          }
        }, 0);
        const forceTurnRecords = await this.forceTurnVersionStageInfoDao.findRecordsByAppIdAndVersion(
          appId,
          versionInfo.version,
        );
        versionAccessControlMeasureInfo.push({
          versionStagesNum,
          accessControlNum,
          passControlNum,
          forceTurnNum: forceTurnRecords?.length,
          appId,
          version: versionInfo.version,
        });
      }
    }
    return versionAccessControlMeasureInfo;
  }

  async versionSubmitBlockUrgentComplete(data: CardCallback) {
    const userEmail = await this.lark.getUserEmail(data.open_id);
    // 非名单内成员无法确认催审已发出
    if (userEmail && !['<EMAIL>', '<EMAIL>'].includes(userEmail)) {
      return undefined;
    }
    const { appId, version } = data.action.value;
    const versionInfo = await this.versionProcessDao.findOneByCriteria({ app_id: Number(appId), version });
    const submitInfos = await this.releasePackageSubmitService.fetchByAppIdAndVersion(Number(appId), version);
    const checkingInfos = submitInfos.filter(it => it.check_status === SubmitPackageCheckStatus.Checking);
    const latestInfo = SortSubmitInfos(checkingInfos)[0];
    if (!versionInfo || !latestInfo) {
      return undefined;
    }
    const newCard = await this.submitHandleCardService.buildBlockUrgentCompleteCard(latestInfo, userEmail);
    await this.messageDao.sendVersionMessageToGroup(
      '催审完成通知',
      VersionTransactionType.SubmitBlockUrgent,
      MsgStrategy.Manual,
      newCard,
      versionInfo,
      '催审完成',
      ReleasePlatformMessageGroupType.BmReleaseGroup,
      [],
      ReleasePlatformMessageSource.InformationSync,
      '催审完成回调',
    );
    return newCard;
  }
}
