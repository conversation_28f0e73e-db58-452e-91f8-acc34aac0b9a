import {
  AppIdsGroup,
  ApprovalBusinessConfigs,
  ApprovalCodeConstant,
  ApprovalConfigDetail,
  ApprovalFixVersionConfig,
  ApprovalHistoryEvent,
  ApprovalHotfixConfig,
  ApprovalOrderStatus,
  ApprovalRequirementConfig,
  ApprovalType,
  CreateTicketApprovalReq,
  FixVersionApprovalReq,
  HotfixActionType,
  HotfixProblemType,
  hotfixRunningTimeText,
  HotfixTaskApprovalReq,
  hotfixTypeText,
  LarkApprovalCreateConfig,
  MeegoWorkFlowNodeStatus,
  PipelineStatus,
  RequirementApprovalReq,
  RequirementChangeActionType,
  RetouchApprovalBusinessConfigs,
} from '@shared/approval/ApprovalOrder';
import { Inject, Injectable } from '@gulux/gulux';
import { useInject } from '@edenx/runtime/bff';
import LarkService from '@pa/backend/dist/src/third/lark';
import { ApprovalForm } from '@pa/shared/dist/src/lark/approval';
import { LarkClient } from '@gulux/gulux/lark';
import { BytedLogger } from '@gulux/gulux/byted-logger';
import { ApprovalDBService } from '../approval/ApprovalDBService';
import dayjs from 'dayjs';
import { errorRsp } from '@pa/shared/dist/src/core';
import UserService from '@pa/backend/dist/src/service/user';
import versionUtils, { lv2ccVersion } from '../../utils/versionUtils';
import ApprovalService from '../approval/ApprovalService';
import { ApprovalInfoTableModel } from '../../model/approval/ApprovalInfoModel';
import { teaCollect, TeaEvent } from '../../tea';
import { fetchBmInfo } from '@api/releasePlatform';
import { BmType } from '@shared/bits/bmInfo';
import { batchGetUserInfo } from '@api/index';
import { compact, unionBy, uniq } from 'lodash';
import {
  convertToCustomFormat,
  extractApprovalBusinessEmails,
  formItem,
  inputItem,
  simpleRadioItem,
  textareaItem,
} from '../utils/ApprovalUtils';
import { UserData, UserIdType } from '@pa/shared/dist/src/lark/userInfo';
import { AppSettingId } from '@pa/shared/dist/src/appSettings/appSettings';
import VersionProcessInfoDao from '../dao/releasePlatform/VersionProcessInfoDao';
import { TccClients } from '@gulux/gulux/tcc';
import MeegoService, { faceuProjectKey } from '../third/meego';
import { WorkflowNode } from '@shared/meego/WorkflowResult';

// 审批流配置
export interface ApprovalProcessConfigItem {
  key: string;
  users: string[];
}

export interface ApprovalProcessConfig {
  items: { [key: string]: ApprovalProcessConfigItem[] };
}

@Injectable()
export default class ApprovalModelService {
  @Inject()
  private logger: BytedLogger;
  @Inject()
  private approvalDbService: ApprovalDBService;
  @Inject()
  private larkService: LarkService;
  @Inject()
  private larkClient: LarkClient;
  @Inject()
  private versionProcessInfoDao: VersionProcessInfoDao;
  @Inject()
  private tcc: TccClients;
  @Inject()
  private meego: MeegoService;

  async queryApprovalConfig(approvalCode: string) {
    return this.larkService.searchApproval(approvalCode);
  }

  async queryApprovalTemplates(appId: number): Promise<ApprovalConfigDetail[]> {
    // await this.approvalDbService.testCreateApprovalConfig();
    const configs = await this.approvalDbService.allApprovalConfigs(appId);

    const configDetails = await Promise.all(
      configs.map(async config => {
        const larkCongfig = await this.queryApprovalConfig(config.approvalCode);
        return {
          ...config,
          larkApprovalConfig: larkCongfig,
        };
      }),
    );
    return configDetails;
  }

  async createApprovalConfig(appId: number, config: LarkApprovalCreateConfig) {
    this.logger.info(
      `createApprovalConfig appId => ${appId}, config => ${config?.form?.form_content}, config?.i18n_resources => ${JSON.stringify(config?.i18n_resources)}`,
    );
    try {
      const i18n_resources = config?.i18n_resources?.map(it => ({
        locale: it?.locale,
        texts: it?.texts ?? [],
        is_default: true,
      }));
      const rsp = await this.larkService.createApprovalConfig({
        approval_name: '@i18n@approval_name',
        // approval_code: '97193B55-113D-4C06-AB33-0AFC93AEB375',
        // approval_code: '1A997D60-9E40-416F-A5CC-11865BFE815B',
        viewers: [
          {
            viewer_type: 'TENANT',
            viewer_user_id: '',
          },
        ],
        form: {
          form_content: config.form.form_content,
        },
        node_list: [...config.node_list],
        settings: {
          revert_interval: 0,
        },
        config: {
          can_update_viewer: false,
          can_update_form: true,
          can_update_process: true,
          can_update_revert: true,
          // help_url: 'https://xxx.xxx.xxx',
        },
        icon: 1,
        i18n_resources: [
          {
            locale: 'zh-CN',
            texts: [
              {
                key: '@i18n@approval_name',
                value: `${config?.approval_name}`,
              },
              ...(i18n_resources?.at(0)?.texts ?? []),
            ],
            is_default: true,
          },
        ],
      });
      const approvalCode = rsp?.data?.approval_code;
      const approvalId = rsp?.data?.approval_id;
      const approvalName = config?.approval_name ?? '默认创建审批流2';
      const approvalType = '自定义创建';
      const createUserEmail = '<EMAIL>';
      const approvalConfig = await this.approvalDbService.findApprovalConfig(appId, approvalCode);
      this.logger.info(`[createApprovalConfig] approvalConfig => ${JSON.stringify(approvalConfig)}`);
      try {
        if (approvalCode) {
          const subscribeRes = await this.larkService.subscribe(approvalCode);
          this.logger.info(`[createApprovalConfig] subscribe rsp => ${JSON.stringify(subscribeRes)}`);
        }
      } catch (e) {
        this.logger.error(`[createApprovalConfig] subscribe error => ${JSON.stringify(e)}`);
      }

      // this.approvalDbService.deleteApprovalConfigs(approvalCode);
      if (!approvalConfig && approvalCode && approvalId) {
        await this.approvalDbService.createApprovalConfig({
          approvalCode,
          approvalId,
          approvalName,
          approvalType,
          createUserEmail,
        });
      } else {
        await this.approvalDbService.updateApprovalConfig({
          ...approvalConfig,
          approvalId,
          approvalName,
        });
      }
      return rsp;
    } catch (e) {
      throw e;
    }
  }

  async getLeaderOpenId(createUserOpenId?: string) {
    if (createUserOpenId) {
      const userInfo = await this.larkService.getUserIdByOpenId(UserIdType.openId, createUserOpenId);
      return userInfo?.leader_user_id;
    }
  }

  // 自定义的审批流发起
  async createDefineApprovalOrder(appId: number, approvalCode: string, formInfoJson: string, formInstanceJson: string) {
    const email = await useInject(UserService).queryLoginEmail();
    const userInfo = await this.larkService.getUserIdByEmail(email);
    const approvalConfig = await this.larkService.searchApproval(approvalCode);
    if (approvalConfig) {
      // form取值逻辑 下面是代码覆盖率的审批流，需要把 bits 链接存储到数据库
      const form: ApprovalForm[] = JSON.parse(approvalConfig.form);
      const formInfo: Array<{
        id: string;
        type: string;
        value: any;
      }> = JSON.parse(formInfoJson);
      const bitsMrURLId = form.find(value => value.name === 'bits链接')?.id;
      const bitsMrURL = formInfo.find(it => it.id === bitsMrURLId)?.value;
      const unmetCriteriaReasonId = form.find(value => value.name === '豁免理由')?.id;
      const unmetCriteriaReason = formInfo.find(it => it.id === unmetCriteriaReasonId)?.value;
      const platformId = form.find(value => value.name === '系统平台')?.id;
      const platform = formInfo.find(it => it.id === platformId)?.value;
      this.logger.info(
        `[createDefineApprovalOrder] creat approval bitsMrURL=${bitsMrURL} unmetCriteriaReason=${unmetCriteriaReason} platform=${platform}`,
      );
      // 自定义用户userId
      const formInstanceInfo = JSON.parse(formInstanceJson);

      this.logger.info(`[createApprovalOrder]:approval_code=${approvalCode}`);
      const larkClient = useInject(LarkClient);
      this.logger.info(`[createApprovalOrder] creating approval from lark, approval_code=${approvalCode}`);
      try {
        const result = await larkClient.approval.instance.create({
          data: {
            approval_code: approvalCode,
            user_id: userInfo?.user_id,
            node_approver_user_id_list: formInstanceInfo?.node_approver_user_id_list,
            form: formInfoJson,
          },
        });
        this.logger.info(
          `[createApprovalOrder] creat approval from lark successfully, approval_code=${approvalCode}, result => ${JSON.stringify(result)}`,
        );
        if (result) {
          const approvalConfig2 = await this.approvalDbService.findApprovalConfig(appId, approvalCode);
          // 审批记录存储到数据库中
          await this.approvalDbService.create({
            bitsLink: {
              url: bitsMrURL,
              name: bitsMrURL,
            }, // 这里是覆盖率审批填写的平台，借用这个字段
            unmetCriteriaReason, // 这里是覆盖率审批填写的平台，借用这个字段
            unqualifiedProjects: platform, // 这里是覆盖率审批填写的平台，借用这个字段
            instanceCode: result.data?.instance_code,
            title: approvalConfig2?.approvalName,
            createUserEmail: email,
            appId,
            approvalCode,
            history: [
              {
                timeStamp: dayjs().unix(),
                event: ApprovalHistoryEvent.CREATE_APPROVAL, // 事件
                message: '创建审批流工单',
              },
            ],
          });
          this.logger.info(
            `[createApprovalOrder] creat approval instance record, approval_code=${approvalCode}, approvalConfig => ${JSON.stringify(approvalConfig2)}`,
          );
          // TODO '审批发起成功，请前往飞书跟进审批进度！'
        }
        return result;
      } catch (e) {
        if (e instanceof Error) {
          return errorRsp(e?.message);
        } else {
          return errorRsp('createDefineApprovalOrder error');
        }
      }
    }

    return;
  }

  async createApprovalHotfixOrder(req: HotfixTaskApprovalReq) {
    const approvalCode = 'E8467972-D80D-412F-B767-5C30C8BBD542';
    const userEmail = await useInject(UserService).queryLoginEmail();
    const approvalConfig = await this.larkService.searchApproval(approvalCode);
    const userInfo = await this.larkService.getUserIdByEmail(userEmail);
    if (!approvalConfig) {
      return;
    }
    // hotfix
    const appId = req?.appId ?? -1;
    const productName = versionUtils.appIdToAppInfo(appId.toString())?.productName;
    const versions = req?.versions;
    const isFE: boolean = (appId === AppSettingId.LV_IOS || appId === AppSettingId.CC_IOS) && (req?.isFE ?? false); // 默认非前端修复
    let isFullReleaseVersion = true; // 默认是全量版本，二级审批
    if ((appId === AppSettingId.LV_IOS || appId === AppSettingId.CC_IOS) && !(req?.isFullReleaseVersion ?? true)) {
      isFullReleaseVersion = false; // 只有剪C iOS才能发起这类审批
    }
    this.logger.info(
      `[createApprovalHotfixOrder] approvalCode: ${approvalCode} form: ${approvalConfig.form} productNames: ${productName}, versions: ${JSON.stringify(versions)}, desireFullTime: ${req?.desireFullTime}, isFE: ${req?.isFE}, isFullReleaseVersion: ${req?.isFullReleaseVersion}`,
    );

    const qaBMs: string[] = [];
    if (versions) {
      for (const version of versions) {
        if (version) {
          const bmRsp = await this.fetchQaBmEmails(appId, version);
          qaBMs.push(...bmRsp);
        }
      }
    }

    if (req.hotfixActionType !== HotfixActionType.Release) {
      // 审批记录存储到数据库中
      await this.approvalDbService.createOneHotfixApproval(req, {
        instanceCode: '-1', // 热修演练/测试 只落库，无需其他动作
        approvalCode,
        approvalType: ApprovalType.HotFix,
        createUserEmail: userEmail,
        approvalStatus: ApprovalOrderStatus.New,
        history: [
          {
            timeStamp: dayjs().unix(),
            event: ApprovalHistoryEvent.CREATE_APPROVAL, // 事件
            message: '创建审批流工单',
          },
        ],
      });
      return -1;
    }

    const formInfo = [
      {
        id: 'widget17309950487330001', // 热修任务名称
        type: 'input',
        value: req.approvalName,
      },
      {
        id: 'widget17309950744230001', // 应用
        type: 'input',
        value: productName,
      },
      {
        id: 'widget17310373361380001', // 平台
        type: 'input',
        value: versionUtils.appIdToAppInfo(appId.toString())?.platform,
      },
      {
        id: 'widget17309950959290001', // 期望修复版本
        type: 'textarea',
        value: req?.versions?.join(' | ') ?? '未知',
      },
      {
        id: 'widget17309951182540001', // 业务方向
        type: 'input',
        value: req?.business ? ApprovalBusinessConfigs?.[req.business]?.name : '未知',
      },
      {
        id: 'widget17292390360730001', // 具体表现
        type: 'textarea',
        value: req?.problemDetail,
      },
      {
        id: 'widget17309952968700001', // 问题类型
        type: 'input',
        value: req?.hotfixProblemType ? hotfixTypeText[req?.hotfixProblemType] : undefined,
      },
      {
        id: 'widget17292393781880001', // 影响用户量级
        type: 'input',
        value: req.userNumber,
      },
      {
        id: 'widget17309951906180001', // 指标影响
        type: 'textarea',
        value: req?.indicatorImpact,
      },
      {
        id: 'widget17309952107090001', // 收入影响
        type: 'textarea',
        value: req?.incomeImpact,
      },
      {
        id: 'widget17309954729000001', // 能否通过线上配置解决
        type: 'radioV2',
        value: req?.canSolveOnline ? 'm37i0f0l-j5s9jehus0b-0' : 'm37i0f0l-rhsa04y43ui-0',
      },
      {
        id: 'widget17309954824250001', // 问题是否必现
        type: 'radioV2',
        value: req?.isInevitable ? 'm37i0md5-m6u6js2tx2f-0' : 'm37i0md5-oaeyvgw8jpc-0',
      },
      {
        id: 'widget17425265788220001', // 问题是否通过前端修复
        type: 'radioV2',
        value: isFE ? 'm8i7c1af-zypv76eu3wf-0' : 'm8i7c1af-wl3eaj8sfc-0',
      },
      {
        id: 'widget17425266361580001',
        type: 'input',
        value: isFE ? req?.FEChannels?.join(' | ') ?? ' ' : ' ', // 问题修复的前端Channel
      },
      {
        id: 'widget17427881503120001',
        type: 'radioV2',
        value: isFullReleaseVersion ? 'm8mj2f55-g6tkfg55qvf-0' : 'm8mj2f55-b44bwmc47zi-0', // 问题是否修复在已全量版本上
      },
      {
        id: 'widget17309955112550001', // 引入问题的MR
        type: 'input',
        value: req?.mrLinkUrl,
      },
      {
        id: 'widget17309955265720001', // 修复代码运行时机
        type: 'input',
        value: req?.startUpRunTimeType ? hotfixRunningTimeText[req?.startUpRunTimeType] : undefined,
      },
      {
        id: 'widget17309955874950001', // 修复方案
        type: 'textarea',
        value: req?.fixPlan,
      },
      {
        id: 'widget17309955953020001', // 测试方案
        type: 'textarea',
        value: req?.testPlan,
      },
      {
        id: 'widget17309956053020001', // 预期修复时间
        type: 'date',
        value: convertToCustomFormat(req?.desireFullTime),
      },
      inputItem('widget17343495589560001', req?.meegoUrl), // 缺陷链接
    ];
    this.logger.info(`[createApprovalHotfixOrder] creating approval from lark, approval_code=${approvalCode}`);
    // 获取可行性评估的owner
    const userInfos = await batchGetUserInfo({
      data: {
        emails: [
          ...(ApprovalHotfixConfig[appId]?.owners?.qaPoc ?? []),
          ...(ApprovalHotfixConfig[appId]?.owners?.rdPoc ?? []),
        ],
      },
    });
    this.logger.info(
      `[createApprovalHotfixOrder] creating approval from lark, approval_code=${approvalCode}, userInfos => ${JSON.stringify(userInfos)}`,
    );
    const _userIds = userInfos?.map(it => it?.user_id)?.filter(it => it !== undefined);
    // 获取评估的业务角色owner
    const ownerUserInfos = await batchGetUserInfo({
      data: {
        emails: [
          ...(ApprovalHotfixConfig[appId]?.owners?.rdOwner ?? []),
          ...(ApprovalHotfixConfig[appId]?.owners?.qaOwner ?? []),
        ],
      },
    });
    const ownerUserIds = ownerUserInfos?.map(it => it?.user_id)?.filter(it => it !== undefined);
    // 获取leader id
    const leaderOpenId = await this.getLeaderOpenId(userInfo?.open_id);
    this.logger.info(
      `[createApprovalHotfixOrder] creating approval from lark, approval_code=${approvalCode}, userInfos => ${JSON.stringify(ownerUserInfos)}, leaderOpenId => ${leaderOpenId}`,
    );
    // 获取评估的BD
    const bdUserInfos = await batchGetUserInfo({
      data: {
        emails: [...(ApprovalHotfixConfig[appId]?.owners?.BD ?? [])],
      },
    });
    const _bdIds = bdUserInfos?.map(it => it?.user_id)?.filter(it => it !== undefined);
    // 默认非前端且全量版本热修 一级业务poc 二级业务owner
    // 前端修复 一级业务poc 二级BD
    // 非前端且非全量版本热修 一级业务poc 二级自动通过
    let secondaryApprovers: any = [];
    if (isFE) {
      secondaryApprovers = _bdIds;
    } else if (!isFullReleaseVersion) {
      secondaryApprovers = [userInfo?.user_id];
    } else {
      secondaryApprovers = ownerUserIds;
    }
    const result = await this.larkClient.approval.instance.create({
      data: {
        approval_code: approvalCode,
        user_id: userInfo?.user_id,
        form: JSON.stringify(formInfo),
        node_approver_user_id_list: [
          { key: 'b6ac57d4e8ce96bdd49524829bbba2ac', value: _userIds },
          {
            key: '4af8416e5bf83c21a8026f743169f7dd',
            value: secondaryApprovers,
          },
        ],
      },
    });
    const instanceCode = result?.data?.instance_code;
    this.logger.info(
      `[createApprovalHotfixOrder] creat approval from lark successfully, approval_code=${approvalCode}, result=${JSON.stringify(result)}, instance_code=${instanceCode}`,
    );
    if (instanceCode) {
      teaCollect(TeaEvent.APPROVAL, {
        approval_type: ApprovalType.HotFix,
        approval_event: ApprovalHistoryEvent.CREATE_APPROVAL,
        isDev: process.env.NODE_ENV === 'development',
      });
      const _userInfos = await batchGetUserInfo({
        data: {
          emails: unionBy(
            ['<EMAIL>', '<EMAIL>']
              .concat(qaBMs)
              .concat(req?.businessQaEmails ?? [])
              .concat(ApprovalHotfixConfig[appId]?.owners?.rdPoc ?? [])
              .concat(ApprovalHotfixConfig[appId]?.owners?.qaPoc ?? []),
          ),
        },
      });
      // 拉群评估通知
      const chatResult = await this.larkService.createLarkGroup(
        {
          user_id_type: UserIdType.openId,
          set_bot_manager: false,
        },
        {
          description: '',
          name: isFE ? `【热修评估&申请】【前端修复】${req.approvalName}` : `【热修评估&申请】${req.approvalName}`,
          owner_id: userInfo?.open_id ?? '',
          user_id_list:
            _userInfos
              ?.map(it => it?.open_id)
              ?.concat(leaderOpenId)
              ?.filter(it => it !== undefined) ?? [],
        },
      );
      if (chatResult.chat_id) {
        this.logger.info(
          `[createApprovalHotfixOrder] creat approval from lark successfully, approval_code=${approvalCode}, groupId=${chatResult.chat_id}, instance_code=${instanceCode}`,
        );
        // 添加群聊Id
        // 审批记录存储到数据库中
        await this.approvalDbService.createOneHotfixApproval(
          {
            ...req,
            reviewChatGroup: chatResult.chat_id,
          },
          {
            instanceCode,
            approvalCode,
            approvalType: ApprovalType.HotFix,
            createUserEmail: userEmail,
            approvalStatus: ApprovalOrderStatus.New,
            history: [
              {
                timeStamp: dayjs().unix(),
                event: ApprovalHistoryEvent.CREATE_APPROVAL, // 事件
                message: '创建审批流工单',
              },
            ],
          },
        );
      }
      const approvalRecord = await ApprovalInfoTableModel.findOne({
        instanceCode,
        approvalCode,
      })
        .lean()
        .exec();
      if (approvalRecord) {
        const approvalService = useInject(ApprovalService);
        const sendResult = await approvalService.hotfixTaskOnAssessment(approvalRecord);
        this.logger.info(
          `[createApprovalHotfixOrder] creat approval from lark successfully, approval_code=${approvalCode}, sendResult=${JSON.stringify(sendResult)}, instance_code=${instanceCode}`,
        );
      }
    }
    return result.data?.instance_code;
  }

  async createRetouchApprovalHotfixOrder(req: HotfixTaskApprovalReq) {
    const approvalCode = '52F85F89-EC63-44CA-BCB9-7214E5911FD5';
    const userEmail = await useInject(UserService).queryLoginEmail();
    const approvalConfig = await this.larkService.searchApproval(approvalCode);
    const userInfo = await this.larkService.getUserIdByEmail(userEmail);
    if (!approvalConfig) {
      return;
    }
    // hotfix
    const appId = req?.appId ?? -1;
    const productName = versionUtils.appIdToAppInfo(appId.toString())?.productName;
    const versions = req?.versions;
    this.logger.info(
      `[createRetouchApprovalHotfixOrder] approvalCode: ${approvalCode} form: ${approvalConfig.form} productNames: ${productName}, versions: ${JSON.stringify(versions)}, desireFullTime: ${req?.desireFullTime}, isFE: ${req?.isFE}, isFullReleaseVersion: ${req?.isFullReleaseVersion}`,
    );

    if (req.hotfixActionType !== HotfixActionType.Release) {
      // 审批记录存储到数据库中
      await this.approvalDbService.createOneHotfixApproval(req, {
        instanceCode: '-1', // 热修演练/测试 只落库，无需其他动作
        approvalCode,
        approvalType: ApprovalType.HotFix,
        createUserEmail: userEmail,
        approvalStatus: ApprovalOrderStatus.New,
        history: [
          {
            timeStamp: dayjs().unix(),
            event: ApprovalHistoryEvent.CREATE_APPROVAL, // 事件
            message: '创建审批流工单',
          },
        ],
      });
      return -1;
    }
    const formInfo = [
      {
        id: 'widget17520510247930001', // 热修任务名称
        type: 'input',
        value: req.approvalName,
      },
      {
        id: 'widget17520510354770001', // 应用
        type: 'input',
        value: productName,
      },
      {
        id: 'widget17520510393720001', // 平台
        type: 'input',
        value: versionUtils.appIdToAppInfo(appId.toString())?.platform,
      },
      {
        id: 'widget17520510590710001', // 期望修复版本
        type: 'textarea',
        value: req?.versions?.join(' | ') ?? '未知',
      },
      {
        id: 'widget17520510697910001', // 业务方向
        type: 'input',
        value: req?.business ? RetouchApprovalBusinessConfigs?.[req.business]?.name : '未知',
      },
      {
        id: 'widget17520510895260001', // 具体表现
        type: 'textarea',
        value: req?.problemDetail,
      },
      {
        id: 'widget17520511086690001', // 问题类型
        type: 'input',
        value: req?.hotfixProblemType ? hotfixTypeText[req?.hotfixProblemType] : undefined,
      },
      {
        id: 'widget17520511210480001', // 影响用户量级
        type: 'input',
        value: req.userNumber,
      },
      {
        id: 'widget17520511312740001', // 指标影响
        type: 'textarea',
        value: req?.indicatorImpact,
      },
      {
        id: 'widget17520511363310001', // 收入影响
        type: 'textarea',
        value: req?.incomeImpact,
      },
      {
        id: 'widget17520511465360001', // 能否通过线上配置解决
        type: 'radioV2',
        value: req?.canSolveOnline ? 'mcvq0krs-3qccluwh16d-0' : 'mcvq0krs-uy6av80mtq9-0',
      },
      {
        id: 'widget17520511626820001', // 问题是否必现
        type: 'radioV2',
        value: req?.isInevitable ? 'mcvq0x8a-7ttdt53nn9w-0' : 'mcvq0x8a-erhvopxwm6l-0',
      },
      {
        id: 'widget17520512129120001', // 引入问题的MR
        type: 'input',
        value: req?.mrLinkUrl,
      },
      {
        id: 'widget17520512222260001', // 修复代码运行时机
        type: 'input',
        value: req?.startUpRunTimeType ? hotfixRunningTimeText[req?.startUpRunTimeType] : undefined,
      },
      {
        id: 'widget17520512342170001', // 修复方案
        type: 'textarea',
        value: req?.fixPlan,
      },
      {
        id: 'widget17520512382920001', // 测试方案
        type: 'textarea',
        value: req?.testPlan,
      },
      {
        id: 'widget17520512568720001', // 预期修复时间
        type: 'date',
        value: convertToCustomFormat(req?.desireFullTime),
      },
      inputItem('widget17520510469600001', req?.meegoUrl), // 缺陷链接
    ];
    this.logger.info(`[createRetouchApprovalHotfixOrder] creating approval from lark, approval_code=${approvalCode}`);

    // 醒图新流程，拉TCC配置获取审批流程
    const configResult = await this.tcc.keys.get(`${appId}_hotfix_config`);
    const appApprovalProcessConfig = JSON.parse(configResult) as ApprovalProcessConfig;
    const nodeUserIds: { [key: string]: string[] } = {};
    const nodeUserEmails: { [key: string]: string[] } = {};
    const hotfixSubType = req?.hotfixProblemType?.toString() ?? 'default';
    // 不同子类型的审批人不同
    for (const item of appApprovalProcessConfig.items[hotfixSubType]) {
      nodeUserEmails[item.key] = (nodeUserEmails[item.key] ?? []).concat(item.users ?? []);
    }
    // 通用类型：动态获取关联Meego单的QA & RD 塞进二级审批节点
    const r = /.*https:\/\/meego\.feishu\.cn\/(\w+)\/(\w+)\/detail\/(\d+).*/g;
    const m = r.exec(req.meegoUrl ?? '');
    if (m && m.length >= 4) {
      const meegoType = m[2];
      const meegoId = m[3];
      const workflowResult = await this.meego.requestWorkflow(faceuProjectKey, meegoType, meegoId);
      const nodes = workflowResult.data.workflow_nodes;
      this.logger.info(
        `queryMeegoInfo url => ${req?.meegoUrl}, meegoId => ${meegoId}, nodes => ${JSON.stringify(nodes)}`,
      );
      const rdUserKeys = nodes
        ?.filter(node => node !== undefined)
        ?.reduce((acc: string[], node: WorkflowNode) => {
          if (node.role_assignee) {
            // 简化流程，取技术Owner
            const owners = node.role_assignee
              ?.filter(it => {
                const role = it?.role;
                return role ? ['role_501834', 'operator'].includes(role) : false;
              })
              ?.flatMap(assignee => assignee.owners);
            acc.push(...owners);
          }
          return acc;
        }, []);
      const qaUserKeys = nodes
        ?.filter(node => node !== undefined)
        ?.reduce((acc: string[], node: WorkflowNode) => {
          if (node.role_assignee) {
            // 需求QA
            const owners = node.role_assignee
              ?.filter(it => {
                const role = it?.role;
                return role ? ['clientqa', 'reporter'].includes(role) : false;
              })
              ?.flatMap(assignee => assignee.owners);
            acc.push(...owners);
          }
          return acc;
        }, []);
      const rdMeegoUsers = await this.meego.requestMeegoUserInfos({
        user_keys: uniq(rdUserKeys ?? []),
      });
      const qaMeegoUsers = await this.meego.requestMeegoUserInfos({
        user_keys: uniq(qaUserKeys ?? []),
      });
      for (const user of [...rdMeegoUsers, ...qaMeegoUsers]) {
        // 热修质量影响面判断
        nodeUserEmails['54e8ce3880b03f8f5f4ed03c19ec4673'] = [
          ...(nodeUserEmails['54e8ce3880b03f8f5f4ed03c19ec4673'] ?? []),
          user.email,
        ];
      }
    }
    // 拉取初始化受影响业务方向 并注入三级审批
    for (const business of req?.affectBusinessLine?.initial ?? []) {
      const info = RetouchApprovalBusinessConfigs[business].owners
        .map(it => Object.values(it))
        .flat()
        .flat();
      nodeUserEmails['8026fb829ed76273da779749334c0e0c'] = [
        ...info,
        ...(nodeUserEmails['8026fb829ed76273da779749334c0e0c'] ?? []),
      ];
    }

    // 子类型
    switch (req.hotfixProblemType) {
      case HotfixProblemType.Experience:
      case HotfixProblemType.ProductData:
        // 产品/体验类 获取业务方向的owner加到一级审批节点
        const pmPocEmails =
          req.businessPMOwner ??
          (RetouchApprovalBusinessConfigs?.[req?.business ?? '']?.owners ?? []).find(
            it => Object.keys(it)[0] === 'pmPOC',
          )?.pmPOC;
        if (pmPocEmails) {
          // SOP问题标准判断
          nodeUserEmails['1c52bb0cce89474d44e3bedb6610082b'] = [
            ...(nodeUserEmails['1c52bb0cce89474d44e3bedb6610082b'] ?? []),
            ...pmPocEmails,
          ];
        }
        break;
      default:
        break;
    }

    // 拉取用户信息
    for (const key of Object.keys(nodeUserEmails)) {
      const userInfos = await batchGetUserInfo({
        data: {
          emails: uniq(nodeUserEmails[key] ?? []),
        },
      });
      nodeUserIds[key] = (nodeUserIds[key] ?? []).concat(
        userInfos?.map(it => it?.user_id)?.filter(it => it !== undefined) ?? [],
      );
    }

    this.logger.info(
      `[createRetouchApprovalHotfixOrder] creating approval from lark, approval_code=${approvalCode}, openIds => ${JSON.stringify(nodeUserIds)}`,
    );
    const result = await this.larkClient.approval.instance.create({
      data: {
        approval_code: approvalCode,
        user_id: userInfo?.user_id,
        form: JSON.stringify(formInfo),
        node_approver_user_id_list: Object.keys(nodeUserIds).map(key => ({ key, value: uniq(nodeUserIds[key]) })),
        node_cc_user_id_list: [],
      },
    });
    const instanceCode = result?.data?.instance_code;
    this.logger.info(
      `[createRetouchApprovalHotfixOrder] creat approval from lark successfully, approval_code=${approvalCode}, result=${JSON.stringify(result)}, instance_code=${instanceCode}`,
    );
    if (instanceCode) {
      teaCollect(TeaEvent.APPROVAL, {
        approval_type: ApprovalType.HotFix,
        approval_event: ApprovalHistoryEvent.CREATE_APPROVAL,
        isDev: process.env.NODE_ENV === 'development',
      });
      // 拉群评估通知
      const chatResult = await this.larkService.createLarkGroup(
        {
          user_id_type: UserIdType.openId,
          set_bot_manager: false,
        },
        {
          description: '',
          name: `【热修评估&申请】${productName}-${versionUtils.appIdToAppInfo(appId.toString())?.platform}-${req?.versions?.join(' | ')}-${req.approvalName}`,
          owner_id: userInfo?.open_id ?? '',
          user_id_list: [],
        },
      );
      if (chatResult.chat_id) {
        this.logger.info(
          `[createRetouchApprovalHotfixOrder] creat approval from lark successfully, approval_code=${approvalCode}, groupId=${chatResult.chat_id}, instance_code=${instanceCode}`,
        );
        // 添加群聊Id
        // 审批记录存储到数据库中
        await this.approvalDbService.createOneHotfixApproval(
          {
            ...req,
            reviewChatGroup: chatResult.chat_id,
          },
          {
            instanceCode,
            approvalCode,
            approvalType: ApprovalType.HotFix,
            createUserEmail: userEmail,
            approvalStatus: ApprovalOrderStatus.New,
            history: [
              {
                timeStamp: dayjs().unix(),
                event: ApprovalHistoryEvent.CREATE_APPROVAL, // 事件
                message: '创建审批流工单',
              },
            ],
            chatId: chatResult?.chat_id,
          },
        );
      }
    }
    return result.data?.instance_code;
  }

  async createApprovalRequirementOrder(req: RequirementApprovalReq) {
    const approvalCode = '1578F772-BBB3-4005-BD69-F6889904133A';
    const userEmail = await useInject(UserService).queryLoginEmail();
    const approvalConfig = await this.larkService.searchApproval(approvalCode);
    const userInfo = await this.larkService.getUserIdByEmail(userEmail);
    if (!approvalConfig) {
      return;
    }

    // const meegoId = this.extractId(meegoUrl);
    // if (!meegoId) {
    //   return errorRsp('解析meego Id错误, 请检查复制的meego链接!');
    // }
    // await this.meegoService.requestTaskInfo(faceuProjectKey, 'story', {
    //   work_item_ids: [Number(meegoId)],
    //   fields: ['name'],
    // });
    // const form: ApprovalForm[] = JSON.parse(approvalConfig.form);
    this.logger.info(
      `[createApprovalRequirementOrder] approvalCode: ${approvalCode} appIds: ${JSON.stringify(req?.appIds)} form: ${approvalConfig.form} notifyUser: ${JSON.stringify(req.notifyUsersEmails)}`,
    );
    const productNames = versionUtils.getProductName(req?.appIds);
    const version = versionUtils.getApprovalVersions(req?.version, req?.appIds);
    const platforms = versionUtils.getPlatformName(req?.appIds);
    this.logger.info(
      `[createApprovalRequirementOrder] approvalCode: ${approvalCode} form: ${approvalConfig.form} productNames: ${productNames} platforms: ${platforms}`,
    );

    const requirementTypeOptions = {
      [RequirementChangeActionType.insertBeforeCodeBreeze]: 'm2y4nxub-ucj6dop2xmr-0',
      [RequirementChangeActionType.changeBeforeCodeBreeze]: 'm2y4nxub-sh40hhiq75o-0',
      [RequirementChangeActionType.unkown]: 'm2y56w8a-27a3wxgb3pu-1',
      [RequirementChangeActionType.insert]: 'm2y4nxum-bswdfc3r09-1',
      [RequirementChangeActionType.inverted]: 'm2y4nxub-l48xl2murjg-0',
    };

    const businessOwnerApprovalOptions = (type?: ApprovalOrderStatus) => {
      if (!type) {
        return 'm34gf3ep-lrhlud2pcki-1';
      }
      switch (type) {
        case ApprovalOrderStatus.Completed:
          return 'm34g6eot-v2sqwxhv5ri-0';
        case ApprovalOrderStatus.Rejected:
          return 'm34g6eot-yrbwqdnudvo-0';
        default:
          return 'm34gf3ep-lrhlud2pcki-1';
      }
    };

    const formInfo = [
      {
        id: 'widget17292310337490001', // 插入需求名称
        type: 'input',
        value: req.name,
      },
      {
        id: 'widget17304288601780001', // 需求类型
        type: 'radioV2',
        value: requirementTypeOptions[req.type],
      },
      {
        id: 'widget17304515321490001', // 业务线
        type: 'input',
        value: ApprovalBusinessConfigs?.[req.business]?.name ?? '未知',
      },
      {
        id: 'widget17294917958900001', // 插入需求链接
        type: 'input',
        value: req.meegoInfo?.link?.url,
      },
      {
        id: 'widget17292310754150001', // 需求变更内容
        type: 'textarea',
        value: req.content,
      },
      {
        id: 'widget17292311409960001', // 前置评估文档
        type: 'input',
        value: req.detailDoc,
      },
      {
        id: 'widget17339913541220001', // 平台
        type: 'input',
        value: platforms,
      },
      {
        id: 'widget17292310860950001', // 插入评估模版
        type: 'textarea',
        value: req.reason,
      },
      {
        id: 'widget17292310926130001', // 插入影响
        type: 'textarea',
        value: req.impact,
      },
      {
        id: 'widget17292311140460001', // 插入版本
        type: 'input',
        value: version ?? '无',
      },
      {
        id: 'widget17308109946520001', // 业务负责人同意插入
        type: 'radioV2',
        value: businessOwnerApprovalOptions(req?.businessOwnerCheckStatus),
      },
      {
        id: 'widget17294943720930001', // 应用
        value: productNames && productNames.length > 0 ? productNames : '未知',
        type: 'input',
      },
      {
        id: 'widget17292311271820001', // 更新后的排期
        type: 'textarea',
        value: req.scheduling,
      },
    ];
    // 拉取RD BM信息
    const firstNodeOpenIds: string[] = [];
    const finalNodeOpenIds: string[] = [];
    const appIds = req?.appIds ?? [];
    const versionPocUserIds = await this.versionPocUserIds(appIds);
    const firsNodeCCOpenIds: string[] = [];
    const businessUserIds = await this.businessUserIds(appIds);
    if (businessUserIds) {
      firsNodeCCOpenIds?.push(...businessUserIds);
    }

    const qaBMs: string[] = [];
    if (appIds) {
      for (const appId of appIds) {
        if (appId && version) {
          const bmRsp = await this.fetchQaOpenIds(appId, version);
          qaBMs.push(...bmRsp);
        }
      }
    }
    // 是否包含FE
    const isIncludeFE = ['marketingTools_EquityPlan', 'marketingTools_CoreAbilities'].includes(req.business);
    const debugUserIds = await this.debugUserIds(appIds);
    // 增加BM知会节点
    const ccUserIds = await this.ccUserIds(appIds);
    // 增加QA节点
    const qaCheckUserIds = await this.qaCheckUserIds(appIds);
    if (req.type === RequirementChangeActionType.insert) {
      const versionBmUserIds = await this.rdBMUserIds(appIds, req?.version);
      const qaBmUserIds = await this.qaBMUserIds(appIds, req?.version);
      // 需求插入后增加RD、QA BM知会节点
      firstNodeOpenIds.push(...versionBmUserIds);
      ccUserIds?.push(...qaBmUserIds);
      ccUserIds?.push(...versionBmUserIds);
      if (versionPocUserIds && !isIncludeFE) {
        firstNodeOpenIds.push(...versionPocUserIds);
      }
      const versionOwnerUserIds = await this.versionOwner(appIds);
      this.logger.info(
        `[createApprovalRequirementOrder] creating approval from lark, approval_code=${approvalCode}, versionOwnerUserIds => ${JSON.stringify(versionOwnerUserIds)}, versionBmUserIds => ${JSON.stringify(versionBmUserIds)}, qaBmUserIds => ${JSON.stringify(qaBmUserIds)}`,
      );
      if (versionOwnerUserIds && !isIncludeFE) {
        finalNodeOpenIds.push(...compact(versionOwnerUserIds));
      }
    } else {
      const userInfos = await batchGetUserInfo({
        data: {
          emails: extractApprovalBusinessEmails(ApprovalBusinessConfigs?.[req.business]),
        },
      });
      this.logger.info(
        `[createApprovalRequirementOrder] creating approval from lark, approval_code=${approvalCode}, userInfos => ${JSON.stringify(userInfos)}`,
      );
      const _userIds = userInfos?.map(it => it?.user_id)?.filter(it => it !== undefined);
      if (_userIds) {
        finalNodeOpenIds.push(...compact(_userIds));
      }
    }
    this.logger.info(
      `[createApprovalRequirementOrder] creating approval from lark, approval_code=${approvalCode}, firstNodeOpenIds => ${JSON.stringify(firstNodeOpenIds)}, finalNodeOpenIds => ${JSON.stringify(finalNodeOpenIds)}, versionPocUserIds => ${JSON.stringify(versionPocUserIds)}, ccUserIds => ${JSON.stringify(ccUserIds)}`,
    );
    const result = await this.larkClient.approval.instance.create({
      data: {
        approval_code: approvalCode,
        user_id: userInfo?.user_id,
        form: JSON.stringify(formInfo),
        node_approver_user_id_list: [
          { key: 'db2a0e4f2cefc4871a7fc4af5bf3dc8b', value: qaCheckUserIds ?? [] },
          { key: '83f4ed64d54192c610322fcae38a3a04', value: uniq(firstNodeOpenIds) },
          { key: 'cc1a79af48e0f5e80cced46418643fe0', value: uniq(finalNodeOpenIds) },
        ],
        node_cc_user_id_list: [
          { key: 'db2a0e4f2cefc4871a7fc4af5bf3dc8b', value: versionPocUserIds?.concat(debugUserIds ?? []) },
          {
            key: '83f4ed64d54192c610322fcae38a3a04',
            value: debugUserIds?.concat(firsNodeCCOpenIds ?? [])?.concat(qaBMs ?? []),
          },
          { key: 'cc1a79af48e0f5e80cced46418643fe0', value: ccUserIds },
        ],
      },
    });
    const instanceCode = result?.data?.instance_code;
    this.logger.info(
      `[createApprovalRequirementOrder] creat approval from lark successfully, approval_code=${approvalCode}, result=${JSON.stringify(result)}, instance_code=${instanceCode}`,
    );
    if (instanceCode) {
      teaCollect(TeaEvent.APPROVAL, {
        approval_type: ApprovalType.InsertProductRequirements,
        approval_event: ApprovalHistoryEvent.CREATE_APPROVAL,
        version: req?.version,
        isDev: process.env.NODE_ENV === 'development',
      });
      // 审批记录存储到数据库中
      await this.approvalDbService.createOneRequirementChangeApproval(req, {
        instanceCode,
        approvalCode,
        approvalType: ApprovalType.InsertProductRequirements,
        createUserEmail: userEmail,
        approvalStatus: ApprovalOrderStatus.New,
        notifyUsersEmails: req.notifyUsersEmails,
        meegoLinks: [
          {
            link: {
              url: req.meegoInfo?.link?.url,
              name: req.meegoInfo?.link?.name,
            },
          },
        ],
        history: [
          {
            timeStamp: dayjs().unix(),
            event: ApprovalHistoryEvent.CREATE_APPROVAL, // 事件
            message: '创建审批流工单',
          },
        ],
      });
      // const approvalRecord = await ApprovalInfoTableModel.findOne({
      //   instanceCode,
      //   approvalCode,
      // })
      //   .lean()
      //   .exec();
      // TODO '审批发起成功，请前往飞书跟进审批进度！'
      // if (approvalRecord) {
      //   const approvalService = useInject(ApprovalService);
      //   const sendResult = await approvalService.requirementChangeApproved(approvalRecord);
      // }
    }
    return result.data?.instance_code;
  }

  async createRetouchApprovalInsertRequirementAfterCodeFreeze(req: RequirementApprovalReq) {
    const approvalCode = 'E978B6C9-CA0D-4D7C-B06A-BA750EAECE89';
    const userEmail = await useInject(UserService).queryLoginEmail();
    const approvalConfig = await this.larkService.searchApproval(approvalCode);
    const userInfo = await this.larkService.getUserIdByEmail(userEmail);
    if (!approvalConfig) {
      return;
    }

    this.logger.info(
      `[createRetouchApprovalInsertRequirementAfterCodeFreeze] approvalCode: ${approvalCode} appIds: ${JSON.stringify(req?.appIds)} form: ${approvalConfig.form} notifyUser: ${JSON.stringify(req.notifyUsersEmails)}`,
    );
    const productNames = versionUtils.getProductName(req?.appIds);
    const version = versionUtils.getApprovalVersions(req?.version, req?.appIds);
    const platforms = versionUtils.getPlatformName(req?.appIds);
    this.logger.info(
      `[createRetouchApprovalInsertRequirementAfterCodeFreeze] approvalCode: ${approvalCode} form: ${approvalConfig.form} productNames: ${productNames} platforms: ${platforms}`,
    );

    const businessOwnerApprovalOptions = (type?: ApprovalOrderStatus) => {
      if (!type) {
        return 'mcveyrpq-mrq51bm0dgh-0';
      }
      switch (type) {
        case ApprovalOrderStatus.Completed:
          return 'mcveyrpq-gh1sxdf340h-0';
        case ApprovalOrderStatus.Rejected:
          return 'mcveyrpq-mrq51bm0dgh-0';
        default:
          return 'mcveyrpq-mrq51bm0dgh-0';
      }
    };

    const getAffectBusinessLines = (businessLines: string[]) => {
      const result: string[] = [];
      for (const businessLine of businessLines) {
        const businessLineConfig = RetouchApprovalBusinessConfigs[businessLine];
        if (businessLineConfig) {
          result.push(businessLineConfig.name);
        }
      }
      return result;
    };

    const formInfo = [
      {
        id: 'widget17520325309350001', // 插入需求名称
        type: 'input',
        value: req.name,
      },
      {
        id: 'widget17520325766530001', // 业务线
        type: 'input',
        value: RetouchApprovalBusinessConfigs?.[req.business]?.name ?? '未知',
      },
      {
        id: 'widget17520326255070001', // 插入需求链接
        type: 'input',
        value: req.meegoInfo?.link?.url,
      },
      {
        id: 'widget17520326989810001', // 需求变更内容
        type: 'textarea',
        value: req.content,
      },
      {
        id: 'widget17520327104390001', // 前置评估文档
        type: 'input',
        value: req.detailDoc,
      },
      {
        id: 'widget17520326088010001', // 平台
        type: 'input',
        value: platforms,
      },
      {
        id: 'widget17520327104390001', // 插入评估模版
        type: 'input',
        value: req.reason,
      },
      {
        id: 'widget17520327580540001', // 插入影响
        type: 'textarea',
        value: req.impact,
      },
      {
        id: 'widget17520326086540001', // 插入版本
        type: 'input',
        value: version ?? '无',
      },
      {
        id: 'widget17520325864460001', // 业务负责人同意插入
        type: 'radioV2',
        value: businessOwnerApprovalOptions(req?.businessOwnerCheckStatus),
      },
      {
        id: 'widget17520326078690001', // 应用
        value: productNames && productNames.length > 0 ? productNames : '未知',
        type: 'input',
      },
      {
        id: 'widget17520327721040001', // 更新后的排期
        type: 'textarea',
        value: req.scheduling,
      },
      {
        id: 'widget17520327629440001', // 需求插入必要性（不插入的影响）
        type: 'textarea',
        value: req.noInsertImpact,
      },
      {
        id: 'widget17520327945300001', // 评估受影响的业务方向
        type: 'textarea',
        value: req?.affectBusinessLine?.initial
          ? getAffectBusinessLines(req.affectBusinessLine.initial).join(' | ')
          : '-',
      },
    ];
    // 醒图新流程，拉TCC配置获取审批流程
    const nodeUserIds: { [key: string]: string[] } = {};
    const nodeUserEmails: { [key: string]: string[] } = {};
    for (const appId of req.appIds) {
      const configResult = await this.tcc.keys.get(`${appId}_insert_requirement_after_codefreeze_config`);
      const appApprovalProcessConfig = JSON.parse(configResult) as ApprovalProcessConfig;
      for (const item of appApprovalProcessConfig?.items?.default) {
        nodeUserEmails[item.key] = (nodeUserEmails[item.key] ?? []).concat(item.users ?? []);
      }
    }
    // 流程加Meego单相关角色
    for (const key of Object.keys(nodeUserEmails)) {
      if (key === '00b4e9a0047c3b5dffb8452972b3878a') {
        // 需求质量影响面判断增加Meego单RD 和 Meego单QA
        const match = req?.meegoInfo?.link?.url?.match(/\/detail\/(\d+)/);
        const meegoId = match ? parseInt(match[1], 10) : null;
        if (meegoId) {
          const workflowResult = await this.meego.requestWorkflow(faceuProjectKey, 'story', meegoId.toString());
          const nodes = workflowResult.data.workflow_nodes;
          this.logger.info(
            `queryMeegoInfo url => ${req?.meegoInfo?.link?.url}, meegoId => ${meegoId}, nodes => ${JSON.stringify(nodes)}`,
          );
          const rdUserKeys = nodes
            ?.filter(node => node !== undefined)
            ?.reduce((acc: string[], node: WorkflowNode) => {
              if (node.role_assignee) {
                // 简化流程，取技术Owner
                const owners = node.role_assignee
                  ?.filter(it => {
                    const role = it?.role;
                    return role ? ['role_501834'].includes(role) : false;
                  })
                  ?.flatMap(assignee => assignee.owners);
                acc.push(...owners);
              }
              return acc;
            }, []);
          const qaUserKeys = nodes
            ?.filter(node => node !== undefined)
            ?.reduce((acc: string[], node: WorkflowNode) => {
              if (node.role_assignee) {
                // 需求QA
                const owners = node.role_assignee
                  ?.filter(it => {
                    const role = it?.role;
                    return role ? ['clientqa'].includes(role) : false;
                  })
                  ?.flatMap(assignee => assignee.owners);
                acc.push(...owners);
              }
              return acc;
            }, []);
          const rdMeegoUsers = await this.meego.requestMeegoUserInfos({
            user_keys: uniq(rdUserKeys ?? []),
          });
          const qaMeegoUsers = await this.meego.requestMeegoUserInfos({
            user_keys: uniq(qaUserKeys ?? []),
          });
          for (const user of [...rdMeegoUsers, ...qaMeegoUsers]) {
            nodeUserEmails[key] = [...(nodeUserEmails[key] ?? []), user.email];
          }
        }
      }
    }
    // 拉取初始化受影响业务方向 并注入审批
    for (const business of req?.affectBusinessLine?.initial ?? []) {
      const info = RetouchApprovalBusinessConfigs[business].owners
        .map(it => Object.values(it))
        .flat()
        .flat();
      nodeUserEmails['767fe65d6e6b4a48239df06123b59e39'] = [
        ...info,
        ...(nodeUserEmails['767fe65d6e6b4a48239df06123b59e39'] ?? []),
      ];
    }
    // 拉取用户信息
    for (const key of Object.keys(nodeUserEmails)) {
      const userInfos = await batchGetUserInfo({
        data: {
          emails: uniq(nodeUserEmails[key] ?? []),
        },
      });
      nodeUserIds[key] = (nodeUserIds[key] ?? []).concat(
        userInfos?.map(it => it?.user_id)?.filter(it => it !== undefined) ?? [],
      );
    }

    this.logger.info(
      `[createRetouchApprovalInsertRequirementAfterCodeFreeze] creating approval from lark, approval_code=${approvalCode}, openIds => ${JSON.stringify(nodeUserIds)}`,
    );
    const result = await this.larkClient.approval.instance.create({
      data: {
        approval_code: approvalCode,
        user_id: userInfo?.user_id,
        form: JSON.stringify(formInfo),
        node_approver_user_id_list: Object.keys(nodeUserIds).map(key => ({ key, value: uniq(nodeUserIds[key]) })),
        node_cc_user_id_list: [],
      },
    });
    const instanceCode = result?.data?.instance_code;
    this.logger.info(
      `[createRetouchApprovalInsertRequirementAfterCodeFreeze] creat approval from lark successfully, approval_code=${approvalCode}, result=${JSON.stringify(result)}, instance_code=${instanceCode}`,
    );
    if (instanceCode) {
      const chatResult = await this.larkService.createLarkGroup(
        {
          user_id_type: UserIdType.openId,
          set_bot_manager: false,
        },
        {
          description: '',
          name: `【封版后需求插入评估&申请】${productNames}-${platforms}-${version}-${req?.name}`,
          owner_id: userInfo?.open_id ?? '',
          user_id_list: [],
        },
      );
      teaCollect(TeaEvent.APPROVAL, {
        approval_type: ApprovalType.InsertProductRequirements,
        approval_event: ApprovalHistoryEvent.CREATE_APPROVAL,
        version: req?.version,
        isDev: process.env.NODE_ENV === 'development',
      });
      // 审批记录存储到数据库中
      await this.approvalDbService.createOneRequirementChangeApproval(req, {
        instanceCode,
        approvalCode,
        approvalType: ApprovalType.InsertProductRequirements,
        createUserEmail: userEmail,
        approvalStatus: ApprovalOrderStatus.New,
        notifyUsersEmails: req.notifyUsersEmails,
        meegoLinks: [
          {
            link: {
              url: req.meegoInfo?.link?.url,
              name: req.meegoInfo?.link?.name,
            },
          },
        ],
        history: [
          {
            timeStamp: dayjs().unix(),
            event: ApprovalHistoryEvent.CREATE_APPROVAL, // 事件
            message: '创建审批流工单',
          },
        ],
        chatId: chatResult?.chat_id,
      });

      const approvalRecord = await ApprovalInfoTableModel.findOne({
        instanceCode,
        approvalCode,
      })
        .lean()
        .exec();
      if (approvalRecord) {
        // 发送风险警示推送
        const approvalService = useInject(ApprovalService);
        await approvalService.retouchRequirementInsertAlert(approvalRecord, [userEmail]);
      }
    }
    return instanceCode;
  }

  private async rdBMUserIds(appIds: number[], version?: string) {
    const bmIds: string[] = [];
    if (!version) {
      return bmIds;
    }
    const containLv = appIds.every(it => AppIdsGroup.LV.includes(it));
    const containCC = appIds.every(it => AppIdsGroup.CC.includes(it));
    // 兼容一下选了剪映-Android（15.5.0）、CapCut-iOS（13.5.0），version为15.5.0，直接通过CapCut-iOS的appId获取BM数据失败
    const overseasVersion = containLv && containCC ? versionUtils.lv2ccVersion(version) : undefined;
    for (const appId of appIds) {
      const _version = versionUtils.isOverseas(appId) && overseasVersion ? overseasVersion : version;
      const appInfo = this.getSpecialInfo(appId, _version);
      const bmRsp = await fetchBmInfo({
        data: {
          appId: appInfo.appId,
          version: appInfo.version,
        },
      });
      this.logger.info(
        `[rdBMUserIds] creating approval from lark, appId => ${appId}, version => ${_version} bmRsp => ${JSON.stringify(bmRsp)}`,
      );
      const rdBms = bmRsp?.data?.filter(it => it.type === BmType.rd).map(bm => bm.userId);
      if (rdBms) {
        bmIds.push(...rdBms);
      }
    }
    return bmIds;
  }

  // pc信息映射
  private getSpecialInfo(appId: number, version: string) {
    if (appId === 35928902) {
      return {
        appId: 2020092892,
        version: versionUtils.lv2ccVersion(version), // x.0.0 -> x-2.0.0
      };
    } else if (appId === 35928901) {
      return {
        appId: 2020092383,
        version: versionUtils.lv2ccVersion(version), // x.0.0 -> x-2.0.0
      };
    }
    return {
      appId,
      version,
    };
  }

  private async qaBMUserIds(appIds: number[], version?: string) {
    const bmIds: string[] = [];
    if (!version) {
      return bmIds;
    }
    for (const appId of appIds) {
      const appInfo = this.getSpecialInfo(appId, version);
      const bmRsp = await fetchBmInfo({
        data: {
          appId: appInfo.appId,
          version: appInfo.version,
        },
      });
      this.logger.info(
        `[qaBMUserIds] creating approval from lark, appId => ${appId}, version => ${version} bmRsp => ${JSON.stringify(bmRsp)}`,
      );
      const bmEmails = bmRsp?.data?.filter(it => it.type === BmType.qa).map(bm => bm.email);
      if (bmEmails) {
        const userId = (
          await this.larkService.batchGetUserId(UserIdType.userId, {
            emails: bmEmails,
          })
        )
          .map((value: UserData) => value.user_id)
          .filter(it => it !== undefined && it.length > 0);
        bmIds.push(...userId);
      }
    }
    return bmIds;
  }

  private async versionOwner(appIds: number[]) {
    const versionOwnerEmails = appIds?.reduce(
      (previousValue, currentValue, currentIndex, array) =>
        previousValue
          .concat(ApprovalRequirementConfig?.[currentValue]?.owners?.rdOwner ?? [])
          .concat(ApprovalRequirementConfig?.[currentValue]?.owners?.qaOwner ?? []),
      [] as string[],
    );
    const versionOwnerInfos = await batchGetUserInfo({
      data: {
        emails: Array.from(new Set(versionOwnerEmails ?? [])),
      },
    });
    return versionOwnerInfos?.map(it => it?.user_id)?.filter(it => it !== undefined);
  }
  private async debugUserIds(appIds: number[]) {
    const userInfos = await batchGetUserInfo({
      data: {
        emails: ['<EMAIL>', '<EMAIL>'],
      },
    });
    return userInfos?.map(it => it?.user_id)?.filter(it => it !== undefined);
  }
  private async versionPocUserIds(appIds: number[]) {
    const versionPocEmails = appIds?.reduce(
      (previousValue, currentValue, currentIndex, array) =>
        previousValue
          .concat(ApprovalRequirementConfig?.[currentValue]?.owners?.rdPoc ?? [])
          .concat(ApprovalRequirementConfig?.[currentValue]?.owners?.qaPoc ?? []),
      [] as string[],
    );
    const versionPocUserInfos = await batchGetUserInfo({
      data: {
        emails: Array.from(new Set(versionPocEmails ?? [])),
      },
    });
    return versionPocUserInfos?.map(it => it?.user_id)?.filter(it => it !== undefined);
  }

  private async businessUserIds(appIds: number[]) {
    const ccConfig: { [key: number]: string[] } = {
      251502: ['<EMAIL>'],
      251501: ['<EMAIL>'],
      2020093924: ['<EMAIL>'],
      2020093988: ['<EMAIL>'],
    };
    const versionPocEmails = appIds?.reduce(
      (previousValue, currentValue, currentIndex, array) =>
        previousValue.concat(ccConfig?.[currentValue] ?? []).concat(ccConfig?.[currentValue] ?? []),
      [] as string[],
    );
    const versionPocUserInfos = await batchGetUserInfo({
      data: {
        emails: uniq(versionPocEmails ?? []),
      },
    });
    return versionPocUserInfos?.map(it => it?.user_id)?.filter(it => it !== undefined);
  }

  private async qaCheckUserIds(appIds: number[]) {
    const qaConfig: { [key: number]: string[] } = {
      177502: ['<EMAIL>', '<EMAIL>'],
      177501: ['<EMAIL>', '<EMAIL>'],
      300601: ['<EMAIL>', '<EMAIL>'],
      300602: ['<EMAIL>', '<EMAIL>'],
      251501: ['<EMAIL>', '<EMAIL>'],
      251502: ['<EMAIL>', '<EMAIL>'],
      2020093924: ['<EMAIL>', '<EMAIL>'],
      2020093988: ['<EMAIL>', '<EMAIL>'],
      244127338754: ['<EMAIL>', '<EMAIL>'],
      225469550850: ['<EMAIL>', '<EMAIL>'],
      2020092892: ['<EMAIL>', '<EMAIL>'],
      2020092383: ['<EMAIL>', '<EMAIL>'],
      35928901: ['<EMAIL>', '<EMAIL>'],
      35928902: ['<EMAIL>', '<EMAIL>'],
      764682035714: ['<EMAIL>', '<EMAIL>'],
      903699867138: ['<EMAIL>', '<EMAIL>'],
      787266764802: ['<EMAIL>', '<EMAIL>'],
      787403161602: ['<EMAIL>', '<EMAIL>'],
      // NOTE 多端接入配置-异常流程
    };
    const emails = appIds?.reduce(
      (previousValue, currentValue, currentIndex, array) =>
        previousValue.concat(qaConfig?.[currentValue] ?? []).concat(qaConfig?.[currentValue] ?? []),
      [] as string[],
    );
    if (!emails || emails.length <= 0) {
      return [] as string[];
    }
    const userInfos = await batchGetUserInfo({
      data: {
        emails: uniq(emails ?? []),
      },
    });
    return userInfos?.map(it => it?.user_id)?.filter(it => it !== undefined) as string[] | undefined;
  }
  private async ccUserIds(appIds: number[]) {
    const ccConfig: { [key: number]: string[] } = {
      130690253570: ['<EMAIL>'],
      177502: [
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
      ],
      177501: [
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
      ],
      300601: [
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
      ],
      300602: [
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
      ],
      251502: [
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
      ],
      251501: [
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
      ],
      2020093924: [
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
      ],
      2020093988: [
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
      ],
      2020092892: [
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
      ],
      2020092383: [
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
      ],
      35928901: [
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
      ],
      35928902: [
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
      ],
      244127338754: [
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
      ],
      225469550850: [
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
      ],
      764682035714: [
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
      ],
      903699867138: [
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
      ],
      787266764802: [
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
      ],
      787403161602: [
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
      ],
      // NOTE 多端接入配置-异常流程
    };
    const emails = appIds?.reduce(
      (previousValue, currentValue, currentIndex, array) =>
        previousValue.concat(ccConfig?.[currentValue] ?? []).concat(ccConfig?.[currentValue] ?? []),
      [] as string[],
    );
    const userInfos = await batchGetUserInfo({
      data: {
        emails: Array.from(new Set(emails ?? [])),
      },
    });
    return userInfos?.map(it => it?.user_id)?.filter(it => it !== undefined);
  }

  async createFixVersionApprovalOrder(req: FixVersionApprovalReq) {
    const approvalCode = '0E0D600B-511C-4B43-BF3D-76E99B8C8BC0';
    const approvalConfig = await this.larkService.searchApproval(approvalCode);
    if (!approvalConfig) {
      return;
    }
    const userEmail = await useInject(UserService).queryLoginEmail();
    const userInfo = await this.larkService.getUserIdByEmail(userEmail);
    // 。业务线基本信息
    const appId = req?.appId ?? -1;
    const productName = versionUtils.appIdToAppInfo(appId.toString())?.productName;
    this.logger.info(
      `[createFixVersionApprovalOrder] approvalCode: ${approvalCode} form: ${approvalConfig.form} productNames: ${productName}, expectedIntegrationTime: ${req?.expectedIntegrationTime}`,
    );

    const version = req?.desireVersion;
    const qaBMs: string[] = [];
    if (version) {
      const bmRsp = await this.fetchQaBmEmails(appId, version);
      qaBMs.push(...bmRsp);
    }

    const formInfo = [
      {
        id: 'widget17292396791400001', // 热修任务名称
        type: 'input',
        value: req.approvalName,
      },
      {
        id: 'widget17311507549260001', // 应用名
        type: 'input',
        value: productName,
      },
      {
        id: 'widget17311507638600001', // 平台
        type: 'input',
        value: versionUtils.appIdToAppInfo(appId.toString())?.platform,
      },
      {
        id: 'widget17311507808880001', // 期望修复版本
        type: 'textarea',
        value: req?.desireVersion ?? '未知',
      },
      {
        id: 'widget17311507946090001', // 业务方向
        type: 'input',
        value: req?.business ? ApprovalBusinessConfigs?.[req.business]?.name : '未知',
      },
      textareaItem('widget17311508068800001', req?.problemDetail), // 具体表现
      inputItem('widget17311508189550001', req?.hotfixProblemType ? hotfixTypeText[req?.hotfixProblemType] : undefined), // 问题类型
      inputItem('widget17311508389810001', req.userNumber), // 影响用户量级
      textareaItem('widget17311508496730001', req.indicatorImpact), // 指标影响
      textareaItem('widget17311508589890001', req.incomeImpact), // 收入影响
      simpleRadioItem(
        'widget17311508814550001',
        'm3a2jd0w-qh8dblbdv0o-0',
        'm3a2jd0w-mihrkjtn07-0',
        req?.canSolveOnline,
      ), // 能否通过线上配置解决
      simpleRadioItem('widget17311509015450001', 'm3a2jsiz-u2hwfabwjx-0', 'm3a2jsiz-2i6oo6n8siu-0', req?.isInevitable), // 问题是否必现
      inputItem('widget17311509172430001', req?.mrLinkUrl), // 引入问题的MR
      textareaItem('widget17311509410140001', req?.fixPlan), // 修复方案
      textareaItem('widget17311509483210001', req?.testPlan), // 测试方案
      formItem('widget17311509584420001', 'date', convertToCustomFormat(req?.expectedIntegrationTime)), // 预期合入时间
      inputItem('widget17316039755540001', req?.meegoUrl), // meego链接
      textareaItem('widget17316039452670001', req?.background), // 背景
    ].filter(it => it !== undefined);
    // 获取可行性评估的owner
    const firstUserInfos = await batchGetUserInfo({
      data: {
        emails: [
          ...(ApprovalFixVersionConfig?.[appId]?.owners?.rdPoc ?? []),
          ...(ApprovalFixVersionConfig?.[appId]?.owners?.qaPoc ?? []),
        ],
      },
    });
    // 获取可行性评估的owner
    const _userInfos = await batchGetUserInfo({
      data: {
        emails: req?.isBugfixType
          ? [
              ...(ApprovalFixVersionConfig?.[appId]?.owners?.qaOwner ?? []),
              ...(ApprovalFixVersionConfig?.[appId]?.owners?.rdOwner ?? []),
            ]
          : [
              ...(ApprovalFixVersionConfig?.[appId]?.owners?.rdManager ?? []),
              ...(ApprovalFixVersionConfig?.[appId]?.owners?.pmManager ?? []),
            ],
      },
    });
    // 抄送名单
    const ccUserInfo = await batchGetUserInfo({
      data: {
        emails: req?.isBugfixType
          ? []
          : [
              ...(ApprovalFixVersionConfig?.[appId]?.owners?.qaOwner ?? []),
              ...(ApprovalFixVersionConfig?.[appId]?.owners?.rdOwner ?? []),
              '<EMAIL>',
            ],
      },
    });
    // 获取leader id
    const leaderOpenId = await this.getLeaderOpenId(userInfo?.open_id);
    this.logger.info(
      `[createFixVersionApprovalOrder] creating approval from lark, approval_code=${approvalCode}, userInfos => ${JSON.stringify(_userInfos)}, leaderOpenId => ${leaderOpenId}`,
    );
    const _userIds = _userInfos?.map(it => it?.user_id)?.filter(it => it !== undefined);
    const firstUserIds = firstUserInfos?.map(it => it?.user_id)?.filter(it => it !== undefined);
    const ccUserIds = ccUserInfo?.map(it => it?.user_id)?.filter(it => it !== undefined);
    const result = await this.larkClient.approval.instance.create({
      data: {
        approval_code: approvalCode,
        user_id: userInfo?.user_id,
        form: JSON.stringify(formInfo),
        node_approver_user_id_list: [
          { key: 'c19e89fe8595bcb1e0829aeac099da58', value: firstUserIds },
          { key: '4b923d30501c5a2616298f8d60aac4b8', value: _userIds },
        ],
        node_cc_user_id_list: [{ key: '4b923d30501c5a2616298f8d60aac4b8', value: ccUserIds }],
      },
    });
    this.logger.info(`[createFixVersionApprovalOrder] approvalCode: ${approvalCode} result: ${JSON.stringify(result)}`);
    const instanceCode = result?.data?.instance_code;
    if (!instanceCode) {
      return;
    }
    // 上报
    teaCollect(TeaEvent.APPROVAL, {
      approval_type: ApprovalType.FixedVersionRequirements,
      approval_event: ApprovalHistoryEvent.CREATE_APPROVAL,
      isDev: process.env.NODE_ENV === 'development',
    });
    // 拉群
    const userInfos = await batchGetUserInfo({
      data: {
        emails: unionBy(
          (req?.businessQaEmails ?? [])
            .concat(qaBMs)
            .concat(ApprovalFixVersionConfig?.[appId]?.owners?.rdPoc ?? [])
            .concat(ApprovalFixVersionConfig?.[appId]?.owners?.qaPoc ?? []),
        ),
      },
    });
    // 拉群评估通知
    const chatResult = await this.larkService.createLarkGroup(
      {
        user_id_type: UserIdType.openId,
        set_bot_manager: false,
      },
      {
        description: '',
        name: `【小版本评估&申请】${req.approvalName}`,
        owner_id: userInfo?.open_id ?? '',
        user_id_list:
          userInfos
            ?.map(it => it?.open_id)
            ?.concat(leaderOpenId)
            ?.filter(it => it !== undefined) ?? [],
      },
    );
    const chatGroupId = chatResult?.chat_id;
    if (!chatGroupId) {
      return;
    }
    this.logger.info(
      `[createFixVersionApprovalOrder] approvalCode: ${approvalCode} chatGroupId: ${chatGroupId} chatResult: ${JSON.stringify(chatResult)}`,
    );
    // 审批记录存储到数据库中
    await this.approvalDbService.createOneVersionApproval(
      {
        ...req,
        reviewChatGroup: chatGroupId,
      },
      {
        instanceCode,
        approvalCode,
        approvalType: ApprovalType.FixedVersionRequirements,
        createUserEmail: userEmail,
        approvalStatus: ApprovalOrderStatus.New,
        history: [
          {
            timeStamp: dayjs().unix(),
            event: ApprovalHistoryEvent.CREATE_APPROVAL, // 事件
            message: '创建审批流工单',
          },
        ],
      },
    );
    // 发送审批工单
    const approvalRecord = await ApprovalInfoTableModel.findOne({
      instanceCode,
      approvalCode,
    })
      .lean()
      .exec();
    if (approvalRecord) {
      const approvalService = useInject(ApprovalService);
      const sendResult = await approvalService.fixVersionTaskStartAssessment(approvalRecord);
      this.logger.info(
        `[createFixVersionApprovalOrder] creat approval from lark successfully, approval_code=${approvalCode}, sendResult=${JSON.stringify(sendResult)}, instance_code=${instanceCode}`,
      );
    }
    return instanceCode;
  }

  async createRetouchFixVersionApprovalOrder(req: FixVersionApprovalReq) {
    const approvalCode = '46728CD2-78E0-449E-9AF4-6B7D49A41D2D';
    const approvalConfig = await this.larkService.searchApproval(approvalCode);
    if (!approvalConfig) {
      return;
    }

    const userEmail = await useInject(UserService).queryLoginEmail();
    const userInfo = await this.larkService.getUserIdByEmail(userEmail);
    // 业务线基本信息
    const appId = req?.appId ?? -1;
    const productName = versionUtils.appIdToAppInfo(appId.toString())?.productName;
    this.logger.info(
      `[createRetouchFixVersionApprovalOrder] approvalCode: ${approvalCode} form: ${approvalConfig.form} productNames: ${productName}, expectedIntegrationTime: ${req?.expectedIntegrationTime}`,
    );

    const formInfo = [
      {
        id: 'widget17520689836200001', // 热修任务名称
        type: 'input',
        value: req.approvalName,
      },
      {
        id: 'widget17520689898560001', // 应用名
        type: 'input',
        value: productName,
      },
      {
        id: 'widget17520689930980001', // 平台
        type: 'input',
        value: versionUtils.appIdToAppInfo(appId.toString())?.platform,
      },
      {
        id: 'widget17520690004520001', // 期望修复版本
        type: 'textarea',
        value: req?.desireVersion ?? '未知',
      },
      {
        id: 'widget17520690071980001', // 业务方向
        type: 'input',
        value: req?.business ? RetouchApprovalBusinessConfigs?.[req.business]?.name : '未知',
      },
      textareaItem('widget17520690309390001', req?.problemDetail), // 具体表现
      inputItem('widget17520690417940001', req?.hotfixProblemType ? hotfixTypeText[req?.hotfixProblemType] : undefined), // 问题类型
      inputItem('widget17520690487050001', req.userNumber), // 影响用户量级
      textareaItem('widget17520690624780001', req.indicatorImpact), // 指标影响
      textareaItem('widget17520690679530001', req.incomeImpact), // 收入影响
      simpleRadioItem(
        'widget17520690867530001',
        'mcw0p3j5-bskoyi5lzpl-0',
        'mcw0p3j5-cvn5oivx60f-0',
        req?.canSolveOnline,
      ), // 能否通过线上配置解决
      simpleRadioItem('widget17520691086690001', 'mcw0pkfy-36xaos3h44q-0', 'mcw0pkfy-nt3wnrtrm8k-0', req?.isInevitable), // 问题是否必现
      inputItem('widget17520691265830001', req?.mrLinkUrl), // 引入问题的MR
      textareaItem('widget17520691333230001', req?.fixPlan), // 修复方案
      textareaItem('widget17520691412920001', req?.testPlan), // 测试方案
      formItem('widget17520691507270001', 'date', convertToCustomFormat(req?.expectedIntegrationTime)), // 预期合入时间
      inputItem('widget17520690212850001', req?.meegoUrl), // meego链接
      textareaItem('widget17520690129850001', req?.background), // 背景
    ].filter(it => it !== undefined);
    this.logger.info(
      `[createRetouchFixVersionApprovalOrder] creating approval from lark, approval_code=${approvalCode}`,
    );

    // 醒图新流程，拉TCC配置获取审批流程
    const configResult = await this.tcc.keys.get(`${appId}_fix_version_config`);
    const appApprovalProcessConfig = JSON.parse(configResult) as ApprovalProcessConfig;
    const nodeUserIds: { [key: string]: string[] } = {};
    const nodeUserEmails: { [key: string]: string[] } = {};
    const hotfixSubType = req?.hotfixProblemType?.toString() ?? 'default';
    // 不同子类型的审批人不同
    for (const item of appApprovalProcessConfig.items[hotfixSubType]) {
      nodeUserEmails[item.key] = [...(nodeUserEmails[item.key] ?? []), ...(item.users ?? [])];
    }
    // 通用类型：动态获取关联Meego单的QA & RD 塞进二级审批节点
    const r = /.*https:\/\/meego\.feishu\.cn\/(\w+)\/(\w+)\/detail\/(\d+).*/g;
    const m = r.exec(req.meegoUrl ?? '');
    if (m && m.length >= 4) {
      const meegoType = m[2];
      const meegoId = m[3];
      const workflowResult = await this.meego.requestWorkflow(faceuProjectKey, meegoType, meegoId);
      const nodes = workflowResult.data.workflow_nodes;
      this.logger.info(
        `queryMeegoInfo url => ${req?.meegoUrl}, meegoId => ${meegoId}, nodes => ${JSON.stringify(nodes)}`,
      );
      const rdUserKeys = nodes
        ?.filter(node => node !== undefined)
        ?.reduce((acc: string[], node: WorkflowNode) => {
          if (node.role_assignee) {
            // 简化流程，取技术Owner
            const owners = node.role_assignee
              ?.filter(it => {
                const role = it?.role;
                return role ? ['role_501834', 'operator'].includes(role) : false;
              })
              ?.flatMap(assignee => assignee.owners);
            acc.push(...owners);
          }
          return acc;
        }, []);
      const qaUserKeys = nodes
        ?.filter(node => node !== undefined)
        ?.reduce((acc: string[], node: WorkflowNode) => {
          if (node.role_assignee) {
            // 需求QA
            const owners = node.role_assignee
              ?.filter(it => {
                const role = it?.role;
                return role ? ['clientqa', 'reporter'].includes(role) : false;
              })
              ?.flatMap(assignee => assignee.owners);
            acc.push(...owners);
          }
          return acc;
        }, []);
      const rdMeegoUsers = await this.meego.requestMeegoUserInfos({
        user_keys: uniq(rdUserKeys ?? []),
      });
      const qaMeegoUsers = await this.meego.requestMeegoUserInfos({
        user_keys: uniq(qaUserKeys ?? []),
      });
      for (const user of [...rdMeegoUsers, ...qaMeegoUsers]) {
        // TODO 质量影响面判断
        nodeUserEmails['1d2e4ad2e88041e2a1876454484c8842'].push(user.email);
      }
    }
    // 拉取初始化受影响业务方向 并注入四级审批
    for (const business of req?.affectBusinessLine?.initial ?? []) {
      const info = RetouchApprovalBusinessConfigs[business].owners
        .map(it => Object.values(it))
        .flat()
        .flat();
      // TODO 受影响方向判断
      nodeUserEmails['08f790110c946298c6e12b5355c71433'] = [
        ...info,
        ...(nodeUserEmails['08f790110c946298c6e12b5355c71433'] ?? []),
      ];
    }

    // 子类型
    switch (req.hotfixProblemType) {
      case HotfixProblemType.Experience:
      case HotfixProblemType.ProductData:
        // 产品/体验类 获取业务方向的owner加到一级审批节点
        const pmPocEmails =
          req.businessPMOwner ??
          (RetouchApprovalBusinessConfigs?.[req?.business ?? '']?.owners ?? []).find(
            it => Object.keys(it)[0] === 'pmPOC',
          )?.pmPOC;
        if (pmPocEmails) {
          // TODO SOP问题标准判断
          nodeUserEmails.cb76074e7498823b2ce069e42e811aa7 = [
            ...(nodeUserEmails.cb76074e7498823b2ce069e42e811aa7 ?? []),
            ...pmPocEmails,
          ];
        }
        break;
      default:
        break;
    }

    // 拉取用户信息
    for (const key of Object.keys(nodeUserEmails)) {
      const userInfos = await batchGetUserInfo({
        data: {
          emails: uniq(nodeUserEmails[key] ?? []),
        },
      });
      nodeUserIds[key] = (nodeUserIds[key] ?? []).concat(
        userInfos?.map(it => it?.user_id)?.filter(it => it !== undefined) ?? [],
      );
    }

    this.logger.info(
      `[createRetouchFixVersionApprovalOrder] creating approval from lark, approval_code=${approvalCode}, openIds => ${JSON.stringify(nodeUserIds)}`,
    );
    const result = await this.larkClient.approval.instance.create({
      data: {
        approval_code: approvalCode,
        user_id: userInfo?.user_id,
        form: JSON.stringify(formInfo),
        node_approver_user_id_list: Object.keys(nodeUserIds).map(key => ({ key, value: uniq(nodeUserIds[key]) })),
        node_cc_user_id_list: [],
      },
    });
    this.logger.info(
      `[createRetouchFixVersionApprovalOrder] approvalCode: ${approvalCode} result: ${JSON.stringify(result)}`,
    );
    const instanceCode = result?.data?.instance_code;
    if (!instanceCode) {
      return;
    }
    // 上报
    teaCollect(TeaEvent.APPROVAL, {
      approval_type: ApprovalType.FixedVersionRequirements,
      approval_event: ApprovalHistoryEvent.CREATE_APPROVAL,
      isDev: process.env.NODE_ENV === 'development',
    });
    // 拉群评估通知
    const chatResult = await this.larkService.createLarkGroup(
      {
        user_id_type: UserIdType.openId,
        set_bot_manager: false,
      },
      {
        description: '',
        name: `【小版本评估&申请】${productName}-${versionUtils.appIdToAppInfo(appId.toString())?.platform}-${req?.desireVersion}-${req.approvalName}`,
        owner_id: userInfo?.open_id ?? '',
        user_id_list: [],
      },
    );
    const chatGroupId = chatResult?.chat_id;
    if (!chatGroupId) {
      return;
    }
    this.logger.info(
      `[createRetouchFixVersionApprovalOrder] approvalCode: ${approvalCode} chatGroupId: ${chatGroupId} chatResult: ${JSON.stringify(chatResult)}`,
    );
    // 审批记录存储到数据库中
    await this.approvalDbService.createOneVersionApproval(
      {
        ...req,
        reviewChatGroup: chatGroupId,
      },
      {
        instanceCode,
        approvalCode,
        approvalType: ApprovalType.FixedVersionRequirements,
        createUserEmail: userEmail,
        approvalStatus: ApprovalOrderStatus.New,
        history: [
          {
            timeStamp: dayjs().unix(),
            event: ApprovalHistoryEvent.CREATE_APPROVAL, // 事件
            message: '创建审批流工单',
          },
        ],
        chatId: chatResult?.chat_id,
      },
    );
    return instanceCode;
  }

  // 需求上车通知
  private async firstNodeUserIds(appIds: number[]) {
    const ccConfig: { [key: number]: string[] } = {
      251502: ['<EMAIL>', '<EMAIL>', '<EMAIL>'],
      251501: ['<EMAIL>', '<EMAIL>', '<EMAIL>'],
      2020093924: ['<EMAIL>', '<EMAIL>', '<EMAIL>'],
      2020093988: ['<EMAIL>', '<EMAIL>', '<EMAIL>'],
      2020092383: [
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
      ],
      2020092892: [
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
      ],
      35928901: [
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
      ],
      35928902: [
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
      ],
    };
    const emails = appIds?.reduce(
      (previousValue, currentValue, currentIndex, array) =>
        previousValue.concat(ccConfig?.[currentValue] ?? []).concat(ccConfig?.[currentValue] ?? []),
      [] as string[],
    );
    const userInfos = await batchGetUserInfo({
      data: {
        emails: uniq(emails ?? []),
      },
    });
    return userInfos?.map(it => it?.user_id)?.filter(it => it !== undefined);
  }
  private async ticketCCUserIds(appIds: number[]) {
    const ccConfig: { [key: number]: string[] } = {
      177502: [
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
      ],
      177501: [
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
      ],
      300601: [
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
      ],
      300602: [
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
      ],
      251502: ['<EMAIL>', '<EMAIL>', '<EMAIL>'],
      251501: ['<EMAIL>', '<EMAIL>', '<EMAIL>'],
      2020093924: ['<EMAIL>', '<EMAIL>'],
      2020093988: ['<EMAIL>', '<EMAIL>'],
      2020092383: [
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
      ],
      2020092892: [
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
      ],
      35928901: [
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
      ],
      35928902: [
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
      ],
    };
    const emails = appIds?.reduce(
      (previousValue, currentValue, currentIndex, array) =>
        previousValue.concat(ccConfig?.[currentValue] ?? []).concat(ccConfig?.[currentValue] ?? []),
      [] as string[],
    );
    const userInfos = await batchGetUserInfo({
      data: {
        emails: uniq(emails ?? []),
      },
    });
    return userInfos?.map(it => it?.user_id)?.filter(it => it !== undefined);
  }

  private async getUserIdsByEmails(emails?: string[]) {
    const userInfos = await batchGetUserInfo({
      data: {
        emails: uniq(emails ?? []),
      },
    });
    return userInfos?.map(it => it?.user_id)?.filter(it => it !== undefined);
  }

  private async finalNodeUserIds(appIds: number[]) {
    const ccConfig: { [key: number]: string[] } = {
      177502: ['<EMAIL>', '<EMAIL>'],
      177501: ['<EMAIL>'],
      300601: ['<EMAIL>'],
      300602: ['<EMAIL>', '<EMAIL>'],
      2020092383: ['<EMAIL>', '<EMAIL>'],
      2020092892: ['<EMAIL>', '<EMAIL>'],
      35928901: ['<EMAIL>', '<EMAIL>'],
      35928902: ['<EMAIL>', '<EMAIL>'],
      251501: ['<EMAIL>'],
      251502: ['<EMAIL>'],
      2020093988: ['<EMAIL>'],
      2020093924: ['<EMAIL>'],
    };
    const emails = appIds?.reduce(
      (previousValue, currentValue, currentIndex, array) =>
        previousValue.concat(ccConfig?.[currentValue] ?? []).concat(ccConfig?.[currentValue] ?? []),
      [] as string[],
    );
    const userInfos = await batchGetUserInfo({
      data: {
        emails: uniq(emails ?? []),
      },
    });
    return userInfos?.map(it => it?.user_id)?.filter(it => it !== undefined);
  }
  async createRetouchApprovalTicketOrder(req: CreateTicketApprovalReq) {
    const approvalCode = '054352F0-AD67-4DF3-B9BD-C959D6F24837';
    const userEmail = await useInject(UserService).queryLoginEmail();
    const userInfo = await this.larkService.getUserIdByEmail(userEmail);
    const approvalConfig = await this.larkService.searchApproval(approvalCode);
    if (approvalConfig) {
      const form: ApprovalForm[] = JSON.parse(approvalConfig.form);
      const requirementName = form.find(value => value.name === '需求名称');
      if (requirementName) {
        this.logger.info(
          `[createRetouchApprovalTicketOrder]:approval_code=${approvalCode}, form: ${approvalConfig.form}`,
        );
        const appId = req.extraInfo?.appId;
        const version = req.extraInfo?.version;

        const productName = appId ? versionUtils.appIdToAppInfo(appId.toString())?.productName : '未知';
        const platformName = appId ? versionUtils.appIdToAppInfo(appId.toString())?.platform?.toString() : '未知';
        const formInfo = [
          {
            id: 'widget17515341789850001', // 需求名称
            type: 'input',
            value: req.meegoLinks[0].link.name,
          },
          {
            id: 'widget17515341879520001', // 产品名
            type: 'input',
            value: productName,
          },
          {
            id: 'widget17515344107640001', // 平台
            type: 'input',
            value: platformName,
          },
          {
            id: 'widget17515344030270001', // 版本
            type: 'input',
            value: version ?? '未知',
          },
          {
            id: 'widget17515344181070001', // bits单链接
            type: 'input',
            value: req?.bitsLink?.url,
          },
          {
            id: 'widget17515344327460001', // meego单链接
            type: 'input',
            value: req.meegoLinks[0].link.url,
          },
          {
            id: 'widget17515344456920001', // 强制上车理由
            type: 'textarea',
            value: req.forceOnboardingReason,
          },
          {
            id: 'widget17515344732500001', // 未达标的项目
            type: 'textarea',
            value: req.unqualifiedProjects,
          },
          {
            id: 'widget17515345072340001', // 未达标的原因
            type: 'textarea',
            value: req.unmetCriteriaReason,
          },
          {
            id: 'widget17515346525370001', // 是否在16点前发起申请
            type: 'radioV2',
            value: req.afterDDL
              ? 'mcn6ibga-xqffh2tqwnm-0' // 有封版时间，且超过了封版日下午16点
              : 'mcn6ibga-ro2x0p8q2ls-0', // 未超过16点
          },
          {
            id: 'widget17515346898870001', // 未在16点前发起申请的原因
            type: 'textarea',
            value: req.afterDDLReason ?? '-',
          },
          {
            id: 'widget17515345309220001', // 后续改进计划
            type: 'textarea',
            value: req.improvementPlan,
          },
          {
            id: 'widget17515345447000001', // 准入条件
            type: 'fieldList',
            value: [
              [
                {
                  id: 'widget17515345566390001', // meego状态
                  type: 'radioV2',
                  value:
                    req.meegoLinks[0]?.meegoStatus === MeegoWorkFlowNodeStatus.pass
                      ? 'mcn6g9gf-p0y1ldvmars-0'
                      : 'mcn6g9gf-n1tay57dg3-0', // 未集成，// 已集成 value: 'lz9ku1gm-ratrrdet6s-1',
                },
              ],
              [
                {
                  id: 'widget17515345626900001', // bug解决率(%)
                  type: 'number',
                  value: req.meegoLinks[0].fixRate, // 未集成，// 已集成 value: 'lz9ku1gm-ratrrdet6s-1',
                },
              ],
              req?.pipelineInfo
                ? [
                    {
                      id: 'widget17515346112410001', // Pipeline状态
                      type: 'radioV2',
                      value:
                        req?.pipelineInfo?.pipelineStatus === PipelineStatus.success
                          ? 'mcn6hfl5-btmb5knq8cj-0'
                          : 'mcn6hfl5-xaor71aoqw8-0', // 通过，// 未通过 value: 'lz9kwc26-lf5txy9qth-0',
                    },
                  ]
                : [],
            ],
          },
        ];
        const larkClient = useInject(LarkClient);
        this.logger.info(
          `[createRetouchApprovalTicketOrder] creating approval from lark, approval_code=${approvalCode}`,
        );
        // 醒图新流程，拉TCC配置获取审批流程
        const configResult = await this.tcc.keys.get(`${appId}_ticket_requirements_config`);
        const appApprovalProcessConfig = JSON.parse(configResult) as ApprovalProcessConfig;
        this.logger.info(
          `[createRetouchApprovalTicketOrder] pull ApprovalProcessConfig=${JSON.stringify(appApprovalProcessConfig)}`,
        );
        const nodeUserIds: { [key: string]: string[] } = {};
        for (const item of appApprovalProcessConfig?.items?.default) {
          const userInfos = await batchGetUserInfo({
            data: {
              emails: uniq(item.users ?? []),
            },
          });
          nodeUserIds[item.key] = userInfos?.map(it => it?.user_id)?.filter(it => it !== undefined) ?? [];
        }
        this.logger.info(
          `[createRetouchApprovalTicketOrder] creating approval from lark, approval_code=${approvalCode}, openIds => ${JSON.stringify(nodeUserIds)}`,
        );
        const result = await larkClient.approval.instance.create({
          data: {
            approval_code: approvalCode,
            user_id: userInfo?.user_id,
            node_approver_user_id_list: Object.keys(nodeUserIds).map(key => ({ key, value: nodeUserIds[key] })),
            node_cc_user_id_list: [],
            form: JSON.stringify(formInfo),
          },
        });
        this.logger.info(
          `[createRetouchApprovalTicketOrder] creat approval from lark successfully, approval_code=${approvalCode}, result=${JSON.stringify(result)}`,
        );
        const chatResult = await this.larkService.createLarkGroup(
          {
            user_id_type: UserIdType.openId,
            set_bot_manager: false,
          },
          {
            description: '',
            name: `【异常需求上车评估&申请】${productName}-${platformName}-${version}-${req.meegoLinks[0].link.name}`,
            owner_id: userInfo?.open_id ?? '',
            user_id_list: [],
          },
        );
        if (result) {
          teaCollect(TeaEvent.APPROVAL, {
            approval_type: ApprovalType.TicketsRequirements,
            approval_event: ApprovalHistoryEvent.CREATE_APPROVAL,
            version: req?.extraInfo?.version,
            isDev: process.env.NODE_ENV === 'development',
          });
          // 审批记录存储到数据库中
          const instanceCode = result?.data?.instance_code;
          await this.approvalDbService.createOneTicketApproval(req, {
            instanceCode,
            approvalType: ApprovalType.TicketsRequirements,
            approvalCode,
            createUserEmail: userEmail,
            history: [
              {
                timeStamp: dayjs().unix(),
                event: ApprovalHistoryEvent.CREATE_APPROVAL, // 事件
                message: '创建审批流工单',
              },
            ],
            chatId: chatResult?.chat_id,
          });
          // const approvalRecord = await ApprovalInfoTableModel.findOne({
          //   instanceCode,
          //   approvalCode,
          // })
          //   .lean()
          //   .exec();
          // TODO 本人审批通过后再推送审批人通知
          // if (approvalRecord) {
          //   const approvalService = useInject(ApprovalService);
          //   const sendResult = await approvalService.ticketApprovalOnAssessment(approvalRecord, chatResult.chat_id);
          //   this.logger.info(
          //     `[createApprovalHotfixOrder] creat approval from lark successfully, approval_code=${approvalCode}, sendResult=${JSON.stringify(sendResult)}, instance_code=${instanceCode}`,
          //   );
          // }
          return instanceCode;
        }
      }
    }
  }

  async createApprovalTicketOrder(req: CreateTicketApprovalReq) {
    const approvalCode = '6DDDE439-2F6E-48D9-8FDA-65771A37E8D3';
    const userEmail = await useInject(UserService).queryLoginEmail();
    const userInfo = await this.larkService.getUserIdByEmail(userEmail);
    const approvalConfig = await this.larkService.searchApproval(approvalCode);
    if (approvalConfig) {
      const form: ApprovalForm[] = JSON.parse(approvalConfig.form);
      const requirementName = form.find(value => value.name === '需求名称');
      if (requirementName) {
        this.logger.info(`[createApprovalOrder]:approval_code=${approvalCode}, form: ${approvalConfig.form}`);
        // const inBusinessValue = requirementName.option?.find(value => value.text === myBusiness)?.value;
        // const outBusinessValue = outBusinessForm.option?.find(value => value.text === data.business)?.value;
        // if (inBusinessValue && outBusinessValue) {
        const appId = req.extraInfo?.appId;
        const version = req.extraInfo?.version;
        const productName = appId ? versionUtils.appIdToAppInfo(appId.toString())?.productName : '未知';
        const platformName = appId ? versionUtils.appIdToAppInfo(appId.toString())?.platform?.toString() : '未知';
        const formInfo = [
          {
            id: 'widget17224142894890001', // 需求名称
            type: 'input',
            value: req.meegoLinks[0].link.name,
          },
          {
            id: 'widget17478078656340001', // 产品名
            type: 'input',
            value: productName,
          },
          {
            id: 'widget17512824490700001', // 平台
            type: 'input',
            value: platformName,
          },
          {
            id: 'widget17478087765340001', // 版本
            type: 'input',
            value: version ?? '未知',
          },
          {
            id: 'widget17278626338770001', // bits单链接
            type: 'input',
            value: req?.bitsLink?.url,
          },
          {
            id: 'widget17224139626880001', // meego单链接
            type: 'input',
            value: req.meegoLinks[0].link.url,
          },
          {
            id: 'widget17224142675710001', // 强制上车理由
            type: 'textarea',
            value: req.forceOnboardingReason,
          },
          {
            id: 'widget17224143162010001', // 准入条件
            type: 'fieldList',
            value: [
              [
                {
                  id: 'widget17224140045370001', // meego状态
                  type: 'radioV2',
                  value:
                    req.meegoLinks[0]?.meegoStatus === MeegoWorkFlowNodeStatus.pass
                      ? 'lz9ku1gm-ratrrdet6s-1'
                      : '$i18n-lz9k5wci-lr9ccbj6s2n-14', // 未集成，// 已集成 value: 'lz9ku1gm-ratrrdet6s-1',
                },
              ],
              [
                {
                  id: 'widget17224142261370001', // bug解决率(%)
                  type: 'number',
                  value: req.meegoLinks[0].fixRate, // 未集成，// 已集成 value: 'lz9ku1gm-ratrrdet6s-1',
                },
              ],
              req?.pipelineInfo
                ? [
                    {
                      id: 'widget17224141243020001', // Pipeline状态
                      type: 'radioV2',
                      value:
                        req?.pipelineInfo?.pipelineStatus === PipelineStatus.success
                          ? 'lz9kwc26-pit5lux2pf-0'
                          : 'lz9kwc26-lf5txy9qth-0', // 通过，// 未通过 value: 'lz9kwc26-lf5txy9qth-0',
                    },
                  ]
                : [],
            ],
          },
          {
            id: 'widget17224144971670001', // 未达标的理由
            type: 'textarea',
            value: req.unmetCriteriaReason,
          },
          {
            id: 'widget17224145065200001', // 后续改进计划
            type: 'textarea',
            value: req.improvementPlan,
          },
        ];
        const larkClient = useInject(LarkClient);
        this.logger.info(`[createApprovalOrder] creating approval from lark, approval_code=${approvalCode}`);
        // 拉取RD BM信息
        const firstNodeOpenIds: string[] = [];
        if (appId && version) {
          const appIds = this.getRelationAppIds(appId);
          for (const _appId of appIds) {
            const _openIds = await this.fetchBMsOpenIds(_appId, version);
            if (_openIds) {
              firstNodeOpenIds.push(..._openIds);
            }
          }
          const _firstNodeUserIds = await this.firstNodeUserIds([appId]);
          if (_firstNodeUserIds) {
            firstNodeOpenIds.push(..._firstNodeUserIds);
          }
        }
        // 抄送用户
        const ccOpenIds: string[] = [];
        const finalOpenIds: string[] = [];
        if (appId) {
          // 抄送节点
          const _openIds = await this.ticketCCUserIds([appId]);
          if (_openIds) {
            ccOpenIds.push(..._openIds);
          }
          // 最终节点
          const finalOpenId = await this.finalNodeUserIds([appId]);
          if (finalOpenId) {
            finalOpenIds.push(...finalOpenId);
          }
        }

        this.logger.info(
          `[createApprovalOrder] creating approval from lark, approval_code=${approvalCode}, openIds => ${JSON.stringify(firstNodeOpenIds)}`,
        );

        const result = await larkClient.approval.instance.create({
          data: {
            approval_code: approvalCode,
            user_id: userInfo?.user_id,
            ...(firstNodeOpenIds && firstNodeOpenIds.length > 0
              ? {
                  node_approver_user_id_list: [
                    { key: '1e5bb5bb97d3d36b41d02db64d860f1e', value: firstNodeOpenIds },
                    { key: 'ed106945a0ac26ed56853875839f6367', value: finalOpenIds },
                  ],
                }
              : {}),
            node_cc_user_id_list: [{ key: 'ed106945a0ac26ed56853875839f6367', value: ccOpenIds }],
            form: JSON.stringify(formInfo),
          },
        });
        this.logger.info(`[createApprovalOrder] creat approval from lark successfully, approval_code=${approvalCode}`);
        if (result) {
          teaCollect(TeaEvent.APPROVAL, {
            approval_type: ApprovalType.TicketsRequirements,
            approval_event: ApprovalHistoryEvent.CREATE_APPROVAL,
            version: req?.extraInfo?.version,
            isDev: process.env.NODE_ENV === 'development',
          });
          // 审批记录存储到数据库中
          await this.approvalDbService.createOneTicketApproval(req, {
            instanceCode: result.data?.instance_code,
            approvalType: ApprovalType.TicketsRequirements,
            approvalCode,
            createUserEmail: userEmail,
            history: [
              {
                timeStamp: dayjs().unix(),
                event: ApprovalHistoryEvent.CREATE_APPROVAL, // 事件
                message: '创建审批流工单',
              },
            ],
          });
          // TODO '审批发起成功，请前往飞书跟进审批进度！'
        }
        return result.data?.instance_code;
      }
    }

    return undefined;
  }

  getRelationAppIds(appId: number) {
    if ([177501, 177502].includes(appId)) {
      return [177501, 177502];
    }
    if ([300601, 300602].includes(appId)) {
      return [300601, 300602];
    }
    return [];
  }

  async fetchQaBmEmails(appId: number, version: string) {
    // 拉取QA BM信息
    const emails: string[] = [];
    const bmRsp = await fetchBmInfo({
      data: {
        appId,
        version,
      },
    });
    this.logger.info(`[fetchQaBmEmails] appId => ${appId}, version => ${version}`);
    const qaBmEmails = bmRsp?.data?.filter(it => it.type === BmType.qa).map(bm => bm.email);
    if (qaBmEmails) {
      emails.push(...qaBmEmails);
    }
    return emails;
  }

  async fetchQaOpenIds(appId: number, version: string) {
    // 拉取QA BM信息
    const emails: string[] = [];
    const bmRsp = await fetchBmInfo({
      data: {
        appId,
        version,
      },
    });
    this.logger.info(`[fetchQaBmEmails] appId => ${appId}, version => ${version}`);
    const qaBmEmails = bmRsp?.data
      ?.filter(it => it.type === BmType.qa)
      .map(bm => bm.userId)
      ?.filter(it => it !== null);
    if (qaBmEmails) {
      emails.push(...qaBmEmails);
    }
    return emails;
  }

  async fetchBMsOpenIds(appId: number, version: string) {
    // 分工变动，BM
    const config: { [key: number]: string[] } = {
      177502: [
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
      ],
      177501: [
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
      ],
      300601: [
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
      ],
      300602: [
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
      ],
      251501: ['<EMAIL>'],
      251502: ['<EMAIL>'],
      2020093924: ['<EMAIL>'],
      2020093988: ['<EMAIL>'],
    };
    if (config[appId]) {
      const userIds: string[] = (await this.getUserIdsByEmails(config[appId])) ?? [];
      return userIds;
    }

    // 拉取RD BM信息
    const openIds: string[] = [];
    const bmRsp = await fetchBmInfo({
      data: {
        appId,
        version,
      },
    });
    this.logger.info(`[fetchBMsOpenIds] appId => ${appId}, version => ${version}`);
    const rdBms = bmRsp?.data?.filter(it => it.type === BmType.rd).map(bm => bm.userId);
    if (rdBms) {
      openIds.push(...rdBms);
    }
    return openIds;
  }
}
