import {
  Card,
  CardActionElement,
  CardActionValue,
  CardButtonAction,
  CardButtonType,
  CardCallbackType,
  CardColumnElement,
  CardColumnSetElement,
  CardContentElement,
  CardElement,
  CardElementTag,
  CardElementTagV2,
  CardHeader,
  CardInfo,
  CardMarkdownElement,
  CardNoteElement,
  CardTemplate,
  CardTextTag,
  LarkCardTemplate,
  LarkTemplateId,
  TitleTag,
} from '@pa/shared/dist/src/lark/larkCard';
import { Version } from '@shared/versionBot/version';
import { ConflictFile, MrInfo, MrReviewerInfo } from '@shared/bits/mrInfo';
import { groupBy, size, uniq } from 'lodash';
import {
  IssueInfo,
  MeegoStoryProgress,
  MrStatus,
  RdIssueDetail,
  UselessDocsInfo,
  UselessSettingMeegoInfo,
} from '@shared/meego/CustomData';
import { ModelIssueInfo } from '@shared/meego/WorkItemResult';
import { BuildCardInfo, BuildiOSCardInfo } from '@shared/customBuild/buildCardInfo';
import { BmInfo, BmType, Duty } from '@shared/bits/bmInfo';
import { HistoryBugResult } from '@shared/meego/HistoryBugProgress';
import {
  BuildMasterInfo,
  LVProductType,
  ProductType,
  VersionProcess,
  VersionState,
} from '@shared/process/versionProcess';
import { MAIN_HOST_HTTPS, PlatformType, User } from '@pa/shared/dist/src/core';
import { MajorIssuePushInfo } from '@shared/process/majorIssue';
import {
  AppId2Name,
  ExperimentCard,
  ExperimentShareInfo,
  HandleStatus,
  IndicatorType,
  Type2CardTitle,
} from '@shared/experiment/experimentInfo';
import { BitsResult } from '@shared/bits';
import { CheckListInfo, CheckListProgressItem, CheckListTaskType } from '@shared/versionBot/checklistinfo';
import { PkCheckResult } from '@shared/bits/packageSize';
import { Inject, Injectable } from '@gulux/gulux';
import { BytedLogger } from '@gulux/gulux/byted-logger';
import versionUtils from '../utils/versionUtils';
import timeUtil from '../utils/timeUtil';
import { MrSnapshot } from '@shared/process/versionSnapshot';
import { BusinessState, DutyInfo } from '@shared/oncall/businessOnCall';
import { add_suffix_ne } from '@pa/shared/dist/src/utils/tools';
import { StoryCheckResult } from './StoryDocChecker';
import { StoryBusVersionCheckResult } from './StoryBusVersionCheck';
import { globalBusinessType } from '../utils/business';
import commonUtils from '../utils/commonUtils';
import { CustomBuildType } from '@shared/customBuild';
import { ApprovalInfoTable } from '../model/approval/ApprovalInfoModel';
import {
  ApprovalBusinessConfigs,
  ApprovalDetail,
  ApprovalFixVersionConfig,
  ApprovalHotfixConfig,
  ApprovalOldBusinessConfigs,
  ApprovalType,
  HotfixProblemType,
  hotfixRunningTimeText,
  HotfixRunningTimeType,
  hotfixTypeText,
  MeegoWorkFlowNodeStatus,
  PipelineStatus,
  RequirementChangeActionType,
  RetouchApprovalBusinessConfigs,
} from '@shared/approval/ApprovalOrder';
import {
  addApprovalButton,
  addViewButton,
  atEmail,
  atEmails,
  convertToCustomFormat,
  line,
  mdLines,
  trimTrailingNewline,
} from './utils/ApprovalUtils';
import { LarkApprovalStatus } from '@pa/shared/dist/src/lark/approval';
import { useInject } from '@edenx/runtime/bff';
import { LarkClient } from '@gulux/gulux/lark';
import LarkService from '@pa/backend/dist/src/third/lark';
import { AppSettingId, BusinessType } from '@pa/shared/dist/src/appSettings/appSettings';
import { CozeWorkflowActionName, CozeWorkflowActionType } from '@shared/releasePlatform/cozeWorkflowContent';
import { VersionConfigKeys } from '@shared/aircraftConfiguration';
import { UserIdType } from '@pa/shared/dist/src/lark/userInfo';
import AirplaneConfigService from './AirplaneConfigService';
import { approvalDetailUrlMobile, approvalDetailUrlPC } from '@shared/approval/ApprovalLarkOrder';
import { VersionProcessInfo } from '@shared/releasePlatform/versionStage';
import { VersionFeatureInfo } from '@shared/releasePlatform/versionFeatureInfo';
import {
  CircuitBreakerCallbackActionType,
  CircuitBreakerTicket,
  CircuitBreakerTicketType,
  FeedbackCircuitBreakerTicket,
  SlardarCircuitBreakerTicket,
} from '@shared/circuitBreaker/circuitBreakerTicket';
import { dayjs } from '@pa/shared/dist/src/utils/dayjs';
import MeegoService, { faceuProjectKey } from './third/meego';
import { WorkflowNode } from '@shared/meego/WorkflowResult';
import * as string_decoder from 'node:string_decoder';

@Injectable()
export default class LarkCardService {
  @Inject()
  private logger: BytedLogger;
  @Inject()
  private lark: LarkService;

  /**
   * 直接构建MR的list
   * @param mrInfoList
   * @private
   */
  private static buildMrListElement(mrInfoList: MrInfo[]): CardElement {
    return {
      tag: CardElementTag.markdown,
      content: `${mrInfoList
        .map(
          value =>
            `**[${value.title}](${value.mr_detail_url})**&nbsp;&nbsp;&nbsp;Author：<at email=${value.author}@bytedance.com></at>`,
        )
        .join('\n')}`,
    } as CardMarkdownElement;
  }

  buildBaseCard(info: CardInfo): Card {
    return {
      config: {},
      header: this.buildHeader(info.title, info.template),
      elements: [],
    };
  }

  buildHeader(title: string, template: CardTemplate): CardHeader {
    return {
      title: {
        content: title,
        tag: TitleTag.plain_text,
      },
      template,
    };
  }

  buildMarkDownColorText(content: string, color: 'red' | 'green' | 'yellow' | 'orange'): string {
    return `<font color='${color}'>${content}</font>`;
  }

  buildMarkDownATText(email: string): string {
    return `<at email="${email}"></at>`;
  }

  buildMarkDownTag(content: string, color: 'red' | 'green' | 'yellow' | 'orange' | 'blue' | 'purple'): string {
    return `<text_tag color='${color}'>${content}</text_tag>`;
  }

  buildMarkDownCard(title: string, template: CardTemplate | undefined, content: string): Card {
    const baseCard = this.buildBaseCard({
      title,
      template: template ?? CardTemplate.orange,
    });
    baseCard.elements.push({
      tag: CardElementTag.markdown,
      content,
    } as CardMarkdownElement);
    return baseCard;
  }

  buildMrkDownCard(title: string, content: string): Card {
    const baseCard = this.buildBaseCard({
      title,
      template: CardTemplate.orange,
    });
    baseCard.elements.push({
      tag: CardElementTag.markdown,
      content,
    } as CardMarkdownElement);
    return baseCard;
  }

  async buildMeegoIssueRateCard(
    versionName: string,
    rate: string[],
    rateDetail: string,
    issueList: IssueInfo[],
    calendarInfo: string[],
    businessOwner = '',
  ): Promise<Card> {
    const issueGroupMap = groupBy(issueList, value => value.priority);
    const baseCard = this.buildBaseCard({
      title: `【${versionName}】的缺陷解决率为:${rate[0]}`,
      template: CardTemplate.green,
      config: { enable_forward: false },
    });
    const elements: CardElement[] = [];
    if (calendarInfo.length >= 6) {
      // 国内众测将于2023/9-19发布，打包时间为当天20:00，灰度准出要求：
      // P0/P1/S级bug无遗留，P2及以上解决率>=70%（整行标红）
      // 要求P0/P1/S级bug无遗留（无解决率要求）
      // RDBM:陈李冠,QABM:陈燕丽
      elements.push({
        tag: CardElementTag.div,
        text: {
          tag: CardTextTag.lark_md,
          // content: `**<font color='green'>${calendarInfo}</font>**`,
          content: `**${calendarInfo[0]}**将于**${calendarInfo[1]}**发布，打包时间为**${calendarInfo[2]}**`,
        },
      } as CardContentElement);
      const rateRequire = calendarInfo[3] === '0' ? `(无解决率要求)` : `，P3及以上解决率>=${calendarInfo[3]}%`;
      elements.push({
        tag: CardElementTag.div,
        text: {
          tag: CardTextTag.lark_md,
          content: `**灰度准出要求：<font color='red'>P0/P1/S级bug无遗留${rateRequire}</font>**`,
        },
      } as CardContentElement);
      elements.push({
        tag: CardElementTag.div,
        text: {
          tag: CardTextTag.lark_md,
          content: `RDBM:${calendarInfo[4]}，QABM:${calendarInfo[5]}`,
        },
      } as CardContentElement);
    }
    elements.push({ tag: CardElementTag.hr });
    elements.push({
      tag: CardElementTag.div,
      text: {
        tag: CardTextTag.lark_md,
        content: `**${rate[2]}**`,
      },
    } as CardContentElement);
    elements.push({
      tag: CardElementTag.div,
      text: {
        tag: CardTextTag.lark_md,
        content: `**当前未解决/重新打开的P0-P1-S级缺陷总计${rateDetail}个**`,
      },
    } as CardContentElement);
    if (calendarInfo.length >= 6 && calendarInfo[3] !== '0') {
      if (rate[1].length > 0) {
        elements.push({
          tag: CardElementTag.div,
          text: {
            tag: CardTextTag.lark_md,
            content: `**<font color='green'>${rate[1]}</font>**`,
          },
        } as CardContentElement);
      }
      const ownerContent = businessOwner.length > 0 ? `**<at email=${businessOwner}></at>** ` : '';
      if (rate[3].length > 0) {
        elements.push({
          tag: CardElementTag.div,
          text: {
            tag: CardTextTag.lark_md,
            content: `${ownerContent}**<font color='red'> ${rate[3]}，辛苦协调RD跟进处理 </font>**`,
          },
        } as CardContentElement);
      }
    }
    elements.push({ tag: CardElementTag.hr });
    for (const groupIssueKey in issueGroupMap) {
      const issueLists = issueGroupMap[groupIssueKey];
      elements.push({
        tag: CardElementTag.div,
        text: {
          tag: CardTextTag.lark_md,
          content: `**${groupIssueKey} 缺陷列表 (${size(issueLists)})**`,
        },
      } as CardContentElement);
      elements.push(this.buildIssueElement(issueLists));
      elements.push({ tag: CardElementTag.hr });
    }
    baseCard.elements = elements;
    return baseCard;
  }

  /**
   * 构建历史bug进度卡片
   * @param result
   * @returns
   */
  buildHistoryBugRateCard(result: HistoryBugResult): Card {
    const baseCard = this.buildBaseCard({
      title: `【${result.title}】当前进度为:${result.all.rate}%`,
      template: CardTemplate.green,
      config: { enable_forward: false },
    });
    const elements: CardElement[] = [];
    elements.push({
      tag: CardElementTag.div,
      text: {
        tag: CardTextTag.lark_md,
        content: `**缺陷总数${result.all.actual}个，目标${result.all.target}个，已解决/已关闭/拒绝修复共${result.all.current}个，[Meego缺陷视图](${result.fix_view})**`,
      },
    } as CardContentElement);
    elements.push({
      tag: CardElementTag.markdown,
      content: `缺陷总数不足3个，可自行从[条件视图](${result.condition_view})中选择缺陷添加到[固定视图](${result.fix_view})，操作方法查看[手册](https://bytedance.feishu.cn/wiki/wikcn6cj6BgslPfBWbuAyT5G1Fd)。\n每个版本专门投入人力修复的bug也需添加到[固定视图](${result.fix_view})中。\n如果本季度没有人力投入清理历史Bug，可联系**<at email=<EMAIL>></at>**豁免。`,
    } as CardMarkdownElement);
    elements.push({ tag: CardElementTag.hr });
    elements.push({
      tag: CardElementTag.markdown,
      content: `${result.users
        .map(user => {
          if (user.rateInfo.actual < user.rateInfo.target) {
            return `**<at email=${user.email}></at>** **缺陷总数不足${user.rateInfo.target}个** ，已完成${
              user.rateInfo.current
            }个，请补充${user.rateInfo.target - user.rateInfo.actual}个到[固定视图](${result.fix_view})中`;
          } else if (user.rateInfo.current < user.rateInfo.target) {
            return `**<at email=${user.email}></at>** **进度为:${user.rateInfo.rate}%** ，缺陷总数${user.rateInfo.actual}个，目标${user.rateInfo.target}个，已完成${user.rateInfo.current}个`;
          } else {
            return `**${user.name}** **进度为:${user.rateInfo.rate}%** ，缺陷总数${user.rateInfo.actual}个，目标${user.rateInfo.target}个，已完成${user.rateInfo.current}个`;
          }
        })
        .join('\n')}`,
    } as CardMarkdownElement);
    baseCard.elements = elements;
    return baseCard;
  }

  buildGrayIssueListCard(targetBranch: string, mrReviewerInfo: MrReviewerInfo[]) {
    const baseCard = this.buildBaseCard({
      title: `【${targetBranch}】未完成合入的 MR 总计 ${mrReviewerInfo.length} 个`,
      template: CardTemplate.green,
      config: { enable_forward: false },
    });
    const mrGroupMap = groupBy(mrReviewerInfo, value => value.mrInfo.type);
    const elements: CardElement[] = [];
    for (const groupIssueKey in mrGroupMap) {
      const mrList = mrGroupMap[groupIssueKey];
      elements.push({
        tag: CardElementTag.div,
        text: {
          tag: CardTextTag.lark_md,
          content: `**${groupIssueKey} MR列表 (${size(mrList)})**`,
        },
      } as CardContentElement);
      elements.push(this.buildMrElement(mrList));
      elements.push({ tag: CardElementTag.hr });
    }
    baseCard.elements = elements;
    return baseCard;
  }

  buildStoryRiskCard(title: string, meegoStory: MeegoStoryProgress): Card {
    const baseCard = this.buildBaseCard({
      title: `${title}`,
      template: CardTemplate.green,
      config: { enable_forward: false },
    });
    const elements: CardElement[] = [];
    elements.push({
      tag: CardElementTag.div,
      text: {
        tag: CardTextTag.lark_md,
        content: `[${meegoStory.name}](${meegoStory.link}) 当前修复率仅为：**${meegoStory.fixRate}%**`,
      },
    } as CardContentElement);
    baseCard.elements = elements;
    return baseCard;
  }

  buildStoryMrReadyCard(title: string, meegoStory: MeegoStoryProgress): Card {
    const baseCard = this.buildBaseCard({
      title: `${title}`,
      template: CardTemplate.green,
      config: { enable_forward: false },
    });
    const elements: CardElement[] = [];
    elements.push({
      tag: CardElementTag.div,
      text: {
        tag: CardTextTag.lark_md,
        content: `[${meegoStory.name}](${meegoStory.link}) 还未合入 [MR-Link](${meegoStory.mrLink})：**${meegoStory.mrMerged}**`,
      },
    } as CardContentElement);
    baseCard.elements = elements;
    return baseCard;
  }

  buildStoryWipMrCard(title: string, meegoStoryList: MeegoStoryProgress[]): Card {
    const baseCard = this.buildBaseCard({
      title: `${title}`,
      template: CardTemplate.green,
      config: { enable_forward: false },
    });
    const mrGroupMap = groupBy(meegoStoryList, value => value.mrMerged);
    const elements: CardElement[] = [];
    elements.push({
      tag: CardElementTag.div,
      text: {
        tag: CardTextTag.lark_md,
        content: `**需求确定延期的，在Meego单上修改「计划上车版本」\n无需客户端代码改动代码的需求，计划上车版本填写为「不跟版」\n检查MR的target branch是否为develop分支 \n关闭MR的WIP状态 \n发起代码评审**`,
      },
    } as CardContentElement);
    for (const groupIssueKey in mrGroupMap) {
      const mrList = mrGroupMap[groupIssueKey];
      elements.push({
        tag: CardElementTag.div,
        text: {
          tag: CardTextTag.lark_md,
          content: `**${groupIssueKey} MR列表 (${size(mrList)})**`,
        },
      } as CardContentElement);
      elements.push(this.buildStoryElement(mrList));
      elements.push({ tag: CardElementTag.hr });
    }
    baseCard.elements = elements;
    return baseCard;
  }

  buildStoryVeEffectMrCard(
    version: Version,
    meegoStoryList: MeegoStoryProgress[],
    msg: string,
    mrList: MrInfo[] = [],
  ): Card {
    const baseCard = this.buildBaseCard({
      title: `【${version.version}】 VE/EFFECT提前封板需求列表`,
      template: CardTemplate.red,
      config: { enable_forward: false },
    });
    const elements: CardElement[] = [];
    let bmAtInfo = '';
    version.bms.forEach(value => {
      bmAtInfo += `<at email="${value.name}@bytedance.com"></at>`;
    });
    this.logger.info(`version ${JSON.stringify(version)} atInfo ${bmAtInfo}`);
    elements.push({
      tag: CardElementTag.div,
      text: {
        tag: CardTextTag.lark_md,
        content: `**${msg}** ${bmAtInfo}`,
      },
    } as CardContentElement);
    elements.push({
      tag: CardElementTag.div,
      text: {
        tag: CardTextTag.lark_md,
        content: `**需求列表 (${size(meegoStoryList)})**`,
      },
    } as CardContentElement);
    if (mrList.length > 0) {
      elements.push(this.buildStoryElementWithMR(meegoStoryList, mrList));
    } else {
      elements.push(this.buildStoryElement(meegoStoryList));
    }
    baseCard.elements = elements;
    return baseCard;
  }

  // version : example "剪映-Android-9.1.0"
  buildGrayIssueModelCard(version: string, modelInfo: ModelIssueInfo[], autoTestP0P1Count: number): Card {
    const baseCard = this.buildBaseCard({
      title: `【${version}】分业务模块缺陷解决率`,
      template: CardTemplate.green,
      config: { enable_forward: false },
    });
    this.logger.info(`${modelInfo}`);
    const elements: CardElement[] = [];
    elements.push(this.buildModelElement(version, modelInfo, autoTestP0P1Count));
    baseCard.elements = elements;
    return baseCard;
  }

  buildClipIssueModelCard(version: string, modelInfo: ModelIssueInfo[]): Card {
    const baseCard = this.buildBaseCard({
      title: `【${version}】工具方向：细分模块缺陷解决率`,
      template: CardTemplate.green,
      config: { enable_forward: false },
    });
    this.logger.info(`${modelInfo}`);
    const elements: CardElement[] = [];
    elements.push(this.buildModelElement(version, modelInfo, 0));
    baseCard.elements = elements;
    return baseCard;
  }

  buildGrayPublishCard(title: string) {
    const baseCard = this.buildBaseCard({
      title: `${title}`,
      template: CardTemplate.green,
      config: { enable_forward: false },
    });
    return baseCard;
  }

  getLevelByRole(role: string) {
    if (role === 'QA') {
      return 0;
    } else if (role === 'RD') {
      return 1;
    } else {
      return 2;
    }
  }

  pad(number: number): string {
    return number < 10 ? `0${number}` : `${number}`;
  }

  createPackageSuccessCard(info: BuildCardInfo): Card {
    const baseCard = this.buildBaseCard({
      title: info.title,
      template: CardTemplate.green,
    });
    const baseInfo: string[] = [`**branch:**\t\t\t\t${info.branch}`];
    if (info.commit) {
      baseInfo.push(`**commit sha**\t\t\t${info.commit}`);
    }
    baseInfo.push('');
    if (info.params.is32) {
      baseInfo.push(`**架构**\t\t\t\t\t32位`);
    } else {
      baseInfo.push(`**架构**\t\t\t\t\t64位`);
    }
    if (info.params.isDebug) {
      baseInfo.push(`**类型**\t\t\t\t\tdebug`);
    } else {
      baseInfo.push(`**类型**\t\t\t\t\trelease`);
    }
    if (info.params.isHyperTest) {
      baseInfo.push(`**HyperTest**\t\t\tyes`);
    }
    if (info.params.isCoverage) {
      baseInfo.push(`**覆盖率**\t\t\t\tyes`);
    }
    if (info.params.veVersion) {
      baseInfo.push(`**VE版本**\t\t\t\t${info.params.veVersion}`);
    }
    if (info.params.effectVersion) {
      baseInfo.push(`**Effect版本**\t\t\t${info.params.effectVersion}`);
    }
    if (info.params.effectVersion) {
      baseInfo.push(`**Effect版本**\t\t\t${info.params.effectVersion}`);
    }
    if (
      Array.isArray(info.params.type) &&
      info.params.type.includes(CustomBuildType.QA_INTEGRATION_TESTING) &&
      info.params.type.includes(CustomBuildType.MR_MERGED)
    ) {
      if (info.createdAt) {
        // const year = info.createdAt.getFullYear();
        // const month = this.pad(info.createdAt.getMonth() + 1); // JavaScript月份是从0开始的，所以加1
        // const day = this.pad(info.createdAt.getDate());
        // const hours = this.pad(info.createdAt.getHours());
        // const minutes = this.pad(info.createdAt.getMinutes());
        // const seconds = this.pad(info.createdAt.getSeconds());
        // const formattedTime = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
        baseInfo.push(`**打包时间**\t\t\t\t${info.createdAt}`);
      }
      if (info.notIntegrationMr && info.notIntegrationMr.length > 0) {
        baseInfo.push(`**已合入未集成的MR：**\n${info.notIntegrationMr.map(mr => mr.meegoLink).join('\n')}`);
      }
    }

    baseCard.elements.push({
      tag: CardElementTag.markdown,
      content: baseInfo.join('\n'),
    } as CardMarkdownElement);
    baseCard.elements.push({
      tag: CardElementTag.hr,
    });
    baseCard.elements.push({
      tag: CardElementTag.div,
      fields: [
        {
          is_short: true,
          text: {
            tag: CardTextTag.lark_md,
            content: info.mappingUrl ? `[mapping下载地址](${info.mappingUrl})` : '当前构建无 mapping',
          },
        },
      ],
      extra: {
        tag: CardElementTag.img,
        img_key: info.apkImgKey,
        alt: {
          tag: CardTextTag.plain_text,
          content: 'APK',
        },
      },
    } as CardContentElement);
    baseCard.elements.push({
      tag: CardElementTag.action,
      actions: [
        {
          tag: CardElementTag.button,
          url: info.apkUrl,
          text: {
            tag: CardTextTag.plain_text,
            content: `download ${info.apkName}`,
          },
          type: CardButtonType.primary,
        } as CardButtonAction,
      ],
    } as CardActionElement);
    this.logger.debug(`构建结果卡片:${JSON.stringify(baseCard)}`);
    return baseCard;
  }

  createPackageSuccessIOSCard(info: BuildiOSCardInfo): Card {
    const baseCard = this.buildBaseCard({
      title: info.title,
      template: CardTemplate.green,
    });
    const baseInfo: string[] = [`**branch:**\t\t\t\t${info.branch}`];
    if (info.commit) {
      baseInfo.push(`**commit sha**\t\t\t${info.commit}`);
    }
    if (info.MrTitle && info.MrUrl) {
      baseInfo.push(`**Mr title**\t\t\t\t[${info.MrTitle}](${info.MrUrl})`);
    }
    if (info.MrOwner) {
      baseInfo.push(`**Mr Owner**\t\t\t**<at email=${info.MrOwner}@bytedance.com></at>**`);
    }
    // if(info.pa)
    baseInfo.push('');
    if (info.params.isDebug) {
      baseInfo.push(`**类型**\t\t\t\t\tdebug`);
    } else {
      baseInfo.push(`**类型**\t\t\t\t\trelease`);
    }
    if (info.params.isHyperTest) {
      baseInfo.push(`**HyperTest**\t\t\tyes`);
    }
    if (info.params.isCoverage) {
      baseInfo.push(`**覆盖率**\t\t\t\tyes`);
    }
    if (info.params.veVersion) {
      baseInfo.push(`**VE版本**\t\t\t\t${info.params.veVersion}`);
    }
    if (info.params.effectVersion) {
      baseInfo.push(`**Effect版本**\t\t\t${info.params.effectVersion}`);
    }
    if (info.params.effectVersion) {
      baseInfo.push(`**Effect版本**\t\t\t${info.params.effectVersion}`);
    }
    if (
      Array.isArray(info.params.type) &&
      info.params.type.includes(CustomBuildType.QA_INTEGRATION_TESTING) &&
      info.params.type.includes(CustomBuildType.MR_MERGED)
    ) {
      if (info.createdAt) {
        // const year = info.createdAt.getFullYear();
        // const month = this.pad(info.createdAt.getMonth() + 1); // JavaScript月份是从0开始的，所以加1
        // const day = this.pad(info.createdAt.getDate());
        // const hours = this.pad(info.createdAt.getHours());
        // const minutes = this.pad(info.createdAt.getMinutes());
        // const seconds = this.pad(info.createdAt.getSeconds());
        // const formattedTime = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
        baseInfo.push(`**打包时间**\t\t\t\t${info.createdAt}`);
      }
      if (info.notIntegrationMr && info.notIntegrationMr.length > 0) {
        baseInfo.push(`**已合入未集成的MR：**\n${info.notIntegrationMr.map(mr => mr.meegoLink).join('\n')}`);
      }
    }

    baseCard.elements.push({
      tag: CardElementTag.markdown,
      content: baseInfo.join('\n'),
    } as CardMarkdownElement);
    baseCard.elements.push({
      tag: CardElementTag.hr,
    });
    baseCard.elements.push({
      tag: CardElementTag.div,
      fields: [
        {
          is_short: true,
          text: {
            tag: CardTextTag.lark_md,
            content: '',
          },
        },
      ],
      extra: {
        tag: CardElementTag.img,
        img_key: info.iOS_INSTALLImgKey,
        alt: {
          tag: CardTextTag.plain_text,
          content: 'iOS_INSTALL',
        },
      },
    } as CardContentElement);
    baseCard.elements.push({
      tag: CardElementTag.action,
      actions: [
        {
          tag: CardElementTag.button,
          url: info.ipaUrl,
          text: {
            tag: CardTextTag.plain_text,
            content: `download ${info.iOS_INSTALLName}`,
          },
          type: CardButtonType.primary,
        } as CardButtonAction,
      ],
    } as CardActionElement);
    this.logger.debug(`构建结果卡片:${JSON.stringify(baseCard)}`);
    return baseCard;
  }

  buildSimpleBuildSuccessCard(title: string, text: string, url?: string, actionText?: string) {
    const baseCard = this.buildBaseCard({
      title,
      template: CardTemplate.green,
    });
    baseCard.elements.push({
      tag: CardElementTag.div,
      text: {
        tag: CardTextTag.lark_md,
        content: text,
      },
      extra:
        url && actionText
          ? ({
              tag: CardElementTag.button,
              text: {
                tag: CardTextTag.plain_text,
                content: `${actionText}`,
              },
              type: CardButtonType.default,
              url,
            } as CardButtonAction)
          : undefined,
    } as CardContentElement);
    return baseCard;
  }

  buildSimpleActionCard(text: string, url?: string, actionText?: string) {
    const baseCard = {
      config: {
        wide_screen_mode: true,
      },
      elements: [],
    } as Card;
    baseCard.elements.push({
      tag: CardElementTag.div,
      text: {
        tag: CardTextTag.lark_md,
        content: text,
      },
      extra:
        url && actionText
          ? ({
              tag: CardElementTag.button,
              text: {
                tag: CardTextTag.plain_text,
                content: `${actionText}`,
              },
              type: CardButtonType.default,
              url,
            } as CardButtonAction)
          : undefined,
    } as CardContentElement);
    return baseCard;
  }

  // return {
  //       tag: CardElementTag.markdown,
  //       content: `${issueList.map(value =>
  //         `**${value.mrInfo.type}**&nbsp;**[${value.mrInfo.title}](${value.mrInfo.mr_detail_url})**&nbsp;&nbsp;&nbsp; owner:**<at email=${value.mrInfo.author}@bytedance.com></at>**`).join('\n')
  //       }`,

  buildXtShadowBranchCard(info: { repo: string; branch: string; commit: string; error?: string }): Card {
    const baseCard = this.buildBaseCard({
      title: info.error ? '❌  shadow branch 创建或更新失败' : '✅ shadow branch 创建成功',
      template: info.error ? CardTemplate.red : CardTemplate.green,
    });
    baseCard.elements.push({
      tag: CardElementTag.markdown,
      content:
        `**Repo:** \t\t\t\t${info.repo}\n` +
        `**Branch:** \t\t\t\t${info.branch}\n` +
        `**Commit:**\t\t\t\t${info.commit}`,
    } as CardMarkdownElement);
    if (info.error) {
      baseCard.elements.push({ tag: CardElementTag.hr });
      baseCard.elements.push({
        tag: CardElementTag.markdown,
        content: `**错误日志:**\n${info.error}`,
      } as CardMarkdownElement);
    }
    return baseCard;
  }

  buildStoryElement(meegoStoryList: MeegoStoryProgress[]): CardElement {
    const contentJson = `${meegoStoryList
      .map(value => {
        if (value.clientrd) {
          return `[${value.name.substring(0, 30)}](${value.link}) RDowner:<at email="${value.clientrd.email}"></at>`;
        } else {
          return `[${value.name.substring(0, 30)}](${value.link}) **RDowner未设置**`;
        }
      })
      .join('\n')}`;
    return {
      tag: CardElementTag.markdown,
      content: contentJson,
    } as CardMarkdownElement;
  }

  buildStoryElementWithMR(meegoStoryList: MeegoStoryProgress[], mrList: MrInfo[]): CardElement {
    const contentJson = `${meegoStoryList
      .map(value => {
        const mrMerged = value.mrMerged === MrStatus.MERGED || value.mrMerged === true;
        const rdOwner = value.clientrd ? `RDowner:<at email="${value.clientrd?.email}"></at>` : '**RDowner未设置**';
        let mrInfo = '**MR不存在**';
        const mrItem = mrList.find(v => v.mr_detail_url === value.mrLink);
        if (mrItem) {
          mrInfo = `[MR](${mrItem.mr_detail_url}) Owner:<at email="${mrItem.author}@bytedance.com"></at>`;
          if (mrMerged) {
            mrInfo += ' 已合入';
          } else {
            mrInfo += ' **未合入**';
          }
        }
        const msg = `[${value.name.substring(0, 30)}](${value.link}) ${rdOwner} 【${mrInfo}】`;
        if (mrMerged) {
          return `~~${msg}~~`;
        }
        return msg;
      })
      .join('\n')}`;
    return {
      tag: CardElementTag.markdown,
      content: contentJson,
    } as CardMarkdownElement;
  }

  /**
   * 构建版本延期/提审卡片
   * @param title
   * @param description
   * @param coreEmail
   * @param tips
   * @param senderEmail
   */
  buildVersionInfoRiskCard(
    title: string,
    description: string,
    coreEmail: string[] | undefined,
    tips: string,
    senderEmail: string,
    fromairplane: boolean,
  ): Card {
    const baseCard = this.buildBaseCard({
      title: `${title}`,
      template: title.includes('延期') || title.includes('跳版') ? CardTemplate.red : CardTemplate.green,
      config: { enable_forward: false },
    });
    const elements: CardElement[] = [];
    if (tips && tips.length > 0) {
      elements.push({
        tag: CardElementTag.div,
        text: {
          tag: CardTextTag.lark_md,
          content: tips,
        },
      } as CardContentElement);
    }
    if (coreEmail) {
      let emailInfo = '';
      for (let i = 0; i < coreEmail.length; i++) {
        emailInfo = emailInfo.concat(`<at email=${coreEmail[i]}></at>`);
      }
      elements.push({
        tag: CardElementTag.div,
        text: {
          tag: CardTextTag.lark_md,
          content: emailInfo,
        },
      } as CardContentElement);
    }
    elements.push({
      tag: CardElementTag.div,
      text: {
        tag: CardTextTag.lark_md,
        content: description,
      },
    } as CardContentElement);

    elements.push({
      tag: CardElementTag.div,
      text: {
        tag: CardTextTag.lark_md,
        content: fromairplane ? `消息来自：纸飞机自动触发` : `消息来自：<at email=${senderEmail}></at>`,
      },
    } as CardContentElement);

    baseCard.elements = elements;
    return baseCard;
  }

  // 发到核心群和订阅的人
  async buildGrayInfoCard(
    platform: PlatformType,
    product: ProductType,
    version: string,
    versionCode: string,
    gray_count: number,
    onlyForSubs: boolean,
    versionProcess: VersionProcess | null,
    ratio?: string,
  ) {
    const baseCard = this.buildBaseCard({
      title: `${product === LVProductType.lv ? '剪映' : 'CapCut'} ${platform} ${version} 灰度信息同步`,
      template: CardTemplate.green,
      config: { enable_forward: false },
    });

    let content = '';
    const version_name = version.split('.').join('');

    let rdBm_email = '';
    if (versionProcess) {
      rdBm_email = versionProcess.bmInfo[BmType.rd].email;
    }

    if (onlyForSubs) {
      content = `${product === LVProductType.lv ? '剪映' : 'CapCut'} ${platform} 已发布 ${
        gray_count === 0 ? '众测' : `第${gray_count}轮灰度`
      }（${versionCode}）`;
    } else {
      if (platform === PlatformType.Android) {
        if (gray_count === 1 || gray_count === 0) {
          if (product === LVProductType.cc) {
            content = `CC${version_name}版本${
              gray_count === 0 ? '灰度' : '一灰'
            }(${versionCode}) 已提交发布,审核通过后放量，放量比例${ratio}
<at email=${rdBm_email}></at><at email="<EMAIL>"></at>`;
          } else {
            content = `Android客户端，版本号${versionCode}，已发布${
              gray_count === 0 ? '众测' : '一灰'
            }，放量3k稳定后加量
<at email="<EMAIL>"></at> 请关注核心数据上报
<at email=${rdBm_email}></at> 请关注crash
<at email="<EMAIL>"></at> 请关注升级低优审核版本号
<at email="<EMAIL>"></at> 请关注安全合规`;
          }
        }
      } else {
        if (gray_count === 1 || gray_count === 2) {
          if (product === LVProductType.cc) {
            content = `iOS CapCut ${version_name} ${gray_count === 1 ? '版本已发布一灰' : '版本已发布二灰'} (${versionCode})
<at email=${rdBm_email}></at><at email="<EMAIL>"></at><at email="<EMAIL>"></at>`;
          } else {
            content = `iOS端, 版本号${versionCode},${gray_count === 1 ? '版本已发布一灰' : '版本已发布二灰'}，放量7000
<at email="<EMAIL>"></at> 请关注核心数据上报
<at email=${rdBm_email}></at> 请关注crash
<at email="<EMAIL>"></at> 请关注升级低优审核版本号
<at email="<EMAIL>"></at> 请关注安全合规`;
          }
        } else {
          const cur_timestamp = timeUtil.nowMilliseconds();
          const cur_date = `${new Date(cur_timestamp).getMonth() + 1}月${new Date(cur_timestamp).getDate()}日`;
          content = `${product === LVProductType.lv ? '剪映' : 'CapCut'}${version_name}版本 已于${cur_date}发布${
            gray_count === 3 ? '三灰包' : '四灰包'
          }(${versionCode})
<at email=${rdBm_email}></at><at email="<EMAIL>"></at><at email="<EMAIL>"></at>`;
        }
      }
    }

    const elements: CardElement[] = [];

    elements.push({
      tag: CardElementTag.div,
      text: {
        tag: CardTextTag.lark_md,
        content,
      },
    } as CardContentElement);

    elements.push({
      tag: CardElementTag.div,
      text: {
        tag: CardTextTag.plain_text,
        content: '消息来自：纸飞机自动触发',
      },
    } as CardContentElement);

    baseCard.elements = elements;

    return baseCard;
  }

  buildPaperSlardarUrl(appName: string, versionCode: string) {
    const base = `${MAIN_HOST_HTTPS}/releaseProcess/slardar`;
    const params = new URLSearchParams();
    params.set('appid', appName === '剪映' ? '177502' : '300602');
    params.set('version', versionUtils.versionCode2Version(versionCode));
    params.set('selected', versionCode);
    return `${base}?${params}`;
  }
  /** --------------------------热修-------------------------------  **/
  async findTask(instanceCode: string, nodeId: string) {
    const larkClient = useInject(LarkClient);
    const rsp = await larkClient.approval.instance.get({
      path: {
        instance_id: instanceCode,
      },
    });
    if (!rsp || rsp.code !== 0) {
      return;
    }
    const instance = rsp?.data;
    // 查找自测节点任务
    return instance?.task_list?.find(it => it?.node_id === nodeId);
  }

  /**
   * ---------------- 醒图-异常需求上车 ----------------
   */
  // 邀请业务方向评估
  async buildRetouchApprovalBusinessInvolveCard(approvalInfo?: ApprovalDetail) {
    let affectBusinessLine = '';
    switch (approvalInfo?.approvalType) {
      case ApprovalType.InsertProductRequirements:
        affectBusinessLine = uniq(
          Object.values(approvalInfo?.requirementChangeInfo?.affectBusinessLine ?? {}).reduce(
            (acc, curr) => [...acc, ...curr],
            [],
          ),
        )
          .map(i => RetouchApprovalBusinessConfigs[i].name)
          .join(' | ');
        break;
      case ApprovalType.HotFix:
        affectBusinessLine = uniq(
          Object.values(approvalInfo?.hotfixTask?.affectBusinessLine ?? {}).reduce(
            (acc, curr) => [...acc, ...curr],
            [],
          ),
        )
          .map(i => RetouchApprovalBusinessConfigs[i].name)
          .join(' | ');
        break;
      case ApprovalType.FixedVersionRequirements:
        affectBusinessLine = uniq(
          Object.values(approvalInfo?.fixVersionTask?.affectBusinessLine ?? {}).reduce(
            (acc, curr) => [...acc, ...curr],
            [],
          ),
        )
          .map(i => RetouchApprovalBusinessConfigs[i].name)
          .join(' | ');
        break;
      default:
        affectBusinessLine = uniq(
          Object.values(approvalInfo?.affectBusinessLine ?? {}).reduce((acc, curr) => [...acc, ...curr], []),
        )
          .map(i => RetouchApprovalBusinessConfigs[i].name)
          .join(' | ');
        break;
    }
    const keys = Object.keys(RetouchApprovalBusinessConfigs).filter(key =>
      RetouchApprovalBusinessConfigs?.[key]?.appIds.some(it =>
        [approvalInfo?.app_id, ...(approvalInfo?.requirementChangeInfo?.appIds ?? [])].includes(it),
      ),
    );
    return new LarkCardTemplate(LarkTemplateId.ApprovalProcessesAffectedBusinessInvolve, {
      affectedBusinessLine: affectBusinessLine,
      affectedBusinessLineSelect: keys.map(key => ({
        text: RetouchApprovalBusinessConfigs[key]?.name,
        value: key,
      })),
      addMoreAffectBusinessLineInfo: {
        cardCallbackType: CardCallbackType.ApprovalAddMoreBusinessLine,
        instanceCode: approvalInfo?.instanceCode,
        approvalCode: approvalInfo?.approvalCode,
      },
    });
  }

  // 生成评估推送
  async buildRetouchTicketApprovalCommonNodeAssessment(
    approvalInfo?: ApprovalDetail,
    userEmails?: string[],
    nodeKeyName?: string,
  ) {
    const appId = approvalInfo?.app_id ?? -1;
    const productName = versionUtils.appIdToAppInfo(appId.toString())?.productName;
    const platform = versionUtils.appIdToAppInfo(appId.toString())?.platform;
    const version = approvalInfo?.version;
    const baseCard = this.buildBaseCard({
      title: `异常需求上车评估通知`,
      template: CardTemplate.orange,
      config: { enable_forward: false },
    });
    const elements: CardElement[] = [];

    let messageInfo = '';
    switch (nodeKeyName) {
      case '0a15fe21862b5dedfa330ccd9440a5b1': // 一级审批节点名称，需求质量影响面判断
        messageInfo = ' **针对需求质量影响面判断**给出评估意见。';
        break;
      case '7d0a7af62a2d2e3000d7dc74e8794c27': // 二级审批节点名称，未达标项解决预期时间合理性判断
        messageInfo = ' **针对未达标项解决预期时间合理性判断**给出评估意见。';
        break;
      case 'f8dbb3a422e97f4abf3b253ee988e144': // 三级审批节点名称，强制上车必要性判断
        messageInfo = '**针对需求强制上车必要性判断**给出评估意见。';
        break;
      default:
        messageInfo = '';
        break;
    }

    mdLines(elements, [
      line('需求名称', approvalInfo?.meegoLinks[0].link.name),
      line('应用', productName),
      line('版本', version),
      line('平台', platform ?? '未知'),
      line('bits单链接', approvalInfo?.bitsLink?.url),
      line('强制上车理由', approvalInfo?.forceOnboardingReason),
      line('未达标的项目', approvalInfo?.unqualifiedProjects),
      line('未达标的原因', approvalInfo?.unmetCriteriaReason),
      line('未在16点前发起申请的原因', approvalInfo?.afterDDLReason),
      line('后续改进计划', approvalInfo?.improvementPlan),
      line(
        '准入条件-meego状态',
        approvalInfo?.meegoLinks[0].meegoStatus === MeegoWorkFlowNodeStatus.pass ? '已集成' : '未集成',
      ),
      line('准入条件-bug解决率', (approvalInfo?.meegoLinks[0].fixRate ?? 0).toString()),
      line(
        '准入条件-Pipeline状态',
        approvalInfo?.pipelineInfo?.pipelineStatus === PipelineStatus.success ? '通过' : '未通过',
      ),
    ]);
    elements.push({
      tag: CardElementTag.div,
      text: {
        tag: CardTextTag.lark_md,
        content: `${atEmail(approvalInfo?.createUserEmail)}发起异常需求上车评估，请 ${atEmails(userEmails ?? [])}${messageInfo}`,
      },
    } as CardContentElement);
    // 去审批按钮
    addViewButton(elements, '去审批', {
      pc_url: approvalDetailUrlPC(approvalInfo?.instanceCode),
      android_url: approvalDetailUrlMobile(approvalInfo?.instanceCode),
      ios_url: approvalDetailUrlMobile(approvalInfo?.instanceCode),
      url: approvalDetailUrlPC(approvalInfo?.instanceCode),
    });
    baseCard.elements = elements;
    return baseCard;
  }

  /**
   * ---------------- 醒图-封版后需求插入 ----------------
   */

  async buildRetouchRequirementInsertAlert() {
    const baseCard = this.buildBaseCard({
      title: `封版后需求插入风险提示`,
      template: CardTemplate.red,
      config: { enable_forward: false },
    });
    const elements: CardElement[] = [];
    elements.push({
      tag: CardElementTag.div,
      text: {
        tag: CardTextTag.lark_md,
        content:
          '**异常流程引入的质量和延期风险，由插入需求方和业务线承担，因未经系统测试，请仔细评估，认识到非常规合入的质量风险非常高。**',
      },
    } as CardContentElement);
    elements.push({
      tag: CardElementTag.div,
      text: {
        tag: CardTextTag.lark_md,
        content: '**需求插入审批通过后，后续质量如未按照预期时间达标合入版本，且对版本节奏影响需重新评估。**',
      },
    } as CardContentElement);
    baseCard.elements = elements;
    return baseCard;
  }
  // 插入评估summary
  async buildRetouchRequirementInsertSummary(approvalInfo?: ApprovalDetail, summaryInfo?: Map<string, any[]>) {
    const appIds = approvalInfo?.requirementChangeInfo?.appIds ?? [];
    const productNames = appIds.map(it => versionUtils.appIdToAppInfo(it.toString())?.productName);
    const platforms = appIds.map(it => versionUtils.appIdToAppInfo(it.toString())?.platform);
    const version = approvalInfo?.requirementChangeInfo?.version;
    const baseCard = this.buildBaseCard({
      title: `封版后需求插入评估意见汇总`,
      template: CardTemplate.green,
      config: { enable_forward: false },
    });
    const elements: CardElement[] = [];
    // 基础信息
    elements.push({
      tag: CardElementTag.div,
      text: {
        tag: CardTextTag.lark_md,
        content: '**需求详情**',
      },
    } as CardContentElement);
    mdLines(elements, [
      line('需求名称', approvalInfo?.title),
      line('应用', productNames.join(' | ')),
      line('版本', version),
      line('平台', platforms.join(' | ')),
      line('异常需求类型', '封版后-需求插入'),
      line('业务线', ApprovalOldBusinessConfigs[approvalInfo?.requirementChangeInfo?.business ?? '']?.name),
      line('需求链接', approvalInfo?.requirementChangeInfo?.meegoLink.url),
      line('前置评估文档', approvalInfo?.requirementChangeInfo?.detailDoc),
      line('变更原因', approvalInfo?.requirementChangeInfo?.reason),
      line('变更影响', approvalInfo?.requirementChangeInfo?.impact),
    ]);
    // 各环节评估comments
    const firstAssessmentTaskKeyName = '00b4e9a0047c3b5dffb8452972b3878a'; // 需求质量影响面判断
    const secondAssessmentTaskKeyName = '43d3875b5529f4ebb41b958677a4b023'; // 版本节奏影响判断
    const thirdAssessmentTaskKeyName = '767fe65d6e6b4a48239df06123b59e39'; // 受影响的各方影响判断
    for (const key of summaryInfo?.keys() ?? []) {
      if (key === firstAssessmentTaskKeyName) {
        const firstSummaryInfo = (summaryInfo?.get(key) ?? []).filter(it => it?.userEmail && it?.comment);
        if (firstSummaryInfo && firstSummaryInfo.length > 0) {
          elements.push({
            tag: CardElementTag.div,
            text: {
              tag: CardTextTag.lark_md,
              content: '**需求质量影响面判断**',
            },
          } as CardContentElement);
          firstSummaryInfo.map(it =>
            elements.push({
              tag: CardElementTag.columnSet,
              columns: [
                {
                  tag: CardElementTag.column,
                  width: 'auto',
                  elements: [
                    {
                      tag: CardElementTag.markdown,
                      content: `<at email="${it.userEmail}"/>`,
                      text_align: 'left',
                      text_size: 'normal',
                    } as CardElement,
                  ],
                  vertical_align: 'top',
                } as CardColumnElement,
                {
                  tag: CardElementTag.column,
                  width: 'auto',
                  elements: [
                    {
                      tag: CardElementTag.markdown,
                      content: `给出的评估意见: ${it.comment}`,
                      text_align: 'left',
                      text_size: 'normal',
                    } as CardElement,
                  ],
                  vertical_align: 'top',
                } as CardColumnElement,
              ],
            } as CardColumnSetElement),
          );
        }
      }
      if (key === secondAssessmentTaskKeyName) {
        const secondSummaryInfo = (summaryInfo?.get(key) ?? []).filter(it => it?.userEmail && it?.comment);
        if (secondSummaryInfo && secondSummaryInfo.length > 0) {
          elements.push({
            tag: CardElementTag.div,
            text: {
              tag: CardTextTag.lark_md,
              content: '**版本节奏影响判断**',
            },
          } as CardContentElement);
          secondSummaryInfo.map(it =>
            elements.push({
              tag: CardElementTag.columnSet,
              columns: [
                {
                  tag: CardElementTag.column,
                  width: 'auto',
                  elements: [
                    {
                      tag: CardElementTag.markdown,
                      content: `<at email="${it.userEmail}"/>`,
                      text_align: 'left',
                      text_size: 'normal',
                    } as CardElement,
                  ],
                  vertical_align: 'top',
                } as CardColumnElement,
                {
                  tag: CardElementTag.column,
                  width: 'auto',
                  elements: [
                    {
                      tag: CardElementTag.markdown,
                      content: `给出的评估意见: ${it.comment}`,
                      text_align: 'left',
                      text_size: 'normal',
                    } as CardElement,
                  ],
                  vertical_align: 'top',
                } as CardColumnElement,
              ],
            } as CardColumnSetElement),
          );
        }
      }
      if (key === thirdAssessmentTaskKeyName) {
        const thirdSummaryInfo = (summaryInfo?.get(key) ?? []).filter(it => it?.userEmail && it?.comment);
        if (thirdSummaryInfo && thirdSummaryInfo.length > 0) {
          elements.push({
            tag: CardElementTag.div,
            text: {
              tag: CardTextTag.lark_md,
              content: '**受影响的业务方向判断**',
            },
          } as CardContentElement);
          thirdSummaryInfo.map(it =>
            elements.push({
              tag: CardElementTag.columnSet,
              columns: [
                {
                  tag: CardElementTag.column,
                  width: 'auto',
                  elements: [
                    {
                      tag: CardElementTag.markdown,
                      content: `<at email="${it.userEmail}"/>`,
                      text_align: 'left',
                      text_size: 'normal',
                    } as CardElement,
                  ],
                  vertical_align: 'top',
                } as CardColumnElement,
                {
                  tag: CardElementTag.column,
                  width: 'auto',
                  elements: [
                    {
                      tag: CardElementTag.markdown,
                      content: `给出的评估意见: ${it.comment}`,
                      text_align: 'left',
                      text_size: 'normal',
                    } as CardElement,
                  ],
                  vertical_align: 'top',
                } as CardColumnElement,
              ],
            } as CardColumnSetElement),
          );
        }
      }
    }
    baseCard.elements = elements;
    return baseCard;
  }

  async _getMeegoUsers(meegoUrl: string) {
    // 通用类型：动态获取关联Meego单的QA & RD 塞进二级审批节点
    const r = /.*https:\/\/meego\.feishu\.cn\/(\w+)\/(\w+)\/detail\/(\d+).*/g;
    const m = r.exec(meegoUrl ?? '');
    const meego = useInject(MeegoService);
    if (m && m.length >= 4) {
      const meegoType = m[2];
      const meegoId = m[3];
      const workflowResult = await meego.requestWorkflow(faceuProjectKey, meegoType, meegoId);
      const nodes = workflowResult.data.workflow_nodes;
      this.logger.info(`queryMeegoInfo url => ${meegoUrl}, meegoId => ${meegoId}, nodes => ${JSON.stringify(nodes)}`);
      const rdUserKeys = nodes
        ?.filter(node => node !== undefined)
        ?.reduce((acc: string[], node: WorkflowNode) => {
          if (node.role_assignee) {
            // 简化流程，取技术Owner
            const owners = node.role_assignee
              ?.filter(it => {
                const role = it?.role;
                return role ? ['role_501834', 'operator'].includes(role) : false;
              })
              ?.flatMap(assignee => assignee.owners);
            acc.push(...owners);
          }
          return acc;
        }, []);
      const qaUserKeys = nodes
        ?.filter(node => node !== undefined)
        ?.reduce((acc: string[], node: WorkflowNode) => {
          if (node.role_assignee) {
            // 需求QA
            const owners = node.role_assignee
              ?.filter(it => {
                const role = it?.role;
                return role ? ['clientqa', 'reporter'].includes(role) : false;
              })
              ?.flatMap(assignee => assignee.owners);
            acc.push(...owners);
          }
          return acc;
        }, []);
      const rdMeegoUsers = await meego.requestMeegoUserInfos({
        user_keys: uniq(rdUserKeys ?? []),
      });
      const qaMeegoUsers = await meego.requestMeegoUserInfos({
        user_keys: uniq(qaUserKeys ?? []),
      });
      return { rdMeegoUsers, qaMeegoUsers };
    }
  }
  // 需求插入评估
  async buildRetouchRequirementInsertCommonNodeAssessment(
    approvalInfo?: ApprovalDetail,
    notifyUsersEmail?: string[],
    nodeKeyName?: string,
  ) {
    const appIds = approvalInfo?.requirementChangeInfo?.appIds ?? [];
    const productNames = appIds.map(it => versionUtils.appIdToAppInfo(it.toString())?.productName);
    const platforms = appIds.map(it => versionUtils.appIdToAppInfo(it.toString())?.platform);
    const version = approvalInfo?.requirementChangeInfo?.version;
    const baseCard = this.buildBaseCard({
      title: `封版后需求插入评估通知`,
      template: CardTemplate.orange,
      config: { enable_forward: false },
    });
    const getAffectBusinessLines = (businessLines: string[]) => {
      const result: string[] = [];
      for (const businessLine of businessLines) {
        const businessLineConfig = RetouchApprovalBusinessConfigs[businessLine];
        if (businessLineConfig) {
          result.push(businessLineConfig.name);
        }
      }
      return result;
    };

    const businessLineKey = Object.values(approvalInfo?.requirementChangeInfo?.affectBusinessLine ?? {})?.reduce(
      (acc, cur) => {
        acc.push(...cur);
        return acc;
      },
      [],
    );
    const elements: CardElement[] = [];
    let messageInfo = '';
    let alertInfo = '';
    const preAlertInfo = "**请判断以下内容，**<font color='red'>**务必在审批意见中给出评估依据**</font>\n";
    switch (nodeKeyName) {
      case '00b4e9a0047c3b5dffb8452972b3878a': // 一级审批节点名称，需求质量影响面判断
        messageInfo = ' **针对需求质量影响面判断**给出评估意见。';
        alertInfo =
          '**质量判断提示 - 所有研发**\n' +
          `1. 请确认本次改动业务方向为**${RetouchApprovalBusinessConfigs[approvalInfo?.requirementChangeInfo?.business ?? ''].name}**，并评估对本业务的影响面\n` +
          '2. 请评估本次改动受影响的其它业务方向，如没有填写无，并填写不受影响的评估依据；如果有，请选择具体的业务方向（可多选），并对每个方向受影响面进行详细描述，受影响业务方会加到后续的审批流中。\n' +
          '**质量判断提示 - 业务QA**\n' +
          '1. 请确认本次改动对业务的影响，结合研发影响面的判断，并根据测试覆盖情况，确认质量最终的风险结果和人力成本风险结果\n';
        break;
      case '43d3875b5529f4ebb41b958677a4b023': // 二级审批节点名称，版本节奏影响判断
        messageInfo = ' **针对版本节奏影响判断**给出评估意见。';
        alertInfo =
          '**版本节奏影响面判断提示 - 端QA**\n' +
          '1. 请确认本次改动对本次版本和下次版本节奏的影响，如没有填写无，并填写不受影响的评估依据；如有，请详细描述影响面';
        break;
      case '767fe65d6e6b4a48239df06123b59e39': // 三级审批节点名称，受影响的各方影响判断
        messageInfo = '**针对受影响的业务方向判断**给出评估意见。';
        alertInfo =
          businessLineKey && businessLineKey.length > 0
            ? `经评估，本需求会对**${getAffectBusinessLines(uniq(businessLineKey)).join(' | ')}**方向有影响\n` +
              '1. 请根据需求及研发的判断，确认本业务影响面的准确性，并描述最终影响评估结果\n' +
              '2. 请确认影响已解决或风险可控，并详细描述解决方式'
            : '';
        break;
      case '2a3e69d94694ca1528dccf3e480c42fe': // 四级审批节点名称，插入必要性判断
        messageInfo = '**针对需求插入必要性判断**给出评估意见。';
        break;
      default:
        messageInfo = '';
        break;
    }
    elements.push({
      tag: CardElementTag.div,
      text: {
        tag: CardTextTag.lark_md,
        content: `${atEmail(approvalInfo?.createUserEmail)}发起封板后需求插入评估，请 ${atEmails(notifyUsersEmail ?? [])} ${messageInfo}`,
      },
    } as CardContentElement);
    if (alertInfo && alertInfo !== '') {
      elements.push({
        tag: CardElementTag.div,
        text: {
          tag: CardTextTag.lark_md,
          content: `${preAlertInfo + alertInfo}`,
        },
      } as CardContentElement);
    }
    elements.push({
      tag: CardElementTag.hr,
    } as CardElement);
    mdLines(elements, [
      line('需求名称', approvalInfo?.title),
      line('应用', productNames.join(' | ')),
      line('版本', version),
      line('平台', platforms.join(' | ')),
      line('异常需求类型', '封版后-需求插入'),
      line('业务线', RetouchApprovalBusinessConfigs[approvalInfo?.requirementChangeInfo?.business ?? '']?.name),
      line('需求链接', approvalInfo?.requirementChangeInfo?.meegoLink.url),
      line('前置评估文档', approvalInfo?.requirementChangeInfo?.detailDoc),
      line('变更原因', approvalInfo?.requirementChangeInfo?.reason),
      line('变更影响', approvalInfo?.requirementChangeInfo?.impact),
      line('如果不插入的影响', approvalInfo?.requirementChangeInfo?.noInsertImpact),
      line('更新后的排期计划', approvalInfo?.requirementChangeInfo?.scheduling),
      line(
        '评估受影响的业务线',
        businessLineKey && businessLineKey.length > 0 ? getAffectBusinessLines(uniq(businessLineKey)).join(' | ') : '-',
      ),
    ]);

    // 去审批按钮
    addViewButton(elements, '去审批', {
      pc_url: approvalDetailUrlPC(approvalInfo?.instanceCode),
      android_url: approvalDetailUrlMobile(approvalInfo?.instanceCode),
      ios_url: approvalDetailUrlMobile(approvalInfo?.instanceCode),
      url: approvalDetailUrlPC(approvalInfo?.instanceCode),
    });
    baseCard.elements = elements;
    return baseCard;
  }

  // 可行性评估通知
  async buildHotfixTaskAssessment(approvalInfo?: ApprovalDetail) {
    const hotfixTask = approvalInfo?.hotfixTask;
    const appId = hotfixTask?.appId ?? -1;
    const productName = versionUtils.appIdToAppInfo(appId.toString())?.productName;
    const platform = versionUtils.appIdToAppInfo(appId.toString())?.platform;
    const versions = hotfixTask?.versions;
    const baseCard = this.buildBaseCard({
      title: `热修评估通知`,
      template: CardTemplate.orange,
      config: { enable_forward: false },
    });
    const business = hotfixTask?.business;

    const elements: CardElement[] = [];
    mdLines(elements, [
      line('热修任务名称', approvalInfo?.hotfixTask?.approvalName),
      line('应用', productName),
      line('版本', versions?.join(' | ') ?? '未知'),
      line('平台', platform ?? '未知'),
      line('业务', business ? ApprovalBusinessConfigs[business]?.name : '未知'),
      line('问题详情', hotfixTask?.problemDetail),
      line('问题类型', hotfixTask?.hotfixProblemType ? hotfixTypeText[hotfixTask?.hotfixProblemType] : '未知'),
      line('用户量级', hotfixTask?.userNumber),
      line('指标影响', hotfixTask?.indicatorImpact),
      line('收入影响', hotfixTask?.incomeImpact),
      line('能否通过线上配置解决', hotfixTask?.canSolveOnline ? '能' : '不能'),
      line('问题是否必现', hotfixTask?.isInevitable ? '是' : '否'),
      line('问题是否通过前端修复', hotfixTask?.isFE ? '是' : '否'),
      line(
        '需要放开的前端Channel名称',
        hotfixTask?.isFE && hotfixTask?.FEChannels && hotfixTask?.FEChannels?.length > 0
          ? hotfixTask?.FEChannels?.join(' | ')
          : '',
      ),
      line('是否全量版本热修', (!hotfixTask?.isFE && hotfixTask?.isFullReleaseVersion) ?? true ? '是' : '否'),
      line('引入问题的MR', hotfixTask?.mrLinkUrl),
      line('修复代码运行阶段', hotfixRunningTimeText[hotfixTask?.startUpRunTimeType ?? HotfixRunningTimeType.Unkown]),
      line('修复方案', hotfixTask?.fixPlan),
      line('测试方案', hotfixTask?.testPlan),
      line('期待全量时间', convertToCustomFormat(hotfixTask?.desireFullTime)),
      line('业务QA', atEmails(hotfixTask?.businessQaEmails)),
    ]);
    elements.push({
      tag: CardElementTag.div,
      text: {
        tag: CardTextTag.lark_md,
        content: `${atEmail(approvalInfo?.createUserEmail)}发起热修评估，请 ${atEmails(ApprovalHotfixConfig[appId].owners.rdPoc)} ${atEmails(ApprovalHotfixConfig[appId].owners.qaPoc)} 给出评估意见`,
      },
    } as CardContentElement);
    // 去审批按钮
    addViewButton(elements, '去审批', {
      pc_url: approvalDetailUrlPC(approvalInfo?.instanceCode),
      android_url: approvalDetailUrlMobile(approvalInfo?.instanceCode),
      ios_url: approvalDetailUrlMobile(approvalInfo?.instanceCode),
      url: approvalDetailUrlPC(approvalInfo?.instanceCode),
    });
    baseCard.elements = elements;
    return baseCard;
  }

  // 可行性评估通过, 通知自测任务
  async buildHotfixTaskAssessmentApproved(approvalInfo?: ApprovalDetail) {
    const hotfixTask = approvalInfo?.hotfixTask;
    const appId = hotfixTask?.appId ?? -1;
    const productName = versionUtils.appIdToAppInfo(appId.toString())?.productName;
    const platform = versionUtils.appIdToAppInfo(appId.toString())?.platform;
    const versions = hotfixTask?.versions;
    const createEmail = approvalInfo?.createUserEmail;
    const baseCard = this.buildBaseCard({
      title: `热修可行性评估通过`,
      template: CardTemplate.green,
      config: { enable_forward: false },
    });
    const business = hotfixTask?.business;

    const elements: CardElement[] = [];
    mdLines(elements, [
      line('热修任务名称', approvalInfo?.hotfixTask?.approvalName),
      line('应用', productName),
      line('平台', platform ?? '未知'),
      line('版本', versions?.join(' | ') ?? '未知'),
      line('业务', business ? ApprovalBusinessConfigs[business]?.name : '未知'),
      line('问题详情', hotfixTask?.problemDetail),
      line('问题类型', hotfixTask?.hotfixProblemType ? hotfixTypeText[hotfixTask?.hotfixProblemType] : '未知'),
      line('用户量级', hotfixTask?.userNumber),
      line('指标影响', hotfixTask?.indicatorImpact),
      line('收入影响', hotfixTask?.incomeImpact),
      line('能否通过线上配置解决', hotfixTask?.canSolveOnline ? '能' : '不能'),
      line('问题是否必现', hotfixTask?.isInevitable ? '是' : '否'),
      line('问题是否通过前端修复', hotfixTask?.isFE ? '是' : '否'),
      line(
        '需要放开的前端Channel名称',
        hotfixTask?.isFE && hotfixTask?.FEChannels && hotfixTask?.FEChannels?.length > 0
          ? hotfixTask?.FEChannels?.join(' | ')
          : '',
      ),
      line('是否全量版本热修', (!hotfixTask?.isFE && hotfixTask?.isFullReleaseVersion) ?? true ? '是' : '否'),
      line('引入问题的MR', hotfixTask?.mrLinkUrl),
      line('修复代码运行阶段', hotfixRunningTimeText[hotfixTask?.startUpRunTimeType ?? HotfixRunningTimeType.Unkown]),
      line('修复方案', hotfixTask?.fixPlan),
      line('测试方案', hotfixTask?.testPlan),
      line('期待全量时间', convertToCustomFormat(hotfixTask?.desireFullTime)),
      line('业务QA', atEmails(hotfixTask?.businessQaEmails)),
    ]);
    elements.push({
      tag: CardElementTag.div,
      text: {
        tag: CardTextTag.lark_md,
        content: `${atEmail(approvalInfo?.createUserEmail)}你发起的热修评估已通过，请bits创建热修任务并做自测。`,
      },
    } as CardContentElement);
    // 审批按钮
    const approvalCode = approvalInfo?.approvalCode;
    const instanceCode = approvalInfo?.instanceCode;
    if (approvalCode && instanceCode) {
      const task = await this.findTask(instanceCode, '2892cc88284203776c8faf96318d771e');
      const openIds: string[] = [];
      if (createEmail) {
        const userInfo = await this.lark.getUserInfoByEmail(createEmail);
        const openId = userInfo?.open_id;
        if (openId) {
          openIds.push(openId);
        }
      }
      addApprovalButton(
        elements,
        {
          text: '自测通过',
          extra: {
            approvalCode: approvalInfo?.approvalCode,
            instanceCode: approvalInfo?.instanceCode,
            approvalAction: LarkApprovalStatus.APPROVED,
            type: 'approval_task',
            permissionOpenIds: openIds.join(','),
            taskId: task?.id,
            userId: task?.user_id,
          },
        },
        {
          text: '自测不通过',
          extra: {
            approvalCode: approvalInfo?.approvalCode,
            instanceCode: approvalInfo?.instanceCode,
            approvalAction: LarkApprovalStatus.REJECTED,
            type: 'approval_task',
            permissionOpenIds: openIds.join(','),
            taskId: task?.id,
            userId: task?.user_id,
          },
        },
      );
    }
    baseCard.elements = elements;
    return baseCard;
  }

  // 插入评估summary
  async buildRetouchHotfixSummary(approvalInfo?: ApprovalDetail, summaryInfo?: Map<string, any[]>) {
    // const version = approvalInfo?.requirementChangeInfo?.version;
    const hotfixTask = approvalInfo?.hotfixTask;
    const appId = hotfixTask?.appId ?? -1;
    const productName = versionUtils.appIdToAppInfo(appId.toString())?.productName;
    const platform = versionUtils.appIdToAppInfo(appId.toString())?.platform;
    const versions = hotfixTask?.versions;
    const createEmail = approvalInfo?.createUserEmail;
    const business = hotfixTask?.business;
    const getAffectBusinessLines = (businessLines: string[]) => {
      const result: string[] = [];
      for (const businessLine of businessLines) {
        const businessLineConfig = RetouchApprovalBusinessConfigs[businessLine];
        if (businessLineConfig) {
          result.push(businessLineConfig.name);
        }
      }
      return result;
    };
    const businessLineKey = Object.values(approvalInfo?.hotfixTask?.affectBusinessLine ?? {})?.reduce((acc, cur) => {
      acc.push(...cur);
      return acc;
    }, []);
    const baseCard = this.buildBaseCard({
      title: `热修评估意见汇总`,
      template: CardTemplate.green,
      config: { enable_forward: false },
    });
    const elements: CardElement[] = [];
    // 基础信息
    elements.push({
      tag: CardElementTag.div,
      text: {
        tag: CardTextTag.lark_md,
        content: '**需求详情**',
      },
    } as CardContentElement);
    mdLines(elements, [
      line('热修任务名称', approvalInfo?.hotfixTask?.approvalName),
      line('应用', productName),
      line('平台', platform ?? '未知'),
      line('版本', versions?.join(' | ') ?? '未知'),
      line('业务', business ? RetouchApprovalBusinessConfigs[business]?.name : '未知'),
      line('问题详情', hotfixTask?.problemDetail),
      line('问题类型', hotfixTask?.hotfixProblemType ? hotfixTypeText[hotfixTask?.hotfixProblemType] : '未知'),
      line('用户量级', hotfixTask?.userNumber),
      line('指标影响', hotfixTask?.indicatorImpact),
      line('收入影响', hotfixTask?.incomeImpact),
      line('能否通过线上配置解决', hotfixTask?.canSolveOnline ? '能' : '不能'),
      line('问题是否必现', hotfixTask?.isInevitable ? '是' : '否'),
      line('缺陷链接', hotfixTask?.meegoUrl),
      line('引入问题的MR', hotfixTask?.mrLinkUrl),
      line('修复代码运行阶段', hotfixRunningTimeText[hotfixTask?.startUpRunTimeType ?? HotfixRunningTimeType.Unkown]),
      line('修复方案', hotfixTask?.fixPlan),
      line('测试方案', hotfixTask?.testPlan),
      line(
        '评估受影响的业务线',
        businessLineKey && businessLineKey.length > 0 ? getAffectBusinessLines(uniq(businessLineKey)).join(' | ') : '-',
      ),
      line('期待全量时间', convertToCustomFormat(hotfixTask?.desireFullTime)),
      line('业务QA', atEmails(hotfixTask?.businessQaEmails)),
    ]);
    // 各环节评估comments
    const firstAssessmentTaskKeyName = '1c52bb0cce89474d44e3bedb6610082b'; // SOP标准判断
    const secondAssessmentTaskKeyName = '54e8ce3880b03f8f5f4ed03c19ec4673'; // 热修质量影响面判断
    const thirdAssessmentTaskKeyName = '8026fb829ed76273da779749334c0e0c'; // 受影响的各方影响判断
    for (const key of summaryInfo?.keys() ?? []) {
      if (key === firstAssessmentTaskKeyName) {
        const firstSummaryInfo = (summaryInfo?.get(key) ?? []).filter(it => it?.userEmail && it?.comment);
        if (firstSummaryInfo && firstSummaryInfo.length > 0) {
          elements.push({
            tag: CardElementTag.div,
            text: {
              tag: CardTextTag.lark_md,
              content:
                approvalInfo?.hotfixTask?.hotfixProblemType === HotfixProblemType.Experience ||
                approvalInfo?.hotfixTask?.hotfixProblemType === HotfixProblemType.ProductData
                  ? '**产品收益判断**'
                  : '**SOP标准判断**',
            },
          } as CardContentElement);
          firstSummaryInfo.map(it =>
            elements.push({
              tag: CardElementTag.columnSet,
              columns: [
                {
                  tag: CardElementTag.column,
                  width: 'auto',
                  elements: [
                    {
                      tag: CardElementTag.markdown,
                      content: `<at email="${it.userEmail}"/>`,
                      text_align: 'left',
                      text_size: 'normal',
                    } as CardElement,
                  ],
                  vertical_align: 'top',
                } as CardColumnElement,
                {
                  tag: CardElementTag.column,
                  width: 'auto',
                  elements: [
                    {
                      tag: CardElementTag.markdown,
                      content: `给出的评估意见: ${it.comment}`,
                      text_align: 'left',
                      text_size: 'normal',
                    } as CardElement,
                  ],
                  vertical_align: 'top',
                } as CardColumnElement,
              ],
            } as CardColumnSetElement),
          );
        }
      }
      if (key === secondAssessmentTaskKeyName) {
        const secondSummaryInfo = (summaryInfo?.get(key) ?? []).filter(it => it?.userEmail && it?.comment);
        if (secondSummaryInfo && secondSummaryInfo.length > 0) {
          elements.push({
            tag: CardElementTag.div,
            text: {
              tag: CardTextTag.lark_md,
              content: '**热修质量影响面判断**',
            },
          } as CardContentElement);
          secondSummaryInfo.map(it =>
            elements.push({
              tag: CardElementTag.columnSet,
              columns: [
                {
                  tag: CardElementTag.column,
                  width: 'auto',
                  elements: [
                    {
                      tag: CardElementTag.markdown,
                      content: `<at email="${it.userEmail}"/>`,
                      text_align: 'left',
                      text_size: 'normal',
                    } as CardElement,
                  ],
                  vertical_align: 'top',
                } as CardColumnElement,
                {
                  tag: CardElementTag.column,
                  width: 'auto',
                  elements: [
                    {
                      tag: CardElementTag.markdown,
                      content: `给出的评估意见: ${it.comment}`,
                      text_align: 'left',
                      text_size: 'normal',
                    } as CardElement,
                  ],
                  vertical_align: 'top',
                } as CardColumnElement,
              ],
            } as CardColumnSetElement),
          );
        }
      }
      if (key === thirdAssessmentTaskKeyName) {
        const thirdSummaryInfo = (summaryInfo?.get(key) ?? []).filter(it => it?.userEmail && it?.comment);
        if (thirdSummaryInfo && thirdSummaryInfo.length > 0) {
          elements.push({
            tag: CardElementTag.div,
            text: {
              tag: CardTextTag.lark_md,
              content: '**受影响的业务方向判断**',
            },
          } as CardContentElement);
          thirdSummaryInfo.map(it =>
            elements.push({
              tag: CardElementTag.columnSet,
              columns: [
                {
                  tag: CardElementTag.column,
                  width: 'auto',
                  elements: [
                    {
                      tag: CardElementTag.markdown,
                      content: `<at email="${it.userEmail}"/>`,
                      text_align: 'left',
                      text_size: 'normal',
                    } as CardElement,
                  ],
                  vertical_align: 'top',
                } as CardColumnElement,
                {
                  tag: CardElementTag.column,
                  width: 'auto',
                  elements: [
                    {
                      tag: CardElementTag.markdown,
                      content: `给出的评估意见: ${it.comment}`,
                      text_align: 'left',
                      text_size: 'normal',
                    } as CardElement,
                  ],
                  vertical_align: 'top',
                } as CardColumnElement,
              ],
            } as CardColumnSetElement),
          );
        }
      }
    }
    baseCard.elements = elements;
    return baseCard;
  }

  // 醒图热修通用审批流通知
  async buildRetouchHotfixTaskNodeCommonApproved(
    approvalInfo?: ApprovalDetail,
    notifyUsersEmail?: string[],
    nodeKeyName?: string,
  ) {
    const hotfixTask = approvalInfo?.hotfixTask;
    const appId = hotfixTask?.appId ?? -1;
    const productName = versionUtils.appIdToAppInfo(appId.toString())?.productName;
    const platform = versionUtils.appIdToAppInfo(appId.toString())?.platform;
    const versions = hotfixTask?.versions;
    const createEmail = approvalInfo?.createUserEmail;
    const baseCard = this.buildBaseCard({
      title: `线上热修评估通知`,
      template: CardTemplate.orange,
      config: { enable_forward: false },
    });
    const business = hotfixTask?.business;
    const getAffectBusinessLines = (businessLines: string[]) => {
      const result: string[] = [];
      for (const businessLine of businessLines) {
        const businessLineConfig = RetouchApprovalBusinessConfigs[businessLine];
        if (businessLineConfig) {
          result.push(businessLineConfig.name);
        }
      }
      return result;
    };
    const businessLineKey = Object.values(approvalInfo?.hotfixTask?.affectBusinessLine ?? {})?.reduce((acc, cur) => {
      acc.push(...cur);
      return acc;
    }, []);
    const elements: CardElement[] = [];

    let messageInfo = '';
    let alertInfo = '';
    const preAlertInfo = "**请判断以下内容，**<font color='red'>**务必在审批意见中给出评估依据**</font>\n";
    switch (nodeKeyName) {
      case '1c52bb0cce89474d44e3bedb6610082b': // 一级审批节点名称，SOP标准判断
        messageInfo =
          hotfixTask?.hotfixProblemType === HotfixProblemType.Experience ||
          hotfixTask?.hotfixProblemType === HotfixProblemType.ProductData
            ? '**针对产品收益判断**给出评估意见。'
            : '**针对SOP标准判断**给出评估意见。';
        alertInfo =
          hotfixTask?.hotfixProblemType === HotfixProblemType.Experience ||
          hotfixTask?.hotfixProblemType === HotfixProblemType.ProductData
            ? '**产品收益判断**\n' +
              '1. 请确认本次改动的原因，提交人预估的产品影响面的准确性，包括影响指标、用户量级、收入影响等\n' +
              '2. 请确认该影响面有必要发起本次改动'
            : '';
        break;
      case '54e8ce3880b03f8f5f4ed03c19ec4673': // 二级审批节点名称，热修质量影响面判断
        messageInfo = '**针对热修质量影响面判断**给出评估意见。';
        alertInfo =
          '**质量判断提示 - 所有研发**\n' +
          `1. 请确认本次改动业务方向为**${RetouchApprovalBusinessConfigs[approvalInfo?.hotfixTask?.business ?? ''].name}**，并评估对本业务的影响面
` +
          '2. 请评估本次改动受影响的其它业务方向，如没有填写无，并填写不受影响的评估依据；如果有，请选择具体的业务方向（可多选），并对每个方向受影响面进行详细描述，受影响业务方会加到后续的审批流中。\n' +
          '**质量判断提示 - 业务QA**\n' +
          '1. 请确认本次改动对业务的影响，结合研发影响面的判断，并根据测试覆盖情况，确认质量最终的风险结果和人力成本风险结果\n';
        break;
      case '8026fb829ed76273da779749334c0e0c': // 三级审批节点名称，受影响的各方判断
        messageInfo = '**针对受影响的各业务方向判断**给出评估意见。';
        alertInfo =
          businessLineKey && businessLineKey.length > 0
            ? `经评估，本需求会对**${getAffectBusinessLines(uniq(businessLineKey)).join(' | ')}**方向有影响\n` +
              '1. 请根据需求及研发的判断，确认本业务影响面的准确性，并描述最终影响评估结果\n' +
              '2. 请确认影响已解决或风险可控，并详细描述解决方式'
            : '';
        break;
      case '543c58330cb4a5b73eae53098be0cc2a': // 四级审批节点名称，热修必要性判断
        messageInfo =
          hotfixTask?.hotfixProblemType === HotfixProblemType.Experience ||
          hotfixTask?.hotfixProblemType === HotfixProblemType.ProductData
            ? '请根据产品收益、质量&版本节奏影响面判断结果及受影响业务反馈结果 做最终的必要性审批。'
            : '请根据问题影响面、质量&版本节奏影响面判断结果及受影响业务反馈结果 做最终的必要性审批。';
        break;
      default:
        messageInfo = '';
        break;
    }

    elements.push({
      tag: CardElementTag.div,
      text: {
        tag: CardTextTag.lark_md,
        content: `${atEmail(approvalInfo?.createUserEmail)}发起线上热修评估，请 ${atEmails(notifyUsersEmail ?? [])} ${messageInfo}`,
      },
    } as CardContentElement);
    if (alertInfo && alertInfo !== '') {
      elements.push({
        tag: CardElementTag.div,
        text: {
          tag: CardTextTag.lark_md,
          content: `${preAlertInfo + alertInfo}`,
        },
      } as CardContentElement);
    }
    elements.push({
      tag: CardElementTag.hr,
    } as CardElement);
    mdLines(elements, [
      line('热修任务名称', approvalInfo?.hotfixTask?.approvalName),
      line('应用', productName),
      line('平台', platform ?? '未知'),
      line('版本', versions?.join(' | ') ?? '未知'),
      line('业务', business ? RetouchApprovalBusinessConfigs[business]?.name : '未知'),
      line('问题详情', hotfixTask?.problemDetail),
      line('问题类型', hotfixTask?.hotfixProblemType ? hotfixTypeText[hotfixTask?.hotfixProblemType] : '未知'),
      line('用户量级', hotfixTask?.userNumber),
      line('指标影响', hotfixTask?.indicatorImpact),
      line('收入影响', hotfixTask?.incomeImpact),
      line('能否通过线上配置解决', hotfixTask?.canSolveOnline ? '能' : '不能'),
      line('问题是否必现', hotfixTask?.isInevitable ? '是' : '否'),
      line('缺陷链接', hotfixTask?.meegoUrl),
      line('引入问题的MR', hotfixTask?.mrLinkUrl),
      line('修复代码运行阶段', hotfixRunningTimeText[hotfixTask?.startUpRunTimeType ?? HotfixRunningTimeType.Unkown]),
      line('修复方案', hotfixTask?.fixPlan),
      line('测试方案', hotfixTask?.testPlan),
      line(
        '评估受影响的业务线',
        businessLineKey && businessLineKey.length > 0 ? getAffectBusinessLines(uniq(businessLineKey)).join(' | ') : '-',
      ),
      line('期待全量时间', convertToCustomFormat(hotfixTask?.desireFullTime)),
      line('业务QA', atEmails(hotfixTask?.businessQaEmails)),
    ]);

    // 审批按钮
    const approvalCode = approvalInfo?.approvalCode;
    const instanceCode = approvalInfo?.instanceCode;
    if (approvalCode && instanceCode) {
      const task = await this.findTask(instanceCode, '2892cc88284203776c8faf96318d771e');
      const openIds: string[] = [];
      if (createEmail) {
        const userInfo = await this.lark.getUserInfoByEmail(createEmail);
        const openId = userInfo?.open_id;
        if (openId) {
          openIds.push(openId);
        }
      }
      // 去审批按钮
      addViewButton(elements, '去审批', {
        pc_url: approvalDetailUrlPC(approvalInfo?.instanceCode),
        android_url: approvalDetailUrlMobile(approvalInfo?.instanceCode),
        ios_url: approvalDetailUrlMobile(approvalInfo?.instanceCode),
        url: approvalDetailUrlPC(approvalInfo?.instanceCode),
      });
    }
    baseCard.elements = elements;
    return baseCard;
  }

  // 自测通过，发起放量审批
  async buildHotfixSelfTestingApproved(approvalInfo?: ApprovalDetail) {
    const hotfixTask = approvalInfo?.hotfixTask;
    const appId = hotfixTask?.appId ?? -1;
    const productName = versionUtils.appIdToAppInfo(appId.toString())?.productName;
    const platform = versionUtils.appIdToAppInfo(appId.toString())?.platform;
    const versions = hotfixTask?.versions;
    const baseCard = this.buildBaseCard({
      title: `热修任务放量审批`,
      template: CardTemplate.orange,
      config: { enable_forward: false },
    });
    const business = hotfixTask?.business;

    const elements: CardElement[] = [];
    mdLines(elements, [
      line('热修任务名称', approvalInfo?.hotfixTask?.approvalName),
      line('应用', productName),
      line('版本', versions?.join(' | ') ?? '未知'),
      line('平台', platform ?? '未知'),
      line('业务', business ? ApprovalBusinessConfigs[business]?.name : '未知'),
      line('问题详情', hotfixTask?.problemDetail),
      line('问题类型', hotfixTask?.hotfixProblemType ? hotfixTypeText[hotfixTask?.hotfixProblemType] : '未知'),
      line('用户量级', hotfixTask?.userNumber),
      line('指标影响', hotfixTask?.indicatorImpact),
      line('收入影响', hotfixTask?.incomeImpact),
      line('能否通过线上配置解决', hotfixTask?.canSolveOnline ? '能' : '不能'),
      line('问题是否必现', hotfixTask?.isInevitable ? '是' : '否'),
      line('问题是否通过前端修复', hotfixTask?.isFE ? '是' : '否'),
      line(
        '需要放开的前端Channel名称',
        hotfixTask?.isFE && hotfixTask?.FEChannels && hotfixTask?.FEChannels?.length > 0
          ? hotfixTask?.FEChannels?.join(' | ')
          : '',
      ),
      line('是否全量版本热修', (!hotfixTask?.isFE && hotfixTask?.isFullReleaseVersion) ?? true ? '是' : '否'),
      line('引入问题的MR', hotfixTask?.mrLinkUrl),
      line('修复代码运行阶段', hotfixRunningTimeText[hotfixTask?.startUpRunTimeType ?? HotfixRunningTimeType.Unkown]),
      line('修复方案', hotfixTask?.fixPlan),
      line('测试方案', hotfixTask?.testPlan),
      line('期待全量时间', convertToCustomFormat(hotfixTask?.desireFullTime)),
      line('业务QA', atEmails(hotfixTask?.businessQaEmails)),
    ]);
    elements.push({
      tag: CardElementTag.div,
      text: {
        tag: CardTextTag.lark_md,
        content: hotfixTask?.isFE
          ? `${atEmail(approvalInfo?.createUserEmail)}自测已通过，发起热修放量评估，请${atEmails(ApprovalHotfixConfig[appId].owners?.BD)}审批。`
          : hotfixTask?.isFullReleaseVersion ?? true
            ? `${atEmail(approvalInfo?.createUserEmail)}自测已通过，发起热修放量评估，请${atEmails(ApprovalHotfixConfig[appId].owners?.rdOwner)} ${atEmails(ApprovalHotfixConfig[appId].owners?.qaOwner)}审批。`
            : `${atEmail(approvalInfo?.createUserEmail)}自测已通过，发起热修放量评估。`,
      },
    } as CardContentElement);
    // 去审批按钮
    addViewButton(elements, '去审批', {
      pc_url: approvalDetailUrlPC(approvalInfo?.instanceCode),
      android_url: approvalDetailUrlMobile(approvalInfo?.instanceCode),
      ios_url: approvalDetailUrlMobile(approvalInfo?.instanceCode),
      url: approvalDetailUrlPC(approvalInfo?.instanceCode),
    });
    baseCard.elements = elements;
    return baseCard;
  }
  // 放量通知
  async buildHotfixTaskApproved(approvalInfo?: ApprovalDetail) {
    const hotfixTask = approvalInfo?.hotfixTask;
    const appId = hotfixTask?.appId ?? -1;
    const productName = versionUtils.appIdToAppInfo(appId.toString())?.productName;
    const platform = versionUtils.appIdToAppInfo(appId.toString())?.platform;
    const versions = hotfixTask?.versions;
    const baseCard = this.buildBaseCard({
      title: `热修任务放量审批通过`,
      template: CardTemplate.green,
      config: { enable_forward: false },
    });
    const business = hotfixTask?.business;

    const elements: CardElement[] = [];
    mdLines(elements, [
      line('热修任务名称', approvalInfo?.hotfixTask?.approvalName),
      line('应用', productName),
      line('平台', platform ?? '未知'),
      line('版本', versions?.join(' | ') ?? '未知'),
      line('业务', business ? ApprovalBusinessConfigs[business]?.name : '未知'),
      line('问题详情', hotfixTask?.problemDetail),
      line('问题类型', hotfixTask?.hotfixProblemType ? hotfixTypeText[hotfixTask?.hotfixProblemType] : '未知'),
      line('用户量级', hotfixTask?.userNumber),
      line('指标影响', hotfixTask?.indicatorImpact),
      line('收入影响', hotfixTask?.incomeImpact),
      line('能否通过线上配置解决', hotfixTask?.canSolveOnline ? '能' : '不能'),
      line('问题是否必现', hotfixTask?.isInevitable ? '是' : '否'),
      line('问题是否通过前端修复', hotfixTask?.isFE ? '是' : '否'),
      line(
        '需要放开的前端Channel名称',
        hotfixTask?.isFE && hotfixTask?.FEChannels && hotfixTask?.FEChannels?.length > 0
          ? hotfixTask?.FEChannels?.join(' | ')
          : '',
      ),
      line('是否全量版本热修', (!hotfixTask?.isFE && hotfixTask?.isFullReleaseVersion) ?? true ? '是' : '否'),
      line('引入问题的MR', hotfixTask?.mrLinkUrl),
      line('修复代码运行阶段', hotfixRunningTimeText[hotfixTask?.startUpRunTimeType ?? HotfixRunningTimeType.Unkown]),
      line('修复方案', hotfixTask?.fixPlan),
      line('测试方案', hotfixTask?.testPlan),
      line('期待全量时间', convertToCustomFormat(hotfixTask?.desireFullTime)),
      line('业务QA', atEmails(hotfixTask?.businessQaEmails)),
    ]);
    elements.push({
      tag: CardElementTag.div,
      text: {
        tag: CardTextTag.lark_md,
        content: `审批通过，请${atEmails(ApprovalHotfixConfig[appId].owners?.qaPoc)}热修放量。`,
      },
    } as CardContentElement);
    baseCard.elements = elements;
    return baseCard;
  }

  async buildHotfixTaskRejected(approvalInfo?: ApprovalDetail) {
    const hotfixTask = approvalInfo?.hotfixTask;
    const appId = hotfixTask?.appId ?? -1;
    const productName = versionUtils.appIdToAppInfo(appId.toString())?.productName;
    const platform = versionUtils.appIdToAppInfo(appId.toString())?.platform;
    const versions = hotfixTask?.versions;
    const baseCard = this.buildBaseCard({
      title: `热修任务审批不通过`,
      template: CardTemplate.red,
      config: { enable_forward: false },
    });
    const business = hotfixTask?.business;

    const elements: CardElement[] = [];
    mdLines(elements, [
      line('热修任务名称', approvalInfo?.hotfixTask?.approvalName),
      line('应用', productName),
      line('版本', versions?.join(' | ') ?? '未知'),
      line('平台', platform ?? '未知'),
      line('业务', business ? ApprovalBusinessConfigs[business]?.name : '未知'),
      line('问题详情', hotfixTask?.problemDetail),
      line('问题类型', hotfixTask?.hotfixProblemType ? hotfixTypeText[hotfixTask?.hotfixProblemType] : '未知'),
      line('用户量级', hotfixTask?.userNumber),
      line('指标影响', hotfixTask?.indicatorImpact),
      line('收入影响', hotfixTask?.incomeImpact),
      line('能否通过线上配置解决', hotfixTask?.canSolveOnline ? '能' : '不能'),
      line('问题是否必现', hotfixTask?.isInevitable ? '是' : '否'),
      line('问题是否通过前端修复', hotfixTask?.isFE ? '是' : '否'),
      line(
        '需要放开的前端Channel名称',
        hotfixTask?.isFE && hotfixTask?.FEChannels && hotfixTask?.FEChannels?.length > 0
          ? hotfixTask?.FEChannels?.join(' | ')
          : '',
      ),
      line('是否全量版本热修', (!hotfixTask?.isFE && hotfixTask?.isFullReleaseVersion) ?? true ? '是' : '否'),
      line('引入问题的MR', hotfixTask?.mrLinkUrl),
      line('修复代码运行阶段', hotfixRunningTimeText[hotfixTask?.startUpRunTimeType ?? HotfixRunningTimeType.Unkown]),
      line('修复方案', hotfixTask?.fixPlan),
      line('测试方案', hotfixTask?.testPlan),
      line('期待全量时间', convertToCustomFormat(hotfixTask?.desireFullTime)),
      line('业务QA', atEmails(hotfixTask?.businessQaEmails)),
    ]);
    elements.push({
      tag: CardElementTag.div,
      text: {
        tag: CardTextTag.lark_md,
        content: `${atEmail(approvalInfo?.createUserEmail)} ${atEmails(ApprovalHotfixConfig[appId].owners?.rdPoc)} ${atEmails(ApprovalHotfixConfig[appId].owners?.qaPoc)}`,
      },
    } as CardContentElement);
    baseCard.elements = elements;
    return baseCard;
  }
  /** -----------------------------------------------------------------------  **/
  /** ------------------------------小版本------------------------------------  **/
  async buildFixVersionAssessment(approvalInfo?: ApprovalDetail) {
    const fixVersionTask = approvalInfo?.fixVersionTask;
    const appId = fixVersionTask?.appId ?? -1;
    const productName = versionUtils.appIdToAppInfo(appId.toString())?.productName;
    const platform = versionUtils.appIdToAppInfo(appId.toString())?.platform;
    const version = fixVersionTask?.desireVersion;
    const baseCard = this.buildBaseCard({
      title: `小版本评估`,
      template: CardTemplate.orange,
      config: { enable_forward: false },
    });
    const business = fixVersionTask?.business;

    const elements: CardElement[] = [];
    if (fixVersionTask?.isBugfixType) {
      mdLines(elements, [
        line('任务名称', approvalInfo?.fixVersionTask?.approvalName),
        line('应用', productName),
        line('平台', platform ?? '未知'),
        line('期望修复版本', version ?? '未知'),
        line('业务', business ? ApprovalBusinessConfigs[business]?.name : '未知'),
        line('问题详情', fixVersionTask?.problemDetail),
        line(
          '问题类型',
          fixVersionTask?.hotfixProblemType ? hotfixTypeText[fixVersionTask?.hotfixProblemType] : '未知',
        ),
        line('用户量级', fixVersionTask?.userNumber),
        line('指标影响', fixVersionTask?.indicatorImpact),
        line('收入影响', fixVersionTask?.incomeImpact),
        line('能否通过线上配置解决', fixVersionTask?.canSolveOnline ? '能' : '不能'),
        line('问题是否必现', fixVersionTask?.isInevitable ? '是' : '否'),
        line('引入问题的MR', fixVersionTask?.mrLinkUrl),
        line('修复方案', fixVersionTask?.fixPlan),
        line('测试方案', fixVersionTask?.testPlan),
        line('预期合入时间', convertToCustomFormat(fixVersionTask?.expectedIntegrationTime)),
        line('业务QA', atEmails(fixVersionTask?.businessQaEmails)),
      ]);
    } else {
      mdLines(elements, [
        line('任务名称', approvalInfo?.fixVersionTask?.approvalName),
        line('应用', productName),
        line('平台', platform ?? '未知'),
        line('业务', business ? ApprovalBusinessConfigs[business]?.name : '未知'),
        line('期望修复版本', version ?? '未知'),
        line('需求链接', fixVersionTask?.meegoUrl ?? '无'),
        line('预期合入时间', convertToCustomFormat(fixVersionTask?.expectedIntegrationTime)),
        line('业务QA', atEmails(fixVersionTask?.businessQaEmails)),
      ]);
    }
    elements.push({
      tag: CardElementTag.div,
      text: {
        tag: CardTextTag.lark_md,
        content: `${atEmail(approvalInfo?.createUserEmail)}发起小版本评估，请 ${atEmails(ApprovalFixVersionConfig[appId].owners.rdPoc)} ${atEmails(ApprovalFixVersionConfig[appId].owners.qaPoc)} 给出评估意见`,
      },
    } as CardContentElement);
    // 去审批按钮
    addViewButton(elements, '去审批', {
      pc_url: approvalDetailUrlPC(approvalInfo?.instanceCode),
      android_url: approvalDetailUrlMobile(approvalInfo?.instanceCode),
      ios_url: approvalDetailUrlMobile(approvalInfo?.instanceCode),
      url: approvalDetailUrlPC(approvalInfo?.instanceCode),
    });
    baseCard.elements = elements;
    return baseCard;
  }

  async buildFixVersionAssessmentApproved(approvalInfo?: ApprovalDetail, userEmails?: string[]) {
    const fixVersionTask = approvalInfo?.fixVersionTask;
    const appId = fixVersionTask?.appId ?? -1;
    const productName = versionUtils.appIdToAppInfo(appId.toString())?.productName;
    const platform = versionUtils.appIdToAppInfo(appId.toString())?.platform;
    const version = fixVersionTask?.desireVersion;
    const baseCard = this.buildBaseCard({
      title: `小版本审批`,
      template: CardTemplate.orange,
      config: { enable_forward: false },
    });
    const business = fixVersionTask?.business;

    const elements: CardElement[] = [];
    if (fixVersionTask?.isBugfixType) {
      mdLines(elements, [
        line('任务名称', approvalInfo?.fixVersionTask?.approvalName),
        line('应用', productName),
        line('平台', platform ?? '未知'),
        line('期望修复版本', version ?? '未知'),
        line('业务', business ? ApprovalBusinessConfigs[business]?.name : '未知'),
        line('问题详情', fixVersionTask?.problemDetail),
        line(
          '问题类型',
          fixVersionTask?.hotfixProblemType ? hotfixTypeText[fixVersionTask?.hotfixProblemType] : '未知',
        ),
        line('用户量级', fixVersionTask?.userNumber),
        line('指标影响', fixVersionTask?.indicatorImpact),
        line('收入影响', fixVersionTask?.incomeImpact),
        line('能否通过线上配置解决', fixVersionTask?.canSolveOnline ? '能' : '不能'),
        line('问题是否必现', fixVersionTask?.isInevitable ? '是' : '否'),
        line('引入问题的MR', fixVersionTask?.mrLinkUrl),
        line('修复方案', fixVersionTask?.fixPlan),
        line('测试方案', fixVersionTask?.testPlan),
        line('预期合入时间', convertToCustomFormat(fixVersionTask?.expectedIntegrationTime)),
        line('业务QA', atEmails(fixVersionTask?.businessQaEmails)),
      ]);
    } else {
      mdLines(elements, [
        line('任务名称', approvalInfo?.fixVersionTask?.approvalName),
        line('应用', productName),
        line('平台', platform ?? '未知'),
        line('业务', business ? ApprovalBusinessConfigs[business]?.name : '未知'),
        line('期望修复版本', version ?? '未知'),
        line('需求链接', fixVersionTask?.meegoUrl ?? '无'),
        line('预期合入时间', convertToCustomFormat(fixVersionTask?.expectedIntegrationTime)),
        line('业务QA', atEmails(fixVersionTask?.businessQaEmails)),
      ]);
    }
    elements.push({
      tag: CardElementTag.div,
      text: {
        tag: CardTextTag.lark_md,
        content: `${atEmail(approvalInfo?.createUserEmail)}发起增发小版本申请，请${atEmails(userEmails)}审批。`,
      },
    } as CardContentElement);
    // 去审批按钮
    addViewButton(elements, '去审批', {
      pc_url: approvalDetailUrlPC(approvalInfo?.instanceCode),
      android_url: approvalDetailUrlMobile(approvalInfo?.instanceCode),
      ios_url: approvalDetailUrlMobile(approvalInfo?.instanceCode),
      url: approvalDetailUrlPC(approvalInfo?.instanceCode),
    });
    baseCard.elements = elements;
    return baseCard;
  }

  // 插入评估summary
  async buildRetouchFixVersionSummary(approvalInfo?: ApprovalDetail, summaryInfo?: Map<string, any[]>) {
    const fixVersionTask = approvalInfo?.fixVersionTask;
    const appId = fixVersionTask?.appId ?? -1;
    const productName = versionUtils.appIdToAppInfo(appId.toString())?.productName;
    const platform = versionUtils.appIdToAppInfo(appId.toString())?.platform;
    const version = fixVersionTask?.desireVersion;
    const baseCard = this.buildBaseCard({
      title: `增发小版本评估意见汇总`,
      template: CardTemplate.green,
      config: { enable_forward: false },
    });
    const business = fixVersionTask?.business;
    const getAffectBusinessLines = (businessLines: string[]) => {
      const result: string[] = [];
      for (const businessLine of businessLines) {
        const businessLineConfig = RetouchApprovalBusinessConfigs[businessLine];
        if (businessLineConfig) {
          result.push(businessLineConfig.name);
        }
      }
      return result;
    };
    const businessLineKey = Object.values(approvalInfo?.fixVersionTask?.affectBusinessLine ?? {})?.reduce(
      (acc, cur) => {
        acc.push(...cur);
        return acc;
      },
      [],
    );
    const elements: CardElement[] = [];
    // // 基础信息
    // elements.push({
    //   tag: CardElementTag.div,
    //   text: {
    //     tag: CardTextTag.lark_md,
    //     content: '**需求详情**',
    //   },
    // } as CardContentElement);
    // mdLines(elements, [
    //   line('任务名称', approvalInfo?.fixVersionTask?.approvalName),
    //   line('应用', productName),
    //   line('平台', platform ?? '未知'),
    //   line('期望修复版本', version ?? '未知'),
    //   line('业务', business ? RetouchApprovalBusinessConfigs[business]?.name : '未知'),
    //   line('问题详情', fixVersionTask?.problemDetail),
    //   line('问题类型', fixVersionTask?.hotfixProblemType ? hotfixTypeText[fixVersionTask?.hotfixProblemType] : '未知'),
    //   line('用户量级', fixVersionTask?.userNumber),
    //   line('指标影响', fixVersionTask?.indicatorImpact),
    //   line('收入影响', fixVersionTask?.incomeImpact),
    //   line('能否通过线上配置解决', fixVersionTask?.canSolveOnline ? '能' : '不能'),
    //   line('问题是否必现', fixVersionTask?.isInevitable ? '是' : '否'),
    //   line('引入问题的MR', fixVersionTask?.mrLinkUrl),
    //   line('修复方案', fixVersionTask?.fixPlan),
    //   line('测试方案', fixVersionTask?.testPlan),
    //   line(
    //     '评估受影响的业务线',
    //     businessLineKey && businessLineKey.length > 0 ? getAffectBusinessLines(uniq(businessLineKey)).join(' | ') : '-',
    //   ),
    //   line('预期合入时间', convertToCustomFormat(fixVersionTask?.expectedIntegrationTime)),
    //   line('业务QA', atEmails(fixVersionTask?.businessQaEmails)),
    // ]);
    // 各环节评估comments
    const firstAssessmentTaskKeyName = 'cb76074e7498823b2ce069e42e811aa7'; // SOP标准判断
    const secondAssessmentTaskKeyName = '1d2e4ad2e88041e2a1876454484c8842'; // 小版本质量影响面判断
    const thirdAssessmentTaskKeyName = 'df7d292744af0054b079dcc8f2349809'; // 版本节奏影响判断
    const fourthAssessmentTaskKeyName = '08f790110c946298c6e12b5355c71433'; // 受影响的各方影响判断
    for (const key of summaryInfo?.keys() ?? []) {
      if (key === firstAssessmentTaskKeyName) {
        const firstSummaryInfo = (summaryInfo?.get(key) ?? []).filter(it => it?.userEmail && it?.comment);
        if (firstSummaryInfo && firstSummaryInfo.length > 0) {
          elements.push({
            tag: CardElementTag.div,
            text: {
              tag: CardTextTag.lark_md,
              content:
                approvalInfo?.fixVersionTask?.hotfixProblemType === HotfixProblemType.Experience ||
                approvalInfo?.fixVersionTask?.hotfixProblemType === HotfixProblemType.ProductData
                  ? '**产品收益判断**'
                  : '**SOP标准判断**',
            },
          } as CardContentElement);
          firstSummaryInfo.map(it =>
            elements.push({
              tag: CardElementTag.columnSet,
              columns: [
                {
                  tag: CardElementTag.column,
                  width: 'auto',
                  elements: [
                    {
                      tag: CardElementTag.markdown,
                      content: `<at email="${it.userEmail}"/>`,
                      text_align: 'left',
                      text_size: 'normal',
                    } as CardElement,
                  ],
                  vertical_align: 'top',
                } as CardColumnElement,
                {
                  tag: CardElementTag.column,
                  width: 'auto',
                  elements: [
                    {
                      tag: CardElementTag.markdown,
                      content: `给出的评估意见: ${it.comment}`,
                      text_align: 'left',
                      text_size: 'normal',
                    } as CardElement,
                  ],
                  vertical_align: 'top',
                } as CardColumnElement,
              ],
            } as CardColumnSetElement),
          );
        }
      }
      if (key === secondAssessmentTaskKeyName) {
        const secondSummaryInfo = (summaryInfo?.get(key) ?? []).filter(it => it?.userEmail && it?.comment);
        if (secondSummaryInfo && secondSummaryInfo.length > 0) {
          elements.push({
            tag: CardElementTag.div,
            text: {
              tag: CardTextTag.lark_md,
              content: '**小版本质量影响面判断**',
            },
          } as CardContentElement);
          secondSummaryInfo.map(it =>
            elements.push({
              tag: CardElementTag.columnSet,
              columns: [
                {
                  tag: CardElementTag.column,
                  width: 'auto',
                  elements: [
                    {
                      tag: CardElementTag.markdown,
                      content: `<at email="${it.userEmail}"/>`,
                      text_align: 'left',
                      text_size: 'normal',
                    } as CardElement,
                  ],
                  vertical_align: 'top',
                } as CardColumnElement,
                {
                  tag: CardElementTag.column,
                  width: 'auto',
                  elements: [
                    {
                      tag: CardElementTag.markdown,
                      content: `给出的评估意见: ${it.comment}`,
                      text_align: 'left',
                      text_size: 'normal',
                    } as CardElement,
                  ],
                  vertical_align: 'top',
                } as CardColumnElement,
              ],
            } as CardColumnSetElement),
          );
        }
      }
      if (key === thirdAssessmentTaskKeyName) {
        const thirdSummaryInfo = (summaryInfo?.get(key) ?? []).filter(it => it?.userEmail && it?.comment);
        if (thirdSummaryInfo && thirdSummaryInfo.length > 0) {
          elements.push({
            tag: CardElementTag.div,
            text: {
              tag: CardTextTag.lark_md,
              content: '**版本节奏影响判断**',
            },
          } as CardContentElement);
          thirdSummaryInfo.map(it =>
            elements.push({
              tag: CardElementTag.columnSet,
              columns: [
                {
                  tag: CardElementTag.column,
                  width: 'auto',
                  elements: [
                    {
                      tag: CardElementTag.markdown,
                      content: `<at email="${it.userEmail}"/>`,
                      text_align: 'left',
                      text_size: 'normal',
                    } as CardElement,
                  ],
                  vertical_align: 'top',
                } as CardColumnElement,
                {
                  tag: CardElementTag.column,
                  width: 'auto',
                  elements: [
                    {
                      tag: CardElementTag.markdown,
                      content: `给出的评估意见: ${it.comment}`,
                      text_align: 'left',
                      text_size: 'normal',
                    } as CardElement,
                  ],
                  vertical_align: 'top',
                } as CardColumnElement,
              ],
            } as CardColumnSetElement),
          );
        }
      }
      if (key === fourthAssessmentTaskKeyName) {
        const fourthSummaryInfo = (summaryInfo?.get(key) ?? []).filter(it => it?.userEmail && it?.comment);
        if (fourthSummaryInfo && fourthSummaryInfo.length > 0) {
          elements.push({
            tag: CardElementTag.div,
            text: {
              tag: CardTextTag.lark_md,
              content: '**受影响的各方影响判断**',
            },
          } as CardContentElement);
          fourthSummaryInfo.map(it =>
            elements.push({
              tag: CardElementTag.columnSet,
              columns: [
                {
                  tag: CardElementTag.column,
                  width: 'auto',
                  elements: [
                    {
                      tag: CardElementTag.markdown,
                      content: `<at email="${it.userEmail}"/>`,
                      text_align: 'left',
                      text_size: 'normal',
                    } as CardElement,
                  ],
                  vertical_align: 'top',
                } as CardColumnElement,
                {
                  tag: CardElementTag.column,
                  width: 'auto',
                  elements: [
                    {
                      tag: CardElementTag.markdown,
                      content: `给出的评估意见: ${it.comment}`,
                      text_align: 'left',
                      text_size: 'normal',
                    } as CardElement,
                  ],
                  vertical_align: 'top',
                } as CardColumnElement,
              ],
            } as CardColumnSetElement),
          );
        }
      }
    }
    baseCard.elements = elements;
    return baseCard;
  }

  async buildRetouchFixVersionCommonNodeApproved(
    approvalInfo?: ApprovalDetail,
    userEmails?: string[],
    nodeKeyName?: string,
  ) {
    const fixVersionTask = approvalInfo?.fixVersionTask;
    const appId = fixVersionTask?.appId ?? -1;
    const productName = versionUtils.appIdToAppInfo(appId.toString())?.productName;
    const platform = versionUtils.appIdToAppInfo(appId.toString())?.platform;
    const version = fixVersionTask?.desireVersion;
    const baseCard = this.buildBaseCard({
      title: `小版本审批`,
      template: CardTemplate.orange,
      config: { enable_forward: false },
    });
    const business = fixVersionTask?.business;
    const getAffectBusinessLines = (businessLines: string[]) => {
      const result: string[] = [];
      for (const businessLine of businessLines) {
        const businessLineConfig = RetouchApprovalBusinessConfigs[businessLine];
        if (businessLineConfig) {
          result.push(businessLineConfig.name);
        }
      }
      return result;
    };
    const businessLineKey = Object.values(approvalInfo?.fixVersionTask?.affectBusinessLine ?? {})?.reduce(
      (acc, cur) => {
        acc.push(...cur);
        return acc;
      },
      [],
    );
    const elements: CardElement[] = [];

    let messageInfo = '';
    let alertInfo = '';
    const preAlertInfo = "**请判断以下内容，**<font color='red'>**务必在审批意见中给出评估依据**</font>\n";
    switch (nodeKeyName) {
      case 'cb76074e7498823b2ce069e42e811aa7': // 一级审批节点名称，SOP标准判断
        messageInfo =
          fixVersionTask?.hotfixProblemType === HotfixProblemType.Experience ||
          fixVersionTask?.hotfixProblemType === HotfixProblemType.ProductData
            ? '**针对产品收益判断**给出评估意见。'
            : '**针对SOP标准判断**给出评估意见。';
        alertInfo =
          fixVersionTask?.hotfixProblemType === HotfixProblemType.Experience ||
          fixVersionTask?.hotfixProblemType === HotfixProblemType.ProductData
            ? '**产品收益判断**\n' +
              '1. 请确认本次改动的原因，提交人预估的产品影响面的准确性，包括影响指标、用户量级、收入影响等\n' +
              '2. 请确认该影响面有必要发起本次改动'
            : '';
        break;
      case '1d2e4ad2e88041e2a1876454484c8842': // 二级审批节点名称，小版本质量影响面判断
        messageInfo = '**针对小版本质量影响面判断**给出评估意见。';
        alertInfo =
          '**质量判断提示 - 所有研发**\n' +
          `1. 请确认本次改动业务方向为**${RetouchApprovalBusinessConfigs[approvalInfo?.hotfixTask?.business ?? ''].name}**，并评估对本业务的影响面
` +
          '2. 请评估本次改动受影响的其它业务方向，如没有填写无，并填写不受影响的评估依据；如果有，请选择具体的业务方向（可多选），并对每个方向受影响面进行详细描述，受影响业务方会加到后续的审批流中。\n' +
          '**质量判断提示 - 业务QA**\n' +
          '1. 请确认本次改动对业务的影响，结合研发影响面的判断，并根据测试覆盖情况，确认质量最终的风险结果和人力成本风险结果\n';
        break;
        break;
      case 'df7d292744af0054b079dcc8f2349809': // 三级审批节点名称，版本节奏影响判断
        messageInfo = '**针对版本节奏影响判断**给出评估意见。';
        alertInfo =
          '**版本节奏影响面判断提示 - 端QA**\n' +
          '1. 请确认本次改动对本次版本和下次版本节奏的影响，如没有填写无，并填写不受影响的评估依据；如有，请详细描述影响面';
        break;
      case '08f790110c946298c6e12b5355c71433': // 四级审批节点名称，受影响业务线判断
        messageInfo = '**针对受影响业务线判断**给出评估意见。';
        alertInfo =
          businessLineKey && businessLineKey.length > 0
            ? `经评估，本需求会对**${getAffectBusinessLines(uniq(businessLineKey)).join(' | ')}**方向有影响\n` +
              '1. 请根据需求及研发的判断，确认本业务影响面的准确性，并描述最终影响评估结果\n' +
              '2. 请确认影响已解决或风险可控，并详细描述解决方式'
            : '';
        break;
      case 'bf0f15b8ff0d785e96551167143104e8': // 最终审批节点名称，必要性判断
        messageInfo =
          fixVersionTask?.hotfixProblemType === HotfixProblemType.Experience ||
          fixVersionTask?.hotfixProblemType === HotfixProblemType.ProductData
            ? '请根据产品收益、质量&版本节奏影响面判断结果及受影响业务反馈结果 做最终的必要性审批。'
            : '请根据问题影响面、质量&版本节奏影响面判断结果及受影响业务反馈结果 做最终的必要性审批。';
        break;
      default:
        messageInfo = '';
        break;
    }

    elements.push({
      tag: CardElementTag.div,
      text: {
        tag: CardTextTag.lark_md,
        content: `${atEmail(approvalInfo?.createUserEmail)}发起增发小版本申请，请${atEmails(userEmails)}${messageInfo}`,
      },
    } as CardContentElement);
    if (alertInfo && alertInfo !== '') {
      elements.push({
        tag: CardElementTag.div,
        text: {
          tag: CardTextTag.lark_md,
          content: `${preAlertInfo + alertInfo}`,
        },
      } as CardContentElement);
    }
    elements.push({
      tag: CardElementTag.hr,
    } as CardElement);

    mdLines(elements, [
      line('任务名称', approvalInfo?.fixVersionTask?.approvalName),
      line('应用', productName),
      line('平台', platform ?? '未知'),
      line('期望修复版本', version ?? '未知'),
      line('业务', business ? RetouchApprovalBusinessConfigs[business]?.name : '未知'),
      line('问题详情', fixVersionTask?.problemDetail),
      line('问题类型', fixVersionTask?.hotfixProblemType ? hotfixTypeText[fixVersionTask?.hotfixProblemType] : '未知'),
      line('用户量级', fixVersionTask?.userNumber),
      line('指标影响', fixVersionTask?.indicatorImpact),
      line('收入影响', fixVersionTask?.incomeImpact),
      line('能否通过线上配置解决', fixVersionTask?.canSolveOnline ? '能' : '不能'),
      line('问题是否必现', fixVersionTask?.isInevitable ? '是' : '否'),
      line('引入问题的MR', fixVersionTask?.mrLinkUrl),
      line('修复方案', fixVersionTask?.fixPlan),
      line('测试方案', fixVersionTask?.testPlan),
      line(
        '评估受影响的业务线',
        businessLineKey && businessLineKey.length > 0 ? getAffectBusinessLines(uniq(businessLineKey)).join(' | ') : '-',
      ),
      line('预期合入时间', convertToCustomFormat(fixVersionTask?.expectedIntegrationTime)),
      line('业务QA', atEmails(fixVersionTask?.businessQaEmails)),
    ]);
    // 去审批按钮
    addViewButton(elements, '去审批', {
      pc_url: approvalDetailUrlPC(approvalInfo?.instanceCode),
      android_url: approvalDetailUrlMobile(approvalInfo?.instanceCode),
      ios_url: approvalDetailUrlMobile(approvalInfo?.instanceCode),
      url: approvalDetailUrlPC(approvalInfo?.instanceCode),
    });
    baseCard.elements = elements;
    return baseCard;
  }

  async buildFixVersionInsertSuccess(approvalInfo?: ApprovalDetail) {
    const fixVersionTask = approvalInfo?.fixVersionTask;
    const appId = fixVersionTask?.appId ?? -1;
    const productName = versionUtils.appIdToAppInfo(appId.toString())?.productName;
    const platform = versionUtils.appIdToAppInfo(appId.toString())?.platform;
    const version = fixVersionTask?.desireVersion;
    const createEmail = approvalInfo?.createUserEmail;
    const baseCard = this.buildBaseCard({
      title: `${productName} ${platform} ${version}增发小版本信息同步`,
      template: CardTemplate.red,
      config: { enable_forward: false },
    });
    let departmentName = '未知';
    if (createEmail) {
      const batch_get_id = await this.lark.batchGetUserId(UserIdType.openId, {
        emails: [createEmail],
      });
      const openId = batch_get_id[0].user_id;
      departmentName = await this.lark.getUserDepartment(openId);
    }
    const business = fixVersionTask?.business;

    const elements: CardElement[] = [];
    mdLines(elements, [
      line(
        '增发小版本原因',
        fixVersionTask?.isBugfixType
          ? approvalInfo?.fixVersionTask?.problemDetail
          : approvalInfo?.fixVersionTask?.background,
      ),
      line('责任人', `${atEmail(approvalInfo?.createUserEmail)}`),
      line('责任团队', departmentName),
      line('业务方向', business ? ApprovalBusinessConfigs[business]?.name : '未知'),
    ]);
    // 获取核心群额外知会的人
    const configService = useInject(AirplaneConfigService);
    let defaultEmails: string[] = [];
    if ([177502, 177501, 300602, 300601].includes(appId)) {
      const platformUserList = (await configService.queryConfigItem(
        '1775',
        VersionConfigKeys.coreGroupDefaultUsers,
      )) as User[];
      this.logger.info(`buildFixVersionInsertSuccess platformUserList: ${JSON.stringify(platformUserList)}`);
      const platformUserInfoList = await this.lark.batchGetUserInfo(
        platformUserList?.map(defaultUser => defaultUser.open_id) ?? [],
        UserIdType.openId,
      );
      this.logger.info(`buildFixVersionInsertSuccess platformUserInfoList: ${JSON.stringify(platformUserInfoList)}`);
      const platformUserEmailList = platformUserInfoList?.map((value: { email: string }) => value.email) ?? [];
      defaultEmails.push(...(platformUserEmailList ?? []));
      this.logger.info(`buildFixVersionInsertSuccess platformUserEmailList: ${JSON.stringify(platformUserEmailList)}`);
    }

    defaultEmails =
      defaultEmails
        ?.concat(ApprovalFixVersionConfig?.[appId]?.owners?.rdPoc ?? [])
        ?.concat(ApprovalFixVersionConfig?.[appId]?.owners?.qaPoc ?? [])
        ?.concat(ApprovalFixVersionConfig?.[appId]?.owners?.rdOwner ?? [])
        ?.concat(ApprovalFixVersionConfig?.[appId]?.owners?.qaOwner ?? []) ?? ([] as string[]);
    this.logger.info(`buildFixVersionInsertSuccess emails: ${JSON.stringify(defaultEmails)}`);
    elements.push({
      tag: CardElementTag.div,
      text: {
        tag: CardTextTag.lark_md,
        content: atEmails(uniq(defaultEmails)),
      },
    } as CardContentElement);
    baseCard.elements = elements;
    return baseCard;
  }

  async buildFixVersionApproved(approvalInfo?: ApprovalDetail) {
    const fixVersionTask = approvalInfo?.fixVersionTask;
    const appId = fixVersionTask?.appId ?? -1;
    const productName = versionUtils.appIdToAppInfo(appId.toString())?.productName;
    const platform = versionUtils.appIdToAppInfo(appId.toString())?.platform;
    const version = fixVersionTask?.desireVersion;
    const baseCard = this.buildBaseCard({
      title: `小版本新增审批通过`,
      template: CardTemplate.green,
      config: { enable_forward: false },
    });
    const business = fixVersionTask?.business;

    const elements: CardElement[] = [];
    if (fixVersionTask?.isBugfixType) {
      mdLines(elements, [
        line('任务名称', approvalInfo?.fixVersionTask?.approvalName),
        line('应用', productName),
        line('平台', platform ?? '未知'),
        line('期望修复版本', version ?? '未知'),
        line('业务', business ? ApprovalBusinessConfigs[business]?.name : '未知'),
        line('问题详情', fixVersionTask?.problemDetail),
        line(
          '问题类型',
          fixVersionTask?.hotfixProblemType ? hotfixTypeText[fixVersionTask?.hotfixProblemType] : '未知',
        ),
        line('用户量级', fixVersionTask?.userNumber),
        line('指标影响', fixVersionTask?.indicatorImpact),
        line('收入影响', fixVersionTask?.incomeImpact),
        line('能否通过线上配置解决', fixVersionTask?.canSolveOnline ? '能' : '不能'),
        line('问题是否必现', fixVersionTask?.isInevitable ? '是' : '否'),
        line('引入问题的MR', fixVersionTask?.mrLinkUrl),
        line('修复方案', fixVersionTask?.fixPlan),
        line('测试方案', fixVersionTask?.testPlan),
        line('预期合入时间', convertToCustomFormat(fixVersionTask?.expectedIntegrationTime)),
        line('业务QA', atEmails(fixVersionTask?.businessQaEmails)),
      ]);
    } else {
      mdLines(elements, [
        line('任务名称', approvalInfo?.fixVersionTask?.approvalName),
        line('应用', productName),
        line('平台', platform ?? '未知'),
        line('业务', business ? ApprovalBusinessConfigs[business]?.name : '未知'),
        line('期望修复版本', version ?? '未知'),
        line('需求链接', fixVersionTask?.meegoUrl ?? '无'),
        line('预期合入时间', convertToCustomFormat(fixVersionTask?.expectedIntegrationTime)),
        line('业务QA', atEmails(fixVersionTask?.businessQaEmails)),
      ]);
    }
    elements.push({
      tag: CardElementTag.div,
      text: {
        tag: CardTextTag.lark_md,
        content: `审批通过，${atEmails(ApprovalFixVersionConfig?.[appId]?.owners?.qaPoc)}。`,
      },
    } as CardContentElement);
    baseCard.elements = elements;
    return baseCard;
  }

  async buildFixVersionCreateSuccess(approvalInfo?: ApprovalDetail) {
    const fixVersionTask = approvalInfo?.fixVersionTask;
    const appId = fixVersionTask?.appId ?? -1;
    const productName = versionUtils.appIdToAppInfo(appId.toString())?.productName;
    const platform = versionUtils.appIdToAppInfo(appId.toString())?.platform;
    const version = fixVersionTask?.desireVersion;
    const baseCard = this.buildBaseCard({
      title: `小版本创建成功`,
      template: CardTemplate.green,
      config: { enable_forward: false },
    });
    const business = fixVersionTask?.business;

    const elements: CardElement[] = [];
    if (fixVersionTask?.isBugfixType) {
      mdLines(elements, [
        line('任务名称', approvalInfo?.fixVersionTask?.approvalName),
        line('应用', productName),
        line('平台', platform ?? '未知'),
        line('期望修复版本', version ?? '未知'),
        line('业务', business ? ApprovalBusinessConfigs[business]?.name : '未知'),
        line('问题详情', fixVersionTask?.problemDetail),
        line(
          '问题类型',
          fixVersionTask?.hotfixProblemType ? hotfixTypeText[fixVersionTask?.hotfixProblemType] : '未知',
        ),
        line('用户量级', fixVersionTask?.userNumber),
        line('指标影响', fixVersionTask?.indicatorImpact),
        line('收入影响', fixVersionTask?.incomeImpact),
        line('能否通过线上配置解决', fixVersionTask?.canSolveOnline ? '能' : '不能'),
        line('问题是否必现', fixVersionTask?.isInevitable ? '是' : '否'),
        line('引入问题的MR', fixVersionTask?.mrLinkUrl),
        line('修复方案', fixVersionTask?.fixPlan),
        line('测试方案', fixVersionTask?.testPlan),
        line('预期合入时间', convertToCustomFormat(fixVersionTask?.expectedIntegrationTime)),
        line('业务QA', atEmails(fixVersionTask?.businessQaEmails)),
      ]);
    } else {
      mdLines(elements, [
        line('任务名称', approvalInfo?.fixVersionTask?.approvalName),
        line('应用', productName),
        line('平台', platform ?? '未知'),
        line('业务', business ? ApprovalBusinessConfigs[business]?.name : '未知'),
        line('期望修复版本', version ?? '未知'),
        line('需求链接', fixVersionTask?.meegoUrl ?? '无'),
        line('预期合入时间', convertToCustomFormat(fixVersionTask?.expectedIntegrationTime)),
        line('业务QA', atEmails(fixVersionTask?.businessQaEmails)),
      ]);
    }
    elements.push({
      tag: CardElementTag.div,
      text: {
        tag: CardTextTag.lark_md,
        content: `小版本创建成功，${atEmails(ApprovalFixVersionConfig?.[appId]?.owners?.qaPoc)}。`,
      },
    } as CardContentElement);
    baseCard.elements = elements;
    return baseCard;
  }

  async buildFixVersionTaskRejected(approvalInfo?: ApprovalDetail) {
    const fixVersionTask = approvalInfo?.fixVersionTask;
    const appId = fixVersionTask?.appId ?? -1;
    const productName = versionUtils.appIdToAppInfo(appId.toString())?.productName;
    const platform = versionUtils.appIdToAppInfo(appId.toString())?.platform;
    const version = fixVersionTask?.desireVersion;
    const baseCard = this.buildBaseCard({
      title: `小版本新增审批不通过`,
      template: CardTemplate.red,
      config: { enable_forward: false },
    });
    const business = fixVersionTask?.business;

    const elements: CardElement[] = [];
    if (fixVersionTask?.isBugfixType) {
      mdLines(elements, [
        line('任务名称', approvalInfo?.fixVersionTask?.approvalName),
        line('应用', productName),
        line('平台', platform ?? '未知'),
        line('期望修复版本', version ?? '未知'),
        line('业务', business ? ApprovalBusinessConfigs[business]?.name : '未知'),
        line('问题详情', fixVersionTask?.problemDetail),
        line(
          '问题类型',
          fixVersionTask?.hotfixProblemType ? hotfixTypeText[fixVersionTask?.hotfixProblemType] : '未知',
        ),
        line('用户量级', fixVersionTask?.userNumber),
        line('指标影响', fixVersionTask?.indicatorImpact),
        line('收入影响', fixVersionTask?.incomeImpact),
        line('能否通过线上配置解决', fixVersionTask?.canSolveOnline ? '能' : '不能'),
        line('问题是否必现', fixVersionTask?.isInevitable ? '是' : '否'),
        line('引入问题的MR', fixVersionTask?.mrLinkUrl),
        line('修复方案', fixVersionTask?.fixPlan),
        line('测试方案', fixVersionTask?.testPlan),
        line('预期合入时间', convertToCustomFormat(fixVersionTask?.expectedIntegrationTime)),
        line('业务QA', atEmails(fixVersionTask?.businessQaEmails)),
      ]);
    } else {
      mdLines(elements, [
        line('任务名称', approvalInfo?.fixVersionTask?.approvalName),
        line('应用', productName),
        line('平台', platform ?? '未知'),
        line('业务', business ? ApprovalBusinessConfigs[business]?.name : '未知'),
        line('期望修复版本', version ?? '未知'),
        line('需求链接', fixVersionTask?.meegoUrl ?? '无'),
        line('预期合入时间', convertToCustomFormat(fixVersionTask?.expectedIntegrationTime)),
        line('业务QA', atEmails(fixVersionTask?.businessQaEmails)),
      ]);
    }
    elements.push({
      tag: CardElementTag.div,
      text: {
        tag: CardTextTag.lark_md,
        content: `${atEmail(approvalInfo?.createUserEmail)} ${atEmails(ApprovalHotfixConfig[appId].owners?.qaPoc)} ${atEmails(ApprovalHotfixConfig[appId].owners?.qaPoc)}`,
      },
    } as CardContentElement);
    baseCard.elements = elements;
    return baseCard;
  }

  /** -----------------------------------------------------------------------  **/
  async buildRequirementChangeApprovalInfo(approvalInfo?: ApprovalInfoTable) {
    function getRequirementChangeReasonText(type?: RequirementChangeActionType) {
      switch (type) {
        case RequirementChangeActionType.inverted:
        case RequirementChangeActionType.changeBeforeCodeBreeze:
        case RequirementChangeActionType.insertBeforeCodeBreeze:
          return {
            title: '需求变更',
            requirement: '变更需求',
            content: '变更内容',
            reason: '变更原因',
            impact: '变更影响',
            scheduling: '更新后的排期',
            version: '',
          };
        case RequirementChangeActionType.insert:
        default:
          return {
            title: '需求插入',
            requirement: '插入需求',
            content: '插入需求内容',
            reason: '插入原因',
            impact: '插入影响',
            scheduling: '更新后的排期',
            version: '插入版本',
          };
      }
    }

    const type = approvalInfo?.requirementChangeInfo?.type;
    const textConfig = getRequirementChangeReasonText(type);

    const baseCard = this.buildBaseCard({
      title: `${textConfig.title}审批通过周知`,
      template: CardTemplate.green,
      config: { enable_forward: false },
    });
    const elements: CardElement[] = [];
    const productNames = versionUtils.getProductName(approvalInfo?.requirementChangeInfo?.appIds);
    const versions = versionUtils.getApprovalVersions(
      approvalInfo?.requirementChangeInfo?.version,
      approvalInfo?.requirementChangeInfo?.appIds,
    );
    elements.push({
      tag: CardElementTag.div,
      text: {
        tag: CardTextTag.lark_md,
        content: `【${textConfig.requirement}】：${trimTrailingNewline(approvalInfo?.meegoLinks?.[0]?.link?.url)}`,
      },
    } as CardContentElement);
    elements.push({
      tag: CardElementTag.div,
      text: {
        tag: CardTextTag.lark_md,
        content: `【${textConfig.content}】：${trimTrailingNewline(approvalInfo?.requirementChangeInfo?.content)}`,
      },
    } as CardContentElement);
    elements.push({
      tag: CardElementTag.div,
      text: {
        tag: CardTextTag.lark_md,
        content: `【${textConfig.reason}】：${trimTrailingNewline(approvalInfo?.requirementChangeInfo?.reason)}`,
      },
    } as CardContentElement);
    elements.push({
      tag: CardElementTag.div,
      text: {
        tag: CardTextTag.lark_md,
        content: `【${textConfig.impact}】：${trimTrailingNewline(approvalInfo?.requirementChangeInfo?.impact)}`,
      },
    } as CardContentElement);
    if (productNames) {
      elements.push({
        tag: CardElementTag.div,
        text: {
          tag: CardTextTag.lark_md,
          content: `【应用】：${productNames}`,
        },
      } as CardContentElement);
    }
    if (versions) {
      elements.push({
        tag: CardElementTag.div,
        text: {
          tag: CardTextTag.lark_md,
          content: `【版本】：${versions}`,
        },
      } as CardContentElement);
    }
    if (textConfig.version.length > 0) {
      elements.push({
        tag: CardElementTag.div,
        text: {
          tag: CardTextTag.lark_md,
          content: `【${textConfig.version}】：${approvalInfo?.version}`,
        },
      } as CardContentElement);
    }
    elements.push({
      tag: CardElementTag.div,
      text: {
        tag: CardTextTag.lark_md,
        content: `【平台】：${versionUtils.getPlatformName(approvalInfo?.requirementChangeInfo?.appIds)}`,
      },
    } as CardContentElement);
    elements.push({
      tag: CardElementTag.div,
      text: {
        tag: CardTextTag.lark_md,
        content: `【${textConfig.scheduling}】：${trimTrailingNewline(approvalInfo?.requirementChangeInfo?.scheduling)}`,
      },
    } as CardContentElement);
    const notifyUsersEmails = approvalInfo?.requirementChangeInfo?.notifyUsersEmails;
    if (notifyUsersEmails && notifyUsersEmails.length > 0) {
      const atUserEmails = notifyUsersEmails.reduce(
        (previousValue, currentValue) => `${previousValue} <at email="${currentValue}"/>`,
        '',
      );
      this.logger.info(
        `buildRequirementChangeApprovalInfo notifyUsersEmails => ${JSON.stringify(notifyUsersEmails)} ${JSON.stringify(atUserEmails)}`,
      );
      elements.push({
        tag: CardElementTag.div,
        text: {
          tag: CardTextTag.lark_md,
          content: atUserEmails,
        },
      } as CardContentElement);
    }
    baseCard.elements = elements;
    return baseCard;
  }

  // 发送到安卓测试群
  async buildGrayInfoCardForTestGroup(
    product: ProductType,
    version: string,
    versionCode: string,
    gray_count: number,
    isFirstTime: boolean,
    versionProcess: VersionProcess | null,
    mrInfos: MrSnapshot,
    veBmInfos: Duty[],
    install_amount: number | -1,
    gray_info: VersionState | undefined,
  ) {
    const baseCard = this.buildBaseCard({
      title: `${product === LVProductType.lv ? '剪映' : 'CapCut'} ${PlatformType.Android} ${version} 灰度信息同步`,
      template: CardTemplate.green,
      config: { enable_forward: false },
    });

    const lv_init_release: string[] = ['3k', '1w', '1w', '1w', '1w'];
    const cc_init_release: string[] = ['1%', '2%', '2%', '2%'];

    const elements: CardElement[] = [];

    let mr_content = '主要更新:<br/>';
    if (mrInfos !== undefined && mrInfos !== null && mrInfos.mrListMerged) {
      for (const detail of mrInfos.mrListMerged) {
        mr_content = `${mr_content}<a href=${detail.mr_detail_url}>${detail.title}</a><br/>`;
      }
    }

    const veBmEmails: string[] = [];

    for (const veBmInfo of veBmInfos) {
      if (veBmInfo.module === 'Android拍摄' || veBmInfo.module === 'Android编辑') {
        veBmEmails.push(veBmInfo.owner.concat('@bytedance.com'));
      }
    }

    let rdBmEmail = '';

    if (versionProcess) {
      rdBmEmail = versionProcess.bmInfo[BmType.rd].email;
    }
    const emails = [...veBmEmails, rdBmEmail];

    if (product === LVProductType.lv) {
      if (isFirstTime) {
        elements.push({
          tag: CardElementTag.div,
          text: {
            tag: CardTextTag.plain_text,
            content: `${version}版本已发布第${gray_count}轮灰度（${versionCode}）,初始放量${
              gray_count > lv_init_release.length
                ? lv_init_release[lv_init_release.length - 1]
                : lv_init_release[gray_count - 1]
            }，稳定后加量`,
          },
        } as CardContentElement);

        elements.push({
          tag: CardElementTag.div,
          text: {
            tag: CardTextTag.lark_md,
            content: `<at email=${rdBmEmail}></at><at email="<EMAIL>"/>`,
          },
        } as CardContentElement);

        elements.push({
          tag: CardElementTag.div,
          text: {
            tag: CardTextTag.lark_md,
            content: mr_content,
          },
        } as CardContentElement);
      } else {
        elements.push({
          tag: CardElementTag.div,
          text: {
            tag: CardTextTag.lark_md,
            content: `${version}版本第${gray_count}轮灰度（${versionCode}）灰度情况`,
          },
        } as CardContentElement);

        elements.push({
          tag: CardElementTag.div,
          text: {
            tag: CardTextTag.lark_md,
            content: emails.map(e => `<at email=${e}></at>`).join(''),
          },
        } as CardContentElement);

        elements.push({
          tag: CardElementTag.div,
          text: {
            tag: CardTextTag.lark_md,
            content: `${
              install_amount !== -1 ? `目前安装量${install_amount}<br/>` : ''
            }<a href=${this.buildPaperSlardarUrl('剪映', versionCode)} />`,
          },
        } as CardContentElement);
      }
    } else {
      if (isFirstTime) {
        elements.push({
          tag: CardElementTag.div,
          text: {
            tag: CardTextTag.plain_text,
            content: `CC${version
              .split('.')
              .join('')}第${gray_count}轮灰度（${versionCode}）已提交发布，审核通过后放量，初始放量比例${
              cc_init_release[Math.min(gray_count, cc_init_release.length) - 1]
            },稳定后加量`,
          },
        } as CardContentElement);

        elements.push({
          tag: CardElementTag.div,
          text: {
            tag: CardTextTag.lark_md,
            content: `<at email=${rdBmEmail}></at><at email="<EMAIL>"/>`,
          },
        } as CardContentElement);

        elements.push({
          tag: CardElementTag.div,
          text: {
            tag: CardTextTag.lark_md,
            content: mr_content,
          },
        } as CardContentElement);
      } else {
        elements.push({
          tag: CardElementTag.div,
          text: {
            tag: CardTextTag.lark_md,
            content: `CC${version}版本第${gray_count}轮灰度（${versionCode}）灰度情况`,
          },
        } as CardContentElement);

        elements.push({
          tag: CardElementTag.div,
          text: {
            tag: CardTextTag.lark_md,
            content: emails.map(e => `<at email=${e}></at>`).join(''),
          },
        } as CardContentElement);

        let cur_release_rate = '';
        if (gray_info?.release_rates && gray_info.release_rates.length > 0) {
          cur_release_rate = gray_info.release_rates[gray_info.release_rates.length - 1];
        }
        elements.push({
          tag: CardElementTag.div,
          text: {
            tag: CardTextTag.lark_md,
            content: `${
              cur_release_rate !== '' ? `放量${cur_release_rate}<br/>` : ''
            }<a href=${this.buildPaperSlardarUrl('Capcut', versionCode)} />`,
          },
        } as CardContentElement);
      }
    }

    elements.push({
      tag: CardElementTag.div,
      text: {
        tag: CardTextTag.plain_text,
        content: '消息来自：纸飞机自动触发',
      },
    } as CardContentElement);

    baseCard.elements = elements;

    return baseCard;
  }

  buildBranchDiffCard(
    title: string,
    diffList: string[],
    bmInfo: BmInfo[],
    featureBranch: string,
    targetBranch: string,
  ): Card {
    const baseCard = this.buildBaseCard({
      title: `${title}`,
      template: CardTemplate.blue,
      config: { enable_forward: false },
    });
    const elements: CardElement[] = [];
    elements.push({
      tag: CardElementTag.div,
      text: {
        tag: CardTextTag.lark_md,
        content: `**${featureBranch}进${targetBranch}还有diff**\n${diffList}`,
      },
    } as CardContentElement);
    elements.push({
      tag: CardElementTag.action,
      actions: [
        {
          tag: CardElementTag.button,
          text: {
            tag: CardTextTag.plain_text,
            content: '创建MR(默认多端)',
          },
          type: CardButtonType.primary,
          value: {
            cardCallbackType: CardCallbackType.CreateMR,
            sourceBranch: featureBranch,
            targetBranch,
          },
        } as CardButtonAction,
      ],
    } as CardActionElement);
    elements.push({
      tag: CardElementTag.action,
      actions: [
        {
          tag: CardElementTag.button,
          text: {
            tag: CardTextTag.plain_text,
            content: '创建MR(仅Android)',
          },
          type: CardButtonType.primary,
          value: {
            cardCallbackType: CardCallbackType.CreateMR,
            sourceBranch: featureBranch,
            targetBranch,
            platform: PlatformType.Android,
          },
        } as CardButtonAction,
      ],
    } as CardActionElement);
    elements.push({
      tag: CardElementTag.action,
      actions: [
        {
          tag: CardElementTag.button,
          text: {
            tag: CardTextTag.plain_text,
            content: '创建MR(仅iOS)',
          },
          type: CardButtonType.primary,
          value: {
            cardCallbackType: CardCallbackType.CreateMR,
            sourceBranch: featureBranch,
            targetBranch,
            platform: PlatformType.iOS,
          },
        } as CardButtonAction,
      ],
    } as CardActionElement);
    baseCard.elements = elements;
    return baseCard;
  }

  /**
   * 向RD个人推送版本缺陷修复率预警
   * @param version 10.7.0
   * @param versionName CC-iOS-8.9.0
   * @param title
   * @param detail
   * @param getP0IssueDDL
   * @param getP1IssueDDL
   * @param getP2IssueDDL
   */
  async buildIssueRiskByRdCard(
    version: string,
    versionName: string,
    title: string,
    detail: RdIssueDetail,
    getP0IssueDDL: (startTime: number) => Promise<string>,
    getP1IssueDDL: (startTime: number) => Promise<string>,
    getP2IssueDDL: (startTime: number) => Promise<string>,
  ): Promise<Card> {
    const baseCard = this.buildBaseCard({
      title: `${title}`,
      template: CardTemplate.red,
      config: { enable_forward: false },
    });
    const elements: CardElement[] = [];
    // 8.5.0存在Bug未及时修复，有造成版本延期的风险，请及时修复
    elements.push({
      tag: CardElementTag.div,
      text: {
        tag: CardTextTag.lark_md,
        content: `**${detail.nameCN}** 在当前版本仍存在Bug未及时修复，有造成版本延期的风险，请及时修复`,
      },
    } as CardContentElement);
    if (detail.unFixedP0P1Count > 0) {
      // https://meego.feishu.cn/faceu/issue/detail/
      const p0p1IdList = detail.p0p1Ids;
      let p0p1BugList = '';
      for (const key1 in p0p1IdList) {
        let ddlTime = '';
        if (p0p1IdList[key1].priority === 'P0' || p0p1IdList[key1].securityLabel === 'P0') {
          ddlTime = await getP0IssueDDL(p0p1IdList[key1].startTime);
        } else {
          ddlTime = await getP1IssueDDL(p0p1IdList[key1].startTime);
        }
        p0p1BugList = p0p1BugList.concat(
          `[P0P1-${p0p1IdList[key1].stateKey}-${p0p1IdList[key1].name}](https://meego.feishu.cn/faceu/issue/detail/${key1}) DDL:${ddlTime}\n`,
        );
      }
      elements.push({
        tag: CardElementTag.div,
        text: {
          tag: CardTextTag.lark_md,
          content: `**P0P1高优bug还剩${detail.unFixedP0P1Count}个未修复**`,
        },
      } as CardContentElement);
      elements.push({
        tag: CardElementTag.div,
        text: {
          tag: CardTextTag.lark_md,
          content: p0p1BugList,
        },
      } as CardContentElement);
    }
    if (detail.unFixedP2CountNeed > 0) {
      const p2IdList = detail.p2Ids;
      let p2BugList = '';
      for (const key2 in p2IdList) {
        const ddlTime = await getP2IssueDDL(p2IdList[key2].startTime);
        p2BugList = p2BugList.concat(
          // 醒图P2问题暂不定DDL
          globalBusinessType() !== BusinessType.Retouch
            ? `[P2-${p2IdList[key2].stateKey}-${p2IdList[key2].name}](https://meego.feishu.cn/faceu/issue/detail/${key2}) DDL:${ddlTime}\n`
            : `[P2-${p2IdList[key2].stateKey}-${p2IdList[key2].name}](https://meego.feishu.cn/faceu/issue/detail/${key2})\n`,
        );
      }
      elements.push({
        tag: CardElementTag.div,
        text: {
          tag: CardTextTag.lark_md,
          content: `P2bug总共有${detail.p2Count}个，应修${detail.shouldFixP2Count}个，**还剩${detail.unFixedP2CountNeed}个未修复**，从下面bug列表**任选${detail.unFixedP2CountNeed}个**修复即可`,
        },
      } as CardContentElement);
      elements.push({
        tag: CardElementTag.div,
        text: {
          tag: CardTextTag.lark_md,
          content: p2BugList,
        },
      } as CardContentElement);
    }
    if (detail.unFixedP0P1Count + detail.unFixedP2CountNeed > 5) {
      elements.push({
        tag: CardElementTag.div,
        text: {
          tag: CardTextTag.lark_md,
          content: `**剩余Bug数量大于5个，请及时联系模块owner协调人力分担。**`,
        },
      } as CardContentElement);
    }
    baseCard.elements = elements;
    return baseCard;
  }

  /**
   * 将版本的个人解决率推送到群
   */
  async buildAllIssueRiskByRdCard(
    title: string,
    version: string,
    versionName: string,
    allInfo: RdIssueDetail[],
    getP0IssueDDL: (startTime: number) => Promise<string>,
    getP1IssueDDL: (startTime: number) => Promise<string>,
    getP2IssueDDL: (startTime: number) => Promise<string>,
  ): Promise<Card> {
    const baseCard = this.buildBaseCard({
      title: `【${versionName}】${title}`,
      template: CardTemplate.red,
      config: { enable_forward: false },
    });
    const elements: CardElement[] = [];
    allInfo = allInfo.sort(
      (a, b) => a.unFixedP2CountNeed + a.unFixedP0P1Count - (b.unFixedP2CountNeed + b.unFixedP0P1Count),
    );
    for (const info of allInfo) {
      let text = `**<at email=${info.email}></at>**:`;
      if (info.unFixedP0P1Count > 0) {
        text = text.concat(`P0P1高优Bug还有${info.unFixedP0P1Count}个未修复. `);
      }
      if (info.unFixedP2CountNeed > 0) {
        text = text.concat(`P2-Bug还有${info.unFixedP2CountNeed}个未修复.`);
      }
      const p0p1IdList = info.p0p1Ids;
      for (const idElement in p0p1IdList) {
        let ddl = '';
        if (p0p1IdList[idElement].priority === 'P0' || p0p1IdList[idElement].securityLabel === 'P0') {
          ddl = await getP0IssueDDL(p0p1IdList[idElement].startTime);
        } else {
          ddl = await getP1IssueDDL(p0p1IdList[idElement].startTime);
        }
        text = text.concat(`[P0P1-${idElement}](https://meego.feishu.cn/faceu/issue/detail/${idElement}) DDL:${ddl} `);
      }
      const p2IdList = info.p2Ids;
      for (const idElement in p2IdList) {
        const ddl = await getP2IssueDDL(p2IdList[idElement].startTime);
        text = text.concat(
          // 醒图P2问题暂不定DDL
          globalBusinessType() !== BusinessType.Retouch
            ? `[P2-${idElement}](https://meego.feishu.cn/faceu/issue/detail/${idElement}) DDL:${ddl} `
            : `[P2-${idElement}](https://meego.feishu.cn/faceu/issue/detail/${idElement}) `,
        );
      }
      elements.push({
        tag: CardElementTag.div,
        text: {
          tag: CardTextTag.lark_md,
          content: text,
        },
      } as CardContentElement);
    }
    baseCard.elements = elements;
    return baseCard;
  }

  /**
   * 发送错误翻译
   */
  buildErrorTransListCard(
    allInfo: {
      [key: string]: {
        [kind: string]: string[];
      };
    },
    username?: string,
  ) {
    const baseCard = this.buildBaseCard({
      title: `[警告]文案更新异常`,
      template: CardTemplate.red,
      config: { enable_forward: false },
    });
    const elements: CardElement[] = [];
    if (username) {
      elements.push({
        tag: CardElementTag.markdown,
        content: `<font color=red>**更新文案存在以下异常，请**</font>**<at email=${username}@bytedance.com></at>**<font color=red>**及时关注,可能造成crash等风险**</font>`,
      } as CardMarkdownElement);
      elements.push({
        tag: CardElementTag.hr,
      } as CardContentElement);
    }
    for (const kind in allInfo) {
      elements.push({
        tag: CardElementTag.div,
        text: {
          tag: CardTextTag.lark_md,
          content: `❌ **异常类型** : ${kind}`,
        },
      } as CardContentElement);
      let content = '';
      for (const key in allInfo[kind]) {
        const set = new Set(allInfo[kind][key]);
        content += `**${key}** : (${[...set].join(', ')})\n`;
      }
      elements.push({
        tag: CardElementTag.div,
        text: {
          tag: CardTextTag.lark_md,
          content,
        },
      } as CardContentElement);
      elements.push({
        tag: CardElementTag.hr,
      } as CardContentElement);
    }
    baseCard.elements = elements;
    return baseCard;
  }

  buildUselessSetttingsInfoCard(
    chatPocs: string[],
    meegoInfos: UselessSettingMeegoInfo[],
    docsInfos: UselessDocsInfo[],
  ): Card {
    const baseCard = this.buildBaseCard({
      title: `【剪映&CC】固化/过期实验 Setting 定期扫描结果`,
      template: CardTemplate.green,
      config: { enable_forward: false },
    });

    const elements: CardElement[] = [];
    const ownerEmails = chatPocs.map(email => `**<at email=${email}></at>**`);

    elements.push({
      tag: CardElementTag.div,
      text: {
        tag: CardTextTag.lark_md,
        content: ownerEmails.join(' '),
      },
    } as CardContentElement);

    elements.push({
      tag: CardElementTag.div,
      text: {
        tag: CardTextTag.lark_md,
        content: '**固化/过期实验扫描结果文档汇总：**',
      },
    } as CardContentElement);

    let docsContent = '';
    for (const docInfo of docsInfos) {
      const docsLink = `[${docInfo.app_prefix} 固化/过期实验扫描结果文档](${docInfo.file_url}):`;
      docsContent += `${docsLink}\n • 总固化/过期 Settings 数：${docInfo.all_count}个。\n • iOS 使用中 Settings 数：${docInfo.ios_count}个。\n`;
    }
    elements.push({
      tag: CardElementTag.div,
      text: {
        tag: CardTextTag.lark_md,
        content: docsContent,
      },
    } as CardContentElement);

    elements.push({
      tag: CardElementTag.div,
      text: {
        tag: CardTextTag.lark_md,
        content: '**固化/过期实验 Meego 信息汇总：**',
      },
    } as CardContentElement);
    let content = '';
    for (const meegoInfo of meegoInfos) {
      if (meegoInfo.started_count === 0 && meegoInfo.doing_count === 0 && meegoInfo.done_count === 0) {
        continue;
      }
      const meegoViewLink = `[${meegoInfo.meego_view_prefix} Meego 视图](https://meego.feishu.cn/faceu/issueView/${meegoInfo.meego_view_id})`;
      content += `${meegoViewLink}, **其中：规划中：${meegoInfo.started_count} 个, 进行中：${meegoInfo.doing_count}个，完成： ${meegoInfo.done_count} 个 **。\n`;
    }
    elements.push({
      tag: CardElementTag.div,
      text: {
        tag: CardTextTag.lark_md,
        content,
      },
    } as CardContentElement);

    elements.push({
      tag: CardElementTag.div,
      text: {
        tag: CardTextTag.lark_md,
        content: `**【剪映/CC】无用 Settings 检测方案：** [技术方案](https://bytedance.feishu.cn/docx/E7k1dyajhoBkAvxjFtecnxXrnLb)`,
      },
    } as CardContentElement);

    baseCard.elements = elements;
    return baseCard;
  }

  buildMeegoDoNotHaveLibraCard(
    meegoId: number,
    meegoName: string,
    title: string,
    content: string,
    errorMsg = '',
    extraMention = '',
  ): Card {
    const baseCard = this.buildBaseCard({
      title,
      template: CardTemplate.red,
      config: { enable_forward: false },
    });
    const elements: CardElement[] = [];
    baseCard.elements = elements;
    elements.push({
      tag: CardElementTag.div,
      text: {
        tag: CardTextTag.lark_md,
        content,
      },
    } as CardContentElement);
    const meegoLink = `https://meego.larkoffice.com/faceu/story/detail/${meegoId}`;
    elements.push({
      tag: CardElementTag.div,
      text: {
        tag: CardTextTag.lark_md,
        content: `[${meegoName}](${meegoLink}) \n`,
      },
    } as CardContentElement);
    return baseCard;
  }

  buildMrReadyNotifyCard(
    mrInfo: MrInfo,
    template: CardTemplate,
    title: string,
    content: string,
    errorMsg = '',
    extraMention = '',
  ): Card {
    const baseCard = this.buildBaseCard({
      title,
      template,
      config: { enable_forward: false },
    });
    const elements: CardElement[] = [];
    baseCard.elements = elements;
    elements.push({
      tag: CardElementTag.div,
      text: {
        tag: CardTextTag.lark_md,
        content,
      },
    } as CardContentElement);
    elements.push({
      tag: CardElementTag.div,
      text: {
        tag: CardTextTag.lark_md,
        content: `[${mrInfo.title}](${mrInfo.mr_detail_url}) \nMR Owner:<at email="${mrInfo.author}@bytedance.com"></at> ${extraMention}`,
      },
    } as CardContentElement);
    if (errorMsg) {
      elements.push({
        tag: CardElementTag.hr,
      } as CardContentElement);
      elements.push({
        tag: CardElementTag.div,
        text: {
          tag: CardTextTag.lark_md,
          content: errorMsg,
        },
      } as CardContentElement);
      elements.push({
        tag: CardElementTag.action,
        actions: [
          {
            tag: CardElementTag.button,
            url: 'https://bytedance.larkoffice.com/sync/GN1ydUErTsjwvGbEvZocFW7EnYb',
            text: {
              tag: CardTextTag.plain_text,
              content: `🚨 异常需求上车`,
            },
            type: CardButtonType.primary,
          } as CardButtonAction,
        ],
      } as CardActionElement);
    }
    elements.push({
      tag: CardElementTag.note,
      elements: [
        {
          tag: CardTextTag.lark_md,
          content: `👉 [剪映 CapCut 封版流程](https://bytedance.larkoffice.com/wiki/MGUVweEfhiTqPik0URlcKKTQntd) `,
        },
      ],
    } as CardNoteElement);
    return baseCard;
  }

  buildPCAndRetouchMrReadyNotifyCard(
    mrInfo: MrInfo,
    template: CardTemplate,
    title: string,
    content: string,
    errorMsg = '',
    extraMention = '',
  ): Card {
    const baseCard = this.buildBaseCard({
      title,
      template,
      config: { enable_forward: false },
    });
    const elements: CardElement[] = [];
    baseCard.elements = elements;
    elements.push({
      tag: CardElementTag.div,
      text: {
        tag: CardTextTag.lark_md,
        content,
      },
    } as CardContentElement);
    elements.push({
      tag: CardElementTag.div,
      text: {
        tag: CardTextTag.lark_md,
        content: `[${mrInfo.title}](${mrInfo.mr_detail_url}) \nMR Owner:<at email="${mrInfo.author}@bytedance.com"></at> ${extraMention}`,
      },
    } as CardContentElement);
    if (errorMsg) {
      elements.push({
        tag: CardElementTag.hr,
      } as CardContentElement);
      elements.push({
        tag: CardElementTag.div,
        text: {
          tag: CardTextTag.lark_md,
          content: errorMsg,
        },
      } as CardContentElement);
    }
    return baseCard;
  }

  /**
   * 创建高优问题push卡片
   * @param meegoVersion
   * @param issueInfo
   */
  buildMajorIssueInfoCard(meegoVersion: string, issueInfo: MajorIssuePushInfo): Card {
    const baseCard = this.buildBaseCard({
      title: `${meegoVersion}阻塞性Bug加急`,
      template: CardTemplate.red,
      config: { enable_forward: false },
    });
    const elements: CardElement[] = [];
    elements.push({
      tag: CardElementTag.div,
      text: {
        tag: CardTextTag.lark_md,
        content: `该问题阻塞剪映/CC灰度发布，预计灰度打包时间今天19点(Android CC 18点)，稳定性问题请及时处理或拉起基础技术onCall协助，如已经在处理请点击我已在跟进，后续将不会收到加急提醒消息`,
      },
    } as CardContentElement);
    elements.push({
      tag: CardElementTag.div,
      text: {
        tag: CardTextTag.lark_md,
        content: `[${issueInfo.stateKey}-${issueInfo.priority}: ${issueInfo.name}](https://meego.feishu.cn/faceu/issue/detail/${issueInfo.meegoId})`,
      },
    } as CardContentElement);
    elements.push({
      tag: CardElementTag.div,
      text: {
        tag: CardTextTag.lark_md,
        content: `<at email=${issueInfo.email}></at>`,
      },
    } as CardContentElement);
    elements.push({
      tag: CardElementTag.action,
      actions: [
        {
          tag: CardElementTag.button,
          text: {
            tag: CardTextTag.plain_text,
            content: '请确认：我已在跟进',
          },
          type: CardButtonType.primary,
          value: {
            cardCallbackType: CardCallbackType.MajorIssueCheck,
            meegoVersion,
            meegoIssueId: issueInfo.meegoId.toString(),
          },
        } as CardButtonAction,
      ],
    } as CardActionElement);
    baseCard.elements = elements;
    return baseCard;
  }

  buildMajorIssueAnswerCard(meegoVersion: string, issueInfo: MajorIssuePushInfo): Card {
    const baseCard = this.buildBaseCard({
      title: `已收到高优缺陷跟进状态反馈`,
      template: CardTemplate.green,
      config: { enable_forward: false },
    });
    const elements: CardElement[] = [];
    elements.push({
      tag: CardElementTag.div,
      text: {
        tag: CardTextTag.lark_md,
        content: `后续将不会收到加急提醒消息，辛苦按时合入灰度发布分支，并扭转Meego平台缺陷状态。`,
      },
    } as CardContentElement);
    elements.push({
      tag: CardElementTag.div,
      text: {
        tag: CardTextTag.lark_md,
        content: `[${issueInfo.stateKey}-${issueInfo.priority}: ${issueInfo.name}](https://meego.feishu.cn/faceu/issue/detail/${issueInfo.meegoId})`,
      },
    } as CardContentElement);
    baseCard.elements = elements;
    return baseCard;
  }

  buildOnCallWikiTipCard(tip: string, docName: string, docLink: string): Card {
    const baseCard = this.buildBaseCard({
      title: `值班人温馨提示`,
      template: CardTemplate.green,
      config: { enable_forward: false },
    });
    const elements: CardElement[] = [];
    elements.push({
      tag: CardElementTag.div,
      text: {
        tag: CardTextTag.lark_md,
        content: `${tip}[${docName}](${docLink})`,
      },
    } as CardContentElement);
    baseCard.elements = elements;
    return baseCard;
  }

  buildOnCallTechnologyTipCard(originator_user: string): Card {
    const baseCard = this.buildBaseCard({
      title: `基础技术值班人温馨提示`,
      template: CardTemplate.green,
      config: { enable_forward: false },
    });
    const elements: CardElement[] = [];
    elements.push({
      tag: CardElementTag.div,
      text: {
        tag: CardTextTag.lark_md,
        content: `为了提高解决效率，请<at email=${originator_user}@bytedance.com></at>提供以下信息：`,
      },
    } as CardContentElement);
    elements.push({
      tag: CardElementTag.div,
      text: {
        tag: CardTextTag.lark_md,
        content: `1. 问题相关链接； `,
      },
    } as CardContentElement);
    elements.push({
      tag: CardElementTag.div,
      text: {
        tag: CardTextTag.lark_md,
        content: `2. 现阶段的分析数据和结论。`,
      },
    } as CardContentElement);
    baseCard.elements = elements;
    return baseCard;
  }

  buildVeCloudVersionUpdateCard(
    title: string,
    veDeliverTimes: number,
    mrUrl: string,
    loginName: string,
    qaName: string,
    rdName: string,
    testContent: string,
    lvVeVersion?: string,
    ccVeVersion?: string,
  ) {
    const baseCard = this.buildBaseCard({
      title,
      template: CardTemplate.green,
      config: { enable_forward: false },
    });
    const elements: CardElement[] = [];
    elements.push({
      tag: CardElementTag.div,
      text: {
        tag: CardTextTag.lark_md,
        content: `交付版本：剪映 ${lvVeVersion} & CC ${ccVeVersion}`,
      },
    } as CardContentElement);
    elements.push({
      tag: CardElementTag.div,
      text: {
        tag: CardTextTag.lark_md,
        content: `MR发起人：<at email=${loginName}@bytedance.com></at>，请<at email=${qaName}@bytedance.com></at><at email=${rdName}@bytedance.com></at>进行CodeReview。`,
      },
    } as CardContentElement);
    elements.push({ tag: CardElementTag.hr });
    elements.push({
      tag: CardElementTag.div,
      text: {
        tag: CardTextTag.lark_md,
        content: `交付内容：`,
      },
    } as CardContentElement);
    elements.push({
      tag: CardElementTag.div,
      text: {
        tag: CardTextTag.lark_md,
        content: testContent,
      },
    } as CardContentElement);
    elements.push({
      tag: CardElementTag.div,
      text: {
        tag: CardTextTag.lark_md,
        content: mrUrl,
      },
    } as CardContentElement);
    baseCard.elements = elements;
    return baseCard;
  }

  buildPCOrRetouchReleaseCheckoutInformCard(version: string, platform: PlatformType) {
    const baseCard = this.buildBaseCard({
      title: `[国内 ${platform}] release/${version} 分支已经拉出`,
      template: CardTemplate.green,
      config: { enable_forward: false },
    });
    const elements: CardElement[] = [];

    if (platform === PlatformType.Android) {
      elements.push({
        tag: CardElementTag.div,
        text: {
          tag: CardTextTag.lark_md,
          content: `**Retouch:** release/retouch/${version}\n**Retouch SDK:** release/retouch/${version}\n**Middleware:** release/retouch/${version}\n**lm-components:** release/retouch/${version}`,
        },
      } as CardElement);
    } else if (platform === PlatformType.iOS) {
      elements.push({
        tag: CardElementTag.div,
        text: {
          tag: CardTextTag.lark_md,
          content: `**iPhoto:** Release/${version}\n**rtsdk:** release/retouch/${version}\n**RetouchMiddleware:** release/retouch/${version}`,
        },
      } as CardElement);
    } else if (platform === PlatformType.PC) {
      elements.push({
        tag: CardElementTag.div,
        text: {
          tag: CardTextTag.lark_md,
          content: `**基于 develop 切出 release/${version}**`,
        },
      } as CardElement);
    }

    elements.push({
      tag: CardElementTag.div,
      text: {
        tag: CardTextTag.lark_md,
        content: `-后续相关提交请提交到新分支
-上个版本release分支合入到新release
-后续 bugfix 请修改对应的 target branch,`,
      },
    } as CardElement);

    elements.push({
      tag: CardElementTag.div,
      text: {
        tag: CardTextTag.lark_md,
        content: `<at id="all"></at>`,
      },
    } as CardElement);

    baseCard.elements = elements;

    return baseCard;
  }

  buildReleaseCheckoutInformCard(version: string, isCC: boolean) {
    const baseCard = this.buildBaseCard({
      title: `[${isCC ? '海外CapCut' : '国内剪映'}] ${isCC ? 'overseas/' : ''}release/${version} 分支已经拉出 [iOS Android]`,
      template: CardTemplate.green,
      config: { enable_forward: false },
    });
    const elements: CardElement[] = [];

    elements.push({
      tag: CardElementTag.div,
      text: {
        tag: CardTextTag.lark_md,
        content: `**基于 rc/develop 切出 ${isCC ? 'overseas/' : ''}release/${version}**`,
      },
    } as CardElement);

    elements.push({
      tag: CardElementTag.div,
      text: {
        tag: CardTextTag.lark_md,
        content: `-后续相关提交请提交到新分支
-上个版本release分支合入到新release
-后续 bugfix 请修改对应的 target branch,`,
      },
    } as CardElement);

    elements.push({
      tag: CardElementTag.div,
      text: {
        tag: CardTextTag.lark_md,
        content: `<at id="all"></at>`,
      },
    } as CardElement);

    baseCard.elements = elements;

    return baseCard;
  }

  /**
   * [海外] overseas/release/10.1.0 分支已经拉出 [iOS]
   * @param lvVersion
   * @param ccVersion
   */
  buildOverseasReleaseCheckoutInformCard(lvVersion: string, ccVersion: string) {
    const baseCard = this.buildBaseCard({
      title: `[海外] overseas/release/${ccVersion} 分支已经拉出 [iOS Android]`,
      template: CardTemplate.green,
      config: { enable_forward: false },
    });
    const elements: CardElement[] = [];
    elements.push({
      tag: CardElementTag.div,
      text: {
        tag: CardTextTag.lark_md,
        content: `**基于 release/${lvVersion} 切出 overseas/release/${ccVersion}**`,
      },
    } as CardElement);
    elements.push({
      tag: CardElementTag.div,
      text: {
        tag: CardTextTag.lark_md,
        content: `-后续相关提交请提交到新分支
-上个版本release分支合入到新release
-后续 bugfix 请修改对应的 target branch
备注：带 overseas 为 cc 分支`,
      },
    } as CardElement);
    elements.push({
      tag: CardElementTag.div,
      text: {
        tag: CardTextTag.lark_md,
        content: `<at id="all"></at>`,
      },
    } as CardElement);
    baseCard.elements = elements;
    return baseCard;
  }

  async buildCherryPickResultCard(
    results: Record<string, BitsResult<number>>,
    mrLink: string,
    requestMr: (mrId: number | undefined) => Promise<MrInfo | undefined>,
  ) {
    const baseCard = this.buildBaseCard({
      title: `Cherry Pick MR创建结果`,
      template: CardTemplate.green,
      config: { enable_forward: false },
    });

    const successBranches: string[] = [];
    const failBranches: string[] = [];
    for (const key in results) {
      if (results[key].code !== 200) {
        failBranches.push(key);
      } else {
        successBranches.push(key);
      }
    }

    const elements: CardElement[] = [];

    if (successBranches.length > 0) {
      elements.push({
        tag: CardElementTag.div,
        text: {
          tag: CardTextTag.lark_md,
          content: `***以下Cherry Pick MR创建成功：***`,
        },
      } as CardContentElement);

      for (const branch of successBranches) {
        const mrInfo: MrInfo | undefined = await requestMr(results[branch].data);
        if (mrInfo) {
          elements.push({
            tag: CardElementTag.div,
            text: {
              tag: CardTextTag.lark_md,
              content: `***${branch}***: <a href=${mrInfo.mr_detail_url}></a>`,
            },
          } as CardContentElement);
        }
      }
    }

    if (failBranches.length > 0) {
      elements.push({
        tag: CardElementTag.div,
        text: {
          tag: CardTextTag.lark_md,
          content: `***以下Cherry Pick MR创建失败：***`,
        },
      } as CardContentElement);

      for (const branch of failBranches) {
        elements.push({
          tag: CardElementTag.div,
          text: {
            tag: CardTextTag.lark_md,
            content: `***${branch}***: ${results[branch].message}`,
          },
          extra: {
            tag: CardElementTag.button,
            text: {
              tag: CardTextTag.lark_md,
              content: 'Retry',
            },
            type: CardButtonType.primary,
            value: {
              cardCallbackType: CardCallbackType.CreateCherryPickMR,
              mrLink,
              targetBranchesForCherryPick: branch,
            } as CardActionValue,
          },
        } as CardContentElement);
      }

      elements.push({
        tag: CardElementTag.action,
        actions: [
          {
            tag: CardElementTag.button,
            text: {
              tag: CardTextTag.plain_text,
              content: '一键重试',
            },
            type: CardButtonType.primary,
            value: {
              cardCallbackType: CardCallbackType.CreateCherryPickMR,
              mrLink,
              targetBranchesForCherryPick: failBranches.join('*'),
            } as CardActionValue,
          } as CardButtonAction,
        ],
      } as CardActionElement);
    }

    baseCard.elements = elements;
    return baseCard;
  }

  buildCherryPickInformCard(
    author: string,
    from_branch: string,
    target_branch: string,
    branchlists: string[],
    mrId: number,
    link: string,
    title: string,
  ) {
    const baseCard = this.buildBaseCard({
      title: `Cherry Pick MR发起提醒`,
      template: CardTemplate.green,
      config: { enable_forward: false },
    });
    const elements: CardElement[] = [];
    elements.push({
      tag: CardElementTag.div,
      text: {
        tag: CardTextTag.lark_md,
        content: `你的「<a href=${link}>${title}</a>」MR已成功合入 ${target_branch} 分支，请前往以下分支发起 Cherry Pick MR：`,
      },
    } as CardContentElement);
    for (const branch of branchlists) {
      elements.push({
        tag: CardElementTag.div,
        text: {
          tag: CardTextTag.lark_md,
          content: `***${branch}***`,
        },
        extra: {
          tag: CardElementTag.button,
          text: {
            tag: CardTextTag.lark_md,
            content: 'Cherry Pick',
          },
          type: CardButtonType.primary,
          value: {
            cardCallbackType: CardCallbackType.CreateCherryPickMR,
            mrLink: link,
            mrId: mrId.toString(),
            targetBranchesForCherryPick: branch,
          } as CardActionValue,
        },
      } as CardContentElement);
    }
    elements.push({
      tag: CardElementTag.div,
      text: {
        tag: CardTextTag.lark_md,
        content: `有疑问请查看：[纸飞机-MR回合提醒功能使用手册](https://bytedance.larkoffice.com/docx/GyBoduVhboM5r8xtNdcc7RfunIg)`,
      },
    } as CardContentElement);
    elements.push({
      tag: CardElementTag.action,
      actions: [
        {
          tag: CardElementTag.button,
          text: {
            tag: CardTextTag.plain_text,
            content: '一键合入',
          },
          type: CardButtonType.primary,
          value: {
            cardCallbackType: CardCallbackType.CreateCherryPickMR,
            mrLink: link,
            mrId: mrId.toString(),
            targetBranchesForCherryPick: branchlists.join('*'),
          } as CardActionValue,
        } as CardButtonAction,
      ],
    } as CardActionElement);
    baseCard.elements = elements;

    return baseCard;
  }

  async buildLVUniteAdMrCreateInformCard(
    mrId: number,
    sourceBranch: string,
    targetBranch: string,
    platforms: PlatformType[],
    mrInfo: MrInfo | undefined,
  ) {
    const baseCard = this.buildBaseCard({
      title: `LVUniteAd回流MR创建成功提醒`,
      template: CardTemplate.green,
      config: { enable_forward: false },
    });
    const elements: CardElement[] = [];

    elements.push({
      tag: CardElementTag.div,
      text: {
        tag: CardTextTag.lark_md,
        content: `[Platforms]: ${platforms.join('、')}`,
      },
    } as CardContentElement);

    elements.push({
      tag: CardElementTag.div,
      text: {
        tag: CardTextTag.lark_md,
        content: `[Branch]: ${sourceBranch} -> ${targetBranch}`,
      },
    } as CardContentElement);

    if (mrInfo) {
      elements.push({
        tag: CardElementTag.div,
        text: {
          tag: CardTextTag.lark_md,
          content: `[Link]: <a href=${mrInfo.mr_detail_url}></a>`,
        },
      } as CardContentElement);
    }

    baseCard.elements = elements;

    return baseCard;
  }

  async buildMrCreateInformCard(
    mrId: number,
    sourceBranch: string,
    targetBranch: string,
    platforms: PlatformType[],
    mrInfo: MrInfo | undefined,
  ) {
    const baseCard = this.buildBaseCard({
      title: `回流MR创建成功提醒`,
      template: CardTemplate.green,
      config: { enable_forward: false },
    });
    const elements: CardElement[] = [];

    elements.push({
      tag: CardElementTag.div,
      text: {
        tag: CardTextTag.lark_md,
        content: `[Platforms]: ${platforms.join('、')}`,
      },
    } as CardContentElement);

    elements.push({
      tag: CardElementTag.div,
      text: {
        tag: CardTextTag.lark_md,
        content: `[Branch]: ${sourceBranch} -> ${targetBranch}`,
      },
    } as CardContentElement);

    if (mrInfo) {
      elements.push({
        tag: CardElementTag.div,
        text: {
          tag: CardTextTag.lark_md,
          content: `[Link]: <a href=${mrInfo.mr_detail_url}></a>`,
        },
      } as CardContentElement);
    }

    baseCard.elements = elements;

    return baseCard;
  }

  async buildMrConflictInformCard(
    mrId: number,
    sourceBranch: string,
    targetBranch: string,
    platforms: PlatformType[],
    mrInfo: MrInfo | undefined,
    conflictData?: Map<string, ConflictFile>,
    bm?: string,
  ) {
    const baseCard = this.buildBaseCard({
      title: `回流MR冲突提醒`,
      template: CardTemplate.red,
      config: { enable_forward: false },
    });
    const elements: CardElement[] = [];

    elements.push({
      tag: CardElementTag.div,
      text: {
        tag: CardTextTag.lark_md,
        content: `[Platforms]: ${platforms.join('、')}`,
      },
    } as CardContentElement);

    elements.push({
      tag: CardElementTag.div,
      text: {
        tag: CardTextTag.lark_md,
        content: `[Branch]: ${sourceBranch} -> ${targetBranch}`,
      },
    } as CardContentElement);

    if (mrInfo) {
      elements.push({
        tag: CardElementTag.div,
        text: {
          tag: CardTextTag.lark_md,
          content: `[Link]: <a href=${mrInfo.mr_detail_url}></a>`,
        },
      } as CardContentElement);
    }

    if (bm) {
      elements.push({
        tag: CardElementTag.div,
        text: {
          tag: CardTextTag.lark_md,
          content: `BM: <at email=${bm}@bytedance.com></at>`,
        },
      } as CardContentElement);
    }

    if (conflictData && conflictData.size > 0) {
      const conflictString = [];
      for (const [filename, conflictFile] of conflictData.entries()) {
        const conflictLines = [];
        conflictLines.push(`<a href=${conflictFile.blameUrl}>${filename}</a>`);
        conflictFile.conflictBlock.forEach(block => {
          if (block.conflictLine && block.conflictLine.length > 0) {
            let sourceAuthorString = 'unknown';
            let targetAuthorString = 'unknown';
            let sourceAuthor = 'unknown';
            let targetAuthor = 'unknown';
            if (block.sourceAuthor !== '') {
              sourceAuthor = `${block.sourceAuthor}@bytedance.com`;
              sourceAuthorString = `<at email=${sourceAuthor}></at>`;
            }
            if (block.targetAuthor !== '') {
              targetAuthor = `${block.targetAuthor}@bytedance.com`;
              targetAuthorString = `<at email=${targetAuthor}></at>`;
            }
            conflictLines.push(
              `Line ${block.conflictLine[0].lineNumber} -> source: ${sourceAuthorString}, target: ${targetAuthorString}`,
            );
          }
        });
        const blockLine = conflictLines.join('；\n');
        const endString = `${blockLine}\n<hr>`;
        conflictString.push(endString);
      }
      const contentString = conflictString.join('\n');
      elements.push({
        tag: CardElementTag.div,
        text: {
          tag: CardTextTag.lark_md,
          content: `[Conflict]: ${contentString}`,
        },
      } as CardContentElement);
    }

    baseCard.elements = elements;

    return baseCard;
  }

  async buildMrTimeoutInformCard(
    mrId: number,
    sourceBranch: string,
    targetBranch: string,
    platforms: PlatformType[],
    mrInfo: MrInfo | undefined,
    bmList: string[],
  ) {
    const baseCard = this.buildBaseCard({
      title: `回流MR超时未合入提醒`,
      template: CardTemplate.red,
      config: { enable_forward: false },
    });
    const elements: CardElement[] = [];

    elements.push({
      tag: CardElementTag.div,
      text: {
        tag: CardTextTag.lark_md,
        content: `[Platforms]: ${platforms.join('、')}`,
      },
    } as CardContentElement);

    if (bmList) {
      const content = bmList.map(bm => `<at email=${bm}@bytedance.com></at>`).join(', ');
      elements.push({
        tag: CardElementTag.div,
        text: {
          tag: CardTextTag.lark_md,
          content,
        },
      } as CardContentElement);
    }

    elements.push({
      tag: CardElementTag.div,
      text: {
        tag: CardTextTag.lark_md,
        content: `[Branch]: ${sourceBranch} -> ${targetBranch}`,
      },
    } as CardContentElement);

    if (mrInfo) {
      elements.push({
        tag: CardElementTag.div,
        text: {
          tag: CardTextTag.lark_md,
          content: `[Link]: <a href=${mrInfo.mr_detail_url}></a>`,
        },
      } as CardContentElement);
    }

    baseCard.elements = elements;

    return baseCard;
  }

  async buildMrPipelineFailedInformCard(
    mrId: number,
    sourceBranch: string,
    targetBranch: string,
    platforms: PlatformType[],
    mrInfo: MrInfo | undefined,
  ) {
    const baseCard = this.buildBaseCard({
      title: `回流MR Pipeline 失败提醒`,
      template: CardTemplate.red,
      config: { enable_forward: false },
    });
    const elements: CardElement[] = [];

    elements.push({
      tag: CardElementTag.div,
      text: {
        tag: CardTextTag.lark_md,
        content: `[Platforms]: ${platforms.join('、')}`,
      },
    } as CardContentElement);

    elements.push({
      tag: CardElementTag.div,
      text: {
        tag: CardTextTag.lark_md,
        content: `[Branch]: ${sourceBranch} -> ${targetBranch}`,
      },
    } as CardContentElement);

    if (mrInfo) {
      elements.push({
        tag: CardElementTag.div,
        text: {
          tag: CardTextTag.lark_md,
          content: `[Link]: <a href=${mrInfo.mr_detail_url}></a>`,
        },
      } as CardContentElement);
    }

    baseCard.elements = elements;

    return baseCard;
  }

  async buildMrFinishInformCard(
    success: boolean,
    sourceBranch: string,
    targetBranch: string,
    platforms: PlatformType[],
    mrInfo: MrInfo | undefined,
  ) {
    const template = success ? CardTemplate.green : CardTemplate.red;
    const title = success ? '回流MR 完成提醒: Merged' : '回流MR 完成提醒: Closed';
    const baseCard = this.buildBaseCard({
      title,
      template,
      config: { enable_forward: false },
    });
    const elements: CardElement[] = [];

    elements.push({
      tag: CardElementTag.div,
      text: {
        tag: CardTextTag.lark_md,
        content: `[Platforms]: ${platforms.join('、')}`,
      },
    } as CardContentElement);

    elements.push({
      tag: CardElementTag.div,
      text: {
        tag: CardTextTag.lark_md,
        content: `[Branch]: ${sourceBranch} -> ${targetBranch}`,
      },
    } as CardContentElement);

    if (mrInfo) {
      elements.push({
        tag: CardElementTag.div,
        text: {
          tag: CardTextTag.lark_md,
          content: `[Link]: <a href=${mrInfo.mr_detail_url}></a>`,
        },
      } as CardContentElement);
    }

    baseCard.elements = elements;

    return baseCard;
  }

  buildChecklistInfoCard(
    checkListInfo: CheckListInfo,
    detail: CheckListProgressItem[],
    strings: string[],
    bmInfoElement: BuildMasterInfo,
  ) {
    let userIds = '';
    for (const element of strings) {
      userIds = userIds.concat(element).concat(',');
    }
    const baseCard = this.buildBaseCard({
      title: `${checkListInfo.product} ${checkListInfo.platform} ${checkListInfo.version} ${checkListInfo.versionStage} ${checkListInfo.grayStageName} BM checklist检查提醒`,
      template: CardTemplate.green,
      config: { enable_forward: false },
    });
    const mrGroupMap = groupBy(detail, value => value.templateDetail?.taskType);
    const elements: CardElement[] = [];
    elements.push({
      tag: CardElementTag.div,
      text: {
        tag: CardTextTag.lark_md,
        content: `**${checkListInfo.versionStage} 当前整体完成度为：${checkListInfo.stageProgress}%**，请版本BM **${bmInfoElement.nameCN}** 尽快完成以下工作项。`,
      },
    } as CardContentElement);
    let appId = '';
    if (checkListInfo.platform === PlatformType.Android) {
      appId = LVProductType.cc === checkListInfo.product ? '300602' : '177502';
    } else {
      appId = LVProductType.cc === checkListInfo.product ? '300601' : '177501';
    }
    for (const groupKey in mrGroupMap) {
      const checkList = mrGroupMap[groupKey].filter(value => !value.finished);
      if (checkList && checkList.length > 0) {
        let type = groupKey;
        if (groupKey === CheckListTaskType.prepare) {
          type = `<font color="green">prepare</font>`;
        }
        if (groupKey === CheckListTaskType.check) {
          type = `<font color="orange">check</font>`;
        }
        if (groupKey === CheckListTaskType.action) {
          type = `<font color="red">action</font>`;
        }
        elements.push({
          tag: CardElementTag.div,
          text: {
            tag: CardTextTag.lark_md,
            content: `**${type}**`,
          },
        } as CardContentElement);
        for (let i = 0; i < checkList.length; i++) {
          let doc = '';
          if (
            checkList[i].templateDetail?.docName &&
            checkList[i].templateDetail?.docLink &&
            checkList[i].templateDetail?.docLink?.startsWith('http')
          ) {
            doc = `[${checkList[i].templateDetail?.docName}](${checkList[i].templateDetail?.docLink})`;
          }
          elements.push({
            tag: CardElementTag.div,
            text: {
              tag: CardTextTag.lark_md,
              content: `**${checkList[i].templateDetail?.priority}** ${checkList[i].templateDetail?.taskDes} ${doc}`,
            },
            extra: {
              tag: CardElementTag.button,
              text: {
                tag: CardTextTag.lark_md,
                content: '确认已完成',
              },
              type: CardButtonType.primary,
              value: {
                cardCallbackType: CardCallbackType.UpdateCheckListInfo,
                product: checkListInfo.product,
                platform: checkListInfo.platform,
                version: checkListInfo.version,
                versionStage: checkListInfo.versionStage,
                grayNum: checkListInfo.grayStageName,
                templateId: checkList[i].templateId,
                userIds,
              } as CardActionValue,
            } as CardButtonAction,
          } as CardContentElement);
        }
        elements.push({
          tag: CardElementTag.hr,
        });
      }
    }
    const stage = checkListInfo.versionStage.includes('系统测试') ? '系统测试' : checkListInfo.versionStage;
    elements.push({
      tag: CardElementTag.div,
      text: {
        tag: CardTextTag.lark_md,
        content: `**在纸飞机查看当前版本的BM checklist详情：**[${checkListInfo.version}-${checkListInfo.versionStage} checklist详情](${MAIN_HOST_HTTPS}/releaseProcess/overview?appid=${appId}&version=${checkListInfo.version}&checklist=true&versionStage=${stage}&grayCount=${checkListInfo.grayStageName})`,
      },
    } as CardContentElement);
    baseCard.elements = elements;
    return baseCard;
  }

  libraPatrolCardJumpURL(data: ExperimentCard, info: ExperimentShareInfo): string {
    let appid = data.appId;
    // 简单的推断一下，是否是 Android 平台，则优先跳到 Android
    let isAndroid = false;
    if (info.name.includes('Android') || info.name.includes('android') || info.name.includes('安卓')) {
      isAndroid = true;
    } else if (info.meego_owners && 'Android' in info.meego_owners) {
      isAndroid = true;
    }
    // 处理一下 appid，确保能定位到具体的应用
    if (appid === 1775) {
      // 剪映 App
      appid = isAndroid ? 177502 : 177501;
    } else if (appid === 3006) {
      // CapCut App
      appid = isAndroid ? 300602 : 300601;
    } else if (appid === 886) {
      // 剪映专业版-Windows
      appid = 2020092383;
    } else if (appid === 3704) {
      // 剪映专业版-Mac
      appid = 2020092892;
    } else if (appid === 359289) {
      // CapCut 专业版
      appid = 35928901; // CapCut专业版-Windows
      // appid = 35928902; // CapCut专业版-Mac
    } else if (appid === 2515) {
      // 醒图 App
      appid = isAndroid ? 251502 : 251501;
    } else if (appid === 7356) {
      // Hypic App
      appid = isAndroid ? 2020093924 : 2020093988;
    }
    if (data.indicatorType === IndicatorType.Crash) {
      // https://pa.bytedance.net/libra/patrol/business?appid=177501&flight_id=2219928&business_type=crash
      return `${MAIN_HOST_HTTPS}/libra/patrol/business?appid=${appid}&flight_id=${data.id}&business_type=crash`;
    }

    if (data.indicatorType === IndicatorType.FeedBack) {
      return `${MAIN_HOST_HTTPS}/libra/patrol/business?appid=${appid}&flight_id=${data.id}&business_type=feedback`;
    }

    if (data.indicatorType === IndicatorType.GroupUser) {
      // eslint-disable-next-line max-len
      return `${MAIN_HOST_HTTPS}/libra/patrol/business?appid=${appid}&flight_id=${data.id}&business_type=base_user`;
    }

    if (data.indicatorType === IndicatorType.LT) {
      return `${MAIN_HOST_HTTPS}/libra/patrol/business?appid=${appid}&flight_id=${data.id}&business_type=core`;
    }

    if (data.indicatorType === IndicatorType.AD) {
      return `${MAIN_HOST_HTTPS}/libra/patrol/business?appid=${appid}&flight_id=${data.id}&business_type=ad`;
    }

    if (data.indicatorType === IndicatorType.Search) {
      return `${MAIN_HOST_HTTPS}/libra/patrol/business?appid=${appid}&flight_id=${data.id}&business_type=search`;
    }

    if (data.indicatorType === IndicatorType.Conmunity) {
      // eslint-disable-next-line max-len
      return `${MAIN_HOST_HTTPS}/libra/patrol/business?appid=${appid}&flight_id=${data.id}&business_type=community`;
    }

    return `${MAIN_HOST_HTTPS}/libra/patrol/business`;
  }

  createExperimentCard(
    Data: ExperimentCard[],
    info: ExperimentShareInfo,
    isAd?: boolean,
    needNoticePerson?: User[],
  ): Card | undefined {
    const baseCard = this.buildBaseCard({
      title: `纸飞机实验巡检异常上下文`,
      template: CardTemplate.green,
      config: { enable_forward: true, update_multi: true },
    });
    const elements: CardElement[] = [];
    elements.push({
      tag: CardElementTag.div,
      text: {
        content: `实验名称：[${info.name}](${info.url})`,
        tag: CardTextTag.lark_md,
      },
    } as CardContentElement);
    elements.push({
      tag: CardElementTag.div,
      text: {
        content: `实验id：${info.id}`,
        tag: CardTextTag.lark_md,
      },
    } as CardContentElement);
    if (needNoticePerson && needNoticePerson.length > 0) {
      const atPerson = needNoticePerson.map(value => `<at email=${value.email}></at>`).join(' ');
      elements.push({
        tag: CardElementTag.div,
        text: {
          content: `请${atPerson} 关注`,
          tag: CardTextTag.lark_md,
        },
      } as CardContentElement);
    } else if (info.meego_owners) {
      let viewers = '';
      if ('PM' in info.meego_owners) {
        viewers = `${viewers} PM:<at email=${info.meego_owners.PM}></at>`;
      }
      if (!('Android' in info.meego_owners || 'iOS' in info.meego_owners)) {
        if ('Server' in info.meego_owners) {
          viewers = `${viewers} Server-RD:<at email=${info.meego_owners.Server}></at>`;
        }
        if ('FE' in info.meego_owners) {
          viewers = `${viewers} FE:<at email=${info.meego_owners.FE}></at>`;
        }
      }
      if ('clientqa' in info.meego_owners) {
        viewers = `${viewers} 业务QA:<at email=${info.meego_owners.clientqa}></at>`;
      }
      if (Data.filter(v => v.indicatorType === IndicatorType.GroupUser)) {
        if ('DA' in info.meego_owners) {
          viewers = `${viewers} DA:<at email=${info.meego_owners.DA}></at>`;
        }
      }
      elements.push({
        tag: CardElementTag.div,
        text: {
          content: `请${viewers}关注`,
          tag: CardTextTag.lark_md,
        },
      } as CardContentElement);
    } else {
      elements.push({
        tag: CardElementTag.div,
        text: {
          content: `请<at id="all"></at>关注`,
          tag: CardTextTag.lark_md,
        },
      } as CardContentElement);
    }
    let hasdata = false;
    for (const data of Data) {
      if ([HandleStatus.Closed, HandleStatus.Suspend, HandleStatus.Error].includes(data.status)) {
        continue;
      }
      hasdata = true;
      elements.push({
        tag: CardElementTag.hr,
      });
      const oversea = data.appId === 3006 || data.appId === 300601 || data.appId === 300602;
      const title = isAd
        ? oversea
          ? `[广告核心指标组CUPED]( https://libra-sg.tiktok-row.net/libra/flight/${data.id}/report/main?group_id=7036620#********** )`
          : `[广告核心指标组CUPED]( https://data.bytedance.net/libra/flight/${data.id}/report/main?group_id=136433 )`
        : `**${AppId2Name[data.appId]}-${Type2CardTitle[data.indicatorType]}**`;
      elements.push({
        tag: CardElementTag.div,
        text: {
          content: title,
          tag: CardTextTag.lark_md,
        },
      } as CardContentElement);
      if (data.indicatorType === IndicatorType.Crash) {
        if (data.appId % 10 === 1 && info.meego_owners && 'iOS' in info.meego_owners) {
          elements.push({
            tag: CardElementTag.div,
            text: {
              content: `请iOS-RD:<at email=${info.meego_owners.iOS}></at>关注跟进`,
              tag: CardTextTag.lark_md,
            },
          } as CardContentElement);
        }
        if (data.appId % 10 === 2 && info.meego_owners && 'Android' in info.meego_owners) {
          elements.push({
            tag: CardElementTag.div,
            text: {
              content: `请Android-RD:<at email=${info.meego_owners.Android}></at>关跟进注`,
              tag: CardTextTag.lark_md,
            },
          } as CardContentElement);
        }
      }
      if (data.indicatorType === IndicatorType.AD || data.indicatorType === IndicatorType.Commerce) {
        elements.push({
          tag: CardElementTag.div,
          text: {
            content: `请<at email="<EMAIL>"></at>关跟进注`,
            tag: CardTextTag.lark_md,
          },
        } as CardContentElement);
      }
      elements.push({
        tag: CardElementTag.div,
        text: {
          content: `优先级：**${data.priority === 0 ? `<font color="red">P${data.priority}</font>` : `P${data.priority}`}**`,
          tag: CardTextTag.lark_md,
        },
      } as CardContentElement);
      if (data.indicatorType === IndicatorType.GroupUser) {
        elements.push({
          tag: CardElementTag.div,
          text: {
            content: `<font color="red">请确认双端曝光时机是否一致</font>`,
            tag: CardTextTag.lark_md,
          },
        } as CardContentElement);
      }
      elements.push({
        tag: CardElementTag.div,
        text: {
          content: `问题列表:
${data.detail}`,
          tag: CardTextTag.lark_md,
        },
      } as CardContentElement);
      // elements.push({
      //   tag: CardElementTag.action,
      //   actions: [
      //     {
      //       tag: CardElementTag.select,
      //       placeholder: {
      //         tag: CardTextTag.plain_text,
      //         content: '更新处理状态',
      //       },
      //       value: {
      //         id: `${data.id}`,
      //         indicatorType: `${data.indicatorType}`,
      //         cardCallbackType: CardCallbackType.ExperimentUpdateStatus,
      //         appId: `${data.appId}`,
      //       },
      //       options: [
      //         {
      //           text: {
      //             tag: CardTextTag.plain_text,
      //             content: '待处理',
      //           },
      //           value: '待处理',
      //         },
      //         {
      //           text: {
      //             tag: CardTextTag.plain_text,
      //             content: '跟进中',
      //           },
      //           value: '跟进中',
      //         },
      //         {
      //           text: {
      //             tag: CardTextTag.plain_text,
      //             content: '暂不处理',
      //           },
      //           value: '暂不处理',
      //         },
      //         {
      //           text: {
      //             tag: CardTextTag.plain_text,
      //             content: '已关闭',
      //           },
      //           value: '已关闭',
      //         },
      //         {
      //           text: {
      //             tag: CardTextTag.plain_text,
      //             content: '误报',
      //           },
      //           value: '误报',
      //         },
      //       ],
      //     } as CardSelectAction,
      //   ],
      //   layout: CardActionLayout.bisected,
      // } as CardActionElement);

      // elements.push({
      //   tag: CardElementTag.action,
      //   actions: [
      //     {
      //       tag: CardElementTag.select,
      //       placeholder: {
      //         tag: CardTextTag.plain_text,
      //         content: '更新处理原因',
      //       },
      //       value: {
      //         id: `${data.id}`,
      //         indicatorType: `${data.indicatorType}`,
      //         cardCallbackType: CardCallbackType.ExperimentUpdateRemark,
      //         appId: `${data.appId}`,
      //       },
      //       options: [
      //         {
      //           text: {
      //             tag: CardTextTag.plain_text,
      //             content: '确认有问题，实验暂停/关闭',
      //           },
      //           value: '确认有问题，实验暂停/关闭',
      //         },
      //         {
      //           text: {
      //             tag: CardTextTag.plain_text,
      //             content: '确认有问题，实验缩量',
      //           },
      //           value: '确认有问题，实验缩量',
      //         },
      //         {
      //           text: {
      //             tag: CardTextTag.plain_text,
      //             content: '确认有问题，延期处理',
      //           },
      //           value: '确认有问题，延期处理',
      //         },
      //         {
      //           text: {
      //             tag: CardTextTag.plain_text,
      //             content: '原因不明，继续观察',
      //           },
      //           value: '原因不明，继续观察',
      //         },
      //         {
      //           text: {
      //             tag: CardTextTag.plain_text,
      //             content: '符合预期，劣化指标可接受',
      //           },
      //           value: '符合预期，劣化指标可接受',
      //         },
      //         {
      //           text: {
      //             tag: CardTextTag.plain_text,
      //             content: '劣化指标与实验无关',
      //           },
      //           value: '劣化指标与实验无关',
      //         },
      //         {
      //           text: {
      //             tag: CardTextTag.plain_text,
      //             content: '符合预期，反转实验',
      //           },
      //           value: '符合预期，反转实验',
      //         },
      //         {
      //           text: {
      //             tag: CardTextTag.plain_text,
      //             content: '符合预期，其他原因',
      //           },
      //           value: '符合预期，其他原因',
      //         },
      //         {
      //           text: {
      //             tag: CardTextTag.plain_text,
      //             content: '劣化数据有误',
      //           },
      //           value: '劣化数据有误',
      //         },
      //       ],
      //     } as CardSelectAction,
      //   ],
      //   layout: CardActionLayout.bisected,
      // } as CardActionElement);
    }
    if (!hasdata) {
      // 所有问题的都已经被处理了
      return undefined;
    }
    let domainHost = MAIN_HOST_HTTPS;
    if (process.env.NODE_ENV === 'development') {
      domainHost = 'http://localhost:8080';
    }
    const sheetUrl = `${domainHost}/libra/patrol/patrol-sidebar-status?libra_flight_id=${info.id}&handle_status=${Data[0].status}&handle_remark=${Data[0].remark}&app_id=${Data[0].appId}&indicator_type=${info.IndicatorTypes[0]}`;
    const editLibraStatusAttributionUrl = encodeURIComponent(sheetUrl);
    const statusAttributionFinalUrl = `https://applink.feishu.cn/client/web_url/open?mode=sidebar-semi&width=480&reload=false&url=${editLibraStatusAttributionUrl}`;
    elements.push({
      tag: CardElementTag.action,
      actions: [
        {
          tag: CardElementTag.button,
          text: {
            tag: CardTextTag.plain_text,
            content: '更新处理状态',
          },
          type: CardButtonType.primary,
          url: statusAttributionFinalUrl,
        } as CardButtonAction,
        {
          tag: CardElementTag.button,
          text: {
            tag: CardTextTag.plain_text,
            content: '纸飞机',
          },
          type: CardButtonType.primary,
          url:
            Data.length > 0 ? this.libraPatrolCardJumpURL(Data[0], info) : `${MAIN_HOST_HTTPS}/libra/patrol/business`,
        } as CardButtonAction,
        {
          tag: CardElementTag.button,
          text: {
            tag: CardTextTag.plain_text,
            content: '跟进文档',
          },
          type: CardButtonType.primary,
          url: 'https://bytedance.larkoffice.com/docx/Ow2ddYE8joEWdNxsi2UcyuDnnfb?from=from_copylink',
        } as CardButtonAction,
      ],
    } as CardActionElement);
    baseCard.elements = elements;
    return baseCard;
  }

  /**
   * 按照不同架构构建list的卡片
   * @param mrInfoList
   * @private
   */
  buildMrGroupListElement(mrInfoList: MrInfo[]): CardElement[] {
    const elements: CardElement[] = [];
    const groupMr = groupBy<MrInfo>(mrInfoList, value => value.platform);
    for (const groupMrKey in groupMr) {
      const mrList = groupMr[groupMrKey];
      elements.push({
        tag: CardElementTag.div,
        text: {
          tag: CardTextTag.lark_md,
          content: `**${groupMrKey} Mr List (${size(mrList)})**`,
        },
      } as CardContentElement);
      elements.push(LarkCardService.buildMrListElement(mrList));
      elements.push({ tag: CardElementTag.hr });
    }
    this.logger.info(`[buildMrListElement]:${JSON.stringify(mrInfoList)}`);
    return elements;
  }

  buildOnCallInviteCard(urlExtraParams: string, extra = ''): Card {
    const url = `${MAIN_HOST_HTTPS}/tools/oncallInvite?extras=${urlExtraParams}`;
    const veUrl = `${MAIN_HOST_HTTPS}/tools/veoncallInvite?extras=${urlExtraParams}`;
    const baseCard = this.buildBaseCard({
      title: `请选择剪映/Capcut OnCall分类`,
      template: CardTemplate.red,
      config: { enable_forward: false },
    });
    const elements: CardElement[] = [];
    elements.push({
      tag: CardElementTag.div,
      text: {
        tag: CardTextTag.lark_md,
        content: `${extra}请点击下方按钮补充自定义表单并拉入模块值班人`,
      },
    } as CardContentElement);

    elements.push({
      tag: CardElementTag.action,
      actions: [
        {
          tag: CardElementTag.button,
          text: {
            tag: CardTextTag.plain_text,
            content: '选择移动端分类',
          },
          type: CardButtonType.primary,
          url: `https://applink.feishu.cn/client/web_url/open?mode=sidebar-semi&panel_style=high&url=${encodeURI(url)}`,
        } as CardButtonAction,
        {
          tag: CardElementTag.button,
          text: {
            tag: CardTextTag.plain_text,
            content: '选择多媒体分类(VE/Effect/CCIS/IE)',
          },
          type: CardButtonType.primary,
          url: `https://applink.feishu.cn/client/web_url/open?mode=sidebar-semi&panel_style=high&url=${encodeURI(veUrl)}`,
        } as CardButtonAction,
      ],
    } as CardActionElement);
    baseCard.elements = elements;
    return baseCard;
  }

  buildOnCallRecommendCard(
    urlExtraParams: string,
    business: string[],
    dutyInfo: DutyInfo,
    defaultTip: boolean,
    reason: string,
    debugUrl: string,
  ): Card {
    const url = `${MAIN_HOST_HTTPS}/tools/oncallInvite?extras=${urlExtraParams}`;
    const veUrl = `${MAIN_HOST_HTTPS}/tools/veoncallInvite?extras=${urlExtraParams}`;
    const baseCard = this.buildBaseCard({
      title: '纸飞机智能切换分类',
      template: CardTemplate.purple,
      config: { enable_forward: false },
    });
    const elements: CardElement[] = [];
    elements.push({
      tag: CardElementTag.div,
      text: {
        tag: CardTextTag.lark_md,
        content: `${
          defaultTip
            ? `检测到从其他业务线切换到剪映OnCall，**<font color='red'>ByteOncall卡片上显示为兜底值班人请勿直接at</font>。**`
            : ''
        } **纸飞机已根据工单上下文为您智能切换OnCall分类，如有错误请手动点击下方按钮重新选择**`,
      },
    } as CardContentElement);
    elements.push({
      tag: CardElementTag.div,
      text: {
        tag: CardTextTag.lark_md,
        content: `<font color='grey'>原因: ${reason} [Debug](${debugUrl})</font>`,
      },
    } as CardContentElement);
    elements.push({
      tag: CardElementTag.hr,
    });
    elements.push({
      tag: CardElementTag.div,
      text: {
        tag: CardTextTag.lark_md,
        content: `**修改后分类：** ${BusinessState[business[0] as keyof typeof BusinessState]}/${business.slice(1).join('/')}`,
      },
    } as CardContentElement);
    elements.push({
      tag: CardElementTag.div,
      text: {
        tag: CardTextTag.lark_md,
        content: `**主值班人:** ${dutyInfo.primary_users.map(v => `<at email="${add_suffix_ne('@bytedance.com')(v)}">`)}`,
      },
    } as CardContentElement);
    if (dutyInfo.backup_users?.length > 0) {
      elements.push({
        tag: CardElementTag.div,
        text: {
          tag: CardTextTag.lark_md,
          content: `**备值班人:** ${dutyInfo.backup_users.map(v => `<at email="${add_suffix_ne('@bytedance.com')(v)}">`)}`,
        },
      } as CardContentElement);
    }
    if (dutyInfo.bystanders?.length > 0) {
      elements.push({
        tag: CardElementTag.div,
        text: {
          tag: CardTextTag.lark_md,
          content: `**旁观者:** ${dutyInfo.bystanders.map(v => `<at email="${add_suffix_ne('@bytedance.com')(v)}">`)}`,
        },
      } as CardContentElement);
    }
    elements.push({
      tag: CardElementTag.action,
      actions: [
        {
          tag: CardElementTag.button,
          text: {
            tag: CardTextTag.plain_text,
            content: '选择移动端分类',
          },
          type: CardButtonType.primary,
          url: `https://applink.feishu.cn/client/web_url/open?mode=sidebar-semi&panel_style=high&url=${encodeURI(url)}`,
        } as CardButtonAction,
        {
          tag: CardElementTag.button,
          text: {
            tag: CardTextTag.plain_text,
            content: '选择多媒体分类(VE/Effect/CCIS/IE)',
          },
          type: CardButtonType.primary,
          url: `https://applink.feishu.cn/client/web_url/open?mode=sidebar-semi&panel_style=high&url=${encodeURI(veUrl)}`,
        } as CardButtonAction,
      ],
    } as CardActionElement);
    baseCard.elements = elements;
    return baseCard;
  }

  buildOnCallSwitchCard(business: string[], dutyInfo: DutyInfo): Card {
    const baseCard = this.buildBaseCard({
      title: `已修改当前OnCall分类`,
      template: CardTemplate.green,
      config: { enable_forward: false },
    });
    const elements: CardElement[] = [];
    elements.push({
      tag: CardElementTag.div,
      text: {
        tag: CardTextTag.lark_md,
        content: `**修改后分类：** ${BusinessState[business[0] as keyof typeof BusinessState]}/${business.slice(1).join('/')}`,
      },
    } as CardContentElement);

    elements.push({
      tag: CardElementTag.div,
      text: {
        tag: CardTextTag.lark_md,
        content: `**主值班人:** ${dutyInfo.primary_users.map(v => `<at email="${add_suffix_ne('@bytedance.com')(v)}">`)}`,
      },
    } as CardContentElement);
    if (dutyInfo.backup_users?.length > 0) {
      elements.push({
        tag: CardElementTag.div,
        text: {
          tag: CardTextTag.lark_md,
          content: `**备值班人:** ${dutyInfo.backup_users.map(v => `<at email="${add_suffix_ne('@bytedance.com')(v)}">`)}`,
        },
      } as CardContentElement);
    }
    if (dutyInfo.bystanders?.length > 0) {
      elements.push({
        tag: CardElementTag.div,
        text: {
          tag: CardTextTag.lark_md,
          content: `**旁观者:** ${dutyInfo.bystanders.map(v => `<at email="${add_suffix_ne('@bytedance.com')(v)}">`)}`,
        },
      } as CardContentElement);
    }
    baseCard.elements = elements;
    return baseCard;
  }

  buildNotifyStoryDocCard(r: StoryCheckResult[]): Card {
    const bc = this.buildBaseCard({
      title: '请检查需求文档权限，以确保研发初估顺利进行',
      template: CardTemplate.red,
      config: { enable_forward: false },
    });
    bc.elements = [
      {
        tag: CardElementTag.div,
        text: {
          tag: CardTextTag.lark_md,
          content:
            '以下需求的文档尚未设置为「组织内获得链接的人可阅读」，请在今天20:00前完成权限开通，便于研发阅读文档和评估其开发工作量',
        },
      },
      {
        tag: CardElementTag.div,
        text: {
          tag: CardTextTag.lark_md,
          content: r
            .map(
              (it, idx) =>
                `${idx + 1}. <at email="${it.owner}"></at> ${it.name} ${it.link.length > 0 ? `<a href="${it.link}">文档链接</a>` : '链接失效'}`,
            )
            .join('\n'),
        },
      },
    ] as CardContentElement[];
    return bc;
  }

  buildStoryBusVersionCheckCard(r: StoryBusVersionCheckResult[]): Card {
    const bc = this.buildBaseCard({
      title: '请检查需求上车版本',
      template: CardTemplate.red,
      config: { enable_forward: false },
    });
    bc.elements = [
      {
        tag: CardElementTag.div,
        text: {
          tag: CardTextTag.lark_md,
          content:
            '经 MR 已合入版本需求与 meego 上填写「上车版本」校验发现以下需求不一致，辛苦技术 owner or PM check meego 上车版本是否填写准确～',
        },
      },
      {
        tag: CardElementTag.div,
        text: {
          tag: CardTextTag.lark_md,
          content: r
            .map(
              (it, idx) =>
                `${idx + 1}. <at email='${it.owner}'></at> <a href='https://meego.larkoffice.com/faceu/story/detail/${it.id}'>${it.name}</a>`,
            )
            .join('\n'),
        },
      },
    ] as CardContentElement[];
    return bc;
  }

  buildDiffSizeCheckCard(
    title: string,
    diffSize: number,
    checkResult: PkCheckResult,
    task_id: string,
    base_task_id: string,
    mrId: number,
    author: string,
    platform: PlatformType,
  ): Card {
    const diffSizeMb =
      platform === PlatformType.Android
        ? Math.round((diffSize / 1024) * 100) / 100
        : Math.round((diffSize / 1000) * 100) / 100;
    const baseCard = this.buildBaseCard({
      title,
      template: checkResult.pass ? CardTemplate.green : CardTemplate.red,
      config: { enable_forward: false },
    });
    const elements: CardElement[] = [];
    try {
      elements.push({
        tag: CardElementTag.div,
        text: {
          tag: CardTextTag.lark_md,
          content: `当前包大小研发负责人: <at email=${author}></at>`,
        },
      } as CardContentElement);
    } catch {}
    if (checkResult.pass) {
      elements.push({
        tag: CardElementTag.div,
        text: {
          tag: CardTextTag.lark_md,
          content: `**检测通过**: diffSize=${diffSize}bytes=${diffSizeMb}KB,增量详情查看[pk150diff链接](https://pk150.bytedance.net/dashboard/task?task_id=${task_id}&base_task_id=${base_task_id})`,
        },
      } as CardContentElement);
      elements.push({
        tag: CardElementTag.div,
        text: {
          tag: CardTextTag.lark_md,
          content: `${checkResult.message}`,
        },
      } as CardContentElement);
    } else {
      if (checkResult.check.length > 0) {
        elements.push({
          tag: CardElementTag.div,
          text: {
            tag: CardTextTag.lark_md,
            content: checkResult.check,
          },
        } as CardContentElement);
      }
      if (checkResult.check1a.length > 0) {
        elements.push({
          tag: CardElementTag.div,
          text: {
            tag: CardTextTag.lark_md,
            content: checkResult.check1a,
          },
        } as CardContentElement);
      }
      if (checkResult.check1b.length > 0) {
        elements.push({
          tag: CardElementTag.div,
          text: {
            tag: CardTextTag.lark_md,
            content: checkResult.check1b,
          },
        } as CardContentElement);
      }
      if (checkResult.check2.length > 0) {
        elements.push({
          tag: CardElementTag.div,
          text: {
            tag: CardTextTag.lark_md,
            content: checkResult.check2,
          },
        } as CardContentElement);
      }
    }
    elements.push({
      tag: CardElementTag.div,
      text: {
        tag: CardTextTag.lark_md,
        content: `https://bits.bytedance.net/bytebus/devops/code/detail/${mrId}`,
      },
    } as CardContentElement);
    baseCard.elements = elements;
    return baseCard;
  }

  buildPackageSizeCheckCard(
    title: string,
    target_size: number,
    base_size: number,
    checkResult: PkCheckResult,
    task_id: string,
    base_task_id: string,
    mrId: number,
    author: string,
    platform: PlatformType,
  ): Card {
    return this.buildDiffSizeCheckCard(
      title,
      target_size - base_size,
      checkResult,
      task_id,
      base_task_id,
      mrId,
      author,
      platform,
    );
  }

  private buildIssueElement(issueList: IssueInfo[]): CardElement {
    return {
      tag: CardElementTag.markdown,
      content: `${issueList
        .map(value => {
          if (value.directorEmail.length > 0) {
            return `**${value.priority}**&nbsp;**<at email=${value.issueOperator}></at>** **<at email=${value.directorEmail}></at>** **<font color='red'> 已逾期:${value.delayTime}</font>** DDL:${value.DDLDate} 剩余:${value.remainHours}h [${commonUtils.ellipsizeText(value.name)}](${value.link})`;
          } else {
            return `**${value.priority}**&nbsp;**<at email=${value.issueOperator}></at>** DDL:${value.DDLDate} 剩余:${value.remainHours}h [${commonUtils.ellipsizeText(value.name)}](${value.link})`;
          }
        })
        .join('\n')}`,
    } as CardMarkdownElement;
  }

  private buildModelElement(version: string, modelInfo: ModelIssueInfo[], autoTestP0P1Count: number): CardElement {
    return {
      tag: CardElementTag.markdown,
      content: `${modelInfo
        .map(value => {
          let resultText = '';
          if (Number(value.rate) < 90) {
            resultText = `**${value.modelName}**&nbsp;**<at email=${value.email}></at>** 修复率为 **<font color='red'> ${value.rate}% </font>** ${value.fixModelIssue}/${value.allModelIssue}`;
          } else {
            resultText = `**${value.modelName}**&nbsp;**${value.owner}** 修复率为 **<font color='red'> ${value.rate}% </font>** ${value.fixModelIssue}/${value.allModelIssue}`;
          }
          if (value.fixModelIssue < value.allModelIssue) {
            resultText = `${resultText} [Meego缺陷视图](https://meego.feishu.cn/faceu/issueView/${value.issueViewId})`;
          }
          if (autoTestP0P1Count > 0 && value.modelName.includes('基础性能自动化')) {
            resultText = `${resultText} 其中S/P0/P1还有 **<font color='red'>${autoTestP0P1Count}</font>** 个待修复`;
          } else {
            if (value.need70 > 0) {
              resultText = `${resultText} 达到70%还需修复${value.need70}个`;
            }
            if (value.need90 > 0) {
              resultText = `${resultText} 达到90%还需修复${value.need90}个`;
            }
          }
          return resultText;
        })
        .join('\n')}`,
    } as CardMarkdownElement;
  }

  //     } as CardMarkdownElement
  private buildMrElement(mrList: MrReviewerInfo[]): CardElement {
    const contentJson = `${mrList
      .map(value => {
        const reviewerInfo =
          value.reviewInfo?.sort((a, b) => this.getLevelByRole(a.review_role) - this.getLevelByRole(b.review_role)) ??
          [];
        this.logger.info(`reviewerInfo = ${JSON.stringify(value.mrInfo.title)}`);
        this.logger.info(`reviewerInfo = ${JSON.stringify(reviewerInfo)}`);
        let reviewText = '';
        let reviewQAText = '';
        let reviewRDText = '';
        const reviewMap = new Map();
        for (let i = 0; i < reviewerInfo.length; i++) {
          if (reviewerInfo[i].approved === 'running' && !reviewMap.has(reviewerInfo[i].username)) {
            reviewMap.set(reviewerInfo[i].username, 1);
            if (reviewerInfo[i].review_role === 'QA') {
              reviewQAText = reviewQAText.concat(`<at email=${reviewerInfo[i].username}@bytedance.com></at>`);
            }
            if (reviewerInfo[i].review_role === 'RD') {
              reviewRDText = reviewRDText.concat(`<at email=${reviewerInfo[i].username}@bytedance.com></at>`);
            }
          }
        }
        if (reviewQAText === '' && reviewRDText === '') {
          reviewText = '';
        } else if (reviewQAText === '') {
          reviewText = `&emsp;* **unReviewers:** **RD:** **${reviewRDText}** \n`;
        } else if (reviewRDText === '') {
          reviewText = `&emsp;* **unReviewers:** **QA:** **${reviewQAText}** \n`;
        } else {
          reviewText = `&emsp;* **unReviewers:** **QA:** **${reviewQAText}** &nbsp; **RD:** **${reviewRDText}** \n`;
        }
        this.logger.info(`reviewText = ${reviewText}`);
        let { title } = value.mrInfo;
        if (value.mrInfo.title.length > 40) {
          title = value.mrInfo.title.substring(0, 40);
        }
        return `**[${title}](${value.mrInfo.mr_detail_url})**\n&emsp;* **owner:** **<at email=${value.mrInfo.author}@bytedance.com></at>** \n${reviewText}`;
      })
      .join('\n')}`;
    return {
      tag: CardElementTag.markdown,
      content: contentJson,
    } as CardMarkdownElement;
  }

  buildApproveBalanceCard(title: string, contents: string[], recordID: string, chatID: string): Card {
    const baseCard = this.buildBaseCard({
      title,
      template: CardTemplate.yellow,
    });
    const elements: CardElement[] = [];
    contents.map(content => {
      elements.push({
        tag: CardElementTag.div,
        text: {
          tag: CardTextTag.lark_md,
          content,
        },
      } as CardContentElement);
    });

    elements.push({
      tag: CardElementTag.action,
      actions: [
        {
          tag: CardElementTag.button,
          text: {
            tag: CardTextTag.plain_text,
            content: '通过',
          },
          type: CardButtonType.primary,
          value: {
            cardCallbackType: CardCallbackType.WithdrawBanlance,
            balanceRecordId: recordID,
            isApproved: 'true',
            chatId: chatID,
          },
        } as CardButtonAction,
        {
          tag: CardElementTag.button,
          text: {
            tag: CardTextTag.plain_text,
            content: '拒绝',
          },
          type: CardButtonType.danger,
          value: {
            cardCallbackType: CardCallbackType.WithdrawBanlance,
            balanceRecordId: recordID,
            isApproved: 'false',
            chatId: chatID,
          },
        } as CardButtonAction,
      ],
    } as CardActionElement);

    baseCard.elements = elements;
    return baseCard;
  }

  buildBalanceResultCard(title: string, contents: string[], color: CardTemplate) {
    const baseCard = this.buildBaseCard({
      title,
      template: color,
    });
    const elements: CardElement[] = [];
    contents.map(content => {
      elements.push({
        tag: CardElementTag.markdown,
        content,
      } as CardMarkdownElement);
    });
    baseCard.elements = elements;
    return baseCard;
  }

  buildExecutCozeFailureCard(helpDocUrl: string): Card {
    const baseCard = {
      config: {},
      elements: [],
    } as Card;
    const elements: CardElement[] = [];
    elements.push({
      tag: CardElementTag.div,
      text: {
        tag: CardTextTag.lark_md,
        content: `**Coze 执行失败，[点击Debug链接排查问题](${helpDocUrl})**`,
      },
    } as CardElement);
    baseCard.elements = elements;
    return baseCard;
  }

  buildLLMUnResolvedCard(actionType: CozeWorkflowActionType): Card {
    const baseCard = {
      config: {},
      elements: [],
    } as Card;
    const elements: CardElement[] = [];
    const helpDocUrl = 'https://bytedance.larkoffice.com/docx/Y7tcdIutXoSMM4xCH6gcMyURnAg';
    elements.push({
      tag: CardElementTag.div,
      text: {
        tag: CardTextTag.lark_md,
        content: `**识别到意图【${CozeWorkflowActionName(actionType)}】，暂不支持此操作，[点击查看帮助文档](${helpDocUrl})**`,
      },
    } as CardElement);
    baseCard.elements = elements;
    return baseCard;
  }

  buildVersionReivewReportCard(versionInfo: VersionProcessInfo, androidVersionInfo: VersionProcessInfo) {
    const baseCard = this.buildBaseCard({
      title: `${versionInfo.version}版本总结开始提醒`,
      template: CardTemplate.green,
    });
    const elements: CardElement[] = [];
    elements.push({
      tag: CardElementTag.div,
      text: {
        tag: CardTextTag.lark_md,
        content: `${versionInfo.version}版本总结已开始，请<at email=${versionInfo.bmInfo[BmType.qa].email}></at> <at email=${androidVersionInfo.bmInfo[BmType.qa].email}></at>及时完成内容填写`,
      },
    } as CardElement);
    elements.push({
      tag: CardElementTag.div,
      text: {
        tag: CardTextTag.lark_md,
        content: `[点击查看版本总结报告](${versionInfo.versionReviewDoc})`,
      },
    } as CardElement);
    baseCard.elements = elements;
    return baseCard;
  }
  buildVersionReportOfRDCard(versionInfo: VersionProcessInfo) {
    const baseCard = this.buildBaseCard({
      title: `RD侧${versionInfo.version}版本总结`,
      template: CardTemplate.green,
    });
    const elements: CardElement[] = [];
    elements.push({
      tag: CardElementTag.div,
      text: {
        tag: CardTextTag.lark_md,
        content: `${versionInfo.version}版本总结报告已完成，请查收`,
      },
    } as CardElement);
    elements.push({
      tag: CardElementTag.div,
      text: {
        tag: CardTextTag.lark_md,
        content: `[点击查看版本总结报告](${versionInfo.versionReviewDocOfRD})`,
      },
    } as CardElement);
    baseCard.elements = elements;
    return baseCard;
  }
  buildAndroidAutoBuildSuccessCard(info: BuildCardInfo) {
    const baseCard = this.buildBaseCard({
      title: info.title,
      template: CardTemplate.green,
    });
    const baseInfo: string[] = [`**branch:**\t\t\t\t${info.branch}`];
    if (info.commit) {
      baseInfo.push(`**commit sha**\t\t\t${info.commit}`);
    }
    baseInfo.push('');
    if (info.params.is32) {
      baseInfo.push(`**架构**\t\t\t\t\t32位`);
    } else {
      baseInfo.push(`**架构**\t\t\t\t\t64位`);
    }
    if (info.params.isDebug) {
      baseInfo.push(`**类型**\t\t\t\t\tdebug`);
    } else {
      baseInfo.push(`**类型**\t\t\t\t\trelease`);
    }
    if (info.params.isHyperTest) {
      baseInfo.push(`**HyperTest**\t\t\tyes`);
    }
    if (info.params.isCoverage) {
      baseInfo.push(`**覆盖率**\t\t\t\tyes`);
    }
    if (info.params.veVersion) {
      baseInfo.push(`**VE版本**\t\t\t\t${info.params.veVersion}`);
    }
    if (info.params.effectVersion) {
      baseInfo.push(`**Effect版本**\t\t\t${info.params.effectVersion}`);
    }
    if (info.params.effectVersion) {
      baseInfo.push(`**Effect版本**\t\t\t${info.params.effectVersion}`);
    }
    baseCard.elements.push({
      tag: CardElementTag.div,
      text: {
        tag: CardTextTag.lark_md,
        content: baseInfo.join('\n'),
      },
      extra: {
        tag: CardElementTag.img,
        img_key: info.apkImgKey,
        alt: {
          tag: CardTextTag.plain_text,
          content: 'APK',
        },
      },
    } as CardContentElement);
    baseCard.elements.push({
      tag: CardElementTag.action,
      actions: [
        {
          tag: CardElementTag.button,
          url: info.apkUrl,
          text: {
            tag: CardTextTag.plain_text,
            content: `download ${info.apkName}`,
          },
          type: CardButtonType.primary,
        } as CardButtonAction,
      ],
    } as CardActionElement);
    this.logger.debug(`构建结果卡片:${JSON.stringify(baseCard)}`);
    return baseCard;
  }

  buildiOSAndPCSuccessCard(platform: PlatformType, downloadLink: string, versionCode?: string, taskName?: string) {
    const baseCard = this.buildBaseCard({
      title: '打包成功',
      template: CardTemplate.green,
    });
    const elements: CardElement[] = [];
    elements.push({
      tag: CardElementTag.div,
      text: {
        tag: CardTextTag.lark_md,
        content: `**版本号:**\t\t\t\t\t${versionCode ?? 'unknow'}\n**任务名称:**\t\t\t\t\t${taskName ?? 'unknow'}`,
      },
    } as CardElement);
    if (platform === PlatformType.iOS) {
      elements.push({
        tag: CardElementTag.div,
        text: {
          tag: CardTextTag.lark_md,
          content: `[打包任务链接](${downloadLink})`,
        },
      } as CardElement);
      elements.push({
        tag: CardElementTag.div,
        text: {
          tag: CardTextTag.lark_md,
          content: `请前往TestFlight下载`,
        },
      } as CardElement);
    } else {
      elements.push({
        tag: CardElementTag.div,
        text: {
          tag: CardTextTag.lark_md,
          content: `[下载](${downloadLink})`,
        },
      } as CardElement);
    }
    baseCard.elements = elements;
    return baseCard;
  }

  buildCrucialVersionInfoCard(version: string, featureInfos: VersionFeatureInfo[], versionInfo: VersionProcessInfo) {
    let productName = '';
    switch (versionInfo.app_id) {
      case AppSettingId.LV_IOS:
        productName = '剪映-iOS';
        break;
      case AppSettingId.LV_ANDROID:
        productName = '剪映-Android';
        break;
      case AppSettingId.CC_IOS:
        productName = 'CapCut-iOS';
        break;
      case AppSettingId.CC_ANDROID:
        productName = 'CapCut-Android';
        break;
      default:
        break;
    }
    const baseCard = this.buildBaseCard({
      title: `${productName} 当前版本有重保需求，请关注`,
      template: CardTemplate.green,
      config: { enable_forward: false },
    });
    const elements: CardElement[] = [];

    for (const featureInfo of featureInfos) {
      let emailInfo = '';
      for (let i = 0; i < featureInfo.pmOwners.length; i++) {
        emailInfo = emailInfo.concat(`<at email=${featureInfo.pmOwners[i].email}></at>`);
      }
      elements.push({
        tag: CardElementTag.div,
        text: {
          tag: CardTextTag.lark_md,
          content: `**${featureInfo.businessLine}-[${featureInfo.featureName}](${featureInfo.meegoUrl})** ${emailInfo}`,
        },
      } as CardElement);
    }

    elements.push({
      tag: CardElementTag.div,
      text: {
        tag: CardTextTag.lark_md,
        content: `<at email=${versionInfo.bmInfo[BmType.qa].email}></at>`,
      },
    } as CardElement);

    baseCard.elements = elements;
    return baseCard;
  }

  /**
   * 构建android最近打包信息卡片（包含Release和Develop分支）
   */
  buildMultiBranchBuildsCardAndroid(param: {
    title: string;
    releaseBuilds: Array<{
      version: string;
      buildTime: string;
      arch: string;
      description: any;
      mrDetailUrl: any;
      buildUrlOut: string;
      downloadUrlOut: string;
      buildUrlDebug: string;
      downloadUrlDebug: string;
      downloadQROut: string;
      downloadQRDebug: string;
    }>;
    developBuilds: Array<{
      buildTime: string;
      arch: string;
      description: any;
      mrDetailUrl: any;
      buildUrlOut: string;
      downloadUrlOut: string;
      buildUrlDebug: string;
      downloadUrlDebug: string;
      downloadQROut: string;
      downloadQRDebug: string;
    }>;
    timestamp?: string;
  }): Card {
    const baseCard = this.buildBaseCard({
      title: param.title,
      template: CardTemplate.blue,
      config: { enable_forward: true },
    });

    const elements: CardElement[] = [];

    // ============== Release分支构建结果部分 ==============
    elements.push({
      tag: CardElementTag.div,
      text: {
        tag: CardTextTag.lark_md,
        content: `🚀 Release分支最新打包信息`,
        text_size: 'heading',
      },
    } as CardContentElement);

    param.releaseBuilds.forEach((build, index) => {
      // 基础信息（版本号/构建时间/描述）
      elements.push({
        tag: CardElementTag.div,
        fields: [
          {
            is_short: true,
            text: {
              tag: CardTextTag.lark_md,
              content: `**版本号**：${build.version} \t **构建时间**：${build.buildTime} \t **架构** ${build.arch}`,
            },
          },
        ],
      } as CardContentElement);

      // MR详情链接
      elements.push({
        tag: CardElementTag.div,
        text: {
          tag: CardTextTag.lark_md,
          content: build.mrDetailUrl
            ? `**MR详情**：[${build.description}](${build.mrDetailUrl})`
            : '无有效的MR详情链接',
        },
      } as CardContentElement);

      // // 下载链接（保留下载链接展示，移除二维码）
      // elements.push({
      //   tag: CardElementTag.div,
      //   text: {
      //     tag: CardTextTag.lark_md,
      //     content: `**外发包下载链接**：[点击下载](${build.downloadUrlOut})`,
      //   },
      // } as CardContentElement);
      // 下载链接
      elements.push({
        tag: CardElementTag.div,
        fields: [
          {
            is_short: true,
            text: {
              tag: CardTextTag.lark_md,
              content: `[外发包构建地址](${build.buildUrlOut})\n[外发包下载链接](${build.downloadUrlOut})`,
            },
          },
        ],
        extra: {
          tag: CardElementTag.img,
          img_key: build.downloadQROut,
          alt: {
            tag: CardTextTag.plain_text,
            content: 'APK',
          },
        },
      } as CardContentElement);

      // 下载链接
      elements.push({
        tag: CardElementTag.div,
        fields: [
          {
            is_short: true,
            text: {
              tag: CardTextTag.lark_md,
              content: `[debug包构建地址](${build.buildUrlDebug})\n[debug包下载链接](${build.downloadUrlDebug})`,
            },
          },
        ],
        extra: {
          tag: CardElementTag.img,
          img_key: build.downloadQRDebug,
          alt: {
            tag: CardTextTag.plain_text,
            content: 'APK',
          },
        },
      } as CardContentElement);

      // 分隔线（最后一条不添加）
      if (index < param.releaseBuilds.length - 1) {
        elements.push({ tag: CardElementTag.hr });
      }
    });

    // 分支分隔线
    elements.push({ tag: CardElementTag.hr });

    // ============== Develop分支构建结果部分 ==============
    elements.push({
      tag: CardElementTag.div,
      text: {
        tag: CardTextTag.lark_md,
        content: `🛠️ Develop分支最新打包信息`,
        text_size: 'heading',
      },
    } as CardContentElement);

    param.developBuilds.forEach((build, index) => {
      // 构建标题（带序号）
      elements.push({
        tag: CardElementTag.div,
        text: {
          tag: CardTextTag.lark_md,
          content: `**${index + 1}. 构建时间** ${build.buildTime}\t\t\t **架构** ${build.arch}`,
          text_size: 'sub_heading',
        },
      } as CardContentElement);

      // 构建描述和MR详情链接
      elements.push({
        tag: CardElementTag.div,
        text: {
          tag: CardTextTag.lark_md,
          content:
            build.mrDetailUrl && build.mrDetailUrl !== '无mr详情链接' && /^https?:\/\//.test(build.mrDetailUrl)
              ? `**MR详情**：[${build.description}](${build.mrDetailUrl})`
              : '无有效的MR详情链接',
        },
      } as CardContentElement);

      // 下载链接
      elements.push({
        tag: CardElementTag.div,
        fields: [
          {
            is_short: true,
            text: {
              tag: CardTextTag.lark_md,
              content: `[构建地址](${build.buildUrlOut})\n[下载链接](${build.downloadUrlOut})`,
            },
          },
        ],
        extra: {
          tag: CardElementTag.img,
          img_key: build.downloadQROut,
          alt: {
            tag: CardTextTag.plain_text,
            content: 'APK',
          },
        },
      } as CardContentElement);
      // 下载链接
      elements.push({
        tag: CardElementTag.div,
        fields: [
          {
            is_short: true,
            text: {
              tag: CardTextTag.lark_md,
              content: `[debug包构建地址](${build.buildUrlDebug})\n[debug包下载链接](${build.downloadUrlDebug})`,
            },
          },
        ],
        extra: {
          tag: CardElementTag.img,
          img_key: build.downloadQRDebug,
          alt: {
            tag: CardTextTag.plain_text,
            content: 'APK',
          },
        },
      } as CardContentElement);

      // 分隔线（最后一条不添加）
      if (index < param.developBuilds.length - 1) {
        elements.push({ tag: CardElementTag.hr });
      }
    });

    baseCard.elements = elements;
    return baseCard;
  }
  buildMultiBranchBuildsCardIOS(param: {
    title: string;
    rcBuilds: Array<{
      buildTime: string;
      arch: string;
      description: any;
      mrDetailUrl: any;
      buildUrl: string;
      downloadUrl: string;
      downloadQR: string;
    }>;
    developBuilds: Array<{
      buildTime: string;
      arch: string;
      description: any;
      mrDetailUrl: any;
      buildUrl: string;
      downloadUrl: string;
      downloadQR: string;
    }>;
    timestamp?: string;
  }): Card {
    const baseCard = this.buildBaseCard({
      title: param.title,
      template: CardTemplate.blue,
      config: { enable_forward: true },
    });
    const elements: CardElement[] = [];

    // ============== rc分支构建结果部分 ==============
    elements.push({
      tag: CardElementTag.div,
      text: {
        tag: CardTextTag.lark_md,
        content: `🌟 rc/develop分支最新打包信息`,
        text_size: 'heading',
      },
    } as CardContentElement);

    param.rcBuilds.forEach((build, index) => {
      // 基础信息（构建时间/描述）
      elements.push({
        tag: CardElementTag.div,
        fields: [
          {
            is_short: true,
            text: {
              tag: CardTextTag.lark_md,
              content: `**构建时间** ${build.buildTime} \t\t **架构** ${build.arch}`,
            },
          },
        ],
      } as CardContentElement);

      // MR详情链接
      elements.push({
        tag: CardElementTag.div,
        text: {
          tag: CardTextTag.lark_md,
          content: build.mrDetailUrl
            ? `**MR详情**：[${build.description}](${build.mrDetailUrl})`
            : '无有效的MR详情链接',
        },
      } as CardContentElement);

      // // 构建地址链接
      // elements.push({
      //   tag: CardElementTag.div,
      //   text: {
      //     tag: CardTextTag.lark_md,
      //     content: ``,
      //   },
      // } as CardContentElement);

      // // 下载链接（保留下载链接展示，移除二维码）
      // elements.push({
      //   tag: CardElementTag.div,
      //   text: {
      //     tag: CardTextTag.lark_md,
      //     content: `**外发包下载链接**：[点击下载](${build.downloadUrlOut})`,
      //   },
      // } as CardContentElement);
      // 下载链接
      elements.push({
        tag: CardElementTag.div,
        fields: [
          {
            is_short: true,
            text: {
              tag: CardTextTag.lark_md,
              content: `[构建地址](${build.buildUrl})\n[下载链接](${build.downloadUrl})`,
            },
          },
        ],
        extra: {
          tag: CardElementTag.img,
          img_key: build.downloadQR,
          alt: {
            tag: CardTextTag.plain_text,
            content: 'IPA',
          },
        },
      } as CardContentElement);

      // 分隔线（最后一条不添加）
      if (index < param.rcBuilds.length - 1) {
        elements.push({ tag: CardElementTag.hr });
      }
    });

    // 分支分隔线
    elements.push({ tag: CardElementTag.hr });

    // ============== Develop分支构建结果部分 ==============
    elements.push({
      tag: CardElementTag.div,
      text: {
        tag: CardTextTag.lark_md,
        content: `🛠️ Develop分支最新打包信息`,
        text_size: 'heading',
      },
    } as CardContentElement);

    param.developBuilds.forEach((build, index) => {
      // 构建标题（带序号）
      elements.push({
        tag: CardElementTag.div,
        text: {
          tag: CardTextTag.lark_md,
          content: `**${index + 1}. 构建时间** ${build.buildTime}\t\t\t **架构** ${build.arch}`,
          text_size: 'sub_heading',
        },
      } as CardContentElement);

      // 构建描述和MR详情链接
      elements.push({
        tag: CardElementTag.div,
        text: {
          tag: CardTextTag.lark_md,
          content:
            build.mrDetailUrl && build.mrDetailUrl !== '无mr详情链接' && /^https?:\/\//.test(build.mrDetailUrl)
              ? `**MR详情**：[${build.description}](${build.mrDetailUrl})`
              : '无有效的MR详情链接',
        },
      } as CardContentElement);

      // 下载链接
      elements.push({
        tag: CardElementTag.div,
        fields: [
          {
            is_short: true,
            text: {
              tag: CardTextTag.lark_md,
              content: `[构建地址](${build.buildUrl})\n[下载链接](${build.downloadUrl})`,
            },
          },
        ],
        extra: {
          tag: CardElementTag.img,
          img_key: build.downloadQR,
          alt: {
            tag: CardTextTag.plain_text,
            content: 'IPA',
          },
        },
      } as CardContentElement);

      // 分隔线（最后一条不添加）
      if (index < param.developBuilds.length - 1) {
        elements.push({ tag: CardElementTag.hr });
      }
    });

    baseCard.elements = elements;
    return baseCard;
  }
  buildGrayCircuitBreakerInfoCard(
    appName: string,
    circuitBreakerInfo: CircuitBreakerTicket,
    versionInfo: VersionProcessInfo,
    actionType: CircuitBreakerCallbackActionType,
    update?: boolean,
  ) {
    const meegoLinkReg = new RegExp(/.*\/faceu\/issue\/detail\/(\d+).?/);
    switch (circuitBreakerInfo.ticketType) {
      case CircuitBreakerTicketType.Slardar:
        const slardarCircuitBreakerInfo = circuitBreakerInfo as SlardarCircuitBreakerTicket;
        const card = new LarkCardTemplate(
          actionType === CircuitBreakerCallbackActionType.FinishTicket
            ? LarkTemplateId.SlardarCircuitBreakerFinish
            : LarkTemplateId.SlardarCircuitBreakerInfo,
          {
            title: `${appName}-${slardarCircuitBreakerInfo.os}-${slardarCircuitBreakerInfo.version} `,
            versionCode: slardarCircuitBreakerInfo.versionCode,
            rdbm: versionInfo.bmInfo[BmType.crash]?.email ?? versionInfo.bmInfo[BmType.rd]?.email,
            qabm: versionInfo.bmInfo[BmType.qa]?.email,
            alarmRuleName: slardarCircuitBreakerInfo.alarmRuleName,
            alarmRuleLink: slardarCircuitBreakerInfo.alarmRuleLink,
            alarmType: slardarCircuitBreakerInfo.alarmType,
            alarmStartTime: dayjs
              .unix(slardarCircuitBreakerInfo.alarmStartTime ?? slardarCircuitBreakerInfo?.ticketCreateTime)
              .tz('Asia/Shanghai', true)
              .format('YYYY-MM-DD HH:mm:ss'),
            mainButtonText: '开始跟进',
            slardarContext: '',
            meegoButtonText:
              slardarCircuitBreakerInfo?.meegoLink && meegoLinkReg.test(slardarCircuitBreakerInfo.meegoLink)
                ? '进群跟进'
                : '创建Meego缺陷单',
            isProcessing: false,
            isFinish: false,

            callbackDataProcess: {
              cardCallbackType: CardCallbackType.CircuitBreakerHandle,
              ticketId: slardarCircuitBreakerInfo.ticketId,
              ticketType: slardarCircuitBreakerInfo.ticketType,
              actionType: CircuitBreakerCallbackActionType.StartProcess,
              appId: versionInfo.app_id,
            },
            callbackDataNoNeed: {
              cardCallbackType: CardCallbackType.CircuitBreakerHandle,
              ticketId: slardarCircuitBreakerInfo.ticketId,
              ticketType: slardarCircuitBreakerInfo.ticketType,
              actionType: CircuitBreakerCallbackActionType.NotNeedProcess,
              appId: versionInfo.app_id,
            },
            callbackDataFinish: {
              cardCallbackType: CardCallbackType.CircuitBreakerHandle,
              ticketId: slardarCircuitBreakerInfo.ticketId,
              ticketType: slardarCircuitBreakerInfo.ticketType,
              actionType: CircuitBreakerCallbackActionType.FinishTicket,
              appId: versionInfo.app_id,
            },
            callbackCreateIssue: {
              cardCallbackType: CardCallbackType.CircuitBreakerHandle,
              ticketId: slardarCircuitBreakerInfo.ticketId,
              ticketType: slardarCircuitBreakerInfo.ticketType,
              actionType: CircuitBreakerCallbackActionType.CreateMeegoAndJoinChat,
              appId: versionInfo.app_id,
            },
          },
        );
        // 卡片样式回调变更，开始跟进 - 跟进中；结束跟进 -
        if (update) {
          if (actionType === CircuitBreakerCallbackActionType.StartProcess) {
            const tvar: any = card.data.template_variable;
            card.data.template_variable = {
              ...(tvar as any),
              title: `【跟进中】${tvar?.title}`,
              mainButtonText: '结束跟进',
              meegoButtonText:
                slardarCircuitBreakerInfo?.meegoLink && meegoLinkReg.test(slardarCircuitBreakerInfo.meegoLink)
                  ? '进群跟进'
                  : '创建Meego缺陷单',
              isProcessing: true,
              isFinish: false,
              callbackDataProcess: {
                cardCallbackType: CardCallbackType.CircuitBreakerHandle,
                ticketId: slardarCircuitBreakerInfo.ticketId,
                ticketType: slardarCircuitBreakerInfo.ticketType,
                actionType: CircuitBreakerCallbackActionType.FinishProcess,
                appId: versionInfo.app_id,
              },
              callbackCreateIssue: {
                cardCallbackType: CardCallbackType.CircuitBreakerHandle,
                ticketId: slardarCircuitBreakerInfo.ticketId,
                ticketType: slardarCircuitBreakerInfo.ticketType,
                actionType: CircuitBreakerCallbackActionType.CreateMeegoAndJoinChat,
                appId: versionInfo.app_id,
              },
            };
          } else if (actionType === CircuitBreakerCallbackActionType.NotNeedProcess) {
            const tvar: any = card.data.template_variable;
            card.data.template_variable = {
              ...(tvar as any),
              title: `【无需跟进】${tvar?.title}`,
              isProcessing: true,
              isFinish: true,
            };
          } else if (actionType === CircuitBreakerCallbackActionType.FinishProcess) {
            const tvar: any = card.data.template_variable;
            card.data.template_variable = {
              ...(tvar as any),
              title: `【结束跟进】${tvar?.title}`,
              mainButtonText: '结束跟进',
              meegoButtonText:
                slardarCircuitBreakerInfo?.meegoLink && meegoLinkReg.test(slardarCircuitBreakerInfo.meegoLink)
                  ? '进群跟进'
                  : '创建Meego缺陷单',
              isProcessing: true,
              isFinish: true,
            };
          } else if (actionType === CircuitBreakerCallbackActionType.FinishTicket) {
            const tvar: any = card.data.template_variable;
            card.data.template_variable = {
              ...(tvar as any),
              title: `【结束跟进】${tvar?.title}`,
              mainButtonText: '结束跟进',
              meegoButtonText:
                slardarCircuitBreakerInfo?.meegoLink && meegoLinkReg.test(slardarCircuitBreakerInfo.meegoLink)
                  ? '进群跟进'
                  : '创建Meego缺陷单',
              isProcessing: true,
              isFinish: true,

              circuitBreakerStatus: circuitBreakerInfo.circuitBreakerStatus,
              affectGray: circuitBreakerInfo.affectGray ? 1 : 0,
              circuitBreakerIssueStatus: circuitBreakerInfo.circuitBreakerIssueStatus,
              circuitBreakerReason: circuitBreakerInfo.circuitBreakerReason,
              circuitBreakerDetailReason: circuitBreakerInfo.circuitBreakerDetailReason,
              circuitBreakerStopLossMean: circuitBreakerInfo.circuitBreakerStopLossMean,
              remark: circuitBreakerInfo.remark,
            };
          }
        }
        this.logger.info(`[CircuitBreaker] slardarCard: ${JSON.stringify(card)}`);
        return card;
      case CircuitBreakerTicketType.Feedback:
        const feedbackCircuitBreakerInfo = circuitBreakerInfo as FeedbackCircuitBreakerTicket;
        const feedbackCard = new LarkCardTemplate(
          actionType === CircuitBreakerCallbackActionType.FinishTicket
            ? LarkTemplateId.FeedbackCircuitBreakFinish
            : LarkTemplateId.FeedbackCircuitBreakerInfo,
          {
            title: `${appName}-${feedbackCircuitBreakerInfo.os}-${feedbackCircuitBreakerInfo.version} `,
            versionCode: feedbackCircuitBreakerInfo.versionCode,
            feedbackDescribe: feedbackCircuitBreakerInfo.feedbackDescribe,
            feedbackType: feedbackCircuitBreakerInfo.feedbackType,
            feedbackLink: feedbackCircuitBreakerInfo.feedbackLink,
            feedbackLevel: feedbackCircuitBreakerInfo.feedbackLevel,
            feedbackStartTime: dayjs
              .unix(feedbackCircuitBreakerInfo.feedbackStartTime)
              .tz('Asia/Shanghai', true)
              .format('YYYY-MM-DD HH:mm:ss'),
            feedbackUserCount: feedbackCircuitBreakerInfo.feedbackUserCount,
            rdbm: versionInfo.bmInfo[BmType.crash]?.email ?? versionInfo.bmInfo[BmType.rd]?.email,
            qabm: versionInfo.bmInfo[BmType.qa]?.email,

            mainButtonText: '开始跟进',
            meegoButtonText:
              feedbackCircuitBreakerInfo?.meegoLink && meegoLinkReg.test(feedbackCircuitBreakerInfo.meegoLink)
                ? '进群跟进'
                : '创建Meego缺陷单',
            isProcessing: false,
            isFinish: false,

            callbackDataProcess: {
              cardCallbackType: CardCallbackType.CircuitBreakerHandle,
              ticketId: feedbackCircuitBreakerInfo.ticketId,
              ticketType: feedbackCircuitBreakerInfo.ticketType,
              actionType: CircuitBreakerCallbackActionType.StartProcess,
              appId: versionInfo.app_id,
            },
            callbackDataNoNeed: {
              cardCallbackType: CardCallbackType.CircuitBreakerHandle,
              ticketId: feedbackCircuitBreakerInfo.ticketId,
              ticketType: feedbackCircuitBreakerInfo.ticketType,
              actionType: CircuitBreakerCallbackActionType.NotNeedProcess,
              appId: versionInfo.app_id,
            },
            callbackDataFinish: {
              cardCallbackType: CardCallbackType.CircuitBreakerHandle,
              ticketId: feedbackCircuitBreakerInfo.ticketId,
              ticketType: feedbackCircuitBreakerInfo.ticketType,
              actionType: CircuitBreakerCallbackActionType.FinishTicket,
              appId: versionInfo.app_id,
            },
            callbackCreateIssue: {
              cardCallbackType: CardCallbackType.CircuitBreakerHandle,
              ticketId: feedbackCircuitBreakerInfo.ticketId,
              ticketType: feedbackCircuitBreakerInfo.ticketType,
              actionType: CircuitBreakerCallbackActionType.CreateMeegoAndJoinChat,
              appId: versionInfo.app_id,
            },
          },
        );
        if (update) {
          if (actionType === CircuitBreakerCallbackActionType.StartProcess) {
            const tvar: any = feedbackCard.data.template_variable;
            feedbackCard.data.template_variable = {
              ...(tvar as any),
              title: `【跟进中】${tvar?.title}`,
              mainButtonText: '结束跟进',
              meegoButtonText:
                feedbackCircuitBreakerInfo?.meegoLink && meegoLinkReg.test(feedbackCircuitBreakerInfo.meegoLink)
                  ? '进群跟进'
                  : '创建Meego缺陷单',
              isProcessing: true,
              isFinish: false,
              callbackDataProcess: {
                cardCallbackType: CardCallbackType.CircuitBreakerHandle,
                ticketId: feedbackCircuitBreakerInfo.ticketId,
                ticketType: feedbackCircuitBreakerInfo.ticketType,
                actionType: CircuitBreakerCallbackActionType.FinishProcess,
                appId: versionInfo.app_id,
              },
              callbackCreateIssue: {
                cardCallbackType: CardCallbackType.CircuitBreakerHandle,
                ticketId: feedbackCircuitBreakerInfo.ticketId,
                ticketType: feedbackCircuitBreakerInfo.ticketType,
                actionType: CircuitBreakerCallbackActionType.CreateMeegoAndJoinChat,
                appId: versionInfo.app_id,
              },
            };
          } else if (actionType === CircuitBreakerCallbackActionType.NotNeedProcess) {
            const tvar: any = feedbackCard.data.template_variable;
            feedbackCard.data.template_variable = {
              ...(tvar as any),
              title: `【无需跟进】${tvar?.title}`,
              isProcessing: true,
              isFinish: true,
            };
          } else if (actionType === CircuitBreakerCallbackActionType.FinishProcess) {
            const tvar: any = feedbackCard.data.template_variable;
            feedbackCard.data.template_variable = {
              ...(tvar as any),
              title: `【结束跟进】${tvar?.title}`,
              mainButtonText: '结束跟进',
              meegoButtonText:
                feedbackCircuitBreakerInfo?.meegoLink && meegoLinkReg.test(feedbackCircuitBreakerInfo.meegoLink)
                  ? '进群跟进'
                  : '创建Meego缺陷单',
              isProcessing: true,
              isFinish: true,
            };
          } else if (actionType === CircuitBreakerCallbackActionType.FinishTicket) {
            const tvar: any = feedbackCard.data.template_variable;
            feedbackCard.data.template_variable = {
              ...(tvar as any),
              title: `【结束跟进】${tvar?.title}`,
              mainButtonText: '结束跟进',
              meegoButtonText:
                feedbackCircuitBreakerInfo?.meegoLink && meegoLinkReg.test(feedbackCircuitBreakerInfo.meegoLink)
                  ? '进群跟进'
                  : '创建Meego缺陷单',
              isProcessing: true,
              isFinish: true,

              circuitBreakerStatus: circuitBreakerInfo.circuitBreakerStatus,
              affectGray: circuitBreakerInfo.affectGray ? 1 : 0,
              circuitBreakerIssueStatus: circuitBreakerInfo.circuitBreakerIssueStatus,
              circuitBreakerReason: circuitBreakerInfo.circuitBreakerReason,
              circuitBreakerDetailReason: circuitBreakerInfo.circuitBreakerDetailReason,
              circuitBreakerStopLossMean: circuitBreakerInfo.circuitBreakerStopLossMean,
              remark: circuitBreakerInfo.remark,
            };
          }
        }
        this.logger.info(`[CircuitBreaker] feedbackCard: ${JSON.stringify(feedbackCard)}`);
        return feedbackCard;
      default:
        break;
    }
  }
}
