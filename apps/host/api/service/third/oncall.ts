import { Inject, Injectable } from '@gulux/gulux';
import OncallModelService from '../model/oncallModel';
import {
  Candidate,
  CascadeStruct,
  DateDetail,
  DutyForecast,
  DutyInfo,
  OnCallCreated,
  OncallDutyResultNew,
  OnCallInfo,
  OnCallMessage,
  OnCallNetworkResult,
  OnCallWorkOrder,
} from '@shared/oncall/businessOnCall';
import _ from 'lodash';
import { NetworkResult, PlatformType } from '@pa/shared/dist/src/core';
import { Temporal } from '@js-temporal/polyfill';
import { UserIdType } from '@pa/shared/dist/src/lark/userInfo';
import { MessageType } from '@pa/shared/dist/src/lark/larkCard';
import { BytedLogger } from '@gulux/gulux/byted-logger';
import { NetworkX } from '../../utils/NetworkX';
import LarkService from '@pa/backend/dist/src/third/lark';
import LarkCardService from '../larkCard';
import commonUtils from '../../utils/commonUtils';
import timeUtil from '../../utils/timeUtil';
import { ModelType } from '@gulux/gulux/typegoose';
import { ExperimentShareInfoTable } from '../../model/ExperimentShareInfo';
import { add_suffix_ne } from '@pa/shared/dist/src/utils/tools';
import CozeService from './coze';
import UserService from '@pa/backend/dist/src/service/user';

const onCallBaseUrl = 'https://data.bytedance.net' as const;
const onCallBackendUrl = 'https://bc-cn-gw.bytedance.net/api/v1/oncall_platform' as const;
// oncall 平台的一级分类
const LV_IOS = 25694;
const LV_ANDROID = 25693;
const CC_IOS = 25696;
const CC_ANDROID = 25695;
// 服务账号密钥
const SERVICR_ACCOUNT_SECRET = '49a42790851ede0021d05908fdbf0cd2';

@Injectable()
export default class OnCallService {
  @Inject()
  private oncallModelService: OncallModelService;

  @Inject()
  private logger: BytedLogger;

  @Inject()
  private larkService: LarkService;

  @Inject()
  private larkCardService: LarkCardService;

  @Inject(ExperimentShareInfoTable)
  private experimentShareInfoModel: ModelType<ExperimentShareInfoTable>;

  @Inject()
  private coze: CozeService;
  /**
   * 新版本:从数据库中查询模块信息
   * @param product
   * @param platform
   * @param business
   */
  async queryModelInfoFromDB(product: string, platform: string, business: string): Promise<CascadeStruct[]> {
    const dbInfo = await this.oncallModelService.queryOnCallInfo(product, platform, business);
    return _(dbInfo)
      .groupBy(it => it.firstModule)
      .mapValues(it => it.map(v => v.secondaryModule))
      .toPairs()
      .map(([k, v]) => ({
        displayName: k,
        value: k,
        children: v.map(model => ({
          value: model,
          label: model,
        })),
      }))
      .value();
  }

  async queryCommercialModelInfo(product: string, platform: string, clientType: string): Promise<any> {
    if (clientType === 'Server') {
      return [
        {
          displayName: '默认',
          value: '默认',
        },
      ];
    } else {
      let theProduct = product;
      if (product === '剪映' || product === '醒图' || product === '即梦') {
        theProduct = 'lv';
      }
      if (product === 'Capcut' || product === 'hypic') {
        theProduct = 'cc';
      }
      let thePlatform = 'Android';
      if (platform === 'ios') {
        thePlatform = 'iOS';
      }
      const dbInfo = await this.oncallModelService.queryOnCallInfoByFirstModule(
        theProduct,
        thePlatform,
        'commercialize',
        '广告',
      );
      return dbInfo.map(it => ({
        value: it.secondaryModule,
        label: it.secondaryModule,
      }));
    }
  }

  async getDutyInfo(onCallInfo: OnCallInfo) {
    const dutyInfo: DutyInfo = {
      backup_users: [],
      bystanders: [],
      primary_users: [],
      qa_users: [],
    };
    // 先更新一下oncall值班顺序，防止当周首次值班人异常
    if (Temporal.Now.zonedDateTimeISO(commonUtils.defaultTimeZone).weekOfYear !== onCallInfo.dutyChangeWeek) {
      onCallInfo.dutyChangeWeek = Temporal.Now.zonedDateTimeISO(commonUtils.defaultTimeZone).weekOfYear;
      onCallInfo.dutyIndex = (onCallInfo.dutyIndex + 1) % onCallInfo.duty.split(',').length;
      await this.oncallModelService.updateOnCallInfo(onCallInfo);
      this.logger.info(`[queryDutyInfoFromDB] updateOnCallInfo1`);
    }
    // 优先查询是否有值班计划配置，如果没有值班计划配置则拉取纸飞机值班配置信息
    let candidate;
    if (onCallInfo.forecast && onCallInfo.forecast.length > 0) {
      candidate = await this.getDutyScheduleForecast(onCallInfo.forecast, timeUtil.nowFormatDateDetail());
      this.logger.info(`[queryDutyInfoFromDB] getDutyScheduleForecast1 ${onCallInfo.forecast}`);
    }
    if (candidate && candidate.id !== -1) {
      // 值班计划查询出了有效的值班人
      dutyInfo.primary_users.push(candidate.primary);
      dutyInfo.backup_users.push(...candidate.backups);
      dutyInfo.bystanders.push(...candidate.bystanders);
    } else {
      dutyInfo.primary_users.push(onCallInfo.duty.split(',')[onCallInfo.dutyIndex]);
      dutyInfo.backup_users.push(
        ...[onCallInfo.duty.split(',')[(onCallInfo.dutyIndex + 1) % onCallInfo.duty.split(',').length]],
      );
      if (onCallInfo.bystanders && onCallInfo.bystanders.length > 0) {
        dutyInfo.bystanders.push(...onCallInfo.bystanders.split(','));
      }
    }
    if (onCallInfo.qa && onCallInfo.qa.length > 0) {
      dutyInfo.qa_users.push(...onCallInfo.qa.split(','));
    }
    return dutyInfo;
  }

  /**
   * 从数据库查询值班人信息
   * "type":"CapCut iOS"
   * "business":{"value":"toolMaterial","name":"业务方向","type":"radio"}
   * "toolMaterial":{"value":["拍摄","美腿"],"name":"工具素材","type":"cascader"}}
   */
  async queryDutyInfoFromDB(type: string, workflow_config: any, remark?: any): Promise<DutyInfo> {
    this.logger.info(`queryDutyInfoFromDB workflow_config:${workflow_config} type:${type}`);
    if (remark) {
      try {
        const remarkInfo = JSON.parse(remark);
        if (remarkInfo?.dutyInfo?.primary_users) {
          return remarkInfo.dutyInfo;
        }
      } catch (e) {}
    }
    if (workflow_config === '') {
      return {
        backup_users: [],
        bystanders: [],
        primary_users: [],
        qa_users: [],
      };
    }
    const workflowConfig = JSON.parse(workflow_config);
    if (!workflowConfig?.business) {
      return {
        backup_users: [],
        bystanders: [],
        primary_users: [],
        qa_users: [],
      };
    }
    const product = type.includes('CapCut') ? 'cc' : 'lv';
    const platform = type.includes('iOS') ? PlatformType.iOS : PlatformType.Android;
    const business = workflowConfig.business.value;
    const modelInfo = workflowConfig[business].value;
    const isBothPlatform = workflowConfig.isBothPlatform ? workflowConfig.isBothPlatform.value : false;
    const onCallInfos = await this.oncallModelService.queryOnCallInfo(product, platform, business);
    this.logger.info(`[queryDutyInfoFromDB] queryOnCallInfo1`);
    if (isBothPlatform) {
      const anotherPlatform = type.includes('iOS') ? PlatformType.Android : PlatformType.iOS;
      const anotherOnCallInfos = await this.oncallModelService.queryOnCallInfo(product, anotherPlatform, business);
      onCallInfos.push(...anotherOnCallInfos);
    }
    const oncallInfo = onCallInfos.find(it => it.firstModule === modelInfo[0] && it.secondaryModule === modelInfo[1]);
    if (oncallInfo) {
      return this.getDutyInfo(oncallInfo);
    }
    return {
      backup_users: [],
      bystanders: [],
      primary_users: [],
      qa_users: [],
    };
  }

  /**
   * 对商业化适配的oncall查询逻辑：
   * workflow_config会和原先的不同
   * @param type
   * @param workflow_config
   * @param remark
   */
  async queryDutyAdOncall(type: string, workflow_config: any, remark?: any): Promise<DutyInfo> {
    this.logger.info(`queryDutyCommercial workflow_config:${workflow_config} type:${type}`);
    if (remark) {
      try {
        const remarkInfo = JSON.parse(remark);
        if (remarkInfo?.dutyInfo?.primary_users) {
          return remarkInfo.dutyInfo;
        }
      } catch (e) {}
    }
    if (workflow_config === '') {
      return {
        backup_users: [],
        bystanders: [],
        primary_users: [],
        qa_users: [],
      };
    }
    const workflowConfig = JSON.parse(workflow_config);
    if (!workflowConfig?.business) {
      return {
        backup_users: [],
        bystanders: [],
        primary_users: [],
        qa_users: [],
      };
    }

    // 如果是server，直接返回值班计划global_ad_server
    if (workflowConfig.ad_oncall_type?.value === 'server') {
      const candidate = await this.getDutyScheduleForecast('global_ad_server', timeUtil.nowFormatDateDetail());
      if (candidate && candidate.id !== -1) {
        return {
          primary_users: [candidate.primary],
          backup_users: candidate.backups,
          bystanders: candidate.bystanders,
          qa_users: [],
        };
      }
      return {
        backup_users: [],
        bystanders: [],
        primary_users: [],
        qa_users: [],
      };
    }
    const business = 'commercialize';
    const businessVal = workflowConfig.business.value;
    let product: string;
    // 匹配相应的product
    if (businessVal === '醒图' || businessVal === '即梦' || businessVal === '剪映') {
      product = 'lv';
    } else if (businessVal === 'hypic' || businessVal === 'Capcut') {
      product = 'cc';
    } else {
      product = 'lv';
    }
    // 光得写的就是ios
    const platform = workflowConfig.ad_app_system_type?.value === 'ios' ? PlatformType.iOS : PlatformType.Android;
    const isBothPlatform = workflowConfig.isBothPlatform?.value ?? false;

    const firstModule = '广告';
    const secondModule = workflowConfig.commercialize_ad.value[0];
    const onCallInfos = await this.oncallModelService.queryOnCallInfo(product, platform, business);
    this.logger.info(`[queryDutyCommercial] onCallInfos: ${JSON.stringify(onCallInfos)}`);
    if (isBothPlatform) {
      const anotherPlatform =
        workflowConfig.ad_app_system_type?.value === 'ios' ? PlatformType.Android : PlatformType.iOS;
      const anotherOnCallInfos = await this.oncallModelService.queryOnCallInfo(product, anotherPlatform, business);
      onCallInfos.push(...anotherOnCallInfos);
    }
    const oncallInfo = onCallInfos.find(it => it.firstModule === firstModule && it.secondaryModule === secondModule);
    this.logger.info(`[queryDutyCommercial] onCallInfo: ${JSON.stringify(oncallInfo)}`);
    if (oncallInfo) {
      return this.getDutyInfo(oncallInfo);
    }
    return {
      backup_users: [],
      bystanders: [],
      primary_users: [],
      qa_users: [],
    };
  }
  /**
   * https://bytedance.feishu.cn/wiki/Ogj3wDEhViruxBkXfhYc9vaFnch
   * @param onCallDutyName
   * @param dateTime
   */
  async getDutyScheduleForecast(onCallDutyName: string, dateTime: string): Promise<Candidate> {
    const network = new NetworkX(onCallBaseUrl, {});
    const result = await network.get<OncallDutyResultNew>(`/duty/api/open/duty/${onCallDutyName}/`, {
      datetime: dateTime,
    });
    if (result?.oncall === undefined) {
      return {
        id: -1,
        backups: [],
        primary: '',
        bystanders: [],
      };
    }
    return {
      id: 0,
      backups: result.oncall.backup_users ?? [],
      primary: result.oncall.primary_user ?? result.oncall.primary ?? '',
      bystanders: result.oncall.bystanders ?? [],
    };
  }

  /**
   * 处理OnCall自定义指令:https://bytedance.feishu.cn/docx/UJB8dtjOGoqwhcx8bphcQlarnEh
   * @param customKey
   * @param message
   * @param oncallFlow
   */
  async dealOnCallCustomInstruction(customKey: string, message: OnCallMessage, oncallFlow: OnCallWorkOrder) {
    const openChatId = message.open_chat_id;
    const { oncall_chat_id, originator_user, id } = oncallFlow;

    // eslint-disable-next-line default-case
    switch (customKey) {
      case 'info': {
        await this.larkService.sendMessage(
          UserIdType.chatId,
          message.open_chat_id,
          JSON.stringify(this.larkCardService.buildOnCallTechnologyTipCard(originator_user)),
          MessageType.interactive,
        );
        break;
      }
      case 'lvinvite': {
        const params = {
          onCallChatId: oncall_chat_id,
          openChatId,
          onCallFlowId: id,
        };
        const extras = Buffer.from(JSON.stringify(params)).toString('base64');
        await this.larkService.sendCardMessage(
          UserIdType.chatId,
          message.open_chat_id,
          this.larkCardService.buildOnCallInviteCard(extras),
        );
        break;
      }
      case 'qa': {
        const dutyInfo = await this.queryDutyInfoFromDB(
          oncallFlow.oncall_question_type,
          oncallFlow.workflow_config,
          oncallFlow.remark,
        );
        if (dutyInfo.qa_users && dutyInfo.qa_users.length > 0) {
          for (const qa of dutyInfo.qa_users) {
            await this.invitePeopleInOnCallGroup(oncall_chat_id, qa, openChatId, `业务QA`);
          }
        } else {
          await this.larkService.sendTextMessage(UserIdType.chatId, openChatId, `找不到QA值班人`);
        }
        break;
      }
      case 'lvauto': {
        await this.autoSwitchOnCall(oncallFlow, false, message.open_chat_id);
        break;
      }
    }
  }

  /**
   * 邀请自定义poc进指定onCall群
   */
  async invitePeopleInOnCallGroup(chatId: number, email: string, openChatId: string, extraMsg = '') {
    // 这里不在oncall改造范围内，使其不受影响
    const prevBackendUrl = 'https://oncall-backend.bytedance.net';
    const network = new NetworkX(prevBackendUrl, {
      'x-tenant-token': `6382-PaperAirplaneAuto-${email}`,
    });
    console.log(chatId);
    const result = await network.get<OnCallNetworkResult<DutyForecast>>(`/api/inf/v1/chat/user2chat`, {
      oncall_chat_id: chatId,
    });
    if (result.code === 400) {
      // 其他产品转过来的，byteOncall机器人 无权限操作拉人，转为纸飞机拉人
      const userId = await this.larkService.batchGetUserId(UserIdType.userId, {
        emails: [`${email}@bytedance.com`],
      });
      if (userId) {
        await this.larkService.addUserToChatGroup(openChatId, UserIdType.userId, [userId[0].user_id]);
      }
    }
    // 纸飞机向群里面发查询值班人成功的信息，指明值班人姓名，防止值班人已经在群里以为指令没反应
    await this.larkService.sendTextMessage(UserIdType.chatId, openChatId, `${extraMsg}值班人：${email}`);
    return result;
  }

  async switchOncallBusiness(
    flowId: number,
    chatId: number,
    openChatId: string,
    business: string[],
    dutyInfo: DutyInfo,
    isVe = false,
    workflow_config: any = undefined,
  ) {
    const inviteUsers = dutyInfo.primary_users.concat(dutyInfo.backup_users).concat(dutyInfo.bystanders);
    const userId = await this.larkService.batchGetUserId(UserIdType.userId, {
      emails: inviteUsers.map(item => add_suffix_ne('@bytedance.com')(item)),
    });
    if (userId) {
      await this.larkService.addUserToChatGroup(
        openChatId,
        UserIdType.userId,
        userId.map(v => v.user_id),
      );
    }
    await this.larkService.sendCardMessage(
      UserIdType.chatId,
      openChatId,
      this.larkCardService.buildOnCallSwitchCard(business, dutyInfo),
    );
    // 保存信息到oncall平台，这里使用了remark字段保存
    const res = await this.editOnCallFlowDetail(
      flowId,
      JSON.stringify({
        business,
        dutyInfo,
      }),
    );
    if (isVe) {
      const network = new NetworkX('https://mn7zexav.fn.bytedance.net/api/Notify/Oncall');
      const data = {
        oncallId: flowId,
        workflow_config,
        dutyInfo,
      };
      const reportInfo = await network.post('/otherOncallToCCCreatorInviteDuty', data);
      this.logger.info(`reportVeOncall req:${JSON.stringify(data)} res:${JSON.stringify(reportInfo)}`);
    }
    return res;
  }

  async editOnCallFlowDetail(flowId: number, remark: string) {
    const network = new NetworkX(onCallBackendUrl, {
      // 'x-tenant-token': `6382-PaperAirplaneAuto-chenruoxin.0910`,
      Authorization: SERVICR_ACCOUNT_SECRET,
    });
    const res = await network.put<NetworkResult<any>>(`/api/inf/v1/oncall_flow/${flowId}`, {
      remark,
    });
    this.logger.info(`editOnCallFlowDetail ${flowId} ${remark}`, res);
    return res;
  }

  async invitePeopleInOnCallGroupByChatId(chatId: string, email: string) {
    // 这里不在oncall改造范围内，使其不受影响
    const prevBackendUrl = 'https://oncall-backend.bytedance.net';
    const network = new NetworkX(prevBackendUrl, {
      'x-tenant-token': `6382-PaperAirplaneAuto-${email}`,
    });
    console.log(chatId);
    const res = await network.get<OnCallNetworkResult<DutyForecast>>(`/api/inf/v1/chat/user2chat`, {
      chat_id: chatId,
    });
    console.log(res);
  }

  /**
   * 获取用户JWT token
   */
  private async getJwtToken(): Promise<string> {
    try {
      const jwtNetwork = new NetworkX('https://cloud.bytedance.net', {
        Authorization: SERVICR_ACCOUNT_SECRET,
      });

      const response = await jwtNetwork.getWithHead('/auth/api/v1/jwt');
      if (response && response.headers && response.headers['x-jwt-token']) {
        this.logger.info('[getJwtToken] Successfully obtained JWT token');
        return response.headers['x-jwt-token'] as string;
      }

      this.logger.error('[getJwtToken] JWT token not found in response headers');
      throw new Error('JWT token not found in response headers');
    } catch (error: any) {
      this.logger.error('[getJwtToken] Failed to get JWT token:', error);
      if (error?.code === 401 || error?.code === 403) {
        throw new Error('Session expired or authentication failed');
      }
      throw error;
    }
  }

  async createOnCallGroup(
    email: string,
    config: string,
    groupName: string,
    product: string,
    platform: string,
    priority: string,
    id: string,
    url: string,
  ) {
    // 获取JWT token
    const jwtToken = await this.getJwtToken();

    const network = new NetworkX(onCallBackendUrl, {
      // 'x-tenant-token': `6382-PaperAirplaneAuto-${email}`,
      'x-jwt-token': jwtToken,
    });
    let typeId = 25693; // lv-Android
    if (platform === 'iOS') {
      typeId += 1; // lv-iOS 35694
    }
    if (product === 'cc') {
      typeId += 2; // cc-Android 35695, cc-iOS 35696
    }
    const result = await network.post<OnCallNetworkResult<OnCallCreated>>(`/api/inf/v1/chat`, {
      tenant_id: 6382,
      trigger_message: groupName,
      type_id: typeId,
      workflow_config: config,
      priority,
      accept_user_agreement: true,
      force_oncall: true,
      source_location: url,
      urgent_description: groupName,
      region: product === 'lv' ? 'cn' : 'us',
      skip_type_diagnosis: false,
    });
    console.log(result);
    if (result.code === 0) {
      const chatId = result.data?.chat_id;
      if (chatId) {
        await this.updateOncallId(id, chatId);
        return chatId;
      }
    }
  }
  async updateOncallId(id: string, chatId: string | undefined) {
    const experiment = await this.experimentShareInfoModel.updateOne(
      {
        id,
      },
      { $set: { oncallId: chatId } },
    );
    return experiment.ok;
  }

  /**
   * nCallFlow工单查询接口
   * @param createTimeStart
   * @param createTimeEnd
   * @param onCallQuestionTypeId 分类id
   */
  async onCallFlowList(createTimeStart: string, createTimeEnd: string, onCallQuestionTypeId: number): Promise<any> {
    const user = 'chenruoxin.0910';
    const network = new NetworkX(onCallBackendUrl, {
      // 'x-tenant-token': `6382-PaperAirplaneAuto-${user}`,
      Authorization: SERVICR_ACCOUNT_SECRET,
    });
    const result: { data: any[] } = { data: [] };
    for (let page = 1; ; page++) {
      const param = {
        filter_fields: [
          {
            field: 'tenant_id',
            value: '6382',
            operator: 'equals',
          },
          {
            field: 'create_time',
            value: createTimeStart,
            operator: 'greater',
          },
          {
            field: 'create_time',
            value: createTimeEnd,
            operator: 'less',
          },
        ],
        extra_arguments: [],
        sort: {
          field: 'id',
          operator: 'asc',
        },
        current_page: page,
        page_size: 100,
      };
      if (onCallQuestionTypeId > 0) {
        param.filter_fields = param.filter_fields.concat({
          field: 'oncall_question_type_id',
          value: onCallQuestionTypeId.toString(),
          operator: 'equals',
        });
      }
      const res: any = await network.post<OnCallNetworkResult<OnCallWorkOrder>>(`/api/inf/v1/oncall_flow/list`, param);
      if (res && res.code === 0) {
        result.data.push(...res.data);
        if (res.page.total_page <= page) {
          break;
        }
      } else {
        break;
      }
    }

    return result;
  }

  /**
   * 字节公共日历详情
   */
  async dateDetail(timestamp: number): Promise<DateDetail | undefined> {
    // 这里不在oncall改造范围内，使其不受影响
    const prevBackendUrl = 'https://oncall-backend.bytedance.net';
    const network = new NetworkX(prevBackendUrl, {
      'x-tenant-token': `6382-PaperAirplaneAuto-chenruoxin.0910`,
    });
    const result = await network.get<NetworkResult<DateDetail>>(`/api/inf/v1/tools/date_detail`, {
      timestamp,
    });
    return result.data;
  }

  async isOffDay(millSecs: number): Promise<boolean> {
    const result = await this.dateDetail(Math.floor(millSecs / 1000));
    return Boolean(result && !result.is_bytedance_workday);
  }

  async recommendOncallCategory(title: string, conversation: string, categoryList: OnCallInfo[]) {
    const simpleCategoryList = categoryList.map(item => ({
      id: item.id,
      businessCN: item.businessCN,
      firstModule: item.firstModule,
      secondaryModule: item.secondaryModule,
    }));
    this.logger.info('[SwitchOnCall] recommend req:', {
      title,
      conversation,
      simpleCategoryList,
    });
    const result = await this.coze.executCozeWorkFLow<string>('7488666375126646847', {
      TITLE: title,
      CONVERSATION: conversation,
      CATEGORY: JSON.stringify(simpleCategoryList),
    });
    this.logger.info('[SwitchOnCall] recommend res:', result);
    const data = JSON.parse(result[0] ?? '{}') as { id: string; reason: string };
    return {
      oncallInfo: categoryList.find(item => item.id === data.id),
      reason: data.reason,
      debugUrl: result[1]?.debug_url,
    };
  }

  async autoSwitchOnCall(oncallFlow: OnCallWorkOrder, defaultTip = true, openChatId: string | undefined = undefined) {
    this.logger.info('[SwitchOnCall] start', oncallFlow);
    if ([LV_IOS, CC_IOS, LV_ANDROID, CC_ANDROID].includes(oncallFlow.oncall_question_type_id)) {
      const product = [LV_IOS, LV_ANDROID].includes(oncallFlow.oncall_question_type_id) ? 'lv' : 'cc';
      const platform = [LV_IOS, CC_IOS].includes(oncallFlow.oncall_question_type_id)
        ? PlatformType.iOS
        : PlatformType.Android;
      const category = await this.oncallModelService.queryOnCallInfoByProductPlatform(product, platform);
      const res = await this.recommendOncallCategory(oncallFlow.name, oncallFlow.message_logs ?? '', category).catch(
        () => undefined,
      );
      if (res?.oncallInfo) {
        const dutyInfo = await this.getDutyInfo(res.oncallInfo);
        this.logger.info('[SwitchOnCall] getDutyInfo res:', res);
        if (dutyInfo.primary_users.length > 0) {
          const open_chat_id = openChatId ?? (await this.larkService.chatId2OpenChatId(oncallFlow.chat_id_str));
          const inviteUsers = dutyInfo.primary_users.concat(dutyInfo.backup_users).concat(dutyInfo.bystanders);
          const userId = await this.larkService.batchGetUserId(UserIdType.userId, {
            emails: inviteUsers.map(item => add_suffix_ne('@bytedance.com')(item)),
          });
          this.logger.info('[SwitchOnCall] batchGetUserId:', userId);
          if (userId) {
            this.logger.info('[SwitchOnCall] addUserToChatGroup:', open_chat_id);
            await this.larkService.addUserToChatGroup(
              open_chat_id,
              UserIdType.userId,
              userId.map(v => v.user_id),
            );
          }
          // 保存信息到oncall平台，这里使用了remark字段保存
          const business = [res.oncallInfo.business, res.oncallInfo.firstModule, res.oncallInfo.secondaryModule];
          const editRes = await this.editOnCallFlowDetail(
            oncallFlow.id,
            JSON.stringify({
              business,
              dutyInfo,
            }),
          );
          this.logger.info('[SwitchOnCall] editOnCallFlow res:', editRes);
          const params = {
            onCallChatId: oncallFlow.oncall_chat_id,
            openChatId: open_chat_id,
            onCallFlowId: oncallFlow.id,
          };
          const extras = Buffer.from(JSON.stringify(params)).toString('base64');
          return this.larkService.sendCardMessage(
            UserIdType.chatId,
            open_chat_id,
            this.larkCardService.buildOnCallRecommendCard(
              extras,
              business,
              dutyInfo,
              defaultTip,
              res.reason,
              res.debugUrl,
            ),
          );
        }
      }
    }
    if (oncallFlow.oncall_question_type_id === 66252) {
      this.logger.info('[SwitchOnCall] 是商业化ad，返回');
      return;
    }
    this.logger.info('[SwitchOnCall] sendOldSwitchTips');
    return this.sendOldSwitchTips(oncallFlow);
  }

  async sendOldSwitchTips(oncallFlow: OnCallWorkOrder) {
    // 旧逻辑
    const open_chat_id = await this.larkService.chatId2OpenChatId(oncallFlow.chat_id_str);
    const params = {
      onCallChatId: oncallFlow.oncall_chat_id,
      openChatId: open_chat_id,
      onCallFlowId: oncallFlow.id,
    };
    const extras = Buffer.from(JSON.stringify(params)).toString('base64');
    return this.larkService.sendCardMessage(
      UserIdType.chatId,
      open_chat_id,
      this.larkCardService.buildOnCallInviteCard(
        extras,
        `检测从其他业务线切换到剪映OnCall，当前值班人为**默认兜底人**，兜底人不处理具体问题**<font color='red'>请勿直接at</font>**。`,
      ),
    );
  }
}
