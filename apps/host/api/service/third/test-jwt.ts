/**
 * 测试JWT token获取功能
 * 这个文件用于验证JWT token获取和使用的实现
 */

import { NetworkX } from '../../utils/NetworkX';

// 服务账号密钥
const SERVICR_ACCOUNT_SECRET = '49a42790851ede0021d05908fdbf0cd2';

/**
 * 测试JWT token获取功能
 */
async function testJwtTokenRetrieval() {
  console.log('开始测试JWT token获取...');

  try {
    // 创建网络请求实例
    const jwtNetwork = new NetworkX('https://bc-cn-gw.bytedance.net', {
      Authorization: SERVICR_ACCOUNT_SECRET,
    });

    console.log('发送JWT token请求...');
    const response = await jwtNetwork.get('/auth/api/v1/jwt');

    console.log('响应状态:', response.code);
    console.log('响应头:', response.headers);

    // 检查是否有JWT token
    if (response && response.headers && response.headers['x-jwt-token']) {
      console.log('✅ 成功获取JWT token:', `${response.headers['x-jwt-token'].substring(0, 20)}...`);
      return response.headers['x-jwt-token'];
    } else {
      console.log('❌ 未找到JWT token');
      console.log('完整响应:', JSON.stringify(response, null, 2));
      return null;
    }
  } catch (error: any) {
    console.error('❌ JWT token获取失败:', error);
    return null;
  }
}

/**
 * 测试使用JWT token创建OnCall群组
 */
async function testCreateOnCallGroupWithJwt(jwtToken: string) {
  console.log('\n开始测试使用JWT token创建OnCall群组...');

  try {
    const onCallBackendUrl = 'https://bc-cn-gw.bytedance.net/api/v1/oncall_platform/';
    const network = new NetworkX(onCallBackendUrl, {
      'x-jwt-token': jwtToken,
    });

    console.log('JWT token已设置，准备发送请求...');

    // 这里只是测试网络连接，不会真正创建群组
    // 实际使用时需要传入正确的参数
    console.log('✅ NetworkX实例创建成功，JWT token已配置');
    return true;
  } catch (error: any) {
    console.error('❌ 使用JWT token创建NetworkX实例失败:', error);
    return false;
  }
}

/**
 * 主测试函数
 */
async function runTests() {
  console.log('=== JWT Token 功能测试 ===\n');

  // 测试1: 获取JWT token
  const jwtToken = await testJwtTokenRetrieval();

  if (jwtToken) {
    // 测试2: 使用JWT token
    await testCreateOnCallGroupWithJwt(jwtToken);
  }

  console.log('\n=== 测试完成 ===');
}

// 如果直接运行此文件，执行测试
if (require.main === module) {
  runTests().catch(console.error);
}

export { testJwtTokenRetrieval, testCreateOnCallGroupWithJwt, runTests };
