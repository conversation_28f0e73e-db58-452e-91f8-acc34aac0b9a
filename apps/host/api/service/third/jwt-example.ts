/**
 * JWT Token 使用示例
 * 展示如何在其他服务中使用JWT token获取功能
 */

import { NetworkX } from '../../utils/NetworkX';

// 服务账号密钥
const SERVICR_ACCOUNT_SECRET = '49a42790851ede0021d05908fdbf0cd2';

/**
 * JWT Token 工具类
 * 提供JWT token获取和管理功能
 */
export class JwtTokenManager {
  private static instance: JwtTokenManager;
  private cachedToken: string | null = null;
  private tokenExpiry = 0;

  private constructor() {}

  /**
   * 获取单例实例
   */
  static getInstance(): JwtTokenManager {
    if (!JwtTokenManager.instance) {
      JwtTokenManager.instance = new JwtTokenManager();
    }
    return JwtTokenManager.instance;
  }

  /**
   * 获取JWT token
   * 支持缓存机制（可选）
   */
  async getJwtToken(useCache = false): Promise<string> {
    // 如果使用缓存且token未过期，直接返回缓存的token
    if (useCache && this.cachedToken && Date.now() < this.tokenExpiry) {
      return this.cachedToken;
    }

    try {
      const jwtNetwork = new NetworkX('https://bc-cn-gw.bytedance.net', {
        Authorization: SERVICR_ACCOUNT_SECRET,
      });

      const response = await jwtNetwork.get('/auth/api/v1/jwt');

      if (response && response.headers && response.headers['x-jwt-token']) {
        const token = response.headers['x-jwt-token'] as string;

        // 如果使用缓存，保存token（假设token有效期为1小时）
        if (useCache) {
          this.cachedToken = token;
          this.tokenExpiry = Date.now() + 60 * 60 * 1000; // 1小时后过期
        }

        return token;
      }

      throw new Error('JWT token not found in response headers');
    } catch (error: any) {
      console.error('[JwtTokenManager] Failed to get JWT token:', error);

      // 处理session过期等错误
      if (error?.code === 401 || error?.code === 403) {
        throw new Error('Session expired or authentication failed');
      }

      throw error;
    }
  }

  /**
   * 清除缓存的token
   */
  clearCache(): void {
    this.cachedToken = null;
    this.tokenExpiry = 0;
  }

  /**
   * 创建带JWT token的NetworkX实例
   */
  async createAuthenticatedNetwork(baseUrl: string, additionalHeaders: Record<string, string> = {}): Promise<NetworkX> {
    const jwtToken = await this.getJwtToken();

    return new NetworkX(baseUrl, {
      'x-jwt-token': jwtToken,
      ...additionalHeaders,
    });
  }
}

/**
 * 使用示例：在OnCall服务中使用JWT token
 */
export class OnCallServiceExample {
  private jwtManager = JwtTokenManager.getInstance();

  /**
   * 示例：创建OnCall群组（使用JWT token）
   */
  async createOnCallGroupExample(
    email: string,
    config: string,
    groupName: string,
    product: string,
    platform: string,
    priority: string,
    id: string,
    url: string,
  ) {
    try {
      // 方法1: 直接获取JWT token
      const jwtToken = await this.jwtManager.getJwtToken();
      const network = new NetworkX('https://bc-cn-gw.bytedance.net/api/v1/oncall_platform/', {
        'x-jwt-token': jwtToken,
      });

      // 方法2: 使用工具方法创建认证的NetworkX实例
      // const network = await this.jwtManager.createAuthenticatedNetwork(
      //   'https://bc-cn-gw.bytedance.net/api/v1/oncall_platform/'
      // );

      console.log('JWT token已配置，准备创建OnCall群组...');

      // 这里是实际的API调用逻辑
      // const result = await network.post('/api/inf/v1/chat', { ... });

      return { success: true, message: 'OnCall群组创建成功' };
    } catch (error: any) {
      console.error('创建OnCall群组失败:', error);

      if (error?.message?.includes('Session expired')) {
        // 处理session过期的情况
        return { success: false, message: '用户session已过期，请重新登录' };
      }

      return { success: false, message: '创建OnCall群组失败' };
    }
  }

  /**
   * 示例：其他需要JWT认证的API调用
   */
  async callAuthenticatedApi(endpoint: string, data: any) {
    try {
      const network = await this.jwtManager.createAuthenticatedNetwork(
        'https://bc-cn-gw.bytedance.net/api/v1/oncall_platform/',
      );

      const result = await network.post(endpoint, data);
      return result;
    } catch (error: any) {
      console.error('API调用失败:', error);
      throw error;
    }
  }
}

/**
 * 使用示例
 */
export async function exampleUsage() {
  const onCallService = new OnCallServiceExample();

  try {
    // 示例1: 创建OnCall群组
    const result = await onCallService.createOnCallGroupExample(
      '<EMAIL>',
      '{}',
      'Test Group',
      'lv',
      'iOS',
      'high',
      'test-id',
      'https://example.com',
    );

    console.log('创建结果:', result);

    // 示例2: 调用其他认证API
    // const apiResult = await onCallService.callAuthenticatedApi('/some/endpoint', { data: 'test' });
  } catch (error: any) {
    console.error('示例执行失败:', error);
  }
}
