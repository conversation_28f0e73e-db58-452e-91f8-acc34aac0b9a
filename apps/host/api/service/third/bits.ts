import {
  ApprovedType,
  isMultiMr,
  MeegoBindMrInfo,
  MergeRequestReviewerRequest,
  MrIdAndT<PERSON><PERSON>,
  Mr<PERSON>n<PERSON>,
  MrReviewerInfo,
  MrState,
  MrType,
  ReviewerInfo,
  ReviewRole,
} from '@shared/bits/mrInfo';
import {
  ArtifactListResp,
  BitsPipelineTemplateResult,
  BitsResult,
  ChangedCommit,
  ConflictDetectTaskResult,
  IntegrationInfo,
  MergeRequestProjectBranches,
  ReleaseWorkFlowInfo,
  TaskResult,
  TFGroupList,
  TimelineItem,
  WorkflowExecInfo,
  WorkFlowIntegrationVersionInfo,
} from '@shared/bits';
import { PipelineInfo } from '@shared/bits/pipelineInfo';
import { Inject, Injectable } from '@gulux/gulux';
import { NetworkX } from '../../utils/NetworkX';
import TccService from './tcc';
import { compact, head, pick, some, uniq, without } from 'lodash';
import { MrSearchRequest } from '@shared/bits/mrSearchRequest';
import { BytedLogger } from '@gulux/gulux/byted-logger';
import { BranchInfo, NetworkCode, NetworkResult, PlatformType } from '@pa/shared/dist/src/core';
import { CreateMrResult, PostOpenapiMergeRequestCherryPickRequest, PublishType } from '@shared/bits/createMrData';
import { BmInfo, Duty, VEOnCallInfo } from '@shared/bits/bmInfo';
import { Components } from '@shared/bits/components';
import { MrCustomInfo } from '@shared/bits/mrCustomInfo';
import { BitsTemplateData } from '@shared/bits/bitsTemplateData';
import { TemplateBuildResult } from '@shared/bits/templateBuildResult';
import { TemplateBuildExtraInfo } from '@shared/bits/templateBuildExtraInfo';
import { MrPipelineInfo } from '@shared/bits/mrPipelineInfo';
import { MrComponentsInfo } from '@shared/bits/mrComponentInfo';
import { MrRelationInfo } from '@shared/bits/mrRelationInfo';
import { MergeRequestReviewInfoResponse, MrReviewersInfo } from '@shared/bits/mrReviewersInfo';
import { MrMeegoInfo } from '@shared/bits/mrMeegoInfo';
import produce from 'immer';
import { ModelComponentInfo, SearchComponent, SearchComponentResult } from '@shared/bits/searchComponents';
import ComponentModelService from '../model/componentModel';
import { ReleaseRecords } from '@shared/bits/release';
import { SendPackagesRequest } from '@shared/customBuild/buildResult';
import { JobStepInfo } from '@shared/bits/jobInfo';
import { DataItem, EventsItem, mrPackage } from '@shared/bits/calendar';
import GitLabService from './gitlab';
import { LVProductType, PCInfo, ProductType, StageInfosItem } from '@shared/process/versionProcess';
import { BitsBranchData, BitsBranchInfo } from '@shared/bits/branch';
import { IntegrationMrsRequest, IntegrationMrsResponse, VersionGroup } from '@shared/bits/integration';
import { MrSnapshot } from '@shared/process/versionSnapshot';
import versionUtils from '../../utils/versionUtils';
import {
  MrStoryVersionInfo,
  StoryIssueInfo,
  StoryMeegoStatus,
  StoryMeegoVersionInfo,
} from '@shared/meego/WorkItemResult';
import { FieldValue, FieldValueTree } from '@shared/meego/MeegoCommon';
import { getBitsSpacesById } from '@shared/bus/config';
import { SpaceType } from '@shared/bus/busType';
import MeegoService from './meego';
import axios from 'axios';
import { ConfigPublishStatus, PublishStatus } from '@shared/meego/CustomData';
import BusinessConfigService from '@pa/backend/dist/src/service/businessConfig';
import { AppSettingId } from '@pa/shared/dist/src/appSettings/appSettings';
import { VersionProcessInfo } from '@shared/releasePlatform/versionStage';

@Injectable()
export default class BitsService {
  @Inject()
  private tcc: TccService;

  @Inject()
  private logger: BytedLogger;

  @Inject()
  private componentModelService: ComponentModelService;

  @Inject()
  private gitlabService: GitLabService;

  @Inject()
  private meegoService: MeegoService;

  @Inject()
  private businessConfigService: BusinessConfigService;

  createRequest(creator?: string): NetworkX {
    const key = this.tcc.getBitsKey();
    return new NetworkX('https://bits.bytedance.net/openapi', {
      Authorization: `Bearer ${key}`,
      'Content-Type': 'application/json',
      ...(creator
        ? {
            'x-bits-user': creator,
          }
        : {}),
    });
  }

  async createArtifactRequest(creator?: string) {
    const key = await this.getArtifactToken();
    return new NetworkX('https://bits.bytedance.net', {
      'X-Jwt-Token': `Bearer ${key}`,
      'Content-Type': 'application/json',
      ...(creator
        ? {
            'x-bits-user': creator,
          }
        : {}),
    });
  }

  createPrivateRequest(creator?: string): NetworkX {
    const key = this.tcc.getBitsKey();
    return new NetworkX('https://bits.bytedance.net/api', {
      Authorization: `Bearer ${key}`,
      'Content-Type': 'application/json',
      ...(creator
        ? {
            'x-bits-user': creator,
          }
        : {}),
    });
  }

  getBitsBuildCallBackUrl(): string {
    return `${process.env.CUSTOM_NODE_DOMAIN}/api/bitsBuildCallback`;
  }

  async setSuperMr(mrInfo: MrInfo, toSuper: boolean) {
    const params = {
      project_id: mrInfo.project_id.toString(),
      iid: mrInfo.iid,
      action: toSuper ? 'set' : 'cancel',
    };
    const request = this.createRequest();
    const superMrResult = await request.post<BitsResult<string>>('/merge_request/super', params);
    if (superMrResult.code === 200) {
      return true;
    } else {
      return false;
    }
  }

  async fetchAllMrInfo(mrIds: number[]): Promise<MrInfo[]> {
    const allTasks = uniq(mrIds).map((id: number) => this.getMrInfo({ mrId: id }));
    return await Promise.all(allTasks).then(compact);
  }

  async addUser(query: { mr_id?: number; username: string; member_type?: string }) {
    const request = this.createRequest();
    const params = {
      mr_id: query.mr_id,
      username: query.username,
      member_type: query.member_type,
    };
    const result = await request.post<BitsResult<MrInfo>>('/merge_request/lark/group/add/user', params);
    this.logger.info(JSON.stringify(result));
    return result.data;
  }

  async removeUser(query: { mr_id?: number; username: string }) {
    const request = this.createRequest();
    const params = {
      mr_id: query.mr_id,
      username: query.username,
    };
    const result = await request.post<BitsResult<MrInfo>>('/merge_request/lark/group/remove/user', params);
    this.logger.info(JSON.stringify(result));
    return result.data;
  }

  async remindCodeReview(mrId: number, userName?: string) {
    const request = this.createRequest();
    const body = {
      mr_id: mrId,
    };
    const result = await request.post<BitsResult<MrInfo>>('/merge_request/review/remind', body);
    this.logger.info(JSON.stringify(result));
    return result.data;
  }

  async remindQACodeReview(mrId: number, userName?: string) {
    const request = this.createRequest(userName);
    const body = {
      mr_id: mrId,
    };
    const result = await request.post<BitsResult<MrInfo>>('/merge_request/qa_review/remind', body);
    this.logger.info(JSON.stringify(result));
    return result.data;
  }

  async getMrInfo(query: { mrId?: number; projectId?: number; iid?: number }): Promise<MrInfo | undefined> {
    const params: Record<string, number> = {};
    if (query.mrId) {
      params.mr_id = query.mrId;
    } else if (query.projectId && query.iid) {
      params.project_id = query.projectId;
      params.iid = query.iid;
    } else {
      return undefined;
    }
    const request = this.createRequest();
    const result = await request.get<BitsResult<MrInfo>>('/merge_request/info', params);
    this.logger.info(JSON.stringify(result));
    return result.data;
  }

  async searchAllMr(mrSearchArgs: MrSearchRequest, maxCount = 100): Promise<number[]> {
    if (maxCount === 0) {
      return [];
    }
    const searchResult: number[] = [];
    while (true) {
      if (searchResult.length > 0) {
        mrSearchArgs.last_id = searchResult[searchResult.length - 1];
      }
      const result = await this.searchMr(mrSearchArgs);
      searchResult.push(...result);
      if (result.length === 0 || searchResult.length >= maxCount) {
        break;
      }
    }
    return searchResult;
  }

  async searchMr(mrSearchArgs: MrSearchRequest): Promise<number[]> {
    const request = this.createRequest();
    const result = await request.get<BitsResult<number[]>>('/merge_request/search', mrSearchArgs);
    this.logger.info(JSON.stringify(result));
    if (result.code === 200) {
      if (result.data) {
        return result.data;
      } else {
        return [];
      }
    } else {
      this.logger.error(JSON.stringify(result));
      return [];
    }
  }
  async searchBranch(projectId: number, branch: string): Promise<BranchInfo[]> {
    this.logger.info('搜索分支');
    const param = {
      project_id: projectId,
      sorts: [
        {
          field: 'update_time',
          order: 'desc',
        },
      ],
      text: branch,
      limit: 150,
    };
    const request = this.createRequest();
    const result = await request.post<BitsBranchData>('/merge_request/project/branches', param);

    return result.data.list
      .filter(
        (value: BitsBranchInfo) =>
          !value.branch.startsWith('p/robot') &&
          !value.branch.startsWith('feature/merge') &&
          value.branch.startsWith(branch),
      )
      .sort((a: BitsBranchInfo, b: BitsBranchInfo) => a.update_time - b.update_time)
      .map(
        (item: BitsBranchInfo) =>
          ({
            branch: item.branch,
            commit: item.commit,
            username: item.username,
            create_time: item.create_time,
            update_time: item.update_time,
          }) as BranchInfo,
      );
  }

  async createMr(params: any, creator?: string): Promise<NetworkResult<CreateMrResult>> {
    const blackUserList: string[] = ['xiexiaoyu.thea', 'zhanxing'];
    const request = this.createRequest(creator && !blackUserList.includes(creator) ? creator : undefined);
    const result = await request.post<NetworkResult<CreateMrResult>>('/merge_request/create', params);
    if (result.code === 200) {
      return {
        code: NetworkCode.Success,
        data: result.data,
        message: 'success',
      };
    }
    this.logger.info('[createMr]', JSON.stringify(result), creator);
    return { code: NetworkCode.Error, message: result.message };
  }

  async requestVersionMaster(groupName: string, version: string): Promise<BmInfo[]> {
    const request = this.createRequest();
    const result = await request.get<BitsResult<BmInfo[]>>('/build_master/version_master', {
      group_name: groupName,
      version,
    });
    return Array.isArray(result.data) ? result.data : [];
  }

  async requestProjectRepo(project_id: string, host_project_id: string): Promise<Components | undefined> {
    const request = this.createRequest();
    const result = await request.get<BitsResult<Components>>('/config/project/repos', {
      project_id,
      host_project_id,
    });
    return result.data;
  }

  async getMrCustomInfo(project_id: number, iid: number, mr_id: number): Promise<MrCustomInfo | undefined> {
    const request = this.createRequest();
    const result = await request.get<BitsResult<string>>('/merge_request/custom/define/params', {
      project_id: `${project_id}`,
      iid: `${iid}`,
      mr_id: `${mr_id}`,
    });

    if (result.data) {
      const customFields = JSON.parse(result.data) as MrCustomInfo;
      this.logger.info(result.data);
      return customFields;
    }
    this.logger.info('没有自定义值');
    return undefined;
  }

  async bitsTemplateParams(configId: number): Promise<BitsTemplateData | undefined> {
    const request = this.createRequest();
    const templateResult = await request.get<BitsResult<BitsTemplateData>>(
      `/workflow/job/template_params?template_config_id=${configId}`,
    );
    return templateResult.data;
  }

  /**
   * 标准化网络请求结果，确保返回的结构符合BitsResult格式
   * @param result 原始网络请求结果
   * @returns 标准化的BitsResult结构，保留原始错误信息
   */
  private normalizeBitsResult<T>(result: any): BitsResult<T> {
    // 如果已经是标准的BitsResult格式且有message字段
    if (result && typeof result.code === 'number' && typeof result.message === 'string') {
      return result;
    }

    // 如果是NetworkX的HTTP错误格式：{code: httpStatus, data: responseBody, logid: ...}
    if (result && typeof result.code === 'number' && result.code !== 200) {
      let message = '';

      // 尝试从data中提取原始错误信息
      if (result.data) {
        if (typeof result.data === 'string') {
          message = result.data;
        } else if (typeof result.data === 'object') {
          // 尝试从常见的错误字段中提取信息
          message = result.data.message || result.data.error || result.data.msg || JSON.stringify(result.data);
        }
      }

      // 如果没有提取到任何错误信息，使用HTTP状态码
      if (!message) {
        message = `HTTP ${result.code}`;
      }

      return {
        code: result.code,
        message,
        data: result.data,
      };
    }

    // 如果是成功的响应，但格式不标准
    return {
      code: result?.code || 200,
      message: result?.message || '',
      data: result?.data || result,
    };
  }

  /**
   * 获取模板参数的Raw版本，直接返回原始网络请求结果
   * @param configId 模板配置ID
   * @returns 标准化的BitsResult结构，包含完整的响应信息
   */
  async bitsTemplateParamsRaw(configId: number): Promise<BitsResult<BitsTemplateData>> {
    const request = this.createRequest();
    const result = await request.get<BitsResult<BitsTemplateData>>(
      `/workflow/job/template_params?template_config_id=${configId}`,
    );

    // 标准化结果：确保返回的结构符合BitsResult格式
    return this.normalizeBitsResult(result);
  }

  async bitsBuild(template: any): Promise<TemplateBuildResult | undefined> {
    const request = this.createRequest();
    template.operator = template.operator || 'PaperAirplane bot';
    const result = await request.post<BitsResult<TemplateBuildResult>>('/workflow/job/trigger_template', template);
    this.logger.info(`bitsBuild:${JSON.stringify(result)}`);
    if (result.code === 200) {
      return result.data;
    }
    return undefined;
  }

  /**
   * 触发构建的Raw版本，直接返回原始网络请求结果
   * @param template 构建模板参数
   * @returns 标准化的BitsResult结构，包含完整的响应信息
   */
  async bitsBuildRaw(template: any): Promise<BitsResult<TemplateBuildResult>> {
    const request = this.createRequest();
    template.operator = template.operator || 'PaperAirplane bot';

    const result = await request.post<BitsResult<TemplateBuildResult>>('/workflow/job/trigger_template', template);
    this.logger.info(`bitsBuildRaw:${JSON.stringify(result)}`);

    // 标准化结果：确保返回的结构符合BitsResult格式
    return this.normalizeBitsResult(result);
  }

  async bitsUpdateResult(jobId: number, jobResult: TemplateBuildExtraInfo): Promise<BitsResult<any>> {
    const request = this.createRequest();
    const result = await request.post<BitsResult<any>>('/workflow/job/update_result', {
      jobId,
      jobResults: jobResult, // 要上报的信息， json object
    });
    this.logger.info(`bitsUpdateResult:${JSON.stringify(result)}`);
    return result;
  }

  async getMrHostInfo(query: { mrId?: number; projectId?: number; iid?: number }): Promise<number | undefined> {
    const params: Record<string, number> = {};
    if (query.mrId) {
      params.mr_id = query.mrId;
    }
    if (query.projectId && query.iid) {
      params.project_id = query.projectId;
      params.iid = query.iid;
    }
    const request = this.createRequest();
    const result = await request.get<BitsResult<{ mr_id: number }>>('/merge_request/host/merge_request/info', params);
    this.logger.info(JSON.stringify(result));
    return result?.data?.mr_id;
  }

  async getMrPipelineList(mrId: string): Promise<MrPipelineInfo[] | undefined> {
    const request = this.createRequest();
    const result = await request.get<BitsResult<MrPipelineInfo[]>>('/merge_request/pipeline/list', {
      mr_id: mrId,
    });
    this.logger.info(JSON.stringify(result));
    return result.data;
  }

  async forceMergeMr(creator: string, project_id: string, mrId: number): Promise<NetworkResult<null>> {
    const request = this.createRequest(creator);
    const result = await request.put<BitsResult<null>>('/merge_request/force_merge', {
      project_id,
      iid: mrId,
    });
    this.logger.info(JSON.stringify(result));
    return result;
  }

  async getMrComponents(mrID: string): Promise<MrComponentsInfo[]> {
    const request = this.createRequest();
    const result = await request.get<BitsResult<MrComponentsInfo[]>>('/merge_request/component/publish/record/list', {
      mr_id: mrID,
    });
    return result.data ?? [];
  }

  async getMrRelationList(mrId: number): Promise<MrRelationInfo[] | undefined> {
    const request = this.createRequest();
    const result = await request.get<BitsResult<MrRelationInfo[]>>('/merge_request/relation/list', {
      mr_id: mrId,
    });
    this.logger.info(`getMrRelationList:${JSON.stringify(result)}`);
    return result.data;
  }

  async getMrReviewersInfo(mrId: number): Promise<MrReviewersInfo[] | undefined> {
    const request = this.createRequest();
    const result = await request.get<BitsResult<MrReviewersInfo[]>>('/merge_request/reviewers/info', {
      mr_id: mrId,
    });
    this.logger.info(`getMrReviewersInfo:${JSON.stringify(result)}`);
    return result.data;
  }

  replaceBaseTemplatePrams(
    template: string,
    branch: string,
    commit: string | undefined,
    gitUrl: string,
    repoId = 0,
  ): string {
    let result: string = template
      .replace('$MAIN_GIT_URL', gitUrl)
      .replace('$MAIN_GIT_BRANCH', branch)
      .replace('$WORKFLOW_REPO_BRANCH', branch);
    if (commit) {
      result = result.replace('$MAIN_GIT_COMMIT', commit).replace('$WORKFLOW_REPO_COMMIT', commit);
    }
    if (repoId !== 0) {
      result = result.replace(`$WORKFLOW_REPO_ID`, repoId.toString());
    }
    return result;
  }

  replaceAutoSyncTemplatePrams(
    template: string,
    branch: string,
    commit: string | undefined,
    gitUrl: string,
    mrId: string,
    mrType: string,
    targetBranch: string,
    currentBranch: string,
  ): string {
    let result: string = template
      .replace('$MAIN_GIT_URL', gitUrl)
      .replace('$WORKFLOW_REPO_URL', gitUrl)
      .replace('$MAIN_GIT_BRANCH', branch)
      .replace('$WORKFLOW_REPO_TARGET_BRANCH', targetBranch)
      .replaceAll('$WORKFLOW_REPO_BRANCH', currentBranch)
      .replace('$CUSTOM_CI_MR_ID', mrId)
      .replace('$CUSTOM_CI_MR_TYPE', mrType);
    if (commit) {
      result = result.replace('$MAIN_GIT_COMMIT', commit).replace('$WORKFLOW_REPO_COMMIT', commit);
    }
    return result;
  }

  async createTimeline(mrId: number, commit: string, level: string, author?: string) {
    const noEmptyAuthor = author || 'lizhengda.da';
    const request = this.createRequest();
    await request.post('/merge_request/timeline', {
      mr_id: mrId,
      operator: noEmptyAuthor,
      data: commit,
      level,
    });
  }

  async getBindMeegoTaskInfo(query: { mr_id?: number; project_id?: number; iid?: number }): Promise<MrMeegoInfo[]> {
    const request = this.createRequest();
    const result = await request.get<BitsResult<MrMeegoInfo[]>>('/feature/mr/record', query);
    if (result.code === 200 && result.data) {
      return result.data.map((value: MrMeegoInfo) =>
        produce(value, draft => {
          if (value.task_type === 'bug' && value.platform === 'meego') {
            draft.task_type = 'issue';
          }
          if (value.task_type === 'issue' && value.platform === 'meego') {
            draft.task_type = 'story';
          }
        }),
      );
    }
    return [];
  }

  async searchComponentInfoByIdWithCache(id: number): Promise<ModelComponentInfo> {
    const dbInfo = await this.componentModelService.findComponent(id);
    if (dbInfo) {
      return dbInfo;
    }
    const searchInfo = await this.searchComponentInfoById(id);
    const componentInfo: ModelComponentInfo = {
      id: searchInfo.id,
      publishType: searchInfo.is_biz_pod === 0 ? PublishType.sem : PublishType.auto,
    };
    await this.componentModelService.saveComponentList(componentInfo);
    componentInfo.rawData = searchInfo;
    return componentInfo;
  }

  async searchComponentInfoById(id: number): Promise<SearchComponent> {
    const key = this.tcc.getBitsKey();
    const network = new NetworkX('http://mobile.bytedance.net', {
      Authorization: `Bearer ${key}`,
      'Content-Type': 'application/json',
    });
    const result = await network.get<SearchComponentResult>('/inner_mpaas/v1/repo/info', { repo_id: id });
    return result.data;
  }

  async getMrReviewerInfo(id: number): Promise<ReviewerInfo[]> {
    const request = this.createRequest();
    const result = await request.get<BitsResult<ReviewerInfo[]>>(`/merge_request/reviewers/info`, {
      mr_id: id,
    });
    return result.code === 200 && result.data ? [...result.data] : [];
  }

  // /openapi/merge_request/reviewers/info
  async getMrInfoAndReviewer(mrIdList: number[]): Promise<MrReviewerInfo[]> {
    const request = this.createRequest();
    const mrReviewerInfo: MrReviewerInfo[] = [];
    for (const id of without(mrIdList, 5020145)) {
      const reviewResult = await request.get<BitsResult<ReviewerInfo[]>>(`/merge_request/reviewers/info?mr_id=${id}`);
      const mrDetail = await this.getMrInfo({
        mrId: id,
        projectId: 40279,
      });
      if (!mrDetail || mrDetail?.type === MrType.package) {
        continue;
      }
      mrReviewerInfo.push({
        mr_id: id,
        reviewInfo: reviewResult.data ? reviewResult.data : [],
        mrInfo: mrDetail,
      });
    }
    return mrReviewerInfo;
  }

  async checkQaReviewOk(mrId: number) {
    const reviewerInfo = await this.getMrReviewerInfo(mrId);
    return !some(reviewerInfo, it => it.review_role === ReviewRole.QA && it.approved !== ApprovedType.approved);
  }

  async getMrIdAndTitle(mrSearchArgs: MrSearchRequest, projectId: number): Promise<MrIdAndTitle[]> {
    const maxCount = mrSearchArgs.keyword ? 100 : 0;
    const mrIdList = await this.searchAllMr(mrSearchArgs, maxCount);
    const mrInfos = await this.fetchAllMrInfo(mrIdList);
    return mrInfos.map(mrInfo => pick(mrInfo, ['id', 'title', 'iid']));
  }

  async getReleaseInfo(
    type: string,
    version: string,
    bits_app_id: number,
    pn = 1,
    rn = 100,
  ): Promise<ReleaseRecords | undefined> {
    const request = this.createRequest();
    const res = await request.get<BitsResult<ReleaseRecords>>(`/v1/release/release_record/list`, {
      bits_app_id,
      type,
      version_name: [version],
      pn,
      rn,
    });
    return res.data;
  }

  async doMrApproveByMrId(token: string, mr_id: number): Promise<string | undefined> {
    this.logger.info(`doMrApproveByMrId:token:${token}`);
    const currentNetwork = new NetworkX('https://bits.bytedance.net/openapi', {
      Authorization: token,
      'Content-Type': 'application/json',
    });
    const result = await currentNetwork.post<BitsResult<string>>(`/merge_request/approve`, {
      mr_id,
    });
    this.logger.info(`doMrApproveByMrId:result:${JSON.stringify(result)}`);
    return result.data;
  }

  async getVeBm(aid: number, version: string): Promise<string[] | undefined> {
    const network = new NetworkX('https://mn7zexav.fn.bytedance.net/api/Oncall/Duty');
    const dutyInfo = await network.post('/getCCCreatorOncallDutyForCustom', {
      product: 'VESDK',
      platform: 'Mobile',
    });
    this.logger.info(`getVeBm result=${JSON.stringify(dutyInfo)}`);
    return dutyInfo?.data?.data?.primary_users ?? [];
  }

  /**
   * 获取Meego平台需求绑定的MR
   * @param meegoId 需求ID
   */
  async getMrInfoFromMeegoFeature(meegoId: string): Promise<BitsResult<MeegoBindMrInfo[]>> {
    const request = this.createRequest();
    return await request.get<BitsResult<MeegoBindMrInfo[]>>('/feature/mr_info', {
      task_id: meegoId,
      task_type: 'issue', // issue可以代表meego平台的story需求，bug指缺陷单
      platform: 'meego',
    });
  }

  async sendPackageToMr(param: SendPackagesRequest) {
    const request = this.createRequest();
    return request.post<BitsResult<any>>('/mr_package/add_package', param);
  }

  async fetchPipeline(pipelineId: string): Promise<PipelineInfo | undefined> {
    const request = this.createRequest();
    const result = await request.get<BitsResult<PipelineInfo>>('/workflow/pipeline/get', { pipelineId });
    return result.code === 200 && result.data ? result.data : undefined;
  }

  async fetchJobInfo(jobId: number): Promise<JobStepInfo | undefined> {
    // /openapi/workflow/job/get
    const request = this.createRequest();
    const result = await request.get<BitsResult<JobStepInfo>>('/workflow/job/get', { jobId });
    this.logger.info(JSON.stringify(result.data));
    return result.code === 200 && result.data ? result.data : undefined;
  }

  async postJobErrorMsg(jobId: number, message: string) {
    const request = this.createRequest();
    const result = await request.post<BitsResult<JobStepInfo>>('/workflow/job/update_msg', { jobId, jobMsg: message });
    this.logger.info(JSON.stringify(result.data));
    return result.code === 200 && result.data ? result.data : undefined;
  }

  // 增加Reviewer
  // 经测试验证不支持merge类型的mr
  async mergeRequestReviewer(reviewerInfo: MergeRequestReviewerRequest): Promise<string> {
    const request = this.createRequest();
    const result = await request.post<BitsResult<string>>('/merge_request/reviewer', reviewerInfo);
    this.logger.info(`bitsUpdateResult:${JSON.stringify(result)}`);
    return result.data ?? 'failed';
  }

  async versionProgressCalendarV2(
    version: string,
    appID: number,
    timestamp: number,
    segmentNameInput = '',
  ): Promise<EventsItem | undefined> {
    const appInfo = await this.businessConfigService.appID2AppInfo(appID);
    const extract_event_item = async (d: DataItem[]): Promise<EventsItem | undefined> => {
      const h = head(d);
      if (version) {
        return h?.name === version ? await this.getOpenapiCalendarEventId(h.event_id) : undefined;
      } else {
        return undefined;
      }
    };
    let result: EventsItem | undefined;
    if (!appInfo) {
      return undefined;
    }
    if (segmentNameInput) {
    } else {
      for (const calendar_segment of appInfo.calendar_segment_list) {
        const dataItem = await this.calendarWorkSpaceEventNext(calendar_segment, timestamp, appInfo.bits_workspace);
        const dataItemResult = await extract_event_item(dataItem);
        if (!result) {
          result = dataItemResult;
        } else if (result?.segments && dataItemResult?.segments) {
          result.segments = result.segments.concat(dataItemResult.segments);
        }
      }
    }
    return result;
  }

  /**
   * 查询对应版本的日历信息
   * @param version
   * @param platform
   * @param timestamp 单位是秒
   * @param segmentNameInput 手动更新的时候才需要传这个参数，否则不需要填
   * @param workspaceID bits空间ID
   */
  async versionProgressCalendar(
    version: string,
    platform: string,
    timestamp: number,
    segmentNameInput = '',
    workspaceID = 30,
  ): Promise<EventsItem | undefined> {
    const commonSegment = workspaceID === 30 ? '封版' : `封版-${platform}`;
    const segmentName =
      segmentNameInput.length > 0
        ? segmentNameInput
        : platform === PlatformType.Android
          ? '国内众测-Android'
          : '国内&海外灰度-iOS';

    const extract_event_item = async (d: DataItem[]): Promise<EventsItem | undefined> => {
      const h = head(d);
      return h?.name === version ? await this.getOpenapiCalendarEventId(h.event_id) : undefined;
    };

    const dateItem = await this.calendarWorkSpaceEventNext(segmentName, timestamp, workspaceID);
    const dataItemResult = await extract_event_item(dateItem);

    const commonDateItem = await this.calendarWorkSpaceEventNext(commonSegment, timestamp, workspaceID);
    const result = await extract_event_item(commonDateItem);

    if (result?.segments && dataItemResult?.segments) {
      result.segments = result.segments.concat(dataItemResult.segments);
    }
    return result;
  }

  async calendarWorkSpaceEventNext(segmentName: string, timestamp: number, workspace = 30): Promise<DataItem[]> {
    const request = this.createRequest();
    const result = await request.get<BitsResult<DataItem[]>>(`/calendar/${workspace}/event/next`, {
      workspace,
      time: timestamp,
      segment_name: segmentName,
    });
    this.logger.info(`calendarEventNext:${JSON.stringify(result)}`);
    return result?.data ?? [];
  }

  async getOpenapiCalendarEventId(id: number): Promise<EventsItem | undefined> {
    const request = this.createRequest();
    const result = await request.get<BitsResult<EventsItem>>(`/calendar/event/${id}`);
    return result?.data;
  }

  /**
   * 获取下载链接
   */
  async getMrPackageUrl(name: string, url: string): Promise<mrPackage> {
    const request = this.createRequest();
    return await request.post<mrPackage>('/mr_package/package_plist', {
      package_url: url,
      package_name: name,
    });
  }

  /**
   * 通过lv版本号获取PC版本号
   * @param version
   */
  async getPCVersion(version: string): Promise<PCInfo | undefined> {
    const networkTask = await new NetworkX('https://robot.cn.goofy.app/');
    const result = await networkTask.get<NetworkResult<PCInfo>>('version_info', {
      lv_version: version,
    });
    if (result.data) {
      return {
        lv_version: result.data.lv_version,
        lvpro_version: result.data.lvpro_version,
        ve_version: result.data.ve_version,
        draft_version: result.data.draft_version,
        cc_version: result.data.cc_version,
        ccpc_version: result.data.ccpc_version,
      } as PCInfo;
    } else {
      return undefined;
    }
  }

  async getVersionGroupName(groupName: string, version: string): Promise<string> {
    const group = await this.getVersionGroup(groupName, version);
    if (group && group.have_group) {
      return group.group_name;
    } else {
      return '';
    }
  }

  /**
   * 获取版本群信息
   */
  async getVersionGroup(groupName: string, version: string): Promise<VersionGroup | undefined> {
    const request = this.createRequest();
    const result = await request.get<BitsResult<VersionGroup>>('/integration/integration_workflow/version_group', {
      group_name: groupName,
      version,
    });
    this.logger.info(JSON.stringify(result));
    if (result.code === 200) {
      if (result.data) {
        return result.data;
      } else {
        return undefined;
      }
    } else {
      this.logger.error(JSON.stringify(result));
      return undefined;
    }
  }

  /**
   * 循环查询当前版本所有的feature mr列表，从而获得所有已上车的mrID列表
   * @param appId
   * @param version
   */
  async getIntegrationMeegoIds(appId: number, version: string): Promise<string[]> {
    const request = {
      app_id: appId,
      version,
      mr_type: MrType.feature,
      mr_state: MrState.merged,
      last_id: 0,
      limit: 50,
    } as IntegrationMrsRequest;
    let mrIds: number[] = [];
    let meegoIds: string[] = [];
    let result;
    do {
      result = await this.getIntegrationMrs(request);
      if (result && result?.mr_lists?.length > 0) {
        for (const mrElement of result.mr_lists) {
          const feature = mrElement.features;
          if (feature) {
            for (const featuresItem of feature) {
              const featureUrl = featuresItem.feature_url;
              const meegoId = featureUrl.split('/')[featureUrl.split('/').length - 1];
              if (!meegoIds.includes(meegoId)) {
                meegoIds = meegoIds.concat(meegoId);
              }
            }
          }
          request.last_id = mrElement.mr_id;
          mrIds = mrIds.concat(mrElement.mr_id);
        }
      } else {
        break;
      }
    } while (result && result.total > mrIds.length);
    return meegoIds;
  }

  /**
   * 循环查询当前版本所有的feature mr列表，从而获得所有已上车的mrID列表
   * @param appId
   * @param version
   * @param targetBranch
   */
  async getIntegrationMrIds(appId: number, version: string, targetBranch?: string): Promise<number[]> {
    const request = {
      app_id: appId,
      version,
      mr_type: MrType.feature,
      mr_state: MrState.merged,
      last_id: 0,
      limit: 50,
      target_branch: targetBranch,
    } as IntegrationMrsRequest;
    let mrIds: number[] = [];
    let meegoIds: string[] = [];
    let result;
    do {
      result = await this.getIntegrationMrs(request);
      if (result && result?.mr_lists?.length > 0) {
        for (const mrElement of result.mr_lists) {
          const feature = mrElement.features;
          if (feature) {
            for (const featuresItem of feature) {
              const featureUrl = featuresItem.feature_url;
              const meegoId = featureUrl.split('/')[featureUrl.split('/').length - 1];
              if (!meegoIds.includes(meegoId)) {
                meegoIds = meegoIds.concat(meegoId);
              }
            }
          }
          request.last_id = mrElement.mr_id;
          mrIds = mrIds.concat(mrElement.mr_id);
        }
      } else {
        break;
      }
    } while (result && result.total > mrIds.length);
    return mrIds;
  }

  /**
   * 获取制定版本的所有MR
   */
  async getIntegrationMrs(mrRequest: IntegrationMrsRequest): Promise<IntegrationMrsResponse | undefined> {
    const request = this.createRequest();
    const result = await request.get<BitsResult<IntegrationMrsResponse>>('/integration/mrs', mrRequest);
    if (result && result.code !== 200) {
      this.logger.error('getIntegrationMrs', `request: ${JSON.stringify(mrRequest)}`, JSON.stringify(result));
    }
    return result.data;
  }

  async getReleaseStage(bits_app_id: number, version: string, app_en_name: string) {
    const request = this.createRequest();
    const result = await request.get<BitsResult<StageInfosItem[]>>('/release/stage', {
      bits_app_id,
      version,
      app_en_name,
    });
    this.logger.info(JSON.stringify(result));
    if (result.code === 200) {
      if (result.data) {
        return result.data;
      } else {
        return [];
      }
    }
    this.logger.error(JSON.stringify(result));
    return [];
  }

  /**
   * 查询灰度过程中合入的mr信息
   * wip的不包括，pipeline=false的不包括
   * @param product
   * @param platform
   * @param version
   * @param versionCode
   * @param lastGrayTime 单位是秒,上一次灰度打包时间
   * @param currentGrayTime 单位是秒，当前这一次灰度打包时间
   */
  async getGrayMrInfo(
    product: ProductType,
    platform: PlatformType,
    version: string,
    versionCode: string,
    lastGrayTime: number,
    currentGrayTime: number,
  ): Promise<MrSnapshot> {
    let targetBranch;
    if (product === LVProductType.cc) {
      // 如果查询CC的要看一下目前有没有拉出来CC分支
      const BranchResult = await this.gitlabService.getBranchInfo(40279, `overseas/release/${version}`);
      if (BranchResult.code === 0) {
        targetBranch = `overseas/release/${version}`;
      } else {
        targetBranch = `release/${versionUtils.cc2lvVersion(version)}`;
      }
    } else {
      targetBranch = `release/${version}`;
    }
    const time = { gte: lastGrayTime, lte: currentGrayTime };
    const mergedMrIdList = await this.searchAllMr({
      group_name: `LV-${platform}`,
      target_branch: targetBranch,
      source: 'all',
      merged_time: `${JSON.stringify(time)}`,
      state: MrState.merged,
      sort: 'mr_merged_time',
    });
    const openMrIdList = await this.searchAllMr({
      group_name: `LV-${platform}`,
      target_branch: targetBranch,
      source: 'all',
      state: MrState.opened,
    });
    this.logger.info(`openMrIdList:${JSON.stringify(openMrIdList)}`);
    const mergedMr: MrInfo[] = [];
    const openMr: MrInfo[] = [];
    for (const mrId of mergedMrIdList) {
      const mrInfo = await this.getMrInfo({ mrId });
      if (mrInfo && mrInfo.state === MrState.merged) {
        mergedMr.push(mrInfo);
      }
    }
    for (const mrId of openMrIdList) {
      const mrInfo = await this.getMrInfo({ mrId });
      if (mrInfo && mrInfo.state === MrState.opened) {
        openMr.push(mrInfo);
      }
    }
    return {
      product, // lv or cc
      productCN: product === LVProductType.lv ? '剪映' : 'CapCut', // 剪映 or CapCut
      platform,
      version,
      versionCode,
      startTime: lastGrayTime,
      endTime: currentGrayTime,
      mrListMerged: mergedMr,
      mrListOpen: openMr,
    };
  }

  /**
   * 通过feature类型的mrId查询Mr绑定的需求单的计划上车版本信息，以及最新封板的版本和日期
   * @param mrId
   */
  async queryMeegoVersionByMrInfo(mrId: number, version: string): Promise<any> {
    const bitsMrInfo = await this.getMrInfo({
      mrId,
    });
    let storyMeegoVersionInfos: StoryMeegoVersionInfo[] = [];
    if (bitsMrInfo?.type === MrType.feature) {
      const meegoResult = await this.getBindMeegoTaskInfo({
        mr_id: mrId,
      });
      for (const meegoResultElem of meegoResult) {
        if (meegoResultElem.platform === 'meego' && meegoResultElem.task_type === 'story') {
          const meegoItemInfo = await this.meegoService.requestTaskInfo('faceu', 'story', {
            work_item_ids: [Number(meegoResultElem.task_id)],
            fields: ['client', 'planning_version'],
          });
          if (!meegoItemInfo.data || !meegoItemInfo.data[0]) {
            continue;
          }
          let curPlatform = '';
          let curPlanVersion: number[] = [];
          for (const field of meegoItemInfo.data[0].fields) {
            if (field.field_key === 'client') {
              const fieldValue = JSON.stringify(field.field_value);
              const value: FieldValue = JSON.parse(fieldValue);
              curPlatform = value.label;
            }
            if (field.field_key === 'planning_version') {
              const fieldValue = JSON.stringify(field.field_value);
              curPlanVersion = JSON.parse(fieldValue);
            }
          }
          const versionResult = await this.meegoService.queryVersionById('faceu', curPlanVersion);
          storyMeegoVersionInfos = storyMeegoVersionInfos.concat({
            meegoId: meegoItemInfo.data[0].id,
            platform: curPlatform,
            planVersion: versionResult,
          } as StoryMeegoVersionInfo);
        }
      }
    }
    return {
      versionInfo: version,
      storyInfo: storyMeegoVersionInfos,
    } as MrStoryVersionInfo;
  }

  async checkMrHasReady(mrId: number, platform: string, appid: number, inDevelopVersions: VersionProcessInfo[]) {
    // FIXME 配置动态化
    const appConfig =
      Number(appid) === 1775
        ? {
            apps: ['剪映', 'CC', '剪映专业版'], // meego版本中的app名
            requireFixRate: 90, // bug解决率
            checkPlaningVersion: true, // 检查计划上车版本
            checkMmrStory: true, // 检查多主仓MR必须绑定移动端需求
            extraMsg: '有疑问Android联系刘云娟,iOS联系刘彤彤,PC联系杨浩雁', // 额外需要提醒的文案
          }
        : {
            apps: ['醒图', 'Hypic'],
            requireFixRate: 85,
            checkPlaningVersion: true,
            checkMmrStory: true,
            checkConfigPublishStatus: true,
            checkAccessMark: true,
            extraMsg: '有疑问联系@叶育杰',
          };
    const meegoResult = await this.getBindMeegoTaskInfo({
      mr_id: mrId,
    });
    const bitsMrInfo = await this.getMrInfo({
      mrId,
    });
    const hostMrId = await this.getMrHostInfo({
      mrId,
    });
    const hostBitsMrInfo = await this.getMrInfo({
      mrId: hostMrId,
    });
    // project_id 40279 https://bits.bytedance.net/code/faceu-android/vega
    // project_id 39995 https://bits.bytedance.net/code/faceu-ios/iMovie
    const relation = await this.getMrRelationList(mrId);
    // 检查是否多仓MR
    let multiTag = false;
    if (relation) {
      for (const mrInfo of relation) {
        if (
          (mrInfo.project_id === 40279 && hostBitsMrInfo?.project_id === 39995) ||
          (mrInfo.project_id === 39995 && hostBitsMrInfo?.project_id === 40279)
        ) {
          multiTag = true;
        }
      }
      const lvProject = relation.find(value => value.project_id === 40279);
      const ccProject = relation.find(value => value.project_id === 39995);
      if (!multiTag && lvProject && ccProject) {
        multiTag = true;
      }
    }
    this.logger.info(
      `getMrRelationList${mrId}: hostBitsMrInfo?.project_id=${hostBitsMrInfo?.project_id} multiTag=${multiTag} 
      ${JSON.stringify(relation)}`,
    );
    this.logger.info(`meego issue = ${JSON.stringify(meegoResult)}`);
    const storyMeegoStatus: StoryMeegoStatus[] = [];
    const storyStatusResult = {
      result: '',
      platform,
      storyMeegoStatus,
      appConfig,
    };

    if (bitsMrInfo && hostBitsMrInfo) {
      let androidStoryID = 0;
      let iOSStoryID = 0;
      // 遍历绑定的需求单列表
      for (const meegoItem of meegoResult) {
        // 确定mr绑定的是meego需求单
        if (
          meegoItem.platform !== 'meego' ||
          meegoItem.task_type !== 'story' ||
          meegoItem.platform_project_name !== 'faceu'
        ) {
          continue;
        }
        // 提取meego信息
        const meegoItemInfo = await this.meegoService.requestTaskInfo(
          meegoItem.platform_project_name,
          meegoItem.task_type,
          {
            work_item_ids: [Number(meegoItem.task_id)],
            fields: ['client', 'field_9a70e6', 'planning_version', 'field_edb46d', 'field_c28db5'], // 醒图需要校验「依赖配置上线情况」和「是否达到封板准入标准」
            expand: {
              need_workflow: true,
            },
          },
        );
        if (!meegoItemInfo.data || !meegoItemInfo.data[0]) {
          continue;
        }
        const curStatus = new StoryMeegoStatus();
        storyMeegoStatus.push(curStatus);
        const curVersions: any[] = [];
        const curPlatforms = new Set<PlatformType>();
        const curApps = new Set<string>();
        for (const field of meegoItemInfo.data[0].fields) {
          if (field.field_key === 'client') {
            // 平台-已废弃
            const fieldValue = JSON.stringify(field.field_value);
            const value: FieldValue = JSON.parse(fieldValue);
            if (value?.label.includes(PlatformType.Android)) {
              curPlatforms.add(PlatformType.Android);
            }
            if (value?.label.includes(PlatformType.iOS)) {
              curPlatforms.add(PlatformType.iOS);
            }
            if (value?.label.includes('Windows&Mac')) {
              curPlatforms.add(PlatformType.PC);
            }
          }
          //
          if (field.field_key === 'field_9a70e6') {
            const fieldValue = JSON.stringify(field.field_value);
            const value: FieldValue = JSON.parse(fieldValue);
            if (value && value.value) {
              // 测试类型需不需要检查DA测试状态 RD保障===c5n_gmxrf
              curStatus.testStrategy = value.value;
            }
          }
          if (field.field_key === 'planning_version') {
            const fieldValue = JSON.stringify(field.field_value);
            const curPlanVersion = JSON.parse(fieldValue);
            if (curPlanVersion && curPlanVersion.length > 0) {
              const meegoVersion = await this.meegoService.queryVersionById('faceu', curPlanVersion);
              if (meegoVersion !== 0) {
                curVersions.push(...meegoVersion);
              }
            }
          }
          if (field.field_key === 'field_edb46d') {
            const list = field.field_value as FieldValueTree[];
            curStatus.configPublishStatus = list?.map(
              v =>
                ({
                  config_name: v.label,
                  publish_status: v.children?.label,
                }) as ConfigPublishStatus,
            );
          }
          if (field.field_key === 'field_c28db5') {
            const field_value = field.field_value as FieldValue;
            curStatus.accessMark = field_value?.label !== '不达标';
          }
        }
        // 准出规则判断
        if (appConfig.checkPlaningVersion && curVersions.length === 0) {
          storyStatusResult.result += `上车版本为空，请检查。`;
        }
        // 记录需求单对应的app&平台
        for (const version of curVersions) {
          if (appConfig.checkPlaningVersion && ['待初估', '非跟版'].some(k => version.versionName.includes(k))) {
            storyStatusResult.result += `需求包含非法的上车版本[${version.versionName}]。`;
            continue;
          }
          // 剪映-Android-11.6.0
          const segs = version.versionName.split('-');
          curApps.add(segs[0]);
          if (['PC', 'Win', 'Mac'].includes(segs[1])) {
            curPlatforms.add(PlatformType.PC);
          } else if (PlatformType.Android === segs[1]) {
            curPlatforms.add(PlatformType.Android);
            androidStoryID = meegoItem.id;
          } else if (PlatformType.iOS === segs[1]) {
            curPlatforms.add(PlatformType.iOS);
            iOSStoryID = meegoItem.id;
          }
        }
        curStatus.versions = Array.from(curVersions);
        curStatus.platforms = Array.from(curPlatforms);
        curStatus.apps = Array.from(curApps);

        curStatus.skip =
          // meego平台必须匹配
          !curStatus.platforms.includes(platform as PlatformType) ||
          // 计划上车版本中需要找到对应app
          !curStatus.apps.some(p => appConfig.apps.includes(p));

        // 校验计划上车版本
        if (
          !curStatus.skip &&
          appConfig.checkPlaningVersion &&
          [PlatformType.Android, PlatformType.iOS].some(p => curStatus.platforms.includes(p) && p === platform)
        ) {
          let hasValidVersion = false;
          for (const version of curVersions) {
            // 剪映-Android-11.6.0
            const segs = version.versionName.split('-');
            if (segs[1] !== platform) {
              continue;
            }
            let appId = 0;
            if (segs[0] === 'CC') {
              appId = platform === PlatformType.Android ? AppSettingId.CC_ANDROID : AppSettingId.CC_IOS;
            } else if (segs[0] === '剪映') {
              appId = platform === PlatformType.Android ? AppSettingId.LV_ANDROID : AppSettingId.LV_IOS;
            } else if (segs[0] === '醒图') {
              appId = platform === PlatformType.Android ? AppSettingId.RETOUCH_ANDROID : AppSettingId.RETOUCH_IOS;
            } else if (segs[0] === 'Hypic') {
              appId = platform === PlatformType.Android ? AppSettingId.HYPIC_ANDROID : AppSettingId.HYPIC_IOS;
            }
            const currentVersion = inDevelopVersions.find(v => v.app_id === appId);
            if (currentVersion) {
              hasValidVersion = true;
              if (currentVersion.version !== segs[2]) {
                storyStatusResult.result += `meego需求单中的计划上车版本号不匹配(当前开发中版本${currentVersion.version},计划上车版本[${version.versionName}])。`;
              }
            }
          }
          if (!hasValidVersion) {
            storyStatusResult.result += `meego单中未找到合法的计划上车版本。`;
          }
        }
        // end
        for (const nodes of meegoItemInfo.data[0].workflow_infos.workflow_nodes) {
          // "status":1 //1:未开始 2:进行中 3:已完成
          // Android集成
          if (nodes.id === 'state_42' || nodes.id === 'state_50' || nodes.id === 'state_48') {
            curStatus.androidStatus = nodes.status;
          }
          // iOS集成
          if (nodes.id === 'state_41' || nodes.id === 'state_49') {
            curStatus.iOSStatus = nodes.status;
          }
          // PC集成
          if (nodes.id === 'ios_completed1' || nodes.id === 'state_94' || nodes.id === 'state_91') {
            curStatus.pcStatus = nodes.status;
          }
        }
        // 查询这个需求单下的解决率
        let queryStoryIssueFixRateResult = new StoryIssueInfo();
        if (!multiTag && curStatus.platforms.length > 1) {
          // 如果不是多端mr，是单端mr,但是story单是双端的需求单，查询解决率的时候要增加平台过滤参数
          if ('platform' in bitsMrInfo) {
            queryStoryIssueFixRateResult = await this.meegoService.queryStoryIssueFixRate(
              meegoItem.task_id,
              bitsMrInfo.platform,
            );
          }
        } else {
          queryStoryIssueFixRateResult = await this.meegoService.queryStoryIssueFixRate(
            meegoItem.task_id,
            platform === 'PC' ? 'Mac & Windows' : platform,
          );
        }
        curStatus.fixRate = queryStoryIssueFixRateResult;
        curStatus.standard =
          curStatus.fixRate.sP0P1Count === 0 &&
          ((Number(curStatus.fixRate.rate) >= appConfig.requireFixRate &&
            curStatus.fixRate.allIssue - curStatus.fixRate.fixIssue < 5) ||
            curStatus.fixRate.allIssue - curStatus.fixRate.fixIssue <= 2);
        this.logger.info(`queryStoryIssueFixRateResult = ${JSON.stringify(queryStoryIssueFixRateResult)}`);
        if (meegoItemInfo.data[0].template_id === 106078) {
          // ug需求
          curStatus.ugCheckResult = await this.checkUgMeegoStatus(mrId.toString());
        }
      }
      this.logger.info(
        `checkFeatureMrReady multiTag = ${JSON.stringify(
          multiTag,
        )} androidStoryID = ${androidStoryID} iOSStoryID = ${iOSStoryID}`,
      );
      if (appConfig.checkMmrStory && multiTag && (androidStoryID === 0 || iOSStoryID === 0) && platform !== 'PC') {
        if (androidStoryID === 0) {
          storyStatusResult.result += `多主仓MR-Android需求单未绑定。`;
        }
        if (iOSStoryID === 0) {
          storyStatusResult.result += `多主仓MR-iOS需求单未绑定。`;
        }
      }
    }

    for (const status of storyMeegoStatus) {
      if (status.skip) {
        continue;
      }
      if (
        !status.standard ||
        (platform === PlatformType.Android && status.androidStatus === 1) ||
        (platform === PlatformType.iOS && status.iOSStatus === 1) ||
        (platform === PlatformType.PC && status.pcStatus === 1) ||
        !status.ugCheckResult.pass
      ) {
        storyStatusResult.result += `${Array.from(status.platforms)}:storyId-${status.fixRate.storyId}`;
        if (platform === PlatformType.Android && status.androidStatus === 1) {
          storyStatusResult.result += `-Android集成节点未开始`;
        }
        if (platform === PlatformType.iOS && status.iOSStatus === 1) {
          storyStatusResult.result += `-iOS集成节点未开始`;
        }
        if (platform === PlatformType.PC && status.pcStatus === 1) {
          storyStatusResult.result += `-PC集成节点未开始`;
        }
        if (!status.standard) {
          if (status.fixRate.sP0P1Count > 0) {
            storyStatusResult.result += `-还剩${status.fixRate.sP0P1Count}个sP0P1未修复`;
          }
          if (Number(status.fixRate.rate) < appConfig.requireFixRate) {
            storyStatusResult.result += `-修复率${status.fixRate.rate}%不达${appConfig.requireFixRate}%`;
          }
          if (status.fixRate.allIssue - status.fixRate.fixIssue >= 5) {
            storyStatusResult.result += `-剩余未修复问题${status.fixRate.allIssue - status.fixRate.fixIssue}个,超过5个`;
          }
        }
        if (!status.ugCheckResult.pass) {
          storyStatusResult.result += `-${status.ugCheckResult.failReason}。`;
        }
      }
      if (appConfig?.checkConfigPublishStatus) {
        status?.configPublishStatus?.forEach(publishStatus => {
          if (publishStatus?.publish_status === PublishStatus.unpublished) {
            storyStatusResult.result += `-需求${publishStatus.config_name}未上线`;
          }
        });
      }
      if (appConfig?.checkAccessMark && status?.accessMark === false) {
        storyStatusResult.result += `-需求未达到准入标准（人工判断）`;
      }
    }
    if (storyStatusResult.result.length > 0) {
      storyStatusResult.result += ` - ${appConfig.extraMsg}`;
    }
    return storyStatusResult;
  }

  async bitsCallBack(job_id: string) {
    const network = new NetworkX('https://bits.bytedance.net/api', {
      'Content-Type': 'application/json',
    });
    const res = await network.post<NetworkResult<any>>('/release-workflow/workflow_job/callback', {
      job_id,
      return_code: '0',
      msg: 'success',
    });
    this.logger.info(`[bits回调响应]: ${JSON.stringify(res)}`);

    return res;
  }

  async getInstallAmount(cloud_app_id: string, platform: string, version: string, is_oversea_data: boolean) {
    const network = new NetworkX('https://bits.bytedance.net/api', {
      'Content-Type': 'application/json',
    });
    const res = await network.get<BitsResult<{ release_num: number; install_num: number }>>(
      '/release-workflow/release_ticket/count',
      {
        cloud_app_id,
        platform,
        version,
        is_oversea_data,
      },
    );

    if (res.code === 200 && res.data?.install_num) {
      return res.data.install_num;
    }

    return -1;
  }

  async getVEBmInfos(): Promise<Duty[]> {
    const network = new NetworkX('https://cloudapi.bytedance.net/faas/services/ttzo4vhvf4wnk5o98m/invoke', {
      'Content-Type': 'application/json',
    });
    const result = await network.get<VEOnCallInfo>(`/ve_oncall_duty`);

    this.logger.info(`[veBmInfo result]: ${JSON.stringify(result)}`);
    try {
      const lv_infos = result.oncall.filter(e => e.business === '剪映');

      if (lv_infos.length > 0) {
        return lv_infos[0].duty;
      }
    } catch (e) {
      this.logger.error(`[veBmInfo err]`, e);
    }
    return [];
  }

  /**
   * 封版日当天创建占位MR
   * @param version
   */
  async raiseCherryPickMR(mrParams: PostOpenapiMergeRequestCherryPickRequest) {
    const request = this.createRequest();
    this.logger.info(`[cherry pick params]: ${JSON.stringify(mrParams)}`);
    const result = await request.post<BitsResult<number>>('/merge_request/cherry_pick', mrParams);
    this.logger.info(`[cherry pick result]: ${JSON.stringify(result)}`);
    return result;
  }
  // 向 bits 查询 MR review 状态
  async queryMergeRequestReviewInfo(
    project_id: string | number,
    iid: string | number,
  ): Promise<MergeRequestReviewInfoResponse> {
    const request = this.createRequest();
    return request.get<MergeRequestReviewInfoResponse>('/merge_request/review/info', {
      project_id,
      iid,
    });
  }

  async mrBindTag(mr_id: string | number, tag_name: string, group_name?: string) {
    const request = this.createRequest();
    const params = { mr_id, tag_name, group_name };
    this.logger.info(`[mrBindTag] params: ${JSON.stringify(params)}`);
    return request.post<BitsResult<string>>('/tags/bind', params);
  }

  async mrUnbindTag(mr_id: string | number, tag_name: string, group_name?: string) {
    const request = this.createRequest();
    const params = { mr_id, tag_name, group_name };
    this.logger.info(`[mrUnbindTag] params: ${JSON.stringify(params)}`);
    return request.delete<BitsResult<string>>('/tags/bind', params);
  }

  async createTag(tag_name: string, color: string, catalog: string, group_project_name: string) {
    const request = this.createRequest();
    const params = { tag_name, color, catalog, group_project_name };
    this.logger.info(`[createTag] params: ${JSON.stringify(params)}`);
    return request.post<BitsResult<string>>('/tags', params);
  }

  /**
   * 检查MR是否包含Android或者iOS主仓
   * @param mrInfo
   */
  async checkMrContainsLvRepo(mrInfo: MrInfo): Promise<boolean> {
    // 剪映空间的project_name和space名是一样的
    return this.checkMrContainsProjects(mrInfo, getBitsSpacesById(AppSettingId.LV_ANDROID));
  }

  async checkMrContainsCCRepo(mrInfo: MrInfo): Promise<boolean> {
    return this.checkMrContainsProjects(mrInfo, ['CapCut 主仓', 'CapCut_iOS']);
  }

  private async checkMrContainsProjects(mrInfo: MrInfo, projects: string[]): Promise<boolean> {
    if (projects.includes(mrInfo.project_name)) {
      return true;
    }
    if (isMultiMr(mrInfo)) {
      const mrRelationList = await this.getMrRelationList(mrInfo.id);
      return some(mrRelationList ?? [], v => projects.includes(v.project_name));
    }
    return false;
  }

  async checkMrContainsPCRepo(mrInfo: MrInfo): Promise<boolean> {
    if (mrInfo.group_name === SpaceType.PC || mrInfo.project_name === 'VideoFusion-win') {
      return true;
    }
    if (isMultiMr(mrInfo)) {
      const mrRelationList = await this.getMrRelationList(mrInfo.id);
      return some(mrRelationList ?? [], v => v.project_name === 'VideoFusion-win');
    }
    return false;
  }

  async checkMrContainsRetouchIOSRepo(mrInfo: MrInfo): Promise<boolean> {
    if (
      mrInfo.group_name === SpaceType.Retouch_iOS ||
      mrInfo.project_name === '醒图' ||
      mrInfo.project_name.includes('RetouchMiddleware')
    ) {
      return true;
    }
    const mrRelationList = await this.getMrRelationList(mrInfo.id);
    return some(mrRelationList ?? [], v => v.project_name === '醒图' || v.project_name.includes('RetouchMiddleware'));
  }

  async checkMrContainsRetouchAdrRepo(mrInfo: MrInfo): Promise<boolean> {
    if (
      mrInfo.group_name === SpaceType.Retouch_Android ||
      mrInfo.project_name.includes('RetouchMiddleware') ||
      mrInfo.project_name === SpaceType.Retouch_Android
    ) {
      return true;
    }
    const mrRelationList = await this.getMrRelationList(mrInfo.id);
    return some(
      mrRelationList ?? [],
      v => v.project_name === SpaceType.Retouch_Android || v.project_name.includes('RetouchMiddleware'),
    );
  }

  async getAppIdsByMrSpace(mrInfo: MrInfo): Promise<AppSettingId[]> {
    const appIds: AppSettingId[] = [];
    if (await this.checkMrContainsLvRepo(mrInfo)) {
      appIds.push(AppSettingId.LV_ANDROID);
    }
    if (await this.checkMrContainsCCRepo(mrInfo)) {
      appIds.push(AppSettingId.CC_ANDROID);
    }
    if (await this.checkMrContainsRetouchAdrRepo(mrInfo)) {
      appIds.push(AppSettingId.RETOUCH_ANDROID);
    }
    if (await this.checkMrContainsRetouchIOSRepo(mrInfo)) {
      appIds.push(AppSettingId.RETOUCH_IOS);
    }
    if (await this.checkMrContainsPCRepo(mrInfo)) {
      appIds.push(AppSettingId.LV_WIN);
    }
    return appIds;
  }

  async getMrRealLvPlatform(mrInfo: MrInfo) {
    const platforms = new Map<PlatformType, MrInfo>();
    if (mrInfo.project_name === SpaceType.Android || mrInfo.group_name === SpaceType.TTP_Android) {
      platforms.set(PlatformType.Android, mrInfo);
    } else if (mrInfo.project_name === SpaceType.iOS || mrInfo.group_name === SpaceType.TTP_iOS) {
      platforms.set(PlatformType.iOS, mrInfo);
    }
    if (isMultiMr(mrInfo)) {
      const mrRelationList = await this.getMrRelationList(mrInfo.id);
      if (mrRelationList) {
        for (const v of mrRelationList) {
          if (v.project_name === SpaceType.Android || v.project_name === 'CapCut 主仓') {
            const subMrInfo = await this.getMrInfo({ mrId: v.id });
            platforms.set(PlatformType.Android, subMrInfo ?? mrInfo);
          } else if (v.project_name === SpaceType.iOS || v.project_name === 'CapCut_iOS') {
            const subMrInfo = await this.getMrInfo({ mrId: v.id });
            platforms.set(PlatformType.iOS, subMrInfo ?? mrInfo);
          }
        }
      }
    }
    return platforms;
  }

  async getAllMrInfos(mrInfo: MrInfo) {
    const mrs = new Map<number, MrInfo>();
    mrs.set(mrInfo.id, mrInfo);
    const mrRelationList = await this.getMrRelationList(mrInfo.id);
    if (mrRelationList) {
      for (const v of mrRelationList) {
        if (!mrs.has(v.id)) {
          const subMrInfo = await this.getMrInfo({ mrId: v.id });
          if (subMrInfo) {
            mrs.set(v.id, subMrInfo);
          }
        }
      }
    }
    return [...mrs.values()];
  }

  async closeMr(mrId: number, groupName: string) {
    const request = this.createRequest();
    const params = { mr_id: mrId, group_name: groupName };
    this.logger.info(`[closeMr] params: ${JSON.stringify(params)}`);
    return request.post<BitsResult<string>>('/merge_request/close', params);
  }

  async checkUgMeegoStatus(mrId: string): Promise<{
    pass: boolean;
    failReason: string;
  }> {
    try {
      const response = await axios({
        method: 'post',
        url: 'https://cloudapi.bytedance.net/faas/services/ttfmuw/invoke/check_meego_status',
        data: {
          mr_id: mrId,
        },
        timeout: 5000,
      });
      return {
        pass: response.data.code === '0',
        failReason: response.data.msg,
      };
    } catch (error) {
      // @ts-ignore
      this.logger.error(`ugcheckMeegoStatus error ${error.message}`);
      return {
        pass: false,
        failReason: `${(error as any).message}`,
      };
    }
  }

  async triggerTemplateJob(body?: Record<string, any>): Promise<number | undefined> {
    const request = this.createRequest();
    const res = await request.post<BitsResult<BitsPipelineTemplateResult>>('/workflow/job/trigger_template', body);
    console.log(`triggerTemplateJob: ${JSON.stringify(res)}`);
    if (res.code === 200) {
      return res.data?.jobId;
    } else {
      throw new Error(res.message);
    }
  }

  async triggerPipeline(templateId: number, appId: number, operator: string, env: Record<string, string>) {
    const request = this.createRequest();
    const res = await request.post<BitsResult<BitsPipelineTemplateResult>>('/pipeline_template/trigger_template', {
      template_id: templateId,
      app_id: appId,
      operator,
      env,
    });
    console.log(`triggerPipeline: ${JSON.stringify(res)}`);
    if (res.code === 200) {
      return res.data?.pipeline_id ? res.data?.pipeline_id : res.data?.jobId;
    } else {
      throw new Error(res.message);
    }
  }

  async getConflictBlameUrl(projectID: number, iid: number) {
    const request = this.createRequest();
    const conflictDetectResult = await request.get<BitsResult<ConflictDetectTaskResult[]>>(
      `/merge_request/v2/machine/task/log`,
      {
        project_id: projectID,
        iid,
        task_name: `tasks.repo.ConflictDetectTask.ConflictDetectTask`,
      },
    );
    if (conflictDetectResult.code === 200) {
      if (conflictDetectResult.data && conflictDetectResult.data.length > 0) {
        const sortedLogs = conflictDetectResult.data.sort((a, b) => b.endTime - a.endTime);
        const latestLog = sortedLogs[0];
        try {
          // 解析 result 字符串为 JSON 对象
          const parsedResult: TaskResult = JSON.parse(latestLog.result);
          if (parsedResult.conflict_files && parsedResult.conflict_files.length > 0) {
            const conflictBlameMap: Record<string, string> = {};

            parsedResult.conflict_files.forEach(file => {
              if (file.conflict_file && file.conflict_blame_file_url) {
                // 将冲突文件名作为 key，责任归属 URL 作为 value 添加到映射中
                conflictBlameMap[file.conflict_file] = file.conflict_blame_file_url;
              }
            });

            return conflictBlameMap;
          } else {
            console.error('No conflict files found');
            return null;
          }
        } catch (error) {
          console.error(`Error parsing result JSON: ${error}`);
          return null;
        }
      }
    } else {
      console.error(`Error: ${conflictDetectResult.message}`);
      return null;
    }
  }

  async getConflictFiles(projectID: number, iid: number) {
    const request = this.createRequest();
    const conflictDetectResult = await request.get<BitsResult<ConflictDetectTaskResult[]>>(
      `/merge_request/v2/machine/task/log`,
      {
        project_id: projectID,
        iid,
        task_name: `tasks.repo.ConflictDetectTask.ConflictDetectTask`,
      },
    );
    const conflictFiles = new Set<string>();
    const conflictFileUrls: any[] = [];
    if (conflictDetectResult.code === 200) {
      if (conflictDetectResult.data && conflictDetectResult.data.length > 0) {
        const sortedLogs = conflictDetectResult.data.sort((a, b) => b.endTime - a.endTime);
        for (const latestLog of sortedLogs) {
          try {
            // 解析 result 字符串为 JSON 对象
            const parsedResult: TaskResult = JSON.parse(latestLog.result);
            if (parsedResult.conflict_files && parsedResult.conflict_files.length > 0) {
              parsedResult.conflict_files.forEach(file => {
                conflictFiles.add(file.conflict_file);
                conflictFileUrls.push({
                  conflict_type: file.conflict_type,
                  conflict_file_mime_type: file.conflict_file_mime_type,
                  start: latestLog.startTime,
                  end: latestLog.endTime,
                  file: file.conflict_file,
                  blameUrl: file.conflict_blame_file_url,
                  url: file.conflict_file_url,
                });
              });
            } else {
              console.error('No conflict files found');
            }
          } catch (error) {
            console.error(`Error parsing result JSON: ${error}`);
          }
        }
        return { conflictFiles, conflictFileUrls };
      }
    } else {
      console.error(`Error: ${conflictDetectResult.message}`);
      return null;
    }
  }

  // 当前bits的这一接口不能正常使用，已提oncall
  async getIfConflicted(mrId: number) {
    const request = this.createRequest();
    const result = await request.get<BitsResult<ConflictDetectTaskResult[]>>(`/code_frozen/can_merge/custom`, {
      mr_id: mrId,
      conditions: ['mr_not_conflicted'],
    });
  }

  // /openapi/integration/info
  async getIntegrationInfo(appId: number, version: string) {
    const request = this.createRequest();
    const result = await request.get<BitsResult<IntegrationInfo>>('/integration/info', {
      id: appId.toString(),
      version,
    });
    this.logger.info(`getIntegrationInfo result: ${JSON.stringify(result)}`);
    return result?.data;
  }

  // 重试workflow
  async rerunWorkflow(mrId: number, groupName: string) {
    const request = this.createRequest();
    const result = await request.post<BitsResult<null>>('/merge_request/workflow/rerun', {
      mr_id: mrId,
      group_name: groupName,
    });
    this.logger.info(`rerun workflow result: ${JSON.stringify(result)}`);
    return result;
  }
  async mergeTarget(projectId: number, iid: number) {
    const request = this.createRequest();
    const result = await request.post<BitsResult<null>>('/merge_request/projects/merge_target', {
      project_id: projectId,
      iid,
    });
    this.logger.info(`merge_target result: ${JSON.stringify(result)}`);
    return result;
  }
  async getTimeline(mrId: number) {
    const request = this.createRequest();
    const result = await request.get<BitsResult<TimelineItem[]>>('/merge_request/timeline/list', {
      mr_id: mrId,
    });
    this.logger.info(`merge_target result: ${JSON.stringify(result)}`);
    return result.data;
  }
  async getMrCommitList(mrId: number, projectId: number, page = 1) {
    const request = this.createRequest();
    const result = await request.get<BitsResult<ChangedCommit>>('/merge_request/changed/commit/list', {
      mr_id: mrId,
      project_id: projectId,
      page,
    });
    this.logger.info(`getMrCommitList result: ${JSON.stringify(result)}`);
    return result.data;
  }
  async getMrByStoryMeegoId(meegoId: number) {
    const request = this.createRequest();
    const result = await request.get('/feature/mr_info', {
      task_id: meegoId,
      task_type: 'issue',
      platform: 'meego',
    });
    if (result.code !== 200) {
      this.logger.error(`getMrByStoryMeegoId ${result}`);
    }
    return result.data ?? [];
  }

  async getPackageGroupsByMr(projectId: number, iid: number) {
    const request = this.createRequest();
    const result = await request.get('/merge_request/packages', {
      project_id: projectId,
      mr_iid: iid,
      limit: 10,
    });
    if (result.code !== 200) {
      this.logger.error(`getPackageGroupsByMr ${result}`);
    }
    return result.data ?? [];
  }

  async listReleaseWorkflowPipelineExec(workflow_exec_id: number, pipeline_task_id: string) {
    const request = this.createPrivateRequest();
    const result = await request.get<BitsResult<WorkflowExecInfo[]>>('/release-workflow/pipeline/exec/list', {
      workflow_exec_id,
      pipeline_task_id,
    });
    this.logger.info(`[Bits Api] /api/release-workflow/pipeline/exec/list ${JSON.stringify(result)}`);
    if (result.code === 200) {
      if (result.data && result.data.length > 0) {
        const jobInput = JSON.parse(result.data[0].input);
        for (const key in jobInput) {
          if (key.includes('artifact_info')) {
            return jobInput[key];
          }
        }
      } else {
        return;
      }
    }
    this.logger.error(JSON.stringify(result));
    return;
  }

  async getTestFlightGroupLink(app_id: number, tf_group_id: string) {
    const request = this.createPrivateRequest();
    const result = await request.post<BitsResult<TFGroupList>>('/release-workflow/atom/exec/testflight/groups', {
      app_id,
      flush_db: true,
    });
    this.logger.info(JSON.stringify(result));
    if (result.code !== 200) {
      this.logger.error(JSON.stringify(result));
      return;
    }
    if (result.data) {
      for (const group of result.data.tf_group_list) {
        if (group.group_id === tf_group_id) {
          return group.public_link;
        }
      }
    }
    return undefined;
  }

  async getReleaseWorkflow(integrationId: number): Promise<any> {
    const request = this.createPrivateRequest();
    const res = await request.get<BitsResult<ReleaseWorkFlowInfo>>('/release-workflow/workflow/exec/main', {
      integration_id: integrationId,
    });
    this.logger.info(`[Bits Api] /api/release-workflow/workflow/exec/main Response: ${JSON.stringify(res.data)}`);
    return res.data;
  }

  async updateReleaseWorkflow(releaseWorkflowId: number, dag: any): Promise<any> {
    const request = this.createPrivateRequest();
    const res = await request.post<BitsResult<ReleaseWorkFlowInfo>>(
      `/release-workflow/workflow/exec/${releaseWorkflowId}`,
      {
        dag,
      },
    );
    this.logger.info(
      `[Bits Api] /api/release-workflow/workflow/exec/${releaseWorkflowId} Response: ${JSON.stringify(res.data)}`,
    );
    return res.data;
  }

  async triggerReleaseWorkflowPipeline(missionId: number): Promise<any> {
    const request = this.createPrivateRequest();
    const res = await request.post<BitsResult<any>>(`/release-workflow/pipeline/exec/${missionId}/trigger`, {});
    this.logger.info(
      `[Bits Api] /api/release-workflow/pipeline/exec/${missionId}/trigger Response: ${JSON.stringify(res.data)}`,
    );
    return res.data;
  }

  async getIntegrationVersionList(groupName: string, platform: PlatformType, page_size = 100, offset = 0) {
    const request = this.createRequest();
    const res = await request.get<BitsResult<WorkFlowIntegrationVersionInfo>>('/integration/versionlist', {
      group_name: groupName,
      page_size,
      offset,
    });
    this.logger.info(`getIntegrationVersionList: ${JSON.stringify(res)}`);
    return res.data;
  }

  async getArtifactToken() {
    const url = 'https://cloud.bytedance.net/auth/api/v1/jwt';
    const iamKey = '25e5a2cfe0c82d1f2d71f16ad41254c0';
    const headers = {
      'Content-Type': 'application/json;charset=utf-8',
      'Content-Encoding': 'gzip',
      Authorization: `Bearer ${iamKey}`,
    };

    try {
      const response = await axios.get(url, { headers });
      const token = response.headers['x-jwt-token'];
      return token;
    } catch (error) {
      console.error('Error fetching the JWT token:', error);
      return null;
    }
  }

  async searchArtifact(appId: number, field: string, versionName: string, type: string[], platform: PlatformType) {
    const request = await this.createArtifactRequest();
    const res = await request.get<BitsResult<any>>('/artifactory/openapi/v1/artifact/field/search', {
      app_id: appId,
      field,
      version_name: versionName,
      type,
      platform,
    });
    this.logger.info(`searchArtifact: ${JSON.stringify(res)}`);
    return res.data;
  }

  async getAndroidArtifact(appId: number, updateVersion: string, releaseEnv: string) {
    const request = await this.createArtifactRequest();
    const res = await request.get<BitsResult<ArtifactListResp>>('/artifactory/openapi/v1/artifact/list', {
      app_id: appId,
      pn: 1,
      rn: 10,
      update_version: updateVersion,
      platform: 'ANDROID',
      release_env: releaseEnv,
    });
    this.logger.info(`getAndroidArtifact: ${JSON.stringify(res)}`);
    return res.data;
  }

  async postMRBasic(mrId: number, customFields: any) {
    const request = this.createRequest();
    const res = await request.post<BitsResult<any>>('/merge_request/basic', {
      mr_id: mrId,
      custom_fields: customFields,
    });
    this.logger.info(`postMRBasic: ${JSON.stringify(res)}`);
    return res.data;
  }
  // postMRBasicWithRemoveSourceBranch 修改 合入后是否删除分支 的接口
  async postMRBasicWithRemoveSourceBranch(mrId: number, remove_source_branch: boolean) {
    const request = new NetworkX('https://bits.bytedance.net/openapi', {
      Authorization: `Bearer d034f996691373b8bf619a7e92207cdc`, // 用 xulu.luke 的 token
    });
    const res = await request.post<BitsResult<any>>('/merge_request/basic', {
      mr_id: mrId,
      remove_source_branch,
    });
    this.logger.info(`postMRBasic: ${JSON.stringify(res)}`);
    return res.data;
  }
}
