import { Component } from '@shared/bits/components';
import { nanoid } from 'nanoid';
import {
  CustomBuildBitsRepoInfo,
  CustomBuildParam,
  CustomBuildRecord,
  CustomBuildRepoComponent,
  CustomBuildResultType,
  CustomBuildType,
  TFVersionCode,
} from '@shared/customBuild';
import { NetworkCode, NetworkResult, PlatformType, User } from '@pa/shared/dist/src/core';
import repos from '@shared/gitlab/repos';
import { TemplatePackageType } from '@shared/bits/templateBuildExtraInfo';
import { DependencyLock } from '@shared/project';
import { UserIdType } from '@pa/shared/dist/src/lark/userInfo';
import { MessageType } from '@pa/shared/dist/src/lark/larkCard';
import { MrInfo } from '@shared/bits/mrInfo';
import { BmInfo, BmType } from '@shared/bits/bmInfo';
import { Inject, Injectable } from '@gulux/gulux';
import BitsService from './third/bits';
import { BytedLogger } from '@gulux/gulux/byted-logger';
import LarkCardService from './larkCard';
import LarkService from '@pa/backend/dist/src/third/lark';
import ComponentModelService from './model/customBuildModel';
import VersionProcessDao from './dao/VersionProcessDao';
import MeegoService from './third/meego';
import UserService from '@pa/backend/dist/src/service/user';
import GitLabService from './third/gitlab';
import commonUtils from '../utils/commonUtils';
import { useInject } from '@edenx/runtime/bff';
import { RedisClient } from '@gulux/gulux/redis';
import { AppSettingId } from '@pa/shared/dist/src/appSettings/appSettings';
import VersionProcessInfoDao from './dao/releasePlatform/VersionProcessInfoDao';
import BusinessConfigService from '@pa/backend/dist/src/service/businessConfig';
import { VersionStageStatus } from '@shared/releasePlatform/versionStage';
import {
  BitsAppIdToAid,
  ReleaseWorkFlowDagChild,
  ReleaseWorkFlowInfo,
  WorkFlowIntegrationVersionInfo,
} from '@shared/bits';
import CustomBuildDao from './dao/releasePlatform/CustomBuildDao';
import { isCriticalBranch } from '@pa/backend/dist/src/utils/branch';
import TFVersionCodeDao from './dao/releasePlatform/TFVersionCodeDao';
import versionUtils from '../utils/versionUtils';

const buildPrefix = 'customBuildInfo';
export const buildCallback = `${commonUtils.serviceUrl}/api/callback/buildResult`;

@Injectable()
export default class CustomBuildService {
  @Inject()
  private bits: BitsService;

  @Inject()
  private logger: BytedLogger;

  @Inject()
  private larkCard: LarkCardService;

  @Inject()
  private lark: LarkService;

  @Inject()
  private gitlab: GitLabService;

  @Inject()
  private componentModel: ComponentModelService;

  @Inject()
  private user: UserService;

  @Inject()
  private versionProcess: VersionProcessDao;

  @Inject()
  private meego: MeegoService;

  @Inject()
  private redis: RedisClient;

  @Inject()
  private versionProcessInfoDao: VersionProcessInfoDao;

  @Inject()
  private businessConfigService: BusinessConfigService;

  @Inject()
  private customBuildDao: CustomBuildDao;

  @Inject()
  private tfVersionCodeDao: TFVersionCodeDao;

  async customBuild(
    param: CustomBuildParam,
    appId?: number,
  ): Promise<NetworkResult<{ jobUrl: string; jobId: number; buildId: string }>> {
    const buildId = await this.recordBuildInfo(param);

    const strTemplate = await this.getBuildTemplate(param);

    // let username: string
    // let userInfo: User | undefined
    const { username, userInfo } = await this.getBuildUser(param);

    const templateParams = await this.replaceTemplatePrams(strTemplate, buildId, username, param);

    const triggerResult = await this.bits.bitsBuild(templateParams);
    let triggerType: CustomBuildResultType,
      customBuildResult: NetworkResult<{ jobUrl: string; jobId: number; buildId: string }>,
      jobId;

    if (triggerResult) {
      const updateResult = await this.bits.bitsUpdateResult(triggerResult.jobId, {
        packageType: TemplatePackageType.CUSTOM_BUILD,
        buildId,
        mrId: param.extra?.mrInfo?.id ?? '',
      });
      jobId = triggerResult.jobId;
      if (updateResult.code !== 200) {
        customBuildResult = {
          code: NetworkCode.Warning,
          message: '更新构建附加信息失败，纸飞机将不会发送构建结果',
          data: {
            jobId: triggerResult.jobId,
            jobUrl: triggerResult.jobURL,
            buildId,
          },
        };
      }
      triggerType = CustomBuildResultType.building;
      customBuildResult = {
        code: NetworkCode.Success,
        message: '触发成功',
        data: {
          jobId: triggerResult.jobId,
          jobUrl: triggerResult.jobURL,
          buildId,
        },
      };
      if (Array.isArray(param.type) ? param.type.includes(CustomBuildType.RD) : param.type === CustomBuildType.RD) {
        const card = this.larkCard.buildSimpleActionCard('构建触发成功', triggerResult.jobURL, '点击跳转构建地址');
        const userId = await this.lark.getUserIdByEmail(`${username}@bytedance.com`);
        if (userId) {
          await this.lark.sendMessage(UserIdType.openId, userId.open_id, JSON.stringify(card), MessageType.interactive);
        }
      }
    } else {
      triggerType = CustomBuildResultType.triggerError;
      if (Array.isArray(param.type) ? param.type.includes(CustomBuildType.RD) : param.type === CustomBuildType.RD) {
        const card = this.larkCard.buildSimpleActionCard('触发构建失败');
        const userId = await this.lark.getUserIdByEmail(`${username}@bytedance.com`);
        if (userId) {
          await this.lark.sendMessage(UserIdType.openId, userId.open_id, JSON.stringify(card), MessageType.interactive);
        }
      }
      customBuildResult = { code: NetworkCode.Error, message: '触发构建失败' };
    }
    let repo = repos.androidMainRepo;
    if (param.isOversea) {
      if (param.arch === PlatformType.iOS) {
        repo = repos.iosTTPMainRepo;
      } else {
        repo = repos.androidTTPMainRepo;
      }
    } else if (param.arch === PlatformType.iOS) {
      repo = repos.iosMainRepo;
    }
    const branchInfo = await this.gitlab.getBranchInfo(repo.projectId, param.lvBranch);

    let extraStr = '{}';
    if (param.extra) {
      extraStr = JSON.stringify(param.extra);
      param.extra = {};
    }
    const buildRecord: CustomBuildRecord = {
      lvBranch: param.lvBranch,
      buildType: param.type,
      buildId,
      jobId,
      userInfo,
      username,
      buildResult: triggerType,
      commit: branchInfo?.data?.commit?.id,
      extra: extraStr ? JSON.parse(extraStr) : undefined,
      buildParams: param,
    };
    this.logger.debug(`${JSON.stringify(buildRecord)}`);
    await this.componentModel.recordCustomBuild(buildRecord);
    return customBuildResult;
  }

  async newCustomBuild(
    param: CustomBuildParam,
    type: CustomBuildType,
    platform: PlatformType,
    appId: number,
    userName: string,
    needSendGroup = false,
    mrLink?: string,
    versionCode?: number,
  ) {
    const needNewBuildType = [
      CustomBuildType.TF_TEST,
      CustomBuildType.GRAY,
      CustomBuildType.FORMAL,
      CustomBuildType.PC_INTEGRATION_TEST,
      CustomBuildType.PC_SYSTEM_TEST,
      CustomBuildType.USER_STORY_SCHEDULE,
    ];
    const userInfo = await this.lark.getUserInfoByEmail(`${userName}@bytedance.com`);
    if (!needNewBuildType.includes(type)) {
      return await this.customBuild(param, appId);
    } else {
      if (platform === PlatformType.Android) {
        const strTemplate = await this.getBuildTemplateWithAppId(param, appId, type);
        const buildId = nanoid(15);
        const templateParams = await this.replaceTemplatePrams(strTemplate, buildId, userName, param, type);

        const triggerResult = await this.bits.bitsBuild(templateParams);
        let triggerType: CustomBuildResultType,
          customBuildResult: NetworkResult<{ jobUrl: string; jobId: number; buildId: string }>,
          jobId;

        if (triggerResult) {
          const updateResult = await this.bits.bitsUpdateResult(triggerResult.jobId, {
            packageType: TemplatePackageType.CUSTOM_BUILD,
            buildId,
            mrId: param.extra?.mrInfo?.id ?? '',
          });
          jobId = triggerResult.jobId;
          if (updateResult.code !== 200) {
            customBuildResult = {
              code: NetworkCode.Warning,
              message: '更新构建附加信息失败，纸飞机将不会发送构建结果',
              data: {
                jobId: triggerResult.jobId,
                jobUrl: triggerResult.jobURL,
                buildId,
              },
            };
          }
          triggerType = CustomBuildResultType.building;
          customBuildResult = {
            code: NetworkCode.Success,
            message: '触发成功',
            data: {
              jobId: triggerResult.jobId,
              jobUrl: triggerResult.jobURL,
              buildId,
            },
          };
        } else {
          triggerType = CustomBuildResultType.triggerError;
          customBuildResult = { code: NetworkCode.Error, message: '触发构建失败' };
        }
        let repo = repos.androidMainRepo;
        if (param.isOversea) {
          if (param.arch === PlatformType.iOS) {
            repo = repos.iosTTPMainRepo;
          } else {
            repo = repos.androidTTPMainRepo;
          }
        } else if (param.arch === PlatformType.iOS) {
          repo = repos.iosMainRepo;
        }
        const branchInfo = await this.gitlab.getBranchInfo(repo.projectId, param.lvBranch);

        let extraStr = '{}';
        if (param.extra) {
          extraStr = JSON.stringify(param.extra);
          param.extra = {};
        }
        const buildRecord: CustomBuildRecord = {
          lvBranch: param.lvBranch,
          buildType: [type],
          buildId,
          jobId,
          userInfo,
          username: userName,
          buildResult: triggerType,
          commit: branchInfo?.data?.commit?.id,
          extra: extraStr ? JSON.parse(extraStr) : undefined,
          buildParams: param,
          needSendGroup,
        };
        this.logger.debug(`${JSON.stringify(buildRecord)}`);
        await this.customBuildDao.create(buildRecord);
        return customBuildResult;
      } else if (platform === PlatformType.iOS) {
        return await this.manualBuildTFPackage(
          userName,
          appId,
          param.lvBranch,
          PlatformType.iOS,
          [type],
          undefined,
          needSendGroup,
          mrLink,
          versionCode,
        );
      }
    }
  }

  async manualBuildTFPackage(
    userName: string,
    appId: AppSettingId,
    branch: string,
    platform: PlatformType,
    buildType: CustomBuildType[],
    version?: string,
    needSendGroup = false,
    mrLink?: string,
    versionCode?: number,
    taskName?: string,
  ) {
    const record: CustomBuildRecord = {
      lvBranch: branch,
      buildType,
      buildId: nanoid(15),
      buildResult: CustomBuildResultType.building,
      username: userName,
      needSendGroup,
      buildParams: {
        arch: platform,
        appId,
        lvBranch: branch,
        type: buildType,
        repos: [],
        extra: {
          mrInfo: {
            id: 0,
            title: '手动触发',
            url: '',
          },
        },
      } as CustomBuildParam,
      mrLink,
      appId,
      tfFiveVersion: versionCode,
      airPlaneTaskName: taskName,
    };
    if (appId === AppSettingId.LV_IOS || appId === AppSettingId.CC_IOS) {
      let repo = repos.iosMainRepo;
      if (appId === AppSettingId.CC_IOS) {
        repo = repos.iosTTPMainRepo;
      }
      const branchInfo = await this.gitlab.getBranchInfo(repo.projectId, branch);
      const latestCommitId = branchInfo?.data?.commit?.id;
      record.commit = latestCommitId;
      record.buildParams.commitId = latestCommitId;
    }
    const result = await this.buildTFPackage(appId, buildType, userName, record, version);
    // 发送构建通知
    if (result?.result === 'ok') {
      const card = this.larkCard.buildSimpleBuildSuccessCard(
        '构建触发成功',
        `版本号：${result.version}，请前往bits平台查看详情`,
        result.bitsUrl,
        '查看详情',
      );
      const userId = await this.lark.getUserIdByEmail(`${userName}@bytedance.com`);
      if (userId) {
        await this.lark.sendCardMessage(UserIdType.userId, userId.user_id, card);
      }
      return {
        code: NetworkCode.Success,
        message: '触发成功，版本号：${result.version}，请留意纸飞机发送的构建卡片',
      };
    }
    return {
      code: NetworkCode.Error,
      message: '触发失败',
    };
  }

  getTFJobNameAndId(buildType: CustomBuildType[], appId: AppSettingId) {
    let jobName = '';
    let jobPipelineId = 0;
    if (appId === AppSettingId.LV_IOS) {
      if (buildType.includes(CustomBuildType.GRAY)) {
        jobName = '【发版平台】打灰度包（不带调试页）';
        jobPipelineId = 7453;
      } else if (buildType.includes(CustomBuildType.TF_TEST)) {
        jobName = '【发版平台】打灰度包（带调试页）';
        jobPipelineId = 6951;
      } else if (buildType.includes(CustomBuildType.FORMAL)) {
        jobName = '【发版平台】打正式包';
        jobPipelineId = 7454;
      }
    } else if (appId === AppSettingId.CC_IOS) {
      if (buildType.includes(CustomBuildType.GRAY)) {
        jobName = '【发版平台】打灰度包（不带调试页）';
        jobPipelineId = 7821;
      } else if (buildType.includes(CustomBuildType.TF_TEST)) {
        jobName = '【发版平台】打灰度包（带调试页）';
        jobPipelineId = 7820;
      } else if (buildType.includes(CustomBuildType.FORMAL)) {
        jobName = '【发版平台】打正式包';
        jobPipelineId = 7822;
      }
    } else if (appId === AppSettingId.RETOUCH_IOS) {
      if (buildType.includes(CustomBuildType.GRAY)) {
        jobName = 'iOS 灰度包构建';
        jobPipelineId = 111551;
      } else if (buildType.includes(CustomBuildType.TF_TEST)) {
        jobName = 'iOS 测试包构建';
        jobPipelineId = 111552;
      } else if (buildType.includes(CustomBuildType.FORMAL)) {
        jobName = 'iOS 正式包构建';
        jobPipelineId = 111553;
      }
    } else if (appId === AppSettingId.HYPIC_IOS) {
      if (buildType.includes(CustomBuildType.GRAY)) {
        jobName = 'iOS 灰度包构建';
        jobPipelineId = 111551;
      } else if (buildType.includes(CustomBuildType.TF_TEST)) {
        jobName = 'iOS 测试包构建';
        jobPipelineId = 111552;
      } else if (buildType.includes(CustomBuildType.FORMAL)) {
        jobName = 'iOS 正式包构建';
        jobPipelineId = 111553;
      }
    }
    return { jobName, jobPipelineId };
  }

  formatVersionCode(versionCode: number): string {
    const versionCodeStr = versionCode.toString();
    const parts = versionCodeStr.match(/(\d{2})(\d{1})(\d{1})(\d{2})/);
    if (parts) {
      return `${parts[1]}.${parts[2]}.${parts[3]}.${parts[4]}`;
    }
    return versionCodeStr;
  }

  async buildTFPackage(
    appId: AppSettingId,
    buildType: CustomBuildType[],
    userName: string,
    record: CustomBuildRecord,
    version?: string,
  ) {
    const currentVersion = await this.versionProcessInfoDao.findOnProgressVersions(appId);
    if (!currentVersion) {
      this.logger.error('当前没有进行中的版本');
      return;
    }
    let buildVersion = version;
    if (!buildVersion) {
      const filterVersion = currentVersion.filter(item => {
        for (const stage of item.version_stages) {
          if (stage.stage_name === 'integration' && stage.status !== VersionStageStatus.NotStart) {
            return true;
          }
        }
        return false;
      });
      if (filterVersion.length === 0) {
        this.logger.error('没有集成版本');
        return;
      }
      const latest = filterVersion.sort((a, b) => b.version.localeCompare(a.version))[0];
      buildVersion = latest.version;
    }
    const appInfo = await this.businessConfigService.appID2AppInfo(appId);
    if (!appInfo) {
      this.logger.error('appId 不存在');
      return;
    }
    const groupName = appId === AppSettingId.LV_IOS ? appInfo.group_name : 'capcut_ios_new';
    const integration = (await this.bits.getIntegrationVersionList(
      groupName,
      PlatformType.iOS,
    )) as WorkFlowIntegrationVersionInfo;
    if (!integration) {
      this.logger.error('integration 不存在');
      return;
    }
    let integrationId;
    for (const item of integration.integrations) {
      if (item.version === buildVersion) {
        integrationId = item.id;
        break;
      }
    }
    if (!integrationId) {
      this.logger.error('integrationId 不存在');
      return;
    }
    const releaseWorkflow = (await this.bits.getReleaseWorkflow(integrationId)) as ReleaseWorkFlowInfo;
    if (!releaseWorkflow) {
      this.logger.error('releaseWorkflow 不存在');
      return;
    }
    const releaseWorkflowId = releaseWorkflow.id;
    const releaseWorkflowDag = releaseWorkflow.dag;
    const { jobName, jobPipelineId } = this.getTFJobNameAndId(buildType, appId);
    // const jobName = '【发版平台】打灰度包（带调试页）';
    // const jobPipelineId = 6951;
    if (!releaseWorkflowDag || releaseWorkflowDag.length === 0) {
      this.logger.error('releaseWorkflowDag 不存在');
      return;
    }
    const index = appId === AppSettingId.LV_IOS ? 0 : 1;
    if (releaseWorkflowDag.length <= index) {
      this.logger.error('releaseWorkflowDag 不存在');
      return;
    }
    const firstChildren = releaseWorkflowDag[index].children;
    const newArray = [
      {
        admin_roles: [],
        id: 0,
        manager_type: 0,
        name: jobName,
        pipeline_id: jobPipelineId,
        trigger_type: 2,
      } as ReleaseWorkFlowDagChild,
    ];
    firstChildren.push(newArray);
    const updateResult = (await this.bits.updateReleaseWorkflow(
      releaseWorkflowId,
      releaseWorkflowDag,
    )) as ReleaseWorkFlowInfo;
    if (!updateResult) {
      this.logger.error('updateResult 不存在');
      return;
    }
    const newDagList = updateResult.dag[index].children;
    if (newDagList.length <= 0) {
      this.logger.error('newDagList 不存在');
      return;
    }
    const newDag = newDagList[newDagList.length - 1][0];
    if (!newDag) {
      this.logger.error('newDag 不存在');
      return;
    }
    record.tfTaskFlowId = newDag.id;
    if (record.tfFiveVersion === undefined) {
      record.tfFiveVersion = await this.getLatestTFCode(appId, buildVersion);
      if (record.tfFiveVersion === 0) {
        this.logger.error(`buildTFPackage: version code greater than 80`);
        return;
      }
    }
    record.version = buildVersion;
    // 157011 转变为 *********
    record.detailVersionCode = this.formatVersionCode(record.tfFiveVersion);
    const createResult = await this.customBuildDao.create(record);
    if (!createResult) {
      this.logger.error('createResult 失败');
      return;
    }
    const triggerResult = await this.bits.triggerReleaseWorkflowPipeline(newDag.id);
    const bitsUrl = `https://bits.bytedance.net/devops/${this.getBitsSpaceReleaseId(appId)}/release/workflow/versionPublish/detail/${integrationId}/workflow/${updateResult.id}/pipeline/${newDag.id}`;
    this.logger.info(`trigger pipeline res: ${JSON.stringify(triggerResult)}`);
    return {
      result: triggerResult,
      version: record.tfFiveVersion,
      bitsUrl,
    };
  }

  private getBitsSpaceReleaseId(appId: number) {
    if (appId === AppSettingId.CC_IOS) {
      return 806247117570;
    }
    return 1486844930;
  }

  async getLatestTFCode(appId: number, version: string) {
    const aid = BitsAppIdToAid(appId);
    const updateVersionList = await this.bits.searchArtifact(
      aid,
      'update_version',
      version,
      ['GRAY', 'OFFICIAL'],
      PlatformType.iOS,
    );
    if (!updateVersionList || updateVersionList.length === 0) {
      return 10001;
    }
    const buildingVersionList = await this.customBuildDao.findBuildTFRecord(appId);
    if (buildingVersionList && buildingVersionList.length > 0) {
      updateVersionList.push(...buildingVersionList.map(v => v.detailVersionCode));
    }
    // 过滤掉最后两位数大于80的版本号
    const filteredUpdateVersionList = updateVersionList.filter((v: string) => {
      const num = parseInt(v.replace(/\./g, ''), 10);
      return num % 100 < 80;
    });
    const sortedUpdateVersionList = filteredUpdateVersionList.sort((a: string, b: string) => {
      const numA = parseInt(a.replace(/\./g, ''), 10);
      const numB = parseInt(b.replace(/\./g, ''), 10);
      return numB - numA;
    });
    let latestUpdateVersion =
      sortedUpdateVersionList && sortedUpdateVersionList.length > 0
        ? parseInt(sortedUpdateVersionList[0].replace(/\./g, ''), 10)
        : parseInt(`${version.replace(/\./g, '')}00`, 10);
    const inDataBaseVersionCode = await this.tfVersionCodeDao.findByCriteria({
      version,
      appId,
      buildResult: CustomBuildResultType.building,
    });
    if (inDataBaseVersionCode && inDataBaseVersionCode.length > 0) {
      const sortedInDataBaseVersionCode = inDataBaseVersionCode.sort((a, b) => b.tfFiveVersion - a.tfFiveVersion);
      latestUpdateVersion =
        sortedInDataBaseVersionCode[0].tfFiveVersion > latestUpdateVersion
          ? sortedInDataBaseVersionCode[0].tfFiveVersion
          : latestUpdateVersion;
    }
    const lastTwoDigits = latestUpdateVersion % 100;

    // 检查最后两位是否大于等于 79
    if (lastTwoDigits >= 79) {
      return 0;
    }
    // 插入数据库
    const tfVersionCode = {
      appId,
      version,
      buildResult: CustomBuildResultType.building,
      tfFiveVersion: latestUpdateVersion + 1,
    } as TFVersionCode;
    await this.tfVersionCodeDao.create(tfVersionCode);
    return latestUpdateVersion + 1;
  }

  async getBuildTemplateWithAppId(param: CustomBuildParam, appId: number, buildType: CustomBuildType) {
    let bitsTemplate;
    let gitUrl = `******************:${repos.androidMainRepo.projectName}.git`;
    switch (appId) {
      case AppSettingId.LV_ANDROID:
        if (buildType === CustomBuildType.GRAY) {
          bitsTemplate = await this.bits.bitsTemplateParams(66251);
        } else if (buildType === CustomBuildType.FORMAL || buildType === CustomBuildType.USER_STORY_SCHEDULE) {
          bitsTemplate = await this.bits.bitsTemplateParams(66256);
        }
        gitUrl = `******************:${repos.androidMainRepo.projectName}.git`;
        break;
      case AppSettingId.LV_IOS:
        bitsTemplate = await this.bits.bitsTemplateParams(29681);
        break;
      case AppSettingId.CC_IOS:
        bitsTemplate = await this.bits.bitsTemplateParams(29681);
        break;
      case AppSettingId.CC_ANDROID:
        if (buildType === CustomBuildType.GRAY) {
          bitsTemplate = await this.bits.bitsTemplateParams(74471);
        } else if (buildType === CustomBuildType.FORMAL || buildType === CustomBuildType.USER_STORY_SCHEDULE) {
          bitsTemplate = await this.bits.bitsTemplateParams(74474);
        }
        gitUrl = `******************:${repos.androidTTPMainRepo.projectName}.git`;
        break;
      case AppSettingId.RETOUCH_ANDROID:
        if (buildType === CustomBuildType.GRAY) {
          bitsTemplate = await this.bits.bitsTemplateParams(27840);
        } else if (buildType === CustomBuildType.FORMAL) {
          bitsTemplate = await this.bits.bitsTemplateParams(1855);
        }
        gitUrl = `******************:faceu-android/retouch.git`;
        break;
      case AppSettingId.RETOUCH_IOS:
        bitsTemplate = await this.bits.bitsTemplateParams(29681);
        break;
      case AppSettingId.HYPIC_IOS:
        bitsTemplate = await this.bits.bitsTemplateParams(29681);
        break;
      case AppSettingId.HYPIC_ANDROID:
        if (buildType === CustomBuildType.GRAY) {
          bitsTemplate = await this.bits.bitsTemplateParams(53997);
        } else if (buildType === CustomBuildType.FORMAL) {
          bitsTemplate = await this.bits.bitsTemplateParams(53997);
        }
        gitUrl = `******************:faceu-android/retouch.git`;
        break;
      case AppSettingId.LV_MAC:
        break;
      case AppSettingId.LV_WIN:
        break;
      case AppSettingId.CC_MAC:
        break;
      case AppSettingId.CC_WIN:
        break;
      case AppSettingId.DREAMINA_ANDROID:
        if (buildType === CustomBuildType.GRAY) {
          bitsTemplate = await this.bits.bitsTemplateParams(57772);
        } else if (buildType === CustomBuildType.FORMAL) {
          bitsTemplate = await this.bits.bitsTemplateParams(57774);
        }
        gitUrl = `******************:${repos.androidMainRepo.projectName}.git`;
        break;
      case AppSettingId.DREAMINA_IOS:
        bitsTemplate = await this.bits.bitsTemplateParams(29681);
        break;
      default:
        bitsTemplate = await this.bits.bitsTemplateParams(64676);
    }
    let strTemplate = JSON.stringify(bitsTemplate);
    strTemplate = this.bits.replaceBaseTemplatePrams(strTemplate, param.lvBranch, undefined, gitUrl);
    return strTemplate;
  }

  async getBuildTemplate(param: CustomBuildParam): Promise<string> {
    let strTemplate;
    if (param.arch === PlatformType.Android) {
      const jobIdDict = {
        domestic: {
          userStory: 29681,
          publishCache: 80763,
          base: 64676,
        },
        overseas: {
          userStory: 74471,
          publishCache: 80763,
          base: 74826,
        },
      };
      let jobId = jobIdDict.domestic.base;
      const region = param.isOversea ? 'overseas' : 'domestic';
      if (param.isUserStory) {
        jobId = jobIdDict[region].userStory;
      } else if (
        (Array.isArray(param.type)
          ? param.type.includes(CustomBuildType.PUBLISH_CACHE)
          : param.type === CustomBuildType.PUBLISH_CACHE) &&
        Math.random() < 0.9
      ) {
        jobId = jobIdDict[region].publishCache;
      } else {
        jobId = jobIdDict[region].base;
      }
      const bitsTemplates = await this.bits.bitsTemplateParams(jobId);
      strTemplate = JSON.stringify(bitsTemplates);
      let repo = repos.androidMainRepo;
      if (param.isOversea) {
        repo = repos.androidTTPMainRepo;
      }
      strTemplate = this.bits.replaceBaseTemplatePrams(
        strTemplate,
        param.lvBranch,
        undefined,
        `******************:${repo.projectName}.git`,
      );
    } else {
      const branchName = param.lvBranch;
      const shouldUseNewJobForTTP = param.isOversea && !(
        branchName === 'overseas/release/14.6.0' ||
        branchName === 'overseas/release/14.7.0' ||
        branchName === 'overseas/release/14.8.0'
      );
      let jobId = 0;
      if (param.isUserStory || param.isTFInUserStory) {
        if (param.isOversea) {
          if (param.isTFInUserStory) {
            jobId = 59247; // 【灰度-带调试页】CapCut-iOS-TF包，外发勿选_xc16_jojo
            if (shouldUseNewJobForTTP) {
              jobId = 81024; // CapCut_iOS_TF包带调试页_v2#81024
            }
          } else {
            jobId = 64085; // 【正式】CapCut-iOS-AppStore包_xc16_jojo
          }
        } else {
          if (param.isTFInUserStory) {
            jobId = 59078; // 【灰度-带调试页】剪映-iOS-TF包，外发勿选_xc16_jojo
          } else {
            jobId = 59080; // 【正式】剪映-iOS-AppStore包_xc16_jojo
          }
        }
      } else if (param.isCoverage) {
        if (param.isOversea) {
          jobId = 74952; // 剪映JoJo 海外覆盖率包_xc16
          if (shouldUseNewJobForTTP) {
            jobId = 80764; // CapCut_iOS_覆盖率包_v2#80764
          }
        } else {
          jobId = 59044; // 剪映JoJo 国内覆盖率包_xc16
        }
      } else {
        if (param.isOversea) {
          jobId = 76646; // CapCut JoJo 企业主干包_xc16（分仓以后使用该 job）
          if (shouldUseNewJobForTTP) {
            jobId = 79327; // CapCut_iOS_企业包_v2#79327
          }
        } else {
          jobId = 69811; // 剪映JoJo国内企业主干包_xc16
        }
      }
      const bitsTemplates = await this.bits.bitsTemplateParams(jobId);
      let repo = repos.androidMainRepo;
      if (param.isOversea) {
        if (param.arch === PlatformType.iOS) {
          repo = repos.iosTTPMainRepo;
        } else {
          repo = repos.androidTTPMainRepo;
        }
      } else if (param.arch === PlatformType.iOS) {
        repo = repos.iosMainRepo;
      }
      strTemplate = JSON.stringify(bitsTemplates);
      strTemplate = this.bits.replaceBaseTemplatePrams(
        strTemplate,
        param.lvBranch,
        param.commitId,
        `******************:${repo.projectName}.git`,
        repo.projectId,
      );
    }
    return strTemplate;
  }

  checkBuildType(type: CustomBuildType | CustomBuildType[], targetType: CustomBuildType[]) {
    if (Array.isArray(type)) {
      for (const customBuildType of targetType) {
        const result = type.includes(customBuildType);
        if (result) {
          return true;
        }
      }
    } else {
      return targetType.includes(type);
    }
    return false;
  }

  async getBuildUser(param: CustomBuildParam): Promise<{ username: string; userInfo: User | undefined }> {
    let username: string, userInfo: User | undefined;
    if (this.checkBuildType(param.type, [CustomBuildType.RD])) {
      const rdEmail = await this.user.queryLoginEmail();
      userInfo = await this.lark.searchUserInfoByEmail(rdEmail);
      username = rdEmail.substring(0, rdEmail.indexOf('@'));
    } else if (this.checkBuildType(param.type, [CustomBuildType.VE])) {
      const veEmail = param.extra?.email ? param.extra?.email : 'PaperAirplane bot by VE';
      userInfo = await this.lark.searchUserInfoByEmail(veEmail);
      username = veEmail.substring(0, veEmail.indexOf('@'));
    } else if (this.checkBuildType(param.type, [CustomBuildType.OUT])) {
      const outEmail = param.extra?.username ? param.extra.username : 'PaperAirplane bot by Out';
      userInfo = await this.lark.searchUserInfoByEmail(outEmail);
      username = outEmail.substring(0, outEmail.indexOf('@'));
    } else if (
      this.checkBuildType(param.type, [
        CustomBuildType.MR_MERGED,
        CustomBuildType.PUBLISH_CACHE,
        CustomBuildType.SMOKE_TEST,
      ]) &&
      param.extra.author
    ) {
      userInfo = await this.lark.searchUserInfoByEmail(`${param.extra.author}@bytedance.com`);
      username = param.extra.author;
    } else if (this.checkBuildType(param.type, [CustomBuildType.QA_INTEGRATION_TESTING])) {
      const qaEmail = param.extra?.bm ? param.extra?.bm : 'PaperAirplane bot by Qa Auto';
      userInfo = await this.lark.searchUserInfoByEmail(qaEmail);
      username = qaEmail.substring(0, qaEmail.indexOf('@'));
    } else if (this.checkBuildType(param.type, [CustomBuildType.E_TEST])) {
      const userEmail = `${param.extra.mrInfo.author}@bytedance.com`;
      userInfo = await this.lark.searchUserInfoByEmail(userEmail);
      username = userEmail.substring(0, userEmail.indexOf('@'));
    } else if (this.checkBuildType(param.type, [CustomBuildType.USER_STORY])) {
      const qaEmail = param.extra?.bm ? param.extra?.bm : 'PaperAirplane bot by Qa Auto';
      userInfo = await this.lark.searchUserInfoByEmail(qaEmail);
      username = qaEmail.substring(0, qaEmail.indexOf('@'));
      // test code
      // const qaEmail = '<EMAIL>';
      // userInfo = await this.lark.searchUserInfoByEmail(qaEmail);
      // username = qaEmail.substring(0, qaEmail.indexOf('@'));
    } else {
      username = 'PaperAirplane bot';
    }
    return { username, userInfo };
  }

  async replaceTemplatePrams(
    strTemplate: string,
    buildId: string,
    username: string,
    param: CustomBuildParam,
    buildType?: CustomBuildType,
  ): Promise<any> {
    let newStrTemplate: string;
    const buildParams: { [key: string]: string } = {};
    if (param.arch === PlatformType.Android) {
      // 基础属性修改
      newStrTemplate = strTemplate.replace('$BUILD_ID', `${buildId}`);
      newStrTemplate = newStrTemplate.replace('$WORKFLOW_REPO_BRANCH', param.lvBranch);
      if (param.isOversea) {
        newStrTemplate = newStrTemplate.replace('$WORKFLOW_REPO_URL', '******************:faceu-android/cc.git');
      } else {
        newStrTemplate = newStrTemplate.replace('$WORKFLOW_REPO_URL', '******************:faceu-android/vega.git');
      }
      newStrTemplate = newStrTemplate.replace('$IS_CUSTOM_BUILD', 'true');
      if (buildType && (buildType === CustomBuildType.FORMAL || buildType === CustomBuildType.GRAY)) {
        newStrTemplate = newStrTemplate.replace('${BUILDPACKAGE_PROJECT_BRANCH}', param.lvBranch);
      }
      buildParams.IS_CUSTOM_BUILD = 'true';
      // 构建选项修改
      if (param.isUserStory) {
        newStrTemplate = newStrTemplate.replace('${BUILDPACKAGE_PROJECT_BRANCH}', param.lvBranch);
      }
      if (param.is32) {
        newStrTemplate = newStrTemplate.replace('$IS_BUILD_64', 'false');
      } else {
        newStrTemplate = newStrTemplate.replace('$IS_BUILD_64', 'true');
      }
      if (param.isDebug) {
        newStrTemplate = newStrTemplate.replace('$IS_DEBUG', 'true');
      } else {
        newStrTemplate = newStrTemplate.replace('$IS_DEBUG', 'false');
      }
      if (param.isOversea) {
        newStrTemplate = newStrTemplate.replace('$IS_OVERSEA', 'true');
      } else {
        newStrTemplate = newStrTemplate.replace('$IS_OVERSEA', 'false');
      }
      if (param.isLynxDebug) {
        newStrTemplate = newStrTemplate.replace('$LYNX_INSPECT', 'true');
        buildParams.LYNX_INSPECT = 'true';
      } else {
        buildParams.LYNX_INSPECT = 'false';
        newStrTemplate = newStrTemplate.replace('$LYNX_INSPECT', 'false');
      }
      if (param.isRhea3) {
        buildParams.IS_BUILD_RHEA_3 = 'true';
        newStrTemplate = newStrTemplate.replace('$IS_BUILD_RHEA_3', 'true');
      }
      if (param.isHyperTest) {
        buildParams.HYPER_BUILD = 'true';
        newStrTemplate = newStrTemplate.replace('$HYPER_BUILD', 'true');
      }
      if (param.isRheaPro) {
        buildParams.IS_BUILD_RHEA_PRO = 'true';
        newStrTemplate = newStrTemplate.replace('$IS_BUILD_RHEA_PRO', 'true');
      }

      if (param.isAnyWhereDorOpen) {
        buildParams.IS_OPEN_ANYWHERE = 'true';
        newStrTemplate = newStrTemplate.replace('$IS_OPEN_ANYWHERE', 'true');
      } else {
        buildParams.IS_OPEN_ANYWHERE = 'false';
        newStrTemplate = newStrTemplate.replace('$IS_OPEN_ANYWHERE', 'false');
      }
      if (param.isDexVmpOpen) {
        buildParams.OPEN_DEX_VMP = 'true';
        newStrTemplate = newStrTemplate.replace('$OPEN_DEX_VMP', 'true');
      }
      if (param.isPublishCache) {
        buildParams.IS_PUSH_CACHE = 'true';
        newStrTemplate = newStrTemplate.replace('$IS_PUSH_CACHE', 'true');
      }

      if (param.isByteInsight) {
        buildParams.IS_BUILD_BYTEINSIGHT = 'true';
        buildParams.CHANNEL = 'auto_test_byteinsight';
        newStrTemplate = newStrTemplate.replace('$IS_BUILD_BYTEINSIGHT', 'true');
        newStrTemplate = newStrTemplate.replace('$CHANNEL', 'auto_test_byteinsight');
      }
      if (param.isOutTest) {
        buildParams.IS_AUTO_TEST = 'true';
        buildParams.CHANNEL = 'mr_auto_test';
        newStrTemplate = newStrTemplate.replace('$IS_AUTO_TEST', 'true');
        newStrTemplate = newStrTemplate.replace('$CHANNEL', 'mr_auto_test');
      }
      if (param.isOutBuild) {
        buildParams.IS_OUT_BUILD = 'true';
        newStrTemplate = newStrTemplate.replace('$IS_OUT_BUILD', 'true');
      }
      if (param.buildTarget) {
        newStrTemplate = newStrTemplate.replace('$BUILD_TARGET', param.buildTarget);
      }
      if (param.isOpenNetWorkTrustUser) {
        buildParams.NETWORK_TRUST_USER = 'true';
        newStrTemplate = newStrTemplate.replace('$NETWORK_TRUST_USER', 'true');
      } else {
        buildParams.NETWORK_TRUST_USER = 'false';
        newStrTemplate = newStrTemplate.replace('$NETWORK_TRUST_USER', 'false');
      }
      if (param.isCoverage) {
        buildParams.IS_COVERAGE = 'true';
        newStrTemplate = newStrTemplate.replace('$IS_COVERAGE', 'true');
      } else {
        buildParams.IS_COVERAGE = 'false';
        newStrTemplate = newStrTemplate.replace('$IS_COVERAGE', 'false');
      }

      if (param.isDebugFix) {
        buildParams.DEBUG_FIX = 'true';
        newStrTemplate = newStrTemplate.replace('$DEBUG_FIX', 'true');
      } else {
        buildParams.DEBUG_FIX = 'false';
        newStrTemplate = newStrTemplate.replace('$DEBUG_FIX', 'false');
      }

      if (param.isCloseLibDevelop) {
        buildParams.IS_BUILD_LIB_DEVELOPE = 'false';
      } else {
        buildParams.IS_BUILD_LIB_DEVELOPE = 'true';
      }

      if (param.isCloseApkOpt) {
        buildParams.IS_APK_SIZE_OP = 'false';
      } else {
        buildParams.IS_APK_SIZE_OP = 'true';
      }

      if (param.isDataAutomation) {
        buildParams.IS_DATA_AUTOMATION = 'true';
        newStrTemplate = newStrTemplate.replace('$IS_DATA_AUTOMATION', 'true');
      }
      // const middleLayer = param.repos.find(value => value.projectId === 134421);

      // if (param.isCheckoutMiddleLayerByVe && !middleLayer) {
      //   this.logger.info('指定根据VE版本选择时候拉取中间层代码参与编译');
      //   newStrTemplate = newStrTemplate.replace(
      //     '$FORCE_BUILD_LV_MIDDLELAYER',
      //     'true',
      //   );
      // }

      // 构建信息填写
      if (param.lvVersionName) {
        newStrTemplate = newStrTemplate.replace('$APP_VERSION_NAME', param.lvVersionName);
      }
      if (param.lvVersionCode) {
        newStrTemplate = newStrTemplate.replace('$APP_VERSION_CODE', param.lvVersionCode);
      }
      if (param.veVersion) {
        buildParams.VE_VERSION = param.veVersion;

        newStrTemplate = newStrTemplate.replace('$VE_VERSION', param.veVersion);
        newStrTemplate = newStrTemplate.replace('$VE_SDK_VERSION_OVERSEA', param.veVersion);
      }
      if (param.effectVersion) {
        buildParams.EFFECT_VERSION = param.effectVersion;
        newStrTemplate = newStrTemplate.replace('$EFFECT_VERSION', param.effectVersion);
      }
      if (param.lynxVersion) {
        buildParams.FORCE_LYNX_DEPEND_VERSION = param.lynxVersion;
        newStrTemplate = newStrTemplate.replace('$FORCE_LYNX_DEPEND_VERSION', param.lynxVersion);
      }
      if (param.cloudVersion) {
        buildParams.FORCE_CLOUD_SDK_VERSION = param.cloudVersion;
        newStrTemplate = newStrTemplate.replace('$FORCE_CLOUD_SDK_VERSION', param.cloudVersion);
      }
      if (param.devVmpVersion) {
        buildParams.DEV_VMP_VERSION = param.devVmpVersion;
      }
      if (param.buildTemplate) {
        newStrTemplate = newStrTemplate.replace('$DEFAULT_TEMPLATE', param.buildTemplate);
      }
    } else {
      newStrTemplate = strTemplate;
      if (param.isUserStory) {
        // newStrTemplate = strTemplate.replace('$BUILD_ID', `${buildId}`);
        newStrTemplate = newStrTemplate.replace('${BUILDPACKAGE_PROJECT_BRANCH}', param.lvBranch);
        // newStrTemplate = newStrTemplate.replace('$IS_CUSTOM_BUILD', 'true');
      }
      if (param.isByteInsight) {
        newStrTemplate = newStrTemplate.replace('$IS_BUILD_BYTEINSIGHT', 'true');
        newStrTemplate = newStrTemplate.replace('$CHANNEL_NAME', 'auto_test_byteinsight');
      }
      if (param.isOutTest) {
        newStrTemplate = newStrTemplate.replace('$IS_AUTO_TEST', 'true');
        newStrTemplate = newStrTemplate.replace('$CHANNEL_NAME', 'mr_auto_test');
      }
    }
    if (param.isPublishGradleCache) {
      newStrTemplate = newStrTemplate.replace('$IS_GRADLE_CACHE_PUBLISH', 'true');
      buildParams.IS_GRADLE_CACHE_PUBLISH = 'true';
    }
    if (param.isForceAllSource) {
      buildParams.IS_FORCE_ALL_SOURCE = 'true';
      newStrTemplate = newStrTemplate.replace('$IS_FORCE_ALL_SOURCE', 'true');
    }
    const temp: {
      id: number;
      build_params: Array<{ id: number; inputs: Array<{ name: string; value: string }> }>;
      after_build_params: Array<{ id: number; inputs: Array<{ name: string; value: string }> }>;
      tags: string;
    } = JSON.parse(newStrTemplate);
    for (const input of temp.build_params[1].inputs) {
      if (input.name === 'BUILD_PARAMS') {
        input.value = JSON.stringify(buildParams);
      }
      if (input.name === 'BUILD_TARGET' && param.buildTarget) {
        input.value = param.buildTarget;
      }
    }
    // for (const inputsKey in temp.build_params[1].inputs) {
    //   if (inputsKey === 'BUILD_PARAMS') {
    //     temp.build_params[1].inputs[inputsKey] = JSON.stringify(buildParams);
    //   }
    // }
    const buildParam = {
      params: temp,
      env: {
        WORKFLOW_APP_ID: '177502',
      },
      sync: true,
      operator: username,
      callbackURLs: [buildCallback],
    };
    this.logger.info(`[replaceTemplatePrams]:${JSON.stringify(buildParam)}`);
    return buildParam;
  }

  async recordBuildInfo(param: CustomBuildParam): Promise<string> {
    const bitsBuildInfo: CustomBuildBitsRepoInfo[] = [];
    for (const customBuildRepo of param.repos) {
      const projectInfo = repos.searchProjectInfo({
        projectId: customBuildRepo.projectId,
      });
      const result = await this.bits.requestProjectRepo(`${customBuildRepo.projectId}`, `40279`);

      if (result) {
        bitsBuildInfo.push({
          projectId: customBuildRepo.projectId,
          repoUrl: `******************:${projectInfo?.projectName}.git`,
          branch: customBuildRepo.branch,
          openComponent: result.repos
            .filter((value: Component) => customBuildRepo.openComponent.includes(value.id))
            .map((value: Component) =>
              (value.repoName.charAt(0).toUpperCase() + value.repoName.slice(1)).split('-').join('_'),
            ),
        });
      }
    }
    this.logger.info(`${JSON.stringify(bitsBuildInfo)}`);
    const buildId = nanoid(15);
    await this.redis.set(`${buildPrefix}-${buildId}`, JSON.stringify(bitsBuildInfo));
    return buildId;
  }

  async requestBuildInfo(buildId: string): Promise<NetworkResult<CustomBuildBitsRepoInfo[]>> {
    const customBitsRepoInfo = await this.redis.get(`${buildPrefix}-${buildId}`);
    if (customBitsRepoInfo) {
      return {
        data: JSON.parse(customBitsRepoInfo),
        code: NetworkCode.Success,
        message: 'success',
      };
    } else {
      return { code: NetworkCode.Error, message: '未找到组件信息' };
    }
  }

  async queryRepoComponent(
    hostProjectId: string,
    projectId: string,
    branch?: string,
  ): Promise<NetworkResult<CustomBuildRepoComponent[]>> {
    const componentInfoList: string[] = [];
    if (branch) {
      const dependencyResult = await this.gitlab.getFileFromRepository(
        `${hostProjectId}`,
        branch,
        'dependency-lock.json',
      );
      const subRepoInfo = repos.searchProjectInfo({
        projectId: parseInt(projectId),
      });
      if (dependencyResult.data) {
        const dependencies = JSON.parse(dependencyResult.data) as DependencyLock;
        const subRepoDependencies = dependencies.dependencies.filter(
          value => value.repo === `******************:${subRepoInfo?.projectName}.git`,
        );
        for (const dependency of subRepoDependencies) {
          if (dependency.flavorType && dependency.targets) {
            for (const target of dependency.targets) {
              componentInfoList.push(`${dependency.artifactId}-${target.name}`);
            }
          } else {
            componentInfoList.push(dependency.artifactId);
          }
        }
      }
    }
    this.logger.info(`主仓中的组件:${JSON.stringify(componentInfoList)}`);
    const result = await this.bits.requestProjectRepo(projectId, hostProjectId);
    if (result) {
      return {
        code: NetworkCode.Success,
        message: 'success',
        data: result.repos
          .filter((value: Component) => componentInfoList.length === 0 || componentInfoList.includes(value.repoName))
          .map((value: Component) => ({
            componentId: value.id,
            repoGroupName: value.repoGroupName,
            componentName: value.repoName,
          })),
      };
    } else {
      return { code: NetworkCode.Error, message: '组件查询失败' };
    }
  }

  async customBuildWithMrMerged(mrInfo: MrInfo) {
    if (
      isCriticalBranch(mrInfo.target_branch) &&
      (mrInfo.platform === PlatformType.iOS || mrInfo.platform === PlatformType.Android)
    ) {
      let iOSchatId: string | undefined;
      const regex = /\/(\d+\.\d+\.\d+)$/;
      let version = mrInfo.target_branch.match(regex)?.[1];
      if (version) {
        const [major, minor, patch] = version.split('.').map(Number);
        const newMajor = mrInfo.target_branch.includes('oversea') ? major + 2 : major;
        const newVersion = `${newMajor}.${minor}.${patch}`;
        version = newVersion;
        const versionMeego = await useInject(VersionProcessDao).findVersionMeego(version);
        if (versionMeego?.iosMeegoId) {
          const addResult = await this.meego.addBotToMeegoChat(versionMeego.iosMeegoId, 'cli_9c8628b7b1f1d102');
          iOSchatId = addResult?.chatId;
        }
      }
      let isOversea = mrInfo.target_branch.includes('oversea');
      // 分仓后，不能仅通过分支来判定 overseas，还需要判断一下 projectId
      if (
        mrInfo.project_id === repos.iosTTPMainRepo.projectId ||
        mrInfo.project_id === repos.androidTTPMainRepo.projectId
      ) {
        isOversea = true;
      }
      if (isOversea) {
        const overseaResult = await this.customBuild({
          lvBranch: mrInfo.target_branch,
          repos: [],
          type:
            mrInfo.platform === PlatformType.iOS && version
              ? [CustomBuildType.MR_MERGED, CustomBuildType.QA_INTEGRATION_TESTING]
              : [CustomBuildType.MR_MERGED],
          arch: mrInfo.platform,
          isOversea: true,
          extra: mrInfo.platform === PlatformType.iOS && version ? { ...mrInfo, chatId: iOSchatId, version } : mrInfo,
        });
      } else {
        const result = await this.customBuild({
          lvBranch: mrInfo.target_branch,
          repos: [],
          arch: mrInfo.platform,
          type:
            mrInfo.platform === PlatformType.iOS && version
              ? [CustomBuildType.MR_MERGED, CustomBuildType.QA_INTEGRATION_TESTING]
              : [CustomBuildType.MR_MERGED],
          isOversea: false,
          extra: mrInfo.platform === PlatformType.iOS && version ? { ...mrInfo, chatId: iOSchatId, version } : mrInfo,
        });
      }
      // return { result, overseaResult };
    }
  }

  /**
   * 协助剪映UG，构建满足调试需求的打包产物
   * 只打Android
   * @param id
   */
  async customBuildWithUgTest(id: number) {
    // const addResult = await this.ctx.service.third.meego.addBotToMeegoChat(
    //   id,
    //   'cli_9c8628b7b1f1d102',
    // );
    // 根据meegoId找到对应版本信息
    const version = await this.versionProcess.findVersionByQAMeegoId(id);
    if (version) {
      if (version.platform === PlatformType.iOS) {
        return;
      }
      const releaseBranch = `release/${version.version}`;
      const branch = await this.gitlab.getBranchInfo(
        version.platform === PlatformType.Android ? repos.androidMainRepo.projectId : repos.iosMainRepo.projectId,
        releaseBranch,
      );
      if (branch.code === 0) {
        // 国内
        await this.customBuild({
          lvBranch: releaseBranch,
          repos: [],
          arch: PlatformType.Android,
          type: [CustomBuildType.UG_TEST],
          isLynxDebug: true,
          isHyperTest: true,
          isOpenNetWorkTrustUser: true,
          extra: {
            version: version.version,
          },
        });
        // 海外
        // await this.customBuild({
        //   lvBranch: releaseBranch,
        //   repos: [],
        //   arch: PlatformType.Android,
        //   type: [CustomBuildType.UG_TEST],
        //   isLynxDebug: true,
        //   isHyperTest: true,
        //   isOpenNetWorkTrustUser: true,
        //   isOversea: true,
        //   extra: {
        //     version: version.version,
        //   },
        // });
      }
    }
  }

  async customBuildWithVersion(id: number, isOversea = false, overseasVersion?: string) {
    const addResult = await this.meego.addBotToMeegoChat(id, 'cli_9c8628b7b1f1d102');
    // 根据meegoId找到对应版本信息
    const version = await this.versionProcess.findVersionByQAMeegoId(id);
    if (version) {
      const releaseBranch = isOversea ? `release/${overseasVersion}` : `release/${version.version}`;
      const bmInfos = await this.bits.requestVersionMaster(
        version.platform === PlatformType.Android ? 'LV-Android' : 'LV-iOS',
        version.version,
      );
      const qaBm = bmInfos.find((value: BmInfo) => value.type === BmType.rd);
      const branch = await this.gitlab.getBranchInfo(
        version.platform === PlatformType.Android ? repos.androidMainRepo.projectId : repos.iosMainRepo.projectId,
        releaseBranch,
      );
      if (branch.code === 0) {
        // 双端国内包
        await this.customBuild({
          lvBranch: releaseBranch,
          repos: [],
          isAnyWhereDorOpen: true,
          arch: version.platform,
          type: [CustomBuildType.QA_INTEGRATION_TESTING],
          isOversea: false,
          isOpenNetWorkTrustUser: true,
          isCoverage: false,
          isDexVmpOpen: true,
          extra: {
            version: version.version,
            chatId: addResult ? addResult.chatId : '',
            bm: qaBm?.email,
          },
        });
      }
      const overseaBranch = commonUtils.getMobileOverseasBranch(releaseBranch);
      const oversea = await this.gitlab.getBranchInfo(
        version.platform === PlatformType.Android ? repos.androidTTPMainRepo.projectId : repos.iosTTPMainRepo.projectId,
        overseaBranch,
      );
      if (oversea.code === 0) {
        // 双端海外包
        await this.customBuild({
          lvBranch: overseaBranch,
          repos: [],
          arch: version.platform,
          isCoverage: false,
          isOpenNetWorkTrustUser: true,
          isAnyWhereDorOpen: true,
          type: [CustomBuildType.QA_INTEGRATION_TESTING],
          isOversea: true,
          devVmpVersion: '1.0.21',
          isDexVmpOpen: true,
          extra: {
            version: version.version,
            chatId: addResult ? addResult.chatId : '',
            bm: qaBm?.email,
          },
        });
        if (version.platform === PlatformType.Android) {
          // android 海外32位包
          await this.customBuild({
            lvBranch: overseaBranch,
            repos: [],
            arch: PlatformType.Android,
            type: [CustomBuildType.QA_INTEGRATION_TESTING],
            isOversea: true,
            isOpenNetWorkTrustUser: true,
            is32: true,
            devVmpVersion: '1.0.21',
            isDexVmpOpen: true,
            isAnyWhereDorOpen: true,
            extra: {
              version: version.version,
              chatId: addResult ? addResult.chatId : '',
              bm: qaBm?.email,
            },
          });
        }
      }
    }
  }

  async customBuildPreciseTestPkg(platform: PlatformType, version: string) {
    // 国内
    let branch = `release/${version}`;
    let branchInfo = await this.gitlab.getBranchInfo(
      platform === PlatformType.Android ? repos.androidMainRepo.projectId : repos.iosMainRepo.projectId,
      branch,
    );
    await this.customBuild({
      lvBranch: branch,
      repos: [],
      isAnyWhereDorOpen: true,
      arch: platform,
      type: [CustomBuildType.PRECISE_TEST],
      isOversea: false,
      isOpenNetWorkTrustUser: true,
      isCoverage: true,
      isDexVmpOpen: true,
      extra: {
        version,
        chatId: 'oc_2410d6b9f22e8126581f1ca67f95025c',
      },
      commitId: branchInfo.data?.commit?.id ?? '',
    });

    // 海外
    branch = `overseas/release/${versionUtils.lv2ccVersion(version)}`;
    branchInfo = await this.gitlab.getBranchInfo(
      platform === PlatformType.Android ? repos.androidTTPMainRepo.projectId : repos.iosTTPMainRepo.projectId,
      branch,
    );
    await this.customBuild({
      lvBranch: branch,
      repos: [],
      arch: platform,
      isCoverage: true,
      isOpenNetWorkTrustUser: true,
      isAnyWhereDorOpen: true,
      type: [CustomBuildType.PRECISE_TEST],
      isOversea: true,
      isDexVmpOpen: true,
      devVmpVersion: '1.0.21',
      extra: {
        version,
        chatId: 'oc_2410d6b9f22e8126581f1ca67f95025c',
      },
      commitId: branchInfo.data?.commit?.id ?? '',
    });
  }
}
