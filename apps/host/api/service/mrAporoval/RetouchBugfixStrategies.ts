import { FieldValue } from '@shared/meego/MeegoCommon';
import { Temporal } from '@js-temporal/polyfill';
import { Inject, Injectable } from '@gulux/gulux';
import { BytedLogger } from '@gulux/gulux/byted-logger';
import { ApprovalResult, BaseStrategy, CheckMrParams } from './baseStrategy';
import commonUtils from '../../utils/commonUtils';
import MeegoService from '../third/meego';
import BitsService from '../third/bits';
import VersionProcessInfoDao from '../dao/releasePlatform/VersionProcessInfoDao';
import { AppSettingId } from '@pa/shared/dist/src/appSettings/appSettings';

// 醒图封包后合码检查
@Injectable()
export default class RetouchBugfixLevelStrategy implements BaseStrategy {
  @Inject()
  private bitsService: BitsService;
  @Inject()
  private meegoService: MeegoService;
  @Inject()
  private logger: BytedLogger;
  @Inject()
  private versionProcessInfoDao: VersionProcessInfoDao;
  rule = '封包后：只能合入 P0 及以上 bugfix MR';

  async checkApproval(params: CheckMrParams): Promise<ApprovalResult> {
    const plainTime = Temporal.Now.zonedDateTimeISO(commonUtils.defaultTimeZone);
    let version = '';
    let isOversea = false;
    let message = '';
    const mrInfo = params.hostMrInfo;
    if (mrInfo?.target_branch.startsWith('release/retouch/')) {
      version = mrInfo.target_branch.replace('release/retouch/', '');
    } else if (mrInfo?.target_branch.startsWith('release/hypic/')) {
      version = mrInfo.target_branch.replace('release/hypic/', '');
      isOversea = true;
    } else if (mrInfo?.target_branch.startsWith('Release/Oversea/')) {
      version = mrInfo.target_branch.replace('Release/Oversea/', '');
      isOversea = true;
    } else if (mrInfo?.target_branch.startsWith('Release/')) {
      version = mrInfo.target_branch.replace('Release/', '');
    } else {
      return {
        rule: this.rule,
        pass: true,
        message,
      };
    }

    // 15.8.x 替换成 15.8.0
    if (version.split('.')[2] !== '0') {
      version = `${version.split('.')[0]}.${version.split('.')[1]}.0`;
    }

    // 醒图iOS用户故事和封包在同一天
    const mrVersion = await this.versionProcessInfoDao.findOneByCriteria({
      app_id: isOversea ? AppSettingId.HYPIC_IOS : AppSettingId.RETOUCH_IOS,
      version,
    });
    const packetReleaseStart = mrVersion?.version_stages?.find(v =>
      ['retouch_ios_user_story'].includes(v.stage_name),
    )?.start_time;

    if (!mrVersion || !packetReleaseStart) {
      // 找不到版本，先不拦这种了
      return {
        rule: this.rule,
        pass: true,
        message,
      };
    }

    this.logger.info(`checkGrayMrTicket 封包开始时间:${new Date(packetReleaseStart * 1000).toLocaleDateString()}`);
    if (plainTime.epochSeconds < packetReleaseStart) {
      // 版本还没封包，不拦截
      return {
        rule: this.rule,
        pass: true,
        message,
      };
    }

    const meegoResult = await this.bitsService.getBindMeegoTaskInfo({
      mr_id: mrInfo.id,
    });
    const docLink = `<a href=\"https://bytedance.larkoffice.com/docx/CWAUdKV2Qo6onExpuNZc6FM2nQH#share-Bj5RdwzA1oWf5sx64BfcJKoPnHf" target="_blank">醒图Hypic | 产研需求全流程Sop-封包后MR准入标准</a>`;

    if ((!meegoResult || meegoResult.length === 0) && plainTime.epochSeconds > packetReleaseStart) {
      // 版本号升级的MR不进行拦截
      if (
        params?.mrInfo?.title?.includes('[BOT]feat: 升级海外 BUILD_CODE') ||
        params?.mrInfo?.title?.includes('[BOT]feat: 升级国内 BUILD_CODE')
      ) {
        return {
          rule: this.rule,
          pass: true,
          message,
        };
      }
      // 版本已经封包，提交的MR未包含bug单
      message += `${isOversea ? 'Hypic' : '醒图'}${version}已封包(${new Date(
        packetReleaseStart * 1000,
      ).toLocaleDateString()}), P0及以上bug修复才能合入，MR未绑定Meego Bug单！详见: ${docLink}`;
      return {
        rule: this.rule,
        pass: message.length === 0,
        message,
      };
    }
    for (const mrMeego of meegoResult) {
      const meego = {
        project: mrMeego.platform_project_name,
        type: mrMeego.task_type,
        id: mrMeego.task_id,
        url: mrMeego.task_url,
      };
      // 修正一下link类型 {\"type\":\"link\",\"content\":\"https://meego.feishu.cn/faceu/issue/detail/4448947550\"}
      if (mrMeego.task_type !== 'issue' && mrMeego.custom_content) {
        const custom_content = JSON.parse(mrMeego.custom_content);
        const r = /.*https:\/\/meego\.feishu\.cn\/(\w+)\/(\w+)\/detail\/(\d+).*/g;
        const m = r.exec(custom_content.content);
        if (m && m.length >= 4) {
          meego.project = m[1];
          meego.type = m[2];
          meego.url = custom_content.content;
          meego.id = m[3];
        }
      }
      if (meego.type !== 'issue') {
        message += `绑定的meego不是bug类型：${JSON.stringify(mrMeego)}`;
      } else {
        try {
          this.logger.info(`checkGrayMrTicket meegoStart:${meego.url}`);
          const meegoInfo = await this.meegoService.requestTaskInfo(meego.project, meego.type, {
            work_item_ids: [parseInt(meego.id, 10)],
            fields: ['priority', 'bug_channel' /** bug发现方式*/, 'issue_stage' /** 发现阶段*/],
          });
          if (meegoInfo.err_code !== 0) {
            message += `绑定meego信息获取失败: ${meego.url} err:${meegoInfo.err_msg}`;
            continue;
          }
          const meegoItem = meegoInfo.data[0];
          const priorityValue = meegoItem.fields.find(v => v.field_key === 'priority');
          const priority = (priorityValue?.field_value as FieldValue).value;
          this.logger.info(`checkGrayMrTicket meego:${meego.url} priority:${priority}`);
          // const docLink = `<a href=\"https://bytedance.larkoffice.com/docx/CWAUdKV2Qo6onExpuNZc6FM2nQH#share-Bj5RdwzA1oWf5sx64BfcJKoPnHf" target="_blank">醒图Hypic | 产研需求全流程Sop-封包后MR准入标准</a>`;
          const levelUpRes = await this.meegoService.checkMeegoLevelUp(meego.id, meegoItem);
          const meegoLink = `<a href=\"${meego.url}" target="_blank">${meegoItem.name}</a>`;
          function checkLevelUp(escapeLevel: string[] = []) {
            // 判断是否升级过
            if (
              levelUpRes.code === 0 &&
              levelUpRes.level_up &&
              (!levelUpRes.last || !escapeLevel.includes(levelUpRes.last))
            ) {
              return `检测到当前MR关联的Bug单(${meegoLink})有过升级记录(上次优先级为${levelUpRes.last}，当前优先级为${levelUpRes.current})，请先确认该优先级调整是否合理，然后走审批跳过`;
            }
            return '';
          }
          if (plainTime.epochSeconds > packetReleaseStart) {
            // 版本已经封包,限制P0及以上
            // p0/s/p00
            if (!['0', '4', 'QXYXWEdPa'].includes(priority)) {
              message += `${isOversea ? 'Hypic' : '醒图'}${version}已封包(${new Date(
                packetReleaseStart * 1000,
              ).toLocaleDateString()}), P0及以上bug修复才能合入，MR绑定的Bug(${meegoLink})不满足要求！详见: ${docLink}`;
            } else {
              message += checkLevelUp();
            }
          }
        } catch (e) {
          message += `绑定meego信息获取失败: ${meego.url} 读取不到优先级 ${e}`;
        }
      }
    }
    return {
      rule: this.rule,
      pass: message.length === 0,
      message,
    };
  }
}
