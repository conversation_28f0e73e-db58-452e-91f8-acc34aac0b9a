export enum CozeWorkflowActionType {
  Unknown = 0,
  VersionStatus = 1, // 查版本状态
  VersionMrStantard = 2, // 查可合入的MR
  VersionReleasePlan = 3, // 查询提审、全量计划
  VersionBuildMaster = 4, // 查询版本BM
  VersionUpdateCode = 5, // 长版本号查询
  UserAction = 6, // 用户行为
  MRProfile = 7,
  IssueAttribution = 8, // 多维度归因
  LibraAttribution = 9, // 实验归因
  ComponentInfo = 10, // 组件查询
}

export function CozeWorkflowActionName(type: CozeWorkflowActionType): string {
  switch (type) {
    case CozeWorkflowActionType.VersionStatus:
      return '查版本状态';
    case CozeWorkflowActionType.VersionMrStantard:
      return '查可合入的MR';
    case CozeWorkflowActionType.VersionReleasePlan:
      return '查询提审、全量计划';
    case CozeWorkflowActionType.VersionBuildMaster:
      return '查询版本BM';
    case CozeWorkflowActionType.UserAction:
      return '用户行为';
    case CozeWorkflowActionType.MRProfile:
      return 'MRProfile';
    case CozeWorkflowActionType.IssueAttribution:
      return '多维度归因';
    case CozeWorkflowActionType.LibraAttribution:
      return '实验归因';
    case CozeWorkflowActionType.ComponentInfo:
      return '组件查询';
    case CozeWorkflowActionType.Unknown:
    default:
      return '未知';
  }
}

export interface CozeWorkflowContent {
  app_name?: string;
  platform?: string;
  version?: string;
  event: CozeWorkflowActionType; // 意图类型
}
