import { queryUserAndSetting, queryVersionListV2, reportTeaEvent, setUserDefaultApp } from '@api/index';

import { configure } from '@edenx/runtime/bff';
import { Provider, useModel } from '@edenx/runtime/model';
import { Outlet, useNavigate, useSearchParams } from '@edenx/runtime/router';
import { LoginUserInfo, MAIN_HOST, NetworkCode, PlatformType } from '@pa/shared/dist/src/core';
import type { MenuProps } from 'antd';
import { Avatar, ConfigProvider, Dropdown, Modal, Skeleton, Space } from 'antd';
import { Icon, Select, Tag, Toast, Tooltip } from '@douyinfe/semi-ui';
import React, { useEffect, useRef, useState } from 'react';
import {
  AndroidFilled,
  AppleFilled,
  BugOutlined,
  InfoCircleOutlined,
  LogoutOutlined,
  PartitionOutlined,
  SettingOutlined,
} from '@ant-design/icons';

import { getPageTitle, PageContainer, ProCard, ProLayout, ProSettings } from '@ant-design/pro-components';
import icon from '@/assets/icon.png';
import {
  AcceptanceIconSvg,
  DevOpsIconSvg,
  EffectivenessStatisticsIconSvg,
  EfficiencyToolIconSvg,
  LibraManagementIconSvg,
  QualityPerformanceIconSvg,
  StoryRevenueIconSvg,
} from '@/icon';
import AppSettingModule from '@/model/appSettingModel';
import { VersionSetting } from '@shared/appSetting';
import UserSettingModule from '@/model/userSettingModel';
import { AuthInfo } from '@shared/kani/type';
import { get_apply_url } from '@shared/kani/util';
import resourceNameMap from '@shared/kani/resourceNameMap';
import VersionSettingModel from '@/model/versionSettingModel';
import './style.css';
import Title from 'antd/es/typography/Title';
import { LVProductType, ProductType } from '@shared/process/versionProcess';
import { add_prefix_ne, trim_prefix, trim_suffix } from '@pa/shared/dist/src/utils/tools';
import {
  IconArchive,
  IconArrowUp,
  IconBeaker,
  IconBell,
  IconBranch,
  IconBriefcase,
  IconBulb,
  IconCalendar,
  IconClock,
  IconDuration,
  IconEdit,
  IconEyeOpened,
  IconFilter,
  IconHistogram,
  IconJianying,
  IconKanban,
  IconLayers,
  IconLikeHeart,
  IconListView,
  IconOrderedList,
  IconPlusCircle,
  IconScissors,
  IconSearch,
  IconSetting,
  IconShield,
  IconTerminal,
  IconWrench,
} from '@douyinfe/semi-icons';
import { HideParent } from '@/component/HideParent';
import HelpMenu from '@/routes/helpMenu';
import { AppSetting, Route } from '@pa/shared/dist/src/appSettings/appSettings';

function checkPathSupport(routesPattern: string[], path: string): boolean {
  if (['/tools/oncallInvite', '/tools/veoncallInvite', '/releaseProcess/overview'].includes(path)) {
    return true;
  }
  for (const config of routesPattern) {
    const pattern = new RegExp(config.replace(/\*(?!\.)/g, '.*'));
    if (pattern.test(path)) {
      return true;
    }
  }
  return false;
}

function filterRoutes(routesPattern: string[] | null, baseRoutes: Route[], basePath = ''): Route[] {
  if (!routesPattern) {
    return baseRoutes;
  }
  const filteredRoutes: Route[] = [];
  for (const route of baseRoutes) {
    if (!route.path) {
      continue;
    }
    const actualPath = trim_prefix('/')(trim_suffix('/', basePath) + add_prefix_ne('/')(route.path));
    if (checkPathSupport(routesPattern, actualPath)) {
      filteredRoutes.push(route);
      continue;
    }
    if (route.routes && route.routes.length > 0) {
      const children = filterRoutes(routesPattern, route.routes, actualPath);
      if (children.length > 0) {
        filteredRoutes.push({
          ...route,
          routes: children,
        });
      }
    }
  }
  return filteredRoutes;
}

// getDefaultRoutes 返回包含主页面的默认路由数组
const getDefaultRoutes = (
  platform: PlatformType,
  showConfigCheckList: boolean,
  showPackageManage: boolean,
): Route[] => [
  {
    path: '/homepage',
    name: '首页',
    rootPath: '/homepage',
    versionFilter: false,
  },
  {
    path: '/release',
    name: '发版平台',
    icon: <Icon svg={<DevOpsIconSvg />} />,
    defaultPath: 'list',
    rootPath: '/release',
    routes: [
      {
        path: 'overview',
        name: '版本总览',
        icon: <IconLayers />,
        versionFilter: false,
      },
      {
        path: 'list',
        name: '版本列表',
        icon: <IconListView />,
        versionFilter: false,
      },
      {
        path: 'calendar',
        name: '版本日历',
        icon: <IconCalendar />,
        versionFilter: false,
      },
      {
        path: 'capacity-kanban',
        name: '定容看板',
        icon: <IconKanban />,
        versionFilter: false,
      },
      {
        path: 'independent-grey',
        name: '独立灰度',
        icon: <IconBulb />,
        versionFilter: false,
      },
      {
        path: 'product-manage',
        name: '产物管理',
        icon: <IconArchive />,
        versionFilter: false,
      },
      {
        path: 'approval-order',
        name: '审批工单',
        icon: <IconOrderedList />,
        versionFilter: false,
      },
    ],
  },
  {
    path: '/releaseProcess',
    name: '版本流程',
    defaultPath: 'overview',
    rootPath: '/releaseProcess',
    icon: <PartitionOutlined />,
    routes: [
      {
        path: 'overview',
        name: '版本总览',
        versionFilter: true,
      },
      {
        path: 'demandInformation',
        name: '需求列表',
        versionFilter: true,
      },
      {
        path: 'otherSpaceMR',
        name: '封版异常MR管理',
        versionFilter: true,
      },
      {
        path: 'gray',
        name: '灰度概况',
        versionFilter: true,
      },
      {
        path: 'issue',
        name: '灰度缺陷',
        versionFilter: true,
      },
      {
        path: 'slardar',
        name: '灰度 Slardar',
        versionFilter: true,
      },
      {
        path: 'tea',
        name: '灰度 TEA',
      },
      {
        path: 'submit',
        name: '提审',
        versionFilter: true,
      },
      {
        path: 'techGrayRegister',
        name: '独立灰度登记',
        hideInMenu: true,
        versionFilter: true,
      },
    ],
  },
  {
    path: '/acceptance',
    name: '验收平台',
    breadcrumbClickable: true,
    defaultPath: 'list',
    rootPath: '/acceptance',
    icon: <Icon svg={<AcceptanceIconSvg />} />,
    routes: [
      {
        path: 'overview',
        name: '验收总览',
        icon: <IconLayers />,
        appFilter: false,
      },
      {
        path: 'list',
        name: '验收列表',
        icon: <IconListView />,
        appFilter: false,
      },
      {
        path: 'plan',
        name: '验收计划',
        icon: <IconClock />,
        appFilter: false,
      },
    ],
  } as Route,
  {
    path: '/libra',
    name: '实验管理',
    icon: <Icon svg={<LibraManagementIconSvg />} />,
    defaultPath: 'list',
    rootPath: '/libra',
    routes: [
      {
        path: 'list',
        name: '实验列表',
        icon: <IconListView />,
        appFilter: false,
        versionFilter: false,
      },
      {
        path: 'statistics',
        name: '实验统计',
        icon: <IconHistogram />,
        appFilter: false,
        versionFilter: false,
        routes: [
          {
            path: 'dashboard_overview',
            name: '大盘总览',
            appFilter: false,
            versionFilter: false,
          },
          {
            path: 'reopen',
            name: '实验重开',
            appFilter: false,
            versionFilter: false,
            routes: [
              {
                path: 'lv_mobile',
                name: '剪映-移动端(整体)',
                appFilter: false,
                versionFilter: false,
              },
              {
                path: 'lv_mobile_recent',
                name: '剪映-移动端(近双周)',
                appFilter: false,
                versionFilter: false,
              },
              {
                path: 'lv_pc_recent',
                name: '剪映-PC(近双周)',
                appFilter: false,
                versionFilter: false,
              },
              {
                path: 'retouch_hypic_recent',
                name: '醒图Hypic(近双周)',
                appFilter: false,
                versionFilter: false,
              },
            ],
          },
          {
            path: 'dashboard_by_flight',
            name: '收益详情(实验维度)',
            appFilter: false,
            versionFilter: false,
          },
          {
            path: 'dashboard_by_metric',
            name: '收益详情(指标维度)',
            appFilter: false,
            versionFilter: false,
          },
          {
            path: 'significance_statistics',
            name: '核心指标显著性',
            appFilter: false,
            versionFilter: false,
          },
          {
            path: 'weekly_reports',
            name: '实验周报',
            hideInMenu: true, // 用风神推送报表，暂时屏蔽入口
            appFilter: false,
            versionFilter: false,
          },
        ],
      },
      {
        path: 'design',
        name: '实验设计',
        icon: <IconEdit />,
        appFilter: false,
        versionFilter: false,
      },
      {
        path: 'create',
        name: '实验创建',
        icon: <IconPlusCircle />,
        appFilter: false,
        versionFilter: false,
      },
      {
        path: 'review',
        name: '实验 Review',
        icon: <IconEyeOpened />,
        appFilter: false,
        versionFilter: false,
      },
      {
        path: 'control',
        name: '实验管控',
        icon: <IconBeaker />,
        routes: [
          {
            path: 'record',
            name: 'Libra 管控记录',
          },
          {
            path: 'event',
            name: 'Libra 变更记录',
            appFilter: false,
          },
          {
            path: 'exempt',
            name: '豁免记录',
            appFilter: false,
          },
        ],
      },
      {
        path: 'patrol',
        name: '实验巡检',
        icon: <IconSearch />,
        appFilter: false,
        versionFilter: false,
        routes: [
          {
            path: 'business',
            name: '业务指标巡检',
          },
          {
            path: 'stability',
            name: '稳定性指标巡检',
          },
          {
            path: 'patrol-sidebar-status',
            name: '实验巡检归因填写',
            icon: <IconEdit />,
            appFilter: false,
            versionFilter: false,
            hideInMenu: true,
          },
        ],
      },
      {
        path: 'governance',
        name: '实验治理',
        icon: <IconScissors />,
        appFilter: false,
        versionFilter: false,
      },
      {
        path: 'notification',
        name: '实验提醒与通知',
        icon: <IconBell />,
        appFilter: false,
        versionFilter: false,
      },
      {
        path: 'tools',
        name: '工具箱',
        icon: <IconBriefcase />,
        routes: [
          {
            path: 'version-code',
            name: '小版本过滤条件计算',
            appFilter: false,
            versionFilter: false,
          },
        ],
      },
      {
        path: 'lark-sidebar-submit',
        name: '实验关闭归因填写',
        icon: <IconEdit />,
        appFilter: false,
        versionFilter: false,
        hideInMenu: true,
      },
      {
        path: 'debug',
        name: '调试页',
        icon: <IconTerminal />,
        appFilter: false,
        versionFilter: false,
        hideInMenu: process.env.NODE_ENV !== 'development',
      },
    ],
  },
  {
    path: '/benefits',
    name: '需求收益',
    icon: <Icon svg={<StoryRevenueIconSvg />} />,
    rootPath: '/benefits',
    defaultPath: 'list',
    routes: [
      {
        path: 'list',
        name: '收益列表',
        icon: <IconListView />,
        versionFilter: false,
        appFilter: false,
      },
      {
        path: 'dashboard',
        name: '收益看板',
        icon: <IconHistogram />,
        versionFilter: false,
        appFilter: false,
      },
    ],
  },
  {
    path: '/quality',
    name: '质量平台',
    icon: <Icon svg={<QualityPerformanceIconSvg />} />,
    defaultPath: 'statistics/performance/dashboard',
    rootPath: '/quality',
    routes: [
      {
        path: 'quick-access',
        name: '常用功能',
        icon: <IconLikeHeart />,
        routes: [
          {
            path: 'compare',
            name: '版本质量对比',
          },
          {
            path: 'offline-stability-metric',
            name: '线下问题看板',
            versionFilter: true,
          },
          {
            path: 'gray-stability-metric',
            name: '灰度稳定性指标',
            versionFilter: true,
          },
          {
            path: 'create-bug',
            name: 'Issue 提单',
          },
        ],
      },
      {
        path: 'statistics',
        name: '质量大盘',
        icon: <IconHistogram />,
        routes: [
          {
            path: 'performance',
            name: '性能体验',
            routes: [
              {
                path: 'dashboard',
                name: '品质大盘',
              },
              {
                path: 'table',
                name: '品质大盘-表格',
              },
              {
                path: 'version',
                name: '版本指标准出',
              },
              {
                path: 'tea',
                name: '版本灰度性能指标',
              },
            ],
          },
          {
            path: 'stability',
            name: '稳定性',
            routes: [
              {
                path: 'circuit-breaker',
                name: '灰度熔断告警记录',
              },
            ],
          },
          {
            path: 'anti-degradation',
            name: '防劣化',
            routes: [
              {
                path: 'dashboard',
                name: '稳定性大盘',
              },
            ],
          },
          {
            path: 'feedback',
            name: '用户反馈',
            routes: [
              {
                path: 'circuit-breaker',
                name: '灰度熔断告警记录',
              },
            ],
          },
        ],
      },
      {
        path: 'diagnosis',
        name: '问题诊断',
        icon: <IconSearch />,
        routes: [
          {
            path: 'code-quality',
            name: '代码质量',
          },
          {
            path: 'auto-test',
            name: '自动化测试',
          },
          {
            path: 'issue-attribution',
            name: '归因工具',
            routes: [
              {
                path: 'compare',
                name: '版本质量对比',
              },
              {
                path: 'escape',
                name: '版本逃逸',
              },
              {
                path: 'statistics/',
                name: '指标统计',
              },
              {
                path: 'statistics/new',
                name: '版本新增',
              },
              {
                path: 'multi-attribution',
                name: '多维度归因',
              },
              {
                path: 'offline-stability-metric',
                name: '线下问题看板',
                versionFilter: true,
              },
              {
                path: 'gray-stability-metric',
                name: '灰度稳定性指标',
                versionFilter: true,
              },
              {
                path: 'mr-profile',
                name: 'MRProfile',
                routes: [
                  {
                    path: 'task-list',
                    name: '任务列表',
                  },
                  {
                    path: 'code-change',
                    name: '代码变更',
                  },
                ],
              },
              {
                path: 'alog/reader',
                name: '行为日志归因',
                // routes: [
                //   {
                //     path: 'reader',
                //     name: '阅读器',
                //   },
                // {
                //   path: 'settings',
                //   name: '规则配置',
                // },
                // ],
              },
              {
                path: 'memory-graph',
                name: 'MemoryGraph',
                routes: [
                  {
                    path: 'detail',
                    name: 'Issue 详情',
                  },
                  // {
                  //   path: 'settings',
                  //   name: '规则配置',
                  // },
                ],
              },
              {
                path: 'libra-attribution',
                name: '实验归因',
              },
            ],
          },
          {
            path: 'tools',
            name: '质量小工具',
            routes: [
              {
                path: 'slardar',
                name: 'Slardar 工具',
                icon: <BugOutlined />,
                routes: [
                  {
                    path: 'create-bug',
                    name: 'Issue 提单',
                  },
                  {
                    path: 'auto-assign',
                    name: 'Issue 自动分配',
                  },
                  {
                    path: 'troubleshooting',
                    name: 'Issue 定位排查',
                  },
                  {
                    path: 'version-update',
                    name: '版本信息修改',
                  },
                  {
                    path: 'quota-dashboard',
                    name: 'Quota 使用情况',
                  },
                  {
                    path: 'data-update',
                    name: '版本数据更新',
                  },
                ],
              },
            ],
          },
          {
            path: 'solutions',
            name: '解决方案',
          },
        ],
      },
      {
        path: 'disaster-tolerance',
        name: '质量容灾',
        icon: <IconShield />,
        routes: [
          {
            path: 'circuit-breaker',
            name: '精准熔断',
          },
          {
            path: 'alarm',
            name: '报警管理',
          },
          {
            path: 'hotfix',
            name: '热修发布',
          },
        ],
      },
      {
        path: 'control',
        name: '质量管控',
        icon: <IconFilter />,
        routes: [
          {
            path: 'package',
            name: '包体管控',
            routes: [
              {
                path: 'monitor',
                name: '包大小监控',
              },
              {
                path: 'borrow',
                name: '包大小额度借用',
              },
              {
                path: 'balance',
                name: '包大小资产负债',
              },
              showPackageManage
                ? {
                    path: 'manage',
                    name: '业务线包大小配额配置',
                  }
                : {},
            ],
          },
          {
            path: 'storage',
            name: '存储管控',
          },
          {
            path: 'launch',
            name: '启动管控',
          },
          {
            path: 'traffic',
            name: '流量管控',
          },
          {
            path: 'publish',
            name: '发布管控',
          },
        ],
      },
      {
        path: 'alarm',
        name: '指标预警',
        hideInMenu: true, // fixme：还未完工，先屏蔽
        routes: [
          {
            path: 'list',
            name: '预警列表',
          },
          {
            path: 'list/titan',
            name: 'Titan 列表',
          },
          {
            path: 'data',
            name: '数据看板',
          },
        ],
      },
    ],
  },
  {
    path: '/devops',
    name: '工程效能',
    icon: <Icon svg={<EfficiencyToolIconSvg />} />,
    defaultPath: 'build-tools/custom-build',
    rootPath: '/devops',
    routes: [
      {
        path: 'tbc',
        name: 'TBC',
        icon: <IconJianying />,
        routes: [
          {
            path: 'ad-code-sync',
            name: '广告代码同步',
            versionFilter: false,
            appFilter: false,
          },
          {
            path: 'code-sync',
            name: '代码同步',
            hideInMenu: true,
          },
          {
            path: 'mr-sync',
            name: 'MR 同步',
            versionFilter: false,
            appFilter: false,
          },
        ],
      },
      {
        path: 'efficiency-metric',
        icon: <IconHistogram />,
        name: '工程效能指标',
        routes: [
          {
            path: 'local',
            name: '本地',
          },
          {
            path: 'ci-cd',
            name: 'CI/CD',
          },
        ],
      },
      {
        path: 'build-tools',
        name: '构建工具',
        icon: <IconWrench />,
        routes: [
          {
            path: 'custom-build',
            name: '触发构建',
          },
          {
            path: 'build-history',
            name: '构建存档',
          },
          {
            path: 'settings',
            name: '构建配置',
          },
          {
            path: 'git-branch',
            name: '分支工具',
            icon: <IconBranch />,
            routes: [
              {
                path: 'merge-back',
                name: '分支回流',
              },
              {
                path: 'operation',
                name: '分支操作',
              },
            ],
          },
        ],
      },
      {
        path: 'upgrade-tools',
        name: '升级工具',
        icon: <IconArrowUp />,
        routes: [
          {
            hideInMenu: platform !== PlatformType.Android,
            path: 've-version-android',
            name: '升级 VE 版本',
          },
          {
            path: 'cloud-album-android',
            name: '升级云相册版本',
            hideInMenu: platform !== PlatformType.Android,
          },
          {
            path: 'components-ios',
            name: '升级组件库版本',
            hideInMenu: platform !== PlatformType.iOS,
          },
          {
            path: 'lynx-version-android',
            name: '升级 Lynx 组件版本',
            hideInMenu: platform !== PlatformType.Android,
          },
          {
            path: 'lynx-template',
            name: '升级 Lynx 模板',
          },
          {
            path: 'i18n-android',
            name: '升级文案',
            hideInMenu: platform !== PlatformType.Android,
          },
          {
            path: 'history',
            name: '升级历史',
          },
        ],
      },
      {
        path: 'debug-tools',
        name: '调试工具',
        icon: <IconTerminal />,
        routes: [
          {
            path: 'pasteboard-synchronizer',
            name: 'Pasteboard Synchronizer',
            // icon: <APIcon type={'icon-Package'} />,
          },
          {
            path: 'draft-browser',
            name: 'Darft Browser',
            // icon: <APIcon type={'icon-Package'} />,
          },
          {
            path: 'alog-getter',
            name: 'Alog Getter',
            // icon: <APIcon type={'icon-Package'} />,
            hideInMenu: platform !== PlatformType.iOS,
          },
          {
            path: 'realtime-log-debug',
            name: 'Realtime Log Debugging',
            // icon: <APIcon type={'icon-Package'} />,
            hideInMenu: platform !== PlatformType.iOS,
          },
          {
            path: 'draft-diff-tracker',
            name: 'Draft Diff Tracker',
            // icon: <APIcon type={'icon-Package'} />,
          },
          {
            path: 'action-tracker',
            name: 'Action Tracker',
          },
        ],
      },
      {
        path: 'arch-tools',
        name: '架构工具',
        icon: <IconArchive />,
        routes: [
          {
            path: 'component',
            name: '组件管理',
          },
        ],
      },
    ],
  },
  {
    path: 'efficiency',
    name: '效能度量',
    defaultPath: 'dashboard/version-metric',
    rootPath: '/efficiency',
    icon: <Icon svg={<EffectivenessStatisticsIconSvg />} />,
    routes: [
      {
        path: 'review',
        name: '复盘',
        hideInMenu: true,
      },
      {
        path: 'dashboard',
        name: '研效看板',
        icon: <IconHistogram />,
        routes: [
          {
            path: 'version-metric',
            name: '版本度量',
            appFilter: false,
          },
          {
            path: 'story-metric',
            name: '需求度量',
            appFilter: false,
          },
          {
            path: 'team-metric',
            name: '团队度量',
            appFilter: false,
          },
          {
            path: 'conflict-board',
            name: '冲突看板',
            appFilter: false,
          },
        ],
      },
      {
        path: 'okr-overview',
        name: 'OKR 总览',
        versionFilter: false,
        appFilter: false,
        icon: <IconDuration />,
      },
      {
        path: 'red-black-list',
        name: '红黑榜',
        appFilter: false,
        icon: <IconOrderedList />,
      },
      {
        path: 'excessivelyOrLowEntry',
        name: '超录/低录报表',
        icon: <IconOrderedList />,
      },
    ],
  },
  {
    path: '/settings',
    name: '设置',
    icon: <SettingOutlined />,
    defaultPath: 'common',
    rootPath: '/settings',
    routes: [
      {
        path: 'common',
        name: '通用配置',
      },
      {
        path: 'multi-business-line',
        name: '多业务线配置',
      },
      {
        path: 'release',
        name: '发版平台配置',
      },
      {
        path: 'libra',
        name: '实验管理配置',
        routes: [
          {
            path: 'permission-manage',
            name: '权限管理',
            versionFilter: false,
            appFilter: false,
          },
          {
            path: 'meego-team-manage',
            name: '团队管理',
            versionFilter: false,
            appFilter: false,
          },
          {
            path: 'debug-page',
            name: '调试页',
            versionFilter: false,
            appFilter: false,
            hideInMenu: true,
          },
        ],
      },
      {
        path: 'quality',
        name: '质量平台配置',
        routes: [
          {
            path: 'metric-config',
            name: '指标配置',
          },
          {
            path: 'scene-owner',
            name: 'VC 页面负责人',
          },
          {
            path: 'slardar',
            name: 'Slardar 配置',
            routes: [
              {
                path: 'auto-level',
                name: '自动开单黑名单',
              },
            ],
          },
        ],
      },
      {
        path: 'efficiency',
        name: '效能度量配置',
        routes: [
          {
            path: 'data-preview',
            name: '数据预览',
          },
        ],
      },
      {
        path: 'user-custom',
        name: '用户设置',
      },
      {
        path: 'permission',
        name: '权限管理',
      },
      {
        path: 'oncall',
        name: 'OnCall 配置',
      },
      showConfigCheckList
        ? {
            path: 'checkListInfo',
            name: '配置 CheckList 模版',
          }
        : {},
      {
        path: 'developer',
        name: '开发者选项',
        routes: [
          {
            path: 'abnormal-approval',
            name: '异常审批流',
          },
          {
            path: 'build-info',
            name: '纸飞机构建信息',
            routes: [
              {
                path: 'host',
                name: '主应用构建信息',
              },
              {
                path: 'quality',
                name: '质量看板构建信息',
              },
            ],
          },
          {
            path: 'message',
            name: '纸飞机消息看板',
          },
        ],
      },
    ],
  },
  {
    path: '/debug',
    name: '调试',
    icon: <InfoCircleOutlined />,
    defaultPath: './',
    hideInMenu: true,
    rootPath: '/debug',
    routes: [
      {
        path: 'common',
        name: '通用调试页面',
      },
    ],
  },
];

const GLOBAL_DEFAULT_PAGE = '/homepage';

let currentSessionAppSetting: AppSetting, currentSessionVersion: VersionSetting;

const App: React.FC = () => {
  const settings: ProSettings | undefined = {
    fixSiderbar: true,
    layout: 'mix',
    splitMenus: true,
  };

  const [loginUserInfo, setLoginUserInfo] = useState<LoginUserInfo>();
  const [showConfigCheckList, setShowConfigCheckList] = useState<boolean>(false);
  const [showPackageManage, setShowPackageManage] = useState<boolean>(false); // 控制包体配置权限
  const [pathname, setPathname] = useState('/');
  const [appList, setAppList] = useState<AppSetting[]>([]);
  const [versionList, setVersionList] = useState<VersionSetting[]>([]);
  const [versionDic, setVersionDic] = useState<{ [key: number]: VersionSetting[] }>({});
  const navigate = useNavigate();

  const [appSettingState, appSettingActions] = useModel(AppSettingModule);
  const [userSettingState, userSettingAction] = useModel(UserSettingModule);
  const [versionSettingState, versionSettingAction] = useModel(VersionSettingModel);
  // const [showVersionSelect, setShowVersionSelect] = useState<boolean>(true);
  const [currentVersion, setCurrentVersion] = useState<string>('');
  const [searchParams, setSearchParams] = useSearchParams();
  const hasInit = useRef(false);
  const [pageRoutes, setPageRoutes] = useState<Route[]>([]);
  const [versionFilter, setVersionFilter] = useState(false);
  const [appFilter, setAppFilter] = useState(false);
  const [ppe, setPpe] = useState<string>('');

  /**
   * 11.0.0->9.0.0
   * @param lvVersion
   */
  const lv2ccVersion = (lvVersion: string): string => {
    const num = Number(lvVersion.substring(0, lvVersion.length - 4)) - 2;
    return num.toString().concat(lvVersion.substring(lvVersion.length - 4, lvVersion.length));
  };

  /**
   * 9.0.0->11.0.0
   * @param ccVersion
   */
  const cc2lvVersion = (ccVersion: string): string => {
    const num = Number(ccVersion.substring(0, ccVersion.length - 4)) + 2;
    return num.toString().concat(ccVersion.substring(ccVersion.length - 4, ccVersion.length));
  };

  const isHomePage = () => pathname === '/homepage';

  const transVersionString = (
    originVersion: string,
    fromProdut: ProductType,
    toProduct: ProductType,
  ): string | undefined => {
    if (fromProdut === LVProductType.cc && toProduct === LVProductType.lv) {
      return cc2lvVersion(originVersion);
    }
    if (fromProdut === 'lv' && toProduct === 'cc') {
      return lv2ccVersion(originVersion);
    }

    return undefined;
  };

  function customNavigate(target: string) {
    // 跳转只保留必要参数
    const whiteList = ['appid', 'version'];
    const p = new URLSearchParams();
    searchParams.forEach((value, key, _) => {
      if (whiteList.includes(key)) {
        p.set(key, value);
      }
    });
    console.log(`navigate ${target}`);
    navigate({
      pathname: target || GLOBAL_DEFAULT_PAGE,
      search: p.toString(),
    });
    setPathname(target);
  }

  useEffect(() => {
    queryUserAndSetting().then(value => {
      console.log('versionOwner', value.versionOwner);
      console.log('loginUser', value.loginUser);
      // if (value.versionOwner) {
      //   setShowConfigCheckList(value.versionOwner.map(owner => owner.name).includes(value.loginUser.name));
      // }
      setAppList(value.appList);
      setLoginUserInfo(value.loginUser);
      userSettingAction.updateUserEmail(value.loginUser.email);
      const paramAppId = searchParams.get('appid');
      const paramVersion = searchParams.get('version');
      console.log(`parseSearchParams appId:${paramAppId} version:${paramVersion}`);
      const selectApp = paramAppId ? parseInt(paramAppId, 10) : value.userSetting.selectApp;
      const selectVersion = !paramVersion || paramVersion.length === 0 ? value.userSetting.selectVersion : paramVersion;
      const appSetting = value.appList.find(app => app.id === selectApp);
      console.log(`selectVersion [${paramVersion}] [${selectVersion}] [${value.userSetting.selectVersion}]`);
      if (appSetting) {
        console.log(`updateSelectAppInfo1 ${JSON.stringify(appSetting)}`);
        appSettingActions.updateSelectAppInfo(appSetting);
        currentSessionAppSetting = appSetting;
      }
      // 判断用户是否有包体管理权限
      const packageManagerList = [
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
      ];
      setShowPackageManage(packageManagerList.includes(value.loginUser.email));

      setCurrentVersion(selectVersion ?? '');
      queryVersionListV2({ data: { app_list: value.appList } }).then(result => {
        const versionMap = result as { [key: number]: VersionSetting[] };
        console.log(versionMap);
        setVersionDic(versionMap);
        hasInit.current = true;
        if (appSetting) {
          const currentAppVersionList = versionMap[appSetting.id];
          if (currentAppVersionList.length > 0) {
            const select =
              currentAppVersionList.find(v => v.normalVersion === selectVersion) ?? currentAppVersionList[0];
            currentSessionVersion = select;
            versionSettingAction.updateSelectVersionInfo(select);
            setCurrentVersion(select.normalVersion ?? '');
          }
        }
      });
      setPpe(value.ppe);
    });
    if (location.pathname === '/') {
      customNavigate(GLOBAL_DEFAULT_PAGE);
    } else {
      setPathname(location.pathname);
    }
  }, []);

  useEffect(() => {
    // 使用全量路由表来判断显示状态
    const defaultRoutes = getDefaultRoutes(appSettingState.info.platform, true, true);
    const checkNeedVersion = (routes: Route[], prefix: string) => {
      for (const route of routes) {
        if (!route.path) {
          continue;
        }
        const actualPath = prefix + add_prefix_ne('/')(route.path);
        if (actualPath === pathname) {
          return route.versionFilter === true;
        }
        if (route.routes && checkNeedVersion(route.routes, actualPath)) {
          return true;
        }
      }
      return false;
    };
    const checkNeedAppFilter = (routes: Route[], prefix: string) => {
      for (const route of routes) {
        if (!route.path) {
          continue;
        }
        const actualPath = prefix + add_prefix_ne('/')(route.path);
        if (actualPath === pathname) {
          // 如果未设置 appFilter，则默认展示 App 版本选择
          return route.appFilter === undefined || route.appFilter;
        }
        if (route.routes && !checkNeedAppFilter(route.routes, actualPath)) {
          return false;
        }
      }
      return true;
    };
    setVersionFilter(checkNeedVersion(defaultRoutes, ''));
    setAppFilter(checkNeedAppFilter(defaultRoutes, ''));
  }, [pathname, appSettingState.info.platform]);

  useEffect(() => {
    console.log(`trigger ${hasInit.current}`);
    if (!hasInit.current) {
      return;
    }
    if (appSettingState?.info?.id) {
      searchParams.set('appid', appSettingState.info.id.toString());
    }
    console.log(`updateSearchParams appSettings:${JSON.stringify(versionSettingState.info)}`);
    if (currentVersion) {
      searchParams.set('version', currentVersion);
    }
    setSearchParams(searchParams);
    console.log(`updateSearchParams ${searchParams.toString()}`);

    currentSessionAppSetting = appSettingState.info;
    currentSessionVersion = versionSettingState.info;
  }, [appSettingState, currentVersion, versionSettingState]);

  useEffect(() => {
    const defaultRoutes = getDefaultRoutes(appSettingState.info.platform, showConfigCheckList, showPackageManage);
    if (appSettingState.info.businessInfo?.route_config) {
      const filtered = filterRoutes(appSettingState.info.businessInfo.route_config, defaultRoutes);
      setPageRoutes(filtered);
      if (!checkPathSupport(appSettingState.info.businessInfo.route_config, pathname)) {
        customNavigate(GLOBAL_DEFAULT_PAGE);
        Toast.info('当前App不支持该功能，已自动跳转到首页');
      }
    }
  }, [appSettingState, showConfigCheckList, showPackageManage]);

  useEffect(() => {
    if (pathname !== window.location.pathname) {
      setPathname(window.location.pathname);
    }
    reportTeaEvent({
      data: {
        eventName: 'PAGE_VIEW',
        params: {
          path: window.location.pathname,
        },
      },
    });
  }, [window.location.pathname]);

  const createUserMenuView: MenuProps['items'] = [
    {
      key: 'name',
      label: loginUserInfo?.name,
    },
    {
      key: 'logout',
      label: (
        <div
          style={{ width: '100%' }}
          onClick={() => {
            location.href = `/api/oauth2/logout?platform=feishu&returnUrl=${encodeURIComponent(
              window.location.origin,
            )}`;
          }}
        >
          <LogoutOutlined /> Logout
        </div>
      ),
    },
  ];

  const onAppSelect = async (selectValue: number) => {
    console.log(appList);
    const selectApp = appList?.find(value => value.id === selectValue);
    let newVersionSetting: VersionSetting = {} as VersionSetting;
    if (selectApp) {
      const lastAppSetting: AppSetting = appSettingState.info;
      if (lastAppSetting.businessID === selectApp.businessID && versionDic[selectApp.id]) {
        for (const versionSetting of versionDic[selectApp.id]) {
          if (
            lastAppSetting.productType === selectApp.productType &&
            versionSetting.normalVersion === versionSettingState.info.normalVersion
          ) {
            newVersionSetting = versionSetting;
            break;
          } else if (
            transVersionString(versionSetting.normalVersion, selectApp.productType, lastAppSetting.productType) ===
            versionSettingState.info.normalVersion
          ) {
            newVersionSetting = versionSetting;
            break;
          }
        }
      }
      if (versionDic[selectApp.id] && !newVersionSetting.normalVersion) {
        newVersionSetting = versionDic[selectApp.id][0];
      }

      setCurrentVersion(newVersionSetting.normalVersion ?? '');

      await setUserDefaultApp({
        data: { select_app: selectValue, select_version: newVersionSetting.normalVersion },
      });
      console.log(`updateSelectAppInfo2 ${JSON.stringify(selectApp)}`);
      currentSessionAppSetting = selectApp;
      userSettingAction.updateSelectAppId(selectApp.id, newVersionSetting.normalVersion);
      appSettingActions.updateSelectAppInfo(selectApp);
      versionSettingAction.updateSelectVersionInfo(newVersionSetting);
    }
  };

  const onAppSelectChange = (value: string | number | any[] | Record<string, any> | undefined) => {
    if (typeof value === 'number') {
      onAppSelect(value).then();
    }
  };

  const onVersionSelectChange = async (selectValue: string | any) => {
    const selectVersion = versionDic[appSettingState.info.id]?.find(value => value.normalVersion === selectValue);
    if (selectVersion) {
      versionSettingAction.updateSelectVersionInfo(selectVersion);
      currentSessionVersion = selectVersion;
      setCurrentVersion(selectValue);
      await setUserDefaultApp({
        data: {
          select_app: appSettingState.info.id,
          select_version: selectValue,
        },
      });
    }
  };

  const onClickLogoOrTitle = () => {
    customNavigate('/homepage');
  };

  return (
    <Skeleton loading={pageRoutes.length === 0} active>
      <ConfigProvider
        theme={{
          components: {
            Table: {
              headerBg: '#fafafa',
            },
          },
        }}
      >
        <ProLayout
          logo={<img src={icon} onClick={onClickLogoOrTitle} />}
          // @ts-ignore
          title={
            <div onClick={onClickLogoOrTitle}>
              <Space direction={'horizontal'}>
                <Title
                  level={3}
                  style={{
                    margin: '0 2px 0 4px',
                    color: '#2575fc',
                  }}
                >
                  纸飞机
                </Title>
                {/* <Typography.Text style={{ fontStyle: 'italic', fontSize: 14, color: 'rgba(var(--semi-grey-3), 1)' }}>*/}
                {/*  剪映一站式研效平台*/}
                {/* </Typography.Text>*/}
              </Space>
            </div>
          }
          pageTitleRender={(props, defaultDom) => `${getPageTitle(props, true)} - 纸飞机`}
          route={{
            path: '/',
            routes: pageRoutes,
          }}
          location={{
            pathname,
          }}
          siderWidth={220}
          menu={{
            type: 'sub',
            defaultOpenAll: false,
          }}
          avatarProps={{
            render: (props, defaultDom) => (
              <Dropdown menu={{ items: createUserMenuView }} arrow>
                <Avatar src={loginUserInfo?.avatar} size={'large'} />
              </Dropdown>
            ),
          }}
          actionsRender={props => [
            ppe ? (
              <Tag key="ppe" color={'red'}>
                PPE: {ppe}
              </Tag>
            ) : (
              <></>
            ),
            versionFilter ? (
              <Select
                key={'versionInfo'}
                style={{ width: 120 }}
                size={'large'}
                value={currentVersion}
                onChange={onVersionSelectChange}
                placeholder={'请选择版本'}
              >
                {versionDic[appSettingState.info.id]?.map(value => (
                  <Select.Option key={value.normalVersion} value={value.normalVersion} label={value.normalVersion}>
                    {value.normalVersion}
                  </Select.Option>
                ))}
              </Select>
            ) : (
              <></>
            ),
            appFilter ? (
              <Select
                key={'appInfo'}
                style={{ width: '100%', minWidth: 180 }}
                size={'large'}
                onChange={onAppSelectChange}
                placeholder={'请选择App'}
                value={appSettingState.info.id}
              >
                {appList?.map(v => (
                  <Select.Option
                    key={v.id}
                    value={v.id}
                    label={
                      <Space direction={'horizontal'}>
                        <Avatar
                          src={
                            v.icon ??
                            (v.platform === PlatformType.iOS ? (
                              <AppleFilled style={{ color: 'darkgrey' }} />
                            ) : (
                              <AndroidFilled style={{ color: 'darkgrey' }} />
                            ))
                          }
                          shape={'square'}
                          size={22}
                        />
                        {`${v.name}-${v.platform}`}
                      </Space>
                    }
                  />
                ))}
              </Select>
            ) : (
              <></>
            ),
            <>
              <HelpMenu />
            </>,
            <Tooltip key={'setting'} position={'bottom'} content={'设置'}>
              <IconSetting
                style={{ color: pathname.startsWith('/settings') ? '#1677ff' : 'rgba(28, 31, 35, 0.8)' }}
                size={'large'}
                onClick={() => {
                  customNavigate('/settings/common');
                }}
              />
            </Tooltip>,
          ]}
          // menuFooterRender={props => {
          //   if (props?.collapsed) {
          //     return undefined;
          //   }
          //   const year = dayjs().year();
          //   return (
          //     <div
          //       style={{
          //         textAlign: 'center',
          //         paddingBlockStart: 12,
          //       }}
          //     >
          //       <div>© {year} Made by</div>
          //       <div>剪映-移动端-基础技术</div>
          //     </div>
          //   );
          // }}
          onMenuHeaderClick={e => console.log(e)}
          menuItemRender={(item, dom) =>
            item.path === '/settings' || item.path === '/homepage' ? (
              <HideParent level={2} />
            ) : (
              <div
                style={{ width: '100%' }}
                onClick={() => {
                  let target = item.path;
                  if ('defaultPath' in item && 'rootPath' in item && item.path === item.rootPath) {
                    target = `${item.path}/${item.defaultPath}`;
                  }
                  if (target) {
                    customNavigate(target);
                  }
                }}
              >
                {dom}
              </div>
            )
          }
          {...settings}
          menuProps={{
            theme: 'light',
          }}
          token={{
            header: {
              colorBgMenuItemSelected: 'rgba(0,0,0,0.04)',
              heightLayoutHeader: 65,
              colorTextMenu: 'rgba(0,0,0,0.7)',
            },
            sider: {
              colorMenuBackground: '#fff',
              colorTextMenuSelected: 'rgb(22, 119, 255)',
            },
            pageContainer: isHomePage()
              ? {
                  paddingBlockPageContainerContent: 0,
                  paddingInlinePageContainerContent: 0,
                }
              : {
                  paddingBlockPageContainerContent: 8,
                  paddingInlinePageContainerContent: 30,
                },
          }}
        >
          <PageContainer
            title={false}
            header={{
              breadcrumbRender: (props, defaultDom) =>
                // @ts-ignore
                props.currentMenu && props.currentMenu.path && props.currentMenu.breadcrumbClickable ? (
                  <div
                    onClick={() => {
                      navigate({
                        // @ts-ignore
                        pathname: props.currentMenu.path,
                      });
                    }}
                  >
                    {defaultDom}
                  </div>
                ) : (
                  defaultDom
                ),
            }}
          >
            <ProCard
              style={
                isHomePage()
                  ? {
                      margin: 0,
                      minHeight: '100vh',
                      borderRadius: 0,
                    }
                  : {
                      marginTop: 12,
                      minHeight: '80vh',
                    }
              }
            >
              <Outlet />
            </ProCard>
          </PageContainer>
        </ProLayout>
      </ConfigProvider>
    </Skeleton>
  );
};
const ExportApp: React.FC = () => {
  const jumpNewUrl = () => {
    console.log('check url');
    const isLocalhost = /^(localhost|127(?:\.[0-9]+){0,2}\.[0-9]+|\[::1])(:\d+)?$/.test(window.location.host);
    const isIP = /^\d+\.\d+\.\d+\.\d+$/.test(window.location.host);
    return window.location.host !== MAIN_HOST && !isIP && !isLocalhost;
  };
  if (jumpNewUrl()) {
    window.location.replace(`${window.location.protocol}//${MAIN_HOST}${window.location.pathname}`);
  } else {
    configure({
      // 这里的 request 是一体化默认的请求工具，interceptor 函数需返回一个新的 request。
      // 新 request 的出参必须是 parse body 之后的结果
      interceptor(request) {
        return async (url, params) => {
          try {
            const headers = params?.headers as any;
            if (currentSessionAppSetting) {
              headers['app-setting'] = encodeURIComponent(JSON.stringify(currentSessionAppSetting));
            }
            if (currentSessionVersion) {
              headers['version-setting'] = encodeURIComponent(JSON.stringify(currentSessionVersion));
            }
            const res = await request(url, params);
            const result = await res.json();
            if (result.code === 10010) {
              location.href = `/api/oauth2/login?platform=feishu&returnUrl=${encodeURIComponent(
                window.location.origin,
              )}`;
            } else if (result.auth_info) {
              const info = result.auth_info as AuthInfo;
              Modal.error({
                title: '暂无权限',
                content: (
                  <>
                    您暂无对 {resourceNameMap[info.resource_key]} 资源的 {info.permission_name} 权限。您可以前往
                    <a
                      href={get_apply_url(info.resource_key, info.permission_name)}
                      target="_blank"
                      rel="noopener noreferrer"
                    >
                      此处
                    </a>
                    申请该权限。敬请谅解。
                  </>
                ),
              });
              return { code: NetworkCode.NoAuthority };
            } else {
              return result;
            }
          } catch (e) {
            console.log(e);
          }
        };
      },
    });
    return (
      <Provider>
        <App />
      </Provider>
    );
  }
};

export default ExportApp;
