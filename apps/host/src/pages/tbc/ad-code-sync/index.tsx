import React, { useState, useEffect } from 'react';
import { Button, Table, Form, Select, Spin, Toast, Row, Col } from '@douyinfe/semi-ui';
import { getAdCodeSyncRecords, syncAdCodeBranchAndCreateMr } from '@api/tbc';
import { TBCAdCodeSyncRecordInfo, TBC_AD_REPO_CONFIG, TBC_AD_BRANCH_CONFIG } from '@shared/tbc/TBCAdCodeSyncRecordInfo';
import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import timezone from 'dayjs/plugin/timezone';
import { useModel } from '@edenx/runtime/model';
import UserSettingModule from '@/model/userSettingModel';

const unixTimestampToFormatStr = (timestamp: number) => {
  // timestamp 是微秒格式
  dayjs.extend(utc);
  dayjs.extend(timezone);
  return dayjs(timestamp)
    .utc() // 将时间标记为 UTC
    .tz('Asia/Shanghai') // 转换为东八区
    .format('YYYY-MM-DD HH:mm:ss');
};

const AdCodeSyncPage: React.FC = () => {
  const [userSettingState] = useModel(UserSettingModule);
  const currentLoginUserEmail = () => userSettingState.info.email ?? 'Unknown User';
  const [records, setRecords] = useState<TBCAdCodeSyncRecordInfo[]>([]);
  const [loading, setLoading] = useState(false);
  const [formApi, setFormApi] = useState<any>();

  const columns = [
    { title: '项目', dataIndex: 'projectId', render: (id: string) => TBC_AD_REPO_CONFIG[id] || id },
    { title: '标题', dataIndex: 'title' },
    { title: '源分支', dataIndex: 'sourceBranch' },
    { title: '目标分支', dataIndex: 'targetBranch' },
    // { title: '创建人', dataIndex: 'creator' },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      render: (timestamp: number) => unixTimestampToFormatStr(timestamp * 1000),
    },
    // { title: '状态', dataIndex: 'status' },
    {
      title: 'MR链接',
      dataIndex: 'mrUrl',
      key: 'mrUrl',
      render: (text: string) => (
        <a href={text} target="_blank" rel="noopener noreferrer">
          查看MR
        </a>
      ),
    },
  ];

  const fetchRecords = async () => {
    setLoading(true);
    try {
      const res = await getAdCodeSyncRecords({ data: {} });
      if (res) {
        setRecords(res as unknown as TBCAdCodeSyncRecordInfo[]);
      }
    } catch (error) {
      Toast.error('获取记录失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchRecords();
  }, []);

  const handleSync = async (values: any) => {
    setLoading(true);
    try {
      const { projectId, sourceBranch, targetBranch } = values;
      await syncAdCodeBranchAndCreateMr({
        data: {
          projectId: Number(projectId),
          sourceBranch,
          targetBranch,
          userName: currentLoginUserEmail().split('@')[0],
        },
      });
      Toast.success('同步和创建MR任务已发起');
      fetchRecords();
    } catch (error) {
      Toast.error('发起同步失败');
    } finally {
      setLoading(false);
    }
  };

  const handleProjectChange = (value: string | number | any[] | Record<string, any>) => {
    if (typeof value !== 'string') {
      return;
    }
    const projectId = value;
    if (formApi && TBC_AD_BRANCH_CONFIG[projectId]) {
      const { sourceBranch, targetBranch } = TBC_AD_BRANCH_CONFIG[projectId];
      formApi.setValue('sourceBranch', sourceBranch);
      formApi.setValue('targetBranch', targetBranch);
    }
  };

  return (
    <div style={{ padding: 20 }}>
      <Spin spinning={loading}>
        <Form
          layout="horizontal"
          getFormApi={setFormApi}
          onSubmit={handleSync}
          initValues={{ sourceBranch: 'lv/dev', targetBranch: 'overseas/dev' }}
        >
          <Row gutter={16}>
            <Col span={8}>
              <Form.Select
                field="projectId"
                label="代码库"
                placeholder="请选择代码库"
                rules={[{ required: true, message: '请选择代码库' }]}
                style={{ width: '100%' }}
                onChange={handleProjectChange}
              >
                <Select.Option value="536633">faceu-android/unified_adloader</Select.Option>
                <Select.Option value="540549">faceu-ios/LVUniteAd</Select.Option>
              </Form.Select>
            </Col>
            <Col span={8}>
              <Form.Input
                field="sourceBranch"
                label="源分支"
                placeholder="例如, lv/dev"
                rules={[{ required: true, message: '请输入源分支' }]}
                style={{ width: '100%' }}
              />
            </Col>
            <Col span={8}>
              <Form.Input
                field="targetBranch"
                label="目标分支"
                placeholder="例如, overseas/dev"
                rules={[{ required: true, message: '请输入目标分支' }]}
                style={{ width: '100%' }}
              />
            </Col>
          </Row>
          <Row style={{ marginTop: 20 }}>
            <Col>
              <Button type="primary" htmlType="submit" style={{ marginRight: 8 }}>
                同步并创建MR
              </Button>
              <Button onClick={fetchRecords}>刷新</Button>
            </Col>
          </Row>
        </Form>
        <Table columns={columns} dataSource={records} style={{ marginTop: 20 }} rowKey="id" />
      </Spin>
    </div>
  );
};

export default AdCodeSyncPage;
