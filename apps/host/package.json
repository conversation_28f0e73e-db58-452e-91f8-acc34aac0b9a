{"name": "host", "version": "1.0.0", "scripts": {"dev": "INSPIRECLOUD_MIDDLEWARE_VERSIONS='{\\\"workday\\\":\\\"0.0.5\\\"}' _LIGHT_SERVICE_ID=ttdcaw SERVICE_TOKEN=418de29c-9e95-4711-9aa0-8cc546131d97 doas -p toutiao.redis.paperairplane -t zti edenx dev", "build": "edenx build", "start": "INSPIRECLOUD_MIDDLEWARE_VERSIONS='{\"workday\":\"0.0.5\"}' _LIGHT_SERVICE_ID=ttdcaw SERVICE_TOKEN=418de29c-9e95-4711-9aa0-8cc546131d97 GULU_ENV=dev edenx start", "serve": "edenx serve", "new": "edenx new", "lint": "edenx lint --fix", "lint:error": "edenx lint --quiet", "preview": "edenx deploy --mode preview", "deploy": "edenx deploy", "upgrade": "edenx upgrade", "gen_gulu_type": "gulu ts  --root ./api"}, "engines": {"node": ">=18.20.4"}, "eslintIgnore": ["node_modules/", "dist/"], "dependencies": {"@ies/cem-open-sdk": "0.8.1", "@gulux-bam/ies_efficiency_experience_feedback_analysis": "^1.0.614-1742300303235", "@gulux-bam/ies_efficiency_cem_analysis": "^1.0.620-1742891567321", "@ant-design/charts": "^1.4.3", "@ant-design/icons": "^5.3.0", "@ant-design/plots": "2.3.2", "@ant-design/pro-components": "~2.6.49", "@ant-design/pro-form": "^2.24.4", "@ant-design/pro-table": "3.17.2", "@ant-design/pro-utils": "2.15.18", "@antv/g2": "^4.2.10", "@byted-light/axios": "1.0.5", "@byted-light/db": "4.0.6", "@byted-service/tea": "^1.5.7", "@byted/bytedmongoose": "2.2.0", "@byted/inspirecloud-api": "^2.0.7", "@byted/typegoose": "8.3.0", "@douyinfe/semi-icons": "^2.52.3", "@douyinfe/semi-icons-lab": "^2.52.3", "@douyinfe/semi-illustrations": "^2.52.3", "@douyinfe/semi-ui": "^2.62.1", "@dp/tea-sdk-node": "^3.0.8", "@edenx/plugin-bff": "1.59.0", "@edenx/plugin-garfish": "1.59.0", "@edenx/plugin-gulux": "1.59.0", "@edenx/runtime": "1.59.0", "@gulux/application-http": "2.1.1", "@gulux/cli": "^1.23.0", "@gulux/gulux": "^1.8.0", "@gulux/plugin-async-local-storage": "^1.1.2", "@gulux/plugin-byted-metrics": "^1.1.0", "@gulux/plugin-http-client": "^1.1.0", "@gulux/plugin-oauth2": "^1.2.1", "@gulux/plugin-redis": "1.3.0", "@gulux/plugin-rocketmq-producer": "1.1.7", "@ies/perfect-process": "^1.3.1", "@js-temporal/polyfill": "^0.4.2", "@larksuiteoapi/api": "1.0.14", "@larksuiteoapi/node-sdk": "^1.23.0", "@logsdk/node-plugin-http": "^3.4.0", "@modelcontextprotocol/sdk": "1.12.1", "@monaco-editor/react": "^4.6.0", "@pa/backend": "workspace:*", "@pa/shared": "workspace:*", "@visactor/react-vchart": "^1.12.5", "@visactor/vchart-semi-theme": "^1.12.1", "antd": "^5.14.2", "axios": "^1.6.7", "copy-to-clipboard": "^3.3.3", "cron-parser": "^4.9.0", "crypto": "^1.0.1", "crypto-browserify": "^3.12.0", "crypto-js": "4.2.0", "dataopen-sdk-nodejs": "^0.0.3", "date-fns": "4.1.0", "dayjs": "^1.11.10", "dayjs-plugin-utc": "^0.1.2", "echarts": "~5.4.3", "echarts-for-react": "3.0.2", "fast-xml-parser": "^4.2.4", "immer": "^9.0.7", "jsonwebtoken": "^9.0.2", "jszip": "^3.10.1", "lodash": "^4.17.21", "loglevel": "1.9.2", "minimist": "^1.2.7", "nanoid": "3.3.4", "node-fetch": "2.7.0", "papaparse": "5.4.1", "qrcode": "^1.5.1", "qrcode.react": "^3.1.0", "rc-resize-observer": "^1.4.0", "rc-segmented": "2.2.2", "react": "~18.2.0", "react-diff-viewer": "^3.1.1", "react-dnd": "14", "react-dnd-html5-backend": "14", "react-dom": "~18.2.0", "react-highlight-words": "^0.20.0", "react-json-view": "^1.21.3", "react-live": "^4.1.7", "react-papaparse": "^4.1.0", "react-qrbtf": "^1.2.1", "react-router-dom": "^6.26.1", "react-syntax-highlighter": "^15.5.0", "type-fest": "2.15.0", "uuid": "10.0.0", "zod": "^3.22.3"}, "devDependencies": {"@bytecloud/common-lib": "^7.30.2", "@byted-emo/config": "workspace:*", "@byted/eslint-config-eden": "^5.0.37", "@edenx/app-tools": "1.59.0", "@edenx/builder-plugin-semi": "1.59.0", "@edenx/tsconfig": "1.59.0", "@pansy/react-charts": "^1.0.0", "@types/crypto-js": "4.2.2", "@types/jsonwebtoken": "^9.0.3", "@types/lodash": "^4.14.202", "@types/minimist": "^1.2.2", "@types/node": "18.6.2", "@types/node-fetch": "2.6.11", "@types/papaparse": "^5.3.9", "@types/qrcode": "^1.5.0", "@types/react": "~18.2.20", "@types/react-dom": "^18.2.7", "@types/react-highlight-words": "^0.16.4", "@types/react-syntax-highlighter": "^15.5.7", "@types/uuid": "^9.0.8", "eslint": "^8.57.0", "eslint-plugin-prettier": "^5.1.3", "lint-staged": "~13.1.0", "prettier": "^3.2.5", "rimraf": "^5.0.1", "ts-node": "^10.9.1", "ts-to-zod": "^3.1.3", "tsconfig-paths": "^4.2.0", "typescript": "~5.5.4"}, "packageManager": "pnpm@8.6.12"}