#!/usr/bin/env python3

import os
import sys
import shutil

def replace_in_file(file_path, search_str, replace_str):
    """Replace occurrences of a string in a file and return True if modified."""
    modified = False
    with open(file_path, 'r', encoding='utf-8') as file:
        content = file.read()

    if search_str in content:
        content = content.replace(search_str, replace_str)
        modified = True

    if modified:
        with open(file_path, 'w', encoding='utf-8') as file:
            file.write(content)
        print(f'Modified file: {file_path}')

    return modified

def find_and_replace_in_files(search_root, search_str, replace_str):
    """Traverse the directory and replace strings in .ts files."""
    print(f'search_str: {search_str} replace_str: {replace_str} root:{search_root}')
    modified_files = []

    for root, dirs, files in os.walk(search_root):
        for file in files:
            if file.endswith('.ts') or file.endswith('.tsx'):
                file_path = os.path.join(root, file)
                if replace_in_file(file_path, search_str, replace_str):
                    modified_files.append(file_path)

    return modified_files

def move_file(src, dst):
    """Move the file from src to dst."""
    # 获取目标文件的目录
    dst_dir = os.path.dirname(dst)
    
    # 如果目标目录不存在，则创建它
    if not os.path.exists(dst_dir):
        os.makedirs(dst_dir)

    # 移动文件，如果目标文件已存在，则会被覆盖
    shutil.move(src, dst)

def main():
    if len(sys.argv) != 3:
        print("Usage: python refactor.py <src> <dst>")
        sys.exit(1)

    src = sys.argv[1]
    dst = sys.argv[2]
    src_parts = src.split('/')
    dst_parts = dst.split('/')
    
    root = '.'
    if 'apps/host' in src:
        root = 'apps/host'
    if 'apps/quality' in src:
        root = 'apps/quality'
    
    if 'shared' in src_parts:
        src_suffix = '/'.join(src_parts[src_parts.index('shared')+1:]).replace('.ts', '')
        search_str = f'@shared/{src_suffix}'
        dst_suffix = '/'.join(dst_parts[dst_parts.index('shared')+1:]).replace('.ts', '')
        replace_str = f'@pa/shared/dist/{dst_suffix}'
        modified_files = find_and_replace_in_files(root, search_str, replace_str)
    if 'api' in src_parts:
        src_index = src_parts.index('api')
        search_strs = []
        while src_index < len(src_parts) - 1:
            src_index += 1
            p = '/'.join(src_parts[src_index:]).replace('.ts', '')
            search_strs.append(f"'./{p}'")
            search_strs.append(f"'../{p}'")
            search_strs.append(f"'../../{p}'")
            search_strs.append(f"'../../../{p}'")
        dst_suffix = '/'.join(dst_parts[dst_parts.index('src')+1:]).replace('.ts', '')
        replace_str = f"'@pa/backend/dist/src/{dst_suffix}'"
        print(f'search_strs: {search_strs} replace_str: {replace_str} root:{root}')
        for search_str in search_strs:
            modified_files = find_and_replace_in_files(root, search_str, replace_str)

    # Step 3: Move the src file to dst
    move_file(src, dst)
    print(f"Moved {src} to {dst}")

if __name__ == "__main__":
    main()