// 纸飞机对外接口协议定义,BAM地址: https://cloud.bytedance.net/bam/rd/serverless.fn.plriycc2g/idl

struct FetchVersionLogRequest {
  1: string bits_app_id (api.body = 'bits_app_id'),
  2: string type (api.body = 'type'),
  3: optional list<string> release_platforms (api.body = 'release_platforms'),
  4: i32 pn (api.query = 'pn'),
  5: i32 rn (api.query = 'rn'),
  6: optional list<string> version_name (api.body = 'version_name'),
  7: optional list<string> update_version (api.body = 'update_version'),
  8: optional list<string> channel (api.body = 'channel'),
  9: optional list<string> cpu (api.body = 'cpu'),
}

struct FetchVersionLog {
    1: required string version_name (go.tag = "json:\"version_name\""),
    2: required string update_version (go.tag = "json:\"update_version\""),
    3: required string cpu (go.tag = "json:\"cpu\""),
    4: optional i64 publish_time (go.tag = "json:\"publish_time\""),
    5: optional i64 release_amount (go.tag = "json:\"release_amount\""),
    6: optional i64 review_pass_time (go.tag = "json:\"review_pass_time\""),
    7: optional i64 full_publish_time (go.tag = "json:\"full_publish_time\""),
    8: optional i64 version_type (go.tag = "json:\"version_type\""),
    9: optional string channel (go.tag = "json:\"channel\""),
}

struct FetchVersionLogResponse {
    1: required i32 code (api.body = 'code'),
    2: required string msg (api.body = 'msg'),
    3: optional list<FetchVersionLog> data (api.body = 'data'),
    4: optional i32 total (api.body = 'total'),
}


service PaHostService {
    // 获取bits版本信息
    FetchVersionLogResponse FetchVersionLog(1: FetchVersionLogRequest r)(api.post='/api/open/bits_release_record', api.serializer='json')
}
