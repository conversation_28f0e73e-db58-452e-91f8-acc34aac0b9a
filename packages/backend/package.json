{"name": "@pa/backend", "version": "0.1.0", "main": "dist/src/index.js", "types": "dist/src/index.d.ts", "source": "src/index.ts", "description": "后端服务基础库", "scripts": {"dev": "rm -rf tsconfig.tsbuildinfo && tsc -p ./tsconfig.json -w", "build": "rm -rf tsconfig.tsbuildinfo && tsc -p ./tsconfig.json", "build:watch": "rm -rf tsconfig.tsbuildinfo && tsc -p ./tsconfig.json -w", "lint": "emox eslint src --fix", "lint:error": "emox eslint src --fix --quiet"}, "dependencies": {"@antv/g2": "^4.2.10", "@byted-light/axios": "1.0.5", "@byted-light/db": "4.0.6", "@byted-service/consul": "2.1.6", "@byted-service/tea": "^1.5.7", "@byted/bytedmongoose": "2.2.0", "@byted/inspirecloud-api": "^2.0.7", "@byted/typegoose": "8.3.0", "@dp/tea-sdk-node": "^3.0.8", "@edenx/plugin-bff": "1.59.0", "@edenx/plugin-garfish": "1.59.0", "@edenx/plugin-gulux": "1.59.0", "@edenx/runtime": "1.59.0", "@gulux/application-http": "2.1.1", "@gulux/cli": "^1.23.0", "@gulux/gulux": "^1.8.0", "@gulux/plugin-async-local-storage": "^1.1.2", "@gulux/plugin-byted-metrics": "^1.1.0", "@gulux/plugin-http-client": "^1.1.0", "@gulux/plugin-oauth2": "^1.2.1", "@gulux/plugin-redis": "1.3.0", "@gulux/plugin-rocketmq-producer": "1.1.7", "@ies/perfect-process": "^1.3.1", "@js-temporal/polyfill": "^0.4.2", "@larksuiteoapi/api": "1.0.14", "@larksuiteoapi/node-sdk": "^1.23.0", "@logsdk/node-plugin-http": "^3.4.0", "@monaco-editor/react": "^4.6.0", "@pa/shared": "workspace:*", "axios": "^1.6.7", "copy-to-clipboard": "^3.3.3", "cron-parser": "^4.9.0", "crypto": "^1.0.1", "crypto-browserify": "^3.12.0", "dataopen-sdk-nodejs": "^0.0.3", "date-fns": "4.1.0", "dayjs": "^1.11.10", "dayjs-plugin-utc": "^0.1.2", "fast-xml-parser": "^4.2.4", "immer": "^9.0.7", "jszip": "^3.10.1", "lodash": "^4.17.21", "loglevel": "1.9.2", "minimist": "^1.2.7", "nanoid": "3.3.4", "node-fetch": "2.7.0", "papaparse": "5.4.1", "rc-resize-observer": "^1.4.0", "rc-segmented": "2.2.2", "type-fest": "2.15.0", "tslib": "2.8.1", "react": "~18.2.0", "react-dom": "~18.2.0"}, "devDependencies": {"@bytecloud/common-lib": "^7.30.2", "@byted-emo/config": "workspace:*", "@edenx/app-tools": "1.59.0", "@edenx/builder-plugin-semi": "1.59.0", "@edenx/tsconfig": "1.59.0", "@pansy/react-charts": "^1.0.0", "@types/jsonwebtoken": "^9.0.3", "@types/lodash": "^4.14.202", "@types/minimist": "^1.2.2", "@types/node": "18.6.2", "@types/node-fetch": "2.6.11", "@types/papaparse": "^5.3.9", "@types/qrcode": "^1.5.0", "eslint": "^8.57.0", "lint-staged": "~13.1.0", "prettier": "^3.2.5", "rimraf": "^5.0.1", "ts-node": "^10.9.1", "ts-to-zod": "^3.1.3", "tsconfig-paths": "^4.2.0", "typescript": "~5.5.4"}, "publishConfig": {"access": "public", "registry": "https://bnpm.byted.org/"}}