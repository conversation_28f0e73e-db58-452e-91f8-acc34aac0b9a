import { ExecutionContainer, GuluXMiddleware, Middleware, Next, NextFunction, UseContainer } from '@gulux/gulux';
import { ContainerInject } from './utils/container';

@Middleware()
export class ContainerMiddleware extends GuluXMiddleware {
  async use(@UseContainer() container: ExecutionContainer, @Next() next: NextFunction): Promise<void> {
    const containerInject = container.get(ContainerInject);
    containerInject.setContainer(container);
    await next();
  }
}
