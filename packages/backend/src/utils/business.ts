import {
  Container,
  Context,
  ExecutionContainer,
  GuluXApplication,
  GuluXInjectEnum,
  Identifier,
  Inject,
  Injectable,
  ParamDecoratorType,
  ScopeEnum,
  TRIGGER_CTX,
} from '@gulux/gulux';
import { asyncLocalStorage } from '@gulux/plugin-async-local-storage';
import { AppSetting, AppSettingSymbol } from '@pa/shared/dist/src/appSettings/appSettings';
import { useInject } from '@edenx/plugin-gulux/runtime';
import { GuluXSession } from '@gulux/gulux/session';
import { AnyParamConstructor, ModelType } from '@byted/typegoose/lib/types';
import { FilterQuery } from '@byted/bytedmongoose';
import { BytedLogger } from '@gulux/gulux/byted-logger';

export function useBusinessInject<T>(id: Identifier<T>): T | undefined {
  const container = asyncLocalStorage.getStore();
  if (!container || !(container instanceof ExecutionContainer)) {
    /* istanbul ignore next */
    throw new Error('can`t get container from async local storage');
  }
  try {
    return container.get(id);
  } catch (e) {
    container.get(BytedLogger).warn('useBusinessInject', e);
    return undefined;
  }
}

export function useNewExecutionContainer(appSetting: AppSetting): Container {
  const container = asyncLocalStorage.getStore();
  if (!container || !(container instanceof ExecutionContainer)) {
    /* istanbul ignore next */
    throw new Error('can`t get container from async local storage');
  }
  // container.set({ id: AppSettingSymbol, value: appSetting });
  const app = useInject<GuluXApplication>(GuluXInjectEnum.Application);
  const ctx: Context = container.get(TRIGGER_CTX);
  const newContainer = new ExecutionContainer(ctx, app.container);
  ctx.container = newContainer;
  newContainer.set({ id: ExecutionContainer, value: newContainer });
  newContainer.set({ id: ParamDecoratorType.REQ, value: ctx.input.req });
  newContainer.set({ id: ParamDecoratorType.RES, value: ctx.input.res });
  newContainer.set({ id: ParamDecoratorType.HEADER, value: ctx.input.req.header });
  newContainer.set({ id: ParamDecoratorType.BODY, value: ctx.input.req.body });
  newContainer.set({ id: TRIGGER_CTX, value: ctx });
  newContainer.set({ id: GuluXSession, value: container.get(GuluXSession), scope: ScopeEnum.EXECUTION });
  newContainer.set({ id: AppSettingSymbol, value: appSetting });
  asyncLocalStorage.enterWith(newContainer);
  return newContainer;
}

// eslint-disable-next-line @typescript-eslint/no-explicit-any
function isFilterQuery(obj: any): obj is FilterQuery<any> {
  return obj && typeof obj === 'object' && Object.keys(obj).length > 0;
}

@Injectable()
// eslint-disable-next-line @typescript-eslint/no-explicit-any
export class BusinessModelProxy<T extends AnyParamConstructor<any>> {
  @Inject(AppSettingSymbol)
  private readonly appSetting: AppSetting;

  get(t: T): ModelType<T> {
    const model = useBusinessInject<ModelType<T>>(t)!;
    return new Proxy(model, {
      apply: (target, thisArg, argArray) => {
        for (const arg of argArray) {
          if (isFilterQuery(arg)) {
            arg.appId = this.appSetting.businessInfo.app_id;
          }
        }
        return Reflect.apply(target, thisArg, argArray);
      },
    });
  }
}
