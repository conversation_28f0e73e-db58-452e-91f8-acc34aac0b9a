import { RedisClient } from '@gulux/plugin-redis/lib';
import { useBusinessInject } from './business';

/**
 * Redis 方法缓存 (特别注意幂等方法才能使用)
 * @param redis RedisClient
 * @param funcKey 方法前缀名，保证全局唯一
 * @param ttlSeconds 缓存过期时间，秒为单位
 * @constructor
 */
export function RedisMethodCache(funcKey: string, ttlSeconds: number) {
  return function (target: unknown, propertyKey: string, descriptor: PropertyDescriptor) {
    const originalMethod = descriptor.value;

    descriptor.value = async function (...args: unknown[]) {
      const redis = useBusinessInject(RedisClient);
      const key = `${funcKey}:${JSON.stringify(args)}`;
      const cachedResult = await redis?.get(key);

      if (cachedResult) {
        console.log(`Cache hit for key: ${key}`);
        return JSON.parse(cachedResult);
      }

      const result = await originalMethod.apply(this, args);
      await redis?.set(key, JSON.stringify(result), 'EX', ttlSeconds);

      return result;
    };

    return descriptor;
  };
}
