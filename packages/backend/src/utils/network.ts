import fetch from 'node-fetch';
import { getAsUriParameters } from './common';

export type NetworkResponse<T> = T & {
  [K in Exclude<'code' | 'data' | 'logid', keyof T>]?: K extends 'code' ? number : unknown;
};

export class NetworkX {
  private readonly baseUrl: string;
  private readonly headers: Record<string, string> = {};

  constructor(baseUrl: string, headers: Record<string, string> = {}) {
    this.baseUrl = baseUrl;
    if (!Object.prototype.hasOwnProperty.call(headers, 'Content-Type')) {
      headers['Content-Type'] = 'application/json';
    }
    this.headers = headers;
  }

  async post<T>(path: string, body?: Record<string, unknown>, params?: Record<string, string | number | boolean>) {
    return this.request<T>('POST', path, body, params);
  }

  async postForFlow<T>(
    path: string,
    body?: Record<string, unknown>,
    params?: Record<string, string | number | boolean>,
  ) {
    return this.requestFlow<T>('POST', path, body, params);
  }

  async get<T>(path: string, params?: Record<string, string | string[]>) {
    return this.request<T>('GET', path, undefined);
  }

  async put<T>(path: string, body?: Record<string, unknown>, params?: Record<string, string | number | boolean>) {
    return this.request<T>('PUT', path, body, params);
  }

  async delete<T>(path: string, body?: Record<string, unknown>, params?: Record<string, string | number | boolean>) {
    return this.request<T>('DELETE', path, body, params);
  }

  async patch<T>(path: string, body?: Record<string, unknown>, params?: Record<string, string | number | boolean>) {
    return this.request<T>('PATCH', path, body, params);
  }

  async head(path: string, params?: Record<string, string | number | boolean>) {
    return this.request<undefined>('HEAD', path, undefined, params);
  }

  private async request<T>(
    method: string,
    path: string,
    body?: Record<string, unknown>,
    params?: Record<string, string | number | boolean>,
  ): Promise<NetworkResponse<T>> {
    const full_path = params ? `${path}?${getAsUriParameters(params)}` : path;
    const url = !path.startsWith('https://') && !path.startsWith('http://') ? `${this.baseUrl}${full_path}` : full_path;
    const result = await fetch(url, {
      headers: this.headers,
      method,
      body: body ? JSON.stringify(body) : undefined,
    });
    if (result.status !== 200) {
      let data;
      try {
        data = await result.json();
      } catch (e) {}
      return {
        code: result.status,
        data,
        logid: result.headers.get('x-tt-logid') || '',
      } as unknown as NetworkResponse<T>;
    }
    if (method === 'HEAD') {
      // Handle HEAD request response here
      return {
        code: result.status,
        data: null, // Since HEAD method doesn't return a body
      } as unknown as NetworkResponse<T>;
    }
    return (await result.json()) as NetworkResponse<T>;
  }

  private async requestFlow<T>(
    method: string,
    path: string,
    body?: Record<string, unknown>,
    params?: Record<string, string | number | boolean>,
  ): Promise<NetworkResponse<T[]>> {
    const full_path = params ? `${path}?${getAsUriParameters(params)}` : path;
    const url = !path.startsWith('https://') && !path.startsWith('http://') ? `${this.baseUrl}${full_path}` : full_path;
    const result = await fetch(url, {
      headers: this.headers,
      method,
      body: body ? JSON.stringify(body) : undefined,
    });
    if (result.status !== 200) {
      let data;
      try {
        data = await result.json();
      } catch (e) {}
      return {
        code: result.status,
        data,
        logid: result.headers.get('x-tt-logid') || '',
      } as unknown as NetworkResponse<T[]>;
    }
    if (method === 'HEAD') {
      // Handle HEAD request response here
      return {
        code: result.status,
        data: null, // Since HEAD method doesn't return a body
      } as unknown as NetworkResponse<T[]>;
    }
    const dataStringArray: string[] = [];
    await result.buffer().then(buffer => {
      const bufferString = buffer.toString();
      dataStringArray.push(
        ...bufferString
          .split('\n')
          .filter(it => it.includes('data:'))
          .map(it => it.slice(5)),
      );
    });
    const resultArray: T[] = [];
    dataStringArray.forEach(it => {
      try {
        resultArray.push(JSON.parse(it) as T);
      } catch (e) {}
    });
    return {
      code: result.status,
      data: resultArray,
    } as NetworkResponse<T[]>;
  }
}
