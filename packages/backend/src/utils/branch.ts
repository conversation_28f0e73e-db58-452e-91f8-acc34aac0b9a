export function isDevelopBranch(branch?: string) {
  return branch === 'develop' || branch === 'rc/develop';
}

export const CRITICAL_BRANCH_REX =
  /(^release\/+[0-9.]+$)|(^overseas\/release\/+[0-9.]+$)|(^develop+$)|(^rc\/develop+$)/;

export function isCriticalBranch(branch?: string) {
  if (!branch) {
    return false;
  }
  return CRITICAL_BRANCH_REX.test(branch);
}

export function isLvWorkBranch(branch?: string) {
  if (!branch) {
    return false;
  }
  return /(^release\/+[0-9.]+$)|(^overseas\/release\/+[0-9.]+$)|(^rc\/develop+$)|(^ttp\/rc\/develop+$)/.test(branch);
}

export const DREAM_CRITICAL_BRANCH_REX = /(^dreamina\/release\/+[0-9.]+$)|(^dreamina\/develop+$)/;

// 是否是即梦的主分支
export function isDreaminaMainBranch(branch?: string) {
  if (!branch) {
    return false;
  }
  return DREAM_CRITICAL_BRANCH_REX.test(branch);
}
