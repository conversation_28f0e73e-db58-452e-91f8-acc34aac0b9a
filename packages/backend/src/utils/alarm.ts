import { Inject, Injectable } from '@gulux/gulux';
import { BytedLogger } from '@gulux/gulux/byted-logger';
import { useReq } from '@edenx/plugin-gulux/runtime';

@Injectable({ scopeEscape: true })
export class PaAlarmService {
  @Inject()
  private logger: BytedLogger;

  reportReqError(e: Error, req = useReq()) {
    const logId = req.header['x-tt-logid'];
    this.reportError(
      `${req.path} ${e.stack ?? `未知调用栈，错误信息如下：${e}`} [req]${JSON.stringify(req.body)} [logid]${logId} `,
    );
  }

  reportError(message: string) {
    this.logger.error(`[PaAlarm] ${message}`);
  }
}
