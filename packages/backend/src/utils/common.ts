import { Temporal } from '@js-temporal/polyfill';
import TimeZone = Temporal.TimeZone;

export const IS_TEST = false;

export const DEFAULT_TIMEZONE = TimeZone.from('Asia/Shanghai');

// 纸飞机测试日志群
export const LOG_CHAT_ID = 'oc_421725f66bd9b296fff97e71a9e50466';
// 纸飞机告警大群
export const ALARM_CHAT_ID = 'oc_3334135fbac77d2944d6bbef4158365f';
// yimu的消息测试
export const TEST_CHAT_ID = 'oc_134c4963c8629440f95b9cdb50543a1e';

export function getAsUriParameters(data: Record<string, string | number | boolean>): string {
  return Object.keys(data)
    .map(k => `${encodeURIComponent(k)}=${encodeURIComponent(data[k])}`)
    .join('&');
}
