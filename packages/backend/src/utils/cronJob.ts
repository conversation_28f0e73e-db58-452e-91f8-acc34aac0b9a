import { useInject, useReq } from '@edenx/plugin-gulux/runtime';
import { UserIdType } from '@pa/shared/dist/src/lark/userInfo';
import { Card, CardElement, CardTemplate, LarkCardTemplate } from '@pa/shared/dist/src/lark/larkCard';
import { BytedLogger } from '@gulux/gulux/byted-logger';
import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import * as cron from 'cron-parser';
import { TimerCloudEvent } from '@gulux/gulux/byted-cloudevent';
import LarkService from '../third/lark';
import { current_region, Region } from './region';
import { ALARM_CHAT_ID, LOG_CHAT_ID } from './common';
import { AppSetting, AppSettingId } from '@pa/shared/dist/src/appSettings/appSettings';
import { AppSettingService } from '../service/appSettingService';
import { useNewExecutionContainer } from './business';
import { Container } from '@gulux/gulux';

const FIELD_JOBS = 'cronJobs';
const FIELD_BUSINESS_JOBS = 'businessCronJobs';

interface JobInfo {
  func: () => Promise<unknown>;
  expression: string;
  jobName: string;
  owners: string[];
  needInform: boolean;
  regions: Region[];
}

interface BusinessJobInfo extends JobInfo {
  appSettingId: AppSettingId[];
}

/**
 * 定时任务方法注解
 * @param expression cron表达式
 * @param jobName 任务名称
 * @param owners 关注人
 * @param needInform 是否需要通知
 * @param regions 生效区域
 */
export function CronJob(
  expression: string,
  jobName: string,
  owners: string[],
  needInform = false,
  regions = [Region.LOCAL, Region.CN, Region.SG],
): MethodDecorator {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  return function (target: any, propertyKey: string | symbol, descriptor: PropertyDescriptor): void {
    if (!Reflect.hasOwnMetadata(FIELD_JOBS, target.constructor)) {
      Reflect.defineMetadata(FIELD_JOBS, [], target.constructor);
    }
    const cronJobs = Reflect.getOwnMetadata(FIELD_JOBS, target.constructor) as JobInfo[];
    cronJobs.push({ func: target[propertyKey], expression, jobName, owners, needInform, regions });
    Reflect.defineMetadata(FIELD_JOBS, cronJobs, target.constructor);
  };
}

export function BusinessCronJob(
  expression: string,
  jobName: string,
  owners: string[],
  appSettingId: AppSettingId[],
  needInform = false,
): MethodDecorator {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  return function (target: any, propertyKey: string | symbol, descriptor: PropertyDescriptor): void {
    if (!Reflect.hasOwnMetadata(FIELD_BUSINESS_JOBS, target.constructor)) {
      Reflect.defineMetadata(FIELD_BUSINESS_JOBS, [], target.constructor);
    }
    const cronJobs = Reflect.getOwnMetadata(FIELD_BUSINESS_JOBS, target.constructor);
    cronJobs.push({ func: target[propertyKey], expression, jobName, owners, appSettingId, needInform });
    Reflect.defineMetadata(FIELD_BUSINESS_JOBS, cronJobs, target.constructor);
  };
}

// eslint-disable-next-line @typescript-eslint/no-explicit-any
function parseTargetBusinessCronJobs(target: any): BusinessJobInfo[] {
  return Reflect.getOwnMetadata(FIELD_BUSINESS_JOBS, target.constructor) || [];
}

/**
 * 从target中解析所有@CronJob注解的定时方法
 * @param target
 */
// eslint-disable-next-line @typescript-eslint/no-explicit-any
function parseTargetCronJobs(target: any): JobInfo[] {
  return Reflect.getOwnMetadata(FIELD_JOBS, target.constructor) || [];
}

// eslint-disable-next-line @typescript-eslint/no-explicit-any
async function invokeFunc(target: any, job: JobInfo) {
  if (!job.regions.includes(current_region())) {
    return;
  }
  const start_date = Date.now();
  const logger = useInject(BytedLogger);
  const lark = useInject(LarkService);
  const prefix = `[${target.constructor.name}] -> [${job.jobName}]`;
  logger.info(`${prefix} 定时任务开始`);
  const build_card = (id: string, colour: CardTemplate, content: string) =>
    ({
      elements: [
        {
          tag: 'markdown',
          content,
        } as CardElement,
      ],
      header: {
        template: colour,
        title: {
          content: `[${Region[current_region()]} | ${id}] 纸飞机定时任务流程通知`,
          tag: 'plain_text',
        },
      },
    }) as Card;

  const start_report = async (id: string, offset = 30) => {
    dayjs.extend(utc);
    const now_str = dayjs().utcOffset(8).subtract(offset, 'second').format('YYYY-MM-DD HH:mm:ss');
    await lark.sendCardMessage(
      UserIdType.chatId,
      LOG_CHAT_ID,
      build_card(id, CardTemplate.grey, `纸飞机定时任务 ${id} 已经在 ${now_str} 开始，请关注进展。`),
    );
  };

  const success_report = async (id: string, start_time: number) => {
    dayjs.extend(utc);
    const now = dayjs().utcOffset(8);
    const now_str = now.format('YYYY-MM-DD HH:mm:ss');

    const time_diff = Math.floor((now.valueOf() - start_time) / 1000);
    const min = Math.floor(time_diff / 60);
    const sec = time_diff % 60;

    await lark.sendCardMessage(
      UserIdType.chatId,
      LOG_CHAT_ID,
      build_card(id, CardTemplate.green, `纸飞机定时任务 ${id} 已经在 ${now_str} 顺利完成，耗时 ${min} 分 ${sec} 秒。`),
    );
  };
  if (job.needInform) {
    await start_report(job.jobName);
  }
  return await job.func
    // 注入target
    .bind(target)()
    .then(
      async result => {
        logger.info(`${prefix} 定时任务执行成功，耗时：%dms`, Date.now() - start_date);
        if (job.needInform) {
          await success_report(job.jobName, start_date);
        }
        return result;
      },
      async err => {
        logger.error(`${prefix} 定时任务执行失败，耗时：%dms，错误信息如下：${err}`, Date.now() - start_date);
        const logId = useReq().header['x-tt-logid'];
        await useInject(LarkService).sendCardMessage(
          UserIdType.chatId,
          ALARM_CHAT_ID,
          new LarkCardTemplate('ctp_AAVCtuV2PjZD', {
            id: `【定时任务-${job.jobName}】`,
            content: `任务负责人:${job.owners.map(v => `<at email="${v}@bytedance.com"></at>`).join('')} \nlogid:${logId} \n${err.stack ?? `未知调用栈，错误信息如下：\n${err}`}`,
          }),
        );
        return undefined;
      },
    );
}

// eslint-disable-next-line @typescript-eslint/no-explicit-any
async function invokeBusinessFunc<T>(target: any, job: BusinessJobInfo) {
  const start_date = Date.now();
  const logger = useInject(BytedLogger);
  const lark = useInject(LarkService);
  const prefix = `[${target.constructor.name}] -> [${job.jobName}]`;
  logger.info(`${prefix} 定时任务开始`);
  const build_card = (id: string, colour: CardTemplate, content: string) =>
    ({
      elements: [
        {
          tag: 'markdown',
          content,
        } as CardElement,
      ],
      header: {
        template: colour,
        title: {
          content: `[${Region[current_region()]} | ${id}] 纸飞机定时任务流程通知`,
          tag: 'plain_text',
        },
      },
    }) as Card;

  const start_report = async (id: string, offset = 30) => {
    dayjs.extend(utc);
    const now_str = dayjs().utcOffset(8).subtract(offset, 'second').format('YYYY-MM-DD HH:mm:ss');
    await lark.sendCardMessage(
      UserIdType.chatId,
      LOG_CHAT_ID,
      build_card(id, CardTemplate.grey, `纸飞机定时任务 ${id} 已经在 ${now_str} 开始，请关注进展。`),
    );
  };

  const success_report = async (id: string, start_time: number) => {
    dayjs.extend(utc);
    const now = dayjs().utcOffset(8);
    const now_str = now.format('YYYY-MM-DD HH:mm:ss');

    const time_diff = Math.floor((now.valueOf() - start_time) / 1000);
    const min = Math.floor(time_diff / 60);
    const sec = time_diff % 60;

    await lark.sendCardMessage(
      UserIdType.chatId,
      LOG_CHAT_ID,
      build_card(id, CardTemplate.green, `纸飞机定时任务 ${id} 已经在 ${now_str} 顺利完成，耗时 ${min} 分 ${sec} 秒。`),
    );
  };
  if (job.needInform) {
    await start_report(job.jobName);
  }
  const appSettingService: AppSettingService = useInject(AppSettingService);
  const appSettingList = await appSettingService.queryAppSettingList();
  const handleAppSettingList = new Array<AppSetting>();
  if (job.appSettingId.length === 1 && job.appSettingId[0] === AppSettingId.ALL) {
    handleAppSettingList.push(...appSettingList);
  } else {
    const filterAppSettingList = appSettingList.filter(value => job.appSettingId.indexOf(value.id) !== -1);
    handleAppSettingList.push(...filterAppSettingList);
  }
  for (const appSetting of handleAppSettingList) {
    const container: Container = useNewExecutionContainer(appSetting);
    const appSettingTarget = container.get(target.constructor);
    try {
      await job.func.bind(appSettingTarget)();
      logger.info(`${prefix} 定时任务执行成功，耗时：%dms`, Date.now() - start_date);
      if (job.needInform) {
        await success_report(job.jobName, start_date);
      }
    } catch (err) {
      logger.error(`${prefix} 定时任务执行失败，耗时：${Date.now() - start_date}，错误信息如下:`, err);
    }
  }
  // const resultPromise = handleAppSettingList.map(
  //   (appSetting: AppSetting) =>
  //     new Promise(async resolve => {
  //       const appSettingTarget = container.get(target.constructor);
  //       try {
  //         const result = await job.func.bind(appSettingTarget)();
  //         logger.info(`${prefix} 定时任务执行成功，耗时：%dms`, Date.now() - start_date);
  //         if (job.needInform) {
  //           await success_report(job.jobName, start_date);
  //         }
  //         resolve(result);
  //       } catch (err) {
  //         logger.error(`${prefix} 定时任务执行失败，耗时：${Date.now() - start_date}，错误信息如下:`, err);
  //         // const logId = useReq().header['x-tt-logid'];
  //         // await useInject(LarkService).sendCardMessage(
  //         //   UserIdType.chatId,
  //         //   ALARM_CHAT_ID,
  //         //   new LarkCardTemplate('ctp_AAVCtuV2PjZD', {
  //         //     id: `【定时任务-${job.jobName}】`,
  //         //     content: `任务负责人:${job.owners.map(v => `<at email="${v}@bytedance.com"></at>`).join('')} \nlogid:${logId} \n${err.stack ?? `未知调用栈，错误信息如下：\n${err}`}`,
  //         //   }),
  //         // );
  //       }
  //     }),
  // );
  // return Promise.all(resultPromise);
}

/**
 * 手动触发
 * @param handlerName 方法名
 */
export async function handleManualTrigger(target: unknown, handlerName: string) {
  const jobs = parseTargetCronJobs(target);
  const businessJobs = parseTargetBusinessCronJobs(target);
  const job = jobs.find((v: JobInfo) => v.func.name === handlerName);
  if (job) {
    await invokeFunc(target, job);
    return `触发成功[${handlerName}]`;
  } else {
    const businessJob = businessJobs.find((v: BusinessJobInfo) => v.func.name === handlerName);
    if (businessJob) {
      await invokeBusinessFunc(target, businessJob);
      return `触发成功[${handlerName}]`;
    }
    return `未找到定时任务[${handlerName}]`;
  }
}

/**
 * 定时任务触发
 * @param target
 * @param event
 */
export async function handleTimerTrigger(target: unknown, event?: TimerCloudEvent) {
  const jobs = parseTargetCronJobs(target);
  const businessJobs = parseTargetBusinessCronJobs(target);
  await Promise.all(
    [...jobs, ...businessJobs].map((job: JobInfo | BusinessJobInfo) => {
      dayjs.extend(utc);
      const now = dayjs().utcOffset(8);
      const cron_result = cron.parseExpression(job.expression, {
        tz: 'Asia/Shanghai',
        currentDate: now.format('YYYY-MM-DD HH:mm:01'),
      });
      const cur = now.startOf('minute').valueOf();
      const prev_trigger = cron_result.prev().toDate().valueOf();
      if (prev_trigger !== cur) {
        return undefined;
      }
      if ((job as BusinessJobInfo).appSettingId) {
        return invokeBusinessFunc(target, job as BusinessJobInfo);
      } else {
        return invokeFunc(target, job);
      }
    }),
  );
}
