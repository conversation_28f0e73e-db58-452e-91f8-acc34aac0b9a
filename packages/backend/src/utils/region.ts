export enum Region {
  UNKNOWN,
  LOCAL, // 本地调试
  CN, // 国内
  SG, // 海外(ROW)
}

export function current_region(): Region {
  if (!process.env.RUNTIME_IDC_NAME || process.env.RUNTIME_IDC_NAME === 'boe') {
    console.log(`当前环境 ${Region.LOCAL} 系统变量 ${process.env.RUNTIME_IDC_NAME}`)
    return Region.LOCAL;
  }
  switch (process.env.RUNTIME_IDC_NAME) {
    case 'lf':
    case 'hl':
    case 'lq':
      return Region.CN;
    case 'sg1':
    case 'my':
    // 以下为NonTT集群
    case 'mya':
    case 'useast9a':
      return Region.SG;
    default:
      return Region.UNKNOWN;
  }
}

export function isSG() {
  return current_region() === Region.SG;
}

export function isCN() {
  return current_region() === Region.CN;
}

export function isLocal() {
  return current_region() === Region.LOCAL;
}

export enum ProjectType {
  Host,
  Quality,
}

let _currentProject: ProjectType = ProjectType.Host;

export function currentProject() {
  return _currentProject;
}

export function initPaConfig(project: ProjectType) {
  _currentProject = project;
}

export function isHost() {
  return currentProject() === ProjectType.Host;
}
