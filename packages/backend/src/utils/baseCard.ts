import {
  Card,
  CardButtonAction,
  CardButtonType,
  CardContentElement,
  CardElementTag,
  CardHeader,
  CardInfo,
  CardMarkdownElement,
  CardSchema,
  CardTemplate,
  CardTextTag,
  CardV2,
  TitleTag,
} from '@pa/shared/dist/src/lark/larkCard';

export default class BaseCardUtils {
  static buildBaseCard(info: CardInfo): Card {
    return {
      config: {},
      header: this.buildHeader(info.title, info.template),
      elements: [],
    };
  }

  static buildBaseCardV2(info: CardInfo): CardV2 {
    return {
      schema: CardSchema.v2, // 卡片 JSON 结构的版本。默认为 1.0。要使用 JSON 2.0 结构，必须显示声明 2.0。
      config: {},
      header: this.buildHeader(info.title, info.template),
      body: {
        elements: [],
      },
    };
  }

  static buildHeader(title: string, template: CardTemplate): CardHeader {
    return {
      title: {
        content: title,
        tag: TitleTag.plain_text,
      },
      template,
    };
  }

  static buildMarkDownColorText(content: string, color: 'red' | 'green' | 'yellow' | 'orange'): string {
    return `<font color='${color}'>${content}</font>`;
  }

  static buildMarkDownATText(email: string): string {
    return `<at email="${email}"></at>`;
  }

  static buildMarkDownTag(content: string, color: 'red' | 'green' | 'yellow' | 'orange' | 'blue' | 'purple'): string {
    return `<text_tag color='${color}'>${content}</text_tag>`;
  }

  static buildMarkDownCard(title: string, template: CardTemplate | undefined, content: string): Card {
    const baseCard = this.buildBaseCard({
      title,
      template: template ?? CardTemplate.orange,
    });
    baseCard.elements.push({
      tag: CardElementTag.markdown,
      content,
    } as CardMarkdownElement);
    return baseCard;
  }

  static buildMrkDownCard(title: string, content: string): Card {
    const baseCard = this.buildBaseCard({
      title,
      template: CardTemplate.orange,
    });
    baseCard.elements.push({
      tag: CardElementTag.markdown,
      content,
    } as CardMarkdownElement);
    return baseCard;
  }

  static buildSimpleActionCard(text: string, url?: string, actionText?: string) {
    const baseCard = {
      config: {
        wide_screen_mode: true,
      },
      elements: [],
    } as Card;
    baseCard.elements.push({
      tag: CardElementTag.div,
      text: {
        tag: CardTextTag.lark_md,
        content: text,
      },
      extra:
        url && actionText
          ? ({
              tag: CardElementTag.button,
              text: {
                tag: CardTextTag.plain_text,
                content: `${actionText}`,
              },
              type: CardButtonType.default,
              url,
            } as CardButtonAction)
          : undefined,
    } as CardContentElement);
    return baseCard;
  }
}
