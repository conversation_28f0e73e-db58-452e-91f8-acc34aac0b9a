import { Database, getModelForClass, modelOptions, Prop, Severity } from '@gulux/gulux/typegoose';
import { MsgCategory, MsgStrategy, MsgTemplateExtraData, MsgType, SendMsgRecord } from '@pa/shared/dist/src/message';
import { LarkCard } from '@pa/shared/dist/src/lark/larkCard';
import { Injectable } from '@gulux/gulux';
import { FilterQuery } from '@byted/bytedmongoose';

@Database('main')
@modelOptions({ options: { allowMixed: Severity.ALLOW } })
export class MsgRecordTable implements SendMsgRecord {
  @Prop()
  name: string;
  @Prop()
  type: MsgType;
  @Prop()
  category: MsgCategory;
  @Prop()
  subCategory: string;
  @Prop()
  strategy: MsgStrategy;
  @Prop()
  subscribable: boolean;
  @Prop()
  msgContent: LarkCard | string;
  @Prop()
  createTs: number;
  @Prop()
  receiverList: string[];
  @Prop()
  messageId: string;
  @Prop()
  hasRead: number;
  @Prop()
  extra?: MsgTemplateExtraData;
}

@Injectable()
export class MsgRecordModel {
  private model = getModelForClass(MsgRecordTable);

  async save(...item: SendMsgRecord[]): Promise<SendMsgRecord[]> {
    return await this.model.create(item);
  }

  async genericFind(
    query: FilterQuery<MsgRecordTable>,
    skip: number,
    limit: number,
    sort?: Partial<Record<keyof SendMsgRecord, number>>,
  ) {
    return {
      success: true,
      total: await this.model.countDocuments(query),
      data: (await this.model.find(query).skip(skip).limit(limit).sort(sort).exec()) as SendMsgRecord[],
    };
  }

  async deleteMany(query: FilterQuery<MsgRecordTable>): Promise<number | undefined> {
    return (await this.model.deleteMany(query)).deletedCount;
  }

  async queryById(id: string): Promise<SendMsgRecord | null> {
    return this.model
      .findOne({
        _id: id,
      })
      .exec();
  }

  async markRead(id: string): Promise<SendMsgRecord | null> {
    return this.model
      .findOneAndUpdate(
        {
          messageId: id,
        },
        {
          hasRead: 1,
        },
        {
          new: true,
        },
      )
      .exec();
  }
}
