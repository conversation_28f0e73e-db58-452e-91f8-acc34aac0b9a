import {
  AppSetting,
  AppUserSetting,
  BusinessAppInfo,
  BusinessType,
  FeatureInfo,
} from '@pa/shared/dist/src/appSettings/appSettings';
import { Database, getModelForClass, modelOptions, Prop, Severity } from '@gulux/gulux/typegoose';
import { PlatformType } from '@pa/shared/dist/src/core';

@Database('main')
@modelOptions({ options: { customName: 'AppSettingTable', allowMixed: Severity.ALLOW } })
export class AppSettingTable implements AppSetting {
  @Prop()
  id: number;
  @Prop()
  name: string;
  @Prop()
  platform: PlatformType;
  @Prop()
  productType: string;
  @Prop()
  businessID: BusinessType;
  @Prop()
  time: number;
  @Prop()
  icon: string;
  @Prop()
  businessInfo: BusinessAppInfo;
  @Prop()
  featureList?: FeatureInfo[] | undefined;
}

export const BusinessSettingModel = getModelForClass(AppSettingTable);
