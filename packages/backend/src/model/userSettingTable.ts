import { AppUserSetting } from '@pa/shared/dist/src/appSettings/appSettings';
import { Database, getModelForClass, modelOptions, Prop, Severity } from '@gulux/gulux/typegoose';

@Database('main')
@modelOptions({ options: { customName: 'UserSettingTable', allowMixed: Severity.ALLOW } })
export class UserSettingTable implements AppUserSetting {
  @Prop()
  email: string;
  @Prop()
  selectApp: number;
  @Prop()
  selectVersion: string;
}

export const UserSettingModel = getModelForClass(UserSettingTable);
