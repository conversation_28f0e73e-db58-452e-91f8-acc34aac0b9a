// plugin.default.ts

export default {
  // GuluX 插件
  // ---------------
  'byted-logger': {
    enable: true,
  },
  'application-http': {
    enable: true,
  },
  // 需要注册 edenx 的 plugin
  edenx: {
    enable: true,
    package: '@edenx/plugin-gulux/plugin',
  },
  session: {
    enable: true,
  },
  'http-client': {
    enable: true,
  },
  redis: {
    enable: true,
  },
  lark: {
    enable: true,
  },
  'async-local-storage': {
    enable: true,
    package: '@gulux/plugin-async-local-storage',
  },
  typegoose: {
    enable: true,
  },
};
