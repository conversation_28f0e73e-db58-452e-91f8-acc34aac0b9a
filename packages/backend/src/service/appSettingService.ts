import { Inject, Injectable } from '@gulux/gulux';
import { UserSettingTable } from '../model/userSettingTable';
import { ModelType } from '@byted/typegoose/lib/types';
import { AppSetting, AppSettingId, AppUserSetting } from '@pa/shared/dist/src/appSettings/appSettings';
import BusinessConfigService from './businessConfig';
import { AppSettingTable } from '../model/appSettingTable';
import { BytedLogger } from '@gulux/gulux/byted-logger';

@Injectable()
export class AppSettingService {
  @Inject(UserSettingTable)
  private userSettingModel: ModelType<UserSettingTable>;
  @Inject(AppSettingTable)
  private appSettingModel: ModelType<AppSettingTable>;
  @Inject()
  private businessConfigService: BusinessConfigService;
  @Inject()
  private logger: BytedLogger;

  async queryDefaultAppSettingList(): Promise<AppSetting[]> {
    const appInfoList = await this.businessConfigService.getAppList();
    const appList: AppSetting[] = [];
    for (const appInfo of appInfoList) {
      appList.push({
        id: appInfo.app_id,
        name: appInfo.app_name,
        platform: appInfo.platform,
        businessID: appInfo.business_id,
        productType: appInfo.product_type,
        icon: appInfo.icon,
        businessInfo: appInfo,
      } as AppSetting);
    }
    return appList;
  }

  async queryAppSettingList(): Promise<AppSetting[]> {
    const appInfoList = await this.queryDefaultAppSettingList();
    const resultPromise = appInfoList.map(appInfo => this.queryAppSetting(appInfo.id));
    const appSettingList = await Promise.all(resultPromise);
    return appSettingList.filter(appSetting => appSetting !== null);
  }

  async updateAppSetting(id: AppSettingId, appSetting: AppSetting): Promise<void> {
    const query = { id };
    const update = { ...appSetting };
    this.logger.info('AppSettingService', 'updateAppSetting', query, update);
    this.appSettingModel.findOneAndUpdate(query, update, { new: true, upsert: true }, (err, doc) => {
      if (err) {
        this.logger.error('AppSettingService', 'updateAppSetting', 'callback', err);
      } else {
        this.logger.info('AppSettingService', 'updateAppSetting', 'callback', doc);
      }
    });
  }

  async queryAppSetting(id: AppSettingId): Promise<AppSetting | null> {
    const appInfoList = await this.queryDefaultAppSettingList();
    const defaultAppSetting = appInfoList.find(value => value.id === id);
    const currentAppSetting = await this.appSettingModel.where({ id }).findOne().exec();
    if (defaultAppSetting && currentAppSetting) {
      // 两者都存在的情况下，进行组合，以 currentAppSetting 为先
      this.logger.info('AppSettingService', 'queryAppSetting', defaultAppSetting, currentAppSetting);
      const result = Object.assign({}, defaultAppSetting, JSON.parse(JSON.stringify(currentAppSetting)));
      this.logger.info('AppSettingService', 'result', result);
      return result;
    } else if (defaultAppSetting) {
      // 只存在 defaultAppSetting 的情况下，直接返回 defaultAppSetting
      this.logger.info('AppSettingService', 'queryAppSetting', 'defaultAppSetting', defaultAppSetting);
      return defaultAppSetting;
    } else if (currentAppSetting) {
      // 只存在 currentAppSetting 的情况下，直接返回 currentAppSetting
      this.logger.info('AppSettingService', 'queryAppSetting', 'defaultAppSetting', currentAppSetting);
      return currentAppSetting;
    } else {
      return null;
    }
  }

  async updateAppUserSetting(appUserSetting: AppUserSetting) {
    const query = { email: appUserSetting.email };
    const update = { ...appUserSetting };
    await this.userSettingModel.findOneAndUpdate(query, update, { new: true, upsert: true });
  }

  async queryAppUserSetting(email: string): Promise<AppUserSetting | null> {
    return this.userSettingModel.where({ email }).findOne().exec();
  }
}
