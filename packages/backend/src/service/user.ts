import { Inject, Injectable } from '@gulux/gulux';
import { GuluXSession } from '@gulux/gulux/session';
import { LoginInfo } from '@pa/shared/dist/src/oauthInfo';
import { useInject } from '@edenx/plugin-gulux/runtime';
import LarkService from '../third/lark';

@Injectable()
export default class UserService {
  @Inject()
  private session: GuluXSession;

  async queryLoginEmail(): Promise<string> {
    const user = this.session.get<LoginInfo>('user');
    if (user && user.email) {
      return user.email;
    } else if (user) {
      return await useInject(LarkService).getUserEmail(user.open_id);
    }
    return '';
  }

  async queryLoginName(): Promise<string> {
    const email = await this.queryLoginEmail();
    return email.substring(0, email.indexOf('@'));
  }

  async queryLoginUserOpenId(): Promise<string> {
    const user = this.session.get<LoginInfo>('user');

    return await useInject(LarkService).getUserEmail(user.open_id);
  }

  async queryAccessToken(): Promise<string> {
    const userToken = this.session.get<LoginInfo>('oauth2Token');
    return userToken?.access_token ?? '';
  }
}
