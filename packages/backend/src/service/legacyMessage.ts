import { Injectable } from '@gulux/gulux';
import { MsgCategory, MsgStrategy, MsgTemplate, MsgType } from '@pa/shared/dist/src/message';
import { UserIdType } from '@pa/shared/dist/src/lark/userInfo';
import { LarkTemplateId, MessageType } from '@pa/shared/dist/src/lark/larkCard';

type TreeMap = {
  category: MsgCategory;
  subCategories: {
    subCategory: string;
    matchers: (string | LarkTemplateId)[];
  }[];
}[];

@Injectable()
export default class LegacyMessageService {
  private treeMap: TreeMap = [
    {
      category: MsgCategory.Release,
      subCategories: [
        {
          subCategory: '封版',
          matchers: ['封版', '保送', '请尽快合码', '合入提醒', '上车提醒', '需求异常MR', 'Batch', '已达到准入标准'],
        },
        {
          subCategory: '自驱',
          matchers: [
            'Bug解决率',
            'P0/P1 Bug通知',
            '测试验收提醒',
            '新增临时检查项',
            '缺陷提醒',
            '版本更新出现异常',
            '延期预警',
            '人工确认项',
            '准出阻塞项',
            '测试进度通知',
          ],
        },
        {
          subCategory: '核心群同步',
          matchers: ['灰度信息', '版本进度', '版本节奏', '分支已拉出', '提审信息', '延期风险同步', '自动化准出'],
        },
        {
          subCategory: '审批流',
          matchers: ['小版本', '需求插入'],
        },
      ],
    },
    {
      category: MsgCategory.Acceptance,
      subCategories: [
        {
          subCategory: '节点流转',
          matchers: ['节点超时提醒'],
        },
        {
          subCategory: '验收平台',
          matchers: ['验收平台', '验收日报', '多维表格字段变更通知'],
        },
      ],
    },
    {
      category: MsgCategory.Experiment,
      subCategories: [
        {
          subCategory: '实验管控',
          matchers: ['实验操作已被纸飞机管控', '纸飞机已自动放量', LarkTemplateId.SettingNoneWarning],
        },
        {
          subCategory: '实验放量提醒',
          matchers: ['注意观察实验数据提醒', '实验未开始放量提醒'],
        },
        {
          subCategory: '实验巡检',
          matchers: ['实验巡检'],
        },
      ],
    },
    {
      category: MsgCategory.Quality,
      subCategories: [
        {
          subCategory: 'P0/P1自动push',
          matchers: [
            LarkTemplateId.IssueAutoPush,
            LarkTemplateId.IssueFollowUp,
            LarkTemplateId.IssueRequestReAssign,
            LarkTemplateId.IssueRequestOncall,
            LarkTemplateId.IssueViolentPush,
          ],
        },
        {
          subCategory: '灰度稳定性巡检',
          matchers: ['稳定性问题', 'MemoryGraph', '稳定性与反馈准出'],
        },
        {
          subCategory: '指标告警巡检',
          matchers: ['灰度告警巡检'],
        },
        {
          subCategory: 'MR归因',
          matchers: ['MR归因'],
        },
      ],
    },
    {
      category: MsgCategory.DevOps,
      subCategories: [
        {
          subCategory: '构建',
          matchers: ['构建', '分支编译异常告警'],
        },
        {
          subCategory: '升级SDK',
          matchers: ['升级失败', '升级成功', '升级超时', '次交付'],
        },
        {
          subCategory: '文案更新',
          matchers: ['文案更新', '拉取资源'],
        },
        {
          subCategory: '定时任务',
          matchers: ['定时任务'],
        },
        {
          subCategory: '回流',
          matchers: ['回流'],
        },
        {
          subCategory: 'OnCall',
          matchers: ['OnCall分类', '值班人温馨提示'],
        },
        {
          subCategory: '插件检测',
          matchers: ['插件检测'],
        },
      ],
    },
  ];

  async convert2MsgTemplate(userIdType: UserIdType, id: string, content: string, msgType = MessageType.interactive) {
    const name = this.parseTitleFromContent(content);
    const c = this.parseCategory(name);
    return {
      name,
      type: userIdType === UserIdType.chatId ? MsgType.GroupChat : MsgType.DirectChat,
      category: c[0],
      subCategory: c[1],
      strategy: MsgStrategy.Manual,
      msgContent: content,
      larkType: msgType,
    } as MsgTemplate;
  }

  private parseTitleFromContent(content: string): string {
    try {
      const card = JSON.parse(content);
      return card.header.title.content ?? card.data.template_id;
    } catch (e) {
      return content;
    }
  }

  private parseCategory(name: string): [MsgCategory, string] {
    for (const category of this.treeMap) {
      for (const subCategory of category.subCategories) {
        for (const m of subCategory.matchers) {
          if (m.length > 0 && name.includes(m)) {
            return [category.category, subCategory.subCategory];
          }
        }
      }
    }
    return [MsgCategory.Unknown, '未知二级分类'];
  }
}
