import { useInject } from '@edenx/plugin-gulux/runtime';
import { Inject, Injectable } from '@gulux/gulux';
import * as fs from 'fs';
import { TccClients } from '@gulux/gulux/tcc';
import { BytedEnv } from '@gulux/gulux/byted-env';
import { BusinessAppInfo, BusinessConfig, ProductType } from '@pa/shared/dist/src/appSettings/appSettings';
import { PlatformType } from '@pa/shared/dist/src/core';
const configName = 'businessconfig.json';
interface BusinessConfigFile {
  config_list: BusinessConfig[];
}

function readJsonFile(fileName: string): BusinessConfigFile | null {
  try {
    const data = fs.readFileSync(fileName, 'utf-8');
    return JSON.parse(data);
  } catch (err) {
    console.error('读取 JSON 文件出错:', err);
    return null;
  }
}

@Injectable()
export default class BusinessConfigService {
  @Inject()
  private readonly tccClients!: TccClients;
  private _businessList: BusinessConfig[];
  private _appInfoMap: { [key: number]: BusinessAppInfo };
  private isInitialized: boolean;

  constructor() {
    this.isInitialized = false;
  }

  get appInfoMap(): { [key: number]: BusinessAppInfo } {
    if (!this.isInitialized) {
      throw new Error('Class not initialized. Please call initialize() method first.');
    }
    return this._appInfoMap;
  }

  async getBusinessList() {
    let tcc_config_name = 'business_config';
    const env = useInject(BytedEnv);
    if (env.isPPE()) {
      tcc_config_name = 'business_config_ppe';
    }
    const businessList = JSON.parse(await this.tccClients.keys.get(tcc_config_name)).config_list as BusinessConfig[];
    return businessList;
  }

  async initialize(): Promise<void> {
    if (!this.isInitialized) {
      try {
        const businessList = !process.env.RUNTIME_IDC_NAME
          ? ((await readJsonFile(configName)!.config_list) as BusinessConfig[])
          : await this.getBusinessList();
        this._businessList = businessList;
        this.isInitialized = true; // 标记为已初始化
        this.createAppInfoMap(); // 创建appInfoMap
      } catch (error) {
        console.warn('Tcc request failed:', error);
        this._businessList = readJsonFile(configName)!.config_list as BusinessConfig[];
        if (this._businessList) {
          this.isInitialized = true; // 标记为已初始化
          this.createAppInfoMap(); // 创建appInfoMap
        } else {
          console.error('Initialization failed:', error);
        }
      }
    }
  }

  async getAppList(bussinessID: number | undefined = undefined): Promise<BusinessAppInfo[]> {
    if (!this.isInitialized) {
      await this.initialize();
    }
    if (!bussinessID) {
      // return Object.values(this.appInfoMap);
      return this._businessList.flatMap(v => v.app_list);
    }
    for (const businessConfig of this._businessList) {
      if (businessConfig.business_id === bussinessID) {
        return businessConfig.app_list;
      }
    }
    return [];
  }
  async appID2AppInfo(appID: number): Promise<BusinessAppInfo | undefined> {
    if (!this.isInitialized) {
      await this.initialize();
    }
    return this.appInfoMap[appID];
  }

  async productTypeOS2AppInfo(productType: ProductType, os: PlatformType): Promise<BusinessAppInfo | undefined> {
    if (!this.isInitialized) {
      await this.initialize();
    }
    return Object.values(this.appInfoMap).find(
      appInfo => appInfo.product_type === productType && appInfo.platform === os,
    );
  }

  async getConfigList() {
    if (!this.isInitialized) {
      await this.initialize();
    }
    return this._businessList;
  }

  async getVersionTimelineConfig(appId: number) {
    if (!this.isInitialized) {
      await this.initialize();
    }
    const configKey = (await this.appID2AppInfo(appId))?.version_timeline_config;
    if (!configKey) {
      return undefined;
    }
    try {
      return JSON.parse(await this.tccClients.keys.get(configKey));
    } catch (error) {
      return null;
    }
  }

  private createAppInfoMap(): void {
    this._appInfoMap = {};
    for (const businessConfig of this._businessList) {
      for (const appInfo of businessConfig.app_list) {
        this._appInfoMap[appInfo.app_id] = appInfo;
      }
    }
  }
}
