import { Inject, Injectable } from '@gulux/gulux';
import {
  fillMsgTemplate,
  MsgTemplate,
  MsgType,
  SendMsgRecord,
  SubscribableMsgTemplate,
} from '@pa/shared/dist/src/message';
import { UserIdType } from '@pa/shared/dist/src/lark/userInfo';
import { PaAlarmService } from '../utils/alarm';
import { LarkClient } from '@gulux/gulux/lark';
import { MessageType } from '@pa/shared/dist/src/lark/larkCard';
import { BytedLogger } from '@gulux/gulux/byted-logger';
import { useInject } from '@edenx/plugin-gulux/runtime';
// import LarkService from '../third/lark';
import { MsgRecordModel } from '../model/msgRecordModel';
import dayjs from 'dayjs';
import { FilterQuery } from '@byted/bytedmongoose';
import { NetworkCode } from '@pa/shared/dist/src/core';
import { isString } from 'lodash';
import LegacyMessageService from './legacyMessage';
import { is_json_string } from '@pa/shared/dist/src/utils/tools';
import { isHost } from '../utils/region';
import RpcProxyManager from '../rpc/proxy';

@Injectable()
export default class MessageService {
  @Inject()
  private lark: LarkClient;
  @Inject()
  private logger: BytedLogger;
  @Inject()
  private alarm: PaAlarmService;
  @Inject()
  private model: MsgRecordModel;
  @Inject()
  private legacy: LegacyMessageService;
  @Inject()
  private proxyManager: RpcProxyManager;

  async sendNormalMsg(msg: MsgTemplate, userIdType: UserIdType, targetId: string) {
    if (!isHost()) {
      return this.proxyManager.getHost().sendNormalMsg(msg, userIdType, targetId);
    }
    fillMsgTemplate(msg);
    let actualContentStr: string;
    if (isString(msg.msgContent)) {
      if (!is_json_string(msg.msgContent)) {
        actualContentStr = JSON.stringify({
          text: msg.msgContent,
        });
      } else {
        actualContentStr = msg.msgContent;
      }
    } else {
      actualContentStr = JSON.stringify(msg.msgContent);
    }
    const ret = await this.actualSendLarkMsg(userIdType, targetId, actualContentStr, msg.larkType);
    if (ret.code === NetworkCode.Success) {
      // 消息入库
      const record = msg as SendMsgRecord;
      record.createTs = dayjs().unix();
      record.receiverList = [targetId];
      record.messageId = ret.data.message_id;
      await this.model.save(record);
    }
    return ret;
  }

  async sendSubscribeMsg(msg: SubscribableMsgTemplate) {
    // pass
  }

  /**
   * @deprecated 用于兼容LarkService中的sendMessage消息方法，通过这个接口发送的消息分类都会被标记为Unclassified，不建议使用。
   * 建议将消息迁移到新的消息模板下发送 MessageService.sendMsg
   * @param userIdType
   * @param id
   * @param content
   * @param msgType
   */
  async sendLegacyMsg(userIdType: UserIdType, id: string, content: string, msgType = MessageType.interactive) {
    return this.sendNormalMsg(await this.legacy.convert2MsgTemplate(userIdType, id, content, msgType), userIdType, id);
  }

  async queryMsgRecord(startTs: number, endTs: number) {
    const query: FilterQuery<SendMsgRecord> = {};
    query.createTs = {
      $gte: startTs,
      $lte: endTs,
    };
    const limit = 10000;
    return this.model.genericFind(query, 0, limit, {
      createTs: -1,
    });
  }

  async listMsgRecordByQuery(query: FilterQuery<SendMsgRecord>) {
    return this.model.genericFind(query, 0, 10000, {
      createTs: -1,
    });
  }

  async deleteMsgRecordByQuery(query: FilterQuery<SendMsgRecord>) {
    return this.model.deleteMany(query);
  }

  private async actualSendLarkMsg(
    userIdType: UserIdType,
    id: string,
    content: string,
    msgType = MessageType.interactive,
  ) {
    return this.lark.im.message
      .create({
        params: {
          receive_id_type: userIdType,
        },
        data: {
          receive_id: id,
          content,
          msg_type: msgType,
        },
      })
      .catch(async err => {
        this.logger.error(`[sendMessage]: err ${JSON.stringify(err)}`);
        try {
          const e: Error = err instanceof Error ? err : new Error(JSON.stringify(err));
          if (![230053, 230002].includes(err.code)) {
            // 本地不告警/用户屏蔽机器人不告警/机器人不在群里不告警
            this.alarm.reportReqError(e);
          }
          if (userIdType === UserIdType.openId && [230053, 230002].includes(err.code)) {
            const notifyChatId = 'oc_2287dcdcd43c0d87ed60455b96f66a79';
            // await useInject(LarkService).addUserToChatGroup(notifyChatId, UserIdType.openId, [id]);
            await this.actualSendLarkMsg(UserIdType.chatId, notifyChatId, content, msgType);
          }
        } catch (e) {}
        return err;
      });
  }
}
