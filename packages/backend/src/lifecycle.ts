import { ApplicationLifecycle, GuluXApplication, Inject, LifecycleHook, LifecycleHookUnit } from '@gulux/gulux';
import { ContainerMiddleware } from './containerMiddleware';

@LifecycleHookUnit()
export default class BackendLifecycle implements ApplicationLifecycle {
  @Inject(GuluXApplication)
  private app: InstanceType<typeof GuluXApplication>;

  @LifecycleHook()
  async didLoad() {
    this.app.use(ContainerMiddleware);
  }
}
