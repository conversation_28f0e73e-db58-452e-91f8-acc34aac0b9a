import { ExecutionContainer, Inject, Injectable, UseContainer } from '@gulux/gulux';
import { current_region, currentProject, isCN, isLocal, isSG, ProjectType, Region } from '../utils/region';
import { HostRpcService, HostRpcServiceSymbol } from './hostRpcService';
import {
  LOCAL_MAIN_HOST,
  LOCAL_QUALITY_HOST,
  MAIN_HOST_HTTPS,
  QUALITY_HOST_CN_HTTPS,
  QUALITY_HOST_SG_HTTPS,
} from '@pa/shared/dist/src/core';
import { NetworkX } from '../utils/network';
import { BytedEnv } from '@gulux/gulux/byted-env';
import { Body, Controller, Post } from '@gulux/application-http';
import { BytedLogger } from '@gulux/gulux/byted-logger';
import { QualityRpcService, QualityRpcServiceSymbol } from './qualityRpcService';

const PROXY_API_PATH = '/open/proxy_method_stub';

interface ProxyHttpReq {
  method: string;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  params: any[];
  oversea: boolean;
}

@Injectable()
export default class RpcProxyManager {
  @Inject()
  private logger: BytedLogger;
  @Inject()
  private env: BytedEnv;

  getHost(): HostRpcService {
    if (currentProject() === ProjectType.Host) {
      throw new Error('Host项目访问Host接口无需通过RPC调用');
    }
    // 非host调用全部转发到http实现
    let baseUrl = `${MAIN_HOST_HTTPS}/api`;
    if (current_region() === Region.LOCAL) {
      baseUrl = `${LOCAL_MAIN_HOST}/api`;
    }
    console.log(`rpc host: ${baseUrl}`)
    return this.createHttpProxy<HostRpcService>(baseUrl);
  }

  getQuality(oversea: boolean): QualityRpcService {
    if (currentProject() === ProjectType.Quality && !isLocal()) {
      // if (oversea && isSG()) {
      //   throw new Error('Quality项目海外访问海外无需通过RPC调用');
      // }
      if (!oversea && isCN()) {
        throw new Error('Quality项目国内访问国内无需通过RPC调用');
      }
    }
    // 全部转发到http实现
    const baseUrl = isLocal()
      ? `${LOCAL_QUALITY_HOST}/api/quality/cn`
      : oversea
        ? `${QUALITY_HOST_SG_HTTPS}/api/quality/sg`
        : `${QUALITY_HOST_CN_HTTPS}/api/quality/cn`;
    return this.createHttpProxy<QualityRpcService>(baseUrl, oversea);
  }

  // eslint-disable-next-line @typescript-eslint/ban-types
  private createHttpProxy<T extends object>(baseUrl: string, oversea = false): T {
    const _env = this.env;
    const _logger = this.logger;
    return new Proxy<T>({} as T, {
      get(target, prop, receiver) {
        return async (...args: unknown[]) => {
          const headers = {
            'Content-Type': 'application/json',
            ...(_env.isPPE()
              ? {
                  'x-tt-env': _env.getEnv(),
                }
              : {}),
          }
          const network = new NetworkX(baseUrl, headers);
          const p = {
            method: prop,
            params: args,
            oversea,
          };
          const ret = await network.post(PROXY_API_PATH, p);
          _logger.info(`[rpcProxyClient] baseUrl: ${baseUrl}, headers: ${JSON.stringify(headers)}, req: ${JSON.stringify(p)} ret: ${JSON.stringify(ret)}}`);
          if (ret.code !== undefined && ret.code !== 0 && ret.code !== 200) {
            throw new Error(`RPC http请求异常: ${JSON.stringify(ret)}`);
          }
          return ret;
        };
      },
    });
  }
}

@Controller('')
export class ProxyController {
  @Inject()
  private logger: BytedLogger;

  @Post(PROXY_API_PATH)
  async proxyMethodStub(@Body() req: ProxyHttpReq, @UseContainer() container: ExecutionContainer) {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    let service: any;
    if (currentProject() === ProjectType.Host) {
      service = container.get(HostRpcServiceSymbol);
    } else if (currentProject() === ProjectType.Quality) {
      service = container.get(QualityRpcServiceSymbol);
    }
    if (typeof service[req.method] === 'function') {
      const ret = await Reflect.apply(service[req.method], service, req.params);
      this.logger.info(`[rpcProxyServer] req: ${JSON.stringify(req)} ret: ${JSON.stringify(ret)}`);
      if (ret === null || ret === undefined) {
        return {
          code: 0,
          message: 'proxy empty return',
        };
      }
      return ret;
    } else {
      throw new Error(`Method ${req.method} does not exist on the object`);
    }
  }
}
