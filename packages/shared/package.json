{"name": "@pa/shared", "version": "1.0.0", "description": "前后端公共包,含model及工具类", "types": "./dist/index.d.ts", "jsnext:source": "./dist/index.ts", "main": "./dist/index.js", "source": "./src/index.ts", "scripts": {"dev": "rm -rf tsconfig.tsbuildinfo && tsc -p ./tsconfig.json -w", "build": "rm -rf tsconfig.tsbuildinfo && tsc -p ./tsconfig.json", "build:watch": "rm -rf tsconfig.tsbuildinfo && tsc -p ./tsconfig.json -w", "lint": "emox eslint src --fix", "lint:error": "emox eslint src --fix --quiet"}, "devDependencies": {"@byted-emo/config": "workspace:*", "@edenx/module-tools": "1.60.4", "@types/lodash": "^4.14.202", "rimraf": "^5.0.1", "typescript": "~5.5.4", "eslint": "^8.57.0"}, "dependencies": {"tslib": "2.8.1", "lodash": "^4.17.21", "dayjs": "^1.11.10", "dayjs-plugin-utc": "^0.1.2"}, "publishConfig": {"access": "public", "registry": "https://bnpm.byted.org/"}}