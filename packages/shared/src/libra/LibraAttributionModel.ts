export interface LibraHitResult {
  experimental_group_hit_rate?: string; // 实验组命中率
  experimental_group_real_traffic?: string; // 实验组流量
  controll_group_hit_rate?: string; // 对照组命中率
  controll_group_real_traffic?: string; // 对照组流量
  link?: string;
  name?: string;
  owner?: string;
  start_time?: string;
  status?: string; // 实验状态，0已结束, 1进行中, 2待调度, 3测试中, 4已暂停, 91同0
  modify_time?: string;
  type?: string;
  hit_vid?: number[];
  error?: string;
}

export interface LibraVersionPlatform {
  android: {
    isHit: boolean;
    minVersionCode: number;
    version: string;
    isBeta: boolean;
  };
  iphone: {
    isHit: boolean;
    minVersionCode: number;
    version: string;
    isBeta: boolean;
  };
}

export interface LibraControlSetting {
  type: string;
  enable: boolean;
}
export interface SendToHostFlightInfo {
  flightId: number;
  flightName: string;
  ownersEmail?: string[];
}
export enum RequestLibraPatrolConfigDaoType {
  Create,
  Update,
  Delete,
  GetList,
  Count,
}
