export interface BlameModel {
  success: boolean;
  message: string;
  result?: string;
}

export interface FullSourceRepo {
  repoId: number;
  name: string;
  componentName: string;
}

export interface ByteBusInfo {
  path: string; // 子仓中的代码路径，不带仓库根目录
  git: string; // codebase地址
  sdk_version: string; // 版本号
  sdk_name: string; // mavenId
  checkoutInfo: string; // 子仓的cid
}

export interface CodeBaseBlameRes {
  commit?: Commit;
  line?: number;
  content?: string;
  err_msg: string;
}

export interface Commit {
  sha: string;
  message: string;
  committer: Committer;
  author: Author;
}

export interface Committer {
  name: string;
  email: string;
  date: string;
}

export interface Author {
  name: string;
  email: string;
  date: string;
}

export interface ComponentClassInfo {
  git: Git;
  classes: Class[];
}

export interface Git {
  commitId: string;
  gitUrl: string;
}

export interface Class {
  path: string;
  name: string;
}
