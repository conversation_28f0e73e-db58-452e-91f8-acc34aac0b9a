/* eslint-disable @typescript-eslint/no-explicit-any */

export interface CustomAssignArgs {
  os: string;
  version: string;
  region: string;
  crash_type: string;
  channel: string;
  aid: number;
  scene_time: number;
  start_time: number;
  end_time: number;
  update_version_codes: any;
  issue_model: IssueModel;
  is_alert: boolean;
  debug_info: DebugInfo;
  android_event: AndroidEvent;
  custom_params: { [key: string]: string };
}

export interface IssueModel {
  title: string;
  crash_file: string;
  crash_clazz: string;
  crash_method: string;
  crash_exception: string;
  user: number;
  count: number;
  event_detail: string;
  issue_id: string;
  start_os_version: string;
  end_os_version: string;
  status: string;
  managers: string[];
  crash_line_number: string;
  thread_name: string;
  modules: Modules;
  issue_level: number;
  crash_type: string;
}

export interface Modules {
  name: string;
}

export interface DebugInfo {
  JustShow: boolean;
}

export interface AndroidEvent {
  event_detail: EventDetail;
  event_log: EventLog;
  android_stacks: any;
  android_native_java_stacks: any;
  lag_drop_frame_stacks: any;
}

export interface EventDetail {
  device_id: string;
  device_model: string;
  event_id: string;
  crash_time: string;
  app_version: string;
  event_detail: string;
  crash_file: string;
  crash_line_number: string;
  update_version_code: string;
  reason: string;
  crash_exception: string;
  crash_clazz: string;
  channel: string;
  os_version: string;
  process_name: string;
  release_build: string;
  java_stacks: string[];
  is_background: boolean;
  extra_info: ExtraInfo;
  test_runtime: TestRuntime;
}

export interface ExtraInfo {
  LV_resource: string;
  VE_SDK_VERSION: string;
  VE_TAG: string;
  abi: string;
  alog_inited: string;
  code: string;
  coredump_bytest: string;
  coredump_cfg_update: string;
  coredump_delete: string;
  coredump_network_type: string;
  coredump_send_cfg_valid: string;
  crash_after_crash: string;
  crash_thread_name: string;
  disable_looper_monitor: string;
  edit_type: string;
  effectSDK: string;
  fd_count: number;
  fd_leak: string;
  fd_leak_reason: string;
  fd_max: string;
  first_update_launch: string;
  fork_crash: string;
  getMemoryInfoCost: string;
  git_branch: string;
  git_sha: string;
  graphic_memory_malloc: string;
  graphics: string;
  has_callback: string;
  has_dump: string;
  has_envinfo_file: string;
  has_exit_info: string;
  has_fds_file: string;
  has_java_stack: string;
  has_kill_info: string;
  has_logcat: string;
  has_logcat_file: string;
  has_malloc_file: string;
  has_maps_file: string;
  has_meminfo_file: string;
  has_minidump: string;
  has_pthread_key_file: string;
  has_pthread_key_map_file: string;
  has_pthreads_file: string;
  has_threads_file: string;
  has_view_tree: string;
  inner_free: string;
  inner_free_real: string;
  is_64_devices: string;
  is_64_runtime: string;
  is_hm_os: string;
  is_root: string;
  is_x86_devices: string;
  'java-heap': string;
  java_heap_leak: string;
  java_heap_usage: string;
  java_memory_free: string;
  java_memory_malloc: string;
  java_memory_max: string;
  java_memory_total: string;
  kernel_version: string;
  leak_threads_count: string;
  logcat_type: string;
  lynxSDK: string;
  lynx_stack: string;
  may_have_core_dump: string;
  memory_leak: string;
  midSoLoadState: string;
  'native-heap': string;
  native_crash_watch: string;
  native_crash_watch_value: string;
  native_heap_leak: string;
  native_heap_leak_300M: string;
  native_heap_leak_500M: string;
  native_heap_leak_800M: string;
  native_memory_malloc: string;
  native_oom_reason: string;
  notify_ne_cost: string;
  patch: string;
  pitaya_executor_crash_info: string;
  pitaya_executor_fatal: string;
  pitaya_executor_stack: string;
  pitaya_recent_run_pack: string;
  pitaya_sdk_build_ver: string;
  'private-other': string;
  sdcard_free: string;
  sdk_version: string;
  stack: string;
  start_uuid: string;
  succ_step: string;
  system: string;
  template_id: string;
  template_trace_id: string;
  thread_count: string;
  threads_leak: string;
  'total-pss': string;
  'total-swap': string;
  veSDK: string;
  ve_instance_count: string;
  version_get_time: string;
  vm_data: string;
  vm_peak: string;
  vm_size: string;
  vm_stack: string;
  vmsize_range: string;
}

export interface TestRuntime {
  automation_test_type: string;
  task_url: string;
  task_id: any;
  case_id: any;
  job_id: any;
}

export interface EventLog {
  app_version: string;
  header: Header;
  activity_trace: ActivityTrace;
  crash_thread_name: string;
  process_name: string;
  filters: Filters;
  custom_long: CustomLong;
  memory_info: MemoryInfo;
  java_data: string;
}

export interface Header {
  release_build: string;
  process_name: string;
}

export interface ActivityTrace {
  alive_activities: AliveActivity[];
  finish_activities: FinishActivity[];
  last_create_activity: LastCreateActivity;
  last_pause_activity: LastPauseActivity;
  last_resume_activity: LastResumeActivity;
  last_start_activity: LastStartActivity;
  last_stop_activity: LastStopActivity;
}

export interface AliveActivity {
  name: string;
}

export interface FinishActivity {
  name: string;
}

export interface LastCreateActivity {
  name: string;
}

export interface LastPauseActivity {
  name: string;
}

export interface LastResumeActivity {
  name: string;
}

export interface LastStartActivity {
  name: string;
}

export interface LastStopActivity {
  name: string;
}

export interface Filters {
  automation_test_type: string;
}

export interface CustomLong {
  diff_settings: string;
}

export interface MemoryInfo {
  dalvikPrivateDirty: number;
  dalvikPss: number;
  dalvikSharedDirty: number;
  nativePrivateDirty: number;
  nativePss: number;
  nativeSharedDirty: number;
  otherPrivateDirty: number;
  otherPss: number;
  otherSharedDirty: number;
  // "summary.graphics": number;
  totalPrivateClean: number;
  totalPrivateDirty: number;
  totalPss: number;
  totalSharedClean: number;
  totalSharedDirty: number;
  totalSwappablePss: number;
}
