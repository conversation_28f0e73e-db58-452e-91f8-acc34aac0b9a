import { curry, curryRight, flow } from 'lodash';

export const wait = async (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

export const extractor = <T>(...it: T[]) => it;

export const concat_string = (s: string, t: string) => s + t;

export const add_prefix = curry(concat_string);

export const add_suffix = curryRight(concat_string);

export const has_prefix = curry((prefix: string, s: string) => s.startsWith(prefix));

export const has_suffix = curry((suffix: string, s: string) => s.endsWith(suffix));

export const trim_prefix = curry((prefix: string, s: string) => (s.startsWith(prefix) ? s.slice(prefix.length) : s));

export const trim_suffix = curry((suffix: string, s: string) =>
  s.endsWith(suffix) ? s.slice(0, s.length - suffix.length) : s,
);

export const add_prefix_ne = (prefix: string) => flow(trim_prefix(prefix), add_prefix(prefix));

export const add_suffix_ne = (suffix: string) => flow(trim_suffix(suffix), add_suffix(suffix));

export function retryPromise<T>(
  fn: () => Promise<T>,
  onReTry: (i: number, error?: unknown) => void = () => {
    // do nothing
  },
  retries = 2,
  delay = 3000,
): Promise<T> {
  return new Promise((resolve, reject) => {
    const attempt = (remainingRetries: number) => {
      fn()
        .then(resolve)
        .catch(error => {
          console.error(error);
          if (remainingRetries === 0) {
            reject(error);
          } else {
            setTimeout(() => {
              console.log(`Retrying... ${remainingRetries} attempts left`);
              attempt(remainingRetries - 1);
              onReTry(remainingRetries - 1, error);
            }, delay);
          }
        });
    };
    attempt(retries);
  });
}

export type WithId<T> = T & {
  _id: string;
};

export const with_retries = async <T>(
  func: () => Promise<T>,
  verdict: (result: T) => boolean,
  retries: number,
): Promise<T | undefined> => {
  for (let i = 0; i < retries; ++i) {
    const result = await func();
    if (verdict(result)) {
      return result;
    }
  }
  return undefined;
};

export const trim_query_from_url = (url: string): string => url.split('?')[0];

export const is_json_string = (s: string) => {
  try {
    JSON.parse(s);
    return true;
  } catch (e) {
    return false;
  }
};
