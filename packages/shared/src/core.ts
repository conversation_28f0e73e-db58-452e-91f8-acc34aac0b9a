export enum PlatformType {
  Android = 'Android',
  iOS = 'iOS',
  PC = 'PC',
  Server = 'server',
  Multi = 'Multi',
  init = 'init',
  Windows = 'Windows',
  Mac = 'Mac',
}

export const MAIN_HOST = 'pa.bytedance.net';
export const MAIN_HOST_HTTPS = `https://${MAIN_HOST}`;
export const QUALITY_HOST_CN = 'paper-airplane-quality.gf.bytedance.net';
export const QUALITY_HOST_CN_HTTPS = `https://${QUALITY_HOST_CN}`;
// export const QUALITY_HOST_SG = 'pa.tiktok-row.net';
export const QUALITY_HOST_SG = 'paper-airplane-quality.gf-i18n.sinf.net';
export const QUALITY_HOST_SG_HTTPS = `https://${QUALITY_HOST_SG}`;
export const LOCAL_MAIN_HOST = 'http://127.0.0.1:8080';
export const LOCAL_QUALITY_HOST = 'http://127.0.0.1:8081';

export interface NetworkResult<T> {
  data?: T;
  message: string;
  code: number;
}

export enum NetworkCode {
  Success = 0,
  Error = -1,
  Warning = 1,
  NoAuthority = 3, // 无权限时返回
}

export interface UserList {
  data?: User[];
}

export interface User {
  name: string;
  email?: string;
  open_id: string;
  user_id?: string;
  employee_no?: string;
  avatar?: string | Avatar;
}

export interface ChatInfo {
  chat_id?: string | undefined;
  avatar?: string | undefined;
  name?: string | undefined;
  description?: string | undefined;
}

export interface Avatar {
  avatar_240?: string;
  avatar_640?: string;
  avatar_72?: string;
  avatar_origin?: string;
}

export interface BranchInfo {
  branch: string;
  commit: string;
  username: string;
  create_time: number;
  update_time: number;
  is_formal: boolean;
}

export interface LoginUserInfo {
  avatar: 'string';
  name: 'string';
  email: 'string';
}

export interface BranchList {
  data: BranchInfo[];
}

export interface transTaskInfo {
  projectId: number;
  taskId: number;
  locked: number;
  taskName: string; // '2023-6-20【cc web】同款槽位素材支持上屏替换 功能文案',
  creator: string; // 'wangyue.0723',
  updator: string; // 'wangyue.0723',
  syncNamespaces: number[]; // [ 53510 ],
  createdAt: string; // '2023-06-20T09:23:27.000Z',
  updatedAt: string; // '2023-06-20T09:23:27.000Z'
}

export interface transTaskListArg {
  projectId: number;
  name?: string;
  offset?: number;
  limit?: number;
}

export function successRsp<T>(message: string, data?: T) {
  return {
    code: NetworkCode.Success,
    message,
    data,
  };
}

export function errorRsp<T>(message: string, data?: T) {
  return {
    code: NetworkCode.Error,
    message,
    data: data && undefined,
  };
}
