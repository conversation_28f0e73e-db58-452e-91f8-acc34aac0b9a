import { PlatformType } from '../core';

export interface FeatureInfo {
  featureName: '';
  featurePath: string;
}

export const AppSettingSymbol = Symbol.for('AppSetting');

export interface AppSetting {
  id: AppSettingId; // 设置id，各端的id不同，前N位为APP，后两位为分端ID 总共位数为N+2
  name: string; // APP名称
  platform: PlatformType;
  productType: string;
  businessID: BusinessType; // 业务id
  featureList?: FeatureInfo[];
  time: number;
  businessInfo: BusinessAppInfo;
  icon: string;
}

export enum BusinessType {
  LV = 1775,
  Retouch = 2515,
  PC = 886,
  Dreamina = 581595,
  Pippit = 8700,
  TinyCut = 8702,
  // NOTE 多端接入配置
}

export type ProductType = string;

export const SettingList = [
  {
    settingName: '基础配置',
    settingSymbol: Symbol.keyFor(AppSettingSymbol) as string,
  },
  {
    settingName: '测试占位',
    settingSymbol: 'AppUserSetting',
  },
];

export interface BusinessAppInfo {
  app_id: number;
  bits_id: number;
  app_name: string;
  platform: PlatformType;
  bits_workspace: number;
  meegoKey: string;
  product_type: ProductType;
  calendar_segment_list: string[];
  group_name: string;
  business_id: BusinessType;
  route_config: string[];
  tea_app_id: number;
  oversea: boolean;
  version_timeline_config: string;
  aid: number;
  icon: string;
}

export interface BusinessConfig {
  business_id: number;
  app_list: BusinessAppInfo[];
}

export interface Route {
  path?: string;
  name?: string;
  routes?: Route[];
  defaultPath?: string;
  rootPath?: string;
  hideInMenu?: boolean;
  versionFilter?: boolean;
  appFilter?: boolean;
  icon?: unknown;
}

/**
 * 具体用户的相关配置
 */
export interface AppUserSetting {
  email: string;
  selectApp: number;
  selectVersion: string;
}

export const MAIN_HOST = 'pa.bytedance.net';
export const MAIN_HOST_HTTPS = `https://${MAIN_HOST}`;

export const QUALITY_HOST_CN = 'paper-airplane-quality.gf.bytedance.net';
export const QUALITY_HOST_CN_HTTPS = `https://${QUALITY_HOST_CN}`;
// export const QUALITY_HOST_SG = 'pa.tiktok-row.net';
export const QUALITY_HOST_SG = 'paper-airplane-quality.gf-i18n.sinf.net';
export const QUALITY_HOST_SG_HTTPS = `https://${QUALITY_HOST_SG}`;

export enum AppSettingId {
  UNKNOWN = -1,
  ALL = 0,
  LV_ANDROID = 177502,
  LV_IOS = 177501,
  LV_WIN = 2020092383,
  LV_MAC = 2020092892,
  CC_ANDROID = 300602,
  CC_IOS = 300601,
  CC_WIN = 35928901,
  CC_MAC = 35928902,
  RETOUCH_IOS = 251501,
  RETOUCH_ANDROID = 251502,
  HYPIC_IOS = 2020093988,
  HYPIC_ANDROID = 2020093924,
  DREAMINA_ANDROID = 244127338754,
  DREAMINA_IOS = 225469550850,
  PIPPIT_ANDROID = 764682035714,
  PIPPIT_IOS = 903699867138,
  TINYCUT_ANDROID = 787266764802,
  TINYCUT_IOS = 787403161602,
  // NOTE 多端接入配置
}

export enum LVProductType {
  lv = 'lv',
  cc = 'cc',
}

export enum SlardarPlatformType {
  Android = 'Android',
  iOS = 'iOS',
}

export function isOverseasApp(appSetting: AppSettingId) {
  return [AppSettingId.CC_ANDROID, AppSettingId.CC_IOS, AppSettingId.CC_MAC, AppSettingId.CC_WIN].includes(appSetting);
}

export function isPlatformApp(appSetting: AppSettingId, platform: PlatformType) {
  if (platform === PlatformType.Android) {
    return [
      AppSettingId.CC_ANDROID,
      AppSettingId.LV_ANDROID,
      AppSettingId.RETOUCH_ANDROID,
      AppSettingId.HYPIC_ANDROID,
    ].includes(appSetting);
  } else if (platform === PlatformType.iOS) {
    return [AppSettingId.CC_IOS, AppSettingId.LV_IOS, AppSettingId.RETOUCH_IOS, AppSettingId.HYPIC_IOS].includes(
      appSetting,
    );
  } else if (platform === PlatformType.PC) {
    return [AppSettingId.LV_WIN, AppSettingId.LV_MAC, AppSettingId.CC_WIN, AppSettingId.CC_MAC].includes(appSetting);
  } else {
    return false;
  }
}
