export enum VersionType {
  GRAY = 1,
  ONL<PERSON><PERSON>,
  SINGLE_GRAY,

  UNKNOWN = 10001,
  TF_GRAY,
  SMALL_TF,
  SMALL,
  FULL_TF,
  FULL,

  MAJOR_VERSION = 20001,
}

export interface FetchVersionLogParams {
  bits_app_id: string | number;
  type: 'OFFICIAL' | 'GRAY' | 'TECH_GRAY';
  release_platforms?: string[];
  pn: number;
  rn: number;
  version_name?: string[];
  update_version?: string[];
  channel?: string[];
  cpu?: string[];
}

export interface FetchVersionLogResponse {
  version_name: string;
  update_version: string;
  cpu: string;
  publish_time?: number;
  release_amount?: number;
  review_pass_time?: number;
  full_publish_time?: number;
  version_type?: VersionType;
  channel?: string;
}
