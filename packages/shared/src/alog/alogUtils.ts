import {
  ActionUserAction,
  AlogAnalyseRuleBase,
  AlogAnalyseRuleCrash,
  AlogAnalyseRuleMemory,
  AlogAnalyseRuleType,
  AlogUserActionInfo,
  EventUserAction,
  GetLogIssueInfoResponse,
  UserAction,
  UserActionType,
  VCUserAction,
} from './models';
import { MAIN_HOST_HTTPS } from '../core';

export const defaultCheckedRate = 0.6;
export const alogAggregateCount = 100;

function replaceKey(key: string): string {
  // 去掉所有的 () 和 数字
  return key.replaceAll('(', '').replaceAll(')', '').replaceAll(/\d+/g, '').replaceAll('maybe', '').replaceAll(':', '');
}

export function actionKey(action: UserAction): string {
  let key = 'unknown';
  if (action.type === UserActionType.VC) {
    const vcAction = action as VCUserAction;
    key = vcAction.name;
  } else if (action.type === UserActionType.Action) {
    const actionAction = action as ActionUserAction;
    key = `${actionAction.actionSender}${actionAction.target}${actionAction.sel}`;
  } else if (action.type === UserActionType.Event) {
    const eventAction = action as EventUserAction;
    key = `${eventAction.cell === '' ? eventAction.view : eventAction.cell}`;
  }
  return replaceKey(key);
}

function filterVCAction(action: VCUserAction): boolean {
  const blackListVCName = [
    'LVNavigationController',
    'MainTabBarController',
    'BottomContainerViewController',
    'DraftBoxContainerControllerNew',
    'TLODraftBoxViewController',
    'LocalDraftListViewControllerNew',
    'MenuContainerViewController',
    'PanelContainerViewController',
    'PreviewContainerViewController',
    'EditorDockMenuViewController',
    'LiteEditorDockMenuViewController',
    'BrowserViewController',
    'CofferViewController', // 同时会有一个 AlbumViewController
  ];

  return !blackListVCName.includes(action.name);
}

export function getVCCount(
  actionInfos: AlogUserActionInfo[],
): [{ [key: string]: number }, { [key: string]: UserAction }] {
  const vcMap: { [key: string]: number } = {};
  const actionMap: { [key: string]: UserAction } = {};
  for (const actionItem of actionInfos) {
    const vcActions = actionItem.actions.filter(
      action => action.type === UserActionType.VC && filterVCAction(action as VCUserAction),
    );
    const uniqueVcActions = Array.from(new Map(vcActions.map(item => [actionKey(item), item])).values());
    for (const action of uniqueVcActions) {
      const key = actionKey(action);
      if (vcMap[key] === undefined) {
        vcMap[key] = 0;
      }
      vcMap[key]++;
      actionMap[key] = action;
    }
  }

  return [vcMap, actionMap];
}

export function getActionCount(
  actionInfos: AlogUserActionInfo[],
): [{ [key: string]: number }, { [key: string]: UserAction }] {
  const countMap: { [key: string]: number } = {};
  const actionMap: { [key: string]: UserAction } = {};
  for (const actionItem of actionInfos) {
    const actionActions = actionItem.actions.filter(action => action.type === UserActionType.Action);
    const uniqueActionActions = Array.from(new Map(actionActions.map(item => [actionKey(item), item])).values());
    for (const action of uniqueActionActions) {
      const key = actionKey(action);
      if (countMap[key] === undefined) {
        countMap[key] = 0;
      }
      countMap[key]++;
      actionMap[key] = action;
    }

    const eventActions = actionItem.actions.filter(action => action.type === UserActionType.Event);
    const uniqueEventActions = Array.from(new Map(eventActions.map(item => [actionKey(item), item])).values());
    for (const action of uniqueEventActions) {
      const key = actionKey(action);
      if (countMap[key] === undefined) {
        countMap[key] = 0;
      }
      countMap[key]++;
      actionMap[key] = action;
    }
  }

  return [countMap, actionMap];
}

export function getKeyInfoFromUserActions(actions: UserAction[]): [string[], string[]] {
  const keyClass: string[] = [];
  const keyMethod: string[] = [];
  for (const userAction of actions) {
    if (userAction.type === UserActionType.Action) {
      const actionUserAction = userAction as ActionUserAction;
      keyClass.push(actionUserAction.actionSender.replace(/\((.*)/g, '')); // 匹配 ( 开始到末尾
      keyClass.push(actionUserAction.target.replace(/\((.*)/g, ''));
      keyMethod.push(actionUserAction.sel);
    } else if (userAction.type === UserActionType.Event) {
      const eventUserAction = userAction as EventUserAction;
      keyClass.push(eventUserAction.view.replace(/\((.*)/g, ''));
      keyClass.push(eventUserAction.cell.replace(/\((.*)/g, ''));
    } else if (userAction.type === UserActionType.VC) {
      const vcUserAction = userAction as VCUserAction;
      keyClass.push(vcUserAction.name);
      keyClass.push(vcUserAction.rootVC);
      keyClass.push(vcUserAction.topVC);
    }
  }

  return [
    Array.from(
      new Set(
        keyClass.filter(item => !item.startsWith('UI') && !item.startsWith('_UI') && item !== '' && item !== 'nil'),
      ),
    ),
    Array.from(new Set(keyMethod)).filter(item => item !== '' && item !== 'nil'),
  ];
}

export function getMRUrl(keyInfo: [string[], string[]], issueInfo: GetLogIssueInfoResponse | undefined): URL {
  const deduplicatedKeyClasses = [...new Set(keyInfo[0])];
  const deduplicatedKeyMethods = [...new Set(keyInfo[1])];

  const base = MAIN_HOST_HTTPS;
  const path = '/quality/diagnosis/issue-attribution/mr-profile/code-change';
  const mrSearchParams = new URLSearchParams({
    filePath: deduplicatedKeyClasses.join(','),
    methodName: deduplicatedKeyMethods.join(','),
    autoSearch: 'true',
  });

  if (issueInfo !== undefined) {
    if (issueInfo.aid !== 0) {
      mrSearchParams.set('appid', `${issueInfo.aid}01`);
    }

    if (issueInfo.minAppVersion !== '') {
      mrSearchParams.set('version', `${issueInfo.minAppVersion}`);
    }
  }

  const url = new URL(path, base);
  url.search = mrSearchParams.toString();

  return url;
}

export function analyseRuleWithSelector(value: string): AlogAnalyseRuleBase | undefined {
  if (value === '0') {
    const crashTimeRule: AlogAnalyseRuleCrash = { ruleType: AlogAnalyseRuleType.CrashTime, value: 3 };
    return crashTimeRule;
  } else if (value === '1') {
    const crashTimeRule: AlogAnalyseRuleCrash = { ruleType: AlogAnalyseRuleType.CrashTime, value: 10 };
    return crashTimeRule;
  } else if (value === '2') {
    const crashTimeRule: AlogAnalyseRuleCrash = { ruleType: AlogAnalyseRuleType.CrashTime, value: 30 };
    return crashTimeRule;
  } else if (value === '3') {
    const memoryRule: AlogAnalyseRuleMemory = { ruleType: AlogAnalyseRuleType.Memory };
    return memoryRule;
  }
  return undefined;
}
