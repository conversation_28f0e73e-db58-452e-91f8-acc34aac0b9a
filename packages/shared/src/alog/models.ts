export enum AlogLevel {
  Verbose = 'V',
  Debug = 'D',
  Info = 'I',
  Warn = 'W',
  Error = 'E',
}

export const UserActionLogTag = 'UserAction';

export interface AlogLine {
  timeStamp: number;
  utc: string;
  pid: number;
  tid: number;
  level: AlogLevel;
  tag: string;
  fileName: string;
  func: string;
  lineNumber: number;
  body: string;
}

export interface AlogContent {
  lines: AlogLine[];
}

export interface Alog {
  contents: AlogContent[];
}

export interface KeyActionFilter {
  hitRegExp: RegExp;
  titleRegExp?: RegExp;
  descriptionRegExp?: RegExp;
}

export interface DurationActionFilter {
  beginRegExp: RegExp;
  endRegExp: RegExp;
  titleRegExp?: RegExp;
  descriptionRegExp?: RegExp;
}

export interface DurationAction extends UserAction {
  title: string;
  description?: string;
  endTimeStamp?: number;
  filter: DurationActionFilter;
}

export interface KeyAction extends UserAction {
  title: string;
  description?: string;
  filter: KeyActionFilter;
}

export interface MemoryLogs {
  timeStamp: number;
  lineNumber: number;
  appUsed: number;
  totalUsed: number;
}

export enum UserActionType {
  VC = 'VC',
  Action = 'Action',
  Event = 'Event',
  Key = 'Key',
  Duration = 'Duration',
}

export interface UserAction {
  type: UserActionType;
  timeStamp: number;
  utc: string;
  lineNumber: number;
}

export interface ActionUserAction extends UserAction {
  actionSender: string;
  target: string;
  sel: string;
}

export enum EventUserActionPhase {
  began = 'began',
  moved = 'moved',
  stationary = 'stationary',
  ended = 'ended',
  cancelled = 'cancelled',
}

export interface EventUserAction extends UserAction {
  view: string;
  cell: string;
  touchIdx: string;
  phase: EventUserActionPhase;
  gestures: string;
  possibleAction: string;
  endTimeStamp?: number;
}

export enum VCUserActionType {
  viewDidAppear = 'viewDidAppear',
  viewDidDisappear = 'viewDidDisappear',
}

export interface VCUserAction extends UserAction {
  vcType: VCUserActionType;
  name: string;
  rootVC: string;
  topVC: string;
  endTimeStamp?: number;
}

export interface AlogKeyInfo {
  keyClass: string[];
  keyMethod: string[];
  aid: number | undefined;
  version: string | undefined;
  actions: UserAction[];
}

export interface AlogUserActionInfo {
  actions: UserAction[];
}

export interface GetLogActionInfoRequest {
  url: string;
  aggregateCount: number;
}

export interface GetLogActionInfoResponse {
  url: string;
  aggregateCount: number;
  actions: AlogUserActionInfo[];
  errorMsg: string;
}

export interface GetLogIssueInfoRequest {
  url: string;
}

export interface GetLogIssueInfoResponse {
  aid: number;
  minAppVersion: string;
  maxAppVersion: string;
  errorMsg: string;
}

export interface GetLogKeyInfoResponse {
  keyInfos: AlogKeyInfo[];
  errorMsg: string;
}

export interface GetLogItem {
  crashTime: string;
  alog: Alog;
}

export interface GetLogResponse {
  items: GetLogItem[];
  errorMsg: string;
}

export enum AlogAnalyseRuleType {
  CrashTime = 'CrashTime',
  Memory = 'Memory',
}

export interface AlogAnalyseRuleBase {
  ruleType: AlogAnalyseRuleType;
}

export interface AlogAnalyseRuleCrash extends AlogAnalyseRuleBase {
  value: number;
}

export interface AlogAnalyseRuleMemory extends AlogAnalyseRuleBase {}
