export interface SheetItemElement {
  type: ItemType;
  // union 字段，下方字段仅会出现其中一种
  text?: SheetItemText | { text: string };
  mention_user?: SheetItemMentionUser;
  mention_document?: SheetItemMentionDocument;
  value?: SheetItemValue;
  date_time?: SheetItemDateTime;
  // "checkbox"?:object(Checkbox);//接口不支持
  file?: SheetItemFile;
  image?: SheetItemImage;
  link?: SheetItemLink;
  reminder?: SheetItemReminder;
  formula?: SheetItemFormula;
  // "single_option"?:object(SingleOption);//接口不支持
  // "multiple_option"?:object(MultipleOption);//接口不支持
  // "date_validation"?:object(DateValidation);//接口不支持
  // "datetime_option"?:object(DateTimeOption);//接口不支持
}

export enum ItemType {
  text = 'text',
  value = 'value',
  link = 'link',
  dateTime = 'date_time',
  mentionUser = 'mention_user',
}

export interface SheetItemText {
  text: string;
  segment_style?: SheetItemSegmentStyle;
}

export interface SheetItemSegmentStyle {
  style: SheetItemStyle;
  affected_text?: string;
}

export interface SheetItemStyle {
  bold?: boolean;
  italic?: boolean;
  strike_through?: boolean;
  underline?: boolean;
  fore_color?: string;
  font_size?: number;
}

export interface UpdateSheetPropertyData {
  updateSheet: {
    properties: {
      sheetId: string;
      title?: string;
    };
  };
}

export interface SheetItemMentionUser {
  name?: string;
  user_id: string;
  notify?: boolean;
  segment_styles?: SheetItemSegmentStyle;
}

export interface SheetItemMentionDocument {
  title: string;
  object_type: string;
  token: string;
  segment_styles: SheetItemSegmentStyle;
}

export interface SheetItemValue {
  value: string;
}

export interface SheetItemDateTime {
  date_time: string;
}

export interface SheetItemFile {
  file_token: string;
  name: string;
  segment_style: SheetItemSegmentStyle;
}

export interface SheetItemImage {
  image_token: string;
}

export interface SheetItemLink {
  text: string;
  link: string;
  segment_styles?: SheetItemSegmentStyle[];
}

export interface SheetItemReminder {
  notify_date_time: string;
  notify_user_id: string[];
  notify_text: string;
  notify_strategy: number;
}

export interface SheetItemFormula {
  formula: string;
  formula_value: string;
  affected_range: string;
}

// mark 查询表的内容
export interface SheetMetaItem {
  sheet_id: string;
  title: string;
  index: number;
  hidden: boolean;
  grid_properties: SheetMetaGridProperty;
  resource_type: string;
  merges: SheetMetaMerge[];
}

export interface SheetMetaGridProperty {
  frozen_row_count: number;
  frozen_column_count: number;
  row_count: number;
  column_count: number;
}

export interface SheetMetaMerge {
  start_row_index: number;
  end_row_index: number;
  start_column_index: number;
  end_column_index: number;
}

// 表的内容
export interface SheetContentItem {
  revision: number;
  spreadsheetToken: string;
  totalCells: number;
  valueRanges: SheetContentDetailValueRange[];
}

export interface SheetContentDetailValueRange {
  majorDimension: string;
  range: string;
  revision: number;
  values: string[][];
}

// 自定义数据结构
export interface SheetWriteContent {
  title: string;
  titleStyle?: Record<string, unknown>;
  columnTitles: string[];
  rowContents: string[][];
}

export interface ValueRange {
  range: string;
  values: SheetItemElement[][][];
}

export interface SheetCellsContent {
  value_ranges: ValueRange[];
}

export interface RangeStyle {
  ranges: string[];
  style: {
    backColor?: string;
    hAlign?: number;
    vAlign?: number;
  };
}

// 创建电子表格回包 Item
export interface SpreadSheetCreateRspItem {
  title: string;
  folder_token: string;
  url: string;
  spreadsheet_token: string;
}

// 查询获取电子表格回包 Item
export interface SpreadSheetQueryRspItem {
  title: string;
  owner_id: string;
  token: string;
  url: string;
}

export interface SheetRange {
  revision: number;
  spreadsheetToken: string;
  valueRange: {
    majorDimension: string;
    range: string;
    revision: number;
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    values: any[][];
  };
}
