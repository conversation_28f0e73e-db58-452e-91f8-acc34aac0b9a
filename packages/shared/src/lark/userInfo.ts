export enum UserIdType {
  openId = 'open_id',
  unionId = 'union_id',
  userId = 'user_id',
  chatId = 'chat_id',
}

export interface UserData {
  email: string;
  user_id: string;
  open_id: string;
}

export interface UserInfo {
  avatar: Avatar;
  city: string;
  country: string;
  department_ids: string[];
  description: string;
  email: string;
  employee_no: string;
  employee_type: number;
  en_name: string;
  e_email: string;
  gender: number;
  is_tenant_manager: boolean;
  job_title: string;
  join_time: number;
  leader_user_id: string;
  mobile_visible: boolean;
  name: string;
  open_id: string;
  orders: Order[];
  status: Status;
  union_id: string;
  user_id: string;
  work_station: string;
  department_path: DepartmentDetail[];
}

export interface DepartmentDetail {
  department_id: string;
  department_name: { name: string };
  department_path: {
    department_ids: string[];
    department_path_name: {
      name: string;
    };
  };
}

export interface Avatar {
  avatar_240: string;
  avatar_640: string;
  avatar_72: string;
  avatar_origin: string;
}

export interface Order {
  department_id: string;
  department_order: number;
  user_order: number;
}

export interface Status {
  is_activated: boolean;
  is_exited: boolean;
  is_frozen: boolean;
  is_resigned: boolean;
  is_unjoin: boolean;
}
