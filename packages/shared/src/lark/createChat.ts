import { UserIdType } from './userInfo';

export interface CreateChatResult {
  chat_id: string;
  avatar: string;
  name: string;
  description: string;
  i18n_names: I18nNames;
  owner_id: string;
  owner_id_type: string;
  add_member_permission: string;
  share_card_permission: string;
  at_all_permission: string;
  edit_permission: string;
  chat_mode: string;
  chat_type: string;
  chat_tag: string;
  external: boolean;
  tenant_key: string;
  join_message_visibility: string;
  leave_message_visibility: string;
  membership_approval: string;
  moderation_permission: string;
}

export interface I18nNames {
  zh_cn: string;
  en_us: string;
  ja_jp: string;
}

export interface CreateChatQuery {
  user_id_type?: UserIdType;
  set_bot_manager?: boolean;
}

export interface CreateChatArgs {
  name: string;
  description: string;
  owner_id: string;
  user_id_list: string[];
  bot_id_list?: string[];
  chat_type?: 'private';
}
