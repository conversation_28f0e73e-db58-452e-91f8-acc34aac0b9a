export enum CardSchema {
  v1 = '1.0',
  v2 = '2.0',
}

export interface CardInfo {
  title: string;
  template: CardTemplate;
  config?: CardConfig;
}

export interface Card {
  config: CardConfig;
  header?: CardHeader;
  elements: CardElement[];
}

export interface CardV2 {
  schema: CardSchema;
  config: CardConfig;
  header?: CardHeader;
  body: {
    elements: CardElement[];
  };
}

export interface CallBackToast {
  toast: {
    type: string;
    content: string;
  };
}

export interface CardConfig {
  enable_forward?: boolean; // 是否允许转发
  update_multi?: boolean; // 是否共享
  wide_screen_mode?: boolean;
}

export enum CardTemplate {
  blue = 'blue',
  wathet = 'wathet',
  turquoise = 'turquoise',
  green = 'green',
  yellow = 'yellow',
  orange = 'orange',
  red = 'red',
  carmine = 'carmine',
  violet = 'violet',
  purple = 'purple',
  indigo = 'indigo',
  grey = 'grey',
}

export interface CardHeader {
  title: CardTitle;
  template: CardTemplate;
}

export interface CardTitle {
  tag: TitleTag.plain_text;
  content: string;
}

export enum TitleTag {
  plain_text = 'plain_text',
}

export interface CardElement {
  tag: CardElementTag | CardElementTagV2;
}

export enum CardElementTag {
  markdown = 'markdown',
  div = 'div',
  hr = 'hr',
  img = 'img',
  note = 'note',
  action = 'action',
  button = 'button',
  select = 'select_static',
  columnSet = 'column_set',
  column = 'column',
  datePicker = 'date_picker',
  table = 'table',
}

// 2.0 版本不再支持 note 和 action 元素
// https://open.larkoffice.com/document/uAjLw4CM/ukzMukzMukzM/feishu-cards/card-json-v2-breaking-changes-release-notes#47cd6626
export enum CardElementTagV2 {
  markdown = 'markdown',
  div = 'div',
  hr = 'hr',
  img = 'img',
  button = 'button',
  select = 'select_static',
  columnSet = 'column_set',
  column = 'column',
  datePicker = 'date_picker',
  table = 'table',
}

export interface CardContentElement extends CardElement {
  text: CardText;
  fields?: CardField[];
  extra?: CardImage | CardButtonAction;
}

export interface CardMarkdownElement extends CardElement {
  content: string;
  href?: Hrefs;
  text_size?: string;
}

export interface Hrefs {
  [key: string]: CardHref;
}

export interface CardHref {
  url: string;
  android_url: string;
  ios_url: string;
  pc_url: string;
}

export interface CardField {
  is_short: boolean; // 是否是并排结构
  text: CardText;
}

export interface CardText {
  tag: CardTextTag;
  content: string;
  token?: string;
  lines?: number;
}

export interface CardImage extends CardElement {
  img_key: string;
  alt: CardText;
  preview?: boolean;
}

export interface CardActionElement extends CardElement {
  actions: CardAction[];
  layout?: CardActionLayout;
}

export interface CardColumnElement extends CardElement {
  elements: CardElement[];
}

export interface CardColumnSetElement extends CardElement {
  columns: CardColumnElement[];
}

export interface CardNoteElement extends CardElement {
  elements: CardText[];
}

export enum CardActionLayout {
  bisected = 'bisected',
  trisection = 'trisection',
  flow = 'flow',
}

export type CardAction = CardElement;

export interface CardSelectAction extends CardAction {
  placeholder: {
    tag: CardTextTag;
    content: string;
  };
  value: {
    [key: string]: string;
  };
  options: {
    text: {
      tag: CardTextTag;
      content: string;
    };
    value: string;
  }[];
}

export interface CardButtonAction extends CardAction {
  text: CardText;
  url?: string;
  multi_url?: CardHref;
  type?: CardButtonType;
  value?: CardActionValue;
  confirm?: CardActionConfirm;
}

export interface CardActionConfirm {
  title: CardText;
  text: CardText;
}

export interface CardActionValue extends CardActionInnerValue {
  cardCallbackType: CardCallbackType;
  cardCallbackSubType: CardCallbackSubType;
}

export type CardActionInnerValue = {
  [key in CardActionKey]: string | CardCallbackType | CardCallbackSubType;
};

export type CardActionKey =
  | 'groupId'
  | 'version'
  | 'cardCallbackType'
  | 'sourceBranch'
  | 'targetBranch'
  | 'platform'
  | 'meegoIssueId'
  | 'meegoVersion'
  | 'id'
  | 'appId'
  | 'isAd'
  | 'indicatorType'
  | 'targetBranchesForCherryPick'
  | 'mrLink'
  | 'mrId'
  | 'product'
  | 'versionStage'
  | 'grayNum'
  | 'templateId'
  | 'busId'
  | 'chatId'
  | 'userIds'
  | 'balanceRecordId'
  | 'isApproved'
  | 'stage'
  | 'checkItemId'
  | 'metricName'
  | 'subStage'
  | 'crashType'
  | 'workItemId'
  | 'nodeId'
  | 'userKey'
  | 'businessName'
  | 'itemType'
  | 'parentStage'
  | 'cardInfoJson'
  | 'QATestStage'
  | 'cardCallbackSubType'
  | 'meegoId'
  | 'assigner'
  | 'approvalCode'
  | 'instanceCode'
  | 'approvalAction'
  | 'taskId'
  | 'userId'
  | 'permissionOpenIds'
  | 'slardarUrl'
  | 'isOversea'
  | 'ticketId'
  | 'ticketType'
  | 'actionType';

export enum CardCallbackType {
  GroupChangeTarget = 'GroupChangeTarget',
  CreateMR = 'CreateMr',
  MajorIssueCheck = 'MajorIssueCheck',
  ExperimentUpdate = 'ExperimentUpdate',
  ExperimentUpdateStatus = 'ExperimentUpdateStatus',
  ExperimentUpdateRemark = 'ExperimentUpdateRemark',
  CreateCherryPickMR = 'CreateCherryPickMR',
  JoinIntegrationGroup = 'JoinIntegrationGroup',
  GetOnBus = 'GetOnBus',
  MarkGetOnBus = 'MarkGetOnBus',
  GetOffBus = 'GetOffBus',
  NotifyBusList = 'NotifyBusList',
  PushBus = 'PushBus',
  DelayCR = 'DelayCR',
  UpdateCheckListInfo = 'UpdateCheckListInfo',
  CancelFastMerge = 'CancelFastMerge',
  RenewFastMerge = 'RenewFastMerge',
  BatchSubMrConflictResolved = 'BatchSubMrConflictResolved',
  WithdrawBanlance = 'WithdrawBanlance',
  VersionConfirmNextStage = 'VersionConfirmNextStage',
  CheckItemStatusChange = 'CheckItemStatusChange',
  VersionAutoStageChangeStatus = 'VersionAutoStageChangeStatus',
  VersionRejectLarkGroup = 'VersionRejectLarkGroup',
  VersionDelayGray = 'VersionDelayGray',
  VersionSkipGray = 'VersionSkipGray',
  MetricsAlarm = 'MetricsAlarm',
  FinishMeegoNode = 'FinishMeegoNode',
  ExemptBug = 'ExemptBug',
  GrayDelayEvaluate = 'GrayDelayEvaluate',
  BusinessSingleBugExemptApproved = 'BusinessSingleBugExemptApproved',
  BusinessSingleBugExemptReject = 'BusinessSingleBugExemptReject',
  BugCheckItemExemptApprove = 'BugCheckItemExemptApprove',
  BugResolveRateExemptApprove = 'BugResolveRateExemptApprove',
  QATestStart = 'QATestStartUser',
  QualityCommonDispatchCN = 'QualityCommonDispatchCN',
  QualityCommonDispatchSG = 'QualityCommonDispatchSG',
  IosFullReleaseAutoApprove = 'IosFullReleaseAutoApprove',
  ApprovalService = 'ApprovalService',
  LibraLaunchGrayNotifyNotSendAnymore = 'LibraLaunchGrayNotifyNotSendAnymore',
  Libra100PercentGrayNotifyNotSendAnymore = 'Libra100PercentGrayNotifyNotSendAnymore',
  LibraCloseGray = 'LibraCloseGray',
  LibraLaunchReleaseNotifyNotSendAnymore = 'LibraLaunchReleaseNotifyNotSendAnymore',
  LibraCloseGrayNotifyNotSendAnymore = 'LibraCloseGrayNotifyNotSendAnymore',
  LibraCreateRelease = 'LibraCreateRelease',
  SilentReleaseNoti = 'SilentReleaseNoti',
  AcceptDailyCardNotSendAnymore = 'AcceptDailyCardNotSendAnymore',
  SubmitBlockUrgentComplete = 'SubmitBlockUrgentComplete',
  CircuitBreakerHandle = 'CircuitBreakerHandle',
  ApprovalAddMoreBusinessLine = 'ApprovalAddMoreBusinessLine',
}

export enum CardCallbackSubType {
  None = 'None',
  IssueSolving = 'IssueSolving',
  IssueRequestReAssign = 'IssueRequestReAssign',
  IssueReAssign = 'IssueReAssign',
  IssueQAPush = 'IssueQAPush',
  IssueQAOnCall = 'IssueQAOnCall',
  IssueCrashBMOnCall = 'IssueCrashBMOnCall',
  IssueRefresh = 'IssueRefresh',
  IssueABTestAnalyze = 'IssueABTestAnalyze',
}

export enum CardButtonType {
  default = 'default',
  primary = 'primary',
  danger = 'danger',
  primary_filled = 'primary_filled',
}

export interface CardImageElement extends CardImage {
  title?: CardText;
  custom_width?: number;
  compact_width?: boolean;
  mode?: CardImageMode;
}

export enum CardImageMode {
  crop_center = 'crop_center',
  fit_horizontal = 'fit_horizontal',
}

export enum CardTextTag {
  plain_text = 'plain_text',
  lark_md = 'lark_md',
  standard_icon = 'standard_icon',
}

export enum MessageType {
  text = 'text',
  interactive = 'interactive',
  post = 'post',
}

export interface CardCallback {
  open_id: string;
  user_id: string;
  open_message_id: string;
  tenant_key: string;
  token: string;
  action: Action;
}

export interface Action {
  value: CardActionValue;
  tag: string;
  option?: string;
  input_value?: string;
  form_value?: never;
}

// ------ ORIGINAL larkV2 ------

export type LarkCard = Card | CardV2 | LarkCardTemplate;

export class LarkCardTemplate {
  type = 'template';
  data: {
    template_id: string;
    template_variable: unknown;
  };

  constructor(template_id: string, template_variable: unknown) {
    this.data = {
      template_id,
      template_variable,
    };
  }
}

export enum LarkTemplateId {
  // https://open.feishu.cn/cardkit/editor?cardId=AAqHcvhaPdHYU&cardLocale=zh_cn
  SettingNoneWarning = 'AAqHcvhaPdHYU',
  // https://open.larkoffice.com/cardkit/editor?cardId=AAqCZIyJo1uO0&cardLocale=zh_cn
  IssueAutoPush = 'AAqCZIyJo1uO0',
  // https://open.larkoffice.com/cardkit/editor?cardId=AAq7HU3xmMmag&cardLocale=zh_cn
  IssueFollowUp = 'AAq7HU3xmMmag',
  // https://open.larkoffice.com/cardkit/editor?cardId=AAq70vsZpHZhk&cardLocale=zh_cn
  IssueRequestReAssign = 'AAq70vsZpHZhk',
  // https://open.larkoffice.com/cardkit/editor?cardId=AAq70b9pKE0D7&cardLocale=zh_cn
  IssueRequestOncall = 'AAq70b9pKE0D7',
  // https://open.larkoffice.com/cardkit/editor?cardId=AAq705lGMY8Fg&cardLocale=zh_cn
  IssueViolentPush = 'AAq705lGMY8Fg',
  // https://open.feishu.cn/cardkit/editor?cardId=AAq4ee6LzHFQm
  FeedbackCircuitBreakerInfo = 'AAq4ee6LzHFQm',
  // https://open.feishu.cn/cardkit/editor?cardId=AAq4eaN2PbMFX
  FeedbackCircuitBreakFinish = 'AAq4eaN2PbMFX',
  // https://open.feishu.cn/cardkit/editor?cardId=AAq4eWBFQlM8c
  SlardarCircuitBreakerInfo = 'AAq4eWBFQlM8c',
  SlardarCircuitBreakerFinish = 'AAq4KkhavDhXF',
  ApprovalProcessesAffectedBusinessInvolve = 'AAqIvFkHBr3K3',
}

export interface SendLarkMessageResponseData {
  message_id?: string | undefined;
  root_id?: string | undefined;
  parent_id?: string | undefined;
  msg_type?: string | undefined;
  create_time?: string | undefined;
  update_time?: string | undefined;
  deleted?: boolean | undefined;
  updated?: boolean | undefined;
  chat_id?: string | undefined;
}

export interface SendLarkMessageResponse {
  code?: number | undefined;
  msg?: string | undefined;
  data?: SendLarkMessageResponseData;
}
