export interface Chats {
  items: Chat[];
  has_more: boolean;
  page_token: string;
}

export interface Chat {
  avatar: string;
  chat_id: string;
  description: string;
  external: boolean;
  name: string;
  owner_id: string;
  owner_id_type: string;
  tenant_key: string;
}

export interface MessageMentionInfo {
  key: string;
  id: {
    union_id?: string;
    user_id?: string;
    open_id?: string;
  };
  name: string;
  tenant_key?: string;
}

export interface LarkBotMessageHookMessageInfo {
  message_id: string;
  root_id?: string;
  parent_id?: string;
  create_time: string;
  update_time?: string;
  chat_id: string;
  thread_id?: string;
  chat_type: string;
  message_type: string;
  content: string;
  mentions?: MessageMentionInfo[];
  user_agent?: string;
}

export interface LarkBotMessageHookReceiveData {
  event_id?: string;
  token?: string;
  create_time?: string;
  event_type?: string;
  tenant_key?: string;
  ts?: string;
  uuid?: string;
  type?: string;
  app_id?: string;
  sender: {
    sender_id?: {
      union_id?: string;
      user_id?: string;
      open_id?: string;
    };
    sender_type: string;
    tenant_key?: string;
  };
  message: LarkBotMessageHookMessageInfo;
}

export interface ChatShareLink {
  share_link: string;
  is_permanent: boolean;
}
