export interface LarkApprovalConfig {
  approval_name: string;
  status: 'ACTIVE' | 'INACTIVE' | 'DELETED' | 'UNKNOWN';
  form: string;
  node_list: Array<LarkApprovalConfigNode>;
  viewers: Array<{
    type: 'TENANT' | 'DEPARTMENT' | 'USER' | 'ROLE' | 'USER_GROUP' | 'NONE';
    id?: string;
    user_id?: string;
  }>;
  approval_admin_ids?: string[] | undefined;
  form_widget_relation?: string | undefined;
}

export interface LarkApprovalConfigNode {
  name: string;
  need_approver: boolean;
  node_id: string;
  custom_node_id?: string;
  node_type: 'AND' | 'OR' | 'SEQUENTIAL' | 'CC_NODE';
  approver_chosen_multi: boolean;
  approver_chosen_range?: Array<{
    approver_range_type?: number;
    approver_range_ids?: Array<string>;
  }>;
  require_signature?: boolean;
}

/**
 * 该定义用于创建审批流定义
 * {@link https://open.feishu.cn/api-explorer?project=approval&resource=approval&apiName=create&version=v4 click to debug }
 *
 * {@link https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/approval-v4/approval/create document }
 **/
export interface LarkApprovalDefineConfig {
  approval_name: string;
  approval_code?: string;
  description?: string;
  viewers: Array<{
    viewer_type?: 'TENANT' | 'DEPARTMENT' | 'USER' | 'NONE';
    viewer_user_id?: string;
    viewer_department_id?: string;
  }>;
  form: {
    form_content: string;
    widget_relation?: string;
  };
  node_list: Array<{
    id: string;
    name?: string;
    node_type?: 'AND' | 'OR' | 'SEQUENTIAL';
    approver?: Array<{
      type: 'Supervisor' | 'SupervisorTopDown' | 'DepartmentManager' | 'DepartmentManagerTopDown' | 'Personal' | 'Free';
      user_id?: string;
      level?: string;
    }>;
    ccer?: Array<{
      type: 'Supervisor' | 'SupervisorTopDown' | 'DepartmentManager' | 'DepartmentManagerTopDown' | 'Personal' | 'Free';
      user_id?: string;
      level?: string;
    }>;
    privilege_field?: {
      writable: Array<string>;
      readable: Array<string>;
    };
    approver_chosen_multi?: boolean;
    approver_chosen_range?: Array<{
      type?: 'ALL' | 'PERSONAL' | 'ROLE';
      id_list?: Array<string>;
    }>;
    starter_assignee?: 'STARTER' | 'AUTO_PASS' | 'SUPERVISOR' | 'DEPARTMENT_MANAGER';
  }>;
  settings?: {
    revert_interval?: number;
    revert_option?: number;
    reject_option?: number;
    quick_approval_option?: number;
  };
  config?: {
    can_update_viewer: boolean;
    can_update_form: boolean;
    can_update_process: boolean;
    can_update_revert: boolean;
    help_url?: string;
  };
  icon?: number;
  i18n_resources: Array<{
    locale: 'zh-CN' | 'en-US' | 'ja-JP';
    texts: Array<{
      key: string;
      value: string;
    }>;
    is_default: boolean;
  }>;
  process_manager_ids?: Array<string>;
}

export interface ApprovalForm {
  default_value_type: string;
  enable_default_value: boolean;
  id: string;
  name: string;
  option: { value: string; text: string }[];
  printable: boolean;
  required: true;
  type: string;
  visible: boolean;
  widget_default_value: string;
  children?: ApprovalForm[];
}

export enum LarkApprovalComponentType {
  input = 'input',
  textarea = 'textarea',
  number = 'number',
  radioV2 = 'radioV2',
  dateInterval = 'dateInterval',
}

export interface LarkApprovalFormConfig {
  id: string;
  name: string;
  required: boolean;
  type: LarkApprovalComponentType;
  value: unknown;
}

export interface ApprovalInstanceEvent {
  app_id: string;
  approval_code: string;
  instance_code: string;
  instance_operate_time: string;
  operate_time: string;
  status: string;
  tenant_key: string;
  type: string;
  uuid: string;
  token: string;
  ts: string;
}

// https://open.larkoffice.com/document/server-docs/approval-v4/event/common-event/approval-task-event
export interface ApprovalTaskEvent {
  app_id: string;
  open_id: string;
  tenant_key: string;
  approval_code: string;
  instance_code: string;
  task_id: string;
  user_id: string;
  status: ApprovalTaskStatus;
  operate_time: string;
  custom_key: string;
  def_key: string;
  extra: string;
}

export interface ApprovalTask {
  id: string;
  user_id: string;
  open_id?: string;
  status: 'PENDING' | 'APPROVED' | 'REJECTED' | 'TRANSFERRED' | 'DONE';
  node_id?: string;
  node_name?: string;
  custom_node_id?: string;
  type?: 'AND' | 'OR' | 'AUTO_PASS' | 'AUTO_REJECT' | 'SEQUENTIAL';
  start_time: string;
  end_time?: string;
}

// https://open.larkoffice.com/document/server-docs/approval-v4/instance/get
export interface ApprovalInstanceDetail {
  approval_name: string;
  start_time?: string | undefined;
  end_time: string;
  user_id: string;
  open_id: string;
  serial_number: string;
  department_id: string;
  status: 'PENDING' | 'APPROVED' | 'REJECTED' | 'CANCELED' | 'DELETED';
  uuid: string;
  form: string;
  task_list: Array<ApprovalTask>;
  comment_list: Array<{
    id: string;
    user_id: string;
    open_id: string;
    comment: string;
    create_time: string;
    files?: Array<{
      url?: string;
      file_size?: number;
      title?: string;
      type?: string;
    }>;
  }>;
  timeline: Array<{
    type:
      | 'START'
      | 'PASS'
      | 'REJECT'
      | 'AUTO_PASS'
      | 'AUTO_REJECT'
      | 'REMOVE_REPEAT'
      | 'TRANSFER'
      | 'ADD_APPROVER_BEFORE'
      | 'ADD_APPROVER'
      | 'ADD_APPROVER_AFTER'
      | 'DELETE_APPROVER'
      | 'ROLLBACK_SELECTED'
      | 'ROLLBACK'
      | 'CANCEL'
      | 'DELETE'
      | 'CC';
    create_time: string;
    user_id?: string;
    open_id?: string;
    user_id_list?: Array<string>;
    open_id_list?: Array<string>;
    task_id?: string;
    comment?: string;
    cc_user_list?: Array<{
      user_id?: string;
      cc_id?: string;
      open_id?: string;
    }>;
    ext: string;
    node_key?: string;
    files?: Array<{
      url?: string;
      file_size?: number;
      title?: string;
      type?: string;
    }>;
  }>;
  modified_instance_code?: string;
  reverted_instance_code?: string;
  approval_code: string;
  reverted?: boolean;
  instance_code: string;
}

export enum ApprovalTaskStatus {
  PENDING = 'PENDING',
  APPROVED = 'APPROVED',
  REJECTED = 'REJECTED',
  DONE = 'DONE',
  ROLLBACK = 'ROLLBACK',
  OVERTIME_CLOSE = 'OVERTIME_CLOSE',
  OVERTIME_RECOVER = 'OVERTIME_RECOVER',
}

export enum LarkApprovalStatus {
  APPROVED = 'APPROVED',
  REJECTED = 'REJECTED',
  PENDING = 'PENDING',
}

export type LarkApprovalNodeType = 'AND' | 'OR' | 'SEQUENTIAL';

export type LarkApprovalEventType = 'approval_task' | 'approval_instance';

export enum ApproverType {
  Personal = 'Personal',
  Supervisor = 'Supervisor',
  DepartmentManager = 'DepartmentManagerTopDown',
  Free = 'Free',
}

export interface ApprovalFormValue {
  id: string;
  type: string;
  value: string | number;
}
