export interface SpaceNode {
  node: {
    space_id: string;
    node_token: string;
    obj_token: string;
    obj_type: string;
    parent_node_token: string;
    node_type: string;
    origin_node_token: string;
    origin_space_id: string;
    has_child: boolean;
    title: string;
    obj_create_time: string;
    obj_edit_time: string;
    node_create_time: string;
    creator: string;
    owner: string;
  };
}
