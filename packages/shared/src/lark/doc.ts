export interface DocumentDisplaySetting {
  show_authors: boolean;
  show_comment_count: boolean;
  show_create_time: boolean;
  show_like_count: boolean;
  show_pv: boolean;
  show_uv: boolean;
}

export interface DocumentCover {
  token: string;
  offset_ratio_x: number;
  offset_ratio_y: number;
}

export interface Document {
  document_id: string;
  revision_id: number;
  title: string;
  display_setting?: DocumentDisplaySetting;
  cover?: DocumentCover;
}

export enum BlockType {
  Page = 1,
  Text,
  Heading1,
  Heading2,
  Heading3,
  Heading4,
  Heading5,
  Heading6,
  Heading7,
  Heading8,
  Heading9,
  Bullet,
  Ordered,
  Code,
  Quote,
  Todo = 17,
  Bitable,
  Callout,
  ChatCard,
  Diagram,
  Divider,
  File,
  Grid,
  GridColumn,
  Iframe,
  Image,
  Isv,
  Mindnote,
  Sheet,
  Table,
  TableCell,
  View,
  QuoteContainer,
  Task,
  Okr,
  OkrObjective,
  OkrKeyResult,
  OkrProgress,
  AddOns,
  JiraIssue,
  WikiCatalog,
  Board,
  Undefined = 999,
}

export interface TextStyle {
  align?: Align;
  done?: boolean;
  folded?: boolean;
  language?: CodeLanguage;
  wrap?: boolean;
}
export enum Align {
  Left = 1,
  Center,
  Right,
}

export enum CodeLanguage {
  PlainText = 1,
  ABAP,
  Ada,
  Apache,
  Apex,
  Assembly,
  Bash,
  CSharp,
  CPlusPlus,
  C,
  COBOL,
  CSS,
  CoffeeScript,
  D,
  Dart,
  Delphi,
  Django,
  Dockerfile,
  Erlang,
  Fortran,
  FoxPro,
  Go,
  Groovy,
  HTML,
  HTMLBars,
  HTTP,
  Haskell,
  JSON,
  Java,
  JavaScript,
  Julia,
  Kotlin,
  LateX,
  Lisp,
  Logo,
  Lua,
  MATLAB,
  Makefile,
  Markdown,
  Nginx,
  Objective,
  OpenEdgeABL,
  PHP,
  Perl,
  PostScript,
  PowerShell,
  Prolog,
  ProtoBuf,
  Python,
  R,
  RPG,
  Ruby,
  Rust,
  SAS,
  SCSS,
  SQL,
  Scala,
  Scheme,
  Scratch,
  Shell,
  Swift,
  Thrift,
  TypeScript,
  VBScript,
  Visual,
  XML,
  YAML,
  CMake,
  Diff,
  Gherkin,
  GraphQL,
  OpenGLShadingLanguage,
  Properties,
  Solidity,
  TOML,
}

export interface TextElement {
  text_run?: TextRun;
  mention_user?: MentionUser;
  mention_doc?: MentionDoc;
  reminder?: Reminder;
  file?: InlineFile;
  inline_block?: InlineBlock;
  equation?: Equation;
  undefined_element?: UndefinedElement;
}

export interface TextElementStyle {
  bold?: boolean;
  italic?: boolean;
  strikethrough?: boolean;
  underline?: boolean;
  inline_code?: boolean;
  text_color?: FontColor;
  background_color?: FontBackgroundColor;
  comment_ids?: string[];
  link?: Link;
}

export interface Link {
  url: string;
}

export enum FontColor {
  Pink = 1,
  Orange,
  Yellow,
  Green,
  Blue,
  Purple,
  Gray,
}

export enum FontBackgroundColor {
  lightPink = 1,
  lightOrange = 2,
  lightYellow = 3,
  lightGreen = 4,
  lightBlue = 5,
  lightPurple = 6,
  lightGray = 7,
  darkPink = 8,
  darkOrange = 9,
  darkYellow = 10,
  darkGreen = 11,
  darkBlue = 12,
  darkPurple = 13,
  darkGray = 14,
}

export interface TextRun {
  content: string;
  text_element_style?: TextElementStyle;
}

export interface MentionUser {
  user_id: string;
  text_element_style?: TextElementStyle;
}

export interface MentionDoc {
  token: string;
  obj_type: MentionObjType;
  url: string;
  text_element_style?: TextElementStyle;
}

export interface Reminder {
  create_user_id: string;
  is_notify?: boolean;
  is_whole_day?: boolean;
  expire_time: number;
  notify_time: number;
  text_element_style?: TextElementStyle;
}

export interface InlineFile {
  file_token?: string;
  source_block_id?: string;
  text_element_style?: TextElementStyle;
}

export interface InlineBlock {
  block_id?: string;
  text_element_style?: TextElementStyle;
}

export interface Equation {
  content: string;
  text_element_style?: TextElementStyle;
}

export interface UndefinedElement {}

/*
MentionObjType
Mention 云文档类型。

枚举值	描述
1	Doc
3	Sheet
8	Bitable
11	MindNote
12	File
15	Slide
16	Wiki
22	Docx
 */
export enum MentionObjType {
  Doc = 1,
  Sheet = 3,
  Bitable = 8,
  MindNote = 11,
  File = 12,
  Slide = 15,
  Wiki = 16,
  Docx = 22,
}

export interface Text {
  style?: TextStyle;
  elements: TextElement[];
}

export interface Sheet {
  token?: string;
  row_size?: number;
  column_size?: number;
}

export interface Table {
  cells?: string[];
  property: TableProperty;
}

export interface TableProperty {
  row_size: number;
  column_size: number;
  column_width?: number[];
  header_row?: boolean;
  header_column?: boolean;
  merge_info?: TableMergeInfo[];
}

export interface TableMergeInfo {
  row_span: number;
  col_span: number;
}

export interface Bitable {
  view_type: number;
}

export interface TableCell {}

export interface Block {
  block_id?: string;
  block_type?: BlockType;
  parent_id?: string;
  children?: string[];
  comment_ids?: string[];
  page?: Text;
  text?: Text;
  heading1?: Text;
  heading2?: Text;
  heading3?: Text;
  heading4?: Text;
  heading5?: Text;
  heading6?: Text;
  heading7?: Text;
  heading8?: Text;
  heading9?: Text;
  bullet?: Text;
  ordered?: Text;
  code?: Text;
  quote?: Text;
  todo?: Text;
  sheet?: Sheet;
  table?: Table;
  bitable?: Bitable;
  table_cell?: TableCell;
}

export interface Descendant {
  index?: number;
  children_id: string[];
  descendants: Block[];
}

export interface DocMember {
  member_type: string;
  member_id: string;
  perm: string;
  perm_type?: string;
  type?: string;
}

export interface DescendantRes {
  children: Block[];
  block_id_relations: { block_id: string; temporary_block_id: string }[];
}

export interface UpdateTextStyle {
  style: TextStyle;
  fields: number[];
}

/*
1：修改 Block 的对齐方式

2：修改 todo 的完成状态。支持对 Todo 和 Task 块进行修改

3：文本的折叠状态。支持对 Heading1~9、和有子块的 Text、Ordered、Bullet、Todo 和 Task 块进行修改

4：代码块的语言类型。仅支持对 Code 块进行修改

5：代码块是否自动换行。支持对 Code 块进行修改
 */
export interface UpdateText {
  elements?: TextElement[];
  style?: TextStyle;
  fields: number[];
}

interface InsertTableRowReq {
  row_index: number;
}

interface MergeTableCellReq {
  row_start_index: number;
  row_end_index: number;
  column_start_index: number;
  column_end_index: number;
}

export interface UpdateBlock {
  update_text_element?: TextElement[];
  update_text_style?: UpdateTextStyle;
  update_table_property?: TableProperty;
  update_text?: UpdateText;
  insert_table_row?: InsertTableRowReq;
  merge_table_cells?: MergeTableCellReq;
}

export interface BitableRecordChangedEvent {
  file_token: string;
  table_id: string;
  file_type: string;
  action_list: {
    record_id: string;
    action: 'record_added' | 'record_deleted' | 'record_edited';
  }[];
  operator_id: {
    open_id: string;
  };
}

export interface BitableFieldChangedEvent {
  file_token: string;
  table_id: string;
  file_type: string;
  action_list: {
    action: 'field_deleted' | 'field_added' | 'field_edited';
    after_value: {
      name: string;
    };
    before_value: {
      name: string;
    };
  }[];
  operator_id: {
    open_id: string;
  };
}

export interface BitableRecord {
  record_id: string;
  shared_url: string;
}
