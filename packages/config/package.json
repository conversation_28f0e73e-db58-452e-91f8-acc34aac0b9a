{"name": "@byted-emo/config", "version": "1.0.0", "private": true, "description": "公共基础配置 (比如 eslint、tsconfig)", "main": ".eslintrc.js", "scripts": {}, "devDependencies": {"eslint": "^8.57.0", "@byted/eslint-config-standard": "^3.1.1", "@byted/eslint-config-standard-react": "^2.1.1", "@byted/eslint-config-standard-ts": "^3.1.1", "eslint-plugin-prettier": "^5.1.3", "@edenx/tsconfig": "1.60.4", "@swc/core": "1.3.35", "@swc/jest": "0.2.22", "@testing-library/jest-dom": "5.16.4", "@types/jest": "28.1.3", "@types/node": "18.6.2", "@types/react": "~18.2.20", "@types/react-dom": "^18.2.7", "jest-environment-jsdom": "28.1.3", "@byted/eslint-config-eden": "^5.0.37"}, "keywords": [], "author": "", "license": "ISC"}