{"compilerOptions": {"lib": ["DOM", "ESNext"], "allowJs": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "declaration": false, "jsx": "preserve", "strict": true, "module": "NodeNext", "moduleResolution": "nodenext", "target": "ES2017", "experimentalDecorators": true, "emitDecoratorMetadata": true, "strictPropertyInitialization": false, "useDefineForClassFields": false}, "$schema": "https://json.schemastore.org/tsconfig", "display": "Base"}