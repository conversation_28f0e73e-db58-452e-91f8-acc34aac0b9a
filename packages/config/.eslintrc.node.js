module.exports = {
  root: true,
  extends: ['@byted/eslint-config-eden/node-ts'],
  plugins: ['prettier'],
  rules: {
    'prettier/prettier': 'error',
    '@typescript-eslint/no-empty-interface': 0,
    '@typescript-eslint/no-explicit-any': 0,
    'require-await': 0,
    '@typescript-eslint/ban-ts-comment': 0,
    '@typescript-eslint/naming-convention': 0,
    '@typescript-eslint/no-empty-function': 0,
    '@byted/eden/max-calls-in-template': 'warn',
    'max-depth': ['error', 7],
  },
};
