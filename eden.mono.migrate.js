module.exports = {
    baseBranch: 'feat/migrate',// 当前 Monorepo 仓库所在的分支，尽可能切出来一个新的分支
    repos: [ // 需要合并哪些仓库
        {
            name: 'bits-ci', // 仓库名，会作为该子项目的目录名
            git: '******************:faceu-android/bits-ci.git', // git 地址
            branch: 'develop', // 该仓库需要合并的分支
            isPathContainsName: false, // 最终的 path 是否带上 name，若为 true，最终的目录为 packages/test/hook，若为 false，则最终目录为 packages/hook
            packages: [
                {
                    name: 'host', // 子项目的名称
                    targetDir: 'apps/host', // 子项目被生成到的目录：packages/hook
                    include: ['host'], // 子项目在原仓库中的目录
                },
                {
                    name: 'quality', // 子项目的名称
                    targetDir: 'apps/quality', // 子项目被生成到的目录：packages/hook
                    include: ['quality'], // 子项目在原仓库中的目录
                },
            ]
        },
    ],
};