name: Partial Pipeline - Before Merge
trigger: change
jobs:
  emo-partial:
    image: hub.byted.org/codebase/ci_nodejs_16
    steps:
     - name: Init
       commands:
         - npm config set registry https://bnpm.byted.org
         - npm install -g @ies/eden-monorepo@3.5.0
     - name: Pipeline
       commands:
         - emo pipeline --scene gitlab --trigger-branch create --target-branch $CI_EVENT_CHANGE_TARGET_BRANCH --revision origin/$CI_EVENT_CHANGE_TARGET_BRANCH
