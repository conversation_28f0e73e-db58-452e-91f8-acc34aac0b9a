name: Full Pipeline - Before Merge
trigger: change
jobs:
  emo-full:
    image: hub.byted.org/codebase/ci_nodejs_18
    steps:
     - name: Init
       commands:
         - npm config set registry https://bnpm.byted.org
         - npm install -g @ies/eden-monorepo@3.5.0
     - name: Install
       commands:
         - emo install
     - name: Emo check
       commands:
         - emo check
     - name: Lint
       commands:
         - emo run lint:error
#     - name: Format
#       commands:
#         - emo run format:ci --infra
#     - name: Build
#       commands:
#         - emo run build
     - name: Test
       commands:
         - emo run test
