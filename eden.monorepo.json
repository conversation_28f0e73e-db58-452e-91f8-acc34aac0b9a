{"$schema": "https://sf-unpkg-src.bytedance.net/@ies/eden-monorepo@3.5.0/lib/monorepo.schema.json", "config": {"strictNodeModules": true, "infraDir": "infra", "pnpmVersion": "8.6.12", "edenMonoVersion": "3.5.0", "scriptName": {"test": ["test"], "build": ["build"], "start": ["build:watch", "dev", "start", "serve"]}, "workspaceCheck": {"externalDependencyCheck": {"autofix": false, "forceCheck": false, "options": {"installedButNotUsed": false, "usedButNotInstalled": {"excludes": ["@shared/bits"]}}}}}, "packages": [{"path": "packages/config", "shouldPublish": false}, {"path": "apps/host", "shouldPublish": false}, {"path": "apps/quality", "shouldPublish": false}, {"path": "packages/shared", "shouldPublish": false}, {"path": "packages/backend", "shouldPublish": false}]}